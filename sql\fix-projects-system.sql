-- ============================================================================
-- FIX PROJECTS SYSTEM - COMPLETE SOLUTION
-- This script creates/fixes the projects table and related tables
-- Run this in Supabase SQL Editor
-- ============================================================================

-- STEP 1: Create departments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert sample departments if they don't exist
INSERT INTO public.departments (name, description) VALUES
    ('IT Department', 'Information Technology and Systems'),
    ('HR Department', 'Human Resources and Administration'),
    ('Finance Department', 'Finance and Accounting'),
    ('Operations', 'Field Operations and Maintenance'),
    ('Engineering', 'Technical Engineering and Design'),
    ('Marketing', 'Marketing and Communications'),
    ('Sales', 'Sales and Business Development')
ON CONFLICT (name) DO NOTHING;

-- STEP 2: Create projects table with correct structure
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    start_date DATE,
    end_date DATE,
    budget DECIMAL(12,2),
    manager_id UUID REFERENCES public.profiles(id),
    department_id UUID REFERENCES public.departments(id),
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id)
);

-- STEP 3: Create project_assignments table
CREATE TABLE IF NOT EXISTS public.project_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID NOT NULL REFERENCES public.projects(id) ON DELETE CASCADE,
    assigned_to UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    role TEXT DEFAULT 'member' CHECK (role IN ('manager', 'lead', 'member', 'consultant')),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed')),
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    hours_allocated DECIMAL(8,2) DEFAULT 0,
    hours_worked DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, assigned_to)
);

-- STEP 4: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_projects_manager_id ON public.projects(manager_id);
CREATE INDEX IF NOT EXISTS idx_projects_department_id ON public.projects(department_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON public.project_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_assigned_to ON public.project_assignments(assigned_to);

-- STEP 5: Enable RLS and create policies
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_assignments ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "departments_select_all" ON public.departments;
DROP POLICY IF EXISTS "departments_service_role" ON public.departments;
DROP POLICY IF EXISTS "projects_select_all" ON public.projects;
DROP POLICY IF EXISTS "projects_insert_authenticated" ON public.projects;
DROP POLICY IF EXISTS "projects_update_own" ON public.projects;
DROP POLICY IF EXISTS "projects_service_role" ON public.projects;
DROP POLICY IF EXISTS "project_assignments_select_own" ON public.project_assignments;
DROP POLICY IF EXISTS "project_assignments_insert_own" ON public.project_assignments;
DROP POLICY IF EXISTS "project_assignments_update_own" ON public.project_assignments;
DROP POLICY IF EXISTS "project_assignments_service_role" ON public.project_assignments;

-- Create safe RLS policies for departments
CREATE POLICY "departments_select_all" ON public.departments
    FOR SELECT USING (true);

CREATE POLICY "departments_service_role" ON public.departments
    FOR ALL USING (current_setting('role') = 'service_role');

-- Create safe RLS policies for projects
CREATE POLICY "projects_select_all" ON public.projects
    FOR SELECT USING (true);

CREATE POLICY "projects_insert_authenticated" ON public.projects
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "projects_update_manager_or_admin" ON public.projects
    FOR UPDATE USING (
        manager_id = auth.uid() OR 
        created_by = auth.uid() OR
        current_setting('role') = 'service_role'
    );

CREATE POLICY "projects_service_role" ON public.projects
    FOR ALL USING (current_setting('role') = 'service_role');

-- Create safe RLS policies for project_assignments
CREATE POLICY "project_assignments_select_related" ON public.project_assignments
    FOR SELECT USING (
        assigned_to = auth.uid() OR
        EXISTS (SELECT 1 FROM public.projects p WHERE p.id = project_id AND p.manager_id = auth.uid()) OR
        current_setting('role') = 'service_role'
    );

CREATE POLICY "project_assignments_insert_manager" ON public.project_assignments
    FOR INSERT WITH CHECK (
        EXISTS (SELECT 1 FROM public.projects p WHERE p.id = project_id AND p.manager_id = auth.uid()) OR
        current_setting('role') = 'service_role'
    );

CREATE POLICY "project_assignments_update_manager_or_assigned" ON public.project_assignments
    FOR UPDATE USING (
        assigned_to = auth.uid() OR
        EXISTS (SELECT 1 FROM public.projects p WHERE p.id = project_id AND p.manager_id = auth.uid()) OR
        current_setting('role') = 'service_role'
    );

CREATE POLICY "project_assignments_service_role" ON public.project_assignments
    FOR ALL USING (current_setting('role') = 'service_role');

-- STEP 6: Grant permissions
GRANT ALL ON public.departments TO authenticated;
GRANT ALL ON public.projects TO authenticated;
GRANT ALL ON public.project_assignments TO authenticated;

-- STEP 7: Insert sample data for testing
DO $$
DECLARE
    dept_it_id UUID;
    dept_eng_id UUID;
    sample_user_id UUID;
    project1_id UUID;
    project2_id UUID;
BEGIN
    -- Get department IDs
    SELECT id INTO dept_it_id FROM public.departments WHERE name = 'IT Department' LIMIT 1;
    SELECT id INTO dept_eng_id FROM public.departments WHERE name = 'Engineering' LIMIT 1;
    
    -- Get a sample user ID (current user or first available)
    SELECT COALESCE(auth.uid(), (SELECT id FROM public.profiles LIMIT 1)) INTO sample_user_id;
    
    -- Only insert sample projects if we have a valid user
    IF sample_user_id IS NOT NULL THEN
        -- Insert sample projects
        INSERT INTO public.projects (name, description, status, priority, manager_id, department_id, progress_percentage)
        VALUES 
            ('Website Redesign', 'Complete redesign of company website', 'active', 'high', sample_user_id, dept_it_id, 25),
            ('Mobile App Development', 'Develop mobile application for field operations', 'planning', 'medium', sample_user_id, dept_eng_id, 0)
        ON CONFLICT DO NOTHING
        RETURNING id INTO project1_id;
        
        -- Get project IDs for assignments
        SELECT id INTO project1_id FROM public.projects WHERE name = 'Website Redesign' LIMIT 1;
        SELECT id INTO project2_id FROM public.projects WHERE name = 'Mobile App Development' LIMIT 1;
        
        -- Insert sample project assignments
        IF project1_id IS NOT NULL THEN
            INSERT INTO public.project_assignments (project_id, assigned_to, role, hours_allocated)
            VALUES (project1_id, sample_user_id, 'manager', 40.0)
            ON CONFLICT (project_id, assigned_to) DO NOTHING;
        END IF;
        
        IF project2_id IS NOT NULL THEN
            INSERT INTO public.project_assignments (project_id, assigned_to, role, hours_allocated)
            VALUES (project2_id, sample_user_id, 'manager', 60.0)
            ON CONFLICT (project_id, assigned_to) DO NOTHING;
        END IF;
    END IF;
END $$;

-- STEP 8: Create helper functions for project management
CREATE OR REPLACE FUNCTION get_user_projects(user_uuid UUID DEFAULT NULL)
RETURNS TABLE(
    project_id UUID,
    project_name TEXT,
    project_status TEXT,
    user_role TEXT,
    progress_percentage INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id as project_id,
        p.name as project_name,
        p.status as project_status,
        pa.role as user_role,
        p.progress_percentage
    FROM public.projects p
    JOIN public.project_assignments pa ON p.id = pa.project_id
    WHERE pa.assigned_to = COALESCE(user_uuid, auth.uid())
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_user_projects TO authenticated;

-- STEP 9: Success message and verification
SELECT 'PROJECTS SYSTEM SETUP COMPLETE!' as status;

-- Verify tables exist
SELECT 
    'Tables Created:' as info,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'departments') as departments_table,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'projects') as projects_table,
    (SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'project_assignments') as assignments_table;

-- Show sample data
SELECT 'Sample Projects:' as info, COUNT(*) as project_count FROM public.projects;
SELECT 'Sample Departments:' as info, COUNT(*) as department_count FROM public.departments;

SELECT 'REFRESH YOUR APPLICATION AND TEST PROJECTS!' as next_step;
