import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Trash2, 
  FileText, 
  File, 
  Image, 
  FileSpreadsheet,
  FileCode,
  Archive,
  Eye
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface FileData {
  id: string;
  name: string;
  size: number;
  type: string;
  uploaded_at: string;
  project_name?: string;
  category?: string;
}

interface ModernFileCardProps {
  file: FileData;
  onDownload: (file: FileData) => void;
  onDelete: (file: FileData) => void;
  onPreview?: (file: FileData) => void;
}

const getFileIcon = (type: string) => {
  if (type.includes('image')) return Image;
  if (type.includes('pdf')) return FileText;
  if (type.includes('spreadsheet') || type.includes('excel')) return FileSpreadsheet;
  if (type.includes('code') || type.includes('javascript') || type.includes('typescript')) return FileCode;
  if (type.includes('zip') || type.includes('rar')) return Archive;
  return File;
};

const getFileTypeColor = (type: string) => {
  if (type.includes('image')) return 'from-green-500 to-emerald-400';
  if (type.includes('pdf')) return 'from-red-500 to-rose-400';
  if (type.includes('spreadsheet') || type.includes('excel')) return 'from-blue-500 to-cyan-400';
  if (type.includes('code')) return 'from-purple-500 to-violet-400';
  if (type.includes('zip') || type.includes('rar')) return 'from-orange-500 to-amber-400';
  return 'from-gray-500 to-slate-400';
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const ModernFileCard: React.FC<ModernFileCardProps> = ({
  file,
  onDownload,
  onDelete,
  onPreview
}) => {
  const FileIcon = getFileIcon(file.type);
  const gradientColor = getFileTypeColor(file.type);

  return (
    <div className="modern-file-card group">
      {/* Top Section with Gradient */}
      <div className={`top-section bg-gradient-to-br ${gradientColor}`}>
        {/* Decorative Border */}
        <div className="border">
          <div className="border-before"></div>
        </div>
        <div className="top-section-before"></div>
        
        {/* Icons Section */}
        <div className="icons">
          {/* File Type Icon */}
          <div className="logo">
            <FileIcon className="w-full h-full text-white" />
          </div>
          
          {/* Action Icons */}
          <div className="social-media">
            {onPreview && (
              <button
                onClick={() => onPreview(file)}
                className="action-btn"
                title="Preview"
              >
                <Eye className="w-4 h-4" />
              </button>
            )}
            <button
              onClick={() => onDownload(file)}
              className="action-btn"
              title="Download"
            >
              <Download className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(file)}
              className="action-btn text-red-300 hover:text-red-100"
              title="Delete"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Mountain Design Elements */}
        <div className="design-elements">
          <div className="mountain-1 shape shadow" />
          <div className="mountain-2 shape" />
          <div className="mountain-3 shape shadow" />
        </div>
      </div>

      {/* Bottom Section */}
      <div className="bottom-section">
        <span className="title" title={file.name}>
          {file.name.length > 20 ? `${file.name.substring(0, 20)}...` : file.name}
        </span>
        
        {file.category && (
          <div className="mt-2">
            <Badge variant="outline" className="text-xs">
              {file.category}
            </Badge>
          </div>
        )}

        <div className="row">
          <div className="item">
            <span className="big-text">{formatFileSize(file.size)}</span>
            <span className="regular-text">Size</span>
          </div>
          <div className="item">
            <span className="big-text">{file.type.split('/')[1]?.toUpperCase() || 'FILE'}</span>
            <span className="regular-text">Type</span>
          </div>
          <div className="item">
            <span className="big-text">
              {formatDistanceToNow(new Date(file.uploaded_at), { addSuffix: false })}
            </span>
            <span className="regular-text">Uploaded</span>
          </div>
        </div>

        {file.project_name && (
          <div className="mt-3 text-center">
            <Badge variant="secondary" className="text-xs">
              📁 {file.project_name}
            </Badge>
          </div>
        )}
      </div>

      <style jsx>{`
        .modern-file-card {
          width: 280px;
          border-radius: 20px;
          background: #1a1a1a;
          padding: 5px;
          overflow: hidden;
          box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px;
          transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          position: relative;
        }

        .modern-file-card:hover {
          transform: scale(1.05);
        }

        .top-section {
          height: 150px;
          border-radius: 15px;
          display: flex;
          flex-direction: column;
          position: relative;
          overflow: hidden;
        }

        .border {
          border-bottom-right-radius: 10px;
          height: 30px;
          width: 130px;
          background: #1a1a1a;
          position: relative;
          transform: skew(-40deg);
          box-shadow: -10px -10px 0 0 #1a1a1a;
        }

        .border::before {
          content: "";
          position: absolute;
          width: 15px;
          height: 15px;
          top: 0;
          right: -15px;
          background: rgba(255, 255, 255, 0);
          border-top-left-radius: 10px;
          box-shadow: -5px -5px 0 2px #1a1a1a;
        }

        .top-section::before {
          content: "";
          position: absolute;
          top: 30px;
          left: 0;
          background: rgba(255, 255, 255, 0);
          height: 15px;
          width: 15px;
          border-top-left-radius: 15px;
          box-shadow: -5px -5px 0 2px #1a1a1a;
        }

        .icons {
          position: absolute;
          top: 0;
          width: 100%;
          height: 30px;
          display: flex;
          justify-content: space-between;
          z-index: 10;
        }

        .logo {
          height: 100%;
          aspect-ratio: 1;
          padding: 7px 0 7px 15px;
        }

        .social-media {
          height: 100%;
          padding: 8px 15px;
          display: flex;
          gap: 7px;
        }

        .action-btn {
          height: 100%;
          background: none;
          border: none;
          color: #1a1a1a;
          cursor: pointer;
          transition: color 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .action-btn:hover {
          color: white;
        }

        .design-elements {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 100%;
          pointer-events: none;
        }

        .shape {
          height: 80px;
          width: 80px;
          background-color: rgba(255, 255, 255, 0.1);
          transform: rotate(45deg);
          position: absolute;
        }

        .shadow {
          box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.3);
        }

        .mountain-1 {
          z-index: 1;
          bottom: -40px;
          left: -40px;
        }

        .mountain-2 {
          bottom: -45px;
          left: -10px;
        }

        .mountain-3 {
          z-index: 2;
          bottom: -60px;
          left: 40px;
        }

        .bottom-section {
          margin-top: 15px;
          padding: 10px 5px;
        }

        .title {
          display: block;
          font-size: 16px;
          font-weight: bold;
          color: white;
          text-align: center;
          letter-spacing: 1px;
          line-height: 1.2;
        }

        .row {
          display: flex;
          justify-content: space-between;
          margin-top: 15px;
        }

        .item {
          flex: 30%;
          text-align: center;
          padding: 5px;
          color: rgba(170, 222, 243, 0.8);
        }

        .big-text {
          font-size: 11px;
          display: block;
          font-weight: 600;
        }

        .regular-text {
          font-size: 8px;
          opacity: 0.7;
        }

        .item:nth-child(2) {
          border-left: 1px solid rgba(255, 255, 255, 0.2);
          border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Dark theme adjustments */
        .dark .modern-file-card {
          background: #0a0a0a;
        }

        .dark .border {
          background: #0a0a0a;
          box-shadow: -10px -10px 0 0 #0a0a0a;
        }

        .dark .border::before {
          box-shadow: -5px -5px 0 2px #0a0a0a;
        }

        .dark .top-section::before {
          box-shadow: -5px -5px 0 2px #0a0a0a;
        }

        .dark .title {
          color: #e5e5e5;
        }
      `}</style>
    </div>
  );
};
