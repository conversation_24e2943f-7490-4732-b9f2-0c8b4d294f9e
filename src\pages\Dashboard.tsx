import { ErrorBoundary } from '@/components/ErrorBoundary'
import { useAuth } from '@/components/auth/AuthProvider'
import { DashboardSkeleton } from '@/components/dashboard/DashboardSkeleton'
import { EmergencyDashboard } from '@/components/dashboard/EmergencyDashboard'
import { RoleBasedWelcome } from '@/components/dashboard/RoleBasedWelcome'
import { UnifiedDashboard } from '@/components/dashboard/UnifiedDashboard'
import { ManagerDashboard } from '@/components/manager/ManagerDashboard'
import EnhancedStaffDashboard from '@/components/staff/EnhancedStaffDashboard'
import { useNavigate } from 'react-router-dom'

const Dashboard = () => {
  const { userProfile, loading, initialized } = useAuth()
  const navigate = useNavigate()

  // Debug logging
  console.log('🔍 Dashboard Debug:', {
    userProfile,
    loading,
    initialized,
    userRole: userProfile?.role
  })

  // Show loading state while checking authentication
  if (loading || !initialized) {
    console.log('📊 Dashboard: Showing loading state')
    return <DashboardSkeleton />
  }

  // Show error if not authenticated - but provide a way to continue
  if (!userProfile) {
    console.log('❌ Dashboard: No user profile, showing fallback dashboard')
    // Instead of redirecting, show a fallback dashboard
    return (
      <ErrorBoundary>
        <div className='space-y-6 p-4'>
          <div className='bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg border'>
            <h2 className='font-bold text-lg mb-2'>⚠️ Authentication Issue</h2>
            <p>No user profile found. Please sign in to access your dashboard.</p>
            <button
              onClick={() => navigate('/auth')}
              className='mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            >
              Go to Sign In
            </button>
          </div>
          {/* Show emergency dashboard for testing */}
          <EmergencyDashboard />
        </div>
      </ErrorBoundary>
    )
  }

  console.log('✅ Dashboard: Rendering dashboard for role:', userProfile.role)

  // Determine which dashboard to render based on user role
  const renderDashboard = () => {
    try {
      if (userProfile.role === 'staff') {
        return <EnhancedStaffDashboard />
      }
      if (userProfile.role === 'manager') {
        return <ManagerDashboard />
      }
      return <UnifiedDashboard />
    } catch (error) {
      console.error('❌ Dashboard render error, using emergency dashboard:', error)
      return <EmergencyDashboard />
    }
  }

  // Main dashboard content - clean layout
  return (
    <ErrorBoundary>
      <div className='space-y-6'>
        {/* Role-based welcome header */}
        <RoleBasedWelcome />

        {/* Role-specific dashboard content */}
        <ErrorBoundary
          fallback={
            <div className='space-y-4'>
              <div className='bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg border'>
                <h3 className='font-bold text-lg mb-2'>⚠️ Dashboard Loading Issue</h3>
                <p>Using emergency dashboard. Some features may be limited.</p>
              </div>
              <EmergencyDashboard />
            </div>
          }
        >
          {renderDashboard()}
        </ErrorBoundary>
      </div>
    </ErrorBoundary>
  )
}

export default Dashboard
