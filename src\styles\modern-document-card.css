
.modern-document-card {
  /* Base card styling with neumorphism */
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  box-shadow: 
    20px 20px 60px #d1d1d1,
    -20px -20px 60px #ffffff,
    inset 0 0 0 1px rgba(239, 68, 68, 0.1);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.dark .modern-document-card {
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  box-shadow: 
    20px 20px 60px #0d0d0d,
    -20px -20px 60px #333333,
    inset 0 0 0 1px rgba(239, 68, 68, 0.2);
}

.modern-document-card:hover {
  transform: scale(1.05) translateY(-5px);
  box-shadow: 
    25px 25px 80px #d1d1d1,
    -25px -25px 80px #ffffff,
    inset 0 0 0 1px rgba(239, 68, 68, 0.2);
}

.dark .modern-document-card:hover {
  box-shadow: 
    25px 25px 80px #0d0d0d,
    -25px -25px 80px #333333,
    inset 0 0 0 1px rgba(239, 68, 68, 0.3);
}

/* Top section gradient animations */
.modern-document-card .top-section {
  position: relative;
  overflow: hidden;
}

.modern-document-card .top-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

.modern-document-card:hover .top-section::before {
  animation: shimmer 1.5s ease-in-out;
  opacity: 1;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Border element styling */
.modern-document-card .border-element {
  position: relative;
}

.modern-document-card .border-element::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-top: 15px solid;
  border-top-color: inherit;
}

/* File type specific gradients with enhanced effects */
.gradient-pdf {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  position: relative;
}

.gradient-image {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
}

.gradient-spreadsheet {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

.gradient-code {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
}

.gradient-archive {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

/* Action buttons with neumorphic effect */
.modern-document-card .action-button {
  background: linear-gradient(145deg, #f0f0f0, #ffffff);
  box-shadow: 
    5px 5px 15px #d1d1d1,
    -5px -5px 15px #ffffff;
  border: none;
  transition: all 0.3s ease;
}

.dark .modern-document-card .action-button {
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  box-shadow: 
    5px 5px 15px #0d0d0d,
    -5px -5px 15px #333333;
}

.modern-document-card .action-button:hover {
  box-shadow: 
    inset 5px 5px 15px #d1d1d1,
    inset -5px -5px 15px #ffffff;
  transform: scale(0.95);
}

.dark .modern-document-card .action-button:hover {
  box-shadow: 
    inset 5px 5px 15px #0d0d0d,
    inset -5px -5px 15px #333333;
}

/* Stats section styling */
.modern-document-card .stats-divider {
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(156, 163, 175, 0.3),
    transparent
  );
  width: 1px;
  height: 100%;
}

.dark .modern-document-card .stats-divider {
  background: linear-gradient(
    to bottom,
    transparent,
    rgba(75, 85, 99, 0.5),
    transparent
  );
}

/* Loading and error states */
.modern-document-card.loading {
  opacity: 0.7;
  pointer-events: none;
}

.modern-document-card.loading .top-section {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: loading-shimmer 2s infinite;
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .modern-document-card {
    width: 200px;
  }
  
  .modern-document-card .top-section {
    height: 120px;
  }
  
  .modern-document-card .title {
    font-size: 14px;
    letter-spacing: 1px;
  }
  
  .modern-document-card .big-text {
    font-size: 10px;
  }
  
  .modern-document-card .regular-text {
    font-size: 8px;
  }
}

/* Accessibility improvements */
.modern-document-card:focus-within {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
}

.modern-document-card .action-button:focus {
  outline: 2px solid #ef4444;
  outline-offset: 1px;
}

/* Print styles */
@media print {
  .modern-document-card {
    box-shadow: none;
    border: 1px solid #ccc;
    break-inside: avoid;
  }
  
  .modern-document-card:hover {
    transform: none;
  }
}
