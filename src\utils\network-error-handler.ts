/**
 * Network Error Handler
 * Provides robust error handling for network-related issues
 */

export interface NetworkErrorInfo {
  title: string;
  message: string;
  isRetryable: boolean;
  retryDelay?: number;
}

export function handleNetworkError(error: any): NetworkErrorInfo {
  const errorMessage = error?.message || error?.toString() || 'Unknown error';
  
  // Network connection errors
  if (errorMessage.includes('ERR_NETWORK_CHANGED')) {
    return {
      title: 'Network Changed',
      message: 'Your network connection changed. Please try again.',
      isRetryable: true,
      retryDelay: 2000
    };
  }
  
  if (errorMessage.includes('ERR_CONNECTION_TIMED_OUT')) {
    return {
      title: 'Connection Timeout',
      message: 'The request timed out. Please check your connection and try again.',
      isRetryable: true,
      retryDelay: 3000
    };
  }
  
  if (errorMessage.includes('Failed to fetch')) {
    return {
      title: 'Connection Failed',
      message: 'Unable to connect to the server. Please check your internet connection.',
      isRetryable: true,
      retryDelay: 2000
    };
  }
  
  // Supabase specific errors
  if (error?.code === 'PGRST116') {
    return {
      title: 'Access Denied',
      message: 'You do not have permission to access this resource.',
      isRetryable: false
    };
  }
  
  if (error?.code === '42P01') {
    return {
      title: 'Resource Not Found',
      message: 'The requested resource does not exist.',
      isRetryable: false
    };
  }
  
  // Rate limiting
  if (error?.status === 429) {
    return {
      title: 'Too Many Requests',
      message: 'Please wait a moment before trying again.',
      isRetryable: true,
      retryDelay: 5000
    };
  }
  
  // Server errors
  if (error?.status >= 500) {
    return {
      title: 'Server Error',
      message: 'The server is experiencing issues. Please try again later.',
      isRetryable: true,
      retryDelay: 10000
    };
  }
  
  // Default error
  return {
    title: 'Error',
    message: errorMessage.length > 100 ? 'An unexpected error occurred' : errorMessage,
    isRetryable: true,
    retryDelay: 3000
  };
}

export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      const errorInfo = handleNetworkError(error);
      
      // Don't retry if error is not retryable
      if (!errorInfo.isRetryable) {
        throw error;
      }
      
      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Calculate delay with exponential backoff
      const delay = errorInfo.retryDelay || (baseDelay * Math.pow(2, attempt));
      
      console.log(`Retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
}

export function isNetworkError(error: any): boolean {
  const errorMessage = error?.message || error?.toString() || '';
  
  return (
    errorMessage.includes('ERR_NETWORK_CHANGED') ||
    errorMessage.includes('ERR_CONNECTION_TIMED_OUT') ||
    errorMessage.includes('Failed to fetch') ||
    errorMessage.includes('NetworkError') ||
    errorMessage.includes('net::ERR_')
  );
}

export function createNetworkAwareQuery<T>(
  queryFn: () => Promise<T>,
  options: {
    maxRetries?: number;
    retryDelay?: number;
    onError?: (error: NetworkErrorInfo) => void;
  } = {}
) {
  const { maxRetries = 3, retryDelay = 1000, onError } = options;
  
  return async (): Promise<T> => {
    try {
      return await retryWithBackoff(queryFn, maxRetries, retryDelay);
    } catch (error) {
      const errorInfo = handleNetworkError(error);
      onError?.(errorInfo);
      throw error;
    }
  };
}

// Global error handler for unhandled promise rejections
if (typeof window !== 'undefined') {
  window.addEventListener('unhandledrejection', (event) => {
    if (isNetworkError(event.reason)) {
      console.warn('Unhandled network error:', event.reason);
      // Prevent the error from being logged to console as unhandled
      event.preventDefault();
    }
  });
}
