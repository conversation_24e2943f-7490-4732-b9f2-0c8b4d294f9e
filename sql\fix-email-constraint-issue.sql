-- ============================================================================
-- FIX EMAIL CONSTRAINT AND RLS ISSUE
-- The profile exists but <PERSON><PERSON> is blocking access, causing duplicate email errors
-- User: 44349058-db4b-4a0b-8c99-8a913d07df74 (<EMAIL>)
-- ============================================================================

-- Step 1: Temporarily disable R<PERSON> to see what's actually in the table
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- Step 2: Check if the profile actually exists
SELECT 'Existing Profile Check' as check_type,
       id, email, full_name, role, status, created_at
FROM public.profiles 
WHERE email = '<EMAIL>' 
   OR id = '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Step 3: If profile exists with different ID, we need to handle this carefully
-- First, let's see all profiles with this email
SELECT 'All profiles with this email' as check_type,
       id, email, full_name, role, status, created_at
FROM public.profiles 
WHERE email = '<EMAIL>';

-- Step 4: Clean up any duplicate or orphaned profiles
-- Remove any profile with this email that doesn't match the current user ID
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Step 5: Now safely upsert the correct profile
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
    role = COALESCE(EXCLUDED.role, profiles.role),
    status = COALESCE(EXCLUDED.status, profiles.status),
    updated_at = NOW();

-- Step 6: Drop ALL existing RLS policies to start completely fresh
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'profiles' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.profiles';
        RAISE NOTICE 'Dropped policy: %', policy_record.policyname;
    END LOOP;
END $$;

-- Step 7: Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Step 8: Create the simplest possible policies that work
CREATE POLICY "profiles_select_policy" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_policy" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_policy" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Step 9: Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- Step 10: Verify the fix worked
SELECT 'Final Verification' as check_type,
       id, email, full_name, role, status, created_at
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Step 11: Test RLS is working
SELECT 'RLS Status' as check_type,
       schemaname, 
       tablename, 
       rowsecurity,
       (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public') as policy_count
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- Step 12: Show active policies
SELECT 'Active Policies' as check_type,
       policyname, 
       cmd as operation
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;

COMMENT ON TABLE public.profiles IS 'Profiles table - Fixed email constraint conflict and RLS issues';
