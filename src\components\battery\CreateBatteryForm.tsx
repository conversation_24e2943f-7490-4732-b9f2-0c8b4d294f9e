import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Battery, Plus, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { batteryService } from '@/services/api/battery';
import type { BatteryType, BatteryLocation, CreateBatteryForm, BatteryStatus, BatteryCondition } from '@/types/battery';
import { toast } from 'sonner';

// Form validation schema
const createBatterySchema = z.object({
  serial_number: z.string().min(1, 'Serial number is required'),
  battery_type_id: z.string().min(1, 'Battery type is required'),
  current_location_id: z.string().optional(),
  status: z.enum(['new', 'active', 'maintenance', 'retired', 'disposed']).default('new'),
  condition: z.enum(['excellent', 'good', 'fair', 'poor', 'failed']).default('excellent'),
  purchase_date: z.string().optional(),
  installation_date: z.string().optional(),
  warranty_expiry_date: z.string().optional(),
  purchase_cost: z.number().min(0).optional(),
  supplier: z.string().optional(),
  notes: z.string().optional()
});

type CreateBatteryFormData = z.infer<typeof createBatterySchema>;

interface CreateBatteryFormProps {
  onSuccess?: (battery: any) => void;
  onCancel?: () => void;
}

export const CreateBatteryForm: React.FC<CreateBatteryFormProps> = ({
  onSuccess,
  onCancel
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [batteryTypes, setBatteryTypes] = useState<BatteryType[]>([]);
  const [locations, setLocations] = useState<BatteryLocation[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<CreateBatteryFormData>({
    resolver: zodResolver(createBatterySchema),
    defaultValues: {
      status: 'new',
      condition: 'excellent'
    }
  });

  // Load battery types and locations
  useEffect(() => {
    const loadData = async () => {
      try {
        const [typesResult, locationsResult] = await Promise.all([
          batteryService.getBatteryTypes(),
          batteryService.getBatteryLocations()
        ]);

        if (typesResult.success) {
          setBatteryTypes(typesResult.data || []);
        }

        if (locationsResult.success) {
          setLocations(locationsResult.data || []);
        }
      } catch (error) {
        console.error('Error loading form data:', error);
        toast.error('Failed to load form data');
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, []);

  const onSubmit = async (data: CreateBatteryFormData) => {
    setIsLoading(true);
    try {
      const result = await batteryService.createBattery({
        ...data,
        purchase_cost: data.purchase_cost || undefined
      });

      if (result.success) {
        toast.success('Battery created successfully');
        onSuccess?.(result.data);
      } else {
        toast.error(result.error || 'Failed to create battery');
      }
    } catch (error) {
      console.error('Error creating battery:', error);
      toast.error('Failed to create battery');
    } finally {
      setIsLoading(false);
    }
  };

  const statusOptions: { value: BatteryStatus; label: string }[] = [
    { value: 'new', label: 'New' },
    { value: 'active', label: 'Active' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'retired', label: 'Retired' },
    { value: 'disposed', label: 'Disposed' }
  ];

  const conditionOptions: { value: BatteryCondition; label: string }[] = [
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
    { value: 'failed', label: 'Failed' }
  ];

  if (loadingData) {
    return (
      <Card className="neumorphism-card">
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading form data...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="neumorphism-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Battery className="h-5 w-5" />
          Create New Battery
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="serial_number">Serial Number *</Label>
              <Input
                id="serial_number"
                {...register('serial_number')}
                placeholder="Enter serial number"
                className={errors.serial_number ? 'border-red-500' : ''}
              />
              {errors.serial_number && (
                <p className="text-sm text-red-500">{errors.serial_number.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="battery_type_id">Battery Type *</Label>
              <Select onValueChange={(value) => setValue('battery_type_id', value)}>
                <SelectTrigger className={errors.battery_type_id ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Select battery type" />
                </SelectTrigger>
                <SelectContent>
                  {batteryTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name} ({type.voltage}V, {type.capacity_ah}Ah)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.battery_type_id && (
                <p className="text-sm text-red-500">{errors.battery_type_id.message}</p>
              )}
            </div>
          </div>

          {/* Status and Location */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select onValueChange={(value) => setValue('status', value as BatteryStatus)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="condition">Condition</Label>
              <Select onValueChange={(value) => setValue('condition', value as BatteryCondition)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select condition" />
                </SelectTrigger>
                <SelectContent>
                  {conditionOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="current_location_id">Current Location</Label>
              <Select onValueChange={(value) => setValue('current_location_id', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select location" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      {location.name} ({location.location_type})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Purchase Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="purchase_cost">Purchase Cost (₦)</Label>
              <Input
                id="purchase_cost"
                type="number"
                step="0.01"
                {...register('purchase_cost', { valueAsNumber: true })}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="supplier">Supplier</Label>
              <Input
                id="supplier"
                {...register('supplier')}
                placeholder="Enter supplier name"
              />
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Additional notes about the battery..."
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Battery
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
