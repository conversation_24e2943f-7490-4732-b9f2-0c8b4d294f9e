-- ============================================================================
-- COMPLETE PROFILE FIX - FINAL SOLUTION
-- This will fix ALL profile-related issues once and for all
-- User: 44349058-db4b-4a0b-8c99-8a913d07df74 (<EMAIL>)
-- ============================================================================

-- STEP 1: Completely disable RLS to see the real state
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- STEP 2: Show current state of profiles table
SELECT 'CURRENT PROFILES STATE:' as info;
SELECT id, email, full_name, role, status, created_at 
FROM public.profiles 
ORDER BY created_at DESC;

-- STEP 3: Check for the specific problematic user
SELECT 'CHECKING SPECIFIC USER:' as info;
SELECT id, email, full_name, role, status, created_at 
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74' 
   OR email = '<EMAIL>';

-- STEP 4: Clean up any duplicate or conflicting profiles
-- Remove any profiles with the same email but different ID
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != '44349058-db4b-4a0b-8c99-8a913d07df74';

-- STEP 5: Remove any profiles with the same ID but different email
DELETE FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74' 
  AND email != '<EMAIL>';

-- STEP 6: Now safely create/update the correct profile
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

-- STEP 7: Verify the profile was created/updated correctly
SELECT 'PROFILE AFTER FIX:' as info;
SELECT id, email, full_name, role, status, created_at, updated_at 
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';

-- STEP 8: Drop ALL existing RLS policies (comprehensive cleanup)
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_manager_department" ON public.profiles;
DROP POLICY IF EXISTS "profiles_hr_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_own" ON public.profiles;
DROP POLICY IF EXISTS "Staff-admin can view profiles" ON public.profiles;
DROP POLICY IF EXISTS "profiles_read_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_update" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;

-- STEP 9: Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- STEP 10: Create simple, non-conflicting policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- STEP 11: Grant all necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;
GRANT ALL ON public.profiles TO service_role;

-- STEP 12: Create/update the profile creation trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
    'active',
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = NOW();
  
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 13: Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- STEP 14: Final verification
SELECT 'FINAL VERIFICATION:' as info;

-- Check RLS status
SELECT 'RLS Status:' as check_type, 
       schemaname, 
       tablename, 
       rowsecurity,
       (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public') as policy_count
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- List active policies
SELECT 'Active Policies:' as check_type,
       policyname, 
       cmd as operation,
       CASE WHEN qual IS NOT NULL THEN 'Has USING clause' ELSE 'No USING clause' END as using_clause,
       CASE WHEN with_check IS NOT NULL THEN 'Has WITH CHECK clause' ELSE 'No WITH CHECK clause' END as check_clause
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;

-- Verify the specific user profile
SELECT 'User Profile:' as check_type,
       id, email, full_name, role, status, created_at, updated_at
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Check for any remaining duplicates
SELECT 'Duplicate Check:' as check_type,
       email, COUNT(*) as count
FROM public.profiles 
GROUP BY email 
HAVING COUNT(*) > 1;

SELECT 'FIX COMPLETED SUCCESSFULLY!' as final_status;
