import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Plus, 
  Search, 
  Filter,
  DollarSign,
  Calendar,
  FileText,
  CheckCircle,
  Clock,
  AlertTriangle
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ExpenseManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newExpense, setNewExpense] = useState({
    title: "",
    description: "",
    amount: "",
    category: "",
    expense_date: new Date().toISOString().split('T')[0],
    vendor_name: "",
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch expenses - simplified query without join
  const { data: expenses, isLoading } = useQuery({
    queryKey: ['staff-admin-expenses'],
    queryFn: async () => {
      const { data: expensesData, error: expensesError } = await supabase
        .from('expenses')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (expensesError) throw expensesError;

      // Fetch user profiles separately to avoid join issues
      const userIds = [...new Set(expensesData?.map(exp => exp.created_by).filter(Boolean) || [])];
      const { data: profiles, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name')
        .in('id', userIds);
      
      if (profilesError) {
        console.warn('Could not fetch profiles:', profilesError);
      }

      // Combine the data
      const expensesWithProfiles = expensesData?.map(expense => ({
        ...expense,
        creator_name: profiles?.find(p => p.id === expense.created_by)?.full_name || 'Unknown'
      })) || [];

      return expensesWithProfiles;
    },
  });

  // Create expense mutation
  const createExpenseMutation = useMutation({
    mutationFn: async (expenseData: any) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('expenses')
        .insert([{
          ...expenseData,
          amount: parseFloat(expenseData.amount),
          created_by: user.id,
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['staff-admin-expenses'] });
      toast({
        title: "Success",
        description: "Expense created successfully",
      });
      setIsDialogOpen(false);
      setNewExpense({
        title: "",
        description: "",
        amount: "",
        category: "",
        expense_date: new Date().toISOString().split('T')[0],
        vendor_name: "",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create expense",
        variant: "destructive",
      });
    },
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Rejected</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const filteredExpenses = expenses?.filter(expense =>
    expense.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expense.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expense.vendor_name?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6" data-aos="fade-up">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Expense Management</h1>
          <p className="text-muted-foreground">Track and manage company expenses</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-[#ff1c04] hover:bg-[#e01703] text-white">
              <Plus className="h-4 w-4 mr-2" />
              Add Expense
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Expense</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={newExpense.title}
                  onChange={(e) => setNewExpense({...newExpense, title: e.target.value})}
                  placeholder="Expense title"
                />
              </div>
              <div>
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  value={newExpense.amount}
                  onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={newExpense.category} onValueChange={(value) => setNewExpense({...newExpense, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="office_supplies">Office Supplies</SelectItem>
                    <SelectItem value="travel">Travel</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="utilities">Utilities</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="vendor_name">Vendor</Label>
                <Input
                  id="vendor_name"
                  value={newExpense.vendor_name}
                  onChange={(e) => setNewExpense({...newExpense, vendor_name: e.target.value})}
                  placeholder="Vendor name"
                />
              </div>
              <div>
                <Label htmlFor="expense_date">Date</Label>
                <Input
                  id="expense_date"
                  type="date"
                  value={newExpense.expense_date}
                  onChange={(e) => setNewExpense({...newExpense, expense_date: e.target.value})}
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newExpense.description}
                  onChange={(e) => setNewExpense({...newExpense, description: e.target.value})}
                  placeholder="Expense description"
                />
              </div>
              <Button 
                onClick={() => createExpenseMutation.mutate(newExpense)}
                disabled={!newExpense.title || !newExpense.amount || !newExpense.category}
                className="w-full bg-[#ff1c04] hover:bg-[#e01703]"
              >
                Create Expense
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4" data-aos="fade-up" data-aos-delay="100">
        <Card className="border-[#ff1c04]/20 hover:border-[#ff1c04]/40 transition-colors">
          <CardContent className="flex items-center p-6">
            <DollarSign className="h-8 w-8 text-[#ff1c04]" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
              <p className="text-2xl font-bold">
                ₦{expenses?.reduce((sum, exp) => sum + Number(exp.amount), 0).toLocaleString() || 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-[#0FA0CE]/20 hover:border-[#0FA0CE]/40 transition-colors">
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-[#0FA0CE]" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Approved</p>
              <p className="text-2xl font-bold">{expenses?.filter(e => e.status === 'approved').length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-yellow-500/20 hover:border-yellow-500/40 transition-colors">
          <CardContent className="flex items-center p-6">
            <Clock className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Pending</p>
              <p className="text-2xl font-bold">{expenses?.filter(e => e.status === 'pending').length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card className="border-red-500/20 hover:border-red-500/40 transition-colors">
          <CardContent className="flex items-center p-6">
            <AlertTriangle className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Rejected</p>
              <p className="text-2xl font-bold">{expenses?.filter(e => e.status === 'rejected').length || 0}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card data-aos="fade-up" data-aos-delay="200">
        <CardContent className="p-4">
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search expenses..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Expenses Table */}
      <Card data-aos="fade-up" data-aos-delay="300">
        <CardHeader>
          <CardTitle>Recent Expenses</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">Loading expenses...</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4">Title</th>
                    <th className="text-left p-4">Amount</th>
                    <th className="text-left p-4">Category</th>
                    <th className="text-left p-4">Vendor</th>
                    <th className="text-left p-4">Date</th>
                    <th className="text-left p-4">Status</th>
                    <th className="text-left p-4">Created By</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredExpenses?.map((expense) => (
                    <tr key={expense.id} className="border-b hover:bg-muted/50">
                      <td className="p-4 font-medium">{expense.title}</td>
                      <td className="p-4">₦{Number(expense.amount).toLocaleString()}</td>
                      <td className="p-4 capitalize">{expense.category}</td>
                      <td className="p-4">{expense.vendor_name || 'N/A'}</td>
                      <td className="p-4">{new Date(expense.expense_date).toLocaleDateString()}</td>
                      <td className="p-4">{getStatusBadge(expense.status)}</td>
                      <td className="p-4">{expense.creator_name}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ExpenseManagement;
