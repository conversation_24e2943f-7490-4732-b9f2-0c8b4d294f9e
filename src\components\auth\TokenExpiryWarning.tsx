import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTokenExpiryWarning } from '@/hooks/useTokenExpiryWarning';
import { Clock, RefreshCw, AlertTriangle, Shield } from 'lucide-react';

interface TokenExpiryWarningProps {
  showAlways?: boolean;           // Show even when not expiring
  warningThresholds?: number[];   // Custom warning thresholds in minutes
  enableAutoRefresh?: boolean;    // Enable automatic token refresh
  className?: string;
}

export const TokenExpiryWarning: React.FC<TokenExpiryWarningProps> = ({
  showAlways = false,
  warningThresholds = [15, 5, 1],
  enableAutoRefresh = true,
  className = ''
}) => {
  const {
    isExpired,
    isExpiring,
    minutesToExpiry,
    timeToExpiry,
    shouldRefresh,
    hasValidToken,
    refreshToken,
    getFormattedTimeToExpiry,
    isInWarningPeriod
  } = useTokenExpiryWarning({
    warningThresholds,
    enableAutoRefresh,
    enableNotifications: false, // We'll handle notifications in this component
  });

  // Don't show if no valid token
  if (!hasValidToken) {
    return null;
  }

  // Don't show if not expiring and showAlways is false
  if (!showAlways && !isExpiring && !shouldRefresh) {
    return null;
  }

  const getAlertVariant = () => {
    if (isExpired) return 'destructive';
    if (minutesToExpiry <= 1) return 'destructive';
    if (minutesToExpiry <= 5) return 'destructive';
    return 'default';
  };

  const getIcon = () => {
    if (isExpired) return <AlertTriangle className="h-4 w-4" />;
    if (isExpiring) return <Clock className="h-4 w-4" />;
    return <Shield className="h-4 w-4" />;
  };

  const getTitle = () => {
    if (isExpired) return 'Session Expired';
    if (minutesToExpiry <= 1) return 'Session Expiring Soon';
    if (minutesToExpiry <= 5) return 'Session Expiring';
    if (shouldRefresh) return 'Session Refresh Recommended';
    return 'Session Status';
  };

  const getDescription = () => {
    if (isExpired) {
      return 'Your session has expired. Please log in again to continue.';
    }
    
    if (minutesToExpiry <= 1) {
      return 'Your session expires in less than 1 minute. Please refresh or you will be logged out.';
    }
    
    if (isExpiring) {
      return `Your session expires in ${minutesToExpiry} minute${minutesToExpiry > 1 ? 's' : ''}. Consider refreshing your session.`;
    }
    
    if (shouldRefresh) {
      return 'Your session should be refreshed soon to maintain uninterrupted access.';
    }
    
    return `Session expires in ${getFormattedTimeToExpiry()}`;
  };

  return (
    <Alert variant={getAlertVariant()} className={className}>
      {getIcon()}
      <div className="flex-1">
        <div className="font-medium">{getTitle()}</div>
        <AlertDescription className="mt-1">
          {getDescription()}
        </AlertDescription>
      </div>
      
      <div className="flex items-center gap-2 ml-4">
        {/* Time Badge */}
        <Badge 
          variant={isInWarningPeriod(5) ? 'destructive' : 'secondary'}
          className="flex items-center gap-1"
        >
          <Clock className="h-3 w-3" />
          {getFormattedTimeToExpiry()}
        </Badge>
        
        {/* Refresh Button */}
        {!isExpired && (
          <Button
            variant="outline"
            size="sm"
            onClick={refreshToken}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-3 w-3" />
            Refresh
          </Button>
        )}
      </div>
    </Alert>
  );
};

/**
 * Compact version for header/navbar use
 */
export const CompactTokenExpiryWarning: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  const {
    isExpired,
    isExpiring,
    minutesToExpiry,
    shouldRefresh,
    hasValidToken,
    refreshToken,
    getFormattedTimeToExpiry,
    isInWarningPeriod
  } = useTokenExpiryWarning({
    enableNotifications: false,
  });

  // Don't show if no valid token or not in warning period
  if (!hasValidToken || (!isExpiring && !shouldRefresh)) {
    return null;
  }

  const getBadgeVariant = () => {
    if (isExpired || minutesToExpiry <= 1) return 'destructive';
    if (minutesToExpiry <= 5) return 'destructive';
    return 'secondary';
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge 
        variant={getBadgeVariant()}
        className="flex items-center gap-1 cursor-pointer"
        onClick={refreshToken}
        title="Click to refresh session"
      >
        <Clock className="h-3 w-3" />
        {isExpired ? 'Expired' : getFormattedTimeToExpiry()}
      </Badge>
      
      {!isExpired && (
        <Button
          variant="ghost"
          size="sm"
          onClick={refreshToken}
          className="h-6 w-6 p-0"
          title="Refresh session"
        >
          <RefreshCw className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

/**
 * Status indicator for development/debug use
 */
export const TokenStatusIndicator: React.FC<{
  showDetails?: boolean;
  className?: string;
}> = ({ showDetails = false, className = '' }) => {
  const {
    isExpired,
    isExpiring,
    minutesToExpiry,
    hasValidToken,
    tokenAge,
    getFormattedTimeToExpiry
  } = useTokenExpiryWarning({
    enableNotifications: false,
  });

  if (!hasValidToken) {
    return (
      <Badge variant="destructive" className={className}>
        No Token
      </Badge>
    );
  }

  const getStatus = () => {
    if (isExpired) return { text: 'Expired', variant: 'destructive' as const };
    if (minutesToExpiry <= 1) return { text: 'Critical', variant: 'destructive' as const };
    if (minutesToExpiry <= 5) return { text: 'Warning', variant: 'destructive' as const };
    if (isExpiring) return { text: 'Expiring', variant: 'secondary' as const };
    return { text: 'Valid', variant: 'default' as const };
  };

  const status = getStatus();

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Badge variant={status.variant} className="flex items-center gap-1">
        <Shield className="h-3 w-3" />
        {status.text}
      </Badge>
      
      {showDetails && (
        <div className="text-xs text-muted-foreground">
          <div>Expires: {getFormattedTimeToExpiry()}</div>
          <div>Age: {Math.floor(tokenAge / (1000 * 60))}m</div>
        </div>
      )}
    </div>
  );
};
