import { Database } from "@/integrations/supabase/types";
import { ProfileWithDetails, TaskWithAssignee } from "./common";

export interface ProjectWithAssignments {
  id: string;
  name: string;
  description: string | null;
  client_name: string | null;
  budget: number | null;
  budget_spent: number | null;
  location: string | null;
  start_date: string | null;
  end_date: string | null;
  actual_end_date: string | null;
  status: string;
  priority: string | null;
  manager_id: string | null;
  department_id: string | null;
  progress_percentage: number | null;
  completion_percentage: number | null;
  actual_hours: number | null;
  estimated_hours: number | null;
  health_score: number | null;
  risk_level: string | null;
  category: string | null;
  tags: string[] | null;
  template_id: string | null;
  metadata: any | null;
  last_activity_at: string | null;
  archived_at: string | null;
  archived_by: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
  project_assignments: Array<{
    id: string;
    project_id: string | null;
    staff_id: string | null;
    profiles?: {
      full_name: string;
    };
    created_at: string | null;
    updated_at: string | null;
  }>;
}

export interface TeamMember extends ProfileWithDetails {
  tasks?: TaskWithAssignee[];
}