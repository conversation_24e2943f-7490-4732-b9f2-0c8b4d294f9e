import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Copy, RotateCcw, Lock, Unlock, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const Base64Tool: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [activeTab, setActiveTab] = useState('encode');
  const [isValid, setIsValid] = useState(true);
  const [error, setError] = useState('');

  const encodeBase64 = (text: string): string => {
    try {
      return btoa(unescape(encodeURIComponent(text)));
    } catch (error) {
      throw new Error('Invalid input for encoding');
    }
  };

  const decodeBase64 = (text: string): string => {
    try {
      // Check if it's valid Base64
      if (!/^[A-Za-z0-9+/]*={0,2}$/.test(text)) {
        throw new Error('Invalid Base64 format');
      }
      return decodeURIComponent(escape(atob(text)));
    } catch (error) {
      throw new Error('Invalid Base64 string');
    }
  };

  const validateInput = (text: string, mode: string) => {
    if (!text.trim()) {
      setIsValid(true);
      setError('');
      return;
    }

    if (mode === 'decode') {
      try {
        decodeBase64(text);
        setIsValid(true);
        setError('');
      } catch (err) {
        setIsValid(false);
        setError(err instanceof Error ? err.message : 'Invalid input');
      }
    } else {
      setIsValid(true);
      setError('');
    }
  };

  useEffect(() => {
    validateInput(inputText, activeTab);
    if (inputText.trim()) {
      handleProcess();
    } else {
      setOutputText('');
    }
  }, [inputText, activeTab]);

  const handleProcess = () => {
    if (!inputText.trim()) {
      setOutputText('');
      return;
    }

    try {
      let result: string;
      if (activeTab === 'encode') {
        result = encodeBase64(inputText);
      } else {
        result = decodeBase64(inputText);
      }
      setOutputText(result);
      setIsValid(true);
      setError('');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Processing failed';
      setError(errorMessage);
      setIsValid(false);
      setOutputText('');
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setOutputText('');
    setError('');
    setIsValid(true);
  };

  const swapInputOutput = () => {
    if (!outputText) return;
    const temp = inputText;
    setInputText(outputText);
    setOutputText(temp);
    setActiveTab(activeTab === 'encode' ? 'decode' : 'encode');
  };

  const getEncodingStats = () => {
    if (!inputText) return null;
    
    const bytes = new TextEncoder().encode(inputText).length;
    const chars = inputText.length;
    const lines = inputText.split('\n').length;
    
    return { bytes, chars, lines };
  };

  const getBase64Stats = () => {
    if (!outputText || activeTab !== 'encode') return null;
    
    const padding = (outputText.match(/=/g) || []).length;
    const withoutPadding = outputText.length - padding;
    const originalBytes = Math.floor(withoutPadding * 3 / 4);
    
    return { padding, originalBytes };
  };

  const stats = getEncodingStats();
  const base64Stats = getBase64Stats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 border-green-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-green-800">
            <div className="p-2 bg-green-100 rounded-lg">
              <Lock className="h-6 w-6 text-green-600" />
            </div>
            {title || 'Base64 Encoder/Decoder'}
          </CardTitle>
          <p className="text-green-600 text-sm">
            Encode or decode text using Base64 with real-time validation
          </p>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-2 bg-green-100">
              <TabsTrigger value="encode" className="flex items-center gap-2 data-[state=active]:bg-white">
                <Lock className="h-4 w-4" />
                Encode to Base64
              </TabsTrigger>
              <TabsTrigger value="decode" className="flex items-center gap-2 data-[state=active]:bg-white">
                <Unlock className="h-4 w-4" />
                Decode from Base64
              </TabsTrigger>
            </TabsList>

            <div className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-semibold text-gray-700">
                    {activeTab === 'encode' ? '📝 Text to Encode:' : '🔐 Base64 to Decode:'}
                  </label>
                  {inputText && (
                    <div className="flex items-center gap-2">
                      {isValid ? (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  )}
                </div>
                <Textarea
                  placeholder={activeTab === 'encode' 
                    ? "Enter text to encode to Base64..." 
                    : "Enter Base64 string to decode..."
                  }
                  value={inputText}
                  onChange={(e) => setInputText(e.target.value)}
                  className={`min-h-[140px] resize-none border-2 transition-colors ${
                    error ? 'border-red-300 focus:border-red-400' : 'border-green-200 focus:border-green-400'
                  } ${activeTab === 'decode' ? 'font-mono text-sm' : ''}`}
                />
                
                {error && (
                  <div className="flex items-center gap-2 text-red-600 text-sm">
                    <AlertCircle className="h-4 w-4" />
                    {error}
                  </div>
                )}

                {stats && (
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      📊 {stats.chars} characters
                    </Badge>
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                      💾 {stats.bytes} bytes
                    </Badge>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      📄 {stats.lines} lines
                    </Badge>
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  onClick={swapInputOutput} 
                  disabled={!outputText}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="h-4 w-4" />
                  Swap
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  Clear All
                </Button>
              </div>

              {outputText && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-semibold text-gray-700">
                      {activeTab === 'encode' ? '🔐 Encoded Base64:' : '📝 Decoded Text:'}
                    </label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(outputText)}
                      className="flex items-center gap-2"
                    >
                      <Copy className="h-4 w-4" />
                      Copy
                    </Button>
                  </div>
                  <Textarea
                    value={outputText}
                    readOnly
                    className={`min-h-[140px] bg-gray-50 border-2 border-gray-200 ${
                      activeTab === 'encode' ? 'font-mono text-sm' : ''
                    }`}
                  />
                  
                  <div className="flex gap-2 flex-wrap">
                    <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                      📊 {outputText.length} characters
                    </Badge>
                    {base64Stats && (
                      <>
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                          🔢 {base64Stats.originalBytes} original bytes
                        </Badge>
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          ➕ {base64Stats.padding} padding chars
                        </Badge>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Tabs>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            About Base64 Encoding
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Common Uses:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Email attachments (MIME)</li>
                <li>Data URLs in web development</li>
                <li>API data transmission</li>
                <li>Configuration files</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Key Facts:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Increases size by ~33%</li>
                <li>Uses A-Z, a-z, 0-9, +, /</li>
                <li>Padding with = characters</li>
                <li>Safe for text transmission</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Base64Tool;
