
import { useAuth } from "@/components/auth/AuthProvider";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { AlertCircle, Battery, CheckCircle, Eye, Plus } from "lucide-react";
import { useState } from "react";
import { BatteryReportForm } from "./BatteryReportForm";

export const BatteryReports = () => {
  const { userProfile } = useAuth();
  const [showNewReportDialog, setShowNewReportDialog] = useState(false);

  const { data: batteryReports, isLoading, error: queryError } = useQuery({
    queryKey: ['battery-reports', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) {
        console.log('No user profile available for battery reports');
        return [];
      }

      try {
        // Try to get all battery reports first
        let { data, error } = await supabase
          .from('battery_reports')
          .select(`
            *,
            reporter:profiles!reporter_id(id, full_name, email)
          `)
          .order('created_at', { ascending: false });

        // If RLS blocks access, try user-specific query
        if (error && (error.code === 'PGRST116' || error.message?.includes('RLS'))) {
          console.log('Trying user-specific battery reports query');
          const result = await supabase
            .from('battery_reports')
            .select(`
              *,
              reporter:profiles!reporter_id(id, full_name, email)
            `)
            .eq('reporter_id', userProfile.id)
            .order('created_at', { ascending: false });

          data = result.data;
          error = result.error;
        }

        // If table doesn't exist, return empty array
        if (error && error.code === '42P01') {
          console.log('Battery reports table does not exist');
          return [];
        }

        if (error) {
          console.error('Battery reports query error:', error);
          throw error;
        }

        console.log('Battery reports loaded:', data?.length || 0, 'reports');
        return data || [];
      } catch (err) {
        console.error('Failed to load battery reports:', err);
        return [];
      }
    },
    enabled: !!userProfile?.id,
    retry: 2,
    retryDelay: 1000
  });

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'default';
      case 'good': return 'secondary';
      case 'fair': return 'outline';
      case 'poor': return 'destructive';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading battery reports...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Battery Reports</h2>
          <p className="text-muted-foreground">Monitor battery system health and performance</p>
        </div>
        <Dialog open={showNewReportDialog} onOpenChange={setShowNewReportDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Battery Report
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Battery Report</DialogTitle>
            </DialogHeader>
            <BatteryReportForm
              onSuccess={() => setShowNewReportDialog(false)}
              onCancel={() => setShowNewReportDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {batteryReports && batteryReports.length > 0 ? (
          batteryReports.map((report) => (
            <Card key={report.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Battery className="h-4 w-4" />
                    {report.site_name || 'Battery System'}
                  </CardTitle>
                  <Badge variant={getHealthStatusColor(report.health_status)}>
                    {report.health_status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Voltage:</span>
                      <div className="font-medium">{report.battery_voltage}V</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Capacity:</span>
                      <div className="font-medium">{report.current_capacity}%</div>
                    </div>
                  </div>
                  
                  {report.maintenance_required && (
                    <div className="flex items-center gap-2 text-amber-600">
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm">Maintenance Required</span>
                    </div>
                  )}
                  
                  {!report.maintenance_required && (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="h-4 w-4" />
                      <span className="text-sm">Operating Normally</span>
                    </div>
                  )}
                  
                  <div className="text-xs text-muted-foreground">
                    Report Date: {format(new Date(report.report_date), 'MMM dd, yyyy')}
                  </div>
                </div>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full mt-4" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Full Report
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Battery Report Details</DialogTitle>
                      <DialogDescription>
                        Detailed view of battery system health metrics, performance data, and maintenance information.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Site Name</label>
                          <p className="text-sm">{report.site_name || 'Battery System'}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Health Status</label>
                          <p className="text-sm">
                            <Badge variant={getHealthStatusColor(report.health_status)}>
                              {report.health_status}
                            </Badge>
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Voltage</label>
                          <p className="text-sm">{report.battery_voltage}V</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Capacity</label>
                          <p className="text-sm">{report.current_capacity}%</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Report Date</label>
                          <p className="text-sm">{format(new Date(report.report_date), 'MMM dd, yyyy')}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Maintenance Required</label>
                          <p className="text-sm">{report.maintenance_required ? 'Yes' : 'No'}</p>
                        </div>
                      </div>
                      {report.maintenance_notes && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Maintenance Notes</label>
                          <p className="text-sm">{report.maintenance_notes}</p>
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full">
            <Card>
              <CardContent className="text-center py-12">
                <Battery className="h-16 w-16 mx-auto mb-4 opacity-50 text-muted-foreground" />
                <h3 className="text-lg font-medium mb-2">No Battery Reports Found</h3>
                <p className="text-muted-foreground mb-4">
                  {queryError
                    ? `Error loading reports: ${queryError.message}`
                    : "You haven't submitted any battery reports yet."
                  }
                </p>
                <div className="space-y-2 text-sm text-muted-foreground">
                  <p>• Click "New Battery Report" to create your first report</p>
                  <p>• Reports help track battery health and maintenance</p>
                  <p>• All reports are automatically saved and tracked</p>
                </div>
                {process.env.NODE_ENV === 'development' && (
                  <div className="mt-4 p-3 bg-muted rounded text-xs text-left">
                    <strong>Debug Info:</strong><br/>
                    User ID: {userProfile?.id || 'Not available'}<br/>
                    Reports Count: {batteryReports?.length || 0}<br/>
                    Loading: {isLoading ? 'Yes' : 'No'}<br/>
                    Error: {queryError?.message || 'None'}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};
