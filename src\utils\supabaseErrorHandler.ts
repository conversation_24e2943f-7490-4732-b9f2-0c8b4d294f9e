import { PostgrestError } from '@supabase/supabase-js';

export interface SupabaseErrorInfo {
  isRLSError: boolean;
  isCORSError: boolean;
  isTableMissing: boolean;
  isPermissionDenied: boolean;
  is406Error: boolean;
  shouldRetry: boolean;
  fallbackData?: any;
  userMessage: string;
  technicalMessage: string;
}

/**
 * Comprehensive error handler for Supabase operations
 * Specifically handles RLS, CORS, and 406 errors
 */
export function analyzeSupabaseError(error: any): SupabaseErrorInfo {
  const errorInfo: SupabaseErrorInfo = {
    isRLSError: false,
    isCORSError: false,
    isTableMissing: false,
    isPermissionDenied: false,
    is406Error: false,
    shouldRetry: false,
    userMessage: 'An unexpected error occurred',
    technicalMessage: error?.message || 'Unknown error'
  };

  // Handle different types of errors
  if (error) {
    const message = error.message?.toLowerCase() || '';
    const code = error.code;
    const status = error.status;

    // 406 Not Acceptable errors (often RLS-related)
    if (status === 406 || code === '406') {
      errorInfo.is406Error = true;
      errorInfo.isRLSError = true;
      errorInfo.userMessage = 'Access restricted. Please check your permissions.';
      errorInfo.technicalMessage = `406 Error: ${error.message}`;
    }

    // CORS errors
    if (message.includes('cors') || message.includes('cross-origin') || message.includes('access-control')) {
      errorInfo.isCORSError = true;
      errorInfo.userMessage = 'Network configuration issue. Please try again.';
      errorInfo.technicalMessage = `CORS Error: ${error.message}`;
    }

    // Table/relation missing errors
    if (code === 'PGRST116' || code === '42P01' || message.includes('relation') && message.includes('does not exist')) {
      errorInfo.isTableMissing = true;
      errorInfo.userMessage = 'Data structure not found. Using default values.';
      errorInfo.technicalMessage = `Table Missing: ${error.message}`;
    }

    // Permission denied errors
    if (code === 'PGRST301' || message.includes('permission denied') || message.includes('insufficient_privilege')) {
      errorInfo.isPermissionDenied = true;
      errorInfo.isRLSError = true;
      errorInfo.userMessage = 'You do not have permission to access this data.';
      errorInfo.technicalMessage = `Permission Denied: ${error.message}`;
    }

    // RLS policy violations
    if (message.includes('row-level security') || message.includes('policy') || code === 'PGRST301') {
      errorInfo.isRLSError = true;
      errorInfo.userMessage = 'Access restricted by security policy.';
      errorInfo.technicalMessage = `RLS Error: ${error.message}`;
    }

    // Network errors that might be retryable
    if (message.includes('network') || message.includes('timeout') || message.includes('fetch')) {
      errorInfo.shouldRetry = true;
      errorInfo.userMessage = 'Network error. Please try again.';
      errorInfo.technicalMessage = `Network Error: ${error.message}`;
    }
  }

  return errorInfo;
}

/**
 * Create fallback data for common scenarios
 */
export function createFallbackData(tableName: string, userId?: string): any {
  const currentYear = new Date().getFullYear();

  switch (tableName) {
    case 'leave_balances':
      return [
        {
          id: 'fallback-annual',
          user_id: userId || 'unknown',
          leave_type: 'annual',
          year: currentYear,
          total_days: 25,
          used_days: 0,
          remaining_days: 25,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 'fallback-sick',
          user_id: userId || 'unknown',
          leave_type: 'sick',
          year: currentYear,
          total_days: 10,
          used_days: 0,
          remaining_days: 10,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

    case 'tasks':
      return [];

    case 'profiles':
      return {
        id: userId || 'unknown',
        full_name: 'User',
        email: '<EMAIL>',
        role: 'staff',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

    default:
      return [];
  }
}

/**
 * Enhanced query wrapper that handles RLS and CORS errors gracefully
 */
export async function safeSupabaseQuery<T>(
  queryFn: () => Promise<{ data: T | null; error: PostgrestError | null }>,
  tableName: string,
  userId?: string,
  options: {
    enableFallback?: boolean;
    logErrors?: boolean;
    retryCount?: number;
  } = {}
): Promise<{ data: T | null; error: SupabaseErrorInfo | null; usedFallback: boolean }> {
  const { enableFallback = true, logErrors = true, retryCount = 0 } = options;

  try {
    const result = await queryFn();

    if (result.error) {
      const errorInfo = analyzeSupabaseError(result.error);

      if (logErrors) {
        console.warn(`Supabase query error for ${tableName}:`, {
          error: result.error,
          analysis: errorInfo,
          userId,
          retryCount
        });
      }

      // If it's a retryable error and we haven't exceeded retry limit
      if (errorInfo.shouldRetry && retryCount < 2) {
        console.log(`Retrying query for ${tableName} (attempt ${retryCount + 1})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
        return safeSupabaseQuery(queryFn, tableName, userId, { ...options, retryCount: retryCount + 1 });
      }

      // If fallback is enabled and it's an appropriate error type
      if (enableFallback && (errorInfo.isTableMissing || errorInfo.isRLSError || errorInfo.is406Error)) {
        const fallbackData = createFallbackData(tableName, userId) as T;
        
        if (logErrors) {
          console.log(`Using fallback data for ${tableName}:`, fallbackData);
        }

        return {
          data: fallbackData,
          error: errorInfo,
          usedFallback: true
        };
      }

      return {
        data: null,
        error: errorInfo,
        usedFallback: false
      };
    }

    return {
      data: result.data,
      error: null,
      usedFallback: false
    };

  } catch (error: any) {
    const errorInfo = analyzeSupabaseError(error);

    if (logErrors) {
      console.error(`Unexpected error in safeSupabaseQuery for ${tableName}:`, error);
    }

    if (enableFallback) {
      const fallbackData = createFallbackData(tableName, userId) as T;
      return {
        data: fallbackData,
        error: errorInfo,
        usedFallback: true
      };
    }

    return {
      data: null,
      error: errorInfo,
      usedFallback: false
    };
  }
}

/**
 * Utility to check if the current environment might have CORS issues
 */
export function detectCORSEnvironment(): { 
  isDevelopment: boolean; 
  isLocalhost: boolean; 
  mightHaveCORS: boolean;
  recommendations: string[];
} {
  const hostname = typeof window !== 'undefined' ? window.location.hostname : 'unknown';
  const protocol = typeof window !== 'undefined' ? window.location.protocol : 'unknown';
  
  const isDevelopment = hostname === 'localhost' || hostname === '127.0.0.1' || hostname.includes('localhost');
  const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
  const mightHaveCORS = !isDevelopment && protocol === 'https:';

  const recommendations: string[] = [];

  if (isDevelopment) {
    recommendations.push('Development environment detected - CORS should be minimal');
  }

  if (mightHaveCORS) {
    recommendations.push('Production environment - ensure CORS is configured in Supabase');
    recommendations.push('Check that your domain is added to Supabase allowed origins');
  }

  if (protocol === 'http:' && !isDevelopment) {
    recommendations.push('HTTP detected in non-dev environment - consider using HTTPS');
  }

  return {
    isDevelopment,
    isLocalhost,
    mightHaveCORS,
    recommendations
  };
}
