import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar, 
  DollarSign, 
  Package, 
  User, 
  Building, 
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface ProcurementRequestDetailsProps {
  request: any;
  onClose?: () => void;
}

const STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800',
  manager_review: 'bg-blue-100 text-blue-800',
  approved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  admin_processing: 'bg-purple-100 text-purple-800',
  accountant_review: 'bg-orange-100 text-orange-800',
  invoice_generated: 'bg-indigo-100 text-indigo-800',
  procured: 'bg-emerald-100 text-emerald-800',
  cancelled: 'bg-gray-100 text-gray-800'
};

const PRIORITY_COLORS = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  urgent: 'bg-red-100 text-red-800'
};

export const ProcurementRequestDetails: React.FC<ProcurementRequestDetailsProps> = ({ 
  request, 
  onClose 
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'procured':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'rejected':
      case 'cancelled':
        return <XCircle className="h-5 w-5 text-red-600" />;
      case 'pending':
      case 'manager_review':
      case 'admin_processing':
      case 'accountant_review':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div>
          <h2 className="text-2xl font-bold">{request.title}</h2>
          <p className="text-muted-foreground">Request #{request.request_number}</p>
        </div>
        <div className="flex items-center gap-2">
          {getStatusIcon(request.status)}
          <Badge className={STATUS_COLORS[request.status as keyof typeof STATUS_COLORS]}>
            {request.status.replace('_', ' ').toUpperCase()}
          </Badge>
          <Badge className={PRIORITY_COLORS[request.priority as keyof typeof PRIORITY_COLORS]}>
            {request.priority.toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Request Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Requested by:</span>
              <span className="font-medium">
                {request.requested_by_profile?.full_name || 'Unknown'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Created:</span>
              <span className="font-medium">{formatDate(request.created_at)}</span>
            </div>

            {request.department && (
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Department:</span>
                <span className="font-medium">{request.department.name}</span>
              </div>
            )}

            {request.required_by_date && (
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Required by:</span>
                <span className="font-medium">{formatDate(request.required_by_date)}</span>
              </div>
            )}

            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Estimated Cost:</span>
              <span className="font-medium">{formatCurrency(request.estimated_total_cost)}</span>
            </div>

            {request.actual_total_cost > 0 && (
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Actual Cost:</span>
                <span className="font-medium">{formatCurrency(request.actual_total_cost)}</span>
              </div>
            )}
          </div>

          {request.description && (
            <div>
              <h4 className="font-medium mb-2">Description</h4>
              <p className="text-sm text-muted-foreground">{request.description}</p>
            </div>
          )}

          <div>
            <h4 className="font-medium mb-2">Business Justification</h4>
            <p className="text-sm text-muted-foreground">{request.justification}</p>
          </div>

          {request.notes && (
            <div>
              <h4 className="font-medium mb-2">Additional Notes</h4>
              <p className="text-sm text-muted-foreground">{request.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Items */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Items ({request.procurement_items?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {request.procurement_items && request.procurement_items.length > 0 ? (
            <div className="space-y-4">
              {request.procurement_items.map((item: any, index: number) => (
                <div key={item.id || index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium">{item.item_name}</h4>
                    <Badge variant="outline">
                      {item.procurement_status?.replace('_', ' ').toUpperCase() || 'PENDING'}
                    </Badge>
                  </div>
                  
                  {item.description && (
                    <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                  )}
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Quantity:</span>
                      <p className="font-medium">{item.quantity} {item.unit_of_measurement}</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground">Unit Cost:</span>
                      <p className="font-medium">{formatCurrency(item.estimated_unit_cost)}</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground">Total Cost:</span>
                      <p className="font-medium">{formatCurrency(item.estimated_total_cost)}</p>
                    </div>
                    
                    {item.vendor_name && (
                      <div>
                        <span className="text-muted-foreground">Vendor:</span>
                        <p className="font-medium">{item.vendor_name}</p>
                      </div>
                    )}
                  </div>

                  {(item.brand_preference || item.model_specification) && (
                    <div className="mt-2 pt-2 border-t">
                      {item.brand_preference && (
                        <p className="text-sm">
                          <span className="text-muted-foreground">Brand:</span> {item.brand_preference}
                        </p>
                      )}
                      {item.model_specification && (
                        <p className="text-sm">
                          <span className="text-muted-foreground">Model:</span> {item.model_specification}
                        </p>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <p className="text-center text-muted-foreground py-4">No items found</p>
          )}
        </CardContent>
      </Card>

      {/* Approval History */}
      {request.procurement_approvals && request.procurement_approvals.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Approval History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {request.procurement_approvals.map((approval: any, index: number) => (
                <div key={approval.id || index} className="flex items-start gap-4 p-4 border rounded-lg">
                  <div className="flex-shrink-0">
                    {approval.status === 'approved' ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : approval.status === 'rejected' ? (
                      <XCircle className="h-5 w-5 text-red-600" />
                    ) : (
                      <Clock className="h-5 w-5 text-yellow-600" />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{approval.approval_step.replace('_', ' ').toUpperCase()}</span>
                      <Badge variant={approval.status === 'approved' ? 'default' : approval.status === 'rejected' ? 'destructive' : 'secondary'}>
                        {approval.status.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <p className="text-sm text-muted-foreground">
                      Role: {approval.approver_role.replace('_', ' ').toUpperCase()}
                    </p>
                    
                    {approval.approval_date && (
                      <p className="text-sm text-muted-foreground">
                        {formatDate(approval.approval_date)}
                      </p>
                    )}
                    
                    {approval.comments && (
                      <p className="text-sm mt-2 p-2 bg-gray-50 rounded">
                        {approval.comments}
                      </p>
                    )}
                    
                    {approval.approved_amount && (
                      <p className="text-sm text-green-600 mt-1">
                        Approved Amount: {formatCurrency(approval.approved_amount)}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Invoices */}
      {request.procurement_invoices && request.procurement_invoices.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Invoices</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {request.procurement_invoices.map((invoice: any, index: number) => (
                <div key={invoice.id || index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">Invoice #{invoice.invoice_number}</h4>
                      <p className="text-sm text-muted-foreground">{invoice.vendor_name}</p>
                    </div>
                    <Badge variant={invoice.payment_status === 'paid' ? 'default' : 'secondary'}>
                      {invoice.payment_status.toUpperCase()}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Total Amount:</span>
                      <p className="font-medium">{formatCurrency(invoice.total_amount)}</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground">Invoice Date:</span>
                      <p className="font-medium">{formatDate(invoice.invoice_date)}</p>
                    </div>
                    
                    {invoice.due_date && (
                      <div>
                        <span className="text-muted-foreground">Due Date:</span>
                        <p className="font-medium">{formatDate(invoice.due_date)}</p>
                      </div>
                    )}
                    
                    {invoice.payment_date && (
                      <div>
                        <span className="text-muted-foreground">Paid Date:</span>
                        <p className="font-medium">{formatDate(invoice.payment_date)}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Close Button */}
      {onClose && (
        <div className="flex justify-end">
          <Button onClick={onClose} variant="outline">
            Close
          </Button>
        </div>
      )}
    </div>
  );
};
