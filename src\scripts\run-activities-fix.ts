import { fixSystemActivitiesComplete } from './fix-system-activities-complete';

/**
 * <PERSON><PERSON><PERSON> to run the complete system activities fix
 * This will be executed when the page loads to ensure the database is properly set up
 */

const runActivitiesFix = async () => {
  console.log('🚀 Starting system activities database fix...');
  
  try {
    const success = await fixSystemActivitiesComplete();
    
    if (success) {
      console.log('✅ System activities fix completed successfully!');
      
      // Store success in localStorage to avoid running again
      localStorage.setItem('activities_fix_completed', new Date().toISOString());
      
      return true;
    } else {
      console.error('❌ System activities fix failed');
      return false;
    }
  } catch (error) {
    console.error('❌ Error running activities fix:', error);
    return false;
  }
};

// Check if fix has already been run recently (within last 24 hours)
const shouldRunFix = () => {
  const lastRun = localStorage.getItem('activities_fix_completed');
  
  if (!lastRun) {
    return true;
  }
  
  const lastRunDate = new Date(lastRun);
  const now = new Date();
  const hoursSinceLastRun = (now.getTime() - lastRunDate.getTime()) / (1000 * 60 * 60);
  
  // Run fix if it's been more than 24 hours
  return hoursSinceLastRun > 24;
};

// Auto-run the fix if needed
if (typeof window !== 'undefined' && shouldRunFix()) {
  runActivitiesFix();
}

export { runActivitiesFix, shouldRunFix };
