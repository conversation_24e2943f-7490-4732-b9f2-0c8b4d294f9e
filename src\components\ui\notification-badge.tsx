
import React from "react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface NotificationBadgeProps {
  count: number;
  className?: string;
  maxCount?: number;
}

export const NotificationBadge = ({ 
  count, 
  className, 
  maxCount = 99 
}: NotificationBadgeProps) => {
  if (count <= 0) return null;

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  return (
    <Badge 
      className={cn(
        "bg-red-500 text-white text-xs min-w-[1.25rem] h-5 flex items-center justify-center rounded-full",
        className
      )}
    >
      {displayCount}
    </Badge>
  );
};
