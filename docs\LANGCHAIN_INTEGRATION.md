# LangChain Integration for CTNL AI Workboard

This document provides a comprehensive guide to the Lang<PERSON>hain integration implemented in the CTNL AI Workboard system.

## Overview

The LangChain integration enhances the AI capabilities of the CTNL AI Workboard with:

- **Advanced Conversational AI** with memory and context awareness
- **Retrieval-Augmented Generation (RAG)** for document-based question answering
- **Intelligent Agents** with custom tools for system operations
- **Document Processing** with automatic chunking and embedding
- **Multi-modal Chains** for complex workflows
- **Comprehensive Monitoring** and analytics

## Architecture

### Core Components

1. **Configuration Service** (`src/lib/langchain/config.ts`)
   - Centralized configuration management
   - Environment variable handling
   - Model initialization

2. **Document Processor** (`src/lib/langchain/document-processor.ts`)
   - Document loading and parsing
   - Text splitting and chunking
   - Vector embedding generation

3. **RAG System** (`src/lib/langchain/rag-system.ts`)
   - Vector store management
   - Similarity search
   - Context-aware response generation

4. **Agents and Tools** (`src/lib/langchain/agents.ts`)
   - Custom tools for database queries
   - Document analysis capabilities
   - System management functions

5. **Memory Management** (`src/lib/langchain/memory.ts`)
   - Conversation history persistence
   - Context-aware responses
   - User-specific memory

6. **Chains and Workflows** (`src/lib/langchain/chains.ts`)
   - Specialized processing chains
   - Multi-step workflows
   - Report generation

7. **Integration Service** (`src/lib/langchain/integration.ts`)
   - Unified API for all LangChain features
   - Strategy-based processing
   - Backward compatibility

8. **Monitoring and Analytics** (`src/lib/langchain/monitoring.ts`)
   - Performance metrics
   - Usage analytics
   - Error tracking

## Setup and Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# LangChain Configuration
USE_LANGCHAIN=true

# Model API Keys
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key

# Model Settings
OPENAI_MODEL=gpt-4-turbo-preview
ANTHROPIC_MODEL=claude-3-sonnet-20240229
OPENAI_TEMPERATURE=0.7
ANTHROPIC_TEMPERATURE=0.7

# Vector Store Configuration
VECTOR_STORE_PROVIDER=chroma
VECTOR_STORE_DIMENSIONS=1536
VECTOR_STORE_COLLECTION=aiworkboard_documents

# RAG Configuration
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200
RAG_TOP_K=5
RAG_SCORE_THRESHOLD=0.7

# Memory Configuration
MEMORY_TYPE=buffer
MEMORY_MAX_TOKENS=2000

# Agent Configuration
AGENT_MAX_ITERATIONS=10
AGENT_VERBOSE=false
```

### Database Setup

Run the LangChain migration to create necessary tables:

```bash
supabase migration up 20240709_langchain_tables.sql
```

### Supabase Functions

Deploy the LangChain Edge Functions:

```bash
supabase functions deploy langchain-ai-assistant
supabase functions deploy langchain-rag
supabase functions deploy langchain-document-processor
```

## Usage

### React Hook

Use the `useLangChain` hook in your React components:

```typescript
import { useLangChain } from '@/hooks/useLangChain';

function MyComponent() {
  const {
    messages,
    isLoading,
    sendMessage,
    processDocument,
    searchKnowledge
  } = useLangChain();

  const handleSendMessage = async () => {
    await sendMessage('Hello, how can you help me?', {
      useRAG: true,
      useMemory: true,
      interface: 'enhanced'
    });
  };

  // ... component implementation
}
```

### Direct Integration

Use the integration service directly:

```typescript
import { langChainIntegration } from '@/lib/langchain';

// Process a request
const response = await langChainIntegration.processRequest({
  message: 'Analyze this document',
  userId: 'user-id',
  context: {
    interface: 'futuristic',
    role: 'admin'
  },
  options: {
    useRAG: true,
    useAgent: true
  }
});

// Process a document
const processedDoc = await langChainIntegration.processDocument(
  '/path/to/document.pdf',
  { addToRAG: true }
);
```

### Supabase Functions

Call LangChain functions directly:

```typescript
// AI Assistant
const { data } = await supabase.functions.invoke('langchain-ai-assistant', {
  body: {
    message: 'Hello',
    userId: 'user-id',
    context: { interface: 'standard' }
  }
});

// RAG Query
const { data } = await supabase.functions.invoke('langchain-rag', {
  body: {
    question: 'What is the company policy?',
    maxResults: 5
  }
});

// Document Processing
const { data } = await supabase.functions.invoke('langchain-document-processor', {
  body: {
    content: 'Document content...',
    metadata: { title: 'My Document' }
  }
});
```

## Features

### 1. Enhanced AI Conversations

- **Memory-aware conversations** that remember context
- **Role-based responses** tailored to user permissions
- **Interface-specific behavior** for different UI components
- **Streaming responses** for real-time interaction

### 2. Document Intelligence

- **Automatic document processing** with chunking and embedding
- **Semantic search** across document collections
- **Document summarization** and analysis
- **Multi-format support** (PDF, DOCX, TXT, CSV, JSON)

### 3. Intelligent Agents

- **Database query agent** for data retrieval
- **Document analysis agent** for content insights
- **Report generation agent** for automated reporting
- **System management agent** for administrative tasks

### 4. RAG Capabilities

- **Context-aware responses** using relevant documents
- **Confidence scoring** for answer reliability
- **Source attribution** for transparency
- **Customizable retrieval** parameters

### 5. Advanced Workflows

- **Multi-step processing** chains
- **Conditional logic** in workflows
- **Error handling** and recovery
- **Progress tracking** and monitoring

## Monitoring and Analytics

### Real-time Metrics

- **Response times** and performance metrics
- **Success/failure rates** and error tracking
- **Token usage** and cost monitoring
- **User activity** and engagement analytics

### Dashboard Integration

Access monitoring data through:

```typescript
import { langChainMonitoring } from '@/lib/langchain';

// Get usage statistics
const stats = await langChainMonitoring.getUsageStats();

// Get performance metrics
const performance = await langChainMonitoring.getPerformanceMetrics();

// Get real-time metrics
const realTime = await langChainMonitoring.getRealTimeMetrics();
```

## Best Practices

### 1. Configuration Management

- Use environment variables for all configuration
- Validate configuration on startup
- Implement fallback strategies for missing keys

### 2. Error Handling

- Implement comprehensive error handling
- Provide meaningful error messages
- Log errors for debugging and monitoring

### 3. Performance Optimization

- Use appropriate chunk sizes for documents
- Implement caching where appropriate
- Monitor token usage and costs

### 4. Security

- Validate all user inputs
- Implement proper authentication
- Use RLS policies for data access

### 5. Testing

- Test all LangChain components thoroughly
- Implement integration tests
- Monitor performance in production

## Troubleshooting

### Common Issues

1. **Configuration Errors**
   - Check environment variables
   - Validate API keys
   - Verify model availability

2. **Performance Issues**
   - Monitor response times
   - Check token usage
   - Optimize chunk sizes

3. **Memory Issues**
   - Clear old conversations
   - Monitor memory usage
   - Implement cleanup routines

### Debug Mode

Enable debug mode for detailed logging:

```bash
AGENT_VERBOSE=true
```

## Migration Guide

### From Basic AI to LangChain

1. **Update environment variables** with LangChain configuration
2. **Run database migrations** to create LangChain tables
3. **Deploy new Supabase functions**
4. **Update frontend components** to use new hooks
5. **Test all functionality** thoroughly

### Backward Compatibility

The integration maintains backward compatibility with existing AI components:

- Existing AI interfaces continue to work
- Gradual migration is supported
- Fallback to basic responses when LangChain is unavailable

## Support

For issues and questions:

1. Check the troubleshooting section
2. Review the configuration
3. Check the monitoring dashboard
4. Contact the development team

## Future Enhancements

Planned improvements include:

- **Multi-modal capabilities** (image, audio processing)
- **Advanced agent orchestration**
- **Custom model fine-tuning**
- **Enhanced monitoring and alerting**
- **Integration with external tools and APIs**
