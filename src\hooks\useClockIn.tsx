
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useState } from 'react';

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
}

export const useClockIn = () => {
  const [loading, setLoading] = useState(false);
  const [currentSession, setCurrentSession] = useState<any>(null);
  const { user } = useAuth();
  const { toast } = useToast();

  const getCurrentLocation = (): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          
          // Use browser's built-in geocoding or fallback to coordinates
          try {
            // Try to get address using browser's reverse geocoding if available
            // For production, replace with your preferred geocoding service
            const address = `Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
            resolve({ latitude, longitude, address });
          } catch {
            resolve({ latitude, longitude, address: `${latitude}, ${longitude}` });
          }
        },
        (error) => {
          reject(new Error(`Location error: ${error.message}`));
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0
        }
      );
    });
  };

  const clockIn = async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to clock in",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const location = await getCurrentLocation();
      
      const { data, error } = await supabase
        .from('time_logs')
        .insert({
          user_id: user.id,
          clock_in: new Date().toISOString(),
          clock_in_timestamp: new Date().toISOString(),
          latitude: location.latitude,
          longitude: location.longitude,
          location_address: location.address,
          date: new Date().toISOString().split('T')[0]
        })
        .select()
        .single();

      if (error) throw error;

      setCurrentSession(data);
      toast({
        title: "Clocked In Successfully",
        description: `Location: ${location.address}`,
      });

      return data;
    } catch (error: any) {
      toast({
        title: "Clock In Failed",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const clockOut = async (sessionId: string) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please log in to clock out",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const location = await getCurrentLocation();
      const clockOutTime = new Date().toISOString();

      const { data, error } = await supabase
        .from('time_logs')
        .update({
          clock_out: clockOutTime,
          clock_out_timestamp: clockOutTime,
          latitude: location.latitude,
          longitude: location.longitude,
          location_address: location.address,
        })
        .eq('id', sessionId)
        .select()
        .single();

      if (error) throw error;

      // Calculate total hours
      const clockInTime = new Date(data.clock_in);
      const clockOutTimeObj = new Date(clockOutTime);
      const totalHours = (clockOutTimeObj.getTime() - clockInTime.getTime()) / (1000 * 60 * 60);

      await supabase
        .from('time_logs')
        .update({ total_hours: totalHours })
        .eq('id', sessionId);

      setCurrentSession(null);
      toast({
        title: "Clocked Out Successfully",
        description: `Total hours: ${totalHours.toFixed(2)}`,
      });

      return data;
    } catch (error: any) {
      toast({
        title: "Clock Out Failed",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const getCurrentSession = async () => {
    if (!user) return null;

    try {
      // Use a more robust query without .single() to avoid 406 errors
      const { data, error } = await supabase
        .from('time_logs')
        .select('*')
        .eq('user_id', user.id)
        .is('clock_out', null)
        .order('clock_in', { ascending: false })
        .limit(1);

      if (error) {
        console.log('Error fetching current session:', error);

        // If it's a table not found error, return null
        if (error.status === 404 || error.code === 'PGRST106') {
          console.log('time_logs table not found, returning null');
          setCurrentSession(null);
          return null;
        }

        // For 406 errors, just return null (no active session)
        if (error.status === 406) {
          console.log('No active session found (406), returning null');
          setCurrentSession(null);
          return null;
        }

        throw error;
      }

      // Return the first result or null if no active session
      const session = (data && data.length > 0 ? data[0] : null);
      setCurrentSession(session);
      return session;
    } catch (error: any) {
      console.error('Error fetching current session:', error);
      setCurrentSession(null);
      return null;
    }
  };

  return {
    loading,
    currentSession,
    clockIn,
    clockOut,
    getCurrentSession,
  };
};
