// Enhanced type definitions for our authentication system
// Based on Supabase generated types but with better type safety

import type { Database } from '@/integrations/supabase/types';

// Extract table types from Supabase schema
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Department = Database['public']['Tables']['departments']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

// Enhanced role types with strict validation
export type UserRole = 'admin' | 'manager' | 'staff' | 'accountant' | 'hr' | 'staff-admin';

// Enhanced account types
export type AccountType = 'admin' | 'manager' | 'staff' | 'accountant' | 'hr' | 'staff-admin';

// User status types
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending';

// Enhanced profile type with better type safety
export interface EnhancedProfile extends Omit<Profile, 'role' | 'account_type' | 'status'> {
  role: UserRole;
  account_type: AccountType | null;
  status: UserStatus | null;
}

// Auth context type with proper typing
export interface AuthContextType {
  user: any; // Supabase User type
  session: any; // Supabase Session type
  userProfile: EnhancedProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, metadata?: any) => Promise<any>;
  signOut: () => Promise<void>;
  updateProfile: (updates: ProfileUpdate) => Promise<any>;
  isAuthenticated: boolean;
  isAdmin: boolean;
  isManager: boolean;
  isStaff: boolean;
}

// Department with enhanced typing
export interface EnhancedDepartment extends Department {
  // Add computed fields if needed
  isActive?: boolean;
}

// Profile creation data for new users
export interface CreateProfileData {
  id: string;
  email: string;
  full_name: string;
  role: UserRole;
  account_type?: AccountType;
  status?: UserStatus;
  department_id?: string;
  position?: string;
  phone?: string;
}

// Profile update data
export interface UpdateProfileData {
  full_name?: string;
  role?: UserRole;
  department_id?: string;
  position?: string;
  phone?: string;
  bio?: string;
  avatar_url?: string;
  location?: string;
  skills?: string[];
  settings?: Record<string, any>;
  notification_preferences?: Record<string, any>;
}

// Role-based route mapping
export const ROLE_ROUTES: Record<UserRole, string> = {
  admin: '/dashboard/admin',
  manager: '/dashboard/manager',
  staff: '/dashboard/staff',
  accountant: '/dashboard/accountant',
  hr: '/dashboard/hr',
  'staff-admin': '/dashboard/staff-admin'
};

// Role hierarchy for permissions
export const ROLE_HIERARCHY: Record<UserRole, number> = {
  admin: 5,
  manager: 4,
  'staff-admin': 3,
  hr: 2,
  accountant: 2,
  staff: 1
};

// Permission helpers
export const hasPermission = (userRole: UserRole, requiredRole: UserRole): boolean => {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
};

export const isAdminOrManager = (role: UserRole): boolean => {
  return role === 'admin' || role === 'manager';
};

export const canManageDepartments = (role: UserRole): boolean => {
  return role === 'admin' || role === 'manager';
};

export const canViewAllProfiles = (role: UserRole): boolean => {
  return role === 'admin' || role === 'manager' || role === 'hr';
};

// Validation helpers
export const isValidRole = (role: string): role is UserRole => {
  return ['admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'].includes(role);
};

export const isValidStatus = (status: string): status is UserStatus => {
  return ['active', 'inactive', 'suspended', 'pending'].includes(status);
};

// Default values
export const DEFAULT_ROLE: UserRole = 'staff';
export const DEFAULT_STATUS: UserStatus = 'active';
export const DEFAULT_ACCOUNT_TYPE: AccountType = 'staff';
