/**
 * Self-Healing System Monitor
 * Real-time monitoring and control of the LangChain self-healing system
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  Activity,
  Zap,
  Brain,
  Settings
} from "lucide-react";
import { selfHealingSystem, type SystemError } from "@/lib/langchain-self-healing";
import { useToast } from "@/hooks/use-toast";

export const SelfHealingMonitor = () => {
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [errors, setErrors] = useState<SystemError[]>([]);
  const [systemHealth, setSystemHealth] = useState<'healthy' | 'warning' | 'critical'>('healthy');
  const [autoFixEnabled, setAutoFixEnabled] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Update errors periodically
    const interval = setInterval(() => {
      const activeErrors = selfHealingSystem.getActiveErrors();
      setErrors(activeErrors);
      
      // Determine system health
      const criticalErrors = activeErrors.filter(e => e.severity === 'critical').length;
      const highErrors = activeErrors.filter(e => e.severity === 'high').length;
      
      if (criticalErrors > 0) {
        setSystemHealth('critical');
      } else if (highErrors > 0 || activeErrors.length > 3) {
        setSystemHealth('warning');
      } else {
        setSystemHealth('healthy');
      }
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const startMonitoring = () => {
    selfHealingSystem.startMonitoring();
    setIsMonitoring(true);
    toast({
      title: "Self-Healing System Started",
      description: "System monitoring and auto-repair is now active",
    });
  };

  const stopMonitoring = () => {
    selfHealingSystem.stopMonitoring();
    setIsMonitoring(false);
    toast({
      title: "Self-Healing System Stopped",
      description: "System monitoring has been disabled",
      variant: "destructive",
    });
  };

  const manualFix = async (errorId: string) => {
    const success = await selfHealingSystem.manualFix(errorId);
    
    toast({
      title: success ? "Fix Attempted" : "Fix Failed",
      description: success 
        ? "Manual fix has been attempted for this error"
        : "Unable to automatically fix this error",
      variant: success ? "default" : "destructive",
    });
  };

  const markResolved = (errorId: string) => {
    selfHealingSystem.markResolved(errorId);
    toast({
      title: "Error Resolved",
      description: "Error has been marked as resolved",
    });
  };

  const getHealthIcon = () => {
    switch (systemHealth) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'critical':
        return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getHealthColor = () => {
    switch (systemHealth) {
      case 'healthy': return 'text-green-600 bg-green-50';
      case 'warning': return 'text-yellow-600 bg-yellow-50';
      case 'critical': return 'text-red-600 bg-red-50';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-blue-100 text-blue-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            LangChain Self-Healing System
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Health Status */}
          <div className={`flex items-center gap-3 p-3 rounded-lg ${getHealthColor()}`}>
            {getHealthIcon()}
            <div>
              <div className="font-medium capitalize">System Health: {systemHealth}</div>
              <div className="text-sm opacity-75">
                {errors.length} active errors • Monitoring: {isMonitoring ? 'Active' : 'Inactive'}
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="flex gap-2">
            {!isMonitoring ? (
              <Button onClick={startMonitoring} className="flex items-center gap-2">
                <Activity className="h-4 w-4" />
                Start Monitoring
              </Button>
            ) : (
              <Button onClick={stopMonitoring} variant="outline" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Stop Monitoring
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => setAutoFixEnabled(!autoFixEnabled)}
              className="flex items-center gap-2"
            >
              <Zap className={`h-4 w-4 ${autoFixEnabled ? 'text-green-500' : 'text-gray-400'}`} />
              Auto-Fix: {autoFixEnabled ? 'ON' : 'OFF'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Active Errors */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Active System Errors ({errors.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {errors.length === 0 ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                No active errors detected. System is running smoothly! 🎉
              </AlertDescription>
            </Alert>
          ) : (
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {errors.map((error) => (
                  <div key={error.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge className={getSeverityColor(error.severity)}>
                          {error.severity}
                        </Badge>
                        <Badge variant="outline">{error.type}</Badge>
                        {error.autoFixAttempted && (
                          <Badge variant="secondary" className="flex items-center gap-1">
                            <Brain className="h-3 w-3" />
                            Auto-fix attempted
                          </Badge>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {error.timestamp.toLocaleTimeString()}
                      </div>
                    </div>
                    
                    <div className="mb-3">
                      <div className="font-medium text-sm mb-1">Error Message:</div>
                      <div className="text-sm text-muted-foreground bg-muted p-2 rounded">
                        {error.message}
                      </div>
                    </div>

                    {error.stack && (
                      <details className="mb-3">
                        <summary className="text-sm font-medium cursor-pointer">
                          Stack Trace
                        </summary>
                        <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-x-auto">
                          {error.stack}
                        </pre>
                      </details>
                    )}

                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => manualFix(error.id)}
                        disabled={error.autoFixAttempted}
                        className="flex items-center gap-1"
                      >
                        <RefreshCw className="h-3 w-3" />
                        Manual Fix
                      </Button>
                      
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => markResolved(error.id)}
                        className="flex items-center gap-1"
                      >
                        <CheckCircle className="h-3 w-3" />
                        Mark Resolved
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {/* System Capabilities */}
      <Card>
        <CardHeader>
          <CardTitle>Self-Healing Capabilities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Automatic Detection
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• JavaScript runtime errors</li>
                <li>• Unhandled promise rejections</li>
                <li>• Database connection issues</li>
                <li>• API endpoint failures</li>
                <li>• LangChain service problems</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Zap className="h-4 w-4 text-blue-500" />
                Auto-Repair Actions
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Database reconnection</li>
                <li>• API retry with backoff</li>
                <li>• LangChain service restart</li>
                <li>• Circuit breaker activation</li>
                <li>• Graceful fallback modes</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Brain className="h-4 w-4 text-purple-500" />
                AI-Powered Analysis
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Root cause analysis</li>
                <li>• Fix suggestions generation</li>
                <li>• Code improvement recommendations</li>
                <li>• Prevention strategies</li>
                <li>• Risk assessment</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Activity className="h-4 w-4 text-orange-500" />
                Monitoring & Logging
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Real-time error tracking</li>
                <li>• Performance monitoring</li>
                <li>• Health check automation</li>
                <li>• Fix attempt logging</li>
                <li>• System metrics collection</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
