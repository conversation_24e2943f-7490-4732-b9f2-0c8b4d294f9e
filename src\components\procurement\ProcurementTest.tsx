import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useProcurementMutations, useProcurementRequests } from '@/hooks/useProcurement';
import { useAuth } from '@/components/auth/AuthProvider';
import { CheckCircle, XCircle, Clock, Package } from 'lucide-react';

export const ProcurementTest: React.FC = () => {
  const { userProfile } = useAuth();
  const { requests, loading } = useProcurementRequests();
  const { createRequest, isCreatingRequest } = useProcurementMutations();
  const [testResult, setTestResult] = useState<string>('');

  const createTestRequest = async () => {
    try {
      setTestResult('Creating test procurement request...');
      
      const testData = {
        title: 'Test Procurement Request',
        description: 'This is a test procurement request to verify the system works',
        justification: 'Testing the procurement management system functionality',
        priority: 'medium' as const,
        category: 'IT Equipment',
        estimated_total_cost: 1000,
        currency: 'USD'
      };

      createRequest(testData);
      setTestResult('✅ Test request created successfully!');
    } catch (error: any) {
      setTestResult(`❌ Error: ${error.message}`);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Procurement System Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">User Information</h4>
              <div className="text-sm space-y-1">
                <p><strong>Name:</strong> {userProfile?.full_name || 'Unknown'}</p>
                <p><strong>Role:</strong> {userProfile?.role || 'Unknown'}</p>
                <p><strong>Email:</strong> {userProfile?.email || 'Unknown'}</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">System Status</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  {loading ? (
                    <Clock className="h-4 w-4 text-yellow-600" />
                  ) : (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  )}
                  <span className="text-sm">
                    {loading ? 'Loading...' : 'Database Connected'}
                  </span>
                </div>
                
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">API Endpoints Ready</span>
                </div>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Test Actions</h4>
            <div className="space-y-2">
              <Button 
                onClick={createTestRequest}
                disabled={isCreatingRequest}
                className="w-full sm:w-auto"
              >
                {isCreatingRequest ? 'Creating...' : 'Create Test Request'}
              </Button>
              
              {testResult && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm">{testResult}</p>
                </div>
              )}
            </div>
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Current Requests ({requests.length})</h4>
            {requests.length === 0 ? (
              <p className="text-sm text-muted-foreground">No requests found</p>
            ) : (
              <div className="space-y-2">
                {requests.slice(0, 3).map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <p className="font-medium text-sm">{request.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {formatCurrency(request.estimated_total_cost)}
                      </p>
                    </div>
                    <Badge variant="outline">
                      {request.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                ))}
                {requests.length > 3 && (
                  <p className="text-xs text-muted-foreground">
                    ...and {requests.length - 3} more
                  </p>
                )}
              </div>
            )}
          </div>

          <div className="border-t pt-4">
            <h4 className="font-medium mb-2">Available Features</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Request Creation</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Manager Approval</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Admin Processing</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Invoice Generation</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Expense Integration</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Data Export</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
