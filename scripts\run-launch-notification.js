// Simple Node.js script to send launch notification via fetch
const fetch = require('node-fetch');

// Configuration
const SUPABASE_URL = 'https://your-project.supabase.co'; // Replace with actual URL
const SUPABASE_ANON_KEY = 'your-anon-key'; // Replace with actual key

// Email recipients for the launch notification
const recipients = [
  '<EMAIL>', // Primary admin/owner from memories
  // Add more recipients as needed:
  // '<EMAIL>',
  // '<EMAIL>', 
  // '<EMAIL>'
];

async function sendLaunchNotification() {
  try {
    console.log('🚀 Sending AI WorkBoard CT launch notification...');
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/send-notification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY
      },
      body: JSON.stringify({
        type: 'system_launch',
        recipients: recipients,
        data: {
          systemName: 'AI WorkBoard CT',
          launchDate: new Date().toLocaleDateString(),
          dashboardUrl: 'https://ai.ctnigeria.com',
          userManualUrl: 'https://ai.ctnigeria.com/user-manual.html',
          features: [
            'AI-Powered Assistant',
            'Real-Time Time Tracking',
            'Project Management',
            'Financial Suite',
            'Mobile Responsive Design',
            'Role-Based Access Control'
          ],
          stats: {
            userRoles: 5,
            databaseTables: 27,
            aiFunctions: '15+',
            mobileReady: '100%'
          }
        }
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${result.error || 'Unknown error'}`);
    }

    console.log('✅ Launch notification sent successfully!');
    console.log('📧 Response:', result);
    console.log(`👥 Recipients: ${recipients.length}`);
    console.log(`📋 Recipients list: ${recipients.join(', ')}`);
    
    console.log('\n🎉 SUCCESS: AI WorkBoard CT launch notification completed!');
    console.log(`📊 Summary: ${recipients.length} recipients notified`);
    console.log('🔗 Users can now access: https://ai.ctnigeria.com');
    console.log('📖 User Manual: https://ai.ctnigeria.com/user-manual.html');
    console.log('\n📋 Next Steps:');
    console.log('1. Users should visit ai.ctnigeria.com to access their dashboards');
    console.log('2. Review the comprehensive user manual for detailed guidance');
    console.log('3. Configure notification preferences in user settings');
    console.log('4. Begin using role-specific features immediately');

    return {
      success: true,
      response: result,
      recipients: recipients.length
    };

  } catch (error) {
    console.error('❌ Failed to send launch notification:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Verify Supabase URL and API key are correct');
    console.log('2. Ensure send-notification function is deployed');
    console.log('3. Check CORS configuration for function calls');
    console.log('4. Verify email service (Resend) is properly configured');
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute the notification
sendLaunchNotification()
  .then(result => {
    if (!result.success) {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });

module.exports = { sendLaunchNotification };
