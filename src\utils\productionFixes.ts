/**
 * Production-specific fixes for authentication and loading issues
 */

// Check if we're running in production
export const isProduction = () => {
  return typeof window !== 'undefined' && 
         (window.location.hostname === 'ai.ctnigeria.com' || 
          process.env.NODE_ENV === 'production');
};

// Check if localStorage is available
export const isLocalStorageAvailable = () => {
  try {
    if (typeof localStorage === 'undefined') return false;
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch (e) {
    return false;
  }
};

// Safe localStorage operations
export const safeLocalStorage = {
  setItem: (key: string, value: string) => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.setItem(key, value);
      }
    } catch (error) {
      console.warn('localStorage setItem failed:', error);
    }
  },
  
  getItem: (key: string): string | null => {
    try {
      if (isLocalStorageAvailable()) {
        return localStorage.getItem(key);
      }
    } catch (error) {
      console.warn('localStorage getItem failed:', error);
    }
    return null;
  },
  
  removeItem: (key: string) => {
    try {
      if (isLocalStorageAvailable()) {
        localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn('localStorage removeItem failed:', error);
    }
  }
};

// Production-specific auth configuration
export const getAuthConfig = () => {
  const baseConfig = {
    storage: isLocalStorageAvailable() ? localStorage : undefined,
    persistSession: true,
    autoRefreshToken: true,
  };

  if (isProduction()) {
    return {
      ...baseConfig,
      // Production-specific settings
      detectSessionInUrl: true,
      flowType: 'pkce' as const,
    };
  }

  return baseConfig;
};

// Check for common production issues
export const diagnoseProductionIssues = () => {
  const issues: string[] = [];

  // Check localStorage
  if (!isLocalStorageAvailable()) {
    issues.push('localStorage not available');
  }

  // Check if running on correct domain
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    if (hostname !== 'ai.ctnigeria.com' && hostname !== 'localhost') {
      issues.push(`Running on unexpected domain: ${hostname}`);
    }

    // Check protocol
    if (window.location.protocol !== 'https:' && hostname !== 'localhost') {
      issues.push('Not using HTTPS in production');
    }
  }

  // Check for network connectivity
  if (typeof navigator !== 'undefined' && !navigator.onLine) {
    issues.push('No network connectivity');
  }

  return issues;
};

// Force resolve loading state (emergency fallback)
export const forceResolveAuth = (setLoading: (loading: boolean) => void, setInitialized: (initialized: boolean) => void) => {
  console.warn('🚨 Force resolving auth state due to timeout');
  setLoading(false);
  setInitialized(true);
};

// Production error handler
export const handleProductionError = (error: any, context: string) => {
  console.error(`Production error in ${context}:`, error);
  
  // Log to external service in production (if available)
  if (isProduction() && typeof window !== 'undefined') {
    // You can add external error logging here
    // Example: Sentry, LogRocket, etc.
  }
};

// Retry mechanism for failed operations
export const retryOperation = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.warn(`Operation failed (attempt ${i + 1}/${maxRetries}):`, error);
      
      if (i < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }
  
  throw lastError;
};

// Check if Supabase is accessible
export const checkSupabaseConnectivity = async () => {
  try {
    const response = await fetch('https://dvflgnqwbsjityrowatf.supabase.co/rest/v1/', {
      method: 'HEAD',
      headers: {
        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28'
      }
    });
    return response.ok;
  } catch (error) {
    console.error('Supabase connectivity check failed:', error);
    return false;
  }
};
