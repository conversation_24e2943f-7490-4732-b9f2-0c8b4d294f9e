/**
 * Enhanced Cache Clearing Utility
 * Fixes blank screen and cache-related issues
 */

export class CacheFixer {
  static clearAllCaches() {
    try {
      // Clear localStorage
      if (typeof localStorage !== 'undefined') {
        localStorage.clear();
        console.log('✅ localStorage cleared');
      }

      // Clear sessionStorage
      if (typeof sessionStorage !== 'undefined') {
        sessionStorage.clear();
        console.log('✅ sessionStorage cleared');
      }

      // Clear IndexedDB
      if (typeof indexedDB !== 'undefined') {
        indexedDB.databases().then(databases => {
          databases.forEach(db => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name);
            }
          });
        });
        console.log('✅ IndexedDB cleared');
      }

      // Clear service worker caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
        console.log('✅ Service worker caches cleared');
      }

      // Unregister service workers
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => {
            registration.unregister();
          });
        });
        console.log('✅ Service workers unregistered');
      }

      return true;
    } catch (error) {
      console.error('❌ Error clearing caches:', error);
      return false;
    }
  }

  static fixBlankScreen() {
    try {
      // Force reload if blank screen detected
      if (document.body.children.length === 0 || 
          document.getElementById('root')?.children.length === 0) {
        console.log('🔄 Blank screen detected, forcing reload...');
        this.clearAllCaches();
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Error fixing blank screen:', error);
      return false;
    }
  }

  static preventCacheStuck() {
    // Prevent cache from getting stuck
    const lastClear = localStorage.getItem('lastCacheClear');
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (!lastClear || (now - parseInt(lastClear)) > oneHour) {
      this.clearAllCaches();
      localStorage.setItem('lastCacheClear', now.toString());
    }
  }

  static init() {
    // Run on page load
    document.addEventListener('DOMContentLoaded', () => {
      this.preventCacheStuck();
      
      // Check for blank screen after a delay
      setTimeout(() => {
        this.fixBlankScreen();
      }, 2000);
    });

    // Make available globally
    (window as any).clearAllCaches = () => this.clearAllCaches();
    (window as any).fixBlankScreen = () => this.fixBlankScreen();
  }
}

// Auto-initialize
CacheFixer.init();
