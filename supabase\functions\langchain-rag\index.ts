/**
 * <PERSON><PERSON><PERSON><PERSON> RAG (Retrieval-Augmented Generation) Supabase Edge Function
 * Provides document-based question answering using LangChain RAG
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { ChatOpenAI } from 'https://esm.sh/@langchain/openai';
import { OpenAIEmbeddings } from 'https://esm.sh/@langchain/openai';
import { SupabaseVectorStore } from 'https://esm.sh/@langchain/community/vectorstores/supabase';
import { PromptTemplate } from 'https://esm.sh/@langchain/core/prompts';
import { RunnableSequence, RunnablePassthrough } from 'https://esm.sh/@langchain/core/runnables';
import { StringOutputParser } from 'https://esm.sh/@langchain/core/output_parsers';
import { Document } from 'https://esm.sh/@langchain/core/documents';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface RAGRequest {
  question: string;
  userId?: string;
  context?: string;
  filters?: Record<string, any>;
  maxResults?: number;
  scoreThreshold?: number;
  includeMetadata?: boolean;
}

interface RAGResponse {
  answer: string;
  sources: Array<{
    content: string;
    metadata: Record<string, any>;
    score?: number;
  }>;
  confidence: number;
  metadata: {
    retrievedDocs: number;
    processingTime: number;
    model: string;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { 
      question, 
      userId, 
      context, 
      filters = {}, 
      maxResults = 5, 
      scoreThreshold = 0.7,
      includeMetadata = true 
    }: RAGRequest = await req.json();

    if (!question?.trim()) {
      return new Response(
        JSON.stringify({ error: 'Question is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const startTime = Date.now();

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Initialize OpenAI components
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: Deno.env.get('OPENAI_API_KEY'),
      modelName: 'text-embedding-ada-002',
    });

    const llm = new ChatOpenAI({
      openAIApiKey: Deno.env.get('OPENAI_API_KEY'),
      modelName: 'gpt-4-turbo-preview',
      temperature: 0.7,
    });

    // Initialize vector store
    const vectorStore = new SupabaseVectorStore(embeddings, {
      client: supabase,
      tableName: 'documents',
      queryName: 'match_documents',
    });

    // Create retriever
    const retriever = vectorStore.asRetriever({
      k: maxResults,
      searchType: 'similarity',
    });

    // Create RAG prompt template
    const ragPrompt = PromptTemplate.fromTemplate(`
You are an AI assistant for the CTNL AI Workboard system. Use the following context to answer the question accurately and helpfully.

Context:
{context}

Question: {question}

Instructions:
- Provide accurate, helpful answers based on the context
- If the context doesn't contain enough information, say so clearly
- Include relevant details from the context
- Be concise but comprehensive
- If appropriate, suggest follow-up actions or related information
- Maintain a professional tone suitable for a business environment

Answer:`);

    // Create RAG chain
    const ragChain = RunnableSequence.from([
      {
        context: retriever.pipe(formatDocuments),
        question: new RunnablePassthrough(),
      },
      ragPrompt,
      llm,
      new StringOutputParser(),
    ]);

    // Execute RAG query
    const answer = await ragChain.invoke(question);

    // Get retrieved documents for sources
    const retrievedDocs = await retriever.getRelevantDocuments(question);
    
    // Filter by score threshold if supported
    const filteredDocs = retrievedDocs.filter((doc: any) => {
      // Note: Actual score filtering would depend on the vector store implementation
      return true; // For now, include all retrieved docs
    });

    // Format sources
    const sources = filteredDocs.map((doc: Document, index: number) => ({
      content: doc.pageContent.substring(0, 500) + (doc.pageContent.length > 500 ? '...' : ''),
      metadata: includeMetadata ? doc.metadata : {},
      score: undefined, // Would be populated if scores are available
    }));

    // Calculate confidence (simplified)
    const confidence = calculateConfidence(filteredDocs.length, maxResults);

    const processingTime = Date.now() - startTime;

    const response: RAGResponse = {
      answer,
      sources,
      confidence,
      metadata: {
        retrievedDocs: filteredDocs.length,
        processingTime,
        model: 'gpt-4-turbo-preview',
      },
    };

    // Log query for analytics if userId provided
    if (userId) {
      await logRAGQuery(supabase, userId, question, answer, filteredDocs.length, processingTime);
    }

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('LangChain RAG Error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message,
        success: false 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

function formatDocuments(docs: Document[]): string {
  return docs
    .map((doc, index) => {
      const source = doc.metadata.source || `Document ${index + 1}`;
      return `Source: ${source}\nContent: ${doc.pageContent}\n`;
    })
    .join('\n---\n');
}

function calculateConfidence(retrievedCount: number, maxResults: number): number {
  if (retrievedCount === 0) return 0;
  
  // Simple confidence calculation based on number of retrieved documents
  const ratio = retrievedCount / maxResults;
  return Math.min(ratio * 0.8 + 0.2, 1.0); // Scale between 0.2 and 1.0
}

async function logRAGQuery(
  supabase: any,
  userId: string,
  question: string,
  answer: string,
  retrievedDocs: number,
  processingTime: number
): Promise<void> {
  try {
    await supabase.from('rag_query_logs').insert({
      user_id: userId,
      question,
      answer,
      retrieved_docs_count: retrievedDocs,
      processing_time: processingTime,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to log RAG query:', error);
  }
}
