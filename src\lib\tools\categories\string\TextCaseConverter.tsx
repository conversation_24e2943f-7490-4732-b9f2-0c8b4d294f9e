import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardH<PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, RotateCcw, <PERSON>rkles } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const TextCaseConverter: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');

  const caseTypes = [
    { name: 'UPPERCASE', func: (text: string) => text.toUpperCase(), description: 'ALL LETTERS IN CAPITAL' },
    { name: 'lowercase', func: (text: string) => text.toLowerCase(), description: 'all letters in small case' },
    { name: 'Title Case', func: (text: string) => text.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()), description: 'First Letter Of Each Word Capitalized' },
    { name: 'Sentence case', func: (text: string) => text.charAt(0).toUpperCase() + text.slice(1).toLowerCase(), description: 'Only first letter capitalized' },
    { name: 'camelCase', func: (text: string) => text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => index === 0 ? word.toLowerCase() : word.toUpperCase()).replace(/\s+/g, ''), description: 'firstWordLowerCaseRestCapitalized' },
    { name: 'PascalCase', func: (text: string) => text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase()).replace(/\s+/g, ''), description: 'EachWordCapitalizedNoSpaces' },
    { name: 'snake_case', func: (text: string) => text.toLowerCase().replace(/\s+/g, '_'), description: 'words_separated_by_underscores' },
    { name: 'kebab-case', func: (text: string) => text.toLowerCase().replace(/\s+/g, '-'), description: 'words-separated-by-hyphens' },
    { name: 'CONSTANT_CASE', func: (text: string) => text.toUpperCase().replace(/\s+/g, '_'), description: 'WORDS_SEPARATED_BY_UNDERSCORES_UPPERCASE' },
    { name: 'aLtErNaTiNg CaSe', func: (text: string) => text.split('').map((char, index) => index % 2 === 0 ? char.toLowerCase() : char.toUpperCase()).join(''), description: 'aLtErNaTiNg BeTwEeN cAsEs' },
    { name: 'dot.case', func: (text: string) => text.toLowerCase().replace(/\s+/g, '.'), description: 'words.separated.by.dots' },
    { name: 'SCREAMING-KEBAB-CASE', func: (text: string) => text.toUpperCase().replace(/\s+/g, '-'), description: 'WORDS-SEPARATED-BY-HYPHENS-UPPERCASE' }
  ];

  const copyToClipboard = async (text: string, caseName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${caseName} copied to clipboard!`);
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearInput = () => {
    setInputText('');
  };

  const getUsageContext = (caseName: string) => {
    const contexts: Record<string, string> = {
      'camelCase': 'JavaScript variables, functions',
      'PascalCase': 'Class names, React components',
      'snake_case': 'Python variables, database fields',
      'kebab-case': 'URLs, CSS classes, HTML attributes',
      'CONSTANT_CASE': 'Environment variables, constants',
      'dot.case': 'File extensions, namespaces',
      'SCREAMING-KEBAB-CASE': 'CSS custom properties'
    };
    return contexts[caseName] || '';
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-blue-800">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Sparkles className="h-6 w-6 text-blue-600" />
            </div>
            {title || 'Text Case Converter'}
          </CardTitle>
          <p className="text-blue-600 text-sm">
            Transform text between 12 different case formats instantly
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <span>📝</span>
              Enter your text:
            </label>
            <div className="relative">
              <Textarea
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
                placeholder="Type or paste your text here to see all case conversions..."
                className="min-h-[140px] resize-none border-2 border-blue-200 focus:border-blue-400 rounded-xl"
              />
              {inputText && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearInput}
                  className="absolute top-3 right-3 h-8 w-8 p-0 hover:bg-blue-100"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {inputText && (
            <div className="space-y-4">
              <div className="flex items-center gap-3 flex-wrap">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  📊 {inputText.length} characters
                </Badge>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  📝 {inputText.split(/\s+/).filter(word => word.length > 0).length} words
                </Badge>
                <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                  📄 {inputText.split('\n').length} lines
                </Badge>
              </div>

              <div className="grid gap-4">
                {caseTypes.map((caseType) => {
                  const convertedText = caseType.func(inputText);
                  const usageContext = getUsageContext(caseType.name);
                  
                  return (
                    <div
                      key={caseType.name}
                      className="group flex items-center gap-4 p-4 bg-white rounded-xl border-2 border-gray-100 hover:border-blue-300 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex-1 min-w-0 space-y-2">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-semibold text-gray-800">
                            {caseType.name}
                          </span>
                          {usageContext && (
                            <Badge variant="outline" className="text-xs">
                              {usageContext}
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-500 italic">
                          {caseType.description}
                        </div>
                        <div className="text-base text-gray-900 font-mono bg-gray-50 p-2 rounded-lg break-all">
                          {convertedText}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(convertedText, caseType.name)}
                        className="shrink-0 opacity-70 group-hover:opacity-100 transition-opacity"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {!inputText && (
            <div className="text-center py-12 text-gray-500">
              <div className="text-6xl mb-4">🔤</div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">Ready to Transform Text</h3>
              <p className="text-sm">Enter some text above to see all 12 case conversions instantly</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Usage Guide
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-3">
            <div className="space-y-2">
              <p><strong className="text-blue-600">camelCase:</strong> JavaScript variables, object properties</p>
              <p><strong className="text-green-600">PascalCase:</strong> Class names, React components</p>
              <p><strong className="text-purple-600">snake_case:</strong> Python variables, database columns</p>
              <p><strong className="text-orange-600">kebab-case:</strong> URLs, CSS classes, HTML attributes</p>
            </div>
            <div className="space-y-2">
              <p><strong className="text-red-600">CONSTANT_CASE:</strong> Environment variables, constants</p>
              <p><strong className="text-indigo-600">dot.case:</strong> File extensions, package names</p>
              <p><strong className="text-pink-600">Title Case:</strong> Headings, proper nouns</p>
              <p><strong className="text-teal-600">Sentence case:</strong> Regular sentences, descriptions</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextCaseConverter;
