import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Copy, RotateCcw, Code, Minimize, AlertCircle, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const JsonFormatter: React.FC = () => {
  const [inputJson, setInputJson] = useState('');
  const [outputJson, setOutputJson] = useState('');
  const [error, setError] = useState('');
  const [isValid, setIsValid] = useState(false);
  const [activeTab, setActiveTab] = useState('format');
  const { toast } = useToast();

  const formatJson = (jsonString: string, indent: number = 2): string => {
    try {
      const parsed = JSON.parse(jsonString);
      setError('');
      setIsValid(true);
      return JSON.stringify(parsed, null, indent);
    } catch (err) {
      setError('Invalid JSON format. Please check your syntax.');
      setIsValid(false);
      return '';
    }
  };

  const minifyJson = (jsonString: string): string => {
    try {
      const parsed = JSON.parse(jsonString);
      setError('');
      setIsValid(true);
      return JSON.stringify(parsed);
    } catch (err) {
      setError('Invalid JSON format. Please check your syntax.');
      setIsValid(false);
      return '';
    }
  };

  const validateJson = (jsonString: string): boolean => {
    try {
      JSON.parse(jsonString);
      setError('');
      setIsValid(true);
      return true;
    } catch (err) {
      setError(`Invalid JSON: ${(err as Error).message}`);
      setIsValid(false);
      return false;
    }
  };

  const handleFormat = () => {
    const formatted = formatJson(inputJson, 2);
    setOutputJson(formatted);
  };

  const handleMinify = () => {
    const minified = minifyJson(inputJson);
    setOutputJson(minified);
  };

  const handleValidate = () => {
    validateJson(inputJson);
    if (isValid) {
      setOutputJson('✅ Valid JSON format');
    }
  };

  const handleProcess = () => {
    if (activeTab === 'format') {
      handleFormat();
    } else if (activeTab === 'minify') {
      handleMinify();
    } else {
      handleValidate();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "JSON copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy JSON",
        variant: "destructive",
      });
    }
  };

  const clearAll = () => {
    setInputJson('');
    setOutputJson('');
    setError('');
    setIsValid(false);
  };

  const loadSampleJson = () => {
    const sample = {
      "name": "John Doe",
      "age": 30,
      "email": "<EMAIL>",
      "address": {
        "street": "123 Main St",
        "city": "New York",
        "zipCode": "10001"
      },
      "hobbies": ["reading", "swimming", "coding"],
      "isActive": true,
      "metadata": null
    };
    setInputJson(JSON.stringify(sample));
  };

  const getJsonStats = (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString);
      const stats = {
        size: jsonString.length,
        keys: 0,
        arrays: 0,
        objects: 0,
        nulls: 0,
        booleans: 0,
        numbers: 0,
        strings: 0
      };

      const analyze = (obj: any) => {
        if (Array.isArray(obj)) {
          stats.arrays++;
          obj.forEach(analyze);
        } else if (obj !== null && typeof obj === 'object') {
          stats.objects++;
          stats.keys += Object.keys(obj).length;
          Object.values(obj).forEach(analyze);
        } else if (obj === null) {
          stats.nulls++;
        } else if (typeof obj === 'boolean') {
          stats.booleans++;
        } else if (typeof obj === 'number') {
          stats.numbers++;
        } else if (typeof obj === 'string') {
          stats.strings++;
        }
      };

      analyze(parsed);
      return stats;
    } catch {
      return null;
    }
  };

  const stats = inputJson ? getJsonStats(inputJson) : null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-cyan-500 rounded-lg">
          <Code className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">JSON Formatter</h2>
          <p className="text-muted-foreground">Format, minify, and validate JSON data</p>
        </div>
      </div>

      {/* Mode Selection */}
      <Card>
        <CardContent className="p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="format" className="flex items-center gap-2">
                <Code className="h-4 w-4" />
                Format
              </TabsTrigger>
              <TabsTrigger value="minify" className="flex items-center gap-2">
                <Minimize className="h-4 w-4" />
                Minify
              </TabsTrigger>
              <TabsTrigger value="validate" className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Validate
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            JSON Input
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={loadSampleJson}>
                Sample
              </Button>
              <Button variant="outline" size="sm" onClick={clearAll}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Paste your JSON here..."
            value={inputJson}
            onChange={(e) => setInputJson(e.target.value)}
            className="min-h-[200px] font-mono text-sm"
          />
          <div className="flex items-center justify-between mt-2">
            <div className="flex gap-2">
              <Badge variant="outline">
                {inputJson.length} characters
              </Badge>
              {inputJson && (
                <Badge variant={isValid ? "default" : "destructive"}>
                  {isValid ? "Valid JSON" : "Invalid JSON"}
                </Badge>
              )}
            </div>
            <Button onClick={handleProcess} disabled={!inputJson.trim()}>
              {activeTab === 'format' ? 'Format' : activeTab === 'minify' ? 'Minify' : 'Validate'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Output Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Output
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(outputJson)}
              disabled={!outputJson}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 rounded-md p-3 min-h-[200px] font-mono text-sm overflow-auto">
            {outputJson || (
              <span className="text-muted-foreground italic">
                {activeTab === 'format' 
                  ? 'Formatted JSON will appear here...' 
                  : activeTab === 'minify'
                  ? 'Minified JSON will appear here...'
                  : 'Validation result will appear here...'}
              </span>
            )}
          </div>
          {outputJson && activeTab !== 'validate' && (
            <div className="flex items-center justify-between mt-2">
              <Badge variant="outline">
                {outputJson.length} characters
              </Badge>
              <Badge variant="secondary">
                {activeTab === 'format' ? 'Formatted' : 'Minified'}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* JSON Statistics */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>JSON Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.objects}</div>
                <div className="text-sm text-muted-foreground">Objects</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.arrays}</div>
                <div className="text-sm text-muted-foreground">Arrays</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.keys}</div>
                <div className="text-sm text-muted-foreground">Keys</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.strings}</div>
                <div className="text-sm text-muted-foreground">Strings</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default JsonFormatter;
