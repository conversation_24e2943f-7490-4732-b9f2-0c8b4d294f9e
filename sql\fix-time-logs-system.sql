-- ============================================================================
-- FIX TIME LOGS SYSTEM - COMPLETE SOLUTION
-- This script creates the time_logs table and RPC functions
-- Run this in Supabase SQL Editor
-- ============================================================================

-- STEP 1: Create time_logs table with correct structure
CREATE TABLE IF NOT EXISTS public.time_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    clock_in TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    clock_out TIMESTAMP WITH TIME ZONE,
    break_start TIMESTAMP WITH TIME ZONE,
    break_end TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL(5,2),
    break_duration DECIMAL(5,2),
    notes TEXT,
    location TEXT,
    ip_address INET,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- STEP 2: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in ON public.time_logs(clock_in);
CREATE INDEX IF NOT EXISTS idx_time_logs_clock_out ON public.time_logs(clock_out);
CREATE INDEX IF NOT EXISTS idx_time_logs_status ON public.time_logs(status);
CREATE INDEX IF NOT EXISTS idx_time_logs_user_date ON public.time_logs(user_id, clock_in);

-- STEP 3: Enable RLS and create safe policies
ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies first
DROP POLICY IF EXISTS "Users can view own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "Users can insert own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "Users can update own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "time_logs_select_own" ON public.time_logs;
DROP POLICY IF EXISTS "time_logs_insert_own" ON public.time_logs;
DROP POLICY IF EXISTS "time_logs_update_own" ON public.time_logs;
DROP POLICY IF EXISTS "time_logs_service_role" ON public.time_logs;

-- Create safe RLS policies
CREATE POLICY "time_logs_select_own" ON public.time_logs
    FOR SELECT
    USING (user_id = auth.uid());

CREATE POLICY "time_logs_insert_own" ON public.time_logs
    FOR INSERT
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "time_logs_update_own" ON public.time_logs
    FOR UPDATE
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

CREATE POLICY "time_logs_service_role" ON public.time_logs
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- STEP 4: Create RPC function for team time logs
CREATE OR REPLACE FUNCTION get_team_time_logs(
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '7 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    id UUID,
    user_id UUID,
    user_name TEXT,
    user_email TEXT,
    clock_in TIMESTAMP WITH TIME ZONE,
    clock_out TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL,
    break_duration DECIMAL,
    status TEXT,
    notes TEXT,
    location TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tl.id,
        tl.user_id,
        COALESCE(p.full_name, au.email) as user_name,
        au.email as user_email,
        tl.clock_in,
        tl.clock_out,
        tl.total_hours,
        tl.break_duration,
        tl.status,
        tl.notes,
        tl.location
    FROM public.time_logs tl
    LEFT JOIN auth.users au ON tl.user_id = au.id
    LEFT JOIN public.profiles p ON tl.user_id = p.id
    WHERE DATE(tl.clock_in) BETWEEN start_date AND end_date
    ORDER BY tl.clock_in DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 5: Create RPC function for attendance stats
CREATE OR REPLACE FUNCTION get_attendance_stats(
    target_user_id UUID DEFAULT NULL,
    start_date DATE DEFAULT CURRENT_DATE - INTERVAL '30 days',
    end_date DATE DEFAULT CURRENT_DATE
)
RETURNS TABLE(
    user_id UUID,
    user_name TEXT,
    total_days INTEGER,
    total_hours DECIMAL,
    average_hours DECIMAL,
    days_present INTEGER,
    days_absent INTEGER,
    late_arrivals INTEGER,
    early_departures INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(target_user_id, auth.uid()) as user_id,
        COALESCE(p.full_name, au.email) as user_name,
        COUNT(DISTINCT DATE(tl.clock_in))::INTEGER as total_days,
        COALESCE(SUM(tl.total_hours), 0) as total_hours,
        COALESCE(AVG(tl.total_hours), 0) as average_hours,
        COUNT(DISTINCT DATE(tl.clock_in))::INTEGER as days_present,
        (end_date - start_date + 1)::INTEGER - COUNT(DISTINCT DATE(tl.clock_in))::INTEGER as days_absent,
        COUNT(CASE WHEN EXTRACT(HOUR FROM tl.clock_in) > 9 THEN 1 END)::INTEGER as late_arrivals,
        COUNT(CASE WHEN tl.clock_out IS NOT NULL AND EXTRACT(HOUR FROM tl.clock_out) < 17 THEN 1 END)::INTEGER as early_departures
    FROM public.time_logs tl
    LEFT JOIN auth.users au ON tl.user_id = au.id
    LEFT JOIN public.profiles p ON tl.user_id = p.id
    WHERE tl.user_id = COALESCE(target_user_id, auth.uid())
    AND DATE(tl.clock_in) BETWEEN start_date AND end_date
    GROUP BY tl.user_id, p.full_name, au.email;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 6: Create function to calculate total hours automatically
CREATE OR REPLACE FUNCTION calculate_total_hours()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate total hours when clock_out is set
    IF NEW.clock_out IS NOT NULL AND NEW.clock_in IS NOT NULL THEN
        NEW.total_hours = EXTRACT(EPOCH FROM (NEW.clock_out - NEW.clock_in)) / 3600.0;
        
        -- Subtract break duration if exists
        IF NEW.break_start IS NOT NULL AND NEW.break_end IS NOT NULL THEN
            NEW.break_duration = EXTRACT(EPOCH FROM (NEW.break_end - NEW.break_start)) / 3600.0;
            NEW.total_hours = NEW.total_hours - NEW.break_duration;
        END IF;
    END IF;
    
    -- Update timestamp
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- STEP 7: Create trigger for automatic calculations
DROP TRIGGER IF EXISTS trigger_calculate_total_hours ON public.time_logs;
CREATE TRIGGER trigger_calculate_total_hours
    BEFORE UPDATE ON public.time_logs
    FOR EACH ROW
    EXECUTE FUNCTION calculate_total_hours();

-- STEP 8: Create function to get current active session
CREATE OR REPLACE FUNCTION get_current_time_session(target_user_id UUID DEFAULT NULL)
RETURNS TABLE(
    id UUID,
    user_id UUID,
    clock_in TIMESTAMP WITH TIME ZONE,
    clock_out TIMESTAMP WITH TIME ZONE,
    break_start TIMESTAMP WITH TIME ZONE,
    break_end TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL,
    break_duration DECIMAL,
    status TEXT,
    notes TEXT,
    location TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tl.id,
        tl.user_id,
        tl.clock_in,
        tl.clock_out,
        tl.break_start,
        tl.break_end,
        tl.total_hours,
        tl.break_duration,
        tl.status,
        tl.notes,
        tl.location
    FROM public.time_logs tl
    WHERE tl.user_id = COALESCE(target_user_id, auth.uid())
    AND tl.clock_out IS NULL
    AND tl.status = 'active'
    ORDER BY tl.clock_in DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 9: Insert sample data for testing
INSERT INTO public.time_logs (user_id, clock_in, clock_out, total_hours, status, notes)
SELECT 
    auth.uid(),
    NOW() - INTERVAL '8 hours',
    NOW() - INTERVAL '30 minutes',
    7.5,
    'completed',
    'Sample time log entry'
WHERE auth.uid() IS NOT NULL
ON CONFLICT DO NOTHING;

-- STEP 10: Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.time_logs TO authenticated;
GRANT EXECUTE ON FUNCTION get_team_time_logs TO authenticated;
GRANT EXECUTE ON FUNCTION get_attendance_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_time_session TO authenticated;

-- STEP 11: Verification and success message
SELECT 
    'TIME LOGS SYSTEM SETUP COMPLETE' as status,
    COUNT(*) as time_logs_count,
    'RPC functions created: get_team_time_logs, get_attendance_stats, get_current_time_session' as functions_status
FROM public.time_logs;

-- Display available functions
SELECT 
    'Available RPC Functions:' as info,
    'get_team_time_logs(start_date, end_date)' as function_1,
    'get_attendance_stats(user_id, start_date, end_date)' as function_2,
    'get_current_time_session(user_id)' as function_3;
