# 🗄️ Database Setup Scripts

This directory contains SQL scripts to set up the complete AI-CTNIGERIA Workboard System with Battery Management.

## 🚀 Quick Start

### Option 1: Quick Setup (Recommended for Testing)
```sql
-- Run this first to get basic functionality working
\i sql/quick-setup.sql
```

### Option 2: Complete Setup (Production Ready)
```sql
-- Run this for full system with all features
\i sql/complete-system-setup.sql
```

### Option 3: Verification Only
```sql
-- Check your existing setup
\i sql/verify-setup.sql
SELECT * FROM quick_status();
```

## 📋 What Gets Created

### 🔋 Battery Management System
- **`battery_types`** - Battery specifications (voltage, capacity, chemistry)
- **`battery_locations`** - Storage locations (sites, warehouses, vehicles)
- **`batteries`** - Main inventory with full lifecycle tracking
- **`battery_readings`** - Performance data and diagnostics
- **`battery_maintenance`** - Service records and scheduling
- **`battery_transfers`** - Movement history between locations

### 👥 User Management
- **`departments`** - Organizational departments
- **`profiles`** - User profiles extending auth.users
- **`notifications`** - User notification system
- **`system_activities`** - Audit trail and activity logs

### 📝 Communication & Projects
- **`memos`** - Internal communication and memos
- **`projects`** - Project management
- **`tasks`** - Task assignment and tracking
- **`expense_reports`** - Financial expense tracking
- **`budgets`** - Budget management

### 🔐 Security Features
- **Row Level Security (RLS)** enabled on all tables
- **Audit columns** on every table (`created_by`, `updated_by`, `profile_id`)
- **Foreign key constraints** for data integrity
- **Indexes** for optimal performance

## 🎯 Audit Columns

Every table includes comprehensive audit tracking:

```sql
-- Standard audit columns
created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
created_by UUID REFERENCES profiles(id),
updated_by UUID REFERENCES profiles(id),
profile_id UUID REFERENCES profiles(id),
generated_by VARCHAR(100)  -- For system/device identification
```

## 📊 Sample Data Included

### Departments
- IT Department
- HR Department  
- Finance Department
- Operations
- Engineering
- Construction

### Battery Types
- Deep Cycle 12V 100Ah (Lead-acid)
- Lithium 48V 200Ah (Lithium-ion)
- AGM 12V 75Ah (AGM)
- Gel 6V 225Ah (Gel)
- LiFePO4 24V 100Ah (LiFePO4)

### Battery Locations
- Main Warehouse
- Site Alpha (Abuja)
- Site Beta (Port Harcourt)
- Mobile Unit 1
- Workshop
- Emergency Storage

## 🔧 Verification Commands

After running setup, verify everything is working:

```sql
-- Quick status check
SELECT * FROM quick_status();

-- Full verification report
SELECT verify_complete_setup();

-- Check specific components
SELECT * FROM check_required_tables();
SELECT * FROM check_sample_data();
SELECT * FROM check_rls_policies();
SELECT * FROM check_foreign_keys();
```

## 🎨 Frontend Integration

The SQL setup is designed to work seamlessly with the React frontend:

### Battery Management
- **URL**: `http://localhost:8083/dashboard/battery`
- **Setup**: `http://localhost:8083/dashboard/admin/battery-setup`

### Features Ready
- ✅ Create/Edit/Delete batteries
- ✅ Track performance readings
- ✅ Manage maintenance records
- ✅ Monitor transfers between locations
- ✅ Search and filter inventory
- ✅ Responsive design for all devices

## 🔒 Security Configuration

### Row Level Security Policies

**Profiles**: Users can view own profile, admins can view all
```sql
CREATE POLICY "Users can view own profile" ON profiles 
FOR SELECT USING (auth.uid() = id);
```

**Batteries**: Users can view own batteries, managers can view all
```sql
CREATE POLICY "Users can view batteries" ON batteries 
FOR SELECT USING (
    created_by = auth.uid() OR 
    EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);
```

**Memos**: Department-based visibility with role overrides
```sql
CREATE POLICY "Users can view published memos" ON memos 
FOR SELECT USING (
    status = 'published' AND (
        visibility = 'public' OR
        created_by = auth.uid() OR
        (visibility = 'department' AND department_id IN (
            SELECT department_id FROM profiles WHERE id = auth.uid()
        ))
    )
);
```

## 🚀 Performance Optimizations

### Indexes Created
- **Profiles**: email, role, department_id, status
- **Batteries**: serial_number, type, location, status, created_by
- **Memos**: created_by, department_id, status, created_at
- **System Activities**: user_id, created_at
- **Notifications**: user_id, is_read

### Triggers
- **Automatic timestamps** on all tables with `updated_at`
- **Audit trail** maintenance
- **Data consistency** enforcement

## 🔄 Migration Strategy

### For Existing Databases
1. **Backup** your current database
2. Run **`verify-setup.sql`** to check current state
3. Run **`quick-setup.sql`** for essential tables
4. Run **`complete-system-setup.sql`** for full features
5. Verify with **`SELECT verify_complete_setup();`**

### For New Databases
1. Run **`complete-system-setup.sql`** directly
2. Verify with **`SELECT quick_status();`**
3. Start using the application immediately

## 🐛 Troubleshooting

### Common Issues

**Foreign Key Errors**
```sql
-- Check missing relationships
SELECT * FROM check_foreign_keys();
```

**Empty Tables**
```sql
-- Check sample data
SELECT * FROM check_sample_data();
```

**Permission Issues**
```sql
-- Check RLS policies
SELECT * FROM check_rls_policies();
```

### Reset Commands
```sql
-- Drop all tables (DANGER!)
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;

-- Then re-run setup scripts
```

## 📞 Support

If you encounter issues:

1. **Check verification**: `SELECT verify_complete_setup();`
2. **Review logs**: Check Supabase dashboard for errors
3. **Test connection**: Verify database connectivity
4. **Check permissions**: Ensure proper RLS policies

## 🎉 Success Indicators

When setup is complete, you should see:

```
🎉 SYSTEM SETUP COMPLETE! 🎉

✅ All tables created with audit columns
✅ Foreign key relationships established  
✅ Indexes created for performance
✅ Sample data inserted
✅ RLS policies configured
✅ Triggers set up for automatic timestamps

🔋 Battery Management System Ready
📝 Memo System Ready  
📊 Project Management Ready
💰 Financial Management Ready
```

**Your system is now ready for production use!** 🚀
