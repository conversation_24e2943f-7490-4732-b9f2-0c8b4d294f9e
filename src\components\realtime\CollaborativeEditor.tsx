/**
 * Collaborative Editor Component
 * Real-time document editing with cursors, comments, and collaboration
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useDocumentCollaboration, useTypingIndicator } from '@/hooks/useRealtime';
import { PresenceIndicator, TypingIndicator } from './PresenceIndicator';
import { MessageSquare, Lock, Unlock, Save, Users } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CollaborativeEditorProps {
  documentId: string;
  className?: string;
  readOnly?: boolean;
  showComments?: boolean;
  showCursors?: boolean;
  autoSave?: boolean;
}

export function CollaborativeEditor({
  documentId,
  className,
  readOnly = false,
  showComments = true,
  showCursors = true,
  autoSave = true,
}: CollaborativeEditorProps) {
  const {
    document,
    isLoading,
    error,
    cursors,
    isLocked,
    applyOperation,
    updateCursor,
    addComment,
    resolveComment,
    lockDocument,
    unlockDocument,
  } = useDocumentCollaboration(documentId);

  const { startTyping, stopTyping } = useTypingIndicator(`document-${documentId}`);
  
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const [selectedText, setSelectedText] = useState<{ start: number; end: number } | null>(null);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [lastContent, setLastContent] = useState('');

  // Handle content changes
  const handleContentChange = useCallback(async (newContent: string) => {
    if (!document || isLocked || readOnly) return;

    const oldContent = lastContent || document.content;
    const cursorPosition = editorRef.current?.selectionStart || 0;

    // Simple diff to determine operation
    if (newContent.length > oldContent.length) {
      // Insert operation
      const insertPosition = cursorPosition - (newContent.length - oldContent.length);
      const insertedText = newContent.slice(insertPosition, cursorPosition);
      
      await applyOperation('insert', insertPosition, insertedText);
    } else if (newContent.length < oldContent.length) {
      // Delete operation
      const deleteLength = oldContent.length - newContent.length;
      await applyOperation('delete', cursorPosition, undefined, deleteLength);
    }

    setLastContent(newContent);
    startTyping();
  }, [document, isLocked, readOnly, lastContent, applyOperation, startTyping]);

  // Handle cursor position changes
  const handleCursorChange = useCallback(async () => {
    if (!editorRef.current || !document) return;

    const { selectionStart, selectionEnd } = editorRef.current;
    
    await updateCursor(
      selectionStart,
      selectionStart !== selectionEnd ? { start: selectionStart, end: selectionEnd } : undefined
    );
  }, [document, updateCursor]);

  // Handle text selection for comments
  const handleTextSelection = useCallback(() => {
    if (!editorRef.current) return;

    const { selectionStart, selectionEnd } = editorRef.current;
    if (selectionStart !== selectionEnd) {
      setSelectedText({ start: selectionStart, end: selectionEnd });
    } else {
      setSelectedText(null);
    }
  }, []);

  // Add comment
  const handleAddComment = useCallback(async () => {
    if (!selectedText || !commentText.trim()) return;

    try {
      await addComment(commentText, selectedText.start);
      setCommentText('');
      setShowCommentDialog(false);
      setSelectedText(null);
    } catch (error) {
      console.error('Failed to add comment:', error);
    }
  }, [selectedText, commentText, addComment]);

  // Update content when document changes
  useEffect(() => {
    if (document && editorRef.current) {
      const currentValue = editorRef.current.value;
      if (currentValue !== document.content) {
        const cursorPosition = editorRef.current.selectionStart;
        editorRef.current.value = document.content;
        editorRef.current.setSelectionRange(cursorPosition, cursorPosition);
        setLastContent(document.content);
      }
    }
  }, [document?.content]);

  // Stop typing when user stops
  useEffect(() => {
    const timer = setTimeout(() => {
      stopTyping();
    }, 1000);

    return () => clearTimeout(timer);
  }, [lastContent, stopTyping]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            Error loading document: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!document) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Document not found
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <span>{document.title}</span>
              {isLocked && (
                <Tooltip>
                  <TooltipTrigger>
                    <Lock className="h-4 w-4 text-red-500" />
                  </TooltipTrigger>
                  <TooltipContent>
                    Document is locked for editing
                  </TooltipContent>
                </Tooltip>
              )}
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              <PresenceIndicator maxVisible={3} />
              
              {!readOnly && (
                <div className="flex items-center space-x-1">
                  {isLocked ? (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={unlockDocument}
                      className="h-8"
                    >
                      <Unlock className="h-3 w-3 mr-1" />
                      Unlock
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={lockDocument}
                      className="h-8"
                    >
                      <Lock className="h-3 w-3 mr-1" />
                      Lock
                    </Button>
                  )}
                  
                  {selectedText && showComments && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowCommentDialog(true)}
                      className="h-8"
                    >
                      <MessageSquare className="h-3 w-3 mr-1" />
                      Comment
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center space-x-4">
              <span>Version {document.version}</span>
              <span>Last saved: {document.lastSaved.toLocaleTimeString()}</span>
            </div>
            
            <TypingIndicator sessionId={`document-${documentId}`} />
          </div>
        </CardHeader>

        <CardContent className="p-6 pt-0">
          <div className="relative">
            {/* Editor */}
            <Textarea
              ref={editorRef}
              defaultValue={document.content}
              onChange={(e) => handleContentChange(e.target.value)}
              onSelect={handleTextSelection}
              onKeyUp={handleCursorChange}
              onMouseUp={handleCursorChange}
              readOnly={readOnly || isLocked}
              className={cn(
                "min-h-[400px] font-mono text-sm resize-none",
                (readOnly || isLocked) && "bg-muted cursor-not-allowed"
              )}
              placeholder="Start typing to collaborate..."
            />

            {/* Cursors overlay */}
            {showCursors && cursors.length > 0 && (
              <div className="absolute inset-0 pointer-events-none">
                {cursors.map((cursor) => (
                  <div
                    key={cursor.userId}
                    className="absolute"
                    style={{
                      // This would need proper positioning calculation
                      // based on cursor.position in the text
                      top: '20px',
                      left: '20px',
                    }}
                  >
                    <div
                      className="w-0.5 h-5 animate-pulse"
                      style={{ backgroundColor: cursor.color }}
                    />
                    <div
                      className="absolute -top-6 left-0 px-1 py-0.5 text-xs text-white rounded whitespace-nowrap"
                      style={{ backgroundColor: cursor.color }}
                    >
                      {cursor.userName}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Comments */}
          {showComments && document.comments.length > 0 && (
            <div className="mt-6 space-y-4">
              <h4 className="font-medium flex items-center">
                <MessageSquare className="h-4 w-4 mr-2" />
                Comments ({document.comments.length})
              </h4>
              
              {document.comments.map((comment) => (
                <Card key={comment.id} className="p-3">
                  <div className="flex items-start space-x-3">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="text-xs">
                        {comment.userName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{comment.userName}</span>
                        <span className="text-xs text-muted-foreground">
                          {comment.createdAt.toLocaleString()}
                        </span>
                        {comment.resolved && (
                          <Badge variant="secondary" className="text-xs">
                            Resolved
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-sm mt-1">{comment.content}</p>
                      
                      {!comment.resolved && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => resolveComment(comment.id)}
                          className="h-6 text-xs mt-2"
                        >
                          Resolve
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Comment dialog */}
          {showCommentDialog && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <Card className="w-96">
                <CardHeader>
                  <CardTitle>Add Comment</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    value={commentText}
                    onChange={(e) => setCommentText(e.target.value)}
                    placeholder="Enter your comment..."
                    className="min-h-[100px]"
                  />
                  
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowCommentDialog(false);
                        setCommentText('');
                      }}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleAddComment}>
                      Add Comment
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
