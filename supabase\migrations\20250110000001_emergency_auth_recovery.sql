-- 🚨 EMERGENCY AUTHENTICATION SYSTEM RECOVERY
-- This migration creates only the essential tables needed for authentication

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ========================================
-- STEP 1: CREATE DEPARTMENTS TABLE
-- ========================================

CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert essential departments
INSERT INTO public.departments (id, name, description) VALUES
    ('********-1111-1111-1111-********1111', 'IT Department', 'Information Technology'),
    ('*************-2222-2222-************', 'HR Department', 'Human Resources'),
    ('*************-3333-3333-************', 'Finance Department', 'Finance and Accounting'),
    ('*************-4444-4444-************', 'Operations', 'Operations Management'),
    ('*************-5555-5555-************', 'Marketing', 'Marketing and Sales'),
    ('*************-6666-6666-************', 'Engineering', 'Engineering and Development')
ON CONFLICT (id) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    updated_at = NOW();

-- ========================================
-- STEP 2: CREATE PROFILES TABLE
-- ========================================

CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    email TEXT UNIQUE,
    role TEXT DEFAULT 'staff' CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin')),
    department_id UUID REFERENCES public.departments(id),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    avatar_url TEXT,
    phone TEXT,
    position TEXT,
    account_type TEXT DEFAULT 'staff',
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ========================================
-- STEP 3: ENABLE ROW LEVEL SECURITY
-- ========================================

ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- ========================================
-- STEP 4: CREATE RLS POLICIES
-- ========================================

-- Departments policies (allow read for all authenticated users)
CREATE POLICY "departments_read_all" ON public.departments
    FOR SELECT TO authenticated USING (true);

CREATE POLICY "departments_service_role_access" ON public.departments
    FOR ALL TO service_role USING (true);

-- Profiles policies
CREATE POLICY "profiles_read_own" ON public.profiles
    FOR SELECT TO authenticated USING (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE TO authenticated USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT TO authenticated WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_service_role_access" ON public.profiles
    FOR ALL TO service_role USING (true);

-- ========================================
-- STEP 5: CREATE PROFILE TRIGGER FUNCTION
-- ========================================

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, email, full_name, role, department_id, created_at, updated_at)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'),
        COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
        COALESCE(
            (NEW.raw_user_meta_data->>'department_id')::UUID,
            '********-1111-1111-1111-********1111'::UUID
        ),
        NOW(),
        NOW()
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        full_name = EXCLUDED.full_name,
        role = EXCLUDED.role,
        department_id = EXCLUDED.department_id,
        updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ========================================
-- STEP 6: VERIFICATION
-- ========================================

DO $$
BEGIN
    RAISE NOTICE '✅ Emergency auth recovery completed';
    RAISE NOTICE '📊 Departments: %', (SELECT COUNT(*) FROM public.departments);
    RAISE NOTICE '👤 Profiles table created successfully';
    RAISE NOTICE '🔐 Authentication system is ready';
END $$;
