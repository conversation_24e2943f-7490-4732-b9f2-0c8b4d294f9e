import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Minus, TrendingDown, TrendingUp } from "lucide-react";
import React from 'react';

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: 'blue' | 'green' | 'red' | 'indigo' | 'yellow' | 'cyan';
  badge?: string;
}

const colorClasses = {
  blue: 'text-blue-600',
  green: 'text-green-600',
  red: 'text-red-600',
  indigo: 'text-indigo-600',
  yellow: 'text-yellow-600',
  cyan: 'text-cyan-600',
};

const trendColors = {
  up: 'text-green-600',
  down: 'text-red-600',
  neutral: 'text-gray-600',
};

const trendIcons = {
  up: <TrendingUp className="h-4 w-4" />,
  down: <TrendingDown className="h-4 w-4" />,
  neutral: <Minus className="h-4 w-4" />,
};

export const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  trendValue,
  color = 'blue',
  badge
}) => {
  // Defensive fallback for value
  const safeValue = value ?? '0';

  return (
    <Card className="modern-3d-card group">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
      
      <CardHeader className="relative flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {badge && (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {badge}
          </span>
        )}
        {icon && (
          <div className={`${colorClasses[color]} opacity-80`}>
            {icon}
          </div>
        )}
      </CardHeader>
      
      <CardContent className="relative">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="text-2xl font-bold tracking-tight">
              {safeValue}
            </div>
            {subtitle && (
              <p className="text-xs text-muted-foreground mt-1">
                {subtitle}
              </p>
            )}
          </div>
          
          {trend && trendValue && (
            <div className={`flex items-center gap-1 ${trendColors[trend]} text-sm font-medium`}>
              {trendIcons[trend]}
              <span>{trendValue}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

interface EnhancedDashboardStatsProps {
  stats: Array<Omit<StatsCardProps, 'className'>>;
  className?: string;
}

export const EnhancedDashboardStats = ({ stats, className }: EnhancedDashboardStatsProps) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className || ''}`} data-aos="fade-up">
      {stats.map((stat, index) => (
        <StatsCard key={index} {...stat} />
      ))}
    </div>
  );
};
