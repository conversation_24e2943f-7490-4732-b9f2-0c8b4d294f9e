import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Car, 
  Plus, 
  Fuel, 
  MapPin,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Edit,
  Search
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const FleetPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isFuelDialogOpen, setIsFuelDialogOpen] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);
  const [editingVehicle, setEditingVehicle] = useState<any>(null);
  const [newVehicle, setNewVehicle] = useState({
    make: "",
    model: "",
    year: new Date().getFullYear(),
    license_plate: "",
    vehicle_number: "",
    fuel_type: "gasoline",
    status: "active",
  });
  const [fuelData, setFuelData] = useState({
    fuel_amount: "",
    cost_per_unit: "",
    fuel_station: "",
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch vehicles from database
  const { data: vehicles, isLoading } = useQuery({
    queryKey: ['fleet-vehicles'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('fleet_vehicles')
        .select(`
          *,
          assigned_driver:profiles!fleet_vehicles_assigned_driver_id_fkey(full_name),
          department:departments!fleet_vehicles_department_id_fkey(name)
        `)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  // Fetch fuel transactions for statistics
  const { data: fuelTransactions } = useQuery({
    queryKey: ['fuel-transactions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('fuel_transactions')
        .select('total_cost')
        .gte('transaction_date', new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString());
      
      if (error) throw error;
      return data;
    },
  });

  // Create vehicle mutation
  const createVehicleMutation = useMutation({
    mutationFn: async (vehicleData: any) => {
      const { data, error } = await supabase
        .from('fleet_vehicles')
        .insert([vehicleData])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fleet-vehicles'] });
      toast({
        title: "Success",
        description: "Vehicle added successfully",
      });
      setIsAddDialogOpen(false);
      setNewVehicle({
        make: "",
        model: "",
        year: new Date().getFullYear(),
        license_plate: "",
        vehicle_number: "",
        fuel_type: "gasoline",
        status: "active",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to add vehicle",
        variant: "destructive",
      });
    },
  });

  // Edit vehicle mutation
  const editVehicleMutation = useMutation({
    mutationFn: async (vehicleData: any) => {
      const { data, error } = await supabase
        .from('fleet_vehicles')
        .update({
          make: vehicleData.make,
          model: vehicleData.model,
          year: vehicleData.year,
          license_plate: vehicleData.license_plate,
          vehicle_number: vehicleData.vehicle_number,
          fuel_type: vehicleData.fuel_type,
          status: vehicleData.status,
          current_mileage: vehicleData.current_mileage,
        })
        .eq('id', vehicleData.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fleet-vehicles'] });
      toast({
        title: "Success",
        description: "Vehicle updated successfully",
      });
      setIsEditDialogOpen(false);
      setEditingVehicle(null);
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update vehicle",
        variant: "destructive",
      });
    },
  });

  // Add fuel transaction mutation
  const addFuelMutation = useMutation({
    mutationFn: async ({ vehicleId, fuelData }: any) => {
      const totalCost = parseFloat(fuelData.fuel_amount) * parseFloat(fuelData.cost_per_unit);
      const { data, error } = await supabase
        .from('fuel_transactions')
        .insert([{
          vehicle_id: vehicleId,
          fuel_amount: parseFloat(fuelData.fuel_amount),
          cost_per_unit: parseFloat(fuelData.cost_per_unit),
          total_cost: totalCost,
          fuel_station: fuelData.fuel_station,
          transaction_date: new Date().toISOString(),
        }])
        .select()
        .single();
      
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fuel-transactions'] });
      toast({
        title: "Success",
        description: "Fuel transaction recorded successfully",
      });
      setIsFuelDialogOpen(false);
      setFuelData({
        fuel_amount: "",
        cost_per_unit: "",
        fuel_station: "",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to record fuel transaction",
        variant: "destructive",
      });
    },
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case 'maintenance':
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Maintenance</Badge>;
      case 'inactive':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Inactive</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const calculateMonthlyFuelCost = () => {
    if (!fuelTransactions) return 0;
    return fuelTransactions.reduce((total, transaction) => total + (transaction.total_cost || 0), 0);
  };

  const filteredVehicles = vehicles?.filter(vehicle =>
    vehicle.make?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vehicle.model?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vehicle.license_plate?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6" data-aos="fade-up">
      {/* Search and Add Vehicle */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vehicles..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Vehicle
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Vehicle</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="make">Make</Label>
                  <Input
                    id="make"
                    value={newVehicle.make}
                    onChange={(e) => setNewVehicle({...newVehicle, make: e.target.value})}
                    placeholder="Toyota"
                  />
                </div>
                <div>
                  <Label htmlFor="model">Model</Label>
                  <Input
                    id="model"
                    value={newVehicle.model}
                    onChange={(e) => setNewVehicle({...newVehicle, model: e.target.value})}
                    placeholder="Camry"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="year">Year</Label>
                  <Input
                    id="year"
                    type="number"
                    value={newVehicle.year}
                    onChange={(e) => setNewVehicle({...newVehicle, year: parseInt(e.target.value)})}
                    min="1990"
                    max={new Date().getFullYear() + 1}
                  />
                </div>
                <div>
                  <Label htmlFor="license_plate">License Plate</Label>
                  <Input
                    id="license_plate"
                    value={newVehicle.license_plate}
                    onChange={(e) => setNewVehicle({...newVehicle, license_plate: e.target.value})}
                    placeholder="ABC-123-XYZ"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="vehicle_number">Vehicle Number</Label>
                <Input
                  id="vehicle_number"
                  value={newVehicle.vehicle_number}
                  onChange={(e) => setNewVehicle({...newVehicle, vehicle_number: e.target.value})}
                  placeholder="FLEET-001"
                />
              </div>
              <div>
                <Label htmlFor="fuel_type">Fuel Type</Label>
                <Select value={newVehicle.fuel_type} onValueChange={(value) => setNewVehicle({...newVehicle, fuel_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gasoline">Gasoline</SelectItem>
                    <SelectItem value="diesel">Diesel</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                    <SelectItem value="electric">Electric</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                onClick={() => createVehicleMutation.mutate(newVehicle)}
                disabled={!newVehicle.make || !newVehicle.model || !newVehicle.license_plate || createVehicleMutation.isPending}
                className="w-full"
              >
                {createVehicleMutation.isPending ? "Adding..." : "Add Vehicle"}
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Edit Vehicle Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Vehicle</DialogTitle>
            </DialogHeader>
            {editingVehicle && (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit_make">Make</Label>
                    <Input
                      id="edit_make"
                      value={editingVehicle.make}
                      onChange={(e) => setEditingVehicle({...editingVehicle, make: e.target.value})}
                      placeholder="Toyota"
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit_model">Model</Label>
                    <Input
                      id="edit_model"
                      value={editingVehicle.model}
                      onChange={(e) => setEditingVehicle({...editingVehicle, model: e.target.value})}
                      placeholder="Camry"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit_year">Year</Label>
                    <Input
                      id="edit_year"
                      type="number"
                      value={editingVehicle.year}
                      onChange={(e) => setEditingVehicle({...editingVehicle, year: parseInt(e.target.value)})}
                      min="1990"
                      max={new Date().getFullYear() + 1}
                    />
                  </div>
                  <div>
                    <Label htmlFor="edit_license_plate">License Plate</Label>
                    <Input
                      id="edit_license_plate"
                      value={editingVehicle.license_plate}
                      onChange={(e) => setEditingVehicle({...editingVehicle, license_plate: e.target.value})}
                      placeholder="ABC-123-XYZ"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit_vehicle_number">Vehicle Number</Label>
                  <Input
                    id="edit_vehicle_number"
                    value={editingVehicle.vehicle_number}
                    onChange={(e) => setEditingVehicle({...editingVehicle, vehicle_number: e.target.value})}
                    placeholder="FLEET-001"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit_fuel_type">Fuel Type</Label>
                    <Select value={editingVehicle.fuel_type} onValueChange={(value) => setEditingVehicle({...editingVehicle, fuel_type: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gasoline">Gasoline</SelectItem>
                        <SelectItem value="diesel">Diesel</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                        <SelectItem value="electric">Electric</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="edit_status">Status</Label>
                    <Select value={editingVehicle.status} onValueChange={(value) => setEditingVehicle({...editingVehicle, status: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit_current_mileage">Current Mileage</Label>
                  <Input
                    id="edit_current_mileage"
                    type="number"
                    value={editingVehicle.current_mileage || 0}
                    onChange={(e) => setEditingVehicle({...editingVehicle, current_mileage: parseInt(e.target.value)})}
                    placeholder="0"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsEditDialogOpen(false);
                      setEditingVehicle(null);
                    }}
                    className="flex-1"
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={() => editVehicleMutation.mutate(editingVehicle)}
                    disabled={!editingVehicle.make || !editingVehicle.model || !editingVehicle.license_plate || editVehicleMutation.isPending}
                    className="flex-1"
                  >
                    {editVehicleMutation.isPending ? "Updating..." : "Update Vehicle"}
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4" data-aos="fade-up" data-aos-delay="100">
        <Card>
          <CardContent className="flex items-center p-6">
            <Car className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Vehicles</p>
              <p className="text-2xl font-bold">{vehicles?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Active</p>
              <p className="text-2xl font-bold">{vehicles?.filter(v => v.status === 'active').length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Clock className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">In Maintenance</p>
              <p className="text-2xl font-bold">{vehicles?.filter(v => v.status === 'maintenance').length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Fuel className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Fuel Cost (Month)</p>
              <p className="text-2xl font-bold">₦{calculateMonthlyFuelCost().toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vehicles Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" data-aos="fade-up" data-aos-delay="300">
        {isLoading ? (
          <div className="col-span-full text-center py-8">
            <div className="text-muted-foreground">Loading vehicles...</div>
          </div>
        ) : filteredVehicles?.length === 0 ? (
          <div className="col-span-full text-center py-8">
            <div className="text-muted-foreground">No vehicles found</div>
          </div>
        ) : (
          filteredVehicles?.map((vehicle) => (
            <Card key={vehicle.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{vehicle.year} {vehicle.make} {vehicle.model}</CardTitle>
                    <p className="text-sm text-muted-foreground">{vehicle.license_plate}</p>
                  </div>
                  {getStatusBadge(vehicle.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm">
                      <User className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{vehicle.assigned_driver?.full_name || 'Unassigned'}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{vehicle.department?.name || 'Unassigned'}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <Fuel className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>Fuel Type</span>
                    </div>
                    <span className="capitalize">{vehicle.fuel_type}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>Vehicle #</span>
                    </div>
                    <span>{vehicle.vehicle_number || 'N/A'}</span>
                  </div>
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={() => {
                      setEditingVehicle({...vehicle});
                      setIsEditDialogOpen(true);
                    }}
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex-1"
                    onClick={() => {
                      setSelectedVehicle(vehicle);
                      setIsFuelDialogOpen(true);
                    }}
                  >
                    <Fuel className="h-3 w-3 mr-1" />
                    Add Fuel
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Fuel Transaction Dialog */}
      <Dialog open={isFuelDialogOpen} onOpenChange={setIsFuelDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Fuel Transaction</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Vehicle</Label>
              <p className="text-sm text-muted-foreground">
                {selectedVehicle?.year} {selectedVehicle?.make} {selectedVehicle?.model} ({selectedVehicle?.license_plate})
              </p>
            </div>
            <div>
              <Label htmlFor="fuel_amount">Fuel Amount (Liters)</Label>
              <Input
                id="fuel_amount"
                type="number"
                value={fuelData.fuel_amount}
                onChange={(e) => setFuelData({...fuelData, fuel_amount: e.target.value})}
                placeholder="50"
              />
            </div>
            <div>
              <Label htmlFor="cost_per_unit">Cost per Unit (₦)</Label>
              <Input
                id="cost_per_unit"
                type="number"
                value={fuelData.cost_per_unit}
                onChange={(e) => setFuelData({...fuelData, cost_per_unit: e.target.value})}
                placeholder="200"
              />
            </div>
            <div>
              <Label htmlFor="fuel_station">Fuel Station</Label>
              <Input
                id="fuel_station"
                value={fuelData.fuel_station}
                onChange={(e) => setFuelData({...fuelData, fuel_station: e.target.value})}
                placeholder="Total Station, Lagos"
              />
            </div>
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm font-medium">Total Cost: ₦{(parseFloat(fuelData.fuel_amount || '0') * parseFloat(fuelData.cost_per_unit || '0')).toLocaleString()}</p>
            </div>
            <Button 
              onClick={() => addFuelMutation.mutate({ vehicleId: selectedVehicle?.id, fuelData })}
              disabled={!fuelData.fuel_amount || !fuelData.cost_per_unit || addFuelMutation.isPending}
              className="w-full"
            >
              {addFuelMutation.isPending ? "Recording..." : "Record Transaction"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FleetPage;
