import { supabase } from '../integrations/supabase/client';

interface TableInfo {
  table_name: string;
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
}

interface ForeignKeyInfo {
  table_name: string;
  column_name: string;
  foreign_table_name: string;
  foreign_column_name: string;
  constraint_name: string;
}

interface SchemaAnalysisResult {
  tables: string[];
  missingTables: string[];
  foreignKeys: ForeignKeyInfo[];
  missingForeignKeys: string[];
  recommendations: string[];
}

export class DatabaseSchemaAnalyzer {
  
  // Expected tables in the system
  private expectedTables = [
    'profiles',
    'departments',
    'projects',
    'project_assignments',
    'tasks',
    'time_logs',
    'assets_inventory',
    'expense_reports',
    'budgets',
    'memos',
    'notifications',
    'recent_activity_logs',
    'system_activities',
    'document_archive',
    'file_uploads',
    'invoices',
    'battery_management',
    'telecom_sites',
    'leave_requests',
    'leave_balances'
  ];

  // Expected foreign key relationships
  private expectedForeignKeys = [
    { table: 'profiles', column: 'department_id', references: 'departments(id)' },
    { table: 'projects', column: 'manager_id', references: 'profiles(id)' },
    { table: 'projects', column: 'department_id', references: 'departments(id)' },
    { table: 'project_assignments', column: 'project_id', references: 'projects(id)' },
    { table: 'project_assignments', column: 'assigned_to', references: 'profiles(id)' },
    { table: 'project_assignments', column: 'assigned_by', references: 'profiles(id)' },
    { table: 'project_assignments', column: 'department_id', references: 'departments(id)' },
    { table: 'tasks', column: 'project_id', references: 'projects(id)' },
    { table: 'tasks', column: 'assigned_to_id', references: 'profiles(id)' },
    { table: 'tasks', column: 'created_by', references: 'profiles(id)' },
    { table: 'time_logs', column: 'user_id', references: 'profiles(id)' },
    { table: 'time_logs', column: 'project_id', references: 'projects(id)' },
    { table: 'assets_inventory', column: 'created_by', references: 'profiles(id)' },
    { table: 'assets_inventory', column: 'department_id', references: 'departments(id)' },
    { table: 'expense_reports', column: 'submitted_by', references: 'profiles(id)' },
    { table: 'expense_reports', column: 'approved_by', references: 'profiles(id)' },
    { table: 'expense_reports', column: 'department_id', references: 'departments(id)' },
    { table: 'expense_reports', column: 'project_id', references: 'projects(id)' },
    { table: 'budgets', column: 'department_id', references: 'departments(id)' },
    { table: 'budgets', column: 'created_by', references: 'profiles(id)' },
    { table: 'budgets', column: 'approved_by', references: 'profiles(id)' },
    { table: 'memos', column: 'created_by', references: 'profiles(id)' },
    { table: 'memos', column: 'department_id', references: 'departments(id)' },
    { table: 'notifications', column: 'user_id', references: 'profiles(id)' },
    { table: 'document_archive', column: 'uploaded_by', references: 'profiles(id)' },
    { table: 'document_archive', column: 'department_id', references: 'departments(id)' },
    { table: 'file_uploads', column: 'uploaded_by', references: 'profiles(id)' },
    { table: 'invoices', column: 'created_by', references: 'profiles(id)' },
    { table: 'leave_requests', column: 'user_id', references: 'profiles(id)' },
    { table: 'leave_requests', column: 'approved_by', references: 'profiles(id)' },
    { table: 'leave_balances', column: 'user_id', references: 'profiles(id)' }
  ];

  /**
   * Get all tables in the database
   */
  private async getTables(): Promise<string[]> {
    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_type = 'BASE TABLE'
          ORDER BY table_name;
        `
      });

      if (error) {
        console.error('Error getting tables:', error);
        return [];
      }

      return data?.map((row: any) => row.table_name) || [];
    } catch (error) {
      console.error('Failed to get tables:', error);
      return [];
    }
  }

  /**
   * Get foreign key constraints
   */
  private async getForeignKeys(): Promise<ForeignKeyInfo[]> {
    try {
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          SELECT 
            tc.table_name,
            kcu.column_name,
            ccu.table_name AS foreign_table_name,
            ccu.column_name AS foreign_column_name,
            tc.constraint_name
          FROM 
            information_schema.table_constraints AS tc 
            JOIN information_schema.key_column_usage AS kcu
              ON tc.constraint_name = kcu.constraint_name
              AND tc.table_schema = kcu.table_schema
            JOIN information_schema.constraint_column_usage AS ccu
              ON ccu.constraint_name = tc.constraint_name
              AND ccu.table_schema = tc.table_schema
          WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_schema = 'public'
          ORDER BY tc.table_name, kcu.column_name;
        `
      });

      if (error) {
        console.error('Error getting foreign keys:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get foreign keys:', error);
      return [];
    }
  }

  /**
   * Analyze the database schema
   */
  public async analyzeSchema(): Promise<SchemaAnalysisResult> {
    console.log('🔍 Starting comprehensive database schema analysis...');

    const [tables, foreignKeys] = await Promise.all([
      this.getTables(),
      this.getForeignKeys()
    ]);

    console.log(`📊 Found ${tables.length} tables and ${foreignKeys.length} foreign keys`);

    // Find missing tables
    const missingTables = this.expectedTables.filter(table => !tables.includes(table));

    // Find missing foreign keys
    const missingForeignKeys: string[] = [];
    
    for (const expected of this.expectedForeignKeys) {
      const exists = foreignKeys.some(fk => 
        fk.table_name === expected.table && 
        fk.column_name === expected.column
      );
      
      if (!exists && tables.includes(expected.table)) {
        missingForeignKeys.push(`${expected.table}.${expected.column} -> ${expected.references}`);
      }
    }

    // Generate recommendations
    const recommendations = this.generateRecommendations(missingTables, missingForeignKeys);

    const result: SchemaAnalysisResult = {
      tables,
      missingTables,
      foreignKeys,
      missingForeignKeys,
      recommendations
    };

    this.printAnalysisReport(result);
    return result;
  }

  /**
   * Generate recommendations based on analysis
   */
  private generateRecommendations(missingTables: string[], missingForeignKeys: string[]): string[] {
    const recommendations: string[] = [];

    if (missingTables.length > 0) {
      recommendations.push('Create missing tables using migration scripts');
      recommendations.push('Run: npm run migrate or execute SQL scripts in src/scripts/');
    }

    if (missingForeignKeys.length > 0) {
      recommendations.push('Add missing foreign key constraints');
      recommendations.push('This will improve data integrity and enable proper joins');
    }

    if (missingTables.includes('assets_inventory')) {
      recommendations.push('Create assets_inventory table for asset management functionality');
    }

    if (missingForeignKeys.some(fk => fk.includes('project_assignments'))) {
      recommendations.push('Fix project_assignments table foreign keys for proper project management');
    }

    return recommendations;
  }

  /**
   * Print analysis report
   */
  private printAnalysisReport(result: SchemaAnalysisResult): void {
    console.log('\n📋 DATABASE SCHEMA ANALYSIS REPORT');
    console.log('=====================================');
    
    console.log(`\n✅ Existing Tables (${result.tables.length}):`);
    result.tables.forEach(table => console.log(`  - ${table}`));
    
    if (result.missingTables.length > 0) {
      console.log(`\n❌ Missing Tables (${result.missingTables.length}):`);
      result.missingTables.forEach(table => console.log(`  - ${table}`));
    }
    
    console.log(`\n🔗 Existing Foreign Keys (${result.foreignKeys.length}):`);
    result.foreignKeys.forEach(fk => 
      console.log(`  - ${fk.table_name}.${fk.column_name} -> ${fk.foreign_table_name}.${fk.foreign_column_name}`)
    );
    
    if (result.missingForeignKeys.length > 0) {
      console.log(`\n⚠️ Missing Foreign Keys (${result.missingForeignKeys.length}):`);
      result.missingForeignKeys.forEach(fk => console.log(`  - ${fk}`));
    }
    
    if (result.recommendations.length > 0) {
      console.log(`\n💡 Recommendations:`);
      result.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
    
    console.log('\n=====================================');
  }
}

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  const analyzer = new DatabaseSchemaAnalyzer();
  (window as any).analyzeDatabase = () => analyzer.analyzeSchema();
  console.log('🛠️ Database schema analyzer loaded. Run analyzeDatabase() to start analysis.');
}

export const databaseSchemaAnalyzer = new DatabaseSchemaAnalyzer();
