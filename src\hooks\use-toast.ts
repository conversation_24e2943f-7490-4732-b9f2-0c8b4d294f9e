import { toast as sonnerToast } from "sonner"

// Compatibility layer to maintain existing useToast API while using Sonner under the hood
type ToastVariant = "default" | "destructive" | "success" | "warning" | "info"

interface ToastOptions {
  title?: string
  description?: string
  variant?: ToastVariant
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// Sonner-based toast function that maintains compatibility with existing API
function toast(options: ToastOptions | string) {
  // Handle string input (just a message)
  if (typeof options === 'string') {
    sonnerToast(options)
    return { id: Date.now().toString(), dismiss: () => {}, update: () => {} }
  }

  const { title, description, variant = "default", duration, action } = options

  // Create the message
  const message = title || description || ""
  const desc = title && description ? description : undefined

  // Map variants to Sonner methods
  switch (variant) {
    case "destructive":
      sonnerToast.error(message, {
        description: desc,
        duration,
        action: action ? {
          label: action.label,
          onClick: action.onClick
        } : undefined
      })
      break
    case "success":
      sonnerToast.success(message, {
        description: desc,
        duration,
        action: action ? {
          label: action.label,
          onClick: action.onClick
        } : undefined
      })
      break
    case "warning":
      sonnerToast.warning(message, {
        description: desc,
        duration,
        action: action ? {
          label: action.label,
          onClick: action.onClick
        } : undefined
      })
      break
    case "info":
      sonnerToast.info(message, {
        description: desc,
        duration,
        action: action ? {
          label: action.label,
          onClick: action.onClick
        } : undefined
      })
      break
    default:
      sonnerToast(message, {
        description: desc,
        duration,
        action: action ? {
          label: action.label,
          onClick: action.onClick
        } : undefined
      })
  }

  return {
    id: Date.now().toString(),
    dismiss: () => sonnerToast.dismiss(),
    update: () => {} // Sonner doesn't support updates, but we provide a no-op for compatibility
  }
}

// Hook that provides the toast function (maintains compatibility)
function useToast() {
  return {
    toast,
    dismiss: (toastId?: string) => sonnerToast.dismiss(toastId),
    toasts: [] // Empty array for compatibility, Sonner manages its own state
  }
}

export { useToast, toast }
