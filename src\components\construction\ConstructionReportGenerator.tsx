import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { FileText, Download, Calendar, MapPin, User, AlertTriangle } from "lucide-react";
import { format } from "date-fns";

interface ConstructionReportGeneratorProps {
  siteId?: string;
  siteName?: string;
  onReportGenerated?: () => void;
}

export const ConstructionReportGenerator = ({ 
  siteId, 
  siteName, 
  onReportGenerated 
}: ConstructionReportGeneratorProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportData, setReportData] = useState({
    reportTitle: '',
    reportType: 'progress',
    workPerformed: '',
    progressPercentage: 0,
    issuesEncountered: '',
    nextSteps: '',
    weatherConditions: '',
    safetyIncidents: '',
    materialsUsed: '',
    laborHours: 0,
    budgetSpent: 0,
    qualityRating: 'good'
  });
  
  const { toast } = useToast();
  const { userProfile } = useAuth();

  const handleInputChange = (field: string, value: any) => {
    setReportData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateReport = async () => {
    if (!userProfile?.id) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    if (!reportData.reportTitle.trim()) {
      toast({
        title: "Error",
        description: "Please enter a report title",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    try {
      // Create construction report
      const { data, error } = await supabase
        .from('construction_reports')
        .insert([{
          report_title: reportData.reportTitle,
          report_type: reportData.reportType,
          site_id: siteId || '00000000-0000-0000-0000-000000000000',
          work_performed: reportData.workPerformed,
          progress_percentage: reportData.progressPercentage,
          issues_encountered: reportData.issuesEncountered,
          next_steps: reportData.nextSteps,
          weather_conditions: reportData.weatherConditions,
          safety_incidents: reportData.safetyIncidents,
          materials_used: reportData.materialsUsed,
          labor_hours: reportData.laborHours,
          budget_spent: reportData.budgetSpent,
          quality_rating: reportData.qualityRating,
          created_by: userProfile.id,
          report_date: new Date().toISOString().split('T')[0]
        }])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Report Generated",
        description: `Construction report "${reportData.reportTitle}" has been generated successfully`,
      });

      // Reset form
      setReportData({
        reportTitle: '',
        reportType: 'progress',
        workPerformed: '',
        progressPercentage: 0,
        issuesEncountered: '',
        nextSteps: '',
        weatherConditions: '',
        safetyIncidents: '',
        materialsUsed: '',
        laborHours: 0,
        budgetSpent: 0,
        qualityRating: 'good'
      });

      if (onReportGenerated) {
        onReportGenerated();
      }

    } catch (error: any) {
      console.error('Error generating construction report:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to generate construction report",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadReport = () => {
    // Create a simple text report for download
    const reportContent = `
CONSTRUCTION REPORT
==================

Report Title: ${reportData.reportTitle}
Report Type: ${reportData.reportType}
Site: ${siteName || 'N/A'}
Date: ${format(new Date(), 'yyyy-MM-dd')}
Generated By: ${userProfile?.full_name || 'Unknown'}

WORK PERFORMED:
${reportData.workPerformed}

PROGRESS: ${reportData.progressPercentage}%

ISSUES ENCOUNTERED:
${reportData.issuesEncountered}

NEXT STEPS:
${reportData.nextSteps}

WEATHER CONDITIONS:
${reportData.weatherConditions}

SAFETY INCIDENTS:
${reportData.safetyIncidents}

MATERIALS USED:
${reportData.materialsUsed}

LABOR HOURS: ${reportData.laborHours}
BUDGET SPENT: $${reportData.budgetSpent}
QUALITY RATING: ${reportData.qualityRating}
    `;

    const blob = new Blob([reportContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `construction-report-${format(new Date(), 'yyyy-MM-dd')}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Report Downloaded",
      description: "Construction report has been downloaded as a text file",
    });
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Construction Report Generator
          {siteName && (
            <span className="text-sm font-normal text-muted-foreground">
              - {siteName}
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="reportTitle">Report Title *</Label>
            <Input
              id="reportTitle"
              value={reportData.reportTitle}
              onChange={(e) => handleInputChange('reportTitle', e.target.value)}
              placeholder="Enter report title"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="reportType">Report Type</Label>
            <Select 
              value={reportData.reportType} 
              onValueChange={(value) => handleInputChange('reportType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="progress">Progress Report</SelectItem>
                <SelectItem value="daily">Daily Report</SelectItem>
                <SelectItem value="weekly">Weekly Report</SelectItem>
                <SelectItem value="monthly">Monthly Report</SelectItem>
                <SelectItem value="incident">Incident Report</SelectItem>
                <SelectItem value="completion">Completion Report</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Work Details */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="workPerformed">Work Performed</Label>
            <Textarea
              id="workPerformed"
              value={reportData.workPerformed}
              onChange={(e) => handleInputChange('workPerformed', e.target.value)}
              placeholder="Describe the work performed during this period"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="progressPercentage">Progress (%)</Label>
              <Input
                id="progressPercentage"
                type="number"
                min="0"
                max="100"
                value={reportData.progressPercentage}
                onChange={(e) => handleInputChange('progressPercentage', parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="laborHours">Labor Hours</Label>
              <Input
                id="laborHours"
                type="number"
                min="0"
                value={reportData.laborHours}
                onChange={(e) => handleInputChange('laborHours', parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="budgetSpent">Budget Spent ($)</Label>
              <Input
                id="budgetSpent"
                type="number"
                min="0"
                step="0.01"
                value={reportData.budgetSpent}
                onChange={(e) => handleInputChange('budgetSpent', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>
        </div>

        {/* Additional Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="issuesEncountered">Issues Encountered</Label>
            <Textarea
              id="issuesEncountered"
              value={reportData.issuesEncountered}
              onChange={(e) => handleInputChange('issuesEncountered', e.target.value)}
              placeholder="Describe any issues or challenges"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="nextSteps">Next Steps</Label>
            <Textarea
              id="nextSteps"
              value={reportData.nextSteps}
              onChange={(e) => handleInputChange('nextSteps', e.target.value)}
              placeholder="Outline the next steps and upcoming work"
              rows={3}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-4 pt-4">
          <Button 
            onClick={generateReport} 
            disabled={isGenerating}
            className="flex items-center gap-2"
          >
            <FileText className="h-4 w-4" />
            {isGenerating ? 'Generating...' : 'Generate Report'}
          </Button>
          <Button 
            variant="outline" 
            onClick={downloadReport}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download Preview
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
