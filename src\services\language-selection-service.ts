import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { VoiceRecognitionService } from './voice-recognition-service';
import { VoiceResponseService } from './voice-response-service';
import { SystemLogsService } from './system-logs-service';
import { UserActivitiesService } from './user-activities-service';

/**
 * Language Selection Service
 * Manages language preferences with focus on Nigerian languages
 * Provides language selection interface and voice samples
 */

export interface LanguageOption {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  isNigerian: boolean;
  voiceSupported: boolean;
  recognitionSupported: boolean;
  sampleText: string;
  greeting: string;
}

export interface UserLanguagePreference {
  userId: string;
  primaryLanguage: string;
  secondaryLanguages: string[];
  voiceLanguage: string;
  interfaceLanguage: string;
  autoDetect: boolean;
  lastUpdated: string;
}

export class LanguageSelectionService {
  private static currentLanguage: string = 'en-GB'; // UK English default
  private static userPreferences: UserLanguagePreference | null = null;
  private static availableLanguages: LanguageOption[] = [];
  private static isInitialized: boolean = false;

  // Initialize language selection service
  static async initialize(): Promise<boolean> {
    try {
      console.log('🌍 Initializing Language Selection Service...');

      // Initialize available languages
      this.initializeLanguages();

      // Load user preferences
      await this.loadUserLanguagePreferences();

      // Set up language detection
      this.setupLanguageDetection();

      this.isInitialized = true;
      console.log('✅ Language Selection Service initialized successfully');

      await SystemLogsService.info('language', 'Language service initialized', 
        `Default language: ${this.currentLanguage}`);

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Language Selection Service:', error);
      await SystemLogsService.error('language', 'Language service initialization failed', error.toString());
      return false;
    }
  }

  // Initialize available languages with Nigerian focus
  private static initializeLanguages(): void {
    this.availableLanguages = [
      {
        code: 'en-GB',
        name: 'English (UK)',
        nativeName: 'English (United Kingdom)',
        flag: '🇬🇧',
        isNigerian: false,
        voiceSupported: true,
        recognitionSupported: true,
        sampleText: 'Welcome to CTN Nigeria. How may I assist you today?',
        greeting: 'Hello! I\'m your AI assistant.'
      },
      {
        code: 'ig-NG',
        name: 'Igbo',
        nativeName: 'Asụsụ Igbo',
        flag: '🇳🇬',
        isNigerian: true,
        voiceSupported: true,
        recognitionSupported: true,
        sampleText: 'Nnọọ na CTN Nigeria. Kedu ka m ga-esi nyere gị aka taa?',
        greeting: 'Nnọọ! Abụ m onye inyeaka AI gị.'
      },
      {
        code: 'ha-NG',
        name: 'Hausa',
        nativeName: 'Harshen Hausa',
        flag: '🇳🇬',
        isNigerian: true,
        voiceSupported: true,
        recognitionSupported: true,
        sampleText: 'Maraba zuwa CTN Nigeria. Ta yaya zan iya taimaka muku yau?',
        greeting: 'Sannu! Ni ne mai taimako AI naku.'
      },
      {
        code: 'yo-NG',
        name: 'Yoruba',
        nativeName: 'Èdè Yorùbá',
        flag: '🇳🇬',
        isNigerian: true,
        voiceSupported: true,
        recognitionSupported: true,
        sampleText: 'Kaabo si CTN Nigeria. Bawo ni mo se le ran yin lowo loni?',
        greeting: 'Bawo! Emi ni oluranlowo AI yin.'
      },
      {
        code: 'en-US',
        name: 'English (US)',
        nativeName: 'English (United States)',
        flag: '🇺🇸',
        isNigerian: false,
        voiceSupported: true,
        recognitionSupported: true,
        sampleText: 'Welcome to CTN Nigeria. How can I help you today?',
        greeting: 'Hi there! I\'m your AI assistant.'
      },
      {
        code: 'fr-FR',
        name: 'French',
        nativeName: 'Français',
        flag: '🇫🇷',
        isNigerian: false,
        voiceSupported: true,
        recognitionSupported: true,
        sampleText: 'Bienvenue à CTN Nigeria. Comment puis-je vous aider aujourd\'hui?',
        greeting: 'Bonjour! Je suis votre assistant IA.'
      }
    ];
  }

  // Load user language preferences
  private static async loadUserLanguagePreferences(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) {
        // Use default language for anonymous users
        this.currentLanguage = 'en-GB';
        return;
      }

      const { data: preferences } = await supabase
        .from('voice_user_preferences')
        .select('*')
        .eq('user_id', currentUser.user_id)
        .single();

      if (preferences) {
        this.userPreferences = {
          userId: currentUser.user_id,
          primaryLanguage: preferences.preferred_language || 'en-GB',
          secondaryLanguages: preferences.secondary_languages || [],
          voiceLanguage: preferences.preferred_language || 'en-GB',
          interfaceLanguage: preferences.interface_language || 'en-GB',
          autoDetect: preferences.auto_detect_language || false,
          lastUpdated: preferences.updated_at
        };

        this.currentLanguage = this.userPreferences.primaryLanguage;
        console.log('🌍 User language preferences loaded:', this.currentLanguage);
      } else {
        // Create default preferences
        await this.createDefaultPreferences(currentUser.user_id);
      }
    } catch (error) {
      console.warn('Could not load user language preferences:', error);
      this.currentLanguage = 'en-GB'; // Fallback to UK English
    }
  }

  // Create default language preferences
  private static async createDefaultPreferences(userId: string): Promise<void> {
    try {
      const defaultPreferences = {
        user_id: userId,
        preferred_language: 'en-GB',
        secondary_languages: ['en-US'],
        interface_language: 'en-GB',
        auto_detect_language: false,
        voice_speed: 1.0,
        voice_pitch: 1.0,
        voice_volume: 1.0,
        preferred_voice_gender: 'neutral',
        wake_word: 'Hey Assistant',
        auto_listen: false,
        voice_feedback_enabled: true,
        visual_feedback_enabled: true,
        confirmation_required: false,
        privacy_mode: false,
        accessibility_features: {},
        custom_commands: {},
        blocked_features: [],
        notification_preferences: {},
        session_timeout_minutes: 30,
        auto_save_conversations: true,
        metadata: { created_by: 'language_service' }
      };

      await supabase
        .from('voice_user_preferences')
        .insert(defaultPreferences);

      this.userPreferences = {
        userId,
        primaryLanguage: 'en-GB',
        secondaryLanguages: ['en-US'],
        voiceLanguage: 'en-GB',
        interfaceLanguage: 'en-GB',
        autoDetect: false,
        lastUpdated: new Date().toISOString()
      };

      console.log('🌍 Default language preferences created');
    } catch (error) {
      console.error('Error creating default language preferences:', error);
    }
  }

  // Set up language detection
  private static setupLanguageDetection(): void {
    // Detect browser language
    const browserLanguage = navigator.language || navigator.languages?.[0] || 'en-GB';
    
    // Check if browser language is supported
    const supportedLanguage = this.availableLanguages.find(lang => 
      lang.code === browserLanguage || lang.code.startsWith(browserLanguage.split('-')[0])
    );

    if (supportedLanguage && !this.userPreferences) {
      this.currentLanguage = supportedLanguage.code;
      console.log('🌍 Browser language detected:', this.currentLanguage);
    }
  }

  // Get available languages
  static getAvailableLanguages(): LanguageOption[] {
    return [...this.availableLanguages];
  }

  // Get Nigerian languages only
  static getNigerianLanguages(): LanguageOption[] {
    return this.availableLanguages.filter(lang => lang.isNigerian || lang.code === 'en-GB');
  }

  // Get current language
  static getCurrentLanguage(): string {
    return this.currentLanguage;
  }

  // Get current language option
  static getCurrentLanguageOption(): LanguageOption | null {
    return this.availableLanguages.find(lang => lang.code === this.currentLanguage) || null;
  }

  // Set language
  static async setLanguage(languageCode: string): Promise<boolean> {
    try {
      const language = this.availableLanguages.find(lang => lang.code === languageCode);
      if (!language) {
        throw new Error(`Language ${languageCode} not supported`);
      }

      const previousLanguage = this.currentLanguage;
      this.currentLanguage = languageCode;

      // Update voice services
      VoiceRecognitionService.updateConfig({ language: languageCode });
      
      // Update user preferences
      await this.saveLanguagePreference(languageCode);

      // Log language change
      await UserActivitiesService.logActivity(
        'update',
        'Language Changed',
        `Changed language from ${previousLanguage} to ${languageCode}`,
        'language',
        null,
        'Language Preference'
      );

      console.log('🌍 Language changed to:', languageCode);
      return true;
    } catch (error) {
      console.error('Error setting language:', error);
      return false;
    }
  }

  // Save language preference
  private static async saveLanguagePreference(languageCode: string): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return;

      await supabase
        .from('voice_user_preferences')
        .upsert({
          user_id: currentUser.user_id,
          preferred_language: languageCode,
          interface_language: languageCode,
          updated_at: new Date().toISOString()
        });

      if (this.userPreferences) {
        this.userPreferences.primaryLanguage = languageCode;
        this.userPreferences.voiceLanguage = languageCode;
        this.userPreferences.interfaceLanguage = languageCode;
        this.userPreferences.lastUpdated = new Date().toISOString();
      }
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  }

  // Test language with voice sample
  static async testLanguageVoice(languageCode: string): Promise<boolean> {
    try {
      const language = this.availableLanguages.find(lang => lang.code === languageCode);
      if (!language || !language.voiceSupported) {
        return false;
      }

      // Speak sample text in the selected language
      await VoiceResponseService.speak({
        text: language.sampleText,
        voice: languageCode,
        interrupt: true
      });

      return true;
    } catch (error) {
      console.error('Error testing language voice:', error);
      return false;
    }
  }

  // Get localized text
  static getLocalizedText(key: string, languageCode?: string): string {
    const lang = languageCode || this.currentLanguage;
    
    const translations: Record<string, Record<string, string>> = {
      'welcome': {
        'en-GB': 'Welcome to CTN Nigeria',
        'en-US': 'Welcome to CTN Nigeria',
        'ig-NG': 'Nnọọ na CTN Nigeria',
        'ha-NG': 'Maraba zuwa CTN Nigeria',
        'yo-NG': 'Kaabo si CTN Nigeria',
        'fr-FR': 'Bienvenue à CTN Nigeria'
      },
      'how_can_help': {
        'en-GB': 'How may I assist you today?',
        'en-US': 'How can I help you today?',
        'ig-NG': 'Kedu ka m ga-esi nyere gị aka taa?',
        'ha-NG': 'Ta yaya zan iya taimaka muku yau?',
        'yo-NG': 'Bawo ni mo se le ran yin lowo loni?',
        'fr-FR': 'Comment puis-je vous aider aujourd\'hui?'
      },
      'ai_assistant': {
        'en-GB': 'AI Assistant',
        'en-US': 'AI Assistant',
        'ig-NG': 'Onye Inyeaka AI',
        'ha-NG': 'Mai Taimako AI',
        'yo-NG': 'Oluranlowo AI',
        'fr-FR': 'Assistant IA'
      },
      'select_language': {
        'en-GB': 'Select Your Language',
        'en-US': 'Select Your Language',
        'ig-NG': 'Họrọ Asụsụ Gị',
        'ha-NG': 'Zaɓi Harshen Ku',
        'yo-NG': 'Yan Ede Yin',
        'fr-FR': 'Sélectionnez Votre Langue'
      }
    };

    return translations[key]?.[lang] || translations[key]?.['en-GB'] || key;
  }

  // Get user preferences
  static getUserPreferences(): UserLanguagePreference | null {
    return this.userPreferences;
  }

  // Check if language is supported
  static isLanguageSupported(languageCode: string): boolean {
    return this.availableLanguages.some(lang => lang.code === languageCode);
  }

  // Get language statistics
  static async getLanguageStats(): Promise<any> {
    try {
      const { data: stats } = await supabase
        .from('voice_user_preferences')
        .select('preferred_language')
        .not('preferred_language', 'is', null);

      if (stats) {
        const languageCount: Record<string, number> = {};
        stats.forEach(pref => {
          const lang = pref.preferred_language;
          languageCount[lang] = (languageCount[lang] || 0) + 1;
        });

        return {
          totalUsers: stats.length,
          languageDistribution: languageCount,
          mostPopular: Object.keys(languageCount).reduce((a, b) => 
            languageCount[a] > languageCount[b] ? a : b
          ),
          nigerianLanguageUsers: Object.keys(languageCount)
            .filter(lang => ['ig-NG', 'ha-NG', 'yo-NG'].includes(lang))
            .reduce((sum, lang) => sum + languageCount[lang], 0)
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting language stats:', error);
      return null;
    }
  }

  // Auto-detect language from text
  static detectLanguageFromText(text: string): string {
    const lowerText = text.toLowerCase();
    
    // Simple keyword-based detection for Nigerian languages
    const igboKeywords = ['ndewo', 'nnọọ', 'kedu', 'maka', 'gị', 'anyị'];
    const hausaKeywords = ['sannu', 'maraba', 'yaya', 'taimaka', 'ku', 'mu'];
    const yorubaKeywords = ['bawo', 'kaabo', 'se', 'yin', 'wa', 'ni'];

    if (igboKeywords.some(keyword => lowerText.includes(keyword))) {
      return 'ig-NG';
    }
    
    if (hausaKeywords.some(keyword => lowerText.includes(keyword))) {
      return 'ha-NG';
    }
    
    if (yorubaKeywords.some(keyword => lowerText.includes(keyword))) {
      return 'yo-NG';
    }

    // Default to current language
    return this.currentLanguage;
  }

  // Show language selection dialog
  static async showLanguageSelection(): Promise<string | null> {
    return new Promise((resolve) => {
      // This would typically show a modal dialog
      // For now, we'll use a simple prompt
      const languages = this.getNigerianLanguages();
      const options = languages.map((lang, index) => 
        `${index + 1}. ${lang.flag} ${lang.name} (${lang.nativeName})`
      ).join('\n');

      const choice = prompt(
        `${this.getLocalizedText('select_language')}:\n\n${options}\n\nEnter number (1-${languages.length}):`
      );

      if (choice) {
        const index = parseInt(choice) - 1;
        if (index >= 0 && index < languages.length) {
          resolve(languages[index].code);
        }
      }
      
      resolve(null);
    });
  }

  // Initialize with language selection
  static async initializeWithSelection(): Promise<void> {
    if (!this.userPreferences) {
      const selectedLanguage = await this.showLanguageSelection();
      if (selectedLanguage) {
        await this.setLanguage(selectedLanguage);
      }
    }
  }
}

export default LanguageSelectionService;
