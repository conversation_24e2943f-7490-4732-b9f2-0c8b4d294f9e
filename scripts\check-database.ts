import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseKey);

const requiredTables = [
  'profiles',
  'tasks', 
  'projects',
  'memos',
  'departments',
  'api_keys',
  'roles',
  'system_activities',
  'document_archive',
  'project_assignments',
  'project_progress_updates',
  'project_milestones',
  'budgets',
  'expenses',
  'telecom_sites',
  'integrations',
  'integration_logs',
  'memo_notifications',
  'leave_requests',
  'email_notifications',
  'battery_management',
  'leave_balances',
  'notifications',
  'notification_preferences',
  'role_dashboard_configs',
  'time_logs',
  'recent_activity_logs'
];

async function checkDatabase() {
  console.log('🔍 Checking database connection and tables...\n');

  try {
    // Test basic connection
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError.message);
      return;
    }

    console.log('✅ Database connection successful\n');

    // Check each required table
    const tableStatus: { [key: string]: boolean } = {};
    
    for (const table of requiredTables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);

        if (error) {
          console.log(`❌ Table '${table}': ${error.message}`);
          tableStatus[table] = false;
        } else {
          console.log(`✅ Table '${table}': OK`);
          tableStatus[table] = true;
        }
      } catch (err) {
        console.log(`❌ Table '${table}': Error checking table`);
        tableStatus[table] = false;
      }
    }

    // Summary
    const existingTables = Object.values(tableStatus).filter(Boolean).length;
    const missingTables = requiredTables.length - existingTables;

    console.log(`\n📊 Summary:`);
    console.log(`✅ Existing tables: ${existingTables}/${requiredTables.length}`);
    console.log(`❌ Missing tables: ${missingTables}`);

    if (missingTables > 0) {
      console.log('\n🔧 Missing tables:');
      Object.entries(tableStatus).forEach(([table, exists]) => {
        if (!exists) {
          console.log(`   - ${table}`);
        }
      });
    }

    // Check specific project management tables
    console.log('\n🎯 Project Management System Tables:');
    const projectTables = ['projects', 'project_assignments', 'project_progress_updates', 'project_milestones'];
    
    for (const table of projectTables) {
      if (tableStatus[table]) {
        try {
          const { count } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          console.log(`   ✅ ${table}: ${count || 0} records`);
        } catch (err) {
          console.log(`   ✅ ${table}: exists (count failed)`);
        }
      } else {
        console.log(`   ❌ ${table}: missing`);
      }
    }

    // Check time tracking tables
    console.log('\n⏰ Time Tracking System Tables:');
    const timeTables = ['time_logs', 'notifications', 'notification_preferences'];

    for (const table of timeTables) {
      if (tableStatus[table]) {
        try {
          const { count } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          console.log(`   ✅ ${table}: ${count || 0} records`);
        } catch (err) {
          console.log(`   ✅ ${table}: exists (count failed)`);
        }
      } else {
        console.log(`   ❌ ${table}: missing`);
      }
    }

    // Check project_assignments table structure
    console.log('\n🔍 Checking project_assignments table structure:');
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .select('*')
        .limit(1);

      if (error) {
        console.log(`   ❌ Error: ${error.message}`);
      } else {
        console.log(`   ✅ Table exists`);
        if (data && data.length > 0) {
          console.log(`   📋 Sample columns: ${Object.keys(data[0]).join(', ')}`);
        } else {
          console.log(`   📋 Table is empty - checking schema via error method`);
          // Try to insert invalid data to see column structure in error
          try {
            await supabase.from('project_assignments').insert({ invalid_column: 'test' });
          } catch (schemaError: any) {
            console.log(`   📋 Schema info from error: ${schemaError.message}`);
          }
        }
      }
    } catch (err) {
      console.log(`   ❌ Error checking structure: ${err}`);
    }

  } catch (error) {
    console.error('❌ Database check failed:', error);
  }
}

checkDatabase().then(() => {
  console.log('\n🏁 Database check completed');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
