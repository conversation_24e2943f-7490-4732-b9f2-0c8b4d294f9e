import { supabase } from '@/integrations/supabase/client';

/**
 * Comprehensive Dashboard and Database Fix
 * This script will fix all dashboard data display issues, RLS problems, and SQL syntax errors
 */

const logStep = (message: string) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message: string) => {
  console.log(`✅ ${message}`);
};

const logError = (message: string) => {
  console.error(`❌ ${message}`);
};

// Step 1: Create missing tables for dashboard data
const createMissingTables = async () => {
  logStep('Creating missing tables for dashboard data...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create invoices table for financial data
        CREATE TABLE IF NOT EXISTS public.invoices (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          invoice_number VARCHAR(50) UNIQUE NOT NULL,
          client_name TEXT NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          total_amount DECIMAL(10,2) NOT NULL,
          payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'overdue', 'cancelled')),
          due_date DATE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL
        );

        -- Create expense_reports table
        CREATE TABLE IF NOT EXISTS public.expense_reports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          amount DECIMAL(10,2) NOT NULL,
          category TEXT NOT NULL,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
          approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
          submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          receipt_url TEXT,
          expense_date DATE DEFAULT CURRENT_DATE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create expenses table (alternative naming)
        CREATE TABLE IF NOT EXISTS public.expenses (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          amount DECIMAL(10,2) NOT NULL,
          category TEXT NOT NULL,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
          created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          receipt_url TEXT,
          expense_date DATE DEFAULT CURRENT_DATE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create reports table for report submissions
        CREATE TABLE IF NOT EXISTS public.reports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          report_type TEXT DEFAULT 'general' CHECK (report_type IN ('general', 'financial', 'project', 'maintenance', 'incident', 'performance')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'completed')),
          submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          reviewed_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
          project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
          due_date DATE,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create battery_reports table for battery management
        CREATE TABLE IF NOT EXISTS public.battery_reports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          battery_id VARCHAR(50) NOT NULL,
          charge_level INTEGER CHECK (charge_level >= 0 AND charge_level <= 100),
          voltage_reading DECIMAL(5,2),
          temperature DECIMAL(5,2),
          status TEXT DEFAULT 'operational' CHECK (status IN ('operational', 'maintenance', 'faulty', 'replaced')),
          maintenance_notes TEXT,
          issues_found TEXT,
          corrective_actions TEXT,
          reported_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          report_date DATE DEFAULT CURRENT_DATE,
          approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create time_logs table for time tracking
        CREATE TABLE IF NOT EXISTS public.time_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
          task_id UUID REFERENCES public.tasks(id) ON DELETE SET NULL,
          description TEXT,
          hours_worked DECIMAL(5,2) NOT NULL,
          log_date DATE DEFAULT CURRENT_DATE,
          billable BOOLEAN DEFAULT true,
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'submitted', 'approved')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create notifications table
        CREATE TABLE IF NOT EXISTS public.notifications (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
          read BOOLEAN DEFAULT false,
          action_url TEXT,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON public.invoices(payment_status);
        CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON public.invoices(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_expense_reports_status ON public.expense_reports(status);
        CREATE INDEX IF NOT EXISTS idx_expense_reports_submitted_by ON public.expense_reports(submitted_by);
        CREATE INDEX IF NOT EXISTS idx_expenses_status ON public.expenses(status);
        CREATE INDEX IF NOT EXISTS idx_expenses_created_by ON public.expenses(created_by);
        CREATE INDEX IF NOT EXISTS idx_reports_status ON public.reports(status);
        CREATE INDEX IF NOT EXISTS idx_reports_submitted_by ON public.reports(submitted_by);
        CREATE INDEX IF NOT EXISTS idx_battery_reports_status ON public.battery_reports(status);
        CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_time_logs_log_date ON public.time_logs(log_date DESC);
        CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
        CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Missing tables created successfully');
    return true;
  } catch (error) {
    logError(`Failed to create missing tables: ${error.message}`);
    return false;
  }
};

// Step 2: Fix RLS policies for all tables
const fixRLSPolicies = async () => {
  logStep('Fixing RLS policies for dashboard tables...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on all tables
        ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.expense_reports ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.battery_reports ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies to avoid conflicts
        DROP POLICY IF EXISTS "invoices_select_admin" ON public.invoices;
        DROP POLICY IF EXISTS "invoices_select_accountant" ON public.invoices;
        DROP POLICY IF EXISTS "invoices_insert_admin" ON public.invoices;
        DROP POLICY IF EXISTS "expense_reports_select_own" ON public.expense_reports;
        DROP POLICY IF EXISTS "expense_reports_select_admin" ON public.expense_reports;
        DROP POLICY IF EXISTS "expense_reports_insert_own" ON public.expense_reports;
        DROP POLICY IF EXISTS "expenses_select_own" ON public.expenses;
        DROP POLICY IF EXISTS "expenses_select_admin" ON public.expenses;
        DROP POLICY IF EXISTS "expenses_insert_own" ON public.expenses;
        DROP POLICY IF EXISTS "reports_select_own" ON public.reports;
        DROP POLICY IF EXISTS "reports_select_admin" ON public.reports;
        DROP POLICY IF EXISTS "reports_insert_own" ON public.reports;
        DROP POLICY IF EXISTS "battery_reports_select_all" ON public.battery_reports;
        DROP POLICY IF EXISTS "battery_reports_insert_authenticated" ON public.battery_reports;
        DROP POLICY IF EXISTS "time_logs_select_own" ON public.time_logs;
        DROP POLICY IF EXISTS "time_logs_select_admin" ON public.time_logs;
        DROP POLICY IF EXISTS "time_logs_insert_own" ON public.time_logs;
        DROP POLICY IF EXISTS "notifications_select_own" ON public.notifications;
        DROP POLICY IF EXISTS "notifications_insert_system" ON public.notifications;

        -- INVOICES POLICIES
        CREATE POLICY "invoices_select_admin_accountant" ON public.invoices
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
            )
          );

        CREATE POLICY "invoices_insert_admin_accountant" ON public.invoices
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'accountant')
            )
          );

        CREATE POLICY "invoices_update_admin_accountant" ON public.invoices
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'accountant')
            )
          );

        -- EXPENSE REPORTS POLICIES
        CREATE POLICY "expense_reports_select_own_or_admin" ON public.expense_reports
          FOR SELECT USING (
            submitted_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant')
            )
          );

        CREATE POLICY "expense_reports_insert_authenticated" ON public.expense_reports
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL AND submitted_by = auth.uid()
          );

        CREATE POLICY "expense_reports_update_own_or_admin" ON public.expense_reports
          FOR UPDATE USING (
            submitted_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant')
            )
          );

        -- EXPENSES POLICIES (alternative table)
        CREATE POLICY "expenses_select_own_or_admin" ON public.expenses
          FOR SELECT USING (
            created_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant')
            )
          );

        CREATE POLICY "expenses_insert_authenticated" ON public.expenses
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL AND created_by = auth.uid()
          );

        -- REPORTS POLICIES
        CREATE POLICY "reports_select_own_or_admin" ON public.reports
          FOR SELECT USING (
            submitted_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'staff-admin')
            )
          );

        CREATE POLICY "reports_insert_authenticated" ON public.reports
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL AND submitted_by = auth.uid()
          );

        CREATE POLICY "reports_update_own_or_admin" ON public.reports
          FOR UPDATE USING (
            submitted_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
          );

        -- BATTERY REPORTS POLICIES
        CREATE POLICY "battery_reports_select_all_authenticated" ON public.battery_reports
          FOR SELECT USING (auth.uid() IS NOT NULL);

        CREATE POLICY "battery_reports_insert_authenticated" ON public.battery_reports
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL AND reported_by = auth.uid()
          );

        -- TIME LOGS POLICIES
        CREATE POLICY "time_logs_select_own_or_admin" ON public.time_logs
          FOR SELECT USING (
            user_id = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
          );

        CREATE POLICY "time_logs_insert_own" ON public.time_logs
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL AND user_id = auth.uid()
          );

        -- NOTIFICATIONS POLICIES
        CREATE POLICY "notifications_select_own" ON public.notifications
          FOR SELECT USING (user_id = auth.uid());

        CREATE POLICY "notifications_insert_system" ON public.notifications
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL OR current_setting('role') = 'service_role'
          );

        -- Grant permissions
        GRANT SELECT, INSERT, UPDATE ON public.invoices TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.expense_reports TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.expenses TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.reports TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.battery_reports TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.time_logs TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;

        GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('RLS policies fixed successfully');
    return true;
  } catch (error) {
    logError(`Failed to fix RLS policies: ${error.message}`);
    return false;
  }
};

// Step 3: Insert sample data for dashboard testing
const insertSampleData = async () => {
  logStep('Inserting sample data for dashboard testing...');
  
  try {
    // Get current user
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      logError('No authenticated user found');
      return false;
    }

    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Insert sample invoices
        INSERT INTO public.invoices (invoice_number, client_name, amount, total_amount, payment_status, due_date, created_by) VALUES
        ('INV-2024-001', 'Acme Corporation', 5000.00, 5000.00, 'paid', CURRENT_DATE + INTERVAL '30 days', '${userId}'),
        ('INV-2024-002', 'Tech Solutions Ltd', 7500.00, 7500.00, 'pending', CURRENT_DATE + INTERVAL '15 days', '${userId}'),
        ('INV-2024-003', 'Global Industries', 3200.00, 3200.00, 'overdue', CURRENT_DATE - INTERVAL '5 days', '${userId}')
        ON CONFLICT (invoice_number) DO NOTHING;

        -- Insert sample expense reports
        INSERT INTO public.expense_reports (title, description, amount, category, status, submitted_by) VALUES
        ('Office Supplies', 'Monthly office supplies purchase', 450.00, 'office', 'approved', '${userId}'),
        ('Travel Expenses', 'Business trip to Lagos', 1200.00, 'travel', 'pending', '${userId}'),
        ('Equipment Purchase', 'New laptop for development', 2500.00, 'equipment', 'approved', '${userId}')
        ON CONFLICT DO NOTHING;

        -- Insert sample reports
        INSERT INTO public.reports (title, description, report_type, priority, status, submitted_by) VALUES
        ('Monthly Performance Report', 'Summary of team performance for this month', 'performance', 'medium', 'submitted', '${userId}'),
        ('Project Status Update', 'Current status of ongoing projects', 'project', 'high', 'under_review', '${userId}'),
        ('Financial Summary', 'Quarterly financial overview', 'financial', 'high', 'approved', '${userId}')
        ON CONFLICT DO NOTHING;

        -- Insert sample time logs
        INSERT INTO public.time_logs (user_id, description, hours_worked, log_date, billable) VALUES
        ('${userId}', 'Dashboard development', 8.0, CURRENT_DATE, true),
        ('${userId}', 'Bug fixes and testing', 6.5, CURRENT_DATE - INTERVAL '1 day', true),
        ('${userId}', 'Client meeting', 2.0, CURRENT_DATE - INTERVAL '2 days', true)
        ON CONFLICT DO NOTHING;

        -- Insert sample notifications
        INSERT INTO public.notifications (user_id, title, message, type, read) VALUES
        ('${userId}', 'Welcome to Dashboard', 'Your dashboard is now ready to use!', 'success', false),
        ('${userId}', 'New Report Submitted', 'A new report has been submitted for review', 'info', false),
        ('${userId}', 'Invoice Overdue', 'Invoice INV-2024-003 is now overdue', 'warning', true)
        ON CONFLICT DO NOTHING;
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Sample data inserted successfully');
    return true;
  } catch (error) {
    logError(`Failed to insert sample data: ${error.message}`);
    return false;
  }
};

// Step 4: Test dashboard data access
const testDashboardAccess = async () => {
  logStep('Testing dashboard data access...');
  
  try {
    // Test invoices access
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('*')
      .limit(5);

    if (invoicesError) {
      throw new Error(`Invoices access failed: ${invoicesError.message}`);
    }

    logSuccess(`Invoices access successful: ${invoices?.length || 0} records`);

    // Test expense reports access
    const { data: expenses, error: expensesError } = await supabase
      .from('expense_reports')
      .select('*')
      .limit(5);

    if (expensesError) {
      throw new Error(`Expense reports access failed: ${expensesError.message}`);
    }

    logSuccess(`Expense reports access successful: ${expenses?.length || 0} records`);

    // Test reports access
    const { data: reports, error: reportsError } = await supabase
      .from('reports')
      .select('*')
      .limit(5);

    if (reportsError) {
      throw new Error(`Reports access failed: ${reportsError.message}`);
    }

    logSuccess(`Reports access successful: ${reports?.length || 0} records`);

    return true;
  } catch (error) {
    logError(`Dashboard access test failed: ${error.message}`);
    return false;
  }
};

// Main function to run all fixes
export const fixDashboardComprehensive = async () => {
  logStep('Starting comprehensive dashboard fix...');

  try {
    // Step 1: Create missing tables
    if (!(await createMissingTables())) {
      throw new Error('Failed to create missing tables');
    }

    // Step 2: Fix RLS policies
    if (!(await fixRLSPolicies())) {
      throw new Error('Failed to fix RLS policies');
    }

    // Step 3: Insert sample data
    if (!(await insertSampleData())) {
      throw new Error('Failed to insert sample data');
    }

    // Step 4: Test dashboard access
    if (!(await testDashboardAccess())) {
      throw new Error('Dashboard access tests failed');
    }

    logSuccess('🎉 Comprehensive dashboard fix completed successfully!');
    
    // Store success in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('dashboard_fix_completed', new Date().toISOString());
    }
    
    return true;

  } catch (error) {
    logError(`Comprehensive dashboard fix failed: ${error.message}`);
    return false;
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  // Check if fix has been run recently
  const lastRun = localStorage.getItem('dashboard_fix_completed');
  const shouldRun = !lastRun || (new Date().getTime() - new Date(lastRun).getTime()) > 24 * 60 * 60 * 1000;
  
  if (shouldRun) {
    fixDashboardComprehensive();
  }
}
