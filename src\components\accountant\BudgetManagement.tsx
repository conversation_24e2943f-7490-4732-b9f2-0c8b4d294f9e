
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { Plus, TrendingUp, TrendingDown, AlertTriangle, CheckCircle, Edit, Trash2 } from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface Budget {
  id: string;
  name: string;
  category: string;
  allocated_amount: number;
  spent_amount: number;
  period_start: string;
  period_end: string;
  status: string;
  department_id?: string;
  created_at: string;
  updated_at: string;
}

export const BudgetManagement = () => {
  const [isCreateOpen, setIsCreateOpen] = useState(false);

  // Fetch budgets from database
  const { data: budgets = [], isLoading, refetch } = useQuery({
    queryKey: ['budgets'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('budgets')
        .select(`
          *,
          department:departments(name)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching budgets:', error);
        // Return empty array if table doesn't exist yet
        return [];
      }

      return data || [];
    },
  });
  
  const [newBudget, setNewBudget] = useState({
    name: "",
    category: "",
    allocated_amount: "",
    period_start: "",
    period_end: "",
    department_id: ""
  });
  const { toast } = useToast();

  const departments = [
    { id: "1", name: "Marketing" },
    { id: "2", name: "Operations" },
    { id: "3", name: "Engineering" }
  ];

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-500/20 text-green-500';
      case 'warning':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'exceeded':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const getBudgetStatus = (allocated: number, spent: number) => {
    const percentage = (spent / allocated) * 100;
    if (percentage >= 100) return 'exceeded';
    if (percentage >= 80) return 'warning';
    return 'active';
  };

  const { userProfile } = useAuth();
  const queryClient = useQueryClient();

  // Create budget mutation
  const createBudgetMutation = useMutation({
    mutationFn: async (budgetData: any) => {
      const { error } = await supabase
        .from('budgets')
        .insert([{
          name: budgetData.name,
          category: budgetData.category,
          allocated_amount: parseFloat(budgetData.allocated_amount),
          spent_amount: 0,
          period_start: budgetData.period_start,
          period_end: budgetData.period_end,
          status: 'active',
          department_id: budgetData.department_id || null,
          created_by: userProfile?.id
        }]);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['budgets'] });
      setIsCreateOpen(false);
      setNewBudget({
        name: "",
        category: "",
        allocated_amount: "",
        period_start: "",
        period_end: "",
        department_id: ""
      });
      toast({
        title: "Success",
        description: "Budget created successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create budget",
        variant: "destructive",
      });
    }
  });

  const handleCreateBudget = () => {
    if (!newBudget.name || !newBudget.allocated_amount) {
      toast({
        title: "Error",
        description: "Please fill in required fields",
        variant: "destructive",
      });
      return;
    }

    createBudgetMutation.mutate(newBudget);
  };

  const totalAllocated = budgets.reduce((sum, budget) => sum + budget.allocated_amount, 0);
  const totalSpent = budgets.reduce((sum, budget) => sum + budget.spent_amount, 0);
  const totalRemaining = totalAllocated - totalSpent;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Budget Management</h2>
          <p className="text-muted-foreground">Monitor and manage organizational budgets</p>
        </div>
        <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Budget
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Budget</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Budget Name *</Label>
                <Input
                  id="name"
                  value={newBudget.name}
                  onChange={(e) => setNewBudget({ ...newBudget, name: e.target.value })}
                  placeholder="e.g., Marketing Q1 2024"
                />
              </div>
              <div>
                <Label htmlFor="category">Category</Label>
                <Select value={newBudget.category} onValueChange={(value) => setNewBudget({ ...newBudget, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="operational">Operational</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="equipment">Equipment</SelectItem>
                    <SelectItem value="travel">Travel</SelectItem>
                    <SelectItem value="utilities">Utilities</SelectItem>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="amount">Allocated Amount (₦) *</Label>
                <Input
                  id="amount"
                  type="number"
                  value={newBudget.allocated_amount}
                  onChange={(e) => setNewBudget({ ...newBudget, allocated_amount: e.target.value })}
                  placeholder="0.00"
                />
              </div>
              <div>
                <Label htmlFor="department">Department</Label>
                <Select value={newBudget.department_id} onValueChange={(value) => setNewBudget({ ...newBudget, department_id: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id}>{dept.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start">Period Start</Label>
                  <Input
                    id="start"
                    type="date"
                    value={newBudget.period_start}
                    onChange={(e) => setNewBudget({ ...newBudget, period_start: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="end">Period End</Label>
                  <Input
                    id="end"
                    type="date"
                    value={newBudget.period_end}
                    onChange={(e) => setNewBudget({ ...newBudget, period_end: e.target.value })}
                  />
                </div>
              </div>
              <Button onClick={handleCreateBudget} className="w-full">
                Create Budget
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Budget Overview Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <TrendingUp className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Allocated</p>
              <p className="text-2xl font-bold text-blue-600">₦{totalAllocated.toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <TrendingDown className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
              <p className="text-2xl font-bold text-red-600">₦{totalSpent.toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Remaining</p>
              <p className="text-2xl font-bold text-green-600">₦{totalRemaining.toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <AlertTriangle className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Utilization</p>
              <p className="text-2xl font-bold text-orange-600">
                {totalAllocated > 0 ? Math.round((totalSpent / totalAllocated) * 100) : 0}%
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget List */}
      <Card>
        <CardHeader>
          <CardTitle>Budget Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          {budgets.length > 0 ? (
            <div className="space-y-4">
              {budgets.map((budget) => {
                const percentage = (budget.spent_amount / budget.allocated_amount) * 100;
                const status = getBudgetStatus(budget.allocated_amount, budget.spent_amount);
                
                return (
                  <Card key={budget.id} className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div className="space-y-2">
                        <h3 className="font-semibold">{budget.name}</h3>
                        <div className="flex gap-2">
                          <Badge variant="outline">{budget.category}</Badge>
                          <Badge className={getStatusColor(status)}>
                            {status}
                          </Badge>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold">₦{budget.allocated_amount.toLocaleString()}</p>
                        <p className="text-sm text-muted-foreground">
                          Spent: ₦{budget.spent_amount.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Budget Utilization</span>
                        <span>{Math.round(percentage)}%</span>
                      </div>
                      <Progress value={percentage} className="h-2" />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Remaining: ₦{(budget.allocated_amount - budget.spent_amount).toLocaleString()}</span>
                        <span>
                          {budget.period_start && budget.period_end && 
                            `${new Date(budget.period_start).toLocaleDateString()} - ${new Date(budget.period_end).toLocaleDateString()}`
                          }
                        </span>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No budgets found. Create your first budget to get started.
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
