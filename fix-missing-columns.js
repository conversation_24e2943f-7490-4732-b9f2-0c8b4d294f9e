// Fix missing columns in existing database tables
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function fixMissingColumns() {
  console.log('🚀 Starting missing columns fix...');
  
  try {
    // Fix memos table - add missing columns
    logStep('Adding missing columns to memos table...');
    
    const { error: memosError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Add missing columns to memos table
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS memo_type TEXT DEFAULT 'general' CHECK (memo_type IN ('general', 'policy', 'announcement', 'urgent', 'meeting'));
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS visibility TEXT DEFAULT 'department' CHECK (visibility IN ('public', 'department', 'private', 'managers_only'));
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS target_audience TEXT[];
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived'));
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS effective_date DATE;
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS expiry_date DATE;
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS tags TEXT[];
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS attachments JSONB DEFAULT '[]';
        
        ALTER TABLE public.memos 
        ADD COLUMN IF NOT EXISTS read_by UUID[];
      `
    });

    if (memosError) {
      console.warn('Memos columns warning:', memosError.message);
    } else {
      logSuccess('Memos table columns added successfully');
    }

    // Fix reports table - add missing columns
    logStep('Adding missing columns to reports table...');
    
    const { error: reportsError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Add missing columns to reports table
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS report_type TEXT DEFAULT 'general' CHECK (report_type IN ('general', 'financial', 'project', 'hr', 'technical', 'battery', 'telecom'));
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected'));
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS submitted_by UUID;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS reviewed_by UUID;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS department_id UUID;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS project_id UUID;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS due_date DATE;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS submitted_date DATE DEFAULT CURRENT_DATE;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS reviewed_date DATE;
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS attachments JSONB DEFAULT '[]';
        
        ALTER TABLE public.reports 
        ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
      `
    });

    if (reportsError) {
      console.warn('Reports columns warning:', reportsError.message);
    } else {
      logSuccess('Reports table columns added successfully');
    }

    // Fix projects table - add missing columns
    logStep('Adding missing columns to projects table...');
    
    const { error: projectsError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Add missing columns to projects table
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS client_name TEXT;
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS budget DECIMAL(15,2);
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS start_date DATE;
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS end_date DATE;
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled'));
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100);
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS manager_id UUID;
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS created_by UUID;
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS department_id UUID;
        
        ALTER TABLE public.projects 
        ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
      `
    });

    if (projectsError) {
      console.warn('Projects columns warning:', projectsError.message);
    } else {
      logSuccess('Projects table columns added successfully');
    }

    // Fix profiles table - add missing columns
    logStep('Adding missing columns to profiles table...');
    
    const { error: profilesError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Add missing columns to profiles table
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'staff' CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff' CHECK (account_type IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending'));
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS department_id UUID;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS phone TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS bio TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS location TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS skills TEXT[];
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
      `
    });

    if (profilesError) {
      console.warn('Profiles columns warning:', profilesError.message);
    } else {
      logSuccess('Profiles table columns added successfully');
    }

    // Create missing tables if they don't exist
    logStep('Creating missing tables...');
    
    const { error: tablesError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Create departments table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.departments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL UNIQUE,
          description TEXT,
          manager_id UUID,
          budget DECIMAL(15,2),
          location TEXT,
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create project_assignments table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.project_assignments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          project_id UUID,
          project_name TEXT,
          assigned_to UUID,
          department_id UUID,
          role TEXT DEFAULT 'team_member',
          status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'active', 'completed', 'removed')),
          start_date DATE DEFAULT CURRENT_DATE,
          end_date DATE,
          hours_allocated INTEGER DEFAULT 40,
          hours_worked INTEGER DEFAULT 0,
          progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create system_activities table if it doesn't exist
        CREATE TABLE IF NOT EXISTS public.system_activities (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID,
          action TEXT NOT NULL,
          description TEXT,
          entity_type TEXT,
          entity_id UUID,
          metadata JSONB DEFAULT '{}',
          severity TEXT DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
          category TEXT DEFAULT 'general',
          ip_address INET,
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create AI tables if they don't exist
        CREATE TABLE IF NOT EXISTS public.ai_interactions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID,
          role TEXT DEFAULT 'user' CHECK (role IN ('user', 'assistant', 'system')),
          message TEXT NOT NULL,
          type TEXT DEFAULT 'chat',
          query TEXT,
          response TEXT,
          actions JSONB DEFAULT '[]',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
        CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
        CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
        CREATE INDEX IF NOT EXISTS idx_memos_created_by ON public.memos(created_by);
        CREATE INDEX IF NOT EXISTS idx_reports_submitted_by ON public.reports(submitted_by);
        CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON public.system_activities(user_id);
        CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON public.ai_interactions(user_id);
      `
    });

    if (tablesError) {
      console.warn('Tables creation warning:', tablesError.message);
    } else {
      logSuccess('Missing tables created successfully');
    }

    // Test the fixes
    logStep('Testing fixed tables...');
    
    // Test memos table with new columns
    const { data: memosTest, error: memosTestError } = await supabase
      .from('memos')
      .select('id, title, memo_type, priority, status')
      .limit(1);

    if (memosTestError) {
      console.warn('Memos test warning:', memosTestError.message);
    } else {
      logSuccess('Memos table with new columns working');
    }

    // Test reports table with new columns
    const { data: reportsTest, error: reportsTestError } = await supabase
      .from('reports')
      .select('id, title, report_type, priority, status')
      .limit(1);

    if (reportsTestError) {
      console.warn('Reports test warning:', reportsTestError.message);
    } else {
      logSuccess('Reports table with new columns working');
    }

    logSuccess('🎉 MISSING COLUMNS FIX COMPLETED SUCCESSFULLY!');
    console.log('\n🚀 ALL TABLES NOW HAVE REQUIRED COLUMNS:');
    console.log('✅ Memos table - memo_type, priority, visibility, status, etc.');
    console.log('✅ Reports table - report_type, priority, status, etc.');
    console.log('✅ Projects table - client_name, budget, status, priority, etc.');
    console.log('✅ Profiles table - role, account_type, status, etc.');
    console.log('✅ Additional tables - departments, project_assignments, etc.');
    console.log('\n🎯 The system should now work without column errors!');
    
    return true;

  } catch (error) {
    logError(`Missing columns fix failed: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
}

// Run the fix
fixMissingColumns()
  .then((success) => {
    if (success) {
      console.log('\n🎉 SUCCESS: Missing columns fix completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Missing columns fix encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
