import { fixProfileRolesComprehensive } from './fix-profile-roles-comprehensive';

/**
 * Simple script to run the comprehensive profile roles fix
 * Can be imported and executed from anywhere in the application
 */

export const runProfileRolesFix = async () => {
  console.log('🚀 Starting profile roles fix...');
  
  try {
    const success = await fixProfileRolesComprehensive();
    
    if (success) {
      console.log('✅ Profile roles fix completed successfully!');
      return { success: true, message: 'Profile roles fix completed successfully!' };
    } else {
      console.error('❌ Profile roles fix failed');
      return { success: false, message: 'Profile roles fix failed' };
    }
  } catch (error) {
    console.error('❌ Error running profile roles fix:', error);
    return { success: false, message: `Error: ${error.message}` };
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  // Check if we should auto-run the fix
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('fix-roles') === 'true') {
    runProfileRolesFix();
  }
}

export default runProfileRolesFix;
