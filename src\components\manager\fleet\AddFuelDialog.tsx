
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Fuel } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface Vehicle {
  id: string;
  vehicle_number: string;
  make: string;
  model: string;
  year: number;
  license_plate: string;
  status: string;
  current_mileage: number;
  fuel_type: string;
  assigned_driver_id?: string;
}

interface AddFuelDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  vehicles: Vehicle[];
  onFuelAdded: () => void;
}

export const AddFuelDialog = ({ isOpen, onOpenChange, vehicles, onFuelAdded }: AddFuelDialogProps) => {
  const { toast } = useToast();

  const handleFuelSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    try {
      const fuelData = {
        vehicle_id: formData.get('vehicle_id') as string,
        fuel_amount: parseFloat(formData.get('fuel_amount') as string),
        total_cost: parseFloat(formData.get('total_cost') as string),
        cost_per_unit: parseFloat(formData.get('total_cost') as string) / parseFloat(formData.get('fuel_amount') as string),
        transaction_date: formData.get('transaction_date') as string,
        fuel_station: formData.get('fuel_station') as string,
        mileage: parseFloat(formData.get('mileage') as string) || null
      };

      const { error } = await supabase
        .from('fuel_transactions')
        .insert([fuelData]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Fuel transaction recorded successfully",
      });

      onOpenChange(false);
      onFuelAdded();
    } catch (error) {
      console.error('Error recording fuel transaction:', error);
      toast({
        title: "Error",
        description: "Failed to record fuel transaction",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button>
          <Fuel className="h-4 w-4 mr-2" />
          Add Fuel
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Record Fuel Transaction</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleFuelSubmit} className="space-y-4">
          <div>
            <label className="text-sm font-medium">Vehicle *</label>
            <Select name="vehicle_id" required>
              <SelectTrigger>
                <SelectValue placeholder="Select a vehicle" />
              </SelectTrigger>
              <SelectContent>
                {vehicles.map(vehicle => (
                  <SelectItem key={vehicle.id} value={vehicle.id}>
                    {vehicle.vehicle_number} - {vehicle.make} {vehicle.model}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Fuel Amount (L) *</label>
              <Input name="fuel_amount" type="number" step="0.01" required />
            </div>
            <div>
              <label className="text-sm font-medium">Total Cost (₦) *</label>
              <Input name="total_cost" type="number" step="0.01" required />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Transaction Date *</label>
              <Input name="transaction_date" type="date" required />
            </div>
            <div>
              <label className="text-sm font-medium">Current Mileage</label>
              <Input name="mileage" type="number" />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium">Fuel Station</label>
            <Input name="fuel_station" />
          </div>

          <Button type="submit" className="w-full">Record Transaction</Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};
