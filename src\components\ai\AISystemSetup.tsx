import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Settings, 
  Database, 
  Brain,
  Zap,
  RefreshCw,
  Copy,
  ExternalLink
} from 'lucide-react';
import { setupAISystem, checkAIHealth, getAISetupInstructions } from '@/scripts/setup-ai-system';
import { LangChainAnalyzer } from './LangChainAnalyzer';
import { AITaskAutomationDemo } from './AITaskAutomationDemo';

interface ComponentHealth {
  [key: string]: boolean;
}

export const AISystemSetup: React.FC = () => {
  const [isSetupRunning, setIsSetupRunning] = useState(false);
  const [setupProgress, setSetupProgress] = useState(0);
  const [healthStatus, setHealthStatus] = useState<{
    overall: boolean;
    components: ComponentHealth;
    message: string;
  } | null>(null);
  const [showInstructions, setShowInstructions] = useState(false);
  const { toast } = useToast();
  const { userProfile, isAdmin, isManager } = useAuth();

  useEffect(() => {
    // Run initial health check
    performHealthCheck();
  }, []);

  const performHealthCheck = async () => {
    try {
      const health = await checkAIHealth();
      setHealthStatus(health);
    } catch (error) {
      console.error('Health check failed:', error);
      setHealthStatus({
        overall: false,
        components: {},
        message: 'Health check failed'
      });
    }
  };

  const runSetup = async () => {
    if (!isAdmin && !isManager) {
      toast({
        title: "Access Denied",
        description: "Admin or Manager role required for AI system setup",
        variant: "destructive"
      });
      return;
    }

    setIsSetupRunning(true);
    setSetupProgress(0);

    try {
      // Simulate progress
      const progressSteps = [10, 30, 50, 70, 90, 100];
      
      for (let i = 0; i < progressSteps.length; i++) {
        setSetupProgress(progressSteps[i]);
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      const result = await setupAISystem();
      
      if (result.success) {
        toast({
          title: "Setup Successful",
          description: result.message,
          variant: "default"
        });
        
        // Refresh health status
        await performHealthCheck();
      } else {
        toast({
          title: "Setup Failed",
          description: result.message,
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Setup Error",
        description: `Setup failed: ${error}`,
        variant: "destructive"
      });
    } finally {
      setIsSetupRunning(false);
      setSetupProgress(0);
    }
  };

  const copyInstructions = () => {
    navigator.clipboard.writeText(getAISetupInstructions());
    toast({
      title: "Copied",
      description: "Setup instructions copied to clipboard",
      variant: "default"
    });
  };

  const getStatusIcon = (isHealthy: boolean) => {
    return isHealthy ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (isHealthy: boolean) => {
    return (
      <Badge variant={isHealthy ? "default" : "destructive"}>
        {isHealthy ? "Healthy" : "Error"}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-blue-500" />
            AI System Setup & Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">
                Configure and monitor the AI system components
              </p>
              {healthStatus && (
                <p className="text-sm mt-1">
                  Status: <span className={healthStatus.overall ? "text-green-600" : "text-red-600"}>
                    {healthStatus.message}
                  </span>
                </p>
              )}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={performHealthCheck}
                disabled={isSetupRunning}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              {(isAdmin || isManager) && (
                <Button
                  onClick={runSetup}
                  disabled={isSetupRunning}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {isSetupRunning ? "Setting up..." : "Run Setup"}
                </Button>
              )}
            </div>
          </div>
          
          {isSetupRunning && (
            <div className="mt-4">
              <Progress value={setupProgress} className="w-full" />
              <p className="text-sm text-muted-foreground mt-2">
                Setting up AI system... {setupProgress}%
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Health Status */}
      {healthStatus && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-green-500" />
              Component Health Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(healthStatus.components).map(([component, isHealthy]) => (
                <div key={component} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(isHealthy)}
                    <span className="font-medium capitalize">
                      {component.replace(/_/g, ' ')}
                    </span>
                  </div>
                  {getStatusBadge(isHealthy)}
                </div>
              ))}
            </div>
            
            {!healthStatus.overall && (
              <Alert className="mt-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Some components are not healthy. Run the setup process or check the manual setup instructions.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Setup Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-purple-500" />
            Manual Setup Instructions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-muted-foreground">
              If automatic setup fails, follow these manual instructions
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyInstructions}
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy Instructions
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowInstructions(!showInstructions)}
              >
                {showInstructions ? "Hide" : "Show"} Instructions
              </Button>
            </div>
          </div>
          
          {showInstructions && (
            <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
              <pre className="text-sm whitespace-pre-wrap overflow-x-auto">
                {getAISetupInstructions()}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-500" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => window.open('/comprehensive-manual.html', '_blank')}
            >
              <ExternalLink className="h-6 w-6" />
              <span className="text-sm">Comprehensive Manual</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={() => window.open('https://supabase.com/dashboard', '_blank')}
            >
              <Database className="h-6 w-6" />
              <span className="text-sm">Supabase Dashboard</span>
            </Button>
            
            <Button
              variant="outline"
              className="h-auto p-4 flex flex-col items-center gap-2"
              onClick={performHealthCheck}
            >
              <RefreshCw className="h-6 w-6" />
              <span className="text-sm">Check Health</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* LangChain Implementation Analysis */}
      <LangChainAnalyzer />

      {/* AI Task Automation System */}
      <AITaskAutomationDemo />

      {/* Access Control Notice */}
      {!isAdmin && !isManager && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Admin or Manager role required to run automatic setup. Contact your system administrator for assistance.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};
