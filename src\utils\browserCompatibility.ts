/**
 * Browser Compatibility Utilities
 * Ensures AI features work across different browsers with graceful degradation
 */

export interface BrowserCapabilities {
  speechRecognition: boolean;
  speechSynthesis: boolean;
  webGL: boolean;
  webWorkers: boolean;
  indexedDB: boolean;
  localStorage: boolean;
  sessionStorage: boolean;
  geolocation: boolean;
  camera: boolean;
  microphone: boolean;
  notifications: boolean;
  serviceWorkers: boolean;
}

export interface FeatureSupport {
  aiNavigation: boolean;
  voiceCommands: boolean;
  visualFeedback: boolean;
  offlineSupport: boolean;
  realTimeUpdates: boolean;
}

class BrowserCompatibilityChecker {
  private capabilities: BrowserCapabilities;
  private featureSupport: FeatureSupport;

  constructor() {
    this.capabilities = this.checkBrowserCapabilities();
    this.featureSupport = this.determineFeatureSupport();
  }

  /**
   * Check all browser capabilities
   */
  private checkBrowserCapabilities(): BrowserCapabilities {
    return {
      speechRecognition: this.checkSpeechRecognition(),
      speechSynthesis: this.checkSpeechSynthesis(),
      webGL: this.checkWebGL(),
      webWorkers: this.checkWebWorkers(),
      indexedDB: this.checkIndexedDB(),
      localStorage: this.checkLocalStorage(),
      sessionStorage: this.checkSessionStorage(),
      geolocation: this.checkGeolocation(),
      camera: this.checkCamera(),
      microphone: this.checkMicrophone(),
      notifications: this.checkNotifications(),
      serviceWorkers: this.checkServiceWorkers()
    };
  }

  /**
   * Check Speech Recognition API support
   */
  private checkSpeechRecognition(): boolean {
    return !!(
      window.SpeechRecognition ||
      window.webkitSpeechRecognition ||
      window.mozSpeechRecognition ||
      window.msSpeechRecognition
    );
  }

  /**
   * Check Speech Synthesis API support
   */
  private checkSpeechSynthesis(): boolean {
    return !!(window.speechSynthesis && window.SpeechSynthesisUtterance);
  }

  /**
   * Check WebGL support
   */
  private checkWebGL(): boolean {
    try {
      const canvas = document.createElement('canvas');
      return !!(
        window.WebGLRenderingContext &&
        (canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
      );
    } catch (e) {
      return false;
    }
  }

  /**
   * Check Web Workers support
   */
  private checkWebWorkers(): boolean {
    return typeof Worker !== 'undefined';
  }

  /**
   * Check IndexedDB support
   */
  private checkIndexedDB(): boolean {
    return !!(window.indexedDB || window.mozIndexedDB || window.webkitIndexedDB || window.msIndexedDB);
  }

  /**
   * Check localStorage support
   */
  private checkLocalStorage(): boolean {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * Check sessionStorage support
   */
  private checkSessionStorage(): boolean {
    try {
      const test = 'test';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch (e) {
      return false;
    }
  }

  /**
   * Check Geolocation API support
   */
  private checkGeolocation(): boolean {
    return !!(navigator.geolocation);
  }

  /**
   * Check Camera access support
   */
  private checkCamera(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  /**
   * Check Microphone access support
   */
  private checkMicrophone(): boolean {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }

  /**
   * Check Notifications API support
   */
  private checkNotifications(): boolean {
    return !!(window.Notification);
  }

  /**
   * Check Service Workers support
   */
  private checkServiceWorkers(): boolean {
    return !!(navigator.serviceWorker);
  }

  /**
   * Determine which features are supported based on capabilities
   */
  private determineFeatureSupport(): FeatureSupport {
    return {
      aiNavigation: this.capabilities.webWorkers && this.capabilities.localStorage,
      voiceCommands: this.capabilities.speechRecognition && this.capabilities.speechSynthesis,
      visualFeedback: this.capabilities.webGL || true, // CSS animations as fallback
      offlineSupport: this.capabilities.serviceWorkers && this.capabilities.indexedDB,
      realTimeUpdates: this.capabilities.webWorkers && this.capabilities.localStorage
    };
  }

  /**
   * Get browser capabilities
   */
  public getCapabilities(): BrowserCapabilities {
    return this.capabilities;
  }

  /**
   * Get feature support
   */
  public getFeatureSupport(): FeatureSupport {
    return this.featureSupport;
  }

  /**
   * Check if a specific feature is supported
   */
  public isFeatureSupported(feature: keyof FeatureSupport): boolean {
    return this.featureSupport[feature];
  }

  /**
   * Get browser information
   */
  public getBrowserInfo(): {
    name: string;
    version: string;
    platform: string;
    mobile: boolean;
  } {
    const userAgent = navigator.userAgent;
    
    // Detect browser
    let name = 'Unknown';
    let version = 'Unknown';
    
    if (userAgent.includes('Chrome')) {
      name = 'Chrome';
      const match = userAgent.match(/Chrome\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    } else if (userAgent.includes('Firefox')) {
      name = 'Firefox';
      const match = userAgent.match(/Firefox\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      name = 'Safari';
      const match = userAgent.match(/Version\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    } else if (userAgent.includes('Edge')) {
      name = 'Edge';
      const match = userAgent.match(/Edge\/(\d+)/);
      version = match ? match[1] : 'Unknown';
    }

    return {
      name,
      version,
      platform: navigator.platform,
      mobile: /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
    };
  }

  /**
   * Get compatibility warnings for unsupported features
   */
  public getCompatibilityWarnings(): string[] {
    const warnings: string[] = [];
    
    if (!this.featureSupport.voiceCommands) {
      warnings.push('Voice commands are not supported in this browser. Please use manual controls.');
    }
    
    if (!this.featureSupport.aiNavigation) {
      warnings.push('AI navigation features may be limited in this browser.');
    }
    
    if (!this.featureSupport.offlineSupport) {
      warnings.push('Offline functionality is not available in this browser.');
    }
    
    if (!this.capabilities.notifications) {
      warnings.push('Browser notifications are not supported.');
    }

    return warnings;
  }

  /**
   * Get fallback options for unsupported features
   */
  public getFallbackOptions(): Record<string, string> {
    const fallbacks: Record<string, string> = {};
    
    if (!this.featureSupport.voiceCommands) {
      fallbacks.voiceCommands = 'Use manual command input or keyboard shortcuts';
    }
    
    if (!this.featureSupport.aiNavigation) {
      fallbacks.aiNavigation = 'Use traditional navigation menu and manual controls';
    }
    
    if (!this.capabilities.speechSynthesis) {
      fallbacks.speechSynthesis = 'Visual feedback and text notifications will be used instead';
    }

    return fallbacks;
  }

  /**
   * Generate compatibility report
   */
  public generateCompatibilityReport(): {
    browser: ReturnType<typeof this.getBrowserInfo>;
    capabilities: BrowserCapabilities;
    featureSupport: FeatureSupport;
    warnings: string[];
    fallbacks: Record<string, string>;
    overallCompatibility: 'excellent' | 'good' | 'fair' | 'poor';
  } {
    const supportedFeatures = Object.values(this.featureSupport).filter(Boolean).length;
    const totalFeatures = Object.keys(this.featureSupport).length;
    const compatibilityRatio = supportedFeatures / totalFeatures;
    
    let overallCompatibility: 'excellent' | 'good' | 'fair' | 'poor';
    if (compatibilityRatio >= 0.9) {
      overallCompatibility = 'excellent';
    } else if (compatibilityRatio >= 0.7) {
      overallCompatibility = 'good';
    } else if (compatibilityRatio >= 0.5) {
      overallCompatibility = 'fair';
    } else {
      overallCompatibility = 'poor';
    }

    return {
      browser: this.getBrowserInfo(),
      capabilities: this.capabilities,
      featureSupport: this.featureSupport,
      warnings: this.getCompatibilityWarnings(),
      fallbacks: this.getFallbackOptions(),
      overallCompatibility
    };
  }
}

// Export singleton instance
export const browserCompatibility = new BrowserCompatibilityChecker();

// Export utility functions
export const checkFeatureSupport = (feature: keyof FeatureSupport): boolean => {
  return browserCompatibility.isFeatureSupported(feature);
};

export const getBrowserCapabilities = (): BrowserCapabilities => {
  return browserCompatibility.getCapabilities();
};

export const getCompatibilityReport = () => {
  return browserCompatibility.generateCompatibilityReport();
};
