import { AuthProvider } from "@/components/auth/AuthProvider";
import { EnhancedErrorBoundary } from "@/components/EnhancedErrorBoundary";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { Lang<PERSON>hain<PERSON>ealtimeProvider } from "@/components/integration/LangChainRealtimeProvider";
import { UnifiedDashboardLayout } from "@/components/layout/UnifiedDashboardLayout";
import { Loader } from "@/components/ui/Loader";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { useStartupCacheClear } from "@/hooks/useStartupCacheClear";
import { setupMissingTables } from "@/scripts/setup-missing-tables";
import { Analytics } from "@vercel/analytics/react";
import { lazy, Suspense, useEffect, useState } from "react";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";

// Import responsive design system
import "@/styles/responsive-breakpoints.css";

// Import light theme border enhancements
import "@/styles/light-theme-borders.css";

// Import mobile-specific styles
import "@/styles/mobile-capacitor.css";
import "@/styles/mobile-responsive.css";

// Import mobile components
import MobileDebug from "@/components/mobile/MobileDebug";
import MobileFallback from "@/components/mobile/MobileFallback";
import MobileTest from "@/components/mobile/MobileTest";

// Import cache buster for PWA cleanup
import { CacheBuster } from "@/utils/cache-buster";

// Import error handler for clean console
import "@/utils/error-handler";

// Import database schema checker for debugging
import "@/utils/database-schema-checker";

// Import assets table checker for debugging
import "@/scripts/check-assets-table";

// Import database schema analyzer for debugging
import "@/scripts/analyze-database-schema";

// Import dashboard data tester for debugging
import "@/scripts/test-dashboard-data";

// Import department fixer for debugging
import "@/scripts/fix-duplicate-departments";

// Import system health checker for monitoring
import "@/utils/system-health-check";

// Public Pages
import { EmergencySignIn } from "@/components/debug/EmergencySignIn";
import { AuthPage } from "@/pages/AuthPage";
import ClockInPage from "@/pages/ClockInPage";
import { DebugPage } from "@/pages/DebugPage";

// Dashboard Root

// Admin Pages

// Manager Pages

// Staff Pages

// Accountant Pages

// Staff Admin Pages

// Asset Pages

// Account and Files Pages

// Lazy-loaded AI Page
const AIPage = lazy(() => import("@/pages/ai/AIPage"));

// 404 Fallback
const NotFound = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">404</h1>
      <p className="text-gray-600 dark:text-gray-400 mt-2">Page not found</p>
      <button
        onClick={() => window.history.back()}
        className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
      >
        Go Back
      </button>
    </div>
  </div>
);

// Mobile Detection Component
const MobileDetector = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isAndroid = /android/i.test(userAgent);
      const isIOS = /iphone|ipad|ipod/i.test(userAgent);
      const isMobileDevice = isAndroid || isIOS || window.innerWidth <= 768;

      console.log('Mobile Detection:', {
        userAgent,
        isAndroid,
        isIOS,
        isMobileDevice,
        width: window.innerWidth,
        height: window.innerHeight
      });

      setIsMobile(isMobileDevice);
      setIsLoading(false);
    };

    // Check immediately
    checkMobile();

    // Also check on resize
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-blue-600 flex items-center justify-center">
        <div className="text-white text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p>Loading CTNL AI Workboard...</p>
        </div>
      </div>
    );
  }

  if (isMobile) {
    return (
      <div className="min-h-screen bg-background">
        <MobileDebug />
        <MobileTest />
        <MobileFallback />
      </div>
    );
  }

  return null; // Let the normal app render
};

function App() {
  // Prevent stuck cache states
  useStartupCacheClear();

  useEffect(() => {
    // Only initialize cache buster in development or when manually triggered
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    const manualCacheInit = localStorage.getItem('manual-cache-init') === 'true';

    if (isDevelopment || manualCacheInit) {
      console.log('🧹 Initializing cache buster...');
      CacheBuster.initialize().catch(error => {
        console.error('Cache buster initialization failed:', error);
      });

      // Remove manual flag after initialization
      if (manualCacheInit) {
        localStorage.removeItem('manual-cache-init');
      }
    }

    // Setup missing database tables
    setupMissingTables().catch(console.error);
  }, []);

  return (
    <EnhancedErrorBoundary>
      <ErrorBoundary>
        <TooltipProvider>
          <BrowserRouter>
            <AuthProvider>
              <LangChainRealtimeProvider>
                <div className="min-h-screen bg-background">
                  <MobileDetector />

                  <Suspense fallback={<Loader />}>
                  <Routes>
                    {/* Redirect root to dashboard */}
                    <Route path="/" element={<Navigate to="/dashboard" replace />} />

                    {/* Auth routes */}
                    <Route path="/auth" element={<AuthPage />} />
                    <Route path="/clock-in" element={<ClockInPage />} />

                    {/* Debug routes */}
                    <Route path="/debug" element={<DebugPage />} />
                    <Route path="/emergency-signin" element={<EmergencySignIn />} />

                    {/* AI routes */}
                    <Route path="/ai" element={<AIPage />} />

                    {/* Dashboard routes */}
                    <Route path="/dashboard/*" element={<UnifiedDashboardLayout />} />

                    {/* 404 fallback */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                  </Suspense>
                </div>
                <Toaster />
                <Analytics />
              </LangChainRealtimeProvider>
            </AuthProvider>
          </BrowserRouter>
        </TooltipProvider>
      </ErrorBoundary>
    </EnhancedErrorBoundary>
  );
}

export default App;
