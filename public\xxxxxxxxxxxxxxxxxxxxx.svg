<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" rx="64" fill="#ff1c04"/>
  <g transform="translate(64, 64)">
    <!-- AI Brain Icon -->
    <circle cx="192" cy="128" r="96" fill="white" opacity="0.1"/>
    <path d="M192 64C236.18 64 272 99.82 272 144C272 188.18 236.18 224 192 224C147.82 224 112 188.18 112 144C112 99.82 147.82 64 192 64Z" fill="white"/>
    
    <!-- Neural Network Lines -->
    <g stroke="white" stroke-width="3" opacity="0.8">
      <line x1="144" y1="112" x2="176" y2="128"/>
      <line x1="176" y1="128" x2="208" y2="112"/>
      <line x1="208" y1="112" x2="240" y2="128"/>
      <line x1="144" y1="160" x2="176" y2="144"/>
      <line x1="176" y1="144" x2="208" y2="160"/>
      <line x1="208" y1="160" x2="240" y2="144"/>
      <line x1="176" y1="128" x2="176" y2="144"/>
      <line x1="208" y1="112" x2="208" y2="160"/>
    </g>
    
    <!-- Neural Nodes -->
    <g fill="#ff1c04">
      <circle cx="144" cy="112" r="4"/>
      <circle cx="176" cy="128" r="4"/>
      <circle cx="208" cy="112" r="4"/>
      <circle cx="240" cy="128" r="4"/>
      <circle cx="144" cy="160" r="4"/>
      <circle cx="176" cy="144" r="4"/>
      <circle cx="208" cy="160" r="4"/>
      <circle cx="240" cy="144" r="4"/>
    </g>
    
    <!-- CTNL Text -->
    <text x="192" y="280" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="48" font-weight="bold">CTNL</text>
    <text x="192" y="320" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="24" opacity="0.9">AI Workboard</text>
  </g>
</svg>
