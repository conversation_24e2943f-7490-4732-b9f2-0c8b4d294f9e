
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useEffect, useState } from 'react';

interface Department {
  id: string;
  name: string;
  description: string | null;
  manager_id: string | null;
  employee_count: number;
}

interface Profile {
  id: string;
  full_name: string | null;
  email: string | null;
  role: string | null;
  department_id: string | null;
}

export const useDepartmentSync = () => {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const { toast } = useToast();

  const fetchDepartments = async () => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name');

      if (error) throw error;
      setDepartments(data || []);
    } catch (error) {
      console.error('Error fetching departments:', error);
      toast({
        title: "Error",
        description: "Failed to fetch departments",
        variant: "destructive",
      });
    }
  };

  const fetchProfiles = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          department_id,
          department:department_id (
            name,
            description
          )
        `)
        .order('full_name');

      if (error) throw error;
      setProfiles(data || []);
    } catch (error) {
      console.error('Error fetching profiles:', error);
      toast({
        title: "Error",
        description: "Failed to fetch user profiles",
        variant: "destructive",
      });
    }
  };

  const syncAllUserDepartments = async () => {
    setSyncing(true);
    try {
      // Get all profiles without departments
      const { data: unassignedProfiles, error: fetchError } = await supabase
        .from('profiles')
        .select('id, role, department_id')
        .is('department_id', null);

      if (fetchError) throw fetchError;

      if (unassignedProfiles && unassignedProfiles.length > 0) {
        // Get department IDs
        const { data: depts, error: deptError } = await supabase
          .from('departments')
          .select('id, name');

        if (deptError) throw deptError;

        const operationsId = depts?.find(d => d.name === 'Operations')?.id;
        const engineeringId = depts?.find(d => d.name === 'Engineering')?.id;
        const financeId = depts?.find(d => d.name === 'Finance')?.id;

        // Update each unassigned profile
        for (const profile of unassignedProfiles) {
          let departmentId = operationsId; // Default

          switch (profile.role) {
            case 'accountant':
              departmentId = financeId;
              break;
            case 'admin':
            case 'manager':
              departmentId = operationsId;
              break;
            default:
              departmentId = engineeringId;
          }

          if (departmentId) {
            const { error: updateError } = await supabase
              .from('profiles')
              .update({ department_id: departmentId })
              .eq('id', profile.id);

            if (updateError) {
              console.error(`Error updating profile ${profile.id}:`, updateError);
            }
          }
        }

        toast({
          title: "Success",
          description: `Synchronized ${unassignedProfiles.length} users with departments`,
        });
      } else {
        toast({
          title: "Info",
          description: "All users are already assigned to departments",
        });
      }

      // Refresh data
      await Promise.all([fetchDepartments(), fetchProfiles()]);
    } catch (error) {
      console.error('Error syncing departments:', error);
      toast({
        title: "Error",
        description: "Failed to sync user departments",
        variant: "destructive",
      });
    } finally {
      setSyncing(false);
    }
  };

  const refreshDepartmentCounts = async () => {
    try {
      // Update department counts manually since we don't have the RPC function
      for (const dept of departments) {
        try {
          const { count } = await supabase
            .from('profiles')
            .select('id', { count: 'exact', head: true })
            .eq('department_id', dept.id);

          await supabase
            .from('departments')
            .update({ employee_count: count || 0 })
            .eq('id', dept.id);
        } catch (deptError) {
          console.error(`Error updating count for department ${dept.name}:`, deptError);
          // Continue with other departments even if one fails
        }
      }

      await fetchDepartments();

      toast({
        title: "Success",
        description: "Department counts refreshed successfully",
      });
    } catch (error) {
      console.error('Error refreshing department counts:', error);
      toast({
        title: "Error",
        description: "Failed to refresh department counts",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchDepartments(), fetchProfiles()]);
      setLoading(false);
    };

    loadData();

    // Set up real-time subscriptions
    const departmentChannel = supabase
      .channel('departments-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'departments'
        },
        () => {
          fetchDepartments();
        }
      )
      .subscribe();

    const profilesChannel = supabase
      .channel('profiles-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles'
        },
        () => {
          fetchProfiles();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(departmentChannel);
      supabase.removeChannel(profilesChannel);
    };
  }, []);

  return {
    departments,
    profiles,
    loading,
    syncing,
    syncAllUserDepartments,
    refreshDepartmentCounts,
    refetch: () => Promise.all([fetchDepartments(), fetchProfiles()])
  };
};
