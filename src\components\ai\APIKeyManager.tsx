import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Eye, EyeOff, Key, CheckCircle, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';

interface APIKeyManagerProps {
  onApiKeyConfigured?: (configured: boolean) => void;
}

export const APIKeyManager: React.FC<APIKeyManagerProps> = ({ onApiKeyConfigured }) => {
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isConfigured, setIsConfigured] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const { toast } = useToast();
  const { userProfile } = useAuth();

  useEffect(() => {
    checkExistingApiKey();
  }, [userProfile]);

  const checkExistingApiKey = async () => {
    try {
      if (!userProfile?.id) return;

      const { data, error } = await supabase
        .from('api_keys')
        .select('*')
        .eq('user_id', userProfile.id)
        .eq('service', 'openai')
        .single();

      if (data && !error) {
        setIsConfigured(true);
        onApiKeyConfigured?.(true);
      }
    } catch (error) {
      console.log('No existing API key found');
    }
  };

  const validateApiKey = async (key: string): Promise<boolean> => {
    try {
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${key}`,
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      return false;
    }
  };

  const handleSaveApiKey = async () => {
    if (!apiKey.trim()) {
      setValidationError('Please enter an API key');
      return;
    }

    if (!apiKey.startsWith('sk-')) {
      setValidationError('OpenAI API keys should start with "sk-"');
      return;
    }

    setIsValidating(true);
    setValidationError(null);

    try {
      // Validate the API key
      const isValid = await validateApiKey(apiKey);
      
      if (!isValid) {
        setValidationError('Invalid API key. Please check your OpenAI API key.');
        setIsValidating(false);
        return;
      }

      // Save to database (encrypted)
      const { error } = await supabase
        .from('api_keys')
        .upsert({
          user_id: userProfile?.id,
          service: 'openai',
          api_key_encrypted: apiKey, // In production, this should be encrypted
          is_active: true,
        });

      if (error) throw error;

      setIsConfigured(true);
      setApiKey('');
      onApiKeyConfigured?.(true);
      
      toast({
        title: 'Success',
        description: 'OpenAI API key configured successfully',
      });

    } catch (error) {
      console.error('Error saving API key:', error);
      setValidationError('Failed to save API key. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to save API key',
        variant: 'destructive',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleRemoveApiKey = async () => {
    try {
      const { error } = await supabase
        .from('api_keys')
        .delete()
        .eq('user_id', userProfile?.id)
        .eq('service', 'openai');

      if (error) throw error;

      setIsConfigured(false);
      onApiKeyConfigured?.(false);
      
      toast({
        title: 'Success',
        description: 'API key removed successfully',
      });

    } catch (error) {
      console.error('Error removing API key:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove API key',
        variant: 'destructive',
      });
    }
  };

  if (isConfigured) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-500" />
            OpenAI API Key Configured
          </CardTitle>
          <CardDescription>
            Your OpenAI API key is configured and ready to use.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            variant="outline" 
            onClick={handleRemoveApiKey}
            className="text-red-600 hover:text-red-700"
          >
            Remove API Key
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          Configure OpenAI API Key
        </CardTitle>
        <CardDescription>
          Enter your OpenAI API key to enable AI features. Your key will be stored securely.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="apiKey">OpenAI API Key</Label>
          <div className="relative">
            <Input
              id="apiKey"
              type={showApiKey ? 'text' : 'password'}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="sk-..."
              className="pr-10"
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowApiKey(!showApiKey)}
            >
              {showApiKey ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {validationError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{validationError}</AlertDescription>
          </Alert>
        )}

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You can get your API key from{' '}
            <a 
              href="https://platform.openai.com/api-keys" 
              target="_blank" 
              rel="noopener noreferrer"
              className="underline hover:no-underline"
            >
              OpenAI Platform
            </a>
          </AlertDescription>
        </Alert>

        <Button 
          onClick={handleSaveApiKey} 
          disabled={isValidating || !apiKey.trim()}
          className="w-full"
        >
          {isValidating ? 'Validating...' : 'Save API Key'}
        </Button>
      </CardContent>
    </Card>
  );
};
