```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Driven Workboard - CT NIGERIA LTD</title>
    <meta name="description" content="Smart project management with AI-powered workboard for modern teams">
    <meta name="author" content="IFEANYI OBIBI Technologies">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary-color: #ffffff; /* Pink is now white */
            --primary-light: #f0f0f0;
            --primary-dark: #cccccc;
            --secondary-color: #0d0d1a; /* Very dark blue/black */
            --card-bg: rgba(13, 13, 26, 0.7); /* Slightly transparent for glass effect */
            --border-color: rgba(255, 255, 255, 0.3); /* Pink border is now white */
            --text-color-light: #e0e0e0;
            --text-color-faded: rgba(224, 224, 224, 0.7);
            --gradient-1: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            --gradient-2: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); /* Original blue gradients */
            --blur-strength: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Open Sans', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
            line-height: 1.6;
            color: var(--text-color-light);
            background: var(--secondary-color);
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
            background-image: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
                              radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 50%);
            background-size: cover;
            background-repeat: no-repeat;
            animation: backgroundFloat 20s ease-in-out infinite;
        }

        @keyframes backgroundFloat {
            0%, 100% { background-position: 0% 0%; }
            33% { background-position: 5% 5%; }
            66% { background-position: -5% -5%; }
        }

        /* Glitch effect on hero title */
        @keyframes glitch {
            0% { text-shadow: 0 0 1px var(--primary-light), 0 0 5px var(--primary-color); transform: translate(0); }
            20% { text-shadow: 2px -2px 0px rgba(255,255,255,0.7), -2px 2px 0px rgba(255,255,255,0.7); transform: translate(-2px, 2px); }
            40% { text-shadow: 0 0 1px var(--primary-light), 0 0 5px var(--primary-color); transform: translate(0); }
            60% { text-shadow: -1px 1px 0px rgba(255,255,255,0.7), 1px -1px 0px rgba(255,255,255,0.7); transform: translate(1px, -1px); }
            80% { text-shadow: 0 0 1px var(--primary-light), 0 0 5px var(--primary-color); transform: translate(0); }
            100% { text-shadow: 0 0 1px var(--primary-light), 0 0 5px var(--primary-color); transform: translate(0); }
        }


        /* Navigation */
        .nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(var(--blur-strength));
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
            transition: background 0.3s ease, border-color 0.3s ease;
        }

        .nav-container {
            max-width: 1400px; /* Wider container */
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--text-color-light);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .logo:hover {
            color: var(--primary-color);
            text-shadow: 0 0 20px var(--primary-color), 0 0 30px rgba(255, 255, 255, 0.1);
        }

        .logo-icon {
            width: 38px; /* Slightly larger */
            height: 38px;
            background: var(--gradient-1);
            border-radius: 12px; /* More rounded */
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--secondary-color); /* Make the icon text dark for contrast */
            font-weight: bold;
            font-size: 1.4rem;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
            animation: logoGlow 3s ease-in-out infinite alternate;
            transform: rotate(-10deg); /* Slight rotation */
            transition: transform 0.3s ease;
        }

        .logo:hover .logo-icon {
            transform: rotate(0deg) scale(1.05);
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.8), 0 0 40px rgba(255, 255, 255, 0.4);
        }

        @keyframes logoGlow {
            0% { box-shadow: 0 0 20px rgba(255, 255, 255, 0.6); }
            100% { box-shadow: 0 0 30px rgba(255, 255, 255, 0.9); }
        }

        .nav-buttons {
            display: flex;
            gap: 1.5rem; /* More space */
            align-items: center;
        }

        .btn {
            padding: 0.9rem 1.8rem; /* Larger buttons */
            border-radius: 10px; /* Slightly more rounded */
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            border: 1px solid transparent;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.6rem;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-1);
            color: var(--secondary-color); /* Dark text on white button */
            border: 1px solid var(--border-color);
            box-shadow: 0 6px 20px rgba(255, 255, 255, 0.4);
            animation: buttonPulse 2s ease-in-out infinite;
        }

        @keyframes buttonPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 6px 20px rgba(255, 255, 255, 0.4); }
            50% { transform: scale(1.02); box-shadow: 0 8px 25px rgba(255, 255, 255, 0.6); }
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.25), transparent); /* Darker shine */
            transition: 0.6s ease-out;
            z-index: -1;
        }

        .btn-primary:hover {
            transform: translateY(-4px) scale(1.05);
            box-shadow: 0 10px 30px rgba(255, 255, 255, 0.8);
            border-color: var(--primary-light);
            animation: none;
            color: var(--secondary-color);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-secondary {
            background: rgba(0, 0, 0, 0.7); /* More transparent */
            color: var(--primary-color); /* White text on secondary button */
            border: 1px solid var(--border-color);
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .btn-secondary::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to top, rgba(255, 255, 255, 0.15), transparent); /* White highlight */
            opacity: 0;
            transition: opacity 0.4s ease, transform 0.4s ease;
            z-index: -1;
        }

        .btn-secondary:hover {
            background: rgba(0, 0, 0, 0.9);
            border-color: var(--primary-color);
            transform: translateY(-4px);
            color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
        }

        .btn-secondary:hover::after {
            opacity: 1;
            transform: translateY(-5px);
        }

        /* Glassmorphic/Bento Card */
        .bento-card {
            background: var(--card-bg);
            border-radius: 18px; /* More rounded corners */
            border: 1px solid var(--border-color);
            backdrop-filter: blur(var(--blur-strength));
            padding: 2.5rem; /* More padding */
            transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
            box-shadow:
                0 12px 40px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1); /* Subtle inner border glow */
            position: relative;
            overflow: hidden;
        }

        .bento-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.05) 0%, transparent 70%); /* White pattern */
            animation: cardPatternFloat 10s linear infinite;
            opacity: 0.3;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes cardPatternFloat {
            0% { transform: translate(0, 0) rotate(0deg); }
            100% { transform: translate(100px, 100px) rotate(360deg); } /* Slower, more subtle movement */
        }

        .bento-card:hover {
            transform: translateY(-12px) scale(1.02); /* More pronounced lift */
            border-color: var(--primary-light);
            box-shadow:
                0 24px 60px rgba(0, 0, 0, 0.6),
                0 0 40px var(--primary-color), /* Stronger glow on hover */
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            z-index: 2; /* Bring to front slightly on hover */
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 10rem 2rem 4rem; /* More top padding */
            position: relative;
            overflow: hidden;
        }

        .hero-video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: -2;
            opacity: 0;
            transition: opacity 2s ease-in-out;
        }

        .hero-video.active {
            opacity: 1;
        }

        .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                rgba(0, 0, 0, 0.98) 0%,
                rgba(0, 0, 0, 0.9) 30%,
                rgba(0, 0, 0, 0.9) 70%,
                rgba(0, 0, 0, 0.98) 100%);
            z-index: -1;
        }

        .hero-container {
            max-width: 800px; /* Wider hero text */
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.6rem;
            background: rgba(0, 0, 0, 0.8);
            padding: 0.6rem 1.2rem;
            border-radius: 10px;
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 2.5rem;
            color: var(--text-color-light);
            border: 1px solid var(--border-color);
            backdrop-filter: blur(15px);
            box-shadow: 0 0 25px rgba(255, 255, 255, 0.4);
            animation: badgeGlow 4s ease-in-out infinite alternate, badgeFloat 4s ease-in-out infinite;
        }

        @keyframes badgeGlow {
            0% { box-shadow: 0 0 25px rgba(255, 255, 255, 0.4); }
            100% { box-shadow: 0 0 35px rgba(255, 255, 255, 0.6); }
        }

        @keyframes badgeFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-8px); }
        }

        .hero-title {
            font-size: clamp(3rem, 6vw, 4.8rem); /* Even larger and more responsive */
            font-weight: 800; /* Bolder */
            line-height: 1.05;
            margin-bottom: 1.8rem;
            color: #ffffff;
            text-shadow: 0 6px 30px rgba(0, 0, 0, 0.6);
            animation: titleGlow 6s ease-in-out infinite alternate, titleSlideUp 1.2s ease-out;
        }

        .hero-title .highlight {
            color: var(--primary-color);
            position: relative;
            display: inline-block;
            animation: glitch 3s infinite; /* Glitch effect on highlighted text */
        }
        .hero-title .highlight::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
            bottom: -10px;
            left: 0;
            border-radius: 2px;
            opacity: 0.7;
            animation: highlightUnderline 2s infinite alternate;
        }

        @keyframes highlightUnderline {
            0% { width: 0%; left: 0%; }
            50% { width: 100%; left: 0%; }
            100% { width: 0%; left: 100%; }
        }

        @keyframes titleGlow {
            0% { text-shadow: 0 6px 30px rgba(0, 0, 0, 0.6); }
            100% { text-shadow: 0 6px 30px rgba(0, 0, 0, 0.6), 0 0 60px rgba(255, 255, 255, 0.25); }
        }

        @keyframes titleSlideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 400;
            color: var(--text-color-faded);
            margin-bottom: 3.5rem;
            max-width: 650px;
            margin-left: auto;
            margin-right: auto;
            text-shadow: 0 3px 15px rgba(0, 0, 0, 0.4);
            animation: subtitleSlideUp 1.4s ease-out 0.3s both;
        }

        @keyframes subtitleSlideUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .hero-cta {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
            animation: ctaSlideUp 1.6s ease-out 0.6s both;
        }

        @keyframes ctaSlideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .btn-large {
            padding: 1.2rem 2.5rem; /* Even larger CTA buttons */
            font-size: 1.1rem;
        }

        /* Sections common styling */
        .section-container {
            max-width: 1400px; /* Wider container */
            margin: 0 auto;
            padding: 0 2rem;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3.2rem);
            font-weight: 700;
            margin-bottom: 4rem; /* More space */
            color: #ffffff;
            text-align: center;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-1);
            border-radius: 2px;
            box-shadow: 0 0 15px var(--primary-color);
        }

        /* Dashboard Preview Section (Bento Grid) */
        .dashboard {
            padding: 8rem 0; /* More vertical padding */
            background: linear-gradient(135deg, #0a0a1a 0%, #0d0d1a 100%); /* Subtle dark gradient */
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            grid-auto-rows: minmax(250px, auto); /* Flexible row height */
            gap: 2rem;
        }

        .dashboard-card {
            background: var(--card-bg);
            border-radius: 18px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(var(--blur-strength));
            padding: 2rem;
            transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .dashboard-card:hover {
            transform: translateY(-8px) scale(1.01);
            border-color: var(--primary-light);
            box-shadow:
                0 18px 45px rgba(0, 0, 0, 0.5),
                0 0 25px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        /* Specific Bento adjustments */
        .dashboard-card:nth-child(1) { /* Project Analytics */
            grid-column: span 2; /* Span two columns on larger screens */
            min-height: 350px;
            background: linear-gradient(135deg, rgba(13, 13, 26, 0.8) 0%, rgba(26, 26, 46, 0.8) 100%), url('https://source.unsplash.com/random/800x400/?charts,data') center/cover no-repeat;
            background-blend-mode: overlay;
            position: relative;
        }
        .dashboard-card:nth-child(1)::after {
            content: 'Live Data Stream'; /* Example content */
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 0.8rem;
            color: rgba(255,255,255,0.5);
            background: rgba(0,0,0,0.5);
            padding: 5px 10px;
            border-radius: 5px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .dashboard-card:nth-child(2) { /* AI Assistant */
            min-height: 280px;
        }

        .dashboard-card:nth-child(3) { /* Smart Documents */
            min-height: 280px;
        }

        .dashboard-card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem; /* More space */
        }

        .dashboard-card-icon {
            width: 55px; /* Larger icon */
            height: 55px;
            background: var(--gradient-1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--secondary-color); /* Dark icon text */
            margin-right: 1.2rem;
            font-size: 1.8rem; /* Larger icon font */
            box-shadow: 0 6px 20px rgba(255, 255, 255, 0.5);
            transform: rotate(-5deg);
            transition: all 0.3s ease;
        }

        .dashboard-card:hover .dashboard-card-icon {
            transform: rotate(0deg) scale(1.1);
            box-shadow: 0 8px 25px rgba(255, 255, 255, 0.7);
        }

        .dashboard-card-title {
            font-size: 1.3rem; /* Larger title */
            font-weight: 700;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(255,255,255,0.1);
        }

        .dashboard-card-content {
            color: var(--text-color-faded);
            font-size: 1rem; /* Slightly larger text */
            line-height: 1.7;
        }

        /* Features Section (Bento Grid) */
        .features {
            padding: 8rem 0;
            background: linear-gradient(135deg, #0f1020 0%, #0d0d1a 100%);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            grid-auto-rows: minmax(200px, auto);
        }

        .feature-card {
            background: var(--card-bg);
            border-radius: 18px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(var(--blur-strength));
            padding: 2rem;
            text-align: center;
            transition: all 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.01);
            border-color: var(--primary-light);
            box-shadow:
                0 18px 45px rgba(0, 0, 0, 0.5),
                0 0 25px rgba(255, 255, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .feature-icon {
            width: 60px; /* Larger icon */
            height: 60px;
            background: var(--gradient-1);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem; /* Larger icon font */
            color: var(--secondary-color); /* Dark icon text */
            box-shadow: 0 6px 20px rgba(255, 255, 255, 0.5);
            animation: iconPulse 3s ease-in-out infinite;
            position: relative;
            z-index: 1;
        }

        .feature-icon::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 14px;
            background: rgba(255,255,255,0.1);
            animation: iconHalo 2.5s infinite ease-out;
            z-index: -1;
        }

        @keyframes iconHalo {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); opacity: 0.5; }
            100% { transform: scale(1.4); opacity: 0; }
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(255,255,255,0.1);
        }

        .feature-description {
            color: var(--text-color-faded);
            font-size: 0.95rem;
            line-height: 1.6;
        }

        /* CTA Section */
        .cta {
            padding: 8rem 0;
            background: linear-gradient(135deg, #0d0d1a 0%, #0a0a1a 100%);
            text-align: center;
        }

        .cta-container {
            max-width: 650px; /* Wider CTA box */
            margin: 0 auto;
        }

        .cta-title {
            font-size: clamp(2.2rem, 4.5vw, 3.5rem);
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: #ffffff;
            text-shadow: 0 0 15px rgba(255,255,255,0.1);
        }

        .cta-subtitle {
            font-size: 1.1rem;
            color: var(--text-color-faded);
            margin-bottom: 2.5rem;
        }

        /* Footer */
        .footer {
            padding: 2.5rem;
            background: rgba(0, 0, 0, 0.8);
            text-align: center;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-color-faded);
            font-size: 0.9rem;
        }

        .footer p {
            margin-bottom: 0.5rem;
        }

        .footer-links a {
            color: var(--primary-color);
            text-decoration: none;
            margin: 0 0.8rem;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: var(--primary-light);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .nav-container, .section-container {
                padding: 0 1.5rem;
            }
            .dashboard-card:nth-child(1) {
                grid-column: span 1; /* Stack on smaller desktops */
            }
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }
            .nav-buttons {
                width: 100%;
                justify-content: center;
                gap: 1rem;
            }
            .logo {
                margin-bottom: 0.5rem;
            }
            .hero-title {
                font-size: clamp(2.5rem, 8vw, 4rem);
            }
            .hero-subtitle {
                font-size: 1.1rem;
            }
            .hero-cta {
                flex-direction: column;
                align-items: center;
            }
            .btn-large {
                width: 80%;
                max-width: 300px;
            }
            .section-title {
                font-size: clamp(1.8rem, 6vw, 2.8rem);
            }
            .dashboard-grid, .features-grid {
                grid-template-columns: 1fr;
            }
            .dashboard-card, .feature-card {
                padding: 1.8rem;
            }
        }

        @media (max-width: 480px) {
            .nav-container, .section-container {
                padding: 0 1rem;
            }
            .logo {
                font-size: 1.4rem;
            }
            .logo-icon {
                width: 35px;
                height: 35px;
                font-size: 1.2rem;
            }
            .btn {
                padding: 0.7rem 1.4rem;
                font-size: 0.9rem;
            }
            .hero-badge {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
            }
            .hero-title {
                font-size: clamp(2rem, 9vw, 3.5rem);
            }
            .hero-subtitle {
                font-size: 1rem;
            }
            .dashboard-card-title, .feature-title {
                font-size: 1.2rem;
            }
            .dashboard-card-icon, .feature-icon {
                width: 50px;
                height: 50px;
                font-size: 1.6rem;
            }
        }
    </style>
</head>
<body>
    <nav class="nav">
        <div class="nav-container">
            <a href="#" class="logo">
                <div class="logo-icon">AI</div>
                CT NIGERIA LTD
            </a>
            <div class="nav-buttons">
                <a href="ai.ctnigeria.com" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
            </div>
        </div>
    </nav>

    <main>
        <section class="hero">
            <video class="hero-video" loop muted autoplay playsinline>
                <source src="https://ctnigeria.com/wp-content/uploads/2024/07/abstract-particle-background-wave-big-data-3d-rendering-animation-digital-wave-particles-form-abstract-lines-dots-background-loop-animation-of-futuristic-technology-background-ai-generated-video.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <div class="hero-overlay"></div>
            <div class="hero-container">
                <div class="hero-badge">
                    <i class="fas fa-magic"></i> AI Powered
                </div>
                <h1 class="hero-title">
                    Unlock Peak Productivity with <span class="highlight">AI-Driven Workboard</span>
                </h1>
                <p class="hero-subtitle">
                    Transform your project management with intelligent automation, real-time insights, and collaborative tools designed to boost efficiency and drive success.
                </p>
                <div class="hero-cta">
                    <a href="ai.ctnigeria.com" class="btn btn-primary btn-large">
                        <i class="fas fa-rocket"></i> Get Started
                    </a>
                </div>
            </div>
        </section>

        <section class="dashboard">
            <div class="section-container">
                <h2 class="section-title">See Our AI Workboard in Action</h2>
                <div class="dashboard-grid">
                    <div class="bento-card dashboard-card">
                        <div class="dashboard-card-header">
                            <div class="dashboard-card-icon"><i class="fas fa-chart-line"></i></div>
                            <h3 class="dashboard-card-title">Intelligent Project Analytics</h3>
                        </div>
                        <p class="dashboard-card-content">Gain deep insights into project performance with AI-driven analytics, predicting potential bottlenecks and suggesting optimal pathways for completion.</p>
                    </div>

                    <div class="bento-card dashboard-card">
                        <div class="dashboard-card-header">
                            <div class="dashboard-card-icon"><i class="fas fa-robot"></i></div>
                            <h3 class="dashboard-card-title">AI Assistant for Task Automation</h3>
                        </div>
                        <p class="dashboard-card-content">Automate repetitive tasks, prioritize urgent items, and receive smart recommendations from your personal AI assistant, freeing up time for what matters.</p>
                    </div>

                    <div class="bento-card dashboard-card">
                        <div class="dashboard-card-header">
                            <div class="dashboard-card-icon"><i class="fas fa-file-alt"></i></div>
                            <h3 class="dashboard-card-title">Smart Document Management</h3>
                        </div>
                        <p class="dashboard-card-content">Organize, search, and summarize documents effortlessly with AI-powered indexing and content analysis, making information retrieval a breeze.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="features">
            <div class="section-container">
                <h2 class="section-title">Key Features to Boost Your Team's Potential</h2>
                <div class="features-grid">
                    <div class="bento-card feature-card">
                        <div class="feature-icon"><i class="fas fa-brain"></i></div>
                        <h3 class="feature-title">Predictive Insights</h3>
                        <p class="feature-description">Leverage AI to foresee project risks and opportunities, enabling proactive decision-making.</p>
                    </div>
                    <div class="bento-card feature-card">
                        <div class="feature-icon"><i class="fas fa-users-cog"></i></div>
                        <h3 class="feature-title">Seamless Collaboration</h3>
                        <p class="feature-description">Real-time collaboration tools with AI assistance for streamlined team communication.</p>
                    </div>
                    <div class="bento-card feature-card">
                        <div class="feature-icon"><i class="fas fa-tachometer-alt"></i></div>
                        <h3 class="feature-title">Optimized Workflows</h3>
                        <p class="feature-description">Design and automate workflows that adapt to your team's needs, powered by intelligent algorithms.</p>
                    </div>
                    <div class="bento-card feature-card">
                        <div class="feature-icon"><i class="fas fa-bell"></i></div>
                        <h3 class="feature-title">Smart Notifications</h3>
                        <p class="feature-description">Receive intelligent alerts and reminders tailored to your tasks and project milestones.</p>
                    </div>
                    <div class="bento-card feature-card">
                        <div class="feature-icon"><i class="fas fa-shield-alt"></i></div>
                        <h3 class="feature-title">Enterprise-Grade Security</h3>
                        <p class="feature-description">Your data is protected with industry-leading security protocols and compliance.</p>
                    </div>
                    <div class="bento-card feature-card">
                        <div class="feature-icon"><i class="fas fa-chart-pie"></i></div>
                        <h3 class="feature-title">Customizable Dashboards</h3>
                        <p class="feature-description">Personalize your workspace to focus on the metrics and projects most critical to you.</p>
                    </div>
                </div>
            </div>
        </section>

        <section class="cta">
            <div class="cta-container">
                <h2 class="cta-title">Ready to Revolutionize Your Work?</h2>
                <p class="cta-subtitle">Join countless teams already benefiting from the power of AI in project management. Get started today and experience the future of work.</p>
                <a href="ai.ctnigeria.com" class="btn btn-primary btn-large">
                    <i class="fas fa-arrow-right"></i> Login to AI Workboard
                </a>
            </div>
        </section>
    </main>

    <footer class="footer">
        <p>&copy; 2025 CT Nigeria Ltd. All rights reserved.</p>
        <div class="footer-links">
            <a href="#">Privacy Policy</a>
            <a href="#">Terms of Service</a>
            <a href="#">Contact Us</a>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const video = document.querySelector('.hero-video');
            video.addEventListener('loadeddata', () => {
                video.classList.add('active');
            });

            // Intersection Observer for animations on scroll
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.1
            };

            const observer = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                        observer.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.bento-card, .section-title, .cta-title, .cta-subtitle, .cta .btn').forEach(el => {
                el.classList.add('fade-in-on-scroll');
                observer.observe(el);
            });
        });
    </script>
</body>
</html>
```
