<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Systematic Final Fix</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .step h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Systematic Final Fix</h1>
        <p>Step-by-step approach to fix the remaining invoice and notification issues</p>
        
        <div class="step">
            <h3>Step 1: Discover Exact Invoice Schema</h3>
            <p>Find the exact required columns for invoices table</p>
            <button id="step1Btn" class="button" onclick="discoverInvoiceSchema()">
                🔍 Discover Invoice Schema
            </button>
        </div>
        
        <div class="step">
            <h3>Step 2: Check Invoice RLS Policies</h3>
            <p>Verify RLS policies allow invoice insertion</p>
            <button id="step2Btn" class="button" onclick="checkInvoiceRLS()" disabled>
                🔒 Check Invoice RLS
            </button>
        </div>
        
        <div class="step">
            <h3>Step 3: Insert Invoice with Correct Schema</h3>
            <p>Insert invoice using discovered schema and RLS-compliant data</p>
            <button id="step3Btn" class="button" onclick="insertInvoice()" disabled>
                📄 Insert Invoice
            </button>
        </div>
        
        <div class="step">
            <h3>Step 4: Check Notification RLS</h3>
            <p>Verify notification RLS policies and permissions</p>
            <button id="step4Btn" class="button" onclick="checkNotificationRLS()" disabled>
                🔔 Check Notification RLS
            </button>
        </div>
        
        <div class="step">
            <h3>Step 5: Final Verification</h3>
            <p>Test all data access and dashboard functionality</p>
            <button id="step5Btn" class="button" onclick="finalVerification()" disabled>
                ✅ Final Verification
            </button>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
        </div>
        
        <div id="status"></div>
        <div id="log" class="log" style="display: none;"></div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button id="dashboardBtn" class="button" onclick="openDashboard()" disabled>
                📊 Open Dashboard
            </button>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        let invoiceSchema = {};
        let currentStep = 1;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        function updateProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percent + '%';
        }
        
        function enableNextStep(step) {
            const nextBtn = document.getElementById(`step${step}Btn`);
            if (nextBtn) {
                nextBtn.disabled = false;
            }
        }
        
        async function discoverInvoiceSchema() {
            const btn = document.getElementById('step1Btn');
            btn.disabled = true;
            btn.textContent = '🔄 Discovering...';
            
            try {
                log('🔍 Step 1: Discovering exact invoice schema...');
                
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('Please log in first');
                
                const userId = user.id;
                log('✅ User authenticated: ' + user.email);
                
                // From the error messages, we know:
                // - subtotal is required (NOT NULL)
                // - balance_amount is required (NOT NULL)
                // - client_name column doesn't exist
                // - vendor_name column doesn't exist
                
                log('📋 Based on error analysis:');
                log('  - subtotal: REQUIRED (NOT NULL)');
                log('  - balance_amount: REQUIRED (NOT NULL)');
                log('  - client_name: DOES NOT EXIST');
                log('  - vendor_name: DOES NOT EXIST');
                
                // Try to find what columns DO exist by testing minimal required fields
                const testInvoice = {
                    invoice_number: 'SCHEMA-TEST-' + Date.now(),
                    subtotal: 1000.00,
                    balance_amount: 1000.00,
                    created_by: userId
                };
                
                log('🧪 Testing minimal invoice schema...');
                const { data: testResult, error: testError } = await supabase
                    .from('invoices')
                    .insert(testInvoice)
                    .select();
                
                if (testError) {
                    log(`⚠️ Test failed: ${testError.message}`);
                    
                    // Try adding more common fields
                    const testInvoice2 = {
                        invoice_number: 'SCHEMA-TEST-2-' + Date.now(),
                        subtotal: 1000.00,
                        balance_amount: 1000.00,
                        total_amount: 1000.00,
                        created_by: userId
                    };
                    
                    const { data: testResult2, error: testError2 } = await supabase
                        .from('invoices')
                        .insert(testInvoice2)
                        .select();
                    
                    if (testError2) {
                        log(`⚠️ Test 2 failed: ${testError2.message}`);
                        invoiceSchema = { error: testError2.message };
                    } else {
                        log('✅ Test 2 successful! Found working schema.');
                        invoiceSchema = { columns: Object.keys(testResult2[0]), working: testInvoice2 };
                        
                        // Clean up test data
                        await supabase.from('invoices').delete().eq('id', testResult2[0].id);
                    }
                } else {
                    log('✅ Test successful! Found working schema.');
                    invoiceSchema = { columns: Object.keys(testResult[0]), working: testInvoice };
                    
                    // Clean up test data
                    await supabase.from('invoices').delete().eq('id', testResult[0].id);
                }
                
                updateProgress(20);
                showStatus('✅ Step 1 completed: Invoice schema discovered', 'success');
                enableNextStep(2);
                
            } catch (error) {
                log('❌ Step 1 failed: ' + error.message);
                showStatus('❌ Step 1 failed: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔍 Discover Invoice Schema';
            }
        }
        
        async function checkInvoiceRLS() {
            const btn = document.getElementById('step2Btn');
            btn.disabled = true;
            btn.textContent = '🔄 Checking...';
            
            try {
                log('🔒 Step 2: Checking invoice RLS policies...');
                
                const { data: { user } } = await supabase.auth.getUser();
                const userId = user.id;
                
                // Check if user can read invoices
                const { data: readTest, error: readError } = await supabase
                    .from('invoices')
                    .select('*')
                    .limit(1);
                
                if (readError) {
                    log(`⚠️ Invoice read access: ${readError.message}`);
                } else {
                    log(`✅ Invoice read access: OK (found ${readTest?.length || 0} records)`);
                }
                
                // Check user's role for invoice permissions
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('role, account_type')
                    .eq('id', userId)
                    .single();
                
                if (profileError) {
                    log(`⚠️ Profile check: ${profileError.message}`);
                } else {
                    log(`✅ User role: ${profile.role} (account_type: ${profile.account_type})`);
                    
                    // Based on typical RLS, admin/accountant/manager should have invoice access
                    if (['admin', 'accountant', 'manager'].includes(profile.role)) {
                        log('✅ User role should have invoice access');
                    } else {
                        log('⚠️ User role may not have invoice access');
                    }
                }
                
                updateProgress(40);
                showStatus('✅ Step 2 completed: RLS policies checked', 'success');
                enableNextStep(3);
                
            } catch (error) {
                log('❌ Step 2 failed: ' + error.message);
                showStatus('❌ Step 2 failed: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔒 Check Invoice RLS';
            }
        }
        
        async function insertInvoice() {
            const btn = document.getElementById('step3Btn');
            btn.disabled = true;
            btn.textContent = '🔄 Inserting...';
            
            try {
                log('📄 Step 3: Inserting invoice with correct schema...');
                
                const { data: { user } } = await supabase.auth.getUser();
                const userId = user.id;
                
                if (!invoiceSchema.working) {
                    throw new Error('No working invoice schema found from Step 1');
                }
                
                // Use the working schema from Step 1
                const invoiceData = {
                    ...invoiceSchema.working,
                    invoice_number: 'INV-FINAL-' + Date.now(),
                    subtotal: 2500.00,
                    balance_amount: 2500.00,
                    total_amount: 2500.00
                };
                
                log('📋 Inserting invoice with schema: ' + Object.keys(invoiceData).join(', '));
                
                const { data: invoiceResult, error: invoiceError } = await supabase
                    .from('invoices')
                    .insert(invoiceData)
                    .select();
                
                if (invoiceError) {
                    log(`❌ Invoice insertion failed: ${invoiceError.message}`);
                    throw invoiceError;
                } else {
                    log(`✅ Invoice inserted successfully! ID: ${invoiceResult[0].id}`);
                }
                
                updateProgress(60);
                showStatus('✅ Step 3 completed: Invoice inserted successfully', 'success');
                enableNextStep(4);
                
            } catch (error) {
                log('❌ Step 3 failed: ' + error.message);
                showStatus('❌ Step 3 failed: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '📄 Insert Invoice';
            }
        }
        
        async function checkNotificationRLS() {
            const btn = document.getElementById('step4Btn');
            btn.disabled = true;
            btn.textContent = '🔄 Checking...';
            
            try {
                log('🔔 Step 4: Checking notification RLS policies...');
                
                const { data: { user } } = await supabase.auth.getUser();
                const userId = user.id;
                
                // Test notification read access
                const { data: notifRead, error: notifReadError } = await supabase
                    .from('notifications')
                    .select('*')
                    .limit(3);
                
                if (notifReadError) {
                    log(`⚠️ Notification read access: ${notifReadError.message}`);
                } else {
                    log(`✅ Notification read access: OK (found ${notifRead?.length || 0} records)`);
                }
                
                // Test notification insert
                const testNotification = {
                    user_id: userId,
                    title: 'RLS Test Notification',
                    message: 'Testing notification RLS policies',
                    type: 'info',
                    is_read: false
                };
                
                const { data: notifInsert, error: notifInsertError } = await supabase
                    .from('notifications')
                    .insert(testNotification)
                    .select();
                
                if (notifInsertError) {
                    log(`⚠️ Notification insert test: ${notifInsertError.message}`);
                } else {
                    log(`✅ Notification insert test: OK`);
                    
                    // Clean up test notification
                    await supabase.from('notifications').delete().eq('id', notifInsert[0].id);
                }
                
                updateProgress(80);
                showStatus('✅ Step 4 completed: Notification RLS verified', 'success');
                enableNextStep(5);
                
            } catch (error) {
                log('❌ Step 4 failed: ' + error.message);
                showStatus('❌ Step 4 failed: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🔔 Check Notification RLS';
            }
        }
        
        async function finalVerification() {
            const btn = document.getElementById('step5Btn');
            const dashboardBtn = document.getElementById('dashboardBtn');
            btn.disabled = true;
            btn.textContent = '🔄 Verifying...';
            
            try {
                log('✅ Step 5: Final verification of all systems...');
                
                // Test all tables
                const tables = ['invoices', 'expense_reports', 'reports', 'time_logs', 'notifications'];
                let workingTables = 0;
                let totalRecords = 0;
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('*', { count: 'exact' })
                            .limit(1);
                        
                        if (error) {
                            log(`⚠️ ${table}: ${error.message}`);
                        } else {
                            workingTables++;
                            // Note: count is not available in the response for security reasons
                            log(`✅ ${table}: Accessible`);
                        }
                    } catch (e) {
                        log(`❌ ${table}: ${e.message}`);
                    }
                }
                
                log(`📊 Final Status: ${workingTables}/${tables.length} tables accessible`);
                
                if (workingTables >= 4) {
                    log('🎉 Dashboard is ready! Most tables are working correctly.');
                    showStatus('🎉 All systems verified! Dashboard is ready to use.', 'success');
                    dashboardBtn.disabled = false;
                } else {
                    log('⚠️ Some tables still have issues, but dashboard should be partially functional.');
                    showStatus('⚠️ Partial success. Dashboard should be partially functional.', 'warning');
                    dashboardBtn.disabled = false;
                }
                
                updateProgress(100);
                
            } catch (error) {
                log('❌ Step 5 failed: ' + error.message);
                showStatus('❌ Step 5 failed: ' + error.message, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '✅ Final Verification';
            }
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Make functions global
        window.discoverInvoiceSchema = discoverInvoiceSchema;
        window.checkInvoiceRLS = checkInvoiceRLS;
        window.insertInvoice = insertInvoice;
        window.checkNotificationRLS = checkNotificationRLS;
        window.finalVerification = finalVerification;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
