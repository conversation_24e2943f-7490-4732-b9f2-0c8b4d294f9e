/**
 * Voice Navigation Button Component
 * A floating red microphone button for voice navigation throughout the system
 */

import { useAuth } from '@/components/auth/AuthProvider';
import { But<PERSON> } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useToast } from '@/hooks/use-toast';
import { voiceCommandSystem } from '@/lib/voice-command-system';
import { Mic, MicOff } from "lucide-react";
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

interface VoiceNavigationButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
}

export const VoiceNavigationButton: React.FC<VoiceNavigationButtonProps> = ({
  position = 'bottom-right',
  size = 'md',
  showTooltip = true
}) => {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const { userProfile } = useAuth();
  const location = useLocation();
  const { toast } = useToast();

  useEffect(() => {
    // Check if voice commands are supported
    setIsSupported(voiceCommandSystem.isSupported());
    
    // Set context based on current page and user role
    const currentPage = location.pathname.split('/').pop() || 'dashboard';
    voiceCommandSystem.setContext(currentPage, userProfile?.role || 'staff');

    // Monitor listening state
    const checkListening = () => {
      setIsListening(voiceCommandSystem.getListeningState());
    };
    
    const interval = setInterval(checkListening, 100);
    return () => clearInterval(interval);
  }, [location.pathname, userProfile?.role]);

  const toggleListening = () => {
    if (!isSupported) {
      toast({
        title: "Voice Commands Not Supported",
        description: "Your browser doesn't support voice commands.",
        variant: "destructive",
      });
      return;
    }

    if (isListening) {
      voiceCommandSystem.stopListening();
      setIsListening(false);
      toast({
        title: "Voice Assistant Stopped",
        description: "Voice navigation is now disabled.",
      });
    } else {
      voiceCommandSystem.startListening();
      setIsListening(true);
      toast({
        title: "Voice Assistant Active",
        description: "Say 'help' to see available commands.",
      });
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-3 left-3 sm:bottom-4 sm:left-4 md:bottom-6 md:left-6';
      case 'top-right':
        return 'top-3 right-3 sm:top-4 sm:right-4 md:top-6 md:right-6';
      case 'top-left':
        return 'top-3 left-3 sm:top-4 sm:left-4 md:top-6 md:left-6';
      default:
        return 'bottom-4 right-4';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-8 h-8 sm:w-10 sm:h-10';
      case 'lg':
        return 'w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16';
      default:
        return 'w-10 h-10 sm:w-12 sm:h-12';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'h-4 w-4';
      case 'lg':
        return 'h-8 w-8';
      default:
        return 'h-5 w-5';
    }
  };

  if (!isSupported) {
    return null; // Don't show if not supported
  }

  const buttonContent = (
    <div className={`fixed ${getPositionClasses()} z-50`}>
      <div className="relative group">
        {/* Outer glow rings */}
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red-500 to-red-600 opacity-75 blur-lg animate-pulse group-hover:opacity-100 transition-opacity duration-300"></div>
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red-400 to-red-500 opacity-50 blur-xl animate-pulse" style={{ animationDelay: '0.5s' }}></div>

        {/* 3D perspective container */}
        <div className="relative transform-gpu transition-all duration-300 group-hover:scale-110 group-hover:rotate-y-12" style={{ transformStyle: 'preserve-3d' }}>
          <Button
            onClick={toggleListening}
            className={`
              relative ${getSizeClasses()}
              shadow-2xl hover:shadow-red-500/50 transition-all duration-500
              bg-gradient-to-br from-red-500 via-red-600 to-red-700
              hover:from-red-400 hover:via-red-500 hover:to-red-600
              text-white border-2 border-red-400/50
              transform-gpu hover:scale-105
              ${isListening ? 'animate-pulse shadow-red-500/70' : 'shadow-red-600/40'}
              before:absolute before:inset-0
              before:bg-gradient-to-t before:from-transparent before:to-white/20
              before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300
            `}
            variant="default"
            style={{
              borderRadius: '8px',
              boxShadow: isListening
                ? '0 0 30px rgba(239, 68, 68, 0.8), 0 0 60px rgba(239, 68, 68, 0.4), inset 0 2px 4px rgba(255, 255, 255, 0.2)'
                : '0 8px 32px rgba(239, 68, 68, 0.4), 0 4px 16px rgba(239, 68, 68, 0.3), inset 0 2px 4px rgba(255, 255, 255, 0.1)'
            }}
          >
            {isListening ? (
              <MicOff className={`${getIconSize()} text-white drop-shadow-lg`} />
            ) : (
              <Mic className={`${getIconSize()} text-white drop-shadow-lg`} />
            )}
          </Button>

          {/* 3D depth effect */}
          <div className="absolute inset-0 rounded-full bg-gradient-to-br from-red-700 to-red-800 -z-10 transform translate-y-1 translate-x-1 opacity-60"></div>
        </div>

        {/* Enhanced active indicators */}
        {isListening && (
          <>
            <div className="absolute -top-2 -right-2 z-10">
              <div className="w-4 h-4 bg-gradient-to-r from-red-400 to-red-500 rounded-full animate-pulse shadow-lg shadow-red-500/50"></div>
              <div className="absolute inset-0 w-4 h-4 bg-red-400 rounded-full animate-ping opacity-75"></div>
            </div>
            <div className="absolute inset-0 rounded-full bg-red-500 opacity-20 animate-ping"></div>
            <div className="absolute inset-0 rounded-full bg-red-400 opacity-10 animate-ping" style={{ animationDelay: '0.5s' }}></div>
          </>
        )}

        {/* Floating particles effect */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1 left-1 w-1 h-1 bg-red-300 rounded-full animate-bounce opacity-60" style={{ animationDelay: '0s' }}></div>
          <div className="absolute top-2 right-1 w-1 h-1 bg-red-400 rounded-full animate-bounce opacity-60" style={{ animationDelay: '0.3s' }}></div>
          <div className="absolute bottom-1 left-2 w-1 h-1 bg-red-300 rounded-full animate-bounce opacity-60" style={{ animationDelay: '0.6s' }}></div>
        </div>
      </div>
    </div>
  );

  if (showTooltip) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          {buttonContent}
        </TooltipTrigger>
        <TooltipContent side="left" className="bg-red-50 border-red-200">
          <div className="text-center">
            <p className="font-medium text-red-800">
              {isListening ? '🎤 Voice Active' : '🎤 Voice Navigation'}
            </p>
            <p className="text-xs text-red-600">
              {isListening ? 'Click to stop listening' : 'Click to start voice control'}
            </p>
            <p className="text-xs text-red-500 mt-1">
              Say "help" for commands
            </p>
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  return buttonContent;
};

// Hook for easy voice navigation integration
export const useVoiceNavigation = () => {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const { userProfile } = useAuth();
  const location = useLocation();

  useEffect(() => {
    setIsSupported(voiceCommandSystem.isSupported());
    
    const currentPage = location.pathname.split('/').pop() || 'dashboard';
    voiceCommandSystem.setContext(currentPage, userProfile?.role || 'staff');

    const checkListening = () => {
      setIsListening(voiceCommandSystem.getListeningState());
    };
    
    const interval = setInterval(checkListening, 100);
    return () => clearInterval(interval);
  }, [location.pathname, userProfile?.role]);

  const startListening = () => {
    if (isSupported) {
      voiceCommandSystem.startListening();
    }
  };

  const stopListening = () => {
    voiceCommandSystem.stopListening();
  };

  const speak = (text: string) => {
    voiceCommandSystem.speak(text);
  };

  const introduceCurrentPage = () => {
    const pageName = location.pathname.split('/').pop() || 'dashboard';
    const introduction = `You are now on the ${pageName.replace('-', ' ')} page. Say "help" to see what you can do here.`;
    speak(introduction);
  };

  return {
    isListening,
    isSupported,
    startListening,
    stopListening,
    speak,
    introduceCurrentPage
  };
};
