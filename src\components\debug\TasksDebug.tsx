import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/components/auth/AuthProvider';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, Database, User, CheckCircle, AlertCircle } from 'lucide-react';

interface DebugResult {
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  data?: any;
}

export const TasksDebug = () => {
  const { userProfile } = useAuth();
  const [results, setResults] = useState<DebugResult[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (result: DebugResult) => {
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testTasksTableSchema = async () => {
    try {
      const { data, error } = await supabase
        .from('information_schema.columns')
        .select('column_name, data_type, is_nullable')
        .eq('table_name', 'tasks')
        .eq('table_schema', 'public');

      if (error) throw error;

      addResult({
        test: 'Tasks Table Schema',
        status: 'success',
        message: `Found ${data?.length || 0} columns in tasks table`,
        data: data
      });
    } catch (error: any) {
      addResult({
        test: 'Tasks Table Schema',
        status: 'error',
        message: error.message
      });
    }
  };

  const testBasicTasksQuery = async () => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .limit(5);

      if (error) throw error;

      addResult({
        test: 'Basic Tasks Query',
        status: 'success',
        message: `Found ${data?.length || 0} tasks`,
        data: data
      });
    } catch (error: any) {
      addResult({
        test: 'Basic Tasks Query',
        status: 'error',
        message: error.message
      });
    }
  };

  const testUserTasksQuery = async () => {
    if (!userProfile?.id) {
      addResult({
        test: 'User Tasks Query',
        status: 'warning',
        message: 'No user profile available'
      });
      return;
    }

    try {
      // Try assigned_to_id first
      let { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('assigned_to_id', userProfile.id);

      if (error && error.code === 'PGRST116') {
        // Try assigned_to if assigned_to_id doesn't exist
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('tasks')
          .select('*')
          .eq('assigned_to', userProfile.id);
        
        data = fallbackData;
        error = fallbackError;
      }

      if (error) throw error;

      addResult({
        test: 'User Tasks Query',
        status: 'success',
        message: `Found ${data?.length || 0} tasks assigned to current user`,
        data: data
      });
    } catch (error: any) {
      addResult({
        test: 'User Tasks Query',
        status: 'error',
        message: error.message
      });
    }
  };

  const testForeignKeyRelationships = async () => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select(`
          id,
          title,
          assignee:profiles!tasks_assigned_to_id_fkey(full_name),
          creator:profiles!tasks_created_by_fkey(full_name)
        `)
        .limit(3);

      if (error) throw error;

      addResult({
        test: 'Foreign Key Relationships',
        status: 'success',
        message: `Successfully loaded tasks with relationships`,
        data: data
      });
    } catch (error: any) {
      addResult({
        test: 'Foreign Key Relationships',
        status: 'error',
        message: error.message
      });
    }
  };

  const createSampleTask = async () => {
    if (!userProfile?.id) {
      addResult({
        test: 'Create Sample Task',
        status: 'warning',
        message: 'No user profile available'
      });
      return;
    }

    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          title: 'Debug Test Task',
          description: 'This is a test task created by the debug component',
          status: 'pending',
          priority: 'low',
          assigned_to_id: userProfile.id,
          created_by: userProfile.id
        })
        .select()
        .single();

      if (error) throw error;

      addResult({
        test: 'Create Sample Task',
        status: 'success',
        message: 'Successfully created sample task',
        data: data
      });
    } catch (error: any) {
      addResult({
        test: 'Create Sample Task',
        status: 'error',
        message: error.message
      });
    }
  };

  const runAllTests = async () => {
    setLoading(true);
    clearResults();
    
    await testTasksTableSchema();
    await testBasicTasksQuery();
    await testUserTasksQuery();
    await testForeignKeyRelationships();
    
    setLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Tasks Debug Panel
        </CardTitle>
        <CardDescription>
          Debug and test the tasks functionality for staff dashboard
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button onClick={runAllTests} disabled={loading}>
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Run All Tests
          </Button>
          <Button variant="outline" onClick={testTasksTableSchema}>
            Test Schema
          </Button>
          <Button variant="outline" onClick={testBasicTasksQuery}>
            Test Basic Query
          </Button>
          <Button variant="outline" onClick={testUserTasksQuery}>
            Test User Tasks
          </Button>
          <Button variant="outline" onClick={createSampleTask}>
            Create Sample Task
          </Button>
          <Button variant="outline" onClick={clearResults}>
            Clear Results
          </Button>
        </div>

        {userProfile && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <User className="h-4 w-4" />
              <span>Current User: {userProfile.full_name} ({userProfile.role})</span>
              <Badge variant="outline">{userProfile.id}</Badge>
            </div>
          </div>
        )}

        <div className="space-y-2">
          {results.map((result, index) => (
            <div key={index} className="p-3 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(result.status)}
                <span className="font-medium">{result.test}</span>
                <Badge variant={result.status === 'success' ? 'default' : result.status === 'error' ? 'destructive' : 'secondary'}>
                  {result.status}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
              {result.data && (
                <pre className="text-xs bg-muted p-2 rounded overflow-auto max-h-40">
                  {JSON.stringify(result.data, null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
