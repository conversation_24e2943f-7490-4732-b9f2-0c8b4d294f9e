/**
 * <PERSON><PERSON><PERSON><PERSON> Document Processor Supabase Edge Function
 * Processes documents and adds them to the vector store
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { OpenAIEmbeddings } from 'https://esm.sh/@langchain/openai';
import { RecursiveCharacterTextSplitter } from 'https://esm.sh/@langchain/textsplitters';
import { SupabaseVectorStore } from 'https://esm.sh/@langchain/community/vectorstores/supabase';
import { Document } from 'https://esm.sh/@langchain/core/documents';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface DocumentProcessRequest {
  content: string;
  metadata?: {
    title?: string;
    source?: string;
    author?: string;
    type?: string;
    department?: string;
    tags?: string[];
    [key: string]: any;
  };
  options?: {
    chunkSize?: number;
    chunkOverlap?: number;
    addToVectorStore?: boolean;
    generateSummary?: boolean;
  };
  userId?: string;
}

interface ProcessedDocument {
  id: string;
  chunks: Array<{
    content: string;
    metadata: Record<string, any>;
  }>;
  summary?: string;
  metadata: {
    totalChunks: number;
    processingTime: number;
    addedToVectorStore: boolean;
  };
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { 
      content, 
      metadata = {}, 
      options = {}, 
      userId 
    }: DocumentProcessRequest = await req.json();

    if (!content?.trim()) {
      return new Response(
        JSON.stringify({ error: 'Document content is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const startTime = Date.now();

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Initialize text splitter
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: options.chunkSize || 1000,
      chunkOverlap: options.chunkOverlap || 200,
      separators: ['\n\n', '\n', ' ', ''],
    });

    // Create document
    const document = new Document({
      pageContent: content,
      metadata: {
        ...metadata,
        processedAt: new Date().toISOString(),
        processedBy: userId || 'system',
      },
    });

    // Split document into chunks
    const chunks = await textSplitter.splitDocuments([document]);

    // Add chunk indices and enhance metadata
    const enhancedChunks = chunks.map((chunk, index) => ({
      ...chunk,
      metadata: {
        ...chunk.metadata,
        chunkIndex: index,
        totalChunks: chunks.length,
        chunkId: `${generateDocumentId()}_chunk_${index}`,
      },
    }));

    const documentId = generateDocumentId();
    let addedToVectorStore = false;
    let summary: string | undefined;

    // Add to vector store if requested
    if (options.addToVectorStore !== false) {
      try {
        const embeddings = new OpenAIEmbeddings({
          openAIApiKey: Deno.env.get('OPENAI_API_KEY'),
          modelName: 'text-embedding-ada-002',
        });

        const vectorStore = new SupabaseVectorStore(embeddings, {
          client: supabase,
          tableName: 'documents',
          queryName: 'match_documents',
        });

        await vectorStore.addDocuments(enhancedChunks);
        addedToVectorStore = true;
      } catch (error) {
        console.error('Failed to add to vector store:', error);
        // Continue processing even if vector store addition fails
      }
    }

    // Generate summary if requested
    if (options.generateSummary) {
      try {
        summary = await generateDocumentSummary(content, metadata.title || 'Document');
      } catch (error) {
        console.error('Failed to generate summary:', error);
      }
    }

    // Save document metadata to database
    await saveDocumentMetadata(supabase, {
      id: documentId,
      content,
      metadata,
      chunks: enhancedChunks.length,
      summary,
      userId,
      addedToVectorStore,
    });

    const processingTime = Date.now() - startTime;

    const response: ProcessedDocument = {
      id: documentId,
      chunks: enhancedChunks.map(chunk => ({
        content: chunk.pageContent,
        metadata: chunk.metadata,
      })),
      summary,
      metadata: {
        totalChunks: enhancedChunks.length,
        processingTime,
        addedToVectorStore,
      },
    };

    return new Response(
      JSON.stringify(response),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Document Processing Error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error.message,
        success: false 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});

async function generateDocumentSummary(content: string, title: string): Promise<string> {
  try {
    const { ChatOpenAI } = await import('https://esm.sh/@langchain/openai');
    
    const llm = new ChatOpenAI({
      openAIApiKey: Deno.env.get('OPENAI_API_KEY'),
      modelName: 'gpt-4-turbo-preview',
      temperature: 0.3,
      maxTokens: 500,
    });

    const prompt = `Please provide a comprehensive summary of the following document:

Title: ${title}
Content: ${content.substring(0, 4000)}${content.length > 4000 ? '...' : ''}

Summary Requirements:
- Provide a concise overview of the main points
- Highlight key findings or conclusions
- Include important details and data
- Structure the summary with clear sections
- Keep the summary between 100-300 words

Summary:`;

    const response = await llm.invoke([{ role: 'user', content: prompt }]);
    return response.content as string;
  } catch (error) {
    console.error('Failed to generate summary:', error);
    return 'Summary generation failed';
  }
}

async function saveDocumentMetadata(
  supabase: any,
  data: {
    id: string;
    content: string;
    metadata: any;
    chunks: number;
    summary?: string;
    userId?: string;
    addedToVectorStore: boolean;
  }
): Promise<void> {
  try {
    await supabase.from('processed_documents').insert({
      id: data.id,
      title: data.metadata.title || 'Untitled Document',
      content: data.content,
      metadata: data.metadata,
      chunks_count: data.chunks,
      summary: data.summary,
      processed_by: data.userId,
      added_to_vector_store: data.addedToVectorStore,
      created_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to save document metadata:', error);
  }
}

function generateDocumentId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `doc_${timestamp}_${random}`;
}
