// Populate voice command templates and knowledge base
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function populateVoiceTemplates() {
  console.log('🚀 Starting voice templates and knowledge base population...');
  
  try {
    // Voice Command Templates
    logStep('Creating voice command templates...');
    
    const commandTemplates = [
      // Navigation Commands
      {
        name: 'Navigate to Dashboard',
        category: 'navigation',
        command_patterns: [
          'go to dashboard',
          'open dashboard',
          'show dashboard',
          'take me to dashboard',
          'navigate to dashboard'
        ],
        intent: 'navigate_dashboard',
        description: 'Navigate to the main dashboard',
        example_phrases: ['Go to dashboard', 'Open the dashboard', 'Show me the dashboard'],
        required_parameters: [],
        optional_parameters: [],
        response_templates: [
          'Navigating to the dashboard',
          'Taking you to the main dashboard',
          'Opening the dashboard for you'
        ],
        action_mappings: {
          handler: 'navigate',
          target: '/',
          description: 'Navigate to dashboard'
        },
        navigation_targets: ['/'],
        is_active: true
      },
      {
        name: 'Navigate to Projects',
        category: 'navigation',
        command_patterns: [
          'go to projects',
          'open projects',
          'show projects',
          'view projects',
          'navigate to projects'
        ],
        intent: 'navigate_projects',
        description: 'Navigate to the projects page',
        example_phrases: ['Go to projects', 'Show me projects', 'Open projects page'],
        required_parameters: [],
        optional_parameters: [],
        response_templates: [
          'Opening the projects page',
          'Navigating to your projects',
          'Taking you to the projects section'
        ],
        action_mappings: {
          handler: 'navigate',
          target: '/projects',
          description: 'Navigate to projects'
        },
        navigation_targets: ['/projects'],
        is_active: true
      },
      {
        name: 'Navigate to Tasks',
        category: 'navigation',
        command_patterns: [
          'go to tasks',
          'open tasks',
          'show tasks',
          'show my tasks',
          'view tasks',
          'navigate to tasks'
        ],
        intent: 'navigate_tasks',
        description: 'Navigate to the tasks page',
        example_phrases: ['Go to tasks', 'Show my tasks', 'Open tasks page'],
        required_parameters: [],
        optional_parameters: [],
        response_templates: [
          'Opening your tasks',
          'Navigating to the tasks page',
          'Showing your task list'
        ],
        action_mappings: {
          handler: 'navigate',
          target: '/tasks',
          description: 'Navigate to tasks'
        },
        navigation_targets: ['/tasks'],
        is_active: true
      },
      
      // Creation Commands
      {
        name: 'Create New Project',
        category: 'creation',
        command_patterns: [
          'create new project',
          'create a project',
          'new project',
          'add project',
          'start new project'
        ],
        intent: 'create_project',
        description: 'Create a new project',
        example_phrases: ['Create a new project', 'Start a new project', 'Add a project'],
        required_parameters: [],
        optional_parameters: ['project_name', 'description'],
        response_templates: [
          'I\'ll help you create a new project',
          'Let\'s create a new project together',
          'Starting the project creation process'
        ],
        action_mappings: {
          handler: 'create',
          target: 'project',
          description: 'Create new project'
        },
        navigation_targets: ['/projects/new'],
        is_active: true
      },
      {
        name: 'Create New Task',
        category: 'creation',
        command_patterns: [
          'create new task',
          'create a task',
          'new task',
          'add task',
          'create task'
        ],
        intent: 'create_task',
        description: 'Create a new task',
        example_phrases: ['Create a new task', 'Add a task', 'Create task'],
        required_parameters: [],
        optional_parameters: ['task_title', 'description', 'priority'],
        response_templates: [
          'I\'ll help you create a new task',
          'Let\'s create a new task',
          'Starting the task creation process'
        ],
        action_mappings: {
          handler: 'create',
          target: 'task',
          description: 'Create new task'
        },
        navigation_targets: ['/tasks/new'],
        is_active: true
      },
      
      // Help Commands
      {
        name: 'General Help',
        category: 'help',
        command_patterns: [
          'help',
          'help me',
          'what can you do',
          'show help',
          'assistance',
          'guide me'
        ],
        intent: 'general_help',
        description: 'Provide general help and assistance',
        example_phrases: ['Help me', 'What can you do?', 'Show me help'],
        required_parameters: [],
        optional_parameters: ['topic'],
        response_templates: [
          'I\'m here to help! I can assist with navigation, creating projects and tasks, managing your team, and much more.',
          'I can help you with various tasks. What would you like assistance with?',
          'I\'m your AI assistant for CTN Nigeria. How can I help you today?'
        ],
        action_mappings: {
          handler: 'help',
          target: 'general',
          description: 'Provide general help'
        },
        is_active: true
      },
      
      // Query Commands
      {
        name: 'Show User Tasks',
        category: 'query',
        command_patterns: [
          'show my tasks',
          'list my tasks',
          'what are my tasks',
          'my task list',
          'display my tasks'
        ],
        intent: 'query_user_tasks',
        description: 'Display user\'s tasks',
        example_phrases: ['Show my tasks', 'What are my tasks?', 'List my tasks'],
        required_parameters: [],
        optional_parameters: ['status', 'priority'],
        response_templates: [
          'Here are your current tasks',
          'Showing your task list',
          'Displaying your assigned tasks'
        ],
        action_mappings: {
          handler: 'query',
          target: 'user_tasks',
          description: 'Show user tasks'
        },
        navigation_targets: ['/tasks/my'],
        is_active: true
      },
      
      // System Commands
      {
        name: 'System Status',
        category: 'system',
        command_patterns: [
          'system status',
          'show status',
          'dashboard overview',
          'system overview',
          'show summary'
        ],
        intent: 'system_status',
        description: 'Show system status and overview',
        example_phrases: ['Show system status', 'System overview', 'Dashboard summary'],
        required_parameters: [],
        optional_parameters: [],
        response_templates: [
          'Here\'s your system overview',
          'Showing the current system status',
          'Displaying your dashboard summary'
        ],
        action_mappings: {
          handler: 'system',
          target: 'status',
          description: 'Show system status'
        },
        navigation_targets: ['/'],
        is_active: true
      }
    ];

    // Insert command templates
    for (const template of commandTemplates) {
      try {
        const { error } = await supabase
          .from('voice_command_templates')
          .insert(template);

        if (error) {
          console.warn(`Warning inserting template ${template.name}:`, error.message);
        } else {
          logSuccess(`Created template: ${template.name}`);
        }
      } catch (err) {
        console.warn(`Error with template ${template.name}:`, err.message);
      }
    }

    // Voice Agent Knowledge Base
    logStep('Creating voice agent knowledge base...');
    
    const knowledgeEntries = [
      {
        category: 'navigation',
        subcategory: 'dashboard',
        topic: 'Dashboard Navigation',
        question_patterns: [
          'how do i go to dashboard',
          'where is the dashboard',
          'show me dashboard',
          'dashboard location'
        ],
        answer_text: 'You can navigate to the dashboard by saying "Go to dashboard" or "Open dashboard". The dashboard shows your project overview, recent activities, and key metrics.',
        related_actions: [
          { type: 'navigate', target: '/', description: 'Go to dashboard' }
        ],
        navigation_hints: ['dashboard', 'main page', 'overview'],
        context_requirements: {},
        confidence_threshold: 0.8,
        response_variations: [
          'The dashboard is your main overview page. Just say "Go to dashboard" to navigate there.',
          'You can access the dashboard by saying "Open dashboard" or "Show dashboard".'
        ],
        follow_up_questions: [
          'Would you like me to take you to the dashboard?',
          'Do you need help with any dashboard features?'
        ],
        tags: ['navigation', 'dashboard', 'overview'],
        is_active: true
      },
      {
        category: 'projects',
        subcategory: 'management',
        topic: 'Project Management',
        question_patterns: [
          'how do i create project',
          'project creation',
          'new project help',
          'project management'
        ],
        answer_text: 'To create a new project, say "Create a new project" or "Start new project". I\'ll guide you through setting up the project name, description, timeline, and team assignments.',
        related_actions: [
          { type: 'navigate', target: '/projects/new', description: 'Create new project' },
          { type: 'help', target: 'project_creation', description: 'Project creation guide' }
        ],
        navigation_hints: ['projects', 'new project', 'create'],
        context_requirements: {},
        confidence_threshold: 0.8,
        response_variations: [
          'Creating projects is easy! Just say "Create a new project" and I\'ll help you through the process.',
          'You can start a new project by saying "New project" or "Create project".'
        ],
        follow_up_questions: [
          'Would you like to create a project now?',
          'Do you need help with project planning?'
        ],
        tags: ['projects', 'creation', 'management'],
        is_active: true
      },
      {
        category: 'tasks',
        subcategory: 'management',
        topic: 'Task Management',
        question_patterns: [
          'how do i manage tasks',
          'task creation',
          'task assignment',
          'task tracking'
        ],
        answer_text: 'For task management, you can say "Show my tasks" to view your assignments, "Create a new task" to add tasks, or "Go to tasks" to access the full task management interface.',
        related_actions: [
          { type: 'navigate', target: '/tasks', description: 'Go to tasks' },
          { type: 'navigate', target: '/tasks/new', description: 'Create new task' },
          { type: 'query', target: 'user_tasks', description: 'Show my tasks' }
        ],
        navigation_hints: ['tasks', 'assignments', 'todo'],
        context_requirements: {},
        confidence_threshold: 0.8,
        response_variations: [
          'Task management is simple with voice commands. Try saying "Show my tasks" or "Create a task".',
          'You can manage tasks by voice - just say what you need to do with your tasks.'
        ],
        follow_up_questions: [
          'Would you like to see your current tasks?',
          'Do you need to create a new task?'
        ],
        tags: ['tasks', 'management', 'assignments'],
        is_active: true
      },
      {
        category: 'help',
        subcategory: 'general',
        topic: 'Voice Commands',
        question_patterns: [
          'what voice commands are available',
          'voice command list',
          'what can i say',
          'voice help'
        ],
        answer_text: 'I understand many voice commands including navigation ("Go to dashboard"), creation ("Create new project"), queries ("Show my tasks"), and help requests. Just speak naturally and I\'ll understand!',
        related_actions: [
          { type: 'help', target: 'voice_commands', description: 'Show voice commands' }
        ],
        navigation_hints: ['help', 'commands', 'voice'],
        context_requirements: {},
        confidence_threshold: 0.9,
        response_variations: [
          'You can use natural language with me. Try commands like "Go to projects", "Create a task", or "Show my dashboard".',
          'I understand conversational voice commands. Just tell me what you want to do!'
        ],
        follow_up_questions: [
          'Is there a specific command you\'d like to try?',
          'Would you like a demonstration of voice commands?'
        ],
        tags: ['help', 'voice', 'commands'],
        is_active: true
      },
      {
        category: 'system',
        subcategory: 'features',
        topic: 'System Capabilities',
        question_patterns: [
          'what can this system do',
          'system features',
          'platform capabilities',
          'what is available'
        ],
        answer_text: 'CTN Nigeria platform offers project management, task tracking, team collaboration, time logging, reporting, memo management, and comprehensive analytics. I can help you navigate and use all these features through voice commands.',
        related_actions: [
          { type: 'navigate', target: '/', description: 'Show dashboard overview' },
          { type: 'help', target: 'system_tour', description: 'System tour' }
        ],
        navigation_hints: ['features', 'capabilities', 'overview'],
        context_requirements: {},
        confidence_threshold: 0.8,
        response_variations: [
          'The platform includes project management, task tracking, team collaboration, and much more. I can guide you through any feature.',
          'We have comprehensive business management tools. What specific area would you like to explore?'
        ],
        follow_up_questions: [
          'Which feature would you like to explore first?',
          'Would you like a guided tour of the system?'
        ],
        tags: ['system', 'features', 'capabilities'],
        is_active: true
      }
    ];

    // Insert knowledge entries
    for (const entry of knowledgeEntries) {
      try {
        const { error } = await supabase
          .from('voice_agent_knowledge')
          .insert(entry);

        if (error) {
          console.warn(`Warning inserting knowledge ${entry.topic}:`, error.message);
        } else {
          logSuccess(`Created knowledge entry: ${entry.topic}`);
        }
      } catch (err) {
        console.warn(`Error with knowledge ${entry.topic}:`, err.message);
      }
    }

    // Voice Navigation Flows
    logStep('Creating voice navigation flows...');
    
    const navigationFlows = [
      {
        name: 'Dashboard Navigation Flow',
        description: 'Guide user to dashboard and explain features',
        start_point: 'any_page',
        end_point: 'dashboard',
        flow_steps: [
          { step: 1, action: 'navigate', target: '/', description: 'Navigate to dashboard' },
          { step: 2, action: 'explain', description: 'Explain dashboard features' },
          { step: 3, action: 'offer_help', description: 'Offer additional assistance' }
        ],
        voice_prompts: {
          start: 'I\'ll take you to the dashboard now',
          navigation: 'Navigating to your main dashboard',
          explanation: 'Here you can see your project overview, recent activities, and key metrics',
          completion: 'You\'re now on the dashboard. What would you like to do next?'
        },
        confirmation_required: false,
        estimated_duration_seconds: 15,
        difficulty_level: 'easy',
        prerequisites: [],
        success_criteria: 'User successfully navigated to dashboard',
        fallback_flows: ['general_help'],
        is_active: true
      },
      {
        name: 'Project Creation Flow',
        description: 'Guide user through project creation process',
        start_point: 'any_page',
        end_point: 'project_created',
        flow_steps: [
          { step: 1, action: 'navigate', target: '/projects/new', description: 'Navigate to project creation' },
          { step: 2, action: 'guide', description: 'Guide through project details' },
          { step: 3, action: 'confirm', description: 'Confirm project creation' },
          { step: 4, action: 'complete', description: 'Complete project creation' }
        ],
        voice_prompts: {
          start: 'I\'ll help you create a new project',
          navigation: 'Taking you to the project creation page',
          guidance: 'Please fill in the project name, description, and timeline',
          confirmation: 'Would you like to create this project?',
          completion: 'Your project has been created successfully!'
        },
        confirmation_required: true,
        estimated_duration_seconds: 60,
        difficulty_level: 'medium',
        prerequisites: ['authenticated'],
        success_criteria: 'Project successfully created',
        fallback_flows: ['project_help', 'general_help'],
        is_active: true
      },
      {
        name: 'Task Management Flow',
        description: 'Guide user through task management features',
        start_point: 'any_page',
        end_point: 'task_managed',
        flow_steps: [
          { step: 1, action: 'navigate', target: '/tasks', description: 'Navigate to tasks' },
          { step: 2, action: 'show_options', description: 'Show task management options' },
          { step: 3, action: 'execute_choice', description: 'Execute user choice' }
        ],
        voice_prompts: {
          start: 'Let me show you your task management options',
          navigation: 'Opening your tasks page',
          options: 'You can view, create, edit, or assign tasks. What would you like to do?',
          completion: 'Task management action completed'
        },
        confirmation_required: false,
        estimated_duration_seconds: 30,
        difficulty_level: 'easy',
        prerequisites: [],
        success_criteria: 'User successfully managed tasks',
        fallback_flows: ['task_help', 'general_help'],
        is_active: true
      }
    ];

    // Insert navigation flows
    for (const flow of navigationFlows) {
      try {
        const { error } = await supabase
          .from('voice_navigation_flows')
          .insert(flow);

        if (error) {
          console.warn(`Warning inserting flow ${flow.name}:`, error.message);
        } else {
          logSuccess(`Created navigation flow: ${flow.name}`);
        }
      } catch (err) {
        console.warn(`Error with flow ${flow.name}:`, err.message);
      }
    }

    logSuccess('🎉 VOICE TEMPLATES POPULATION COMPLETED!');
    console.log('\n🚀 CREATED VOICE SYSTEM DATA:');
    console.log(`✅ ${commandTemplates.length} Voice Command Templates`);
    console.log(`✅ ${knowledgeEntries.length} Knowledge Base Entries`);
    console.log(`✅ ${navigationFlows.length} Navigation Flows`);
    console.log('\n🎯 Voice command system is fully configured!');
    
    return true;

  } catch (error) {
    logError(`Voice templates population failed: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
}

// Run the population
populateVoiceTemplates()
  .then((success) => {
    if (success) {
      console.log('\n🎉 SUCCESS: Voice templates populated successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Voice templates population encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
