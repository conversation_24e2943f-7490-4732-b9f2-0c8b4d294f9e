<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Current User Profile - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .sql-box {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .error-details {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Current User Profile</h1>
            <p>Quick fix for the current user profile issues</p>
        </div>

        <div class="card">
            <h3>🚨 Current Issue</h3>
            <div class="error-details">
                <h4>New User Having Same Issues:</h4>
                <ul>
                    <li><strong>User ID:</strong> 94827b40-c98a-4fd3-bfff-8cefc21fbbd6</li>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Name:</strong> Obibi Ifeanyi</li>
                    <li><strong>406 Error:</strong> Profile fetch blocked by RLS</li>
                    <li><strong>409 Error:</strong> Duplicate email constraint violation</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>⚡ Immediate Fix</h3>
            <p>This SQL will fix the current user's profile issues:</p>
            
            <div class="sql-box" id="sqlCode">-- IMMEDIATE FIX FOR CURRENT USER
-- Copy and run this in Supabase SQL Editor

-- 1. Disable RLS temporarily
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Clean up any duplicate profiles for this email
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != '94827b40-c98a-4fd3-bfff-8cefc21fbbd6';

-- 3. Create/update the correct profile for current user
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '94827b40-c98a-4fd3-bfff-8cefc21fbbd6'::UUID,
    'Obibi Ifeanyi',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

-- 4. Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;

-- 5. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 6. Create simple, working policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- 7. Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- 8. Verify both users are working
SELECT 'VERIFICATION:' as status;
SELECT id, email, full_name, role, status 
FROM public.profiles 
WHERE id IN (
    '94827b40-c98a-4fd3-bfff-8cefc21fbbd6',
    '44349058-db4b-4a0b-8c99-8a913d07df74'
)
ORDER BY email;</div>

            <button class="button" onclick="copySQL()">📋 Copy Fix SQL</button>
        </div>

        <div class="card">
            <h3>📋 Quick Steps</h3>
            <ol>
                <li><strong>Copy the SQL</strong> by clicking the button above</li>
                <li><strong>Go to Supabase Dashboard</strong> → SQL Editor</li>
                <li><strong>Paste and run the SQL</strong></li>
                <li><strong>Refresh your application</strong></li>
                <li><strong>Try logging in again</strong> - should work without errors</li>
            </ol>
        </div>

        <div id="status" class="status info">
            🔧 Ready to fix the current user profile issues.
        </div>
    </div>

    <script>
        function copySQL() {
            const sqlCode = document.getElementById('sqlCode').textContent;
            navigator.clipboard.writeText(sqlCode).then(() => {
                const statusDiv = document.getElementById('status');
                statusDiv.className = 'status success';
                statusDiv.innerHTML = '✅ SQL copied to clipboard!\n\nNext steps:\n1. Go to Supabase Dashboard → SQL Editor\n2. Paste and run the SQL\n3. Refresh your application\n4. Try logging in again\n\nThis will fix the 406/409 errors for the current user.';
            }).catch(() => {
                alert('Failed to copy SQL. Please select and copy manually.');
            });
        }

        // Show initial status
        window.addEventListener('load', () => {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '🔧 Ready to fix profile issues for user: <EMAIL>\n\nClick "Copy Fix SQL" to get the solution.';
        });
    </script>
</body>
</html>
