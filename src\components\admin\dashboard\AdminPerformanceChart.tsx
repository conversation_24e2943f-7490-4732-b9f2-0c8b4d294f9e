import { useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import { useEffect } from "react";

const formatNaira = (value: number) => {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};

export const AdminPerformanceChart = () => {
  const queryClient = useQueryClient();

  // Fetch performance data from invoices with real-time updates
  const { data: performanceData, isLoading } = useQuery({
    queryKey: ['admin-performance'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select('total_amount, created_at, payment_status')
        .eq('payment_status', 'paid')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });

  // Set up real-time subscription for invoice changes
  useEffect(() => {
    const channel = supabase
      .channel('admin-invoices-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'invoices'
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['admin-performance'] });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);

  // Process data for chart
  const processedData = () => {
    if (!performanceData || performanceData.length === 0) {
      // Fallback data when no real data is available
      return [
        { name: 'Mar 15', value: 2400 },
        { name: 'Mar 20', value: 3600 },
        { name: 'Mar 25', value: 3200 },
        { name: 'Mar 30', value: 4500 },
        { name: 'Apr 5', value: 4200 },
        { name: 'Apr 10', value: 5200 },
        { name: 'Apr 15', value: 5800 },
      ];
    }

    // Group data by week and sum amounts
    const weeklyData: { [key: string]: number } = {};
    
    performanceData.forEach(invoice => {
      const date = new Date(invoice.created_at);
      const weekStart = new Date(date.setDate(date.getDate() - date.getDay()));
      const weekKey = weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      
      weeklyData[weekKey] = (weeklyData[weekKey] || 0) + (invoice.total_amount || 0);
    });

    return Object.entries(weeklyData)
      .map(([name, value]) => ({ name, value }))
      .slice(-7); // Last 7 weeks
  };

  const data = processedData();
  
  // Calculate performance trend
  const calculateTrend = () => {
    if (data.length < 2) return 0;
    const latest = data[data.length - 1].value;
    const previous = data[data.length - 2].value;
    return previous > 0 ? ((latest - previous) / previous) * 100 : 0;
  };

  const trend = calculateTrend();

  return (
    <Card className="chart-container" data-aos="fade-up">
      <CardHeader>
        <CardTitle className="text-lg font-medium modern-heading">
          Performance Overview
          <span className={`ml-2 text-sm font-semibold ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {trend >= 0 ? '+' : ''}{trend.toFixed(2)}%
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-[200px] flex items-center justify-center">
            <div className="text-muted-foreground">Loading performance data...</div>
          </div>
        ) : (
          <div className="h-[200px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data}>
                <XAxis 
                  dataKey="name" 
                  stroke="currentColor" 
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis 
                  stroke="currentColor" 
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => formatNaira(value)}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'var(--background)',
                    border: '1px solid var(--border)',
                    borderRadius: '8px',
                  }}
                  formatter={(value: number) => [formatNaira(value), 'Revenue']}
                />
                <Line 
                  type="monotone" 
                  dataKey="value" 
                  stroke="#ff1c04" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
};