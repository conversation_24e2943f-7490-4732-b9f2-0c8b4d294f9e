/**
 * Global Error Handler and <PERSON><PERSON>e Cleaner
 * Handles various browser extension errors and provides clean error reporting
 */

export class ErrorHandler {
  private static instance: <PERSON>rror<PERSON>andler;
  private originalConsoleError: typeof console.error;
  private originalConsoleWarn: typeof console.warn;
  private blockedErrors: Set<string> = new Set();

  constructor() {
    this.originalConsoleError = console.error.bind(console);
    this.originalConsoleWarn = console.warn.bind(console);
    this.initializeErrorBlocking();
  }

  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  /**
   * Initialize error blocking for known problematic extensions
   */
  private initializeErrorBlocking(): void {
    // Known problematic patterns to block
    const blockedPatterns = [
      'grm ERROR',
      'grammarly',
      'iterable',
      'Not supported: in app messages',
      'chrome-extension://',
      'moz-extension://',
      'safari-extension://',
      'edge-extension://',
      'Failed to send a request to the Edge Function', // Supabase Edge Function errors
      'FunctionsFetchError',
      'Network request failed',
      'TypeError: Failed to fetch'
    ];

    // Override console.error
    console.error = (...args: any[]) => {
      const message = args.join(' ').toLowerCase();
      
      // Check if this is a blocked error pattern
      for (const pattern of blockedPatterns) {
        if (message.includes(pattern.toLowerCase())) {
          this.blockedErrors.add(pattern);
          
          // Only log Supabase errors in development
          if (pattern.includes('Edge Function') || pattern.includes('FunctionsFetchError')) {
            if (process.env.NODE_ENV === 'development') {
              this.originalConsoleError('🔄 [Supabase Edge Function]', ...args);
              this.originalConsoleError('💡 Tip: This is expected if Edge Functions are not deployed locally');
            }
          }
          
          return; // Block the error
        }
      }
      
      // Allow legitimate errors through
      this.originalConsoleError(...args);
    };

    // Override console.warn
    console.warn = (...args: any[]) => {
      const message = args.join(' ').toLowerCase();
      
      // Check if this is a blocked warning pattern
      for (const pattern of blockedPatterns) {
        if (message.includes(pattern.toLowerCase())) {
          this.blockedErrors.add(pattern);
          return; // Block the warning
        }
      }
      
      // Allow legitimate warnings through
      this.originalConsoleWarn(...args);
    };

    console.log('🛡️ Error handler initialized - blocking extension errors');
  }

  /**
   * Handle Supabase Edge Function errors specifically
   */
  public static handleSupabaseError(error: any, functionName: string): {
    shouldRetry: boolean;
    fallbackMessage: string;
    errorType: 'network' | 'function' | 'auth' | 'unknown';
  } {
    const errorMessage = error?.message || error?.toString() || '';
    
    // Network-related errors
    if (errorMessage.includes('Failed to fetch') || 
        errorMessage.includes('Network request failed') ||
        errorMessage.includes('FunctionsFetchError')) {
      return {
        shouldRetry: true,
        fallbackMessage: 'Network connection issue. Please check your internet connection.',
        errorType: 'network'
      };
    }

    // Function not found or not deployed
    if (errorMessage.includes('Function not found') ||
        errorMessage.includes('404')) {
      return {
        shouldRetry: false,
        fallbackMessage: `The ${functionName} service is currently unavailable. Using backup system.`,
        errorType: 'function'
      };
    }

    // Authentication errors
    if (errorMessage.includes('401') || 
        errorMessage.includes('Unauthorized') ||
        errorMessage.includes('JWT')) {
      return {
        shouldRetry: false,
        fallbackMessage: 'Authentication required. Please log in again.',
        errorType: 'auth'
      };
    }

    // Unknown error
    return {
      shouldRetry: false,
      fallbackMessage: 'An unexpected error occurred. Please try again.',
      errorType: 'unknown'
    };
  }

  /**
   * Clean error message for user display
   */
  public static cleanErrorMessage(error: any): string {
    if (!error) return 'An unknown error occurred';
    
    const message = error.message || error.toString();
    
    // Remove technical jargon and make user-friendly
    const cleanPatterns = [
      { pattern: /FunctionsFetchError:?\s*/gi, replacement: '' },
      { pattern: /Failed to send a request to the Edge Function/gi, replacement: 'Service temporarily unavailable' },
      { pattern: /TypeError:?\s*/gi, replacement: '' },
      { pattern: /ReferenceError:?\s*/gi, replacement: '' },
      { pattern: /SyntaxError:?\s*/gi, replacement: 'Configuration error' },
      { pattern: /NetworkError:?\s*/gi, replacement: 'Network connection issue' },
      { pattern: /at\s+.*\s+\(.*\)/gi, replacement: '' }, // Remove stack trace references
    ];

    let cleanMessage = message;
    for (const { pattern, replacement } of cleanPatterns) {
      cleanMessage = cleanMessage.replace(pattern, replacement);
    }

    // Capitalize first letter and ensure it ends with a period
    cleanMessage = cleanMessage.trim();
    if (cleanMessage) {
      cleanMessage = cleanMessage.charAt(0).toUpperCase() + cleanMessage.slice(1);
      if (!cleanMessage.endsWith('.')) {
        cleanMessage += '.';
      }
    } else {
      cleanMessage = 'An unexpected error occurred.';
    }

    return cleanMessage;
  }

  /**
   * Log blocked errors summary (for debugging)
   */
  public getBlockedErrorsSummary(): { [key: string]: number } {
    const summary: { [key: string]: number } = {};
    this.blockedErrors.forEach(error => {
      summary[error] = (summary[error] || 0) + 1;
    });
    return summary;
  }

  /**
   * Reset blocked errors tracking
   */
  public resetBlockedErrors(): void {
    this.blockedErrors.clear();
  }

  /**
   * Restore original console methods (for debugging)
   */
  public restoreConsole(): void {
    console.error = this.originalConsoleError;
    console.warn = this.originalConsoleWarn;
    console.log('🔧 Console methods restored to original');
  }

  /**
   * Handle unhandled promise rejections
   */
  public static initializeGlobalErrorHandling(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason;
      const errorInfo = ErrorHandler.handleSupabaseError(error, 'unknown');
      
      if (errorInfo.errorType === 'network' || errorInfo.errorType === 'function') {
        // Prevent the error from appearing in console for known issues
        event.preventDefault();
        console.log('🔄 Handled unhandled rejection:', errorInfo.fallbackMessage);
      }
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      const error = event.error;
      const message = event.message || '';
      
      // Block extension-related errors
      if (message.includes('grammarly') || 
          message.includes('chrome-extension') ||
          message.includes('moz-extension')) {
        event.preventDefault();
        return;
      }

      // Handle Supabase-related errors
      if (message.includes('supabase') || message.includes('Edge Function')) {
        const errorInfo = ErrorHandler.handleSupabaseError(error, 'global');
        console.log('🔄 Global Supabase error handled:', errorInfo.fallbackMessage);
        event.preventDefault();
      }
    });

    console.log('🛡️ Global error handling initialized');
  }
}

// Auto-initialize error handler
if (typeof window !== 'undefined') {
  const errorHandler = ErrorHandler.getInstance();
  ErrorHandler.initializeGlobalErrorHandling();
  
  // Expose for debugging in development
  if (process.env.NODE_ENV === 'development') {
    (window as any).errorHandler = errorHandler;
  }
}

export default ErrorHandler;
