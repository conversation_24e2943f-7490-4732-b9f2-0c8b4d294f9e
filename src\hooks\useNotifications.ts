import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { notificationService, TimeNotification } from '@/services/notificationService';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';

export interface NotificationState {
  notifications: TimeNotification[];
  unreadCount: number;
  isLoading: boolean;
  error: Error | null;
}

export const useNotifications = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubscribed, setIsSubscribed] = useState(false);

  // Fetch notifications
  const {
    data: notifications = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['notifications', userProfile?.id],
    queryFn: async () => {
      try {
        return await notificationService.getUserNotifications(userProfile!.id);
      } catch (error: any) {
        // Handle network errors gracefully
        if (error?.message?.includes('ERR_NETWORK_CHANGED') ||
            error?.message?.includes('ERR_CONNECTION_TIMED_OUT') ||
            error?.message?.includes('Failed to fetch')) {
          console.warn('Network error fetching notifications:', error.message);
          return []; // Return empty array instead of throwing
        }
        throw error; // Re-throw other errors
      }
    },
    enabled: !!userProfile?.id,
    refetchInterval: 30000, // Refetch every 30 seconds
    retry: (failureCount, error: any) => {
      // Retry network errors up to 3 times
      if (error?.message?.includes('ERR_NETWORK_CHANGED') ||
          error?.message?.includes('ERR_CONNECTION_TIMED_OUT') ||
          error?.message?.includes('Failed to fetch')) {
        return failureCount < 3;
      }
      return false; // Don't retry other errors
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Calculate unread count
  const unreadCount = notifications.filter(n => !n.read).length;
  // Mark notification as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: (notificationId: string) => notificationService.markAsRead(notificationId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications', userProfile?.id] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read',
        variant: 'destructive',
      });
    },
  });

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation({
    mutationFn: () => notificationService.markAllAsRead(userProfile!.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications', userProfile?.id] });
      toast({
        title: 'Success',
        description: 'All notifications marked as read',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to mark all notifications as read',
        variant: 'destructive',
      });
    },
  });

  // Handle new notification
  const handleNewNotification = useCallback((notification: TimeNotification) => {
    // Update the query cache
    queryClient.setQueryData(
      ['notifications', userProfile?.id],
      (oldData: TimeNotification[] = []) => [notification, ...oldData]
    );

    // Show toast notification
    toast({
      title: notification.title,
      description: notification.message,
      duration: 5000,
    });

    // Play notification sound (optional)
    if ('Audio' in window) {
      try {
        const audio = new Audio('/notification-sound.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
          // Ignore audio play errors (user interaction required)
        });
      } catch (error) {
        // Ignore audio errors
      }
    }
  }, [userProfile?.id, queryClient, toast]);

  // Subscribe to real-time notifications
  useEffect(() => {
    if (!userProfile?.id || isSubscribed) return;

    const unsubscribe = notificationService.subscribeToNotifications(
      userProfile.id,
      handleNewNotification
    );

    setIsSubscribed(true);

    return () => {
      unsubscribe();
      setIsSubscribed(false);
    };
  }, [userProfile?.id, isSubscribed, handleNewNotification]);

  // Request notification permission on mount
  useEffect(() => {
    notificationService.requestNotificationPermission();
  }, []);

  const markAsRead = useCallback((notificationId: string) => {
    markAsReadMutation.mutate(notificationId);
  }, [markAsReadMutation]);

  const markAllAsRead = useCallback(() => {
    markAllAsReadMutation.mutate();
  }, [markAllAsReadMutation]);

  const refreshNotifications = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    notifications,
    unreadCount,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    refreshNotifications,
    isMarkingAsRead: markAsReadMutation.isPending,
    isMarkingAllAsRead: markAllAsReadMutation.isPending,
  };
};

// Hook for notification preferences
export const useNotificationPreferences = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch preferences
  const {
    data: preferences,
    isLoading,
    error
  } = useQuery({
    queryKey: ['notification-preferences', userProfile?.id],
    queryFn: () => notificationService.getNotificationPreferences(userProfile!.id),
    enabled: !!userProfile?.id,
  });

  // Update preferences mutation
  const updatePreferencesMutation = useMutation({
    mutationFn: (newPreferences: any) =>
      notificationService.updateNotificationPreferences(userProfile!.id, newPreferences),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notification-preferences', userProfile?.id] });
      toast({
        title: 'Success',
        description: 'Notification preferences updated',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to update notification preferences',
        variant: 'destructive',
      });
    },
  });

  const updatePreferences = useCallback((newPreferences: any) => {
    updatePreferencesMutation.mutate(newPreferences);
  }, [updatePreferencesMutation]);

  return {
    preferences,
    isLoading,
    error,
    updatePreferences,
    isUpdating: updatePreferencesMutation.isPending,
  };
};

// Hook for sending notifications (for managers/admins)
export const useNotificationSender = () => {
  const { toast } = useToast();

  const sendClockInNotification = useCallback(async (
    userId: string,
    userName: string,
    location: string,
    time: string
  ) => {
    try {
      await notificationService.notifyClockIn(userId, userName, location, time);
    } catch (error) {
      console.error('Failed to send clock-in notification:', error);
    }
  }, []);

  const sendClockOutNotification = useCallback(async (
    userId: string,
    userName: string,
    location: string,
    time: string,
    duration: string
  ) => {
    try {
      await notificationService.notifyClockOut(userId, userName, location, time, duration);
    } catch (error) {
      console.error('Failed to send clock-out notification:', error);
    }
  }, []);

  const sendOvertimeAlert = useCallback(async (
    userId: string,
    userName: string,
    overtimeHours: number
  ) => {
    try {
      await notificationService.notifyOvertime(userId, userName, overtimeHours);
    } catch (error) {
      console.error('Failed to send overtime alert:', error);
    }
  }, []);

  const sendLateArrivalNotification = useCallback(async (
    userId: string,
    userName: string,
    minutesLate: number
  ) => {
    try {
      await notificationService.notifyLateArrival(userId, userName, minutesLate);
    } catch (error) {
      console.error('Failed to send late arrival notification:', error);
    }
  }, []);

  return {
    sendClockInNotification,
    sendClockOutNotification,
    sendOvertimeAlert,
    sendLateArrivalNotification,
  };
};
