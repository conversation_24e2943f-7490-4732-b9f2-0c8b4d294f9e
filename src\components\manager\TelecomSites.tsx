import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MapPin, Bar<PERSON>hart3, FileText, Calendar, Loader2 } from "lucide-react"; // Added Loader2 for loading state
import { useToast } from "@/hooks/use-toast";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useNavigate } from "react-router-dom";

// --- Interfaces ---
interface TelecomSite {
  id: string;
  name: string;
  location: string;
  status: string;
  created_at?: string;
  updated_at?: string;
}

interface ProjectReport {
  id: string;
  report_title: string;
  site_id: string;
  created_at: string;
  created_by: string;
}

// --- Component ---
export const TelecomSites = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { userProfile } = useAuth();
  const [sites, setSites] = useState<TelecomSite[]>([]);
  const [reports, setReports] = useState<ProjectReport[]>([]);
  const [loading, setLoading] = useState(true);

  // --- Utility Function (Corrected) ---
  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "bg-green-500/20 text-green-500";
      case "maintenance":
      case "planning":
        return "bg-yellow-500/20 text-yellow-500";
      case "inactive":
      case "cancelled":
        return "bg-red-500/20 text-red-500";
      case "completed":
        return "bg-blue-500/20 text-blue-500";
      default:
        return "bg-gray-500/20 text-gray-500"; // Fallback for unknown statuses
    }
  };

  // --- Data Fetching ---
  useEffect(() => {
    const fetchTelecomSites = async () => {
      try {
        const { data, error } = await supabase
          .from("telecom_sites")
          .select("*")
          .order("name");
        if (error) throw error;
        setSites(data || []);
      } catch (error) {
        console.error("Error fetching telecom sites:", error);
        toast({
          title: "Error",
          description: "Failed to fetch telecom sites",
          variant: "destructive",
        });
      }
    };

    const fetchReports = async () => {
      try {
        const { data, error } = await supabase
          .from("construction_reports")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(5);
        if (error) throw error;
        setReports(data || []);
      } catch (error) {
        console.error("Error fetching reports:", error);
        toast({
          title: "Error",
          description: "Failed to fetch recent reports",
          variant: "destructive",
        });
      }
    };

    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchTelecomSites(), fetchReports()]);
      setLoading(false);
    };

    loadData();
    // The empty dependency array `[]` ensures this runs only once on mount.
  }, [toast]); // Added toast to dependency array as it's an external function

  // --- Event Handlers ---
  const handleAction = async (
    action: string,
    siteName: string,
    siteId: string
  ) => {
    if (action === "Submit Report") {
      try {
        const { error } = await supabase.from("construction_reports").insert({
          report_title: `${siteName} - Telecom Site Report`,
          site_id: siteId,
          report_type: "site_inspection",
          work_performed: `Automated telecom site report for ${siteName}`,
          created_by: userProfile?.id,
        });

        if (error) throw error;

        toast({
          title: "Report Submitted",
          description: `Report for ${siteName} submitted successfully`,
        });
        // Re-fetch reports to show the new one
        const { data, error: fetchError } = await supabase
          .from("construction_reports")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(5);
        if (fetchError) throw fetchError;
        setReports(data || []);
      } catch (error: any) {
        console.error("Error submitting report:", error);
        toast({
          title: "Error",
          description: error.message || "Failed to submit report",
          variant: "destructive",
        });
      }
    } else if (action === "View Analytics") {
      toast({
        title: "Analytics",
        description: `Loading analytics for ${siteName}`,
      });
      navigate(`/dashboard/manager?tab=analytics&site=${siteId}`);
    }
  };

  // --- Render Logic ---

  // 1. Show a loading state while fetching data
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-12 w-12 animate-spin text-muted-foreground" />
      </div>
    );
  }

  // 2. Render the component with fetched data
  return (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <MapPin className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Total Sites
              </p>
              <p className="text-2xl font-bold">{sites.length}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <BarChart3 className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Active Sites
              </p>
              <p className="text-2xl font-bold">
                {sites.filter((site) => site.status === "active").length}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Calendar className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                In Progress
              </p>
              <p className="text-2xl font-bold">
                {
                  sites.filter((site) =>
                    ["maintenance", "planning"].includes(site.status)
                  ).length
                }
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <FileText className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">
                Recent Reports
              </p>
              <p className="text-2xl font-bold">{reports.length}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Show sites grid or empty state */}
      {sites.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2">
          {sites.map((site) => (
            <Card
              key={site.id}
              className="bg-black/10 border-none shadow-lg rounded-2xl hover:bg-black/20 transition-all"
            >
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg font-medium">
                  <MapPin className="h-5 w-5" />
                  {site.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      {site.location}
                    </p>
                    <div className="mt-4 flex items-center justify-between">
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(site.status)}`}
                      >
                        {site.status || "Unknown"}
                      </span>
                      {site.created_at && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          <span>
                            Created:{" "}
                            {new Date(site.created_at).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between gap-4 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 rounded-xl"
                      onClick={() =>
                        handleAction("View Analytics", site.name, site.id)
                      }
                    >
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Analytics
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 rounded-xl"
                      onClick={() =>
                        handleAction("Submit Report", site.name, site.id)
                      }
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Report
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              No Telecom Sites Found
            </h3>
            <p className="text-muted-foreground">
              Contact your administrator to add telecom sites.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
