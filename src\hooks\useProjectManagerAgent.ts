/**
 * Hook for Project Manager Assistant Agent integration
 * Connects AI-generated project plans with existing project management system
 */

import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { ProjectManagerAgent, ProjectPlan, TeamMember } from '@/lib/project-manager-agent';
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';

interface UseProjectManagerAgentReturn {
  isGenerating: boolean;
  currentStep: string;
  progress: number;
  generateProjectPlan: (description: string, teamMembers: TeamMember[]) => Promise<ProjectPlan | null>;
  saveProjectToDatabase: (projectPlan: ProjectPlan, projectName: string) => Promise<string | null>;
  getTeamMembersFromDatabase: () => Promise<TeamMember[]>;
  createProjectFromPlan: (projectPlan: ProjectPlan, projectName: string, description: string) => Promise<boolean>;
}

export const useProjectManagerAgent = (): UseProjectManagerAgentReturn => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  const agent = new ProjectManagerAgent();

  const generateProjectPlan = useCallback(async (
    description: string, 
    teamMembers: TeamMember[]
  ): Promise<ProjectPlan | null> => {
    if (!description.trim() || teamMembers.length === 0) {
      toast({
        title: "Invalid Input",
        description: "Please provide project description and team members.",
        variant: "destructive",
      });
      return null;
    }

    setIsGenerating(true);
    setProgress(0);

    try {
      const steps = [
        'Initializing AI Agent...',
        'Analyzing project description...',
        'Generating actionable tasks...',
        'Mapping task dependencies...',
        'Creating optimized schedule...',
        'Allocating tasks to team members...',
        'Assessing project risks...',
        'Generating insights and optimizations...',
        'Finalizing project plan...'
      ];

      // Simulate progress updates
      for (let i = 0; i < steps.length - 1; i++) {
        setCurrentStep(steps[i]);
        setProgress((i + 1) / steps.length * 90); // Leave 10% for final processing
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      setCurrentStep(steps[steps.length - 1]);
      
      // Generate the actual project plan
      const projectPlan = await agent.createProjectPlan(description, teamMembers, 3);
      
      setProgress(100);
      setCurrentStep('Project plan generated successfully!');

      // Log the AI interaction
      await supabase.from('ai_interactions').insert({
        user_id: userProfile?.id,
        interaction_type: 'project_planning',
        input_data: {
          description,
          team_size: teamMembers.length,
          team_roles: teamMembers.map(m => m.role)
        },
        output_data: {
          tasks_count: projectPlan.tasks.length,
          duration_days: projectPlan.total_duration_days,
          risk_score: projectPlan.overall_risk_score
        },
        metadata: {
          agent_type: 'project_manager_assistant',
          model: 'gpt-4o-mini',
          iterations: 3
        }
      });

      toast({
        title: "Project Plan Generated",
        description: `Created ${projectPlan.tasks.length} tasks with ${projectPlan.total_duration_days} days duration.`,
      });

      return projectPlan;

    } catch (error) {
      console.error('Error generating project plan:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate project plan. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, [userProfile?.id, toast]);

  const getTeamMembersFromDatabase = useCallback(async (): Promise<TeamMember[]> => {
    try {
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('id, full_name, role, department')
        .neq('role', 'admin'); // Exclude admin from team assignments

      if (error) throw error;

      return profiles.map(profile => ({
        id: profile.id,
        name: profile.full_name || 'Unknown',
        role: profile.role || 'Staff',
        skills: [profile.department || 'General'].filter(Boolean),
        availability: 100, // Default availability
        current_workload: 0 // Default workload
      }));

    } catch (error) {
      console.error('Error fetching team members:', error);
      toast({
        title: "Error",
        description: "Failed to fetch team members from database.",
        variant: "destructive",
      });
      return [];
    }
  }, [toast]);

  const saveProjectToDatabase = useCallback(async (
    projectPlan: ProjectPlan, 
    projectName: string
  ): Promise<string | null> => {
    try {
      // Create the main project record
      const projectData: any = {
        name: projectName,
        description: `AI-generated project plan with ${projectPlan.tasks.length} tasks`,
        status: 'planning',
        start_date: new Date().toISOString(),
        end_date: new Date(Date.now() + projectPlan.total_duration_days * 24 * 60 * 60 * 1000).toISOString(),
        ai_generated: true,
        ai_metadata: {
          total_tasks: projectPlan.tasks.length,
          duration_days: projectPlan.total_duration_days,
          risk_score: projectPlan.overall_risk_score,
          generated_at: new Date().toISOString()
        }
      };

      // Add created_by if user profile exists
      if (userProfile?.id) {
        projectData.created_by = userProfile.id;
      }

      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert(projectData)
        .select()
        .single();

      if (projectError) throw projectError;

      // Create tasks
      const tasksToInsert = projectPlan.tasks.map(task => {
        const taskData: any = {
          project_id: project.id,
          title: task.task_name,
          description: task.task_description,
          status: 'pending', // Use 'pending' as it's more common in the schema
          priority: task.priority,
          ai_generated: true,
          ai_task_id: task.id,
          ai_metadata: {
            original_task: task,
            risk_score: projectPlan.risks.find(r => r.task_id === task.id)?.risk_score || 0,
            estimated_days: task.estimated_days
          }
        };

        // Add estimated_hours if the column exists
        if (task.estimated_days) {
          taskData.estimated_hours = task.estimated_days * 8; // Convert days to hours
        }

        return taskData;
      });

      const { error: tasksError } = await supabase
        .from('tasks')
        .insert(tasksToInsert);

      if (tasksError) throw tasksError;

      // Store the complete project plan in a separate table for reference
      const { error: planError } = await supabase
        .from('ai_project_plans')
        .insert({
          project_id: project.id,
          plan_data: projectPlan,
          created_by: userProfile?.id,
          version: 1
        });

      if (planError) {
        console.warn('Failed to store detailed project plan:', planError);
        // Don't fail the entire operation for this
      }

      toast({
        title: "Project Saved",
        description: `Project "${projectName}" has been saved to the database.`,
      });

      return project.id;

    } catch (error) {
      console.error('Error saving project to database:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save project to database.",
        variant: "destructive",
      });
      return null;
    }
  }, [userProfile?.id, toast]);

  const createProjectFromPlan = useCallback(async (
    projectPlan: ProjectPlan,
    projectName: string,
    description: string
  ): Promise<boolean> => {
    try {
      const projectId = await saveProjectToDatabase(projectPlan, projectName);
      
      if (!projectId) {
        return false;
      }

      // Create team assignments based on allocations
      const teamAssignments = projectPlan.allocations.map(allocation => ({
        project_id: projectId,
        user_id: allocation.team_member_id,
        role: 'member',
        assigned_at: new Date().toISOString()
      }));

      if (teamAssignments.length > 0) {
        const { error: assignmentError } = await supabase
          .from('project_team')
          .insert(teamAssignments);

        if (assignmentError) {
          console.warn('Failed to create team assignments:', assignmentError);
        }
      }

      // Create project milestones based on high-priority tasks
      const milestones = projectPlan.tasks
        .filter(task => task.priority === 'critical' || task.priority === 'high')
        .map(task => {
          const schedule = projectPlan.schedule.find(s => s.task_id === task.id);
          return {
            project_id: projectId,
            title: `Milestone: ${task.task_name}`,
            description: task.task_description,
            due_date: schedule?.end_date.toISOString() || new Date().toISOString(),
            status: 'pending'
          };
        });

      if (milestones.length > 0) {
        const { error: milestonesError } = await supabase
          .from('project_milestones')
          .insert(milestones);

        if (milestonesError) {
          console.warn('Failed to create milestones:', milestonesError);
        }
      }

      toast({
        title: "Project Created Successfully",
        description: `Project "${projectName}" has been created with all tasks and team assignments.`,
      });

      return true;

    } catch (error) {
      console.error('Error creating project from plan:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create project from plan.",
        variant: "destructive",
      });
      return false;
    }
  }, [saveProjectToDatabase, toast]);

  return {
    isGenerating,
    currentStep,
    progress,
    generateProjectPlan,
    saveProjectToDatabase,
    getTeamMembersFromDatabase,
    createProjectFromPlan
  };
};
