
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download, Maximize2, RefreshCw } from "lucide-react";
import { ResponsiveContainer } from "recharts";
import { useState, ReactElement } from "react";

interface EnhancedChartProps {
  title: string;
  children: ReactElement;
  icon?: React.ReactNode;
  timeFilter?: boolean;
  exportable?: boolean;
  refreshable?: boolean;
  fullscreen?: boolean;
  className?: string;
  onRefresh?: () => void;
  onTimeRangeChange?: (range: string) => void;
}

export const EnhancedChart = ({
  title,
  children,
  icon,
  timeFilter = false,
  exportable = false,
  refreshable = false,
  fullscreen = false,
  className,
  onRefresh,
  onTimeRangeChange
}: EnhancedChartProps) => {
  const [timeRange, setTimeRange] = useState("30");
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      if (onRefresh) {
        await onRefresh();
      } else {
        // Default refresh delay
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    if (onTimeRangeChange) {
      onTimeRangeChange(range);
    }
  };

  const handleExport = () => {
    // Export functionality would be implemented here
    console.log("Exporting chart data...");
  };

  return (
    <Card className={`chart-container ${className || ''}`} data-aos="fade-up">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 modern-heading">
            <div className="p-2 rounded-full bg-primary/10">
              {icon}
            </div>
            {title}
          </CardTitle>
          <div className="flex items-center gap-2">
            {timeFilter && (
              <Select value={timeRange} onValueChange={handleTimeRangeChange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            )}
            {refreshable && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </Button>
            )}
            {exportable && (
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4" />
              </Button>
            )}
            {fullscreen && (
              <Button variant="outline" size="sm">
                <Maximize2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          {children}
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};
