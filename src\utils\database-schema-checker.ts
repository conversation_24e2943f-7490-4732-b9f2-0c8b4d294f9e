import { supabase } from '@/integrations/supabase/client';

export class DatabaseSchemaChecker {
  /**
   * Check the current schema of project_assignments table
   */
  static async checkProjectAssignmentsSchema() {
    try {
      console.log('🔍 Checking project_assignments table schema...');
      
      // Try to get a sample record to see the structure
      const { data: sampleData, error: sampleError } = await supabase
        .from('project_assignments')
        .select('*')
        .limit(1);
      
      if (sampleError) {
        console.error('❌ Error querying project_assignments:', sampleError);
        return { hasProjectId: false, hasProjectName: false, error: sampleError };
      }
      
      const columns = sampleData && sampleData.length > 0 ? Object.keys(sampleData[0]) : [];
      console.log('📋 Available columns:', columns);
      
      const hasProjectId = columns.includes('project_id');
      const hasProjectName = columns.includes('project_name');
      const hasRole = columns.includes('role');
      const hasHoursAllocated = columns.includes('hours_allocated');
      const hasHoursWorked = columns.includes('hours_worked');
      
      console.log('🔍 Schema analysis:');
      console.log('  - project_id:', hasProjectId ? '✅' : '❌');
      console.log('  - project_name:', hasProjectName ? '✅' : '❌');
      console.log('  - role:', hasRole ? '✅' : '❌');
      console.log('  - hours_allocated:', hasHoursAllocated ? '✅' : '❌');
      console.log('  - hours_worked:', hasHoursWorked ? '✅' : '❌');
      
      return {
        hasProjectId,
        hasProjectName,
        hasRole,
        hasHoursAllocated,
        hasHoursWorked,
        columns,
        sampleData: sampleData?.[0] || null
      };
      
    } catch (error) {
      console.error('💥 Failed to check schema:', error);
      return { hasProjectId: false, hasProjectName: false, error };
    }
  }
  
  /**
   * Test project assignment creation with different schemas
   */
  static async testProjectAssignmentCreation(projectId: string, staffId: string) {
    try {
      console.log('🧪 Testing project assignment creation...');
      
      // First check what projects exist
      const { data: projects } = await supabase
        .from('projects')
        .select('id, name')
        .limit(5);
      
      console.log('📋 Available projects:', projects);
      
      if (!projects || projects.length === 0) {
        console.log('⚠️ No projects found. Creating a test project first...');
        
        const { data: newProject, error: projectError } = await supabase
          .from('projects')
          .insert({
            name: 'Test Project for Schema Check',
            description: 'Temporary project for testing schema',
            status: 'planning'
          })
          .select()
          .single();
        
        if (projectError) {
          console.error('❌ Failed to create test project:', projectError);
          return { success: false, error: projectError };
        }
        
        projectId = newProject.id;
        console.log('✅ Created test project:', newProject);
      } else {
        projectId = projectId || projects[0].id;
      }
      
      // Check what users exist
      const { data: users } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .limit(5);
      
      console.log('📋 Available users:', users);
      
      if (!users || users.length === 0) {
        console.log('❌ No users found. Cannot test assignment.');
        return { success: false, error: 'No users available' };
      }
      
      staffId = staffId || users[0].id;
      
      // Try new schema first
      console.log('🔄 Trying new schema (with project_id)...');
      const { data: newSchemaData, error: newSchemaError } = await supabase
        .from('project_assignments')
        .insert({
          project_id: projectId,
          assigned_to: staffId,
          status: 'assigned',
          role: 'team_member',
          progress_percentage: 0,
          hours_allocated: 40,
          hours_worked: 0,
          start_date: new Date().toISOString()
        })
        .select()
        .single();
      
      if (!newSchemaError) {
        console.log('✅ New schema works!', newSchemaData);
        
        // Clean up test record
        await supabase
          .from('project_assignments')
          .delete()
          .eq('id', newSchemaData.id);
        
        return { success: true, schema: 'new', data: newSchemaData };
      }
      
      console.log('❌ New schema failed:', newSchemaError);
      
      // Try old schema
      console.log('🔄 Trying old schema (with project_name)...');
      const project = projects?.find(p => p.id === projectId);
      
      const { data: oldSchemaData, error: oldSchemaError } = await supabase
        .from('project_assignments')
        .insert({
          project_name: project?.name || 'Test Project',
          assigned_to: staffId,
          status: 'pending',
          start_date: new Date().toISOString().split('T')[0]
        })
        .select()
        .single();
      
      if (!oldSchemaError) {
        console.log('✅ Old schema works!', oldSchemaData);
        
        // Clean up test record
        await supabase
          .from('project_assignments')
          .delete()
          .eq('id', oldSchemaData.id);
        
        return { success: true, schema: 'old', data: oldSchemaData };
      }
      
      console.log('❌ Old schema also failed:', oldSchemaError);
      
      return { 
        success: false, 
        newSchemaError, 
        oldSchemaError,
        message: 'Both schemas failed'
      };
      
    } catch (error) {
      console.error('💥 Test failed:', error);
      return { success: false, error };
    }
  }
  
  /**
   * Get detailed error information for debugging
   */
  static async getDetailedErrorInfo() {
    try {
      console.log('🔍 Getting detailed database information...');
      
      // Check if tables exist
      const tables = ['projects', 'project_assignments', 'profiles', 'departments'];
      
      for (const table of tables) {
        try {
          const { data, error } = await supabase
            .from(table)
            .select('*')
            .limit(1);
          
          if (error) {
            console.log(`❌ Table ${table}:`, error.message);
          } else {
            const columns = data && data.length > 0 ? Object.keys(data[0]) : [];
            console.log(`✅ Table ${table}: ${columns.length} columns -`, columns.slice(0, 5).join(', '), columns.length > 5 ? '...' : '');
          }
        } catch (err) {
          console.log(`💥 Table ${table}: Failed to query`);
        }
      }
      
      return { success: true };
      
    } catch (error) {
      console.error('💥 Failed to get database info:', error);
      return { success: false, error };
    }
  }
}

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  (window as any).checkDatabaseSchema = DatabaseSchemaChecker.checkProjectAssignmentsSchema;
  (window as any).testProjectAssignment = DatabaseSchemaChecker.testProjectAssignmentCreation;
  (window as any).getDatabaseInfo = DatabaseSchemaChecker.getDetailedErrorInfo;
  
  console.log('🛠️ Database schema checker loaded. Available commands:');
  console.log('  - checkDatabaseSchema()');
  console.log('  - testProjectAssignment(projectId?, staffId?)');
  console.log('  - getDatabaseInfo()');
}
