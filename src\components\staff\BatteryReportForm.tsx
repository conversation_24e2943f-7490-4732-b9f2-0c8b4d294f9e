import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Battery, 
  Save, 
  Camera, 
  MapPin, 
  Thermometer,
  Zap,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';

interface BatteryReportFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface BatteryReportData {
  battery_id: string;
  battery_serial_number: string;
  site_name: string;
  site_id: string;
  health_status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  battery_voltage: number;
  current_capacity: number;
  charging_status: 'fully_charged' | 'charging' | 'discharging' | 'fault';
  temperature: number;
  load_current: number;
  backup_time_remaining: number;
  runtime_hours: number;
  maintenance_required: boolean;
  next_maintenance_date: string;
  customer_name: string;
  amount_naira: number;
  maintenance_notes: string;
  issues_reported: string;
  recommendations: string;
  report_date: string;
}

export const BatteryReportForm = ({ onSuccess, onCancel }: BatteryReportFormProps) => {
  const { userProfile } = useSupabaseAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState<BatteryReportData>({
    battery_id: '',
    battery_serial_number: '',
    site_name: '',
    site_id: '',
    health_status: 'good',
    battery_voltage: 48.0,
    current_capacity: 100,
    charging_status: 'fully_charged',
    temperature: 25.0,
    load_current: 0,
    backup_time_remaining: 8,
    runtime_hours: 0,
    maintenance_required: false,
    next_maintenance_date: '',
    customer_name: '',
    amount_naira: 0,
    maintenance_notes: '',
    issues_reported: '',
    recommendations: '',
    report_date: new Date().toISOString().split('T')[0]
  });

  const createReportMutation = useMutation({
    mutationFn: async (reportData: BatteryReportData) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('battery_reports')
        .insert({
          ...reportData,
          reporter_id: userProfile.id,
          photos: [] // Will be implemented later for photo uploads
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['battery-reports'] });
      toast({
        title: 'Success',
        description: 'Battery report created successfully',
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create battery report',
        variant: 'destructive',
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.battery_id.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Battery ID is required',
        variant: 'destructive',
      });
      return;
    }

    if (!formData.site_name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Site name is required',
        variant: 'destructive',
      });
      return;
    }

    createReportMutation.mutate(formData);
  };

  const updateField = (field: keyof BatteryReportData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'fair': return 'bg-yellow-100 text-yellow-800';
      case 'poor': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <CheckCircle className="h-4 w-4" />;
      case 'good': return <CheckCircle className="h-4 w-4" />;
      case 'fair': return <AlertTriangle className="h-4 w-4" />;
      case 'poor': return <AlertTriangle className="h-4 w-4" />;
      case 'critical': return <XCircle className="h-4 w-4" />;
      default: return <Battery className="h-4 w-4" />;
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Battery className="h-5 w-5 text-blue-500" />
            New Battery Report
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="battery_id">Battery ID *</Label>
              <Input
                id="battery_id"
                value={formData.battery_id}
                onChange={(e) => updateField('battery_id', e.target.value)}
                placeholder="e.g., BATT-001"
                required
              />
            </div>
            <div>
              <Label htmlFor="battery_serial_number">Serial Number</Label>
              <Input
                id="battery_serial_number"
                value={formData.battery_serial_number}
                onChange={(e) => updateField('battery_serial_number', e.target.value)}
                placeholder="e.g., SN-12345-001"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="site_name">Site Name *</Label>
              <Input
                id="site_name"
                value={formData.site_name}
                onChange={(e) => updateField('site_name', e.target.value)}
                placeholder="e.g., Lagos Central Tower"
                required
              />
            </div>
            <div>
              <Label htmlFor="site_id">Site ID</Label>
              <Input
                id="site_id"
                value={formData.site_id}
                onChange={(e) => updateField('site_id', e.target.value)}
                placeholder="e.g., SITE-001"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="customer_name">Customer Name</Label>
            <Input
              id="customer_name"
              value={formData.customer_name}
              onChange={(e) => updateField('customer_name', e.target.value)}
              placeholder="e.g., MTN Nigeria"
            />
          </div>

          <div>
            <Label htmlFor="report_date">Report Date</Label>
            <Input
              id="report_date"
              type="date"
              value={formData.report_date}
              onChange={(e) => updateField('report_date', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Battery Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-500" />
            Battery Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="health_status">Health Status</Label>
              <Select
                value={formData.health_status}
                onValueChange={(value: any) => updateField('health_status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="excellent">
                    <span className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Excellent
                    </span>
                  </SelectItem>
                  <SelectItem value="good">
                    <span className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-blue-500" />
                      Good
                    </span>
                  </SelectItem>
                  <SelectItem value="fair">
                    <span className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      Fair
                    </span>
                  </SelectItem>
                  <SelectItem value="poor">
                    <span className="flex items-center gap-2">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      Poor
                    </span>
                  </SelectItem>
                  <SelectItem value="critical">
                    <span className="flex items-center gap-2">
                      <XCircle className="h-4 w-4 text-red-500" />
                      Critical
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="charging_status">Charging Status</Label>
              <Select
                value={formData.charging_status}
                onValueChange={(value: any) => updateField('charging_status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fully_charged">Fully Charged</SelectItem>
                  <SelectItem value="charging">Charging</SelectItem>
                  <SelectItem value="discharging">Discharging</SelectItem>
                  <SelectItem value="fault">Fault</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="battery_voltage">Battery Voltage (V)</Label>
              <Input
                id="battery_voltage"
                type="number"
                step="0.1"
                value={formData.battery_voltage}
                onChange={(e) => updateField('battery_voltage', parseFloat(e.target.value) || 0)}
              />
            </div>
            <div>
              <Label htmlFor="current_capacity">Current Capacity (%)</Label>
              <Input
                id="current_capacity"
                type="number"
                min="0"
                max="100"
                value={formData.current_capacity}
                onChange={(e) => updateField('current_capacity', parseInt(e.target.value) || 0)}
              />
            </div>
            <div>
              <Label htmlFor="temperature">Temperature (°C)</Label>
              <Input
                id="temperature"
                type="number"
                step="0.1"
                value={formData.temperature}
                onChange={(e) => updateField('temperature', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="load_current">Load Current (A)</Label>
              <Input
                id="load_current"
                type="number"
                step="0.1"
                value={formData.load_current}
                onChange={(e) => updateField('load_current', parseFloat(e.target.value) || 0)}
              />
            </div>
            <div>
              <Label htmlFor="backup_time_remaining">Backup Time (hours)</Label>
              <Input
                id="backup_time_remaining"
                type="number"
                step="0.1"
                value={formData.backup_time_remaining}
                onChange={(e) => updateField('backup_time_remaining', parseFloat(e.target.value) || 0)}
              />
            </div>
            <div>
              <Label htmlFor="runtime_hours">Runtime Hours</Label>
              <Input
                id="runtime_hours"
                type="number"
                value={formData.runtime_hours}
                onChange={(e) => updateField('runtime_hours', parseInt(e.target.value) || 0)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Maintenance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-purple-500" />
            Maintenance Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="maintenance_required"
              checked={formData.maintenance_required}
              onCheckedChange={(checked) => updateField('maintenance_required', checked)}
            />
            <Label htmlFor="maintenance_required">Maintenance Required</Label>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="next_maintenance_date">Next Maintenance Date</Label>
              <Input
                id="next_maintenance_date"
                type="date"
                value={formData.next_maintenance_date}
                onChange={(e) => updateField('next_maintenance_date', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="amount_naira">Service Amount (₦)</Label>
              <Input
                id="amount_naira"
                type="number"
                value={formData.amount_naira}
                onChange={(e) => updateField('amount_naira', parseInt(e.target.value) || 0)}
                placeholder="0"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="maintenance_notes">Maintenance Notes</Label>
            <Textarea
              id="maintenance_notes"
              value={formData.maintenance_notes}
              onChange={(e) => updateField('maintenance_notes', e.target.value)}
              placeholder="Enter maintenance observations and notes..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="issues_reported">Issues Reported</Label>
            <Textarea
              id="issues_reported"
              value={formData.issues_reported}
              onChange={(e) => updateField('issues_reported', e.target.value)}
              placeholder="Describe any issues or problems found..."
              rows={3}
            />
          </div>

          <div>
            <Label htmlFor="recommendations">Recommendations</Label>
            <Textarea
              id="recommendations"
              value={formData.recommendations}
              onChange={(e) => updateField('recommendations', e.target.value)}
              placeholder="Enter recommendations for future maintenance or actions..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Status Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Report Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Badge className={`${getHealthStatusColor(formData.health_status)} flex items-center gap-1`}>
              {getHealthStatusIcon(formData.health_status)}
              {formData.health_status.charAt(0).toUpperCase() + formData.health_status.slice(1)}
            </Badge>
            <span className="text-sm text-muted-foreground">
              {formData.current_capacity}% capacity • {formData.battery_voltage}V • {formData.temperature}°C
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={createReportMutation.isPending}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          {createReportMutation.isPending ? 'Creating...' : 'Create Report'}
        </Button>
      </div>
    </form>
  );
};
