
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Send } from "lucide-react";
import { useStaffReports } from "@/hooks/useStaffReports";

export const ProjectReport = () => {
  const [projectTitle, setProjectTitle] = useState("");
  const [completionStatus, setCompletionStatus] = useState("");
  const [reportDetails, setReportDetails] = useState("");
  
  const { createReport } = useStaffReports();

  const handleSubmitReport = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!projectTitle || !completionStatus || !reportDetails) {
      return;
    }

    const metadata = {
      project_title: projectTitle,
      completion_status: completionStatus,
      report_type: 'project'
    };

    createReport.mutate({
      report_type: 'project',
      title: `Project Report - ${projectTitle}`,
      description: reportDetails,
      metadata
    });

    // Reset form
    setProjectTitle("");
    setCompletionStatus("");
    setReportDetails("");
  };

  return (
    <Card className="bg-black/10 dark:bg-white/5 backdrop-blur-lg border-none">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg font-medium">
          <FileText className="h-5 w-5 text-primary" />
          Project Report
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmitReport} className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <label className="text-sm font-medium">Project Title</label>
              <Input
                type="text"
                value={projectTitle}
                onChange={(e) => setProjectTitle(e.target.value)}
                className="bg-white/5"
                placeholder="Enter project title"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Completion Status</label>
              <Select value={completionStatus} onValueChange={setCompletionStatus}>
                <SelectTrigger className="bg-white/5">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="in-progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="delayed">Delayed</SelectItem>
                  <SelectItem value="on-hold">On Hold</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm font-medium">Report Details</label>
            <Textarea
              value={reportDetails}
              onChange={(e) => setReportDetails(e.target.value)}
              className="bg-white/5 min-h-[100px]"
              placeholder="Enter detailed report..."
              required
            />
          </div>
          
          <Button 
            type="submit" 
            className="w-full md:w-auto"
            disabled={createReport.isPending}
          >
            <Send className="h-4 w-4 mr-2" />
            {createReport.isPending ? "Submitting..." : "Submit Report"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
