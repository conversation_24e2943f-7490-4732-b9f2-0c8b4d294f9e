import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapPin, Users, Settings, Plus, Edit, Trash2, RefreshCw, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface TelecomSite {
  id: string;
  name: string;
  location: string;
  manager_id: string | null;
  manager?: {
    id: string;
    full_name: string | null;
    email: string | null;
  } | null;
  clients_count: number;
  status: "active" | "maintenance" | "inactive";
  performance_score: number;
  created_at: string;
  updated_at: string;
}

interface NewSiteForm {
  name: string;
  location: string;
  manager_id: string;
  status: "active" | "maintenance" | "inactive";
  performance_score: number;
}

export const TelecomSites = () => {
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const [sites, setSites] = useState<TelecomSite[]>([]);
  const [managers, setManagers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSite, setSelectedSite] = useState<TelecomSite | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState<NewSiteForm>({
    name: '',
    location: '',
    manager_id: '',
    status: 'active',
    performance_score: 85
  });

  useEffect(() => {
    loadSites();
    loadManagers();
  }, []);

  const loadSites = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('telecom_sites')
        .select(`
          *,
          manager:profiles!telecom_sites_manager_id_fkey(id, full_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform data to match our interface
      const transformedSites = (data || []).map((site: any) => ({
        id: site.id,
        name: site.name,
        location: site.location || site.address || '',
        manager_id: site.manager_id,
        manager: Array.isArray(site.manager) ? site.manager[0] : site.manager,
        clients_count: site.clients_count || (site.client_list ? site.client_list.length : 0),
        status: site.status || 'active',
        performance_score: site.performance_score || site.performance || 85,
        created_at: site.created_at,
        updated_at: site.updated_at
      }));

      setSites(transformedSites);
    } catch (error: any) {
      console.error('Error loading sites:', error);
      toast({
        title: "Error Loading Sites",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadManagers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .in('role', ['admin', 'manager'])
        .order('full_name');

      if (error) throw error;

      setManagers(data || []);
    } catch (error: any) {
      console.error('Error loading managers:', error);
    }
  };

  const handleCreateSite = async () => {
    try {
      const { data, error } = await supabase
        .from('telecom_sites')
        .insert([{
          name: formData.name,
          location: formData.location,
          manager_id: formData.manager_id || null,
          status: formData.status,
          performance_score: formData.performance_score,
          clients_count: 0
        }])
        .select()
        .single();

      if (error) throw error;

      await loadSites();
      setDialogOpen(false);
      resetForm();
      
      toast({
        title: "Site Created",
        description: `${formData.name} has been created successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Error Creating Site",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleUpdateSite = async () => {
    if (!selectedSite) return;

    try {
      const { error } = await supabase
        .from('telecom_sites')
        .update({
          name: formData.name,
          location: formData.location,
          manager_id: formData.manager_id || null,
          status: formData.status,
          performance_score: formData.performance_score,
          updated_at: new Date().toISOString()
        })
        .eq('id', selectedSite.id);

      if (error) throw error;

      await loadSites();
      setDialogOpen(false);
      setIsEditing(false);
      resetForm();
      
      toast({
        title: "Site Updated",
        description: `${formData.name} has been updated successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Error Updating Site",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const handleDeleteSite = async (siteId: string, siteName: string) => {
    try {
      const { error } = await supabase
        .from('telecom_sites')
        .delete()
        .eq('id', siteId);

      if (error) throw error;

      await loadSites();
      
      toast({
        title: "Site Deleted",
        description: `${siteName} has been deleted`,
      });
    } catch (error: any) {
      toast({
        title: "Error Deleting Site",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      location: '',
      manager_id: '',
      status: 'active',
      performance_score: 85
    });
    setSelectedSite(null);
    setIsEditing(false);
  };

  const openEditDialog = (site: TelecomSite) => {
    setSelectedSite(site);
    setFormData({
      name: site.name,
      location: site.location || '',
      manager_id: site.manager_id || '',
      status: site.status,
      performance_score: site.performance_score || 85
    });
    setIsEditing(true);
    setDialogOpen(true);
  };

  const openCreateDialog = () => {
    resetForm();
    setDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case 'maintenance':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Maintenance</Badge>;
      case 'inactive':
        return <Badge variant="destructive">Inactive</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MapPin className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Telecom Sites Management</h2>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadSites} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Add Site
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>
                  {isEditing ? 'Edit Site' : 'Create New Site'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Site Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    placeholder="Enter site name"
                  />
                </div>
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData({...formData, location: e.target.value})}
                    placeholder="Enter location"
                  />
                </div>
                <div>
                  <Label htmlFor="manager">Manager</Label>
                  <Select value={formData.manager_id} onValueChange={(value) => setFormData({...formData, manager_id: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select manager" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="no-manager">No Manager</SelectItem>
                      {managers.map((manager) => (
                        <SelectItem key={manager.id} value={manager.id}>
                          {manager.full_name || manager.email}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select value={formData.status} onValueChange={(value: any) => setFormData({...formData, status: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="performance">Performance Score</Label>
                  <Input
                    id="performance"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.performance_score}
                    onChange={(e) => setFormData({...formData, performance_score: Number(e.target.value)})}
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button onClick={isEditing ? handleUpdateSite : handleCreateSite} className="flex-1">
                    {isEditing ? 'Update Site' : 'Create Site'}
                  </Button>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Sites Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            All Sites ({sites.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading sites...</span>
            </div>
          ) : sites.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No telecom sites found</p>
              <Button onClick={openCreateDialog} className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Create First Site
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Site Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Manager</TableHead>
                    <TableHead>Clients</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Performance</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sites.map((site) => (
                    <TableRow key={site.id} className="hover:bg-muted/50">
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-blue-500" />
                          <span className="font-medium">{site.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">{site.location}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">
                            {site.manager 
                              ? (site.manager.full_name || site.manager.email || 'Unknown Manager')
                              : 'Unassigned'
                            }
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{site.clients_count || 0}</Badge>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(site.status)}
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${getPerformanceColor(site.performance_score || 0)}`}>
                          {site.performance_score ? `${site.performance_score}%` : 'N/A'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(site)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSite(site.id, site.name)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Site Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <MapPin className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sites</p>
                <p className="text-2xl font-bold">{sites.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <Settings className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Active Sites</p>
                <p className="text-2xl font-bold">
                  {sites.filter(s => s.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Maintenance</p>
                <p className="text-2xl font-bold">
                  {sites.filter(s => s.status === 'maintenance').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Clients</p>
                <p className="text-2xl font-bold">
                  {sites.reduce((sum, s) => sum + (s.clients_count || 0), 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};