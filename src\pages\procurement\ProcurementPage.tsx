import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Package, 
  Plus, 
  CheckCircle, 
  Clock, 
  DollarSign,
  FileText,
  Download,
  Users,
  TrendingUp
} from 'lucide-react';
import { ProcurementDashboard } from '@/components/procurement/ProcurementDashboard';
import { ManagerApprovalInterface } from '@/components/procurement/ManagerApprovalInterface';
import { AdminProcessingInterface } from '@/components/procurement/AdminProcessingInterface';
import { ProcurementExportDialog } from '@/components/procurement/ProcurementExportDialog';
import { ProcurementTest } from '@/components/procurement/ProcurementTest';
import { useProcurementDashboard } from '@/hooks/useProcurement';
import { useAuth } from '@/components/auth/AuthProvider';

const ProcurementPage: React.FC = () => {
  const { userProfile } = useAuth();
  const { summary, loading } = useProcurementDashboard();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Determine which tabs to show based on user role
  const getAvailableTabs = () => {
    const tabs = [];
    
    // All users can see the main dashboard
    tabs.push({
      value: 'dashboard',
      label: 'Dashboard',
      icon: Package,
      component: <ProcurementDashboard />
    });

    // Add test tab for development
    tabs.push({
      value: 'test',
      label: 'Test',
      icon: CheckCircle,
      component: <ProcurementTest />
    });

    // Managers can see approval interface
    if (userProfile?.role === 'manager') {
      tabs.push({
        value: 'approvals',
        label: 'Approvals',
        icon: CheckCircle,
        component: <ManagerApprovalInterface />
      });
    }

    // Admin and accountants can see processing interface
    if (userProfile?.role === 'admin' || userProfile?.role === 'staff-admin' || userProfile?.role === 'accountant') {
      tabs.push({
        value: 'processing',
        label: userProfile?.role === 'accountant' ? 'Accounting' : 'Processing',
        icon: FileText,
        component: <AdminProcessingInterface />
      });
    }

    return tabs;
  };

  const availableTabs = getAvailableTabs();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading procurement system...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Procurement Management</h1>
          <p className="text-muted-foreground">
            Complete procurement workflow from request to expense tracking
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <ProcurementExportDialog 
            trigger={
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            }
          />
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
                <p className="text-2xl font-bold">{summary.total}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  All time
                </p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{summary.pending}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Awaiting action
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{summary.procured}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Fully procured
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">{formatCurrency(summary.totalValue)}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Estimated cost
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Workflow Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Procurement Workflow
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              Staff Request
            </Badge>
            <span className="text-muted-foreground">→</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              Manager Approval
            </Badge>
            <span className="text-muted-foreground">→</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              Admin Processing
            </Badge>
            <span className="text-muted-foreground">→</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              Accountant Invoice
            </Badge>
            <span className="text-muted-foreground">→</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Procured & Expensed
            </Badge>
          </div>
          
          <div className="mt-4 text-sm text-muted-foreground">
            <p>
              <strong>Complete Integration:</strong> Procured items are automatically added to the expenses table 
              for seamless financial tracking and reporting.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Card>
        <CardContent className="p-0">
          <Tabs defaultValue={availableTabs[0]?.value} className="w-full">
            <div className="border-b">
              <TabsList className="grid w-full grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 h-auto p-1">
                {availableTabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <TabsTrigger 
                      key={tab.value} 
                      value={tab.value}
                      className="flex items-center gap-2 py-3"
                    >
                      <Icon className="h-4 w-4" />
                      {tab.label}
                    </TabsTrigger>
                  );
                })}
              </TabsList>
            </div>
            
            {availableTabs.map((tab) => (
              <TabsContent key={tab.value} value={tab.value} className="p-6">
                {tab.component}
              </TabsContent>
            ))}
          </Tabs>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Role-Based Access
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium text-blue-600 mb-2">Staff</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Submit procurement requests</li>
                <li>• Track request status</li>
                <li>• View own requests</li>
              </ul>
            </div>
            
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium text-green-600 mb-2">Manager</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Review and approve requests</li>
                <li>• Set approval conditions</li>
                <li>• Manage team requests</li>
              </ul>
            </div>
            
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium text-purple-600 mb-2">Admin</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Process approved requests</li>
                <li>• Coordinate with vendors</li>
                <li>• Route to accounting</li>
              </ul>
            </div>
            
            <div className="p-3 border rounded-lg">
              <h4 className="font-medium text-orange-600 mb-2">Accountant</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Generate invoices</li>
                <li>• Process payments</li>
                <li>• Track expenses</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProcurementPage;
