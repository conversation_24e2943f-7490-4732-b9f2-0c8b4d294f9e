
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Users, Building, UserCheck } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";

interface UserProfile {
  id: string;
  full_name: string | null;
  email: string | null;
  role: string | null;
  department_id: string | null;
  avatar_url: string | null;
  department?: {
    name: string;
  };
}

interface Department {
  id: string;
  name: string;
  description: string | null;
}

export const UserDepartmentAssignment = () => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [bulkRole, setBulkRole] = useState<string>('');
  const [bulkDepartment, setBulkDepartment] = useState<string>('');
  const { toast } = useToast();

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          email,
          role,
          department_id,
          avatar_url,
          department:department_id (
            name
          )
        `)
        .order('full_name');

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    }
  };

  const fetchDepartments = async () => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name, description')
        .order('name');

      if (error) throw error;
      setDepartments(data || []);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      await Promise.all([fetchUsers(), fetchDepartments()]);
      setLoading(false);
    };
    loadData();
  }, []);

  // Function to update department counts
  const updateDepartmentCounts = async (oldDeptId: string | null, newDeptId: string | null) => {
    try {
      const departmentsToUpdate = [];

      // Add old department to update list if it exists
      if (oldDeptId) departmentsToUpdate.push(oldDeptId);

      // Add new department to update list if it exists and different from old
      if (newDeptId && newDeptId !== oldDeptId) departmentsToUpdate.push(newDeptId);

      // Update counts for affected departments
      for (const deptId of departmentsToUpdate) {
        const { count } = await supabase
          .from('profiles')
          .select('id', { count: 'exact' })
          .eq('department_id', deptId);

        await supabase
          .from('departments')
          .update({ employee_count: count || 0 })
          .eq('id', deptId);
      }
    } catch (error) {
      console.error('Error updating department counts:', error);
    }
  };

  const updateUserAssignment = async (userId: string, role?: string, departmentId?: string) => {
    try {
      // Get current user data to track department changes
      const { data: currentUser } = await supabase
        .from('profiles')
        .select('department_id')
        .eq('id', userId)
        .single();

      const updates: any = {};
      if (role !== undefined) updates.role = role;
      if (departmentId !== undefined) updates.department_id = departmentId === 'none' ? null : departmentId;

      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      // Update department counts if department changed
      if (departmentId !== undefined && currentUser) {
        await updateDepartmentCounts(currentUser.department_id, departmentId === 'none' ? null : departmentId);
      }

      // Log the activity
      await supabase.from('system_activities').insert({
        type: 'user_management',
        action: 'update_user_assignment',
        description: `Updated user assignment: ${role ? `role to ${role}` : ''} ${departmentId ? `department assignment` : ''}`,
        user_id: userId,
        metadata: { updates }
      });

      await fetchUsers();
      toast({
        title: "Success",
        description: "User assignment updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating user assignment:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update user assignment",
        variant: "destructive",
      });
    }
  };

  const getRoleColor = (role: string | null) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'manager':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'staff':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'accountant':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'staff-admin':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleBulkAssignment = async () => {
    if (selectedUsers.length === 0) {
      toast({
        title: "Warning",
        description: "Please select users to update",
        variant: "destructive",
      });
      return;
    }

    if (!bulkRole || bulkRole === 'no-change') {
      if (!bulkDepartment || bulkDepartment === 'no-change') {
        toast({
          title: "Warning",
          description: "Please select a role or department to assign",
          variant: "destructive",
        });
        return;
      }
    }

    try {
      const updates: any = {};
      if (bulkRole && bulkRole !== 'no-change') updates.role = bulkRole;
      if (bulkDepartment && bulkDepartment !== 'no-change') {
        if (bulkDepartment === 'none') {
          updates.department_id = null;
        } else {
          updates.department_id = bulkDepartment;
        }
      }

      // Track departments that need count updates
      const affectedDepartments = new Set<string>();

      for (const userId of selectedUsers) {
        // Get current user data if department is being changed
        if (bulkDepartment && bulkDepartment !== 'no-change') {
          const { data: currentUser } = await supabase
            .from('profiles')
            .select('department_id')
            .eq('id', userId)
            .single();

          if (currentUser?.department_id) {
            affectedDepartments.add(currentUser.department_id);
          }

          if (bulkDepartment !== 'none') {
            affectedDepartments.add(bulkDepartment);
          }
        }

        await supabase
          .from('profiles')
          .update({
            ...updates,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);
      }

      // Update counts for all affected departments
      for (const deptId of affectedDepartments) {
        const { count } = await supabase
          .from('profiles')
          .select('id', { count: 'exact' })
          .eq('department_id', deptId);

        await supabase
          .from('departments')
          .update({ employee_count: count || 0 })
          .eq('id', deptId);
      }

      // Log bulk activity
      await supabase.from('system_activities').insert({
        type: 'user_management',
        action: 'bulk_user_assignment',
        description: `Bulk updated ${selectedUsers.length} users: ${bulkRole ? `role to ${bulkRole}` : ''} ${bulkDepartment ? `department assignment` : ''}`,
        metadata: { userCount: selectedUsers.length, updates }
      });

      await fetchUsers();
      setSelectedUsers([]);
      setBulkRole('');
      setBulkDepartment('');

      toast({
        title: "Success",
        description: `Updated ${selectedUsers.length} users successfully`,
      });
    } catch (error: any) {
      console.error('Error in bulk assignment:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update users",
        variant: "destructive",
      });
    }
  };

  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const selectAllUsers = () => {
    setSelectedUsers(users.map(user => user.id));
  };

  const clearSelection = () => {
    setSelectedUsers([]);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>User Department & Role Assignment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            Loading user assignments...
          </div>
        </CardContent>
      </Card>
    );
  }

  const unassignedUsers = users.filter(user => !user.department_id);
  const assignedUsers = users.filter(user => user.department_id);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="modern-3d-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">Total Users</p>
              <div className="p-2 rounded-xl bg-primary/10">
                <Users className="h-4 w-4 text-primary" />
              </div>
            </div>
            <p className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">{users.length}</p>
          </CardContent>
        </Card>
        <Card className="modern-3d-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">Assigned Users</p>
              <div className="p-2 rounded-xl bg-green-100">
                <UserCheck className="h-4 w-4 text-green-600" />
              </div>
            </div>
            <p className="text-2xl font-bold bg-gradient-to-r from-green-600 to-green-400 bg-clip-text text-transparent">{assignedUsers.length}</p>
          </CardContent>
        </Card>
        <Card className="modern-3d-card">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">Unassigned Users</p>
              <div className="p-2 rounded-xl bg-orange-100">
                <Building className="h-4 w-4 text-orange-600" />
              </div>
            </div>
            <p className="text-2xl font-bold bg-gradient-to-r from-orange-600 to-orange-400 bg-clip-text text-transparent">{unassignedUsers.length}</p>
          </CardContent>
        </Card>
      </div>

      {/* User Assignment Table */}
      <Card className="modern-3d-card">
        <CardHeader>
          <CardTitle>User Department & Role Assignment</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Bulk Assignment Section */}
          <div className="mb-6 p-4 border rounded-lg bg-muted/50">
            <h3 className="text-lg font-semibold mb-4">Bulk Assignment</h3>
            <div className="flex flex-wrap gap-4 items-end">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllUsers}
                  disabled={users.length === 0}
                >
                  Select All ({users.length})
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSelection}
                  disabled={selectedUsers.length === 0}
                >
                  Clear ({selectedUsers.length})
                </Button>
              </div>

              <div className="flex gap-2">
                <Select value={bulkRole} onValueChange={setBulkRole}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-change">No Change</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="staff">Staff</SelectItem>
                    <SelectItem value="accountant">Accountant</SelectItem>
                    <SelectItem value="staff-admin">Staff Admin</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={bulkDepartment} onValueChange={setBulkDepartment}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-change">No Change</SelectItem>
                    <SelectItem value="none">No Department</SelectItem>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button
                  onClick={handleBulkAssignment}
                  disabled={selectedUsers.length === 0 || ((!bulkRole || bulkRole === 'no-change') && (!bulkDepartment || bulkDepartment === 'no-change'))}
                  className="bg-primary hover:bg-primary/90"
                >
                  Update Selected ({selectedUsers.length})
                </Button>
              </div>
            </div>
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === users.length && users.length > 0}
                    onChange={selectedUsers.length === users.length ? clearSelection : selectAllUsers}
                    className="rounded"
                  />
                </TableHead>
                <TableHead>User</TableHead>
                <TableHead>Current Role</TableHead>
                <TableHead>Current Department</TableHead>
                <TableHead>Assign Role</TableHead>
                <TableHead>Assign Department</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id} className={selectedUsers.includes(user.id) ? 'bg-muted/50' : ''}>
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => toggleUserSelection(user.id)}
                      className="rounded"
                    />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar_url || undefined} />
                        <AvatarFallback>
                          {user.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{user.full_name || 'No Name'}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getRoleColor(user.role)}>
                      {user.role || 'No Role'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.department?.name || (
                      <span className="text-muted-foreground">Unassigned</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Select
                      value={user.role || 'none'}
                      onValueChange={(role) => updateUserAssignment(user.id, role === 'none' ? null : role)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Role</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="staff">Staff</SelectItem>
                        <SelectItem value="accountant">Accountant</SelectItem>
                        <SelectItem value="staff-admin">Staff Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <Select
                      value={user.department_id || 'none'}
                      onValueChange={(departmentId) => 
                        updateUserAssignment(user.id, undefined, departmentId)
                      }
                    >
                      <SelectTrigger className="w-40">
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Department</SelectItem>
                        {departments.map((dept) => (
                          <SelectItem key={dept.id} value={dept.id}>
                            {dept.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
