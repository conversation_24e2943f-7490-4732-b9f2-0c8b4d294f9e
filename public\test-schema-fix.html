<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Schema Fix</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .success-box {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .test-result {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .log {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Schema Fix</h1>
            <p>Verify that database schema issues are resolved</p>
        </div>
        
        <div class="success-box">
            <h3>🎯 Testing Strategy</h3>
            <p>This test will verify that all the schema issues have been fixed and data can be loaded properly.</p>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="button" onclick="testSchemaFix()">
                🧪 Test Database Schema
            </button>
            <button class="button" onclick="testDashboardData()">
                📊 Test Dashboard Data
            </button>
            <a href="/dashboard/test" class="button">
                🚀 Test Dashboard
            </a>
        </div>
        
        <div id="testResults"></div>
        <div id="logContainer" style="display: none;">
            <h3>🔍 Test Log:</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const logContainer = document.getElementById('logContainer');
            logContainer.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        async function testSchemaFix() {
            log('🧪 Starting schema fix test...');
            
            try {
                // Test 1: Check if required columns exist
                log('📋 Testing table columns...');
                
                const tables = [
                    { name: 'invoices', columns: ['subtotal', 'balance_amount', 'total_amount'] },
                    { name: 'expense_reports', columns: ['expense_date'] },
                    { name: 'reports', columns: ['priority'] },
                    { name: 'time_logs', columns: ['description'] },
                    { name: 'notifications', columns: ['read', 'is_read'] }
                ];
                
                let allTestsPassed = true;
                let results = [];
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table.name)
                            .select('*')
                            .limit(1);
                        
                        if (error) {
                            log(`❌ ${table.name}: ${error.message}`);
                            results.push({ table: table.name, status: 'error', message: error.message });
                            allTestsPassed = false;
                        } else {
                            log(`✅ ${table.name}: Table accessible`);
                            results.push({ table: table.name, status: 'success', message: 'Table accessible' });
                        }
                    } catch (err) {
                        log(`❌ ${table.name}: ${err.message}`);
                        results.push({ table: table.name, status: 'error', message: err.message });
                        allTestsPassed = false;
                    }
                }
                
                // Test 2: Try to insert test data
                log('📊 Testing data insertion...');
                
                const { data: { user } } = await supabase.auth.getUser();
                const userId = user?.id || 'test-user';
                
                try {
                    // Test invoice insertion
                    const { error: invoiceError } = await supabase
                        .from('invoices')
                        .insert({
                            invoice_number: 'TEST-' + Date.now(),
                            subtotal: 100.00,
                            balance_amount: 100.00,
                            total_amount: 100.00,
                            payment_status: 'pending',
                            created_by: userId
                        });
                    
                    if (invoiceError) {
                        log(`❌ Invoice insertion: ${invoiceError.message}`);
                        allTestsPassed = false;
                    } else {
                        log('✅ Invoice insertion: Success');
                    }
                } catch (err) {
                    log(`❌ Invoice insertion: ${err.message}`);
                    allTestsPassed = false;
                }
                
                try {
                    // Test expense report insertion
                    const { error: expenseError } = await supabase
                        .from('expense_reports')
                        .insert({
                            title: 'Test Expense',
                            amount: 50.00,
                            category: 'test',
                            expense_date: new Date().toISOString().split('T')[0],
                            submitted_by: userId
                        });
                    
                    if (expenseError) {
                        log(`❌ Expense report insertion: ${expenseError.message}`);
                        allTestsPassed = false;
                    } else {
                        log('✅ Expense report insertion: Success');
                    }
                } catch (err) {
                    log(`❌ Expense report insertion: ${err.message}`);
                    allTestsPassed = false;
                }
                
                // Show results
                const resultsHtml = `
                    <div class="${allTestsPassed ? 'success-box' : 'test-result'}">
                        <h3>${allTestsPassed ? '✅ All Tests Passed!' : '⚠️ Some Tests Failed'}</h3>
                        <p><strong>Schema Fix Status:</strong> ${allTestsPassed ? 'Complete' : 'Needs Attention'}</p>
                        <ul>
                            ${results.map(r => `<li>${r.status === 'success' ? '✅' : '❌'} ${r.table}: ${r.message}</li>`).join('')}
                        </ul>
                        ${allTestsPassed ? 
                            '<p><strong>Your dashboard should now load data without schema errors!</strong></p>' :
                            '<p><strong>Please run the schema fix tool to resolve remaining issues.</strong></p>'
                        }
                    </div>
                `;
                
                document.getElementById('testResults').innerHTML = resultsHtml;
                
            } catch (error) {
                log(`❌ Test failed: ${error.message}`);
                document.getElementById('testResults').innerHTML = `
                    <div class="test-result">
                        <h3>❌ Test Failed</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testDashboardData() {
            log('📊 Testing dashboard data loading...');
            
            try {
                // Test basic data queries that the dashboard uses
                const tests = [
                    { name: 'Invoices', query: () => supabase.from('invoices').select('*').limit(5) },
                    { name: 'Expense Reports', query: () => supabase.from('expense_reports').select('*').limit(5) },
                    { name: 'Reports', query: () => supabase.from('reports').select('*').limit(5) },
                    { name: 'Time Logs', query: () => supabase.from('time_logs').select('*').limit(5) },
                    { name: 'Notifications', query: () => supabase.from('notifications').select('*').limit(5) }
                ];
                
                let results = [];
                
                for (const test of tests) {
                    try {
                        const { data, error } = await test.query();
                        
                        if (error) {
                            log(`❌ ${test.name}: ${error.message}`);
                            results.push({ name: test.name, status: 'error', message: error.message, count: 0 });
                        } else {
                            log(`✅ ${test.name}: ${data.length} records loaded`);
                            results.push({ name: test.name, status: 'success', message: 'Data loaded', count: data.length });
                        }
                    } catch (err) {
                        log(`❌ ${test.name}: ${err.message}`);
                        results.push({ name: test.name, status: 'error', message: err.message, count: 0 });
                    }
                }
                
                const successCount = results.filter(r => r.status === 'success').length;
                const totalCount = results.length;
                
                const resultsHtml = `
                    <div class="${successCount === totalCount ? 'success-box' : 'test-result'}">
                        <h3>📊 Dashboard Data Test Results</h3>
                        <p><strong>Success Rate:</strong> ${successCount}/${totalCount} (${Math.round((successCount/totalCount) * 100)}%)</p>
                        <ul>
                            ${results.map(r => `<li>${r.status === 'success' ? '✅' : '❌'} ${r.name}: ${r.message} ${r.status === 'success' ? `(${r.count} records)` : ''}</li>`).join('')}
                        </ul>
                        ${successCount === totalCount ? 
                            '<p><strong>All dashboard data sources are working!</strong></p>' :
                            '<p><strong>Some data sources need attention. Check the schema fix tool.</strong></p>'
                        }
                    </div>
                `;
                
                document.getElementById('testResults').innerHTML = resultsHtml;
                
            } catch (error) {
                log(`❌ Dashboard data test failed: ${error.message}`);
                document.getElementById('testResults').innerHTML = `
                    <div class="test-result">
                        <h3>❌ Dashboard Data Test Failed</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Make functions global
        window.testSchemaFix = testSchemaFix;
        window.testDashboardData = testDashboardData;
    </script>
</body>
</html>
