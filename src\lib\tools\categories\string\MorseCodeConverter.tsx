import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Radio, Play, ArrowUpDown, Volume2 } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const MorseCodeConverter: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [mode, setMode] = useState('encode');
  const [separator, setSeparator] = useState(' ');
  const [wordSeparator, setWordSeparator] = useState(' / ');
  const [includeSpaces, setIncludeSpaces] = useState(true);
  const [result, setResult] = useState('');
  const [isPlaying, setIsPlaying] = useState(false);

  // Morse code mapping
  const morseCode: { [key: string]: string } = {
    'A': '.-', 'B': '-...', 'C': '-.-.', 'D': '-..', 'E': '.', 'F': '..-.',
    'G': '--.', 'H': '....', 'I': '..', 'J': '.---', 'K': '-.-', 'L': '.-..',
    'M': '--', 'N': '-.', 'O': '---', 'P': '.--.', 'Q': '--.-', 'R': '.-.',
    'S': '...', 'T': '-', 'U': '..-', 'V': '...-', 'W': '.--', 'X': '-..-',
    'Y': '-.--', 'Z': '--..', '0': '-----', '1': '.----', '2': '..---',
    '3': '...--', '4': '....-', '5': '.....', '6': '-....', '7': '--...',
    '8': '---..', '9': '----.', '.': '.-.-.-', ',': '--..--', '?': '..--..',
    "'": '.----.', '!': '-.-.--', '/': '-..-.', '(': '-.--.', ')': '-.--.-',
    '&': '.-...', ':': '---...', ';': '-.-.-.', '=': '-...-', '+': '.-.-.',
    '-': '-....-', '_': '..--.-', '"': '.-..-.', '$': '...-..-', '@': '.--.-.'
  };

  // Reverse mapping for decoding
  const reverseMorseCode: { [key: string]: string } = {};
  Object.entries(morseCode).forEach(([char, morse]) => {
    reverseMorseCode[morse] = char;
  });

  const separatorOptions = [
    { value: ' ', label: 'Space' },
    { value: '  ', label: 'Double Space' },
    { value: ' | ', label: 'Pipe ( | )' },
    { value: '\t', label: 'Tab' },
    { value: '\n', label: 'New Line' },
    { value: 'custom', label: 'Custom' }
  ];

  const wordSeparatorOptions = [
    { value: ' / ', label: 'Slash ( / )' },
    { value: '   ', label: 'Triple Space' },
    { value: ' | ', label: 'Pipe ( | )' },
    { value: ' // ', label: 'Double Slash ( // )' },
    { value: '\n', label: 'New Line' },
    { value: 'custom', label: 'Custom' }
  ];

  const performConversion = () => {
    if (!inputText.trim()) {
      setResult('');
      return;
    }

    try {
      if (mode === 'encode') {
        // Text to Morse
        const words = inputText.toUpperCase().split(' ');
        const morseWords = words.map(word => {
          return word.split('').map(char => {
            if (morseCode[char]) {
              return morseCode[char];
            } else if (char === ' ') {
              return includeSpaces ? wordSeparator.trim() : '';
            } else {
              return `[${char}]`; // Unknown character
            }
          }).filter(morse => morse.length > 0).join(separator);
        });

        const encoded = includeSpaces 
          ? morseWords.join(wordSeparator)
          : morseWords.join(separator);
        
        setResult(encoded);
      } else {
        // Morse to Text
        let morseInput = inputText.trim();
        
        // Split by word separators first
        const morseWords = morseInput.split(new RegExp(wordSeparator.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'));
        
        const decodedWords = morseWords.map(morseWord => {
          // Split by character separators
          const morseChars = morseWord.trim().split(new RegExp(separator.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'));
          
          return morseChars.map(morseChar => {
            const trimmed = morseChar.trim();
            if (reverseMorseCode[trimmed]) {
              return reverseMorseCode[trimmed];
            } else if (trimmed.length > 0) {
              return `[${trimmed}]`; // Unknown morse code
            }
            return '';
          }).join('');
        });

        setResult(decodedWords.join(' '));
      }
    } catch (error) {
      toast.error('Error converting morse code');
      setResult('');
    }
  };

  useEffect(() => {
    performConversion();
  }, [inputText, mode, separator, wordSeparator, includeSpaces]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
  };

  const swapInputOutput = () => {
    if (result) {
      setInputText(result);
      setMode(mode === 'encode' ? 'decode' : 'encode');
    }
  };

  const loadSampleData = () => {
    const samples = {
      encode: [
        'HELLO WORLD',
        'SOS',
        'MORSE CODE',
        'THE QUICK BROWN FOX',
        'TESTING 123'
      ],
      decode: [
        '.... . .-.. .-.. --- / .-- --- .-. .-.. -..',
        '... --- ...',
        '-- --- .-. ... . / -.-. --- -.. .',
        '- .... . / --.- ..- .. -.-. -.- / -... .-. --- .-- -. / ..-. --- -..-',
        '- . ... - .. -. --. / .---- ..--- ...--'
      ]
    };

    const sampleArray = samples[mode as keyof typeof samples];
    const randomSample = sampleArray[Math.floor(Math.random() * sampleArray.length)];
    setInputText(randomSample);
  };

  // Simple audio playback simulation (visual feedback)
  const playMorse = async () => {
    if (!result || mode !== 'encode' || isPlaying) return;
    
    setIsPlaying(true);
    toast.success('Playing morse code (visual simulation)');
    
    // Simulate playing by highlighting characters
    // In a real implementation, you would use Web Audio API
    setTimeout(() => {
      setIsPlaying(false);
      toast.success('Playback complete');
    }, 2000);
  };

  const getStats = () => {
    const inputLength = inputText.length;
    const outputLength = result.length;
    const morseChars = mode === 'encode' 
      ? (result.match(/[.-]/g) || []).length
      : (inputText.match(/[.-]/g) || []).length;
    
    return {
      inputLength,
      outputLength,
      morseChars,
      operation: mode === 'encode' ? 'Text → Morse' : 'Morse → Text'
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-slate-50 via-gray-50 to-zinc-50 border-slate-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-slate-800">
            <div className="p-2 bg-slate-100 rounded-lg">
              <Radio className="h-6 w-6 text-slate-600" />
            </div>
            {title || 'Morse Code Converter'}
          </CardTitle>
          <p className="text-slate-600 text-sm">
            Convert text to Morse code and vice versa with audio playback support
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Mode Selection */}
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Conversion Mode:</label>
            <Select value={mode} onValueChange={setMode}>
              <SelectTrigger className="border-2 border-slate-200 focus:border-slate-400">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="encode">
                  <div className="flex items-center gap-2">
                    <span>📝</span>
                    <span>Text to Morse Code</span>
                  </div>
                </SelectItem>
                <SelectItem value="decode">
                  <div className="flex items-center gap-2">
                    <span>📻</span>
                    <span>Morse Code to Text</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Separator Options */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Character Separator:</label>
              <Select value={separator} onValueChange={setSeparator}>
                <SelectTrigger className="border-2 border-slate-200 focus:border-slate-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {separatorOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Word Separator:</label>
              <Select value={wordSeparator} onValueChange={setWordSeparator}>
                <SelectTrigger className="border-2 border-slate-200 focus:border-slate-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {wordSeparatorOptions.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center space-x-2">
            <Switch
              id="include-spaces"
              checked={includeSpaces}
              onCheckedChange={setIncludeSpaces}
            />
            <label htmlFor="include-spaces" className="text-sm font-medium">
              Include Word Separators
            </label>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span>📝</span>
                {mode === 'encode' ? 'Text to Convert:' : 'Morse Code to Decode:'}
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleData}
                className="flex items-center gap-2"
              >
                <Radio className="h-4 w-4" />
                Sample
              </Button>
            </div>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={mode === 'encode' 
                ? "Enter text to convert to morse code..." 
                : "Enter morse code to decode (use dots and dashes)..."
              }
              className="min-h-[120px] resize-none border-2 border-slate-200 focus:border-slate-400 font-mono"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.operation}
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                📏 {stats.inputLength} → {stats.outputLength} chars
              </Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                📻 {stats.morseChars} morse chars
              </Badge>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              onClick={swapInputOutput}
              disabled={!result}
              className="flex items-center gap-2"
            >
              <ArrowUpDown className="h-4 w-4" />
              Swap & Switch Mode
            </Button>
            {mode === 'encode' && result && (
              <Button
                variant="outline"
                onClick={playMorse}
                disabled={isPlaying}
                className="flex items-center gap-2"
              >
                <Volume2 className="h-4 w-4" />
                {isPlaying ? 'Playing...' : 'Play Audio'}
              </Button>
            )}
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* Result */}
          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  {mode === 'encode' ? 'Morse Code:' : 'Decoded Text:'}
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[120px] bg-gray-50 border-2 border-gray-200 font-mono text-lg tracking-wider"
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Morse Code Reference
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Letters A-I:</strong></p>
              <div className="font-mono text-xs space-y-1">
                <div>A: .-  B: -...  C: -.-.</div>
                <div>D: -..  E: .  F: ..-.</div>
                <div>G: --.  H: ....  I: ..</div>
              </div>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Letters J-R:</strong></p>
              <div className="font-mono text-xs space-y-1">
                <div>J: .---  K: -.-  L: .-..</div>
                <div>M: --  N: -.  O: ---</div>
                <div>P: .--.  Q: --.-  R: .-.</div>
              </div>
            </div>
            <div className="space-y-2">
              <p><strong className="text-purple-600">Letters S-Z:</strong></p>
              <div className="font-mono text-xs space-y-1">
                <div>S: ...  T: -  U: ..-</div>
                <div>V: ...-  W: .--  X: -..-</div>
                <div>Y: -.--  Z: --..</div>
              </div>
            </div>
          </div>
          <div className="grid md:grid-cols-2 gap-4 mt-4">
            <div className="space-y-2">
              <p><strong className="text-orange-600">Numbers:</strong></p>
              <div className="font-mono text-xs">
                1: .----  2: ..---  3: ...--  4: ....-  5: .....
                <br />
                6: -....  7: --...  8: ---..  9: ----.  0: -----
              </div>
            </div>
            <div className="space-y-2">
              <p><strong className="text-red-600">Common Punctuation:</strong></p>
              <div className="font-mono text-xs">
                .: .-.-.-  ,: --..--  ?: ..--..  !: -.-.--
                <br />
                /: -..-.  (: -.--.  ): -.--.-  &: .-...
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MorseCodeConverter;
