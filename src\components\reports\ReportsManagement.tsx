import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { SubmitReportForm } from './SubmitReportForm'
import { ReportsList } from './ReportsList'
import { useAuth } from '@/components/auth/AuthProvider'

export const ReportsManagement = () => {
  const { userProfile } = useAuth()
  const isAdminOrModerator = userProfile?.role === 'admin' || userProfile?.role === 'moderator'

  return (
    <div className='space-y-6'>
      <Tabs defaultValue='submit' className='w-full'>
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='submit'>Submit Report</TabsTrigger>
          <TabsTrigger value='view'>View Reports</TabsTrigger>
        </TabsList>

        <TabsContent value='submit' className='space-y-4'>
          <SubmitReportForm />
        </TabsContent>

        <TabsContent value='view' className='space-y-4'>
          <ReportsList />
        </TabsContent>
      </Tabs>
    </div>
  )
}
