import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface DocumentGenerationRequest {
  generation_id: string;
  config: {
    template_id: string;
    document_name: string;
    ai_enhancement: boolean;
    data_sources: string[];
    variables: Record<string, any>;
    chart_types: string[];
    include_analysis: boolean;
    output_format: string;
  };
  user_id: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    const { generation_id, config, user_id }: DocumentGenerationRequest = await req.json()

    console.log('Starting document generation:', { generation_id, config })

    // Update generation status to processing
    await supabaseClient
      .from('document_generations')
      .update({ 
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', generation_id)

    // Get template configuration
    const { data: template, error: templateError } = await supabaseClient
      .from('document_templates')
      .select('*')
      .eq('id', config.template_id)
      .single()

    if (templateError) {
      throw new Error(`Template not found: ${templateError.message}`)
    }

    // Fetch data from configured sources
    const dataResults = await fetchDataSources(supabaseClient, template.data_sources, config.variables)

    // Apply AI enhancements if enabled
    let aiEnhancements = null
    if (config.ai_enhancement) {
      aiEnhancements = await applyAIEnhancements(dataResults, template, config)
    }

    // Generate document based on format
    let documentResult
    switch (config.output_format) {
      case 'excel':
        documentResult = await generateExcelDocument(supabaseClient, template, dataResults, aiEnhancements, config)
        break
      case 'pdf':
        documentResult = await generatePDFDocument(supabaseClient, template, dataResults, aiEnhancements, config)
        break
      case 'dashboard':
        documentResult = await generateDashboard(supabaseClient, template, dataResults, aiEnhancements, config)
        break
      default:
        throw new Error(`Unsupported output format: ${config.output_format}`)
    }

    // Update generation record with success
    await supabaseClient
      .from('document_generations')
      .update({
        status: 'completed',
        file_path: documentResult.filePath,
        file_size: documentResult.fileSize,
        ai_enhancements: aiEnhancements,
        processing_time: documentResult.processingTime,
        updated_at: new Date().toISOString()
      })
      .eq('id', generation_id)

    // Log AI enhancement usage if applied
    if (aiEnhancements) {
      await logAIEnhancement(supabaseClient, generation_id, aiEnhancements)
    }

    return new Response(
      JSON.stringify({
        success: true,
        generation_id: generation_id,
        file_path: documentResult.filePath,
        ai_enhancements: aiEnhancements
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )

  } catch (error) {
    console.error('Document generation error:', error)

    // Update generation record with error if we have the ID
    const body = await req.json().catch(() => ({}))
    if (body.generation_id) {
      const supabaseClient = createClient(
        Deno.env.get('SUPABASE_URL') ?? '',
        Deno.env.get('SUPABASE_ANON_KEY') ?? ''
      )

      await supabaseClient
        .from('document_generations')
        .update({
          status: 'failed',
          error_details: error.message,
          updated_at: new Date().toISOString()
        })
        .eq('id', body.generation_id)
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      },
    )
  }
})

async function fetchDataSources(supabaseClient: any, dataSources: any[], variables: Record<string, any>) {
  const results = []

  for (const source of dataSources) {
    try {
      let data: any[] = []

      switch (source.type) {
        case 'database_table':
          data = await fetchDatabaseData(supabaseClient, source, variables)
          break
        case 'api_endpoint':
          data = await fetchAPIData(source, variables)
          break
        case 'real_time_query':
          data = await fetchRealTimeData(supabaseClient, source, variables)
          break
        default:
          console.warn(`Unsupported data source type: ${source.type}`)
      }

      results.push({
        source_name: source.name,
        data: data
      })
    } catch (error) {
      console.error(`Error fetching data from ${source.name}:`, error)
      results.push({
        source_name: source.name,
        data: [],
        error: error.message
      })
    }
  }

  return results
}

async function fetchDatabaseData(supabaseClient: any, source: any, variables: Record<string, any>) {
  // Replace variables in query
  let query = source.query
  Object.entries(variables).forEach(([key, value]) => {
    query = query.replace(new RegExp(`{{${key}}}`, 'g'), value)
  })

  // Execute query based on source configuration
  if (source.table_name) {
    const { data, error } = await supabaseClient
      .from(source.table_name)
      .select(source.columns || '*')

    if (error) throw error
    return data || []
  }

  return []
}

async function fetchAPIData(source: any, variables: Record<string, any>) {
  // Replace variables in URL
  let url = source.url
  Object.entries(variables).forEach(([key, value]) => {
    url = url.replace(new RegExp(`{{${key}}}`, 'g'), value)
  })

  const response = await fetch(url, {
    method: source.method || 'GET',
    headers: source.headers || {},
    body: source.body ? JSON.stringify(source.body) : undefined
  })

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`)
  }

  const data = await response.json()
  return Array.isArray(data) ? data : [data]
}

async function fetchRealTimeData(supabaseClient: any, source: any, variables: Record<string, any>) {
  // Implement real-time data fetching
  return []
}

async function applyAIEnhancements(data: any[], template: any, config: any) {
  try {
    // Call OpenAI API for AI enhancements
    const openaiApiKey = Deno.env.get('OPENAI_API_KEY')
    if (!openaiApiKey) {
      console.warn('OpenAI API key not configured')
      return null
    }

    const prompt = `
    Analyze the following data and provide insights for a ${config.output_format} document:
    
    Data: ${JSON.stringify(data.slice(0, 100))} // Limit data size for prompt
    
    Template: ${template.name} - ${template.description}
    
    Please provide:
    1. Key insights from the data
    2. Recommendations for improvement
    3. Suggested chart types for visualization
    4. Content optimizations
    
    Respond in JSON format with insights, recommendations, optimizations, and generated_content fields.
    `

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert data analyst and document generation assistant. Provide actionable insights and recommendations.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1500,
        temperature: 0.7
      })
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`)
    }

    const aiResponse = await response.json()
    const content = aiResponse.choices[0]?.message?.content

    try {
      return JSON.parse(content)
    } catch (parseError) {
      // If JSON parsing fails, return structured response
      return {
        insights: [content],
        recommendations: [],
        optimizations: [],
        generated_content: {}
      }
    }
  } catch (error) {
    console.error('AI enhancement failed:', error)
    return null
  }
}

async function generateExcelDocument(supabaseClient: any, template: any, data: any[], aiEnhancements: any, config: any) {
  // For now, return a mock result
  // In a real implementation, this would generate the actual Excel file
  const startTime = Date.now()
  
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  const processingTime = Date.now() - startTime
  const fileName = `${config.document_name}_${Date.now()}.xlsx`
  const filePath = `generated-documents/${fileName}`
  
  return {
    filePath: filePath,
    fileSize: 1024 * 50, // 50KB mock size
    processingTime: processingTime
  }
}

async function generatePDFDocument(supabaseClient: any, template: any, data: any[], aiEnhancements: any, config: any) {
  // PDF generation implementation would go here
  throw new Error('PDF generation not yet implemented')
}

async function generateDashboard(supabaseClient: any, template: any, data: any[], aiEnhancements: any, config: any) {
  // Dashboard generation implementation would go here
  throw new Error('Dashboard generation not yet implemented')
}

async function logAIEnhancement(supabaseClient: any, generationId: string, enhancements: any) {
  try {
    await supabaseClient
      .from('ai_enhancement_logs')
      .insert({
        document_generation_id: generationId,
        enhancement_type: 'comprehensive',
        ai_provider: 'openai',
        model_used: 'gpt-4',
        ai_response: enhancements,
        success: true,
        processing_time: 1000,
        tokens_used: 500,
        cost_estimate: 0.01
      })
  } catch (error) {
    console.error('Error logging AI enhancement:', error)
  }
}
