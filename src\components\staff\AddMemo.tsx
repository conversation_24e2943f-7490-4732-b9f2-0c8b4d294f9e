import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery } from "@tanstack/react-query";
import { PlusCircle, Upload, X } from "lucide-react";

export function AddMemo() {
  const [title, setTitle] = useState("");
  const [subject, setSubject] = useState("");
  const [content, setContent] = useState("");
  const [purpose, setPurpose] = useState("");
  const [toRecipient, setToRecipient] = useState("");
  const [accountDetails, setAccountDetails] = useState("");
  const [totalAmount, setTotalAmount] = useState("");
  const [memoDate, setMemoDate] = useState("");
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const { userProfile } = useAuth();

  // Fetch all users for recipient dropdown
  const { data: users } = useQuery({
    queryKey: ['users-list'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .order('full_name');

      if (error) throw error;
      return data;
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setAttachments(Array.from(e.target.files));
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const uploadAttachments = async (memoId: string) => {
    const uploadPromises = attachments.map(async (file) => {
      const fileExt = file.name.split('.').pop();
      const fileName = `${memoId}/${Date.now()}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('memo-attachments')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      // Save attachment metadata
      const { error: metaError } = await supabase
        .from('memo_attachments')
        .insert({
          memo_id: memoId,
          file_name: file.name,
          file_path: uploadData.path,
          file_type: file.type,
          file_size: file.size,
          uploaded_by: userProfile?.id
        });

      if (metaError) throw metaError;
    });

    await Promise.all(uploadPromises);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim() || !content.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both title and content",
        variant: "destructive",
      });
      return;
    }

    if (!userProfile?.id) {
      toast({
        title: "Error",
        description: "You must be logged in to create a memo",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const { data: memoData, error } = await supabase.from("memos").insert([
        {
          title: title.trim(),
          subject: subject.trim(),
          content: content.trim(),
          purpose: purpose.trim(),
          to_recipient: toRecipient,
          account_details: accountDetails.trim(),
          total_amount: totalAmount ? parseFloat(totalAmount) : 0,
          memo_date: memoDate || new Date().toISOString().split('T')[0],
          created_by: userProfile.id,
          from_user: userProfile.id,
        },
      ]).select().single();

      if (error) throw error;

      // Upload attachments if any
      if (attachments.length > 0 && memoData) {
        await uploadAttachments(memoData.id);
      }

      // Reset form
      setTitle("");
      setSubject("");
      setContent("");
      setPurpose("");
      setToRecipient("");
      setAccountDetails("");
      setTotalAmount("");
      setMemoDate("");
      setAttachments([]);
      
      toast({
        title: "Success",
        description: "Memo submitted successfully!",
      });
    } catch (error) {
      console.error("Error submitting memo:", error);
      toast({
        title: "Error",
        description: "Failed to submit memo. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="glass-card border border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PlusCircle className="h-5 w-5" />
          Create New Memo
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Title *</label>
              <Input
                placeholder="Memo title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                disabled={isSubmitting}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Subject</label>
              <Input
                placeholder="Subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">To (Recipient)</label>
              <Select value={toRecipient} onValueChange={setToRecipient} disabled={isSubmitting}>
                <SelectTrigger>
                  <SelectValue placeholder="Select recipient" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all-staff">All Staff</SelectItem>
                  <SelectItem value="management">Management</SelectItem>
                  <SelectItem value="accounting">Accounting Department</SelectItem>
                  {users?.filter(user => user.full_name && user.full_name.trim() !== '').map((user) => (
                    <SelectItem key={user.id} value={user.full_name || user.id}>
                      {user.full_name} ({user.role})
                    </SelectItem>
                  ))}
                  {users?.filter(user => !user.full_name || user.full_name.trim() === '').map((user) => (
                    <SelectItem key={user.id} value={user.email || user.id}>
                      {user.email} ({user.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Date</label>
              <Input
                type="date"
                value={memoDate}
                onChange={(e) => setMemoDate(e.target.value)}
                disabled={isSubmitting}
              />
            </div>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Purpose/Content *</label>
            <Textarea
              placeholder="Memo purpose and content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              disabled={isSubmitting}
              className="min-h-[120px]"
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Detailed Purpose</label>
            <Textarea
              placeholder="Additional details about the memo purpose"
              value={purpose}
              onChange={(e) => setPurpose(e.target.value)}
              disabled={isSubmitting}
              className="min-h-[80px]"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Total Amount (₦)</label>
              <Input
                type="number"
                step="0.01"
                placeholder="0.00"
                value={totalAmount}
                onChange={(e) => setTotalAmount(e.target.value)}
                disabled={isSubmitting}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Attachments</label>
              <div className="flex items-center gap-2">
                <Input
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.xlsx,.xls,.png,.jpg,.jpeg"
                  onChange={handleFileChange}
                  disabled={isSubmitting}
                  className="hidden"
                  id="memo-attachments"
                />
                <label
                  htmlFor="memo-attachments"
                  className="flex items-center gap-2 px-3 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground rounded-md cursor-pointer text-sm"
                >
                  <Upload className="h-4 w-4" />
                  Choose Files
                </label>
              </div>
            </div>
          </div>

          {attachments.length > 0 && (
            <div>
              <label className="text-sm font-medium mb-2 block">Selected Files:</label>
              <div className="space-y-2">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between bg-muted p-2 rounded">
                    <span className="text-sm">{file.name}</span>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeAttachment(index)}
                      disabled={isSubmitting}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div>
            <label className="text-sm font-medium mb-2 block">Account Details (if financial)</label>
            <Textarea
              placeholder="Bank account details, payment instructions, etc."
              value={accountDetails}
              onChange={(e) => setAccountDetails(e.target.value)}
              disabled={isSubmitting}
              className="min-h-[80px]"
            />
          </div>

          <Button 
            type="submit" 
            disabled={isSubmitting || !title.trim() || !content.trim()}
            className="w-full"
          >
            {isSubmitting ? "Submitting..." : "Submit Memo"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
