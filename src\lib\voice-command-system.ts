/**
 * Voice Command System for CTNL AI Workboard
 * Provides voice-guided navigation and assistance throughout the system
 */

import { supabase } from '@/integrations/supabase/client';
import { enhancedVoiceSystem } from './enhanced-voice-system';
import { aiTaskAutomation } from './ai-task-automation';

export interface VoiceCommand {
  id: string;
  trigger: string[];
  action: string;
  description: string;
  context: string[];
  response: string;
  navigation?: string;
  parameters?: Record<string, any>;
}

export interface VoiceGuide {
  page: string;
  introduction: string;
  availableCommands: VoiceCommand[];
  helpText: string;
  quickActions: string[];
}

export class VoiceCommandSystem {
  private recognition: SpeechRecognition | null = null;
  private synthesis: SpeechSynthesis;
  private isListening = false;
  private currentPage = '';
  private userRole = '';
  private voices: SpeechSynthesisVoice[] = [];

  constructor() {
    this.synthesis = window.speechSynthesis;
    this.initializeSpeechRecognition();
    this.loadVoices();
  }

  /**
   * Initialize speech recognition
   */
  private initializeSpeechRecognition(): void {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      
      this.recognition.continuous = false;
      this.recognition.interimResults = false;
      this.recognition.lang = 'en-US';
      
      this.recognition.onresult = this.handleSpeechResult.bind(this);
      this.recognition.onerror = this.handleSpeechError.bind(this);
      this.recognition.onend = () => {
        this.isListening = false;
      };
    }
  }

  /**
   * Load available voices
   */
  private loadVoices(): void {
    this.voices = this.synthesis.getVoices();
    if (this.voices.length === 0) {
      this.synthesis.onvoiceschanged = () => {
        this.voices = this.synthesis.getVoices();
      };
    }
  }

  /**
   * Start listening for voice commands
   */
  public startListening(): void {
    if (this.recognition && !this.isListening) {
      this.isListening = true;
      this.recognition.start();
      this.speak("I'm listening. How can I help you?");
    }
  }

  /**
   * Stop listening
   */
  public stopListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
      this.isListening = false;
    }
  }

  /**
   * Handle speech recognition results
   */
  private handleSpeechResult(event: SpeechRecognitionEvent): void {
    const transcript = event.results[0][0].transcript.toLowerCase().trim();
    console.log('Voice command received:', transcript);
    this.processCommand(transcript);
  }

  /**
   * Handle speech recognition errors
   */
  private handleSpeechError(event: SpeechRecognitionErrorEvent): void {
    console.error('Speech recognition error:', event.error);
    this.speak("Sorry, I didn't catch that. Please try again.");
  }

  /**
   * Process voice command
   */
  private async processCommand(transcript: string): Promise<void> {
    const commands = this.getCommandsForCurrentContext();
    const matchedCommand = this.findMatchingCommand(transcript, commands);

    if (matchedCommand) {
      await this.executeCommand(matchedCommand, transcript);
    } else {
      // Try AI-powered command interpretation
      await this.handleUnknownCommand(transcript);
    }
  }

  /**
   * Find matching command
   */
  private findMatchingCommand(transcript: string, commands: VoiceCommand[]): VoiceCommand | null {
    for (const command of commands) {
      for (const trigger of command.trigger) {
        if (transcript.includes(trigger.toLowerCase())) {
          return command;
        }
      }
    }
    return null;
  }

  /**
   * Execute voice command
   */
  private async executeCommand(command: VoiceCommand, transcript: string): Promise<void> {
    this.speak(command.response);

    switch (command.action) {
      case 'navigate':
        if (command.navigation) {
          setTimeout(() => {
            window.location.href = command.navigation!;
          }, 1000); // Small delay to let the speech finish
        }
        break;

      case 'clock_in':
        await this.handleClockInCommand();
        break;

      case 'clock_out':
        await this.handleClockOutCommand();
        break;

      case 'show_help':
        this.provideContextualHelp();
        break;

      case 'show_status':
        await this.showSystemStatus();
        break;

      case 'show_projects':
        this.handleShowProjects();
        break;

      case 'create_project':
        this.handleCreateProject(transcript);
        break;

      case 'search':
        this.handleSearch(transcript);
        break;

      case 'refresh':
        this.handleRefresh();
        break;

      case 'go_back':
        this.handleGoBack();
        break;

      case 'ai_task_automation':
        await this.handleAITaskAutomation(transcript);
        break;

      default:
        // Try AI task automation for complex commands
        await this.handleUnknownCommand(transcript);
    }
  }

  /**
   * Handle AI task automation commands
   */
  private async handleAITaskAutomation(transcript: string): Promise<void> {
    this.speak("I'll help you execute that task. Let me break it down into steps.");

    try {
      // Get current user context
      const userContext = this.getCurrentUserContext();

      if (!userContext.userId) {
        this.speak("Please log in first to use AI task automation.");
        return;
      }

      const execution = await aiTaskAutomation.executeUserCommand({
        command: transcript,
        userId: userContext.userId,
        userRole: userContext.userRole || 'staff',
        context: {
          currentPage: window.location.pathname
        }
      });

      this.speak(`Starting task: ${execution.name}. I'll guide you through each step.`);
    } catch (error) {
      this.speak("I encountered an error while trying to execute that task. Please try again or be more specific.");
      console.error('AI Task Automation error:', error);
    }
  }

  /**
   * Get current user context
   */
  private getCurrentUserContext(): { userId?: string; userRole?: string } {
    // Get from localStorage or global state
    return {
      userId: localStorage.getItem('userId') || undefined,
      userRole: localStorage.getItem('userRole') || 'staff'
    };
  }

  /**
   * Handle unknown commands with AI
   */
  private async handleUnknownCommand(transcript: string): Promise<void> {
    // Check if this might be a complex task automation command
    const taskKeywords = ['write', 'create', 'upload', 'fill', 'submit', 'navigate', 'click', 'open', 'memo'];
    const hasTaskKeyword = taskKeywords.some(keyword => transcript.toLowerCase().includes(keyword));

    if (hasTaskKeyword) {
      this.speak("That sounds like a task I can help you with. Let me try to execute it.");
      await this.handleAITaskAutomation(transcript);
      return;
    }

    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: `Voice command: "${transcript}". Current page: ${this.currentPage}. User role: ${this.userRole}. Please interpret this command and provide guidance.`,
          context: {
            interface: 'voice_command',
            page: this.currentPage,
            role: this.userRole
          }
        }
      });

      if (error) throw error;

      const response = data.response || "I'm not sure how to help with that. Try saying 'help' for available commands.";
      this.speak(response);
    } catch (error) {
      console.error('AI command interpretation failed:', error);
      this.speak("I didn't understand that command. Try saying 'help' to see what I can do.");
    }
  }

  /**
   * Speak text using enhanced voice system with human-like male voice
   */
  public speak(text: string, options: { rate?: number; pitch?: number; volume?: number; personality?: 'professional' | 'friendly' | 'enthusiastic' } = {}): void {
    // Use the enhanced voice system for better speech quality
    if (enhancedVoiceSystem.isSupported() && enhancedVoiceSystem.isReady()) {
      enhancedVoiceSystem.speakWithPersonality(text, options.personality || 'professional')
        .catch(error => {
          console.error('Enhanced voice system failed, falling back to basic TTS:', error);
          this.fallbackSpeak(text, options);
        });
    } else {
      this.fallbackSpeak(text, options);
    }
  }

  /**
   * Fallback speech method using basic browser TTS
   */
  private fallbackSpeak(text: string, options: { rate?: number; pitch?: number; volume?: number } = {}): void {
    if (this.synthesis) {
      // Cancel any ongoing speech
      this.synthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // Find the best male voice for fallback
      const maleVoice = this.voices.find(voice =>
        voice.lang.startsWith('en') && (
          voice.name.toLowerCase().includes('david') ||
          voice.name.toLowerCase().includes('mark') ||
          voice.name.toLowerCase().includes('alex') ||
          voice.name.toLowerCase().includes('daniel') ||
          voice.name.toLowerCase().includes('male')
        )
      ) || this.voices.find(voice => voice.lang.startsWith('en')) || this.voices[0];

      if (maleVoice) {
        utterance.voice = maleVoice;
      }

      // Male voice parameters
      utterance.rate = options.rate || 0.9;
      utterance.pitch = options.pitch || 0.8; // Lower pitch for masculine sound
      utterance.volume = options.volume || 0.95;

      // Add natural pauses and emphasis
      const enhancedText = this.enhanceTextForSpeech(text);
      utterance.text = enhancedText;

      this.synthesis.speak(utterance);
    }
  }

  /**
   * Enhance text for more natural speech
   */
  private enhanceTextForSpeech(text: string): string {
    return text
      // Add pauses after sentences
      .replace(/\./g, '. ')
      // Add emphasis to important words
      .replace(/\b(welcome|hello|hi|thanks|please|sorry)\b/gi, '$1')
      // Add natural breathing pauses
      .replace(/,/g, ', ')
      // Make it sound more like Ifeanyi's assistant
      .replace(/\bmy maker ifeanyi\b/gi, 'my creator Ifeanyi')
      .replace(/\bifeanyi\b/gi, 'Ifeanyi, my developer')
      // Make responses more conversational
      .replace(/\blet me\b/gi, 'Let me just')
      .replace(/\bi will\b/gi, 'I\'ll go ahead and')
      .replace(/\bi am\b/gi, 'I\'m');
  }

  /**
   * Set current context
   */
  public setContext(page: string, userRole: string): void {
    this.currentPage = page;
    this.userRole = userRole;
  }

  /**
   * Get commands for current context
   */
  private getCommandsForCurrentContext(): VoiceCommand[] {
    const baseCommands: VoiceCommand[] = [
      // Help and Information
      {
        id: 'help',
        trigger: ['help', 'what can you do', 'commands', 'what can I say'],
        action: 'show_help',
        description: 'Show available voice commands',
        context: ['all'],
        response: 'Here are the commands you can use. I can help you navigate, manage tasks, and control the system with your voice.'
      },

      // Navigation Commands
      {
        id: 'navigate_dashboard',
        trigger: ['go to dashboard', 'dashboard', 'home', 'main page', 'go home'],
        action: 'navigate',
        description: 'Navigate to dashboard',
        context: ['all'],
        response: 'Taking you to your dashboard now',
        navigation: '/dashboard'
      },
      {
        id: 'navigate_projects',
        trigger: ['go to projects', 'projects', 'show projects', 'project page'],
        action: 'navigate',
        description: 'Navigate to projects page',
        context: ['all'],
        response: 'Opening your projects page',
        navigation: '/dashboard/projects'
      },
      {
        id: 'navigate_time_management',
        trigger: ['go to time management', 'time management', 'time logs', 'time tracking'],
        action: 'navigate',
        description: 'Navigate to time management',
        context: ['all'],
        response: 'Opening time management for you',
        navigation: '/dashboard/time-management'
      },
      {
        id: 'navigate_reports',
        trigger: ['go to reports', 'reports', 'show reports', 'analytics'],
        action: 'navigate',
        description: 'Navigate to reports page',
        context: ['all'],
        response: 'Taking you to the reports section',
        navigation: '/dashboard/reports'
      },
      {
        id: 'navigate_ai',
        trigger: ['go to ai', 'ai page', 'artificial intelligence', 'ai assistant'],
        action: 'navigate',
        description: 'Navigate to AI page',
        context: ['all'],
        response: 'Opening the AI assistant page',
        navigation: '/ai'
      },
      {
        id: 'navigate_settings',
        trigger: ['go to settings', 'settings', 'preferences', 'configuration'],
        action: 'navigate',
        description: 'Navigate to settings',
        context: ['all'],
        response: 'Opening your settings',
        navigation: '/dashboard/settings'
      },
      {
        id: 'navigate_profile',
        trigger: ['go to profile', 'my profile', 'user profile', 'account'],
        action: 'navigate',
        description: 'Navigate to user profile',
        context: ['all'],
        response: 'Opening your profile page',
        navigation: '/dashboard/profile'
      },

      // Clock In/Out Commands
      {
        id: 'clock_in',
        trigger: ['clock in', 'start work', 'check in', 'begin work', 'start my day'],
        action: 'clock_in',
        description: 'Clock in to start work',
        context: ['clock-in', 'dashboard'],
        response: 'Let me help you clock in for the day'
      },
      {
        id: 'clock_out',
        trigger: ['clock out', 'end work', 'check out', 'finish work', 'end my day'],
        action: 'clock_out',
        description: 'Clock out to end work',
        context: ['dashboard'],
        response: 'Let me help you clock out'
      },

      // Project Management
      {
        id: 'create_project',
        trigger: ['create project', 'new project', 'start project', 'add project'],
        action: 'create_project',
        description: 'Create a new project',
        context: ['projects', 'dashboard'],
        response: 'I\'ll help you create a new project'
      },
      {
        id: 'show_my_projects',
        trigger: ['show my projects', 'my projects', 'list projects'],
        action: 'show_projects',
        description: 'Show user projects',
        context: ['all'],
        response: 'Here are your current projects'
      },

      // System Control
      {
        id: 'search',
        trigger: ['search', 'find', 'look for'],
        action: 'search',
        description: 'Search the system',
        context: ['all'],
        response: 'What would you like me to search for?'
      },
      {
        id: 'show_status',
        trigger: ['show status', 'system status', 'my status', 'current status'],
        action: 'show_status',
        description: 'Show system status',
        context: ['all'],
        response: 'Let me check your current status'
      },
      {
        id: 'refresh_page',
        trigger: ['refresh', 'reload', 'refresh page', 'reload page'],
        action: 'refresh',
        description: 'Refresh the current page',
        context: ['all'],
        response: 'Refreshing the page for you'
      },
      {
        id: 'go_back',
        trigger: ['go back', 'back', 'previous page', 'return'],
        action: 'go_back',
        description: 'Go back to previous page',
        context: ['all'],
        response: 'Going back to the previous page'
      }
    ];

    // Add page-specific commands
    const pageCommands = this.getPageSpecificCommands();
    const roleCommands = this.getRoleSpecificCommands();

    return [...baseCommands, ...pageCommands, ...roleCommands];
  }

  /**
   * Get page-specific commands
   */
  private getPageSpecificCommands(): VoiceCommand[] {
    switch (this.currentPage) {
      case 'clock-in':
        return [
          {
            id: 'get_location',
            trigger: ['get location', 'find location', 'where am i'],
            action: 'get_location',
            description: 'Get current location for clock-in',
            context: ['clock-in'],
            response: 'Getting your current location...'
          }
        ];
      
      case 'dashboard':
        return [
          {
            id: 'show_stats',
            trigger: ['show stats', 'statistics', 'my performance'],
            action: 'show_status',
            description: 'Show dashboard statistics',
            context: ['dashboard'],
            response: 'Here are your current statistics...'
          }
        ];
      
      default:
        return [];
    }
  }

  /**
   * Get role-specific commands
   */
  private getRoleSpecificCommands(): VoiceCommand[] {
    switch (this.userRole) {
      case 'admin':
        return [
          {
            id: 'user_management',
            trigger: ['manage users', 'user management', 'add user'],
            action: 'navigate',
            description: 'Go to user management',
            context: ['admin'],
            response: 'Opening user management',
            navigation: '/dashboard/admin'
          }
        ];
      
      case 'manager':
        return [
          {
            id: 'project_overview',
            trigger: ['project overview', 'projects', 'team status'],
            action: 'navigate',
            description: 'View project overview',
            context: ['manager'],
            response: 'Showing project overview',
            navigation: '/dashboard/manager'
          }
        ];
      
      default:
        return [];
    }
  }

  /**
   * Provide contextual help
   */
  private provideContextualHelp(): void {
    const guide = this.getPageGuide();
    let helpText = guide.introduction + " ";
    
    if (guide.quickActions.length > 0) {
      helpText += "You can say: " + guide.quickActions.join(", ");
    }
    
    this.speak(helpText);
  }

  /**
   * Get page guide
   */
  private getPageGuide(): VoiceGuide {
    const guides: Record<string, VoiceGuide> = {
      'clock-in': {
        page: 'clock-in',
        introduction: 'Welcome to the clock-in page. Here you can start your work day.',
        availableCommands: [],
        helpText: 'Say "clock in" to start work, or "get location" to find your current location.',
        quickActions: ['clock in', 'get location', 'go to dashboard']
      },
      'dashboard': {
        page: 'dashboard',
        introduction: 'You are on your dashboard. This is your main workspace.',
        availableCommands: [],
        helpText: 'You can view your statistics, navigate to different sections, or ask for help.',
        quickActions: ['show stats', 'go to projects', 'time management']
      }
    };

    return guides[this.currentPage] || {
      page: this.currentPage,
      introduction: 'You are currently on the ' + this.currentPage + ' page.',
      availableCommands: [],
      helpText: 'Say "help" to see available commands or "go to dashboard" to return to the main page.',
      quickActions: ['help', 'go to dashboard']
    };
  }

  /**
   * Handle specific commands
   */
  private async handleClockInCommand(): Promise<void> {
    // Trigger clock-in process
    const clockInButton = document.querySelector('[data-testid="clock-in-button"]') as HTMLButtonElement;
    if (clockInButton) {
      clockInButton.click();
      this.speak("Starting your clock-in process now. I'm getting your location to record your attendance.");
    } else {
      this.speak("I don't see the clock-in button on this page. Let me take you to the main page where you can clock in.");
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    }
  }

  private async handleClockOutCommand(): Promise<void> {
    // Trigger clock-out process
    const clockOutButton = document.querySelector('[data-testid="clock-out-button"]') as HTMLButtonElement;
    if (clockOutButton) {
      clockOutButton.click();
      this.speak("Processing your clock-out now. Thank you for your hard work today!");
    } else {
      this.speak("I don't see the clock-out option here. Let me take you to the main page where you can clock out.");
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    }
  }

  private handleShowProjects(): void {
    this.speak("Let me show you your current projects.");
    setTimeout(() => {
      window.location.href = '/dashboard/projects';
    }, 1500);
  }

  private handleRefresh(): void {
    this.speak("Refreshing the page now.");
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }

  private handleGoBack(): void {
    this.speak("Taking you back to the previous page.");
    setTimeout(() => {
      window.history.back();
    }, 1000);
  }

  private async showSystemStatus(): Promise<void> {
    // Get system status and speak it
    this.speak("Checking system status... All systems are operational. You are logged in and ready to work.");
  }

  private handleCreateProject(transcript: string): void {
    // Extract project name from transcript
    const projectName = transcript.replace(/create project|new project/gi, '').trim();
    if (projectName) {
      this.speak(`Creating project: ${projectName}. Please wait...`);
      // Navigate to project creation
      window.location.href = '/dashboard/admin?tab=projects';
    } else {
      this.speak("What would you like to name the new project?");
    }
  }

  private handleSearch(transcript: string): void {
    const searchTerm = transcript.replace(/search|find|look for/gi, '').trim();
    if (searchTerm) {
      this.speak(`Searching for: ${searchTerm}`);
      // Implement search functionality
    } else {
      this.speak("What would you like to search for?");
    }
  }

  /**
   * Public methods
   */
  public isSupported(): boolean {
    return 'speechSynthesis' in window && 
           ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window);
  }

  public getListeningState(): boolean {
    return this.isListening;
  }

  public introduceSystem(): void {
    const intro = `Welcome to CTNL AI Workboard! I'm your voice assistant. 
                   I can help you navigate the system, clock in and out, manage projects, 
                   and answer questions. Just say "help" anytime to see what I can do, 
                   or say "start listening" to begin voice commands.`;
    this.speak(intro);
  }
}

// Export singleton instance
export const voiceCommandSystem = new VoiceCommandSystem();
