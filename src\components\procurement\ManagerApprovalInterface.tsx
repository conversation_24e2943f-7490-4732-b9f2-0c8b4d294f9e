import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  Eye, 
  DollarSign, 
  Calendar, 
  User, 
  Package,
  AlertTriangle,
  MessageSquare,
  FileText
} from 'lucide-react';
import { useProcurementRequests, useProcurementMutations } from '@/hooks/useProcurement';
import { ProcurementRequestDetails } from './ProcurementRequestDetails';

interface ApprovalDialogProps {
  request: any;
  isOpen: boolean;
  onClose: () => void;
  onApprove: (data: { comments?: string; approved_amount?: number; conditions?: string }) => void;
  onReject: (comments: string) => void;
  isLoading: boolean;
}

const ApprovalDialog: React.FC<ApprovalDialogProps> = ({
  request,
  isOpen,
  onClose,
  onApprove,
  onReject,
  isLoading
}) => {
  const [action, setAction] = useState<'approve' | 'reject' | null>(null);
  const [comments, setComments] = useState('');
  const [approvedAmount, setApprovedAmount] = useState(request?.estimated_total_cost || 0);
  const [conditions, setConditions] = useState('');

  const handleSubmit = () => {
    if (action === 'approve') {
      onApprove({
        comments: comments.trim() || undefined,
        approved_amount: approvedAmount !== request?.estimated_total_cost ? approvedAmount : undefined,
        conditions: conditions.trim() || undefined
      });
    } else if (action === 'reject') {
      if (!comments.trim()) {
        alert('Please provide a reason for rejection');
        return;
      }
      onReject(comments.trim());
    }
    
    // Reset form
    setAction(null);
    setComments('');
    setApprovedAmount(request?.estimated_total_cost || 0);
    setConditions('');
    onClose();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Review Procurement Request</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Request Summary */}
          <Card>
            <CardContent className="p-4">
              <h3 className="font-semibold mb-2">{request?.title}</h3>
              <p className="text-sm text-muted-foreground mb-2">
                Request #{request?.request_number}
              </p>
              <div className="flex items-center gap-4 text-sm">
                <span className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  {request?.requested_by_profile?.full_name}
                </span>
                <span className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  {formatCurrency(request?.estimated_total_cost || 0)}
                </span>
                <span className="flex items-center gap-1">
                  <Package className="h-4 w-4" />
                  {request?.procurement_items?.length || 0} items
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Action Selection */}
          <div className="flex gap-4">
            <Button
              onClick={() => setAction('approve')}
              variant={action === 'approve' ? 'default' : 'outline'}
              className="flex-1"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Approve
            </Button>
            <Button
              onClick={() => setAction('reject')}
              variant={action === 'reject' ? 'destructive' : 'outline'}
              className="flex-1"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Reject
            </Button>
          </div>

          {/* Approval Form */}
          {action === 'approve' && (
            <div className="space-y-4 p-4 border rounded-lg bg-green-50">
              <h4 className="font-medium text-green-800">Approval Details</h4>
              
              <div>
                <Label htmlFor="approved_amount">Approved Amount</Label>
                <Input
                  id="approved_amount"
                  type="number"
                  step="0.01"
                  value={approvedAmount}
                  onChange={(e) => setApprovedAmount(parseFloat(e.target.value) || 0)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Original estimate: {formatCurrency(request?.estimated_total_cost || 0)}
                </p>
              </div>

              <div>
                <Label htmlFor="conditions">Conditions (Optional)</Label>
                <Textarea
                  id="conditions"
                  value={conditions}
                  onChange={(e) => setConditions(e.target.value)}
                  placeholder="Any conditions or requirements for this approval..."
                  rows={2}
                />
              </div>

              <div>
                <Label htmlFor="approval_comments">Comments (Optional)</Label>
                <Textarea
                  id="approval_comments"
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  placeholder="Additional comments about this approval..."
                  rows={3}
                />
              </div>
            </div>
          )}

          {/* Rejection Form */}
          {action === 'reject' && (
            <div className="space-y-4 p-4 border rounded-lg bg-red-50">
              <h4 className="font-medium text-red-800">Rejection Reason</h4>
              
              <div>
                <Label htmlFor="rejection_comments">Reason for Rejection *</Label>
                <Textarea
                  id="rejection_comments"
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  placeholder="Please provide a clear reason for rejecting this request..."
                  rows={4}
                  required
                />
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button onClick={onClose} variant="outline" className="flex-1">
              Cancel
            </Button>
            {action && (
              <Button 
                onClick={handleSubmit} 
                disabled={isLoading}
                className={`flex-1 ${action === 'reject' ? 'bg-red-600 hover:bg-red-700' : ''}`}
              >
                {isLoading ? 'Processing...' : action === 'approve' ? 'Approve Request' : 'Reject Request'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export const ManagerApprovalInterface: React.FC = () => {
  const { requests, loading } = useProcurementRequests({ status: 'manager_review' });
  const { approveRequest, rejectRequest, isApprovingRequest, isRejectingRequest } = useProcurementMutations();
  
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [showApprovalDialog, setShowApprovalDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleApprove = (approvalData: { comments?: string; approved_amount?: number; conditions?: string }) => {
    if (selectedRequest) {
      approveRequest({ requestId: selectedRequest.id, approvalData });
    }
  };

  const handleReject = (comments: string) => {
    if (selectedRequest) {
      rejectRequest({ requestId: selectedRequest.id, comments });
    }
  };

  const openApprovalDialog = (request: any) => {
    setSelectedRequest(request);
    setShowApprovalDialog(true);
  };

  const openDetailsDialog = (request: any) => {
    setSelectedRequest(request);
    setShowDetailsDialog(true);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading requests for approval...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Procurement Approvals</h1>
        <p className="text-muted-foreground">
          Review and approve procurement requests from your team
        </p>
      </div>

      {/* Summary */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Pending Approvals</p>
              <p className="text-2xl font-bold">{requests.length}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Value</p>
              <p className="text-2xl font-bold">
                {formatCurrency(requests.reduce((sum, r) => sum + r.estimated_total_cost, 0))}
              </p>
            </div>
            <AlertTriangle className="h-8 w-8 text-orange-600" />
          </div>
        </CardContent>
      </Card>

      {/* Requests List */}
      <Card>
        <CardHeader>
          <CardTitle>Requests Awaiting Approval</CardTitle>
        </CardHeader>
        <CardContent>
          {requests.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <p className="text-muted-foreground">No requests pending approval</p>
              <p className="text-sm text-muted-foreground">All caught up!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {requests.map((request) => (
                <div key={request.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold">{request.title}</h3>
                        <Badge className="bg-blue-100 text-blue-800">
                          MANAGER REVIEW
                        </Badge>
                        <Badge className={`${
                          request.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                          request.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                          request.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {request.priority.toUpperCase()}
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        Request #{request.request_number} • {request.requested_by_profile?.full_name}
                      </p>
                      
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDate(request.created_at)}
                        </span>
                        <span className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {formatCurrency(request.estimated_total_cost)}
                        </span>
                        <span className="flex items-center gap-1">
                          <Package className="h-4 w-4" />
                          {request.procurement_items?.length || 0} items
                        </span>
                        {request.required_by_date && (
                          <span className="flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" />
                            Due: {formatDate(request.required_by_date)}
                          </span>
                        )}
                      </div>

                      <p className="text-sm text-muted-foreground">
                        <strong>Justification:</strong> {request.justification}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-2 ml-4">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openDetailsDialog(request)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                      
                      <Button
                        size="sm"
                        onClick={() => openApprovalDialog(request)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Review
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Approval Dialog */}
      <ApprovalDialog
        request={selectedRequest}
        isOpen={showApprovalDialog}
        onClose={() => setShowApprovalDialog(false)}
        onApprove={handleApprove}
        onReject={handleReject}
        isLoading={isApprovingRequest || isRejectingRequest}
      />

      {/* Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Procurement Request Details</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <ProcurementRequestDetails 
              request={selectedRequest}
              onClose={() => setShowDetailsDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
