#!/usr/bin/env node

/**
 * Fix MIME Type Issues Script
 * Clears caches and restarts development server with proper configuration
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing MIME type issues...');

// 1. Clear Vite cache
console.log('📁 Clearing Vite cache...');
try {
  const viteCacheDir = path.join(process.cwd(), 'node_modules', '.vite');
  if (fs.existsSync(viteCacheDir)) {
    fs.rmSync(viteCacheDir, { recursive: true, force: true });
    console.log('✅ Vite cache cleared');
  }
} catch (error) {
  console.warn('⚠️ Could not clear Vite cache:', error.message);
}

// 2. Clear dist directory
console.log('📁 Clearing dist directory...');
try {
  const distDir = path.join(process.cwd(), 'dist');
  if (fs.existsSync(distDir)) {
    fs.rmSync(distDir, { recursive: true, force: true });
    console.log('✅ Dist directory cleared');
  }
} catch (error) {
  console.warn('⚠️ Could not clear dist directory:', error.message);
}

// 3. Clear node_modules cache
console.log('📁 Clearing node_modules cache...');
try {
  const nodeModulesCacheDir = path.join(process.cwd(), 'node_modules', '.cache');
  if (fs.existsSync(nodeModulesCacheDir)) {
    fs.rmSync(nodeModulesCacheDir, { recursive: true, force: true });
    console.log('✅ Node modules cache cleared');
  }
} catch (error) {
  console.warn('⚠️ Could not clear node_modules cache:', error.message);
}

// 4. Create .htaccess for proper MIME types (if needed for production)
console.log('📄 Creating .htaccess for proper MIME types...');
const htaccessContent = `
# Ensure proper MIME types for JavaScript modules
AddType application/javascript .js
AddType application/javascript .mjs
AddType text/javascript .js
AddType text/javascript .mjs

# Enable CORS for development
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Cache control for development
<FilesMatch "\\.(js|mjs|css|html)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

# Ensure proper module script handling
<FilesMatch "\\.m?js$">
    Header set Content-Type "application/javascript"
</FilesMatch>
`;

try {
  const publicDir = path.join(process.cwd(), 'public');
  if (!fs.existsSync(publicDir)) {
    fs.mkdirSync(publicDir, { recursive: true });
  }
  fs.writeFileSync(path.join(publicDir, '.htaccess'), htaccessContent.trim());
  console.log('✅ .htaccess created with proper MIME types');
} catch (error) {
  console.warn('⚠️ Could not create .htaccess:', error.message);
}

console.log('');
console.log('🎉 MIME type fixes applied!');
console.log('');
console.log('📋 Next steps:');
console.log('1. Hard refresh your browser (Ctrl+Shift+R or Cmd+Shift+R)');
console.log('2. Clear browser cache and cookies for localhost:8083');
console.log('3. If issues persist, restart the development server');
console.log('');
console.log('🔧 Troubleshooting:');
console.log('- Try opening in incognito/private browsing mode');
console.log('- Check browser developer tools for any cached resources');
console.log('- Ensure no browser extensions are interfering');
console.log('');
