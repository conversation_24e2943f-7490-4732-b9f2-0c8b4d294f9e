
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, <PERSON>, <PERSON>, MapP<PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { format } from "date-fns";

interface TelecomSite {
  id: string;
  site_name: string;
  location: string;
  coordinates: string;
  site_type: string;
  status: string;
  site_manager_id: string;
  start_date: string;
  expected_completion: string;
  actual_completion: string | null;
  budget_allocated: number;
  budget_spent: number;
  notes: string;
  created_at: string;
  updated_at: string;
}

interface SiteFormData {
  site_name: string;
  location: string;
  coordinates: string;
  site_type: string;
  status: string;
  site_manager_id: string;
  start_date: string;
  expected_completion: string;
  budget_allocated: string;
  notes: string;
}

export const TelecomSiteManagement = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSite, setEditingSite] = useState<TelecomSite | null>(null);
  const [formData, setFormData] = useState<SiteFormData>({
    site_name: "",
    location: "",
    coordinates: "",
    site_type: "BTS",
    status: "planning",
    site_manager_id: "",
    start_date: "",
    expected_completion: "",
    budget_allocated: "",
    notes: "",
  });

  // Fetch telecom sites
  const { data: sites, isLoading } = useQuery({
    queryKey: ['telecom-sites'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('telecom_sites')
        .select(`
          *,
          manager:profiles!manager_id(id, full_name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as TelecomSite[];
    },
  });

  // Fetch managers for the dropdown
  const { data: managers } = useQuery({
    queryKey: ['managers-list'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .in('role', ['manager', 'admin'])
        .order('full_name');

      if (error) throw error;
      return data;
    },
  });

  // Create/Update site mutation
  const saveSiteMutation = useMutation({
    mutationFn: async (siteData: SiteFormData) => {
      const payload = {
        site_name: siteData.site_name,
        location: siteData.location,
        coordinates: siteData.coordinates,
        site_type: siteData.site_type,
        status: siteData.status,
        site_manager_id: siteData.site_manager_id || null,
        start_date: siteData.start_date || null,
        expected_completion: siteData.expected_completion || null,
        budget_allocated: parseFloat(siteData.budget_allocated) || 0,
        notes: siteData.notes,
      };

      if (editingSite) {
        const { data, error } = await supabase
          .from('construction_sites')
          .update(payload)
          .eq('id', editingSite.id)
          .select()
          .single();
        
        if (error) throw error;
        return data;
      } else {
        const { data, error } = await supabase
          .from('construction_sites')
          .insert([payload])
          .select()
          .single();
        
        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: `Site ${editingSite ? 'updated' : 'created'} successfully`,
      });
      queryClient.invalidateQueries({ queryKey: ['telecom-sites'] });
      setIsDialogOpen(false);
      resetForm();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to ${editingSite ? 'update' : 'create'} site`,
        variant: "destructive",
      });
      console.error('Site save error:', error);
    },
  });

  const resetForm = () => {
    setFormData({
      site_name: "",
      location: "",
      coordinates: "",
      site_type: "BTS",
      status: "planning",
      site_manager_id: "",
      start_date: "",
      expected_completion: "",
      budget_allocated: "",
      notes: "",
    });
    setEditingSite(null);
  };

  const handleEdit = (site: TelecomSite) => {
    setEditingSite(site);
    setFormData({
      site_name: site.site_name,
      location: site.location,
      coordinates: site.coordinates || "",
      site_type: site.site_type,
      status: site.status,
      site_manager_id: site.site_manager_id || "",
      start_date: site.start_date?.split('T')[0] || "",
      expected_completion: site.expected_completion?.split('T')[0] || "",
      budget_allocated: site.budget_allocated?.toString() || "",
      notes: site.notes || "",
    });
    setIsDialogOpen(true);
  };

  const handleCreate = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      planning: "default",
      active: "secondary",
      completed: "outline",
      on_hold: "destructive"
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || "default"}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getManagerName = (managerId: string) => {
    const manager = managers?.find(m => m.id === managerId);
    return manager?.full_name || 'Unassigned';
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading sites...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Telecom Sites ({sites?.length || 0})
            </CardTitle>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={handleCreate}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Site
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>
                    {editingSite ? 'Edit Site' : 'Add New Site'}
                  </DialogTitle>
                </DialogHeader>
                <form onSubmit={(e) => {
                  e.preventDefault();
                  saveSiteMutation.mutate(formData);
                }} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Site Name *</label>
                      <Input
                        value={formData.site_name}
                        onChange={(e) => setFormData({ ...formData, site_name: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Site Type</label>
                      <Select 
                        value={formData.site_type} 
                        onValueChange={(value) => setFormData({ ...formData, site_type: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="BTS">BTS (Base Transceiver Station)</SelectItem>
                          <SelectItem value="Microwave">Microwave Link</SelectItem>
                          <SelectItem value="Fiber">Fiber Optic</SelectItem>
                          <SelectItem value="Tower">Communication Tower</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Location *</label>
                    <Input
                      value={formData.location}
                      onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                      required
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Coordinates (Lat, Long)</label>
                    <Input
                      value={formData.coordinates}
                      onChange={(e) => setFormData({ ...formData, coordinates: e.target.value })}
                      placeholder="6.5244, 3.3792"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Status</label>
                      <Select 
                        value={formData.status} 
                        onValueChange={(value) => setFormData({ ...formData, status: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="planning">Planning</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="on_hold">On Hold</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Site Manager</label>
                      <Select 
                        value={formData.site_manager_id} 
                        onValueChange={(value) => setFormData({ ...formData, site_manager_id: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select manager" />
                        </SelectTrigger>
                        <SelectContent>
                          {managers?.map((manager) => (
                            <SelectItem key={manager.id} value={manager.id}>
                              {manager.full_name} ({manager.email})
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Start Date</label>
                      <Input
                        type="date"
                        value={formData.start_date}
                        onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Expected Completion</label>
                      <Input
                        type="date"
                        value={formData.expected_completion}
                        onChange={(e) => setFormData({ ...formData, expected_completion: e.target.value })}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Budget Allocated (₦)</label>
                    <Input
                      type="number"
                      step="0.01"
                      value={formData.budget_allocated}
                      onChange={(e) => setFormData({ ...formData, budget_allocated: e.target.value })}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Notes</label>
                    <Textarea
                      value={formData.notes}
                      onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                      rows={3}
                    />
                  </div>

                  <div className="flex gap-2 pt-4">
                    <Button 
                      type="submit" 
                      disabled={saveSiteMutation.isPending}
                    >
                      {saveSiteMutation.isPending ? 'Saving...' : editingSite ? 'Update Site' : 'Create Site'}
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {sites && sites.length > 0 ? (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Site Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Manager</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Budget</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sites.map((site) => (
                    <TableRow key={site.id}>
                      <TableCell className="font-medium">{site.site_name}</TableCell>
                      <TableCell>{site.site_type}</TableCell>
                      <TableCell>{site.location}</TableCell>
                      <TableCell>{getManagerName(site.site_manager_id)}</TableCell>
                      <TableCell>{getStatusBadge(site.status)}</TableCell>
                      <TableCell>₦{site.budget_allocated?.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(site)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <MapPin className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No telecom sites found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
