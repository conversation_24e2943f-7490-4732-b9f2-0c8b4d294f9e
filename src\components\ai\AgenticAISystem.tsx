import { useToast } from "@/components/ui/use-toast"; // Using ShadCN's toast hook
import { supabase } from "@/integrations/supabase/client";
import { AgentStep, AgentTask } from "@/types/agent"; // Import our new types
import { useReducer, useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Loader, Play, XCircle } from "lucide-react";

// --- State Management with useReducer ---

type AgentAction =
  | { type: "ADD_TASK"; payload: AgentTask }
  | { type: "START_TASK"; payload: { taskId: string } }
  | {
      type: "UPDATE_STEP_STATUS";
      payload: {
        taskId: string;
        stepIndex: number;
        status: AgentStep["status"];
        result?: any;
      };
    }
  | { type: "UPDATE_PROGRESS"; payload: { taskId: string; progress: number } }
  | { type: "COMPLETE_TASK"; payload: { taskId: string } }
  | { type: "FAIL_TASK"; payload: { taskId: string; error: string } };

const agentReducer = (state: AgentTask[], action: AgentAction): AgentTask[] => {
  switch (action.type) {
    case "ADD_TASK":
      return [...state, action.payload];
    case "START_TASK":
      return state.map((t) =>
        t.id === action.payload.taskId
          ? { ...t, status: "running", startTime: new Date(), progress: 0 }
          : t
      );
    case "UPDATE_STEP_STATUS":
      return state.map((t) =>
        t.id === action.payload.taskId
          ? {
              ...t,
              steps: t.steps.map((step, index) =>
                index === action.payload.stepIndex
                  ? {
                      ...step,
                      status: action.payload.status,
                      result: action.payload.result,
                    }
                  : step
              ),
            }
          : t
      );
    case "UPDATE_PROGRESS":
      return state.map((t) =>
        t.id === action.payload.taskId
          ? { ...t, progress: action.payload.progress }
          : t
      );
    case "COMPLETE_TASK":
      return state.map((t) =>
        t.id === action.payload.taskId
          ? { ...t, status: "completed", progress: 100, endTime: new Date() }
          : t
      );
    case "FAIL_TASK":
      return state.map((t) =>
        t.id === action.payload.taskId
          ? {
              ...t,
              status: "failed",
              error: action.payload.error,
              endTime: new Date(),
            }
          : t
      );
    default:
      return state;
  }
};

// Dummy placeholders - replace with your actual context/hooks
import { useAuth } from "@/components/auth/AuthProvider";

// Use actual user context
const AgenticAISystem = () => {
  const { userProfile } = useAuth();
  const [tasks, dispatch] = useReducer(agentReducer, []);
  const [newTaskName, setNewTaskName] = useState("");
  const { toast } = useToast();

  const handleAddTask = () => {
    if (!newTaskName.trim()) {
      toast({ title: "Task name cannot be empty.", variant: "destructive" });
      return;
    }
    const newTask: AgentTask = {
      id: `task-${Date.now()}`,
      name: newTaskName,
      status: "pending",
      progress: 0,
      // Example steps - in a real app, these would be dynamic
      steps: [
        {
          id: "step1",
          action: "fetch-data",
          description: "Gather initial data",
          status: "pending",
          parameters: { source: "api" },
        },
        {
          id: "step2",
          action: "analyze-data",
          description: "Process the data",
          status: "pending",
          parameters: {},
        },
        {
          id: "step3",
          action: "generate-report",
          description: "Create final report",
          status: "pending",
          parameters: { format: "pdf" },
        },
      ],
    };
    dispatch({ type: "ADD_TASK", payload: newTask });
    setNewTaskName("");
  };

  const executeTask = async (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId);
    if (!task || task.status === "running") return;

    dispatch({ type: "START_TASK", payload: { taskId } });

    try {
      for (let i = 0; i < task.steps.length; i++) {
        const step = task.steps[i];

        // Mark step as running
        dispatch({
          type: "UPDATE_STEP_STATUS",
          payload: { taskId, stepIndex: i, status: "running" },
        });

        let stepResult: any;

        try {
          const { data, error } = await supabase.functions.invoke(
            "ai-agent-executor",
            {
              body: {
                intent: step.action,
                query: step.description,
                parameters: step.parameters,
                userId: userProfile.id,
              },
            }
          );

          if (error) throw error; // Let the outer catch handle it
          stepResult = data;
          dispatch({
            type: "UPDATE_STEP_STATUS",
            payload: {
              taskId,
              stepIndex: i,
              status: "completed",
              result: stepResult,
            },
          });
        } catch (executionError: any) {
          // If a step fails, mark it and stop the task
          dispatch({
            type: "UPDATE_STEP_STATUS",
            payload: {
              taskId,
              stepIndex: i,
              status: "failed",
              result: executionError.message,
            },
          });
          throw new Error(
            `Step "${step.description}" failed: ${executionError.message}`
          );
        }

        // Update overall progress
        const progress = ((i + 1) / task.steps.length) * 100;
        dispatch({ type: "UPDATE_PROGRESS", payload: { taskId, progress } });
      }

      dispatch({ type: "COMPLETE_TASK", payload: { taskId } });
      toast({
        title: "✅ Task Completed",
        description: `Agent successfully completed "${task.name}"`,
      });
    } catch (error: any) {
      dispatch({
        type: "FAIL_TASK",
        payload: { taskId, error: error.message },
      });
      toast({
        title: "❌ Task Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const renderStepStatusIcon = (status: AgentStep["status"]) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "running":
        return <Loader className="h-4 w-4 animate-spin text-blue-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4" />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Agentic AI System</CardTitle>
        <div className="flex w-full max-w-sm items-center space-x-2 pt-4">
          <Input
            type="text"
            placeholder="Enter new task name..."
            value={newTaskName}
            onChange={(e) => setNewTaskName(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleAddTask()}
          />
          <Button onClick={handleAddTask}>Add Task</Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {tasks.length === 0 ? (
          <p className="text-center text-muted-foreground py-8">
            No tasks available. Add one to get started.
          </p>
        ) : (
          tasks.map((task) => (
            <Card key={task.id} className="p-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-lg">{task.name}</h3>
                <Button
                  onClick={() => executeTask(task.id)}
                  disabled={task.status === "running"}
                  size="sm"
                >
                  {task.status === "running" ? (
                    <Loader className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  <span className="ml-2">Run Task</span>
                </Button>
              </div>
              <p className="text-sm text-muted-foreground capitalize">
                Status: {task.status}
              </p>
              {task.error && (
                <p className="text-sm text-red-500">Error: {task.error}</p>
              )}
              <Progress value={task.progress} className="w-full mt-2" />
              <div className="mt-4 space-y-2">
                <h4 className="font-medium">Steps:</h4>
                <ul className="list-inside list-disc space-y-1">
                  {task.steps.map((step) => (
                    <li
                      key={step.id}
                      className="flex items-center gap-2 text-sm"
                    >
                      {renderStepStatusIcon(step.status)}
                      <span>{step.description}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </Card>
          ))
        )}
      </CardContent>
    </Card>
  );
};

export { AgenticAISystem };
export default AgenticAISystem;
