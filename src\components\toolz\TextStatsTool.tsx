import React, { useState, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, RotateCcw, BarChart3, FileText } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface TextStats {
  characters: number;
  charactersNoSpaces: number;
  words: number;
  sentences: number;
  paragraphs: number;
  lines: number;
  averageWordsPerSentence: number;
  averageCharactersPerWord: number;
  readingTime: number;
  mostCommonWords: Array<{ word: string; count: number }>;
  longestWord: string;
  shortestWord: string;
}

const TextStatsTool: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const { toast } = useToast();

  const stats: TextStats = useMemo(() => {
    if (!inputText.trim()) {
      return {
        characters: 0,
        charactersNoSpaces: 0,
        words: 0,
        sentences: 0,
        paragraphs: 0,
        lines: 0,
        averageWordsPerSentence: 0,
        averageCharactersPerWord: 0,
        readingTime: 0,
        mostCommonWords: [],
        longestWord: '',
        shortestWord: ''
      };
    }

    const text = inputText;
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    
    // Words
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const wordCount = words.length;
    
    // Sentences
    const sentences = text.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    const sentenceCount = sentences.length;
    
    // Paragraphs
    const paragraphs = text.split(/\n\s*\n/).filter(para => para.trim().length > 0);
    const paragraphCount = paragraphs.length;
    
    // Lines
    const lines = text.split('\n');
    const lineCount = lines.length;
    
    // Averages
    const averageWordsPerSentence = sentenceCount > 0 ? Math.round((wordCount / sentenceCount) * 100) / 100 : 0;
    const averageCharactersPerWord = wordCount > 0 ? Math.round((charactersNoSpaces / wordCount) * 100) / 100 : 0;
    
    // Reading time (average 200 words per minute)
    const readingTime = Math.ceil(wordCount / 200);
    
    // Word frequency
    const wordFreq: { [key: string]: number } = {};
    words.forEach(word => {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      if (cleanWord.length > 2) { // Only count words longer than 2 characters
        wordFreq[cleanWord] = (wordFreq[cleanWord] || 0) + 1;
      }
    });
    
    const mostCommonWords = Object.entries(wordFreq)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([word, count]) => ({ word, count }));
    
    // Longest and shortest words
    const cleanWords = words.map(word => word.replace(/[^\w]/g, '')).filter(word => word.length > 0);
    const longestWord = cleanWords.reduce((longest, current) => 
      current.length > longest.length ? current : longest, '');
    const shortestWord = cleanWords.reduce((shortest, current) => 
      current.length < shortest.length ? current : shortest, cleanWords[0] || '');

    return {
      characters,
      charactersNoSpaces,
      words: wordCount,
      sentences: sentenceCount,
      paragraphs: paragraphCount,
      lines: lineCount,
      averageWordsPerSentence,
      averageCharactersPerWord,
      readingTime,
      mostCommonWords,
      longestWord,
      shortestWord
    };
  }, [inputText]);

  const copyStats = async () => {
    const statsText = `Text Statistics:
Characters: ${stats.characters}
Characters (no spaces): ${stats.charactersNoSpaces}
Words: ${stats.words}
Sentences: ${stats.sentences}
Paragraphs: ${stats.paragraphs}
Lines: ${stats.lines}
Average words per sentence: ${stats.averageWordsPerSentence}
Average characters per word: ${stats.averageCharactersPerWord}
Estimated reading time: ${stats.readingTime} minute(s)
Longest word: ${stats.longestWord}
Shortest word: ${stats.shortestWord}`;

    try {
      await navigator.clipboard.writeText(statsText);
      toast({
        title: "Copied!",
        description: "Statistics copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy statistics",
        variant: "destructive",
      });
    }
  };

  const clearAll = () => {
    setInputText('');
  };

  const loadSample = () => {
    setInputText(`Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-green-500 rounded-lg">
          <BarChart3 className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">Text Statistics</h2>
          <p className="text-muted-foreground">Analyze text metrics and statistics</p>
        </div>
      </div>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Text Input
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={loadSample}>
                Load Sample
              </Button>
              <Button variant="outline" size="sm" onClick={clearAll}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Enter or paste your text here to analyze..."
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="min-h-[200px] font-mono text-sm"
          />
        </CardContent>
      </Card>

      {/* Statistics Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* Basic Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Basic Counts</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Characters:</span>
              <Badge variant="outline">{stats.characters.toLocaleString()}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Characters (no spaces):</span>
              <Badge variant="outline">{stats.charactersNoSpaces.toLocaleString()}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Words:</span>
              <Badge variant="outline">{stats.words.toLocaleString()}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Sentences:</span>
              <Badge variant="outline">{stats.sentences.toLocaleString()}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Paragraphs:</span>
              <Badge variant="outline">{stats.paragraphs.toLocaleString()}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Lines:</span>
              <Badge variant="outline">{stats.lines.toLocaleString()}</Badge>
            </div>
          </CardContent>
        </Card>

        {/* Averages */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Averages</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span>Words per sentence:</span>
              <Badge variant="outline">{stats.averageWordsPerSentence}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Characters per word:</span>
              <Badge variant="outline">{stats.averageCharactersPerWord}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Reading time:</span>
              <Badge variant="outline">{stats.readingTime} min</Badge>
            </div>
            <div className="flex justify-between">
              <span>Longest word:</span>
              <Badge variant="outline" className="max-w-[100px] truncate" title={stats.longestWord}>
                {stats.longestWord || 'N/A'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Shortest word:</span>
              <Badge variant="outline" className="max-w-[100px] truncate" title={stats.shortestWord}>
                {stats.shortestWord || 'N/A'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Most Common Words */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Most Common Words</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            {stats.mostCommonWords.length > 0 ? (
              stats.mostCommonWords.map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="text-sm">{item.word}</span>
                  <Badge variant="secondary">{item.count}</Badge>
                </div>
              ))
            ) : (
              <p className="text-muted-foreground text-sm">No words to analyze</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Actions */}
      <Card>
        <CardContent className="p-4">
          <div className="flex justify-center">
            <Button onClick={copyStats} disabled={!inputText.trim()}>
              <Copy className="h-4 w-4 mr-2" />
              Copy Statistics
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Information */}
      <Card>
        <CardHeader>
          <CardTitle>About Text Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground space-y-2">
            <p><strong>Reading Time:</strong> Calculated based on average reading speed of 200 words per minute.</p>
            <p><strong>Most Common Words:</strong> Shows the 5 most frequently used words (excluding words with 2 or fewer characters).</p>
            <p><strong>Sentences:</strong> Counted by splitting on periods, exclamation marks, and question marks.</p>
            <p><strong>Paragraphs:</strong> Counted by splitting on double line breaks.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextStatsTool;
