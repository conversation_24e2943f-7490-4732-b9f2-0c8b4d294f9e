<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Fix Instructions</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #3498db;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .method {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .method h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .button {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            font-weight: 600;
        }
        .button:hover {
            background: #2980b9;
        }
        .button.success {
            background: #27ae60;
        }
        .button.warning {
            background: #f39c12;
        }
        .code {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .step {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #27ae60;
        }
        .warning {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
            color: #856404;
        }
        .error {
            background: #f8d7da;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Dashboard Fix Instructions</h1>
        
        <div class="error">
            <strong>Issue Identified:</strong> The <code>exec_sql</code> function doesn't exist in Supabase. We need to use alternative methods.
        </div>
        
        <h2>🔧 Available Fix Methods</h2>
        
        <div class="method">
            <h3>Method 1: Working Browser Tool (Recommended)</h3>
            <p>This tool uses direct Supabase API calls instead of SQL functions.</p>
            <a href="/fix-all-working.html" class="button success">🚀 Open Working Fix Tool</a>
            <div class="step">
                <strong>What it does:</strong>
                <ul>
                    <li>Checks for existing tables</li>
                    <li>Inserts sample data using direct API calls</li>
                    <li>Tests data access permissions</li>
                    <li>Verifies dashboard functionality</li>
                </ul>
            </div>
        </div>
        
        <div class="method">
            <h3>Method 2: Manual SQL Script (Most Reliable)</h3>
            <p>Run this SQL script directly in your Supabase dashboard for complete control.</p>
            <a href="/sql/manual-dashboard-fix.sql" class="button warning" download>📄 Download SQL Script</a>
            
            <div class="step">
                <strong>Steps:</strong>
                <ol>
                    <li>Go to your <a href="https://supabase.com/dashboard" target="_blank">Supabase Dashboard</a></li>
                    <li>Navigate to SQL Editor</li>
                    <li>Copy the SQL script content</li>
                    <li>Find your user ID with: <code>SELECT id FROM auth.users WHERE email = '<EMAIL>';</code></li>
                    <li>Replace 'YOUR_USER_ID' in the script with your actual user ID</li>
                    <li>Run the complete script</li>
                </ol>
            </div>
        </div>
        
        <div class="method">
            <h3>Method 3: Individual Fix Tools</h3>
            <p>Use specific tools for targeted fixes.</p>
            <a href="/fix-dashboard-complete.html" class="button">📊 Dashboard Fix</a>
            <a href="/fix-profile-roles.html" class="button">👥 Profile Roles Fix</a>
            <a href="/fix-activities.html" class="button">📝 Activities Fix</a>
        </div>
        
        <h2>📋 What Gets Fixed</h2>
        
        <div class="step">
            <h4>Database Tables Created:</h4>
            <ul>
                <li><strong>invoices</strong> - Financial invoice management</li>
                <li><strong>expense_reports</strong> - Expense tracking and approval</li>
                <li><strong>reports</strong> - Report submission system</li>
                <li><strong>time_logs</strong> - Time tracking functionality</li>
                <li><strong>notifications</strong> - User notification system</li>
                <li><strong>system_activities</strong> - Activity logging</li>
            </ul>
        </div>
        
        <div class="step">
            <h4>RLS Policies Fixed:</h4>
            <ul>
                <li>Role-based SELECT policies for all tables</li>
                <li>Proper INSERT/UPDATE restrictions</li>
                <li>Admin override capabilities</li>
                <li>Department-based access for managers</li>
            </ul>
        </div>
        
        <div class="step">
            <h4>Sample Data Inserted:</h4>
            <ul>
                <li>5 sample invoices (paid, pending, overdue)</li>
                <li>5 sample expense reports (various categories)</li>
                <li>5 sample reports (different types)</li>
                <li>7 sample time log entries</li>
                <li>5 sample notifications</li>
            </ul>
        </div>
        
        <h2>🧪 Testing Your Fix</h2>
        
        <div class="step">
            <p>After running any fix method, test the following:</p>
            <ol>
                <li><strong>Dashboard Access:</strong> Go to <a href="/" target="_blank">your dashboard</a> and check if charts display data</li>
                <li><strong>Role Access:</strong> Verify your user role is working correctly</li>
                <li><strong>Report Submission:</strong> Try submitting a test report</li>
                <li><strong>Time Tracking:</strong> Check if time logs are accessible</li>
                <li><strong>Financial Data:</strong> Verify invoice and expense data displays</li>
            </ol>
        </div>
        
        <h2>🔍 Troubleshooting</h2>
        
        <div class="warning">
            <h4>Common Issues:</h4>
            <ul>
                <li><strong>"Permission denied":</strong> Run the profile roles fix first</li>
                <li><strong>"Table does not exist":</strong> Use the manual SQL script method</li>
                <li><strong>"exec_sql function not found":</strong> Use the working browser tool instead</li>
                <li><strong>Empty charts:</strong> Ensure sample data was inserted successfully</li>
            </ul>
        </div>
        
        <div class="step">
            <h4>If All Else Fails:</h4>
            <ol>
                <li>Use the manual SQL script method (most reliable)</li>
                <li>Check your Supabase project permissions</li>
                <li>Verify you're logged in with admin access</li>
                <li>Contact support with specific error messages</li>
            </ol>
        </div>
        
        <h2>📞 Quick Links</h2>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="/fix-all-working.html" class="button success">🚀 Working Fix Tool</a>
            <a href="https://supabase.com/dashboard" class="button" target="_blank">🗄️ Supabase Dashboard</a>
            <a href="/" class="button">📊 Main Dashboard</a>
        </div>
        
        <div class="step">
            <p><strong>Server Status:</strong> Development server running on <code>http://localhost:8083</code></p>
            <p><strong>Last Updated:</strong> <span id="timestamp"></span></p>
        </div>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString();
    </script>
</body>
</html>
