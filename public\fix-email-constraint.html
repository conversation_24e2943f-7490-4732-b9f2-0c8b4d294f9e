<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Email Constraint Issue - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .sql-box {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .error-details {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Email Constraint Issue</h1>
            <p>Targeted fix for duplicate email constraint error</p>
        </div>

        <div class="card">
            <h3>🔍 Issue Identified</h3>
            <div class="error-details">
                <h4>Root Cause:</h4>
                <ul>
                    <li><strong>406 Error:</strong> RLS policies blocking profile access</li>
                    <li><strong>409 Error:</strong> Duplicate email constraint violation</li>
                    <li><strong>Problem:</strong> Profile exists but RLS prevents seeing it</li>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>User ID:</strong> 44349058-db4b-4a0b-8c99-8a913d07df74</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>🚀 Immediate Fix</h3>
            <p><strong>This SQL will resolve the issue by:</strong></p>
            <ol>
                <li>Temporarily disabling RLS to access existing data</li>
                <li>Cleaning up any duplicate email entries</li>
                <li>Creating/updating the correct profile</li>
                <li>Resetting RLS with simple, working policies</li>
            </ol>
            
            <div class="sql-box" id="sqlCode">-- CORRECTED FIX FOR EMAIL CONSTRAINT ISSUE
-- Copy and run this in Supabase SQL Editor

-- 1. Disable RLS to access existing data
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Clean up any duplicate profiles with this email
DELETE FROM public.profiles
WHERE email = '<EMAIL>'
  AND id != '44349058-db4b-4a0b-8c99-8a913d07df74';

-- 3. Create/update the correct profile
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

-- 4. Drop existing RLS policies (explicit list)
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_update" ON public.profiles;

-- 5. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 6. Create simple, working policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- 7. Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- 8. Verify the fix
SELECT 'Profile Fixed' as status, id, email, role, status
FROM public.profiles
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';</div>

            <button class="button" onclick="copySQL()">📋 Copy SQL Fix</button>
        </div>

        <div class="card">
            <h3>📋 Steps to Apply Fix</h3>
            <ol>
                <li><strong>Copy the SQL above</strong> by clicking the "Copy SQL Fix" button</li>
                <li><strong>Go to your Supabase dashboard</strong> → SQL Editor</li>
                <li><strong>Paste and run the SQL</strong></li>
                <li><strong>Refresh your application</strong> - the errors should be gone</li>
            </ol>
        </div>

        <div id="status" class="status success" style="display: none;"></div>
    </div>

    <script>
        function copySQL() {
            const sqlCode = document.getElementById('sqlCode').textContent;
            navigator.clipboard.writeText(sqlCode).then(() => {
                const statusDiv = document.getElementById('status');
                statusDiv.innerHTML = '✅ SQL copied to clipboard!\n\nNext steps:\n1. Go to Supabase Dashboard → SQL Editor\n2. Paste and run the SQL\n3. Refresh your application\n\nThis will resolve both the 406 and 409 errors.';
                statusDiv.style.display = 'block';
            }).catch(() => {
                alert('Failed to copy SQL. Please select and copy manually.');
            });
        }

        // Show instructions on load
        window.addEventListener('load', () => {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '🔧 Ready to fix the email constraint issue.\n\nClick "Copy SQL Fix" to get the solution.';
            statusDiv.style.display = 'block';
        });
    </script>
</body>
</html>
