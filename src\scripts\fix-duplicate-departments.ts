import { supabase } from '../integrations/supabase/client';

export class DepartmentFixer {
  
  /**
   * Fix duplicate department constraint errors
   */
  static async fixDuplicateDepartments() {
    console.log('🔧 Fixing duplicate department constraint errors...');
    
    try {
      // First, check what departments already exist
      const { data: existingDepartments, error: fetchError } = await supabase
        .from('departments')
        .select('id, name, description');
      
      if (fetchError) {
        console.error('❌ Error fetching departments:', fetchError.message);
        return;
      }
      
      console.log('📋 Existing departments:', existingDepartments?.map(d => d.name) || []);
      
      // List of departments we want to ensure exist
      const requiredDepartments = [
        { name: 'Information Technology', description: 'IT Department - Software, Hardware, and Network Management' },
        { name: 'Human Resources', description: 'HR Department - Employee Management and Relations' },
        { name: 'Finance', description: 'Finance Department - Accounting and Financial Management' },
        { name: 'Operations', description: 'Operations Department - Daily Operations and Logistics' },
        { name: 'Marketing', description: 'Marketing Department - Brand and Customer Engagement' },
        { name: 'Sales', description: 'Sales Department - Revenue Generation and Client Relations' },
        { name: 'Administration', description: 'Administration Department - General Administrative Tasks' }
      ];
      
      const existingNames = existingDepartments?.map(d => d.name.toLowerCase()) || [];
      
      // Only insert departments that don't exist
      const departmentsToCreate = requiredDepartments.filter(dept => 
        !existingNames.includes(dept.name.toLowerCase())
      );
      
      if (departmentsToCreate.length === 0) {
        console.log('✅ All required departments already exist');
        return { success: true, created: 0, existing: existingDepartments?.length || 0 };
      }
      
      console.log(`🔧 Creating ${departmentsToCreate.length} missing departments...`);
      
      // Insert missing departments one by one to handle any remaining conflicts
      let createdCount = 0;
      const errors: string[] = [];
      
      for (const dept of departmentsToCreate) {
        try {
          const { error: insertError } = await supabase
            .from('departments')
            .insert([dept]);
          
          if (insertError) {
            if (insertError.message.includes('duplicate key value')) {
              console.log(`ℹ️ Department "${dept.name}" already exists (concurrent creation)`);
            } else {
              console.error(`❌ Failed to create "${dept.name}":`, insertError.message);
              errors.push(`${dept.name}: ${insertError.message}`);
            }
          } else {
            console.log(`✅ Created department: ${dept.name}`);
            createdCount++;
          }
        } catch (err: any) {
          console.error(`💥 Unexpected error creating "${dept.name}":`, err.message);
          errors.push(`${dept.name}: ${err.message}`);
        }
      }
      
      // Verify final state
      const { data: finalDepartments } = await supabase
        .from('departments')
        .select('id, name, description')
        .order('name');
      
      console.log('\n📊 DEPARTMENT CREATION SUMMARY');
      console.log('=====================================');
      console.log(`✅ Created: ${createdCount} departments`);
      console.log(`📋 Total departments: ${finalDepartments?.length || 0}`);
      
      if (finalDepartments && finalDepartments.length > 0) {
        console.log('\n📋 All departments:');
        finalDepartments.forEach(dept => {
          console.log(`  - ${dept.name}`);
        });
      }
      
      if (errors.length > 0) {
        console.log('\n⚠️ Errors encountered:');
        errors.forEach(error => console.log(`  - ${error}`));
      }
      
      return {
        success: true,
        created: createdCount,
        total: finalDepartments?.length || 0,
        errors
      };
      
    } catch (error: any) {
      console.error('💥 Failed to fix departments:', error.message);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * Create sample data for empty dashboard (avoiding duplicates)
   */
  static async createSampleDataSafely() {
    console.log('🔧 Creating sample data safely (avoiding duplicates)...');
    
    try {
      // Fix departments first
      await this.fixDuplicateDepartments();
      
      // Get departments for foreign key references
      const { data: departments } = await supabase
        .from('departments')
        .select('id, name')
        .limit(3);
      
      const itDeptId = departments?.find(d => d.name.toLowerCase().includes('information') || d.name.toLowerCase().includes('it'))?.id;
      
      // Check and create projects (avoiding duplicates)
      const { data: existingProjects } = await supabase
        .from('projects')
        .select('name');
      
      const existingProjectNames = existingProjects?.map(p => p.name.toLowerCase()) || [];
      
      const sampleProjects = [
        {
          name: 'Dashboard Enhancement',
          description: 'Improve dashboard UI/UX with neumorphism design',
          status: 'active',
          progress_percentage: 75,
          department_id: itDeptId
        },
        {
          name: 'Database Optimization',
          description: 'Optimize database queries and schema',
          status: 'active',
          progress_percentage: 60,
          department_id: itDeptId
        },
        {
          name: 'User Management System',
          description: 'Complete user management and authentication',
          status: 'completed',
          progress_percentage: 100,
          department_id: itDeptId
        }
      ];
      
      const projectsToCreate = sampleProjects.filter(project => 
        !existingProjectNames.includes(project.name.toLowerCase())
      );
      
      if (projectsToCreate.length > 0) {
        const { error: projectError } = await supabase
          .from('projects')
          .insert(projectsToCreate);
        
        if (projectError) {
          console.log('⚠️ Some projects may already exist:', projectError.message);
        } else {
          console.log(`✅ Created ${projectsToCreate.length} sample projects`);
        }
      }
      
      // Check and create tasks (avoiding duplicates)
      const { data: existingTasks } = await supabase
        .from('tasks')
        .select('title');
      
      const existingTaskTitles = existingTasks?.map(t => t.title.toLowerCase()) || [];
      
      const sampleTasks = [
        {
          title: 'Implement Neumorphism Design',
          description: 'Add neumorphism effects to all UI components',
          status: 'in_progress',
          priority: 'high'
        },
        {
          title: 'Fix Database Constraints',
          description: 'Resolve duplicate key constraint issues',
          status: 'completed',
          priority: 'high'
        },
        {
          title: 'Update Documentation',
          description: 'Document new features and API changes',
          status: 'pending',
          priority: 'medium'
        },
        {
          title: 'Test Dashboard Performance',
          description: 'Performance testing for dashboard components',
          status: 'pending',
          priority: 'low'
        }
      ];
      
      const tasksToCreate = sampleTasks.filter(task => 
        !existingTaskTitles.includes(task.title.toLowerCase())
      );
      
      if (tasksToCreate.length > 0) {
        const { error: taskError } = await supabase
          .from('tasks')
          .insert(tasksToCreate);
        
        if (taskError) {
          console.log('⚠️ Some tasks may already exist:', taskError.message);
        } else {
          console.log(`✅ Created ${tasksToCreate.length} sample tasks`);
        }
      }
      
      console.log('✅ Sample data creation completed successfully');
      return { success: true };
      
    } catch (error: any) {
      console.error('💥 Failed to create sample data:', error.message);
      return { success: false, error: error.message };
    }
  }
  
  /**
   * Quick fix for the current error
   */
  static async quickFix() {
    console.log('🚀 Quick fix for duplicate department error...');
    
    try {
      // Just ensure we have the basic departments without duplicating
      await this.fixDuplicateDepartments();
      
      // Create minimal sample data for dashboard
      await this.createSampleDataSafely();
      
      console.log('✅ Quick fix completed! Dashboard should now have data.');
      console.log('💡 Refresh your browser to see the changes.');
      
      return { success: true };
      
    } catch (error: any) {
      console.error('💥 Quick fix failed:', error.message);
      return { success: false, error: error.message };
    }
  }
}

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  (window as any).fixDuplicateDepartments = () => DepartmentFixer.fixDuplicateDepartments();
  (window as any).createSampleDataSafely = () => DepartmentFixer.createSampleDataSafely();
  (window as any).quickFixDuplicates = () => DepartmentFixer.quickFix();
  
  console.log('🛠️ Department fixer loaded. Available commands:');
  console.log('  - fixDuplicateDepartments() - Fix duplicate department errors');
  console.log('  - createSampleDataSafely() - Create sample data avoiding duplicates');
  console.log('  - quickFixDuplicates() - Quick fix for current error');
}

// DepartmentFixer already exported as class declaration above
