import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { 
  Upload, 
  FileText, 
  Brain, 
  Eye, 
  Users, 
  MapPin, 
  Calendar, 
  DollarSign,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Loader
} from 'lucide-react';

interface AnalysisResult {
  extractedText: string;
  summary: string;
  keyInsights: string[];
  actionItems: string[];
  entities: {
    people: string[];
    organizations: string[];
    locations: string[];
    dates: string[];
    amounts: string[];
  };
  sentiment: {
    score: number;
    label: 'positive' | 'negative' | 'neutral';
  };
  topics: string[];
  confidenceLevel: number;
  metadata: {
    pageCount?: number;
    wordCount: number;
    language: string;
    documentType: string;
  };
}

export function AdvancedDocumentAnalysis() {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [analysisType, setAnalysisType] = useState<'basic' | 'comprehensive' | 'semantic' | 'ocr'>('comprehensive');

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setAnalysisResult(null);
      
      // Validate file type
      const supportedTypes = [
        'application/pdf',
        'image/jpeg',
        'image/png',
        'image/gif',
        'text/plain',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ];
      
      if (!supportedTypes.includes(file.type)) {
        toast({
          title: "Unsupported File Type",
          description: "Please select a PDF, image, or text document.",
          variant: "destructive",
        });
        setSelectedFile(null);
        return;
      }

      toast({
        title: "File Selected",
        description: `${file.name} is ready for analysis`,
      });
    }
  }, [toast]);

  const performAnalysis = async () => {
    if (!selectedFile) {
      toast({
        title: "No File Selected",
        description: "Please select a file to analyze",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    setProgress(0);

    try {
      // Step 1: Upload file to Supabase Storage
      setProgress(20);
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('documents')
        .upload(fileName, selectedFile);

      if (uploadError) throw uploadError;

      // Step 2: Get public URL
      setProgress(40);
      const { data: { publicUrl } } = supabase.storage
        .from('documents')
        .getPublicUrl(fileName);

      // Step 3: Call analysis function
      setProgress(60);
      const { data: analysisData, error: analysisError } = await supabase.functions
        .invoke('advanced-document-analysis', {
          body: {
            fileUrl: publicUrl,
            fileName: selectedFile.name,
            fileType: selectedFile.type,
            analysisType,
            userId: userProfile?.id
          }
        });

      if (analysisError) throw analysisError;

      setProgress(100);
      setAnalysisResult(analysisData.analysis);

      toast({
        title: "🧠 Analysis Complete",
        description: `Successfully analyzed ${selectedFile.name} with ${Math.round(analysisData.analysis.confidenceLevel * 100)}% confidence`,
      });

      // Clean up uploaded file
      await supabase.storage.from('documents').remove([fileName]);

    } catch (error: any) {
      console.error('Analysis error:', error);
      toast({
        title: "Analysis Failed",
        description: error.message || "Failed to analyze document",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
      setProgress(0);
    }
  };

  const getSentimentColor = (sentiment: AnalysisResult['sentiment']) => {
    switch (sentiment.label) {
      case 'positive': return 'bg-green-500';
      case 'negative': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const getSentimentIcon = (sentiment: AnalysisResult['sentiment']) => {
    switch (sentiment.label) {
      case 'positive': return <TrendingUp className="h-4 w-4" />;
      case 'negative': return <AlertCircle className="h-4 w-4" />;
      default: return <Eye className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Brain className="h-6 w-6" />
            Advanced Document Analysis
          </CardTitle>
          <CardDescription className="text-green-600">
            Enhanced OCR and semantic processing with AI-powered insights
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* File Upload Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <input
                type="file"
                id="document-upload"
                className="hidden"
                onChange={handleFileSelect}
                accept=".pdf,.jpg,.jpeg,.png,.gif,.txt,.doc,.docx"
              />
              <label
                htmlFor="document-upload"
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg cursor-pointer hover:bg-green-700 transition-colors"
              >
                <Upload className="h-4 w-4" />
                Select Document
              </label>
              
              <select
                value={analysisType}
                onChange={(e) => setAnalysisType(e.target.value as any)}
                className="px-3 py-2 border border-green-300 rounded-lg bg-white"
              >
                <option value="basic">Basic Analysis</option>
                <option value="comprehensive">Comprehensive Analysis</option>
                <option value="semantic">Semantic Analysis</option>
                <option value="ocr">OCR + Analysis</option>
              </select>
            </div>

            {selectedFile && (
              <div className="flex items-center gap-2 p-3 bg-green-100 rounded-lg">
                <FileText className="h-5 w-5 text-green-600" />
                <span className="text-green-800">{selectedFile.name}</span>
                <Badge variant="secondary" className="ml-auto">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </Badge>
              </div>
            )}

            <Button
              onClick={performAnalysis}
              disabled={!selectedFile || isAnalyzing}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {isAnalyzing ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing Document...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Start Analysis
                </>
              )}
            </Button>

            {isAnalyzing && (
              <div className="space-y-2">
                <Progress value={progress} className="w-full" />
                <p className="text-sm text-green-600 text-center">
                  {progress < 30 ? 'Uploading document...' :
                   progress < 60 ? 'Processing file...' :
                   progress < 90 ? 'Performing AI analysis...' :
                   'Finalizing results...'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysisResult && (
        <Card className="border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <CheckCircle className="h-6 w-6" />
              Analysis Results
            </CardTitle>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3" />
                {Math.round(analysisResult.confidenceLevel * 100)}% Confidence
              </Badge>
              <Badge variant="outline">
                {analysisResult.metadata.documentType}
              </Badge>
              <Badge variant="outline">
                {analysisResult.metadata.wordCount} words
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="insights">Insights</TabsTrigger>
                <TabsTrigger value="entities">Entities</TabsTrigger>
                <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
                <TabsTrigger value="text">Full Text</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">Document Summary</h4>
                  <p className="text-blue-700">{analysisResult.summary}</p>
                </div>
                
                {analysisResult.actionItems.length > 0 && (
                  <div className="p-4 bg-orange-50 rounded-lg">
                    <h4 className="font-semibold text-orange-800 mb-2">Action Items</h4>
                    <ul className="list-disc list-inside space-y-1">
                      {analysisResult.actionItems.map((item, index) => (
                        <li key={index} className="text-orange-700">{item}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-semibold text-purple-800 mb-2">Topics</h4>
                  <div className="flex flex-wrap gap-2">
                    {analysisResult.topics.map((topic, index) => (
                      <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-800">
                        {topic}
                      </Badge>
                    ))}
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="insights" className="space-y-4">
                <div className="grid gap-4">
                  {analysisResult.keyInsights.map((insight, index) => (
                    <div key={index} className="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                      <p className="text-green-800">{insight}</p>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="entities" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-semibold text-blue-800 mb-2 flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      People
                    </h4>
                    <div className="space-y-1">
                      {analysisResult.entities.people.map((person, index) => (
                        <Badge key={index} variant="outline" className="mr-1 mb-1">
                          {person}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-semibold text-purple-800 mb-2 flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Locations
                    </h4>
                    <div className="space-y-1">
                      {analysisResult.entities.locations.map((location, index) => (
                        <Badge key={index} variant="outline" className="mr-1 mb-1">
                          {location}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-semibold text-green-800 mb-2 flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Amounts
                    </h4>
                    <div className="space-y-1">
                      {analysisResult.entities.amounts.map((amount, index) => (
                        <Badge key={index} variant="outline" className="mr-1 mb-1">
                          {amount}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="sentiment" className="space-y-4">
                <div className="p-6 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-semibold text-gray-800">Sentiment Analysis</h4>
                    <div className={`flex items-center gap-2 px-3 py-1 rounded-full text-white ${getSentimentColor(analysisResult.sentiment)}`}>
                      {getSentimentIcon(analysisResult.sentiment)}
                      <span className="capitalize">{analysisResult.sentiment.label}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Sentiment Score</span>
                      <span>{analysisResult.sentiment.score.toFixed(2)}</span>
                    </div>
                    <Progress 
                      value={(analysisResult.sentiment.score + 1) * 50} 
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Negative</span>
                      <span>Neutral</span>
                      <span>Positive</span>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="text" className="space-y-4">
                <div className="p-4 bg-gray-50 rounded-lg max-h-96 overflow-y-auto">
                  <h4 className="font-semibold text-gray-800 mb-2">Extracted Text</h4>
                  <pre className="whitespace-pre-wrap text-sm text-gray-700">
                    {analysisResult.extractedText}
                  </pre>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
