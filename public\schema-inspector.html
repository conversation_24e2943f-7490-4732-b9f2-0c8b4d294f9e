<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema Inspector</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .schema-table {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .schema-table h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .column-list {
            font-family: monospace;
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Database Schema Inspector</h1>
        <p>This tool will inspect your actual database schema to find the exact column names and constraints.</p>
        
        <div id="status"></div>
        
        <button id="inspectBtn" class="button" onclick="inspectSchema()">
            🔍 Inspect Database Schema
        </button>
        
        <button id="fixBtn" class="button" onclick="createFinalFix()" disabled>
            🚀 Create Final Fix
        </button>
        
        <div id="schemaResults"></div>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        let discoveredSchemas = {};
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        async function inspectSchema() {
            const inspectBtn = document.getElementById('inspectBtn');
            const fixBtn = document.getElementById('fixBtn');
            const resultsDiv = document.getElementById('schemaResults');
            
            inspectBtn.disabled = true;
            inspectBtn.textContent = '🔄 Inspecting...';
            
            try {
                log('🔍 Starting database schema inspection...');
                
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('Please log in first');
                
                log('✅ User authenticated: ' + user.email);
                const userId = user.id;
                
                const tables = ['invoices', 'expense_reports', 'reports', 'time_logs', 'notifications'];
                let schemaHtml = '';
                
                for (const table of tables) {
                    log(`🔍 Inspecting ${table} schema...`);
                    
                    try {
                        // Try to get existing data to see column structure
                        const { data: existingData, error: selectError } = await supabase
                            .from(table)
                            .select('*')
                            .limit(1);
                        
                        if (!selectError && existingData && existingData.length > 0) {
                            const columns = Object.keys(existingData[0]);
                            discoveredSchemas[table] = { columns, hasData: true };
                            
                            schemaHtml += `
                                <div class="schema-table">
                                    <h4>${table} (${columns.length} columns, has data)</h4>
                                    <div class="column-list">${columns.join(', ')}</div>
                                </div>
                            `;
                            
                            log(`✅ ${table}: Found ${columns.length} columns with data`);
                        } else {
                            // Try minimal insert to discover required fields
                            log(`ℹ️ ${table}: No existing data, testing minimal insert...`);
                            
                            let testData = {};
                            
                            // Add user reference if likely needed
                            if (table === 'time_logs') {
                                testData.user_id = userId;
                                testData.clock_in = new Date().toISOString();
                            } else if (table === 'reports') {
                                testData.title = 'Test Report';
                                testData.submitted_by = userId;
                            } else if (table === 'invoices') {
                                testData.invoice_number = 'TEST-' + Date.now();
                            } else if (table === 'notifications') {
                                testData.user_id = userId;
                                testData.title = 'Test';
                                testData.message = 'Test';
                            } else if (table === 'expense_reports') {
                                testData.title = 'Test';
                                testData.amount = 100;
                                testData.category = 'test';
                                testData.submitted_by = userId;
                                testData.expense_date = new Date().toISOString().split('T')[0];
                            }
                            
                            const { data: insertResult, error: insertError } = await supabase
                                .from(table)
                                .insert(testData)
                                .select();
                            
                            if (insertError) {
                                log(`⚠️ ${table}: ${insertError.message}`);
                                discoveredSchemas[table] = { error: insertError.message, hasData: false };
                                
                                schemaHtml += `
                                    <div class="schema-table">
                                        <h4>${table} (error during inspection)</h4>
                                        <div class="column-list" style="color: #dc3545;">${insertError.message}</div>
                                    </div>
                                `;
                            } else if (insertResult && insertResult.length > 0) {
                                const columns = Object.keys(insertResult[0]);
                                discoveredSchemas[table] = { columns, hasData: false, testInserted: true };
                                
                                schemaHtml += `
                                    <div class="schema-table">
                                        <h4>${table} (${columns.length} columns, test inserted)</h4>
                                        <div class="column-list">${columns.join(', ')}</div>
                                    </div>
                                `;
                                
                                log(`✅ ${table}: Test insert successful, found ${columns.length} columns`);
                                
                                // Clean up test data
                                await supabase.from(table).delete().eq('id', insertResult[0].id);
                            }
                        }
                    } catch (e) {
                        log(`❌ ${table}: ${e.message}`);
                        discoveredSchemas[table] = { error: e.message, hasData: false };
                        
                        schemaHtml += `
                            <div class="schema-table">
                                <h4>${table} (inspection failed)</h4>
                                <div class="column-list" style="color: #dc3545;">${e.message}</div>
                            </div>
                        `;
                    }
                }
                
                resultsDiv.innerHTML = schemaHtml;
                
                log('🎉 Schema inspection completed!');
                showStatus('✅ Schema inspection completed. Review the results below.', 'success');
                
                fixBtn.disabled = false;
                
            } catch (error) {
                log('❌ Schema inspection failed: ' + error.message);
                showStatus('❌ Schema inspection failed: ' + error.message, 'error');
            } finally {
                inspectBtn.disabled = false;
                inspectBtn.textContent = '🔍 Inspect Database Schema';
            }
        }
        
        async function createFinalFix() {
            const fixBtn = document.getElementById('fixBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Creating Fix...';
            
            try {
                log('🚀 Creating final fix based on discovered schema...');
                
                const { data: { user } } = await supabase.auth.getUser();
                const userId = user.id;
                
                let totalInserted = 0;
                
                // Fix reports based on discovered constraints
                if (discoveredSchemas.reports) {
                    log('🔧 Fixing reports with discovered schema...');
                    
                    // Try different report_type values to find valid ones
                    const reportTypes = ['general', 'financial', 'project', 'maintenance', 'incident', 'performance', 'weekly', 'monthly', 'safety', 'quality'];
                    
                    for (const reportType of reportTypes) {
                        try {
                            const { data: reportResult, error: reportError } = await supabase
                                .from('reports')
                                .insert({
                                    title: `${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`,
                                    description: `Sample ${reportType} report for testing`,
                                    report_type: reportType,
                                    submitted_by: userId
                                })
                                .select();
                            
                            if (!reportError) {
                                log(`✅ Successfully inserted ${reportType} report`);
                                totalInserted++;
                                break; // Stop after first successful insert
                            } else {
                                log(`⚠️ Failed ${reportType}: ${reportError.message}`);
                            }
                        } catch (e) {
                            log(`⚠️ Error with ${reportType}: ${e.message}`);
                        }
                    }
                }
                
                // Fix invoices based on discovered schema
                if (discoveredSchemas.invoices) {
                    log('🔧 Fixing invoices with discovered schema...');
                    
                    // Try different column combinations
                    const invoiceVariations = [
                        {
                            invoice_number: 'INV-' + Date.now() + '-A',
                            client_name: 'Test Client A',
                            subtotal: 1000.00,
                            total_amount: 1000.00,
                            created_by: userId
                        },
                        {
                            invoice_number: 'INV-' + Date.now() + '-B',
                            vendor_name: 'Test Vendor B',
                            subtotal: 1500.00,
                            total_amount: 1500.00,
                            created_by: userId
                        },
                        {
                            invoice_number: 'INV-' + Date.now() + '-C',
                            subtotal: 2000.00,
                            total_amount: 2000.00,
                            created_by: userId
                        }
                    ];
                    
                    for (const invoiceData of invoiceVariations) {
                        try {
                            const { data: invoiceResult, error: invoiceError } = await supabase
                                .from('invoices')
                                .insert(invoiceData)
                                .select();
                            
                            if (!invoiceError) {
                                log(`✅ Successfully inserted invoice with structure: ${Object.keys(invoiceData).join(', ')}`);
                                totalInserted++;
                                break; // Stop after first successful insert
                            } else {
                                log(`⚠️ Failed invoice variation: ${invoiceError.message}`);
                            }
                        } catch (e) {
                            log(`⚠️ Invoice error: ${e.message}`);
                        }
                    }
                }
                
                log(`🎉 Final fix completed! Total new records: ${totalInserted}`);
                
                if (totalInserted > 0) {
                    showStatus(`🎉 Successfully inserted ${totalInserted} additional records!`, 'success');
                } else {
                    showStatus('ℹ️ No additional records inserted, but existing data should work.', 'warning');
                }
                
            } catch (error) {
                log('❌ Final fix failed: ' + error.message);
                showStatus('❌ Final fix failed: ' + error.message, 'error');
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🚀 Create Final Fix';
            }
        }
        
        // Make functions global
        window.inspectSchema = inspectSchema;
        window.createFinalFix = createFinalFix;
    </script>
</body>
</html>
