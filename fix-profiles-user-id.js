// Fix missing user_id column in profiles table
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function fixProfilesUserId() {
  console.log('🚀 Starting profiles user_id column fix...');
  
  try {
    // First, let's check what columns currently exist in the profiles table
    logStep('Checking current profiles table structure...');
    
    const { data: currentProfiles, error: checkError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (checkError) {
      console.warn('Profiles table check warning:', checkError.message);
    } else {
      console.log('Current profiles table accessible');
    }

    // Add the missing user_id column and other essential columns to profiles table
    logStep('Adding user_id and other missing columns to profiles table...');
    
    const { error: profilesError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Add user_id column to profiles table (this is critical for authentication)
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE;
        
        -- Ensure all other essential columns exist
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS full_name TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS email TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'staff';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS status TEXT DEFAULT 'active';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS department_id UUID;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS avatar_url TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS phone TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS bio TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS location TEXT;
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS skills TEXT[];
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC';
        
        ALTER TABLE public.profiles 
        ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
        
        -- Add constraints if they don't exist
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.check_constraints 
            WHERE constraint_name = 'profiles_role_check'
          ) THEN
            ALTER TABLE public.profiles 
            ADD CONSTRAINT profiles_role_check 
            CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
          END IF;
        END $$;
        
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.check_constraints 
            WHERE constraint_name = 'profiles_account_type_check'
          ) THEN
            ALTER TABLE public.profiles 
            ADD CONSTRAINT profiles_account_type_check 
            CHECK (account_type IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
          END IF;
        END $$;
        
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.check_constraints 
            WHERE constraint_name = 'profiles_status_check'
          ) THEN
            ALTER TABLE public.profiles 
            ADD CONSTRAINT profiles_status_check 
            CHECK (status IN ('active', 'inactive', 'suspended', 'pending'));
          END IF;
        END $$;
        
        -- Create unique constraint on email if it doesn't exist
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'profiles_email_key'
          ) THEN
            ALTER TABLE public.profiles 
            ADD CONSTRAINT profiles_email_key UNIQUE (email);
          END IF;
        END $$;
        
        -- Create unique constraint on user_id if it doesn't exist
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'profiles_user_id_key'
          ) THEN
            ALTER TABLE public.profiles 
            ADD CONSTRAINT profiles_user_id_key UNIQUE (user_id);
          END IF;
        END $$;
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON public.profiles(user_id);
        CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
        CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
        CREATE INDEX IF NOT EXISTS idx_profiles_status ON public.profiles(status);
        CREATE INDEX IF NOT EXISTS idx_profiles_department_id ON public.profiles(department_id);
      `
    });

    if (profilesError) {
      throw new Error('Profiles columns addition failed: ' + profilesError.message);
    }

    logSuccess('user_id column and other missing columns added successfully');

    // Create a function to safely create/update profiles
    logStep('Creating profile management functions...');
    
    const { error: functionError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Function to create or update a profile
        CREATE OR REPLACE FUNCTION upsert_profile(
          p_user_id UUID,
          p_full_name TEXT,
          p_email TEXT,
          p_role TEXT DEFAULT 'staff',
          p_account_type TEXT DEFAULT NULL,
          p_status TEXT DEFAULT 'active',
          p_department_id UUID DEFAULT NULL,
          p_avatar_url TEXT DEFAULT NULL,
          p_phone TEXT DEFAULT NULL,
          p_bio TEXT DEFAULT NULL,
          p_location TEXT DEFAULT NULL
        )
        RETURNS UUID AS $$
        DECLARE
          profile_id UUID;
        BEGIN
          -- Set account_type to role if not provided
          IF p_account_type IS NULL THEN
            p_account_type := p_role;
          END IF;
          
          INSERT INTO public.profiles (
            user_id, full_name, email, role, account_type, status,
            department_id, avatar_url, phone, bio, location,
            preferences, settings, notification_preferences,
            timezone, created_at, updated_at
          ) VALUES (
            p_user_id, p_full_name, p_email, p_role, p_account_type, p_status,
            p_department_id, p_avatar_url, p_phone, p_bio, p_location,
            '{}', '{}', '{}',
            'UTC', NOW(), NOW()
          )
          ON CONFLICT (user_id) 
          DO UPDATE SET
            full_name = EXCLUDED.full_name,
            email = EXCLUDED.email,
            role = EXCLUDED.role,
            account_type = EXCLUDED.account_type,
            status = EXCLUDED.status,
            department_id = EXCLUDED.department_id,
            avatar_url = EXCLUDED.avatar_url,
            phone = EXCLUDED.phone,
            bio = EXCLUDED.bio,
            location = EXCLUDED.location,
            updated_at = NOW()
          RETURNING id INTO profile_id;
          
          -- Log the profile action
          INSERT INTO public.system_activities (
            user_id, action, description, entity_type, entity_id,
            metadata, created_at
          ) VALUES (
            p_user_id, 'profile_updated', 
            'Profile updated for ' || p_full_name,
            'profile', profile_id,
            jsonb_build_object('email', p_email, 'role', p_role),
            NOW()
          );
          
          RETURN profile_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to get profile by user_id
        CREATE OR REPLACE FUNCTION get_profile_by_user_id(p_user_id UUID)
        RETURNS TABLE (
          id UUID,
          user_id UUID,
          full_name TEXT,
          email TEXT,
          role TEXT,
          account_type TEXT,
          status TEXT,
          department_id UUID,
          avatar_url TEXT,
          phone TEXT,
          bio TEXT,
          location TEXT,
          skills TEXT[],
          preferences JSONB,
          settings JSONB,
          notification_preferences JSONB,
          timezone TEXT,
          last_login TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE,
          updated_at TIMESTAMP WITH TIME ZONE
        ) AS $$
        BEGIN
          RETURN QUERY
          SELECT 
            p.id, p.user_id, p.full_name, p.email, p.role, p.account_type, p.status,
            p.department_id, p.avatar_url, p.phone, p.bio, p.location, p.skills,
            p.preferences, p.settings, p.notification_preferences, p.timezone,
            p.last_login, p.created_at, p.updated_at
          FROM public.profiles p
          WHERE p.user_id = p_user_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Grant execute permissions
        GRANT EXECUTE ON FUNCTION upsert_profile TO authenticated;
        GRANT EXECUTE ON FUNCTION upsert_profile TO anon;
        GRANT EXECUTE ON FUNCTION get_profile_by_user_id TO authenticated;
        GRANT EXECUTE ON FUNCTION get_profile_by_user_id TO anon;
      `
    });

    if (functionError) {
      console.warn('Function creation warning:', functionError.message);
    } else {
      logSuccess('Profile management functions created successfully');
    }

    // Test the fix by checking all columns are accessible
    logStep('Testing all profiles table columns...');
    
    const { data: allColumnsTest, error: allColumnsError } = await supabase
      .from('profiles')
      .select('id, user_id, full_name, email, role, account_type, status, department_id, avatar_url, phone, bio, location, skills, preferences, settings, notification_preferences, timezone, last_login, created_at, updated_at')
      .limit(3);
    
    if (allColumnsError) {
      console.warn('All columns test warning:', allColumnsError.message);
    } else {
      logSuccess(`All columns accessible. Found ${allColumnsTest?.length || 0} profiles.`);
    }

    // Test the upsert function
    logStep('Testing profile upsert function...');
    
    try {
      // Generate a test UUID (this won't actually link to a real auth user, but tests the function)
      const testUserId = '********-0000-0000-0000-********0001';
      
      const { data: testProfileId, error: testError } = await supabase.rpc('upsert_profile', {
        p_user_id: testUserId,
        p_full_name: 'Test User Profile Fix',
        p_email: '<EMAIL>',
        p_role: 'staff',
        p_status: 'active'
      });
      
      if (testError) {
        console.warn('Test profile upsert warning:', testError.message);
      } else {
        logSuccess(`Test profile upserted successfully with ID: ${testProfileId}`);
      }
    } catch (testErr) {
      console.warn('Test profile upsert error:', testErr.message);
    }

    logSuccess('🎉 PROFILES USER_ID FIX COMPLETED SUCCESSFULLY!');
    console.log('\n🚀 PROFILES TABLE NOW INCLUDES:');
    console.log('✅ user_id - Links to auth.users table (CRITICAL for authentication)');
    console.log('✅ full_name - User\'s full name');
    console.log('✅ email - User\'s email address');
    console.log('✅ role - User role (admin, manager, staff, etc.)');
    console.log('✅ account_type - Account type classification');
    console.log('✅ status - Account status (active, inactive, etc.)');
    console.log('✅ department_id - Department assignment');
    console.log('✅ avatar_url - Profile picture URL');
    console.log('✅ phone - Phone number');
    console.log('✅ bio - User biography');
    console.log('✅ location - User location');
    console.log('✅ skills - User skills array');
    console.log('✅ preferences - User preferences JSON');
    console.log('✅ settings - User settings JSON');
    console.log('✅ notification_preferences - Notification settings');
    console.log('✅ timezone - User timezone');
    console.log('✅ last_login - Last login timestamp');
    console.log('\n🎯 Profile creation and authentication should now work without errors!');
    
    return true;

  } catch (error) {
    logError(`Profiles user_id fix failed: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
}

// Run the fix
fixProfilesUserId()
  .then((success) => {
    if (success) {
      console.log('\n🎉 SUCCESS: Profiles user_id fix completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Profiles user_id fix encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
