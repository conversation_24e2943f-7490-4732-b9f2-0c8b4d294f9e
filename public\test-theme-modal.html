<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Modal Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            backdrop-filter: blur(4px);
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .modal-content {
            background: white;
            border-radius: 12px;
            padding: 24px;
            width: 90vw;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            animation: modalSlideIn 0.3s ease-out;
        }
        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        .modal-header {
            margin-bottom: 20px;
        }
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0 0 8px 0;
            color: #1f2937;
        }
        .modal-description {
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.5;
        }
        .theme-options {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin: 20px 0;
        }
        @media (min-width: 640px) {
            .theme-options {
                flex-direction: row;
            }
        }
        .theme-option {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: white;
        }
        .theme-option:hover {
            background: #f9fafb;
            border-color: #d1d5db;
        }
        .theme-icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
        }
        .theme-info {
            text-align: left;
        }
        .theme-name {
            font-weight: 500;
            font-size: 0.875rem;
            color: #1f2937;
            margin: 0;
        }
        .theme-desc {
            font-size: 0.75rem;
            color: #6b7280;
            margin: 2px 0 0 0;
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            margin-top: 24px;
        }
        .close-btn {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .close-btn:hover {
            background: #e5e7eb;
        }
        .info-section {
            background: #f0f9ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #0ea5e9;
        }
        .responsive-test {
            background: #fef3c7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #f59e0b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Theme Modal Responsiveness Test</h1>
        <p>Test the responsive behavior of the theme preference modal on different screen sizes.</p>
        
        <div class="info-section">
            <h3>✅ Responsive Improvements Made:</h3>
            <ul>
                <li><strong>Width:</strong> 90vw with max-width of 400px (was fixed sm:max-w-md)</li>
                <li><strong>Padding:</strong> Responsive padding (p-4 on mobile, sm:p-6 on larger screens)</li>
                <li><strong>Layout:</strong> Vertical stack on mobile, horizontal on desktop</li>
                <li><strong>Typography:</strong> Responsive text sizes</li>
                <li><strong>Interactive Elements:</strong> Better touch targets for mobile</li>
            </ul>
        </div>
        
        <div class="responsive-test">
            <h4>📱 Responsive Features:</h4>
            <ul>
                <li>Modal adapts to screen width (90% of viewport)</li>
                <li>Theme options stack vertically on mobile</li>
                <li>Touch-friendly button sizes</li>
                <li>Proper spacing and typography scaling</li>
            </ul>
        </div>
        
        <button class="button" onclick="showModal()">
            🎨 Test Theme Modal
        </button>
        
        <button class="button" onclick="resetThemeDialog()">
            🔄 Reset Theme Dialog (will show on next app load)
        </button>
        
        <button class="button" onclick="openDashboard()">
            📊 Open Dashboard
        </button>
        
        <div class="info-section">
            <h4>🔧 Technical Details:</h4>
            <p><strong>Before:</strong> Fixed width modal that could be too wide on mobile devices</p>
            <p><strong>After:</strong> Responsive modal that:</p>
            <ul>
                <li>Uses 90% of viewport width on small screens</li>
                <li>Has a maximum width of 400px on larger screens</li>
                <li>Includes interactive theme selection buttons</li>
                <li>Automatically closes when a theme is selected</li>
                <li>Has proper spacing and typography for all screen sizes</li>
            </ul>
        </div>
    </div>
    
    <!-- Modal -->
    <div id="themeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Theme Preference</h2>
                <p class="modal-description">
                    Try our dark mode for a more comfortable viewing experience! 
                    Click the theme switcher button in the top-right corner to toggle between light, dark, and system modes.
                </p>
            </div>
            
            <div class="theme-options">
                <div class="theme-option" onclick="selectTheme('light')">
                    <svg class="theme-icon" fill="currentColor" viewBox="0 0 20 20" style="color: #eab308;">
                        <path fill-rule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clip-rule="evenodd" />
                    </svg>
                    <div class="theme-info">
                        <div class="theme-name">Light</div>
                        <div class="theme-desc">Bright and clean</div>
                    </div>
                </div>
                
                <div class="theme-option" onclick="selectTheme('dark')">
                    <svg class="theme-icon" fill="currentColor" viewBox="0 0 20 20" style="color: #3b82f6;">
                        <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                    </svg>
                    <div class="theme-info">
                        <div class="theme-name">Dark</div>
                        <div class="theme-desc">Easy on the eyes</div>
                    </div>
                </div>
                
                <div class="theme-option" onclick="selectTheme('system')">
                    <svg class="theme-icon" fill="currentColor" viewBox="0 0 20 20" style="color: #6b7280;">
                        <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
                    </svg>
                    <div class="theme-info">
                        <div class="theme-name">System</div>
                        <div class="theme-desc">Follows device</div>
                    </div>
                </div>
            </div>
            
            <div class="modal-footer">
                <button class="close-btn" onclick="hideModal()">Got it!</button>
            </div>
        </div>
    </div>
    
    <script>
        function showModal() {
            document.getElementById('themeModal').classList.add('show');
        }
        
        function hideModal() {
            document.getElementById('themeModal').classList.remove('show');
        }
        
        function selectTheme(theme) {
            alert(`Selected theme: ${theme}`);
            hideModal();
        }
        
        function resetThemeDialog() {
            localStorage.removeItem('hasSeenThemeDialog');
            alert('Theme dialog reset! It will show again when you next load the app.');
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Close modal when clicking outside
        document.getElementById('themeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal();
            }
        });
        
        // Test responsiveness
        window.addEventListener('resize', function() {
            console.log('Window size:', window.innerWidth, 'x', window.innerHeight);
        });
    </script>
</body>
</html>
