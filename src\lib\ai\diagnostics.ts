import { supabase } from '@/integrations/supabase/client';

export interface AIErrorDetails {
  type: 'configuration' | 'network' | 'rate_limit' | 'authentication' | 'processing' | 'unknown';
  message: string;
  userMessage: string;
  suggestions: string[];
  timestamp: Date;
  context?: string;
}

export interface AIServiceStatus {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  environment: {
    openAI: 'configured' | 'missing';
    supabase: 'configured' | 'missing';
  };
  timestamp: string;
  error?: string;
}

export class AIDiagnostics {
  /**
   * Test the health of AI services
   */
  static async checkAIServices(): Promise<AIServiceStatus[]> {
    const services = ['analyze-document', 'ai-file-analyzer', 'advanced-document-analysis'];
    const results: AIServiceStatus[] = [];

    for (const service of services) {
      try {
        const { data, error } = await supabase.functions.invoke(service, {
          body: { healthCheck: true }
        });

        if (error) {
          results.push({
            service,
            status: 'unhealthy',
            environment: { openAI: 'unknown', supabase: 'unknown' },
            timestamp: new Date().toISOString(),
            error: error.message
          });
        } else {
          results.push({
            service,
            status: data?.status === 'healthy' ? 'healthy' : 'unhealthy',
            environment: data?.environment || { openAI: 'unknown', supabase: 'unknown' },
            timestamp: data?.timestamp || new Date().toISOString()
          });
        }
      } catch (error) {
        results.push({
          service,
          status: 'unhealthy',
          environment: { openAI: 'unknown', supabase: 'unknown' },
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Test document analysis with a simple text
   */
  static async testDocumentAnalysis(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      const testContent = "This is a test document for AI analysis. It contains sample text to verify the document analysis functionality is working correctly.";
      
      const { data, error } = await supabase.functions.invoke('analyze-document', {
        body: {
          content: testContent,
          fileName: 'test-document.txt',
          type: 'test'
        }
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Test file analyzer with sample data
   */
  static async testFileAnalyzer(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      const testContent = "Name,Age,Department\nJohn Doe,30,Engineering\nJane Smith,25,Marketing\nBob Johnson,35,Sales";
      
      const { data, error } = await supabase.functions.invoke('ai-file-analyzer', {
        body: {
          fileName: 'test-data.csv',
          fileType: 'text/csv',
          content: testContent,
          userId: (await supabase.auth.getUser()).data.user?.id
        }
      });

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Get comprehensive AI system status
   */
  static async getSystemStatus(): Promise<{
    overall: 'healthy' | 'degraded' | 'unhealthy';
    services: AIServiceStatus[];
    recommendations: string[];
  }> {
    const services = await this.checkAIServices();
    const healthyServices = services.filter(s => s.status === 'healthy').length;
    const totalServices = services.length;

    let overall: 'healthy' | 'degraded' | 'unhealthy';
    const recommendations: string[] = [];

    if (healthyServices === totalServices) {
      overall = 'healthy';
    } else if (healthyServices > 0) {
      overall = 'degraded';
      recommendations.push('Some AI services are experiencing issues. Check service logs for details.');
    } else {
      overall = 'unhealthy';
      recommendations.push('All AI services are down. Check OpenAI API key configuration.');
    }

    // Check for common issues
    const missingOpenAI = services.some(s => s.environment.openAI === 'missing');
    const missingSupabase = services.some(s => s.environment.supabase === 'missing');

    if (missingOpenAI) {
      recommendations.push('OpenAI API key is not configured. Add OPENAI_API_KEY to Supabase Edge Functions secrets.');
    }

    if (missingSupabase) {
      recommendations.push('Supabase configuration is missing. Check SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.');
    }

    return {
      overall,
      services,
      recommendations
    };
  }

  /**
   * Run all diagnostic tests
   */
  static async runFullDiagnostics(): Promise<{
    systemStatus: Awaited<ReturnType<typeof this.getSystemStatus>>;
    documentAnalysisTest: Awaited<ReturnType<typeof this.testDocumentAnalysis>>;
    fileAnalyzerTest: Awaited<ReturnType<typeof this.testFileAnalyzer>>;
  }> {
    const [systemStatus, documentAnalysisTest, fileAnalyzerTest] = await Promise.all([
      this.getSystemStatus(),
      this.testDocumentAnalysis(),
      this.testFileAnalyzer()
    ]);

    return {
      systemStatus,
      documentAnalysisTest,
      fileAnalyzerTest
    };
  }

  /**
   * Enhanced error analysis
   */
  static analyzeAIError(error: any, context?: string): AIErrorDetails {
    const timestamp = new Date();
    const errorMessage = error.message || error.toString();

    // API Key issues
    if (errorMessage.includes('API key') || errorMessage.includes('authentication') || errorMessage.includes('unauthorized')) {
      return {
        type: 'authentication',
        message: errorMessage,
        userMessage: 'AI service authentication failed. Please check your API key configuration.',
        suggestions: [
          'Verify OpenAI API key is correctly set in environment variables',
          'Check if the API key has sufficient credits',
          'Ensure the API key has the correct permissions'
        ],
        timestamp,
        context
      };
    }

    // Rate limiting
    if (errorMessage.includes('rate limit') || errorMessage.includes('quota') || errorMessage.includes('429')) {
      return {
        type: 'rate_limit',
        message: errorMessage,
        userMessage: 'AI service is temporarily busy due to high demand. Please try again in a moment.',
        suggestions: [
          'Wait a few seconds before retrying',
          'Consider upgrading your API plan for higher limits',
          'Implement request queuing for better handling'
        ],
        timestamp,
        context
      };
    }

    // Network issues
    if (errorMessage.includes('network') || errorMessage.includes('fetch') || errorMessage.includes('timeout') || errorMessage.includes('ECONNREFUSED')) {
      return {
        type: 'network',
        message: errorMessage,
        userMessage: 'Network connectivity issue. Please check your connection and try again.',
        suggestions: [
          'Check your internet connection',
          'Verify firewall settings allow API access',
          'Try again in a few moments'
        ],
        timestamp,
        context
      };
    }

    // Configuration issues
    if (errorMessage.includes('configuration') || errorMessage.includes('missing') || errorMessage.includes('undefined')) {
      return {
        type: 'configuration',
        message: errorMessage,
        userMessage: 'AI service configuration issue. Please contact your administrator.',
        suggestions: [
          'Check environment variables are properly set',
          'Verify all required services are configured',
          'Review system configuration settings'
        ],
        timestamp,
        context
      };
    }

    // Processing errors
    if (errorMessage.includes('processing') || errorMessage.includes('invalid') || errorMessage.includes('format')) {
      return {
        type: 'processing',
        message: errorMessage,
        userMessage: 'There was an issue processing your request. Please try rephrasing or try again.',
        suggestions: [
          'Try rephrasing your request',
          'Check if your input is in the correct format',
          'Reduce the complexity of your request'
        ],
        timestamp,
        context
      };
    }

    // Unknown errors
    return {
      type: 'unknown',
      message: errorMessage,
      userMessage: 'AI service encountered an unexpected issue. Please try again later.',
      suggestions: [
        'Try again in a few moments',
        'Contact support if the issue persists',
        'Check system status for any ongoing issues'
      ],
      timestamp,
      context
    };
  }
}

/**
 * Enhanced error handling function
 */
export const handleAIError = (error: any, context?: string): string => {
  const errorDetails = AIDiagnostics.analyzeAIError(error, context);

  // Log detailed error for debugging
  console.error(`AI Error [${errorDetails.type}]${context ? ` in ${context}` : ''}:`, {
    message: errorDetails.message,
    suggestions: errorDetails.suggestions,
    timestamp: errorDetails.timestamp
  });

  return errorDetails.userMessage;
};

/**
 * Enhanced error handler for AI operations with structured response
 */
export function handleAIErrorStructured(error: any): {
  title: string;
  message: string;
  actionable: boolean;
  action?: string;
} {
  const errorMessage = error?.message || error?.error || error || 'Unknown error occurred';
  const errorString = typeof errorMessage === 'string' ? errorMessage.toLowerCase() : '';

  // API Key related errors
  if (errorString.includes('api key') || errorString.includes('authentication')) {
    return {
      title: 'AI Service Configuration Error',
      message: 'The AI service is not properly configured. Please contact your administrator.',
      actionable: true,
      action: 'Contact administrator to configure OpenAI API key'
    };
  }

  // Network errors
  if (errorString.includes('network') || errorString.includes('fetch') || errorString.includes('connection')) {
    return {
      title: 'Network Error',
      message: 'Unable to connect to AI services. Please check your internet connection and try again.',
      actionable: true,
      action: 'Check internet connection and retry'
    };
  }

  // Service unavailable
  if (errorString.includes('service unavailable') || errorString.includes('503')) {
    return {
      title: 'Service Temporarily Unavailable',
      message: 'The AI analysis service is temporarily unavailable. Please try again in a few minutes.',
      actionable: true,
      action: 'Wait a few minutes and try again'
    };
  }

  // File size errors
  if (errorString.includes('file size') || errorString.includes('too large')) {
    return {
      title: 'File Too Large',
      message: 'The selected file is too large for analysis. Please choose a smaller file.',
      actionable: true,
      action: 'Select a smaller file (under 10MB)'
    };
  }

  // Rate limiting
  if (errorString.includes('rate limit') || errorString.includes('quota')) {
    return {
      title: 'Service Limit Reached',
      message: 'The AI service has reached its usage limit. Please try again later.',
      actionable: true,
      action: 'Wait and try again later'
    };
  }

  // Generic error
  return {
    title: 'AI Analysis Error',
    message: errorMessage,
    actionable: false
  };
}

// All diagnostics and error handling are correct.
