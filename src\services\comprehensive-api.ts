/**
 * BMD TECH HUB Comprehensive API Service
 *
 * Copyright (c) 2024 BMD TECH HUB / IFEANYI OBIBI Technologies
 * Licensed under BMD TECH HUB Proprietary License
 *
 * Contact: <EMAIL>
 * Website: https://bmdtechhub.com
 */

import type { Database } from '@/integrations/supabase/types';

/**
 * BMD TECH HUB Comprehensive API Service
 * Proprietary API service for CTN Nigeria platform
 * Powered by BMD TECH HUB / IFEANYI OBIBI Technologies
 */

type Tables = Database['public']['Tables'];
type Profile = Tables['profiles']['Row'];
type Project = Tables['projects']['Row'];
type ProjectAssignment = Tables['project_assignments']['Row'];
type Memo = Tables['memos']['Row'];
type Report = Tables['reports']['Row'];
type Task = Tables['tasks']['Row'];
type Department = Tables['departments']['Row'];

export class ComprehensiveAPI {
  // Profile Management
  static async getCurrentUserProfile(): Promise<Profile | null> {
    try {
      const { data: user } = await bmdDatabase.auth.getUser();
      if (!user.user) return null;

      const { data, error } = await bmdDatabase
        .rpc('get_user_profile', { auth_user_id: user.user.id });

      if (error) throw error;
      return data?.[0] || null;
    } catch (error) {
      console.error('Error getting current user profile:', error);
      return null;
    }
  }

  static async getAllProfiles(): Promise<Profile[]> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('full_name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all profiles:', error);
      return [];
    }
  }

  static async updateProfile(id: string, updates: Partial<Profile>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('profiles')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating profile:', error);
      return false;
    }
  }

  // Project Management
  static async createProject(projectData: {
    name: string;
    description?: string;
    client_name?: string;
    budget?: number;
    location?: string;
    start_date?: string;
    end_date?: string;
    status?: string;
    priority?: string;
    manager_id?: string;
    department_id?: string;
  }): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('create_project', {
        p_name: projectData.name,
        p_description: projectData.description,
        p_client_name: projectData.client_name,
        p_budget: projectData.budget,
        p_location: projectData.location,
        p_start_date: projectData.start_date,
        p_end_date: projectData.end_date,
        p_status: projectData.status || 'planning',
        p_priority: projectData.priority || 'medium',
        p_manager_id: projectData.manager_id,
        p_department_id: projectData.department_id
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating project:', error);
      return null;
    }
  }

  static async getAllProjects(): Promise<Project[]> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          manager:profiles!projects_manager_id_fkey(id, full_name, email),
          creator:profiles!projects_created_by_fkey(id, full_name, email),
          department:departments(id, name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all projects:', error);
      return [];
    }
  }

  static async getProjectById(id: string): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          manager:profiles!projects_manager_id_fkey(id, full_name, email),
          creator:profiles!projects_created_by_fkey(id, full_name, email),
          department:departments(id, name),
          assignments:project_assignments(
            *,
            assignee:profiles!project_assignments_assigned_to_fkey(id, full_name, email, role)
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting project by ID:', error);
      return null;
    }
  }

  static async updateProject(id: string, updates: Partial<Project>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('projects')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating project:', error);
      return false;
    }
  }

  // Project Member Management
  static async assignProjectMember(
    projectId: string,
    assignedTo: string,
    role: string = 'team_member',
    hoursAllocated: number = 40
  ): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('assign_project_member', {
        p_project_id: projectId,
        p_assigned_to: assignedTo,
        p_role: role,
        p_hours_allocated: hoursAllocated
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error assigning project member:', error);
      return null;
    }
  }

  static async getProjectAssignments(projectId: string): Promise<ProjectAssignment[]> {
    try {
      const { data, error } = await supabase
        .from('project_assignments')
        .select(`
          *,
          assignee:profiles!project_assignments_assigned_to_fkey(id, full_name, email, role, avatar_url),
          project:projects(id, name, status)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting project assignments:', error);
      return [];
    }
  }

  static async updateProjectAssignment(id: string, updates: Partial<ProjectAssignment>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('project_assignments')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error updating project assignment:', error);
      return false;
    }
  }

  // Memo Management
  static async submitMemo(memoData: {
    title: string;
    content: string;
    memo_type?: string;
    priority?: string;
    visibility?: string;
    target_audience?: string[];
    department_id?: string;
    effective_date?: string;
    expiry_date?: string;
    tags?: string[];
  }): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('submit_memo', {
        p_title: memoData.title,
        p_content: memoData.content,
        p_memo_type: memoData.memo_type || 'general',
        p_priority: memoData.priority || 'medium',
        p_visibility: memoData.visibility || 'department',
        p_target_audience: memoData.target_audience,
        p_department_id: memoData.department_id,
        p_effective_date: memoData.effective_date,
        p_expiry_date: memoData.expiry_date,
        p_tags: memoData.tags
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error submitting memo:', error);
      return null;
    }
  }

  static async getAllMemos(): Promise<Memo[]> {
    try {
      const { data, error } = await supabase
        .from('memos')
        .select(`
          *,
          creator:profiles!memos_created_by_fkey(id, full_name, email, avatar_url),
          department:departments(id, name)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all memos:', error);
      return [];
    }
  }

  static async getMemoById(id: string): Promise<Memo | null> {
    try {
      const { data, error } = await supabase
        .from('memos')
        .select(`
          *,
          creator:profiles!memos_created_by_fkey(id, full_name, email, avatar_url),
          department:departments(id, name)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting memo by ID:', error);
      return null;
    }
  }

  // Report Management
  static async submitReport(reportData: {
    title: string;
    description?: string;
    report_type?: string;
    priority?: string;
    department_id?: string;
    project_id?: string;
    due_date?: string;
    content?: any;
    attachments?: any[];
  }): Promise<string | null> {
    try {
      const { data, error } = await supabase.rpc('submit_report', {
        p_title: reportData.title,
        p_description: reportData.description,
        p_report_type: reportData.report_type || 'general',
        p_priority: reportData.priority || 'medium',
        p_department_id: reportData.department_id,
        p_project_id: reportData.project_id,
        p_due_date: reportData.due_date,
        p_content: reportData.content || {},
        p_attachments: reportData.attachments || []
      });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error submitting report:', error);
      return null;
    }
  }

  static async getAllReports(): Promise<Report[]> {
    try {
      const { data, error } = await supabase
        .from('reports')
        .select(`
          *,
          submitter:profiles!reports_submitted_by_fkey(id, full_name, email, avatar_url),
          reviewer:profiles!reports_reviewed_by_fkey(id, full_name, email),
          department:departments(id, name),
          project:projects(id, name, status)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all reports:', error);
      return [];
    }
  }

  static async getReportById(id: string): Promise<Report | null> {
    try {
      const { data, error } = await supabase
        .from('reports')
        .select(`
          *,
          submitter:profiles!reports_submitted_by_fkey(id, full_name, email, avatar_url),
          reviewer:profiles!reports_reviewed_by_fkey(id, full_name, email),
          department:departments(id, name),
          project:projects(id, name, status)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error getting report by ID:', error);
      return null;
    }
  }

  // Department Management
  static async getAllDepartments(): Promise<Department[]> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select(`
          *,
          manager:profiles!departments_manager_id_fkey(id, full_name, email)
        `)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error getting all departments:', error);
      return [];
    }
  }

  // System Activities
  static async logActivity(
    action: string,
    description: string,
    entityType?: string,
    entityId?: string,
    metadata?: any,
    severity: string = 'info',
    category: string = 'general'
  ): Promise<boolean> {
    try {
      const currentProfile = await this.getCurrentUserProfile();
      
      const { error } = await supabase.rpc('log_system_activity', {
        p_user_id: currentProfile?.id,
        p_action: action,
        p_description: description,
        p_entity_type: entityType,
        p_entity_id: entityId,
        p_metadata: metadata || {},
        p_severity: severity,
        p_category: category
      });

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error logging activity:', error);
      return false;
    }
  }

  // Health Check
  static async healthCheck(): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);

      return !error;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}

export default ComprehensiveAPI;
