import { supabase } from "@/integrations/supabase/client";

export interface MemoFormData {
  title: string;
  subject?: string;
  content: string;
  purpose?: string;
  to_recipient: string;
  account_details?: string;
  total_amount?: number;
  memo_date?: string;
  payment_items?: Array<{
    description: string;
    amount: string | number;
  }>;
  department?: string;
}

export interface MemoAttachment {
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
}

export class MemoAPI {
  /**
   * Create a new memo with all required fields
   */
  static async createMemo(formData: MemoFormData, userId: string): Promise<{ data: any; error: any }> {
    try {
      // Validate required fields
      if (!formData.title?.trim()) {
        throw new Error("Title is required");
      }
      if (!formData.content?.trim()) {
        throw new Error("Content is required");
      }
      if (!formData.to_recipient?.trim()) {
        throw new Error("Recipient is required");
      }

      // Prepare memo data with all fields
      const memoData = {
        title: formData.title.trim(),
        subject: formData.subject?.trim() || formData.title.trim(),
        content: formData.content.trim(),
        purpose: formData.purpose?.trim() || formData.content.trim(),
        to_recipient: formData.to_recipient.trim(),
        account_details: formData.account_details?.trim() || null,
        total_amount: formData.total_amount || 0,
        memo_date: formData.memo_date || new Date().toISOString().split('T')[0],
        payment_items: formData.payment_items || null,
        created_by: userId,
        from_user: userId,
        status: 'pending' as const,
        department: formData.department || null,
      };

      const { data, error } = await supabase
        .from('memos')
        .insert([memoData])
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Get memos for a specific user
   */
  static async getUserMemos(userId: string, limit?: number): Promise<{ data: any[]; error: any }> {
    try {
      let query = supabase
        .from('memos')
        .select(`
          *
        `)
        .eq('created_by', userId)
        .order('created_at', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;
      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Get all memos (for managers/admins)
   */
  static async getAllMemos(limit?: number): Promise<{ data: any[]; error: any }> {
    try {
      let query = supabase
        .from('memos')
        .select(`
          *
        `)
        .order('created_at', { ascending: false });

      if (limit) {
        query = query.limit(limit);
      }

      const { data, error } = await query;
      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Update memo status (approve/reject)
   */
  static async updateMemoStatus(memoId: string, status: 'approved' | 'rejected', userId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('memos')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', memoId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Get memo by ID with full details
   */
  static async getMemoById(memoId: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('memos')
        .select(`
          *
        `)
        .eq('id', memoId)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Upload memo attachment
   */
  static async uploadAttachment(
    memoId: string, 
    file: File, 
    userId: string
  ): Promise<{ data: any; error: any }> {
    try {
      // Upload file to Supabase Storage
      const fileExt = file.name.split('.').pop();
      const fileName = `${memoId}/${Date.now()}.${fileExt}`;
      
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('memo-attachments')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      // Save attachment metadata to database
      const { data, error } = await supabase
        .from('memo_attachments')
        .insert([{
          memo_id: memoId,
          file_name: file.name,
          file_path: uploadData.path,
          file_type: file.type,
          file_size: file.size,
          uploaded_by: userId
        }])
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Get memo statistics for dashboard
   */
  static async getMemoStats(userId: string): Promise<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    thisMonth: number;
  }> {
    try {
      const { data: memos } = await this.getUserMemos(userId);
      
      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      
      const stats = {
        total: memos.length,
        pending: memos.filter(m => m.status === 'pending' || !m.status).length,
        approved: memos.filter(m => m.status === 'approved').length,
        rejected: memos.filter(m => m.status === 'rejected').length,
        thisMonth: memos.filter(m => new Date(m.created_at) >= thisMonth).length,
      };

      return stats;
    } catch (error) {
      console.error('Error getting memo stats:', error);
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        thisMonth: 0,
      };
    }
  }

  /**
   * Get all profiles for recipient selection
   */
  static async getProfiles(): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .order('full_name');

      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }
}
