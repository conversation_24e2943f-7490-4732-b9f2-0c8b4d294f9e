export interface DocumentAnalysis {
  id: string;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  analysis_status: 'pending' | 'completed' | 'error';
  analysis_result: {
    summary: string;
    keyPoints: string[];
    suggestedActions: string[];
    categories?: string[];
  } | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface AIResult {
  id: string;
  query_text: string;
  result_data: any;
  model_used: string;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface AIKnowledgeBase {
  id: string;
  title: string;
  content: string;
  document_type?: string | null;
  category: string | null;
  tags: string[] | null;
  analysis?: any;
  embedding?: string | null;
  indexed_at?: string | null;
  index_version?: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface AIInteraction {
  id: string;
  user_id: string | null;
  role: 'user' | 'assistant' | 'system';
  message: string;
  type?: string;
  query?: string | null;
  response?: string | null;
  actions?: any[] | null;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface DocumentAIAnalysis {
  id: string;
  document_id: string;
  analysis_type: string;
  analysis_result: any;
  confidence_level?: number | null;
  key_insights?: string[] | null;
  action_items?: string[] | null;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  analyzed_by: string | null;
  created_at: string;
  updated_at: string;
}

// New interfaces for Memo and PowerReport
export interface Memo {
  id: string;
  title: string;
  content: string;
  status: string;
  department: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface PowerReport {
  id: string;
  site_id: string;
  report_datetime: string;
  generator_runtime: number | null;
  diesel_level: number | null;
  comments: string | null;
  status: string;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}
