import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { AddMemo } from '@/components/staff/AddMemo'
import { MemoList } from '@/components/staff/MemoList'
import { MemoGeneration } from '@/components/staff/MemoGeneration'
import { FileText, Plus, List, DollarSign } from 'lucide-react'

export const Memos = () => {
  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold flex items-center gap-2'>
            <FileText className='h-6 w-6' />
            Staff Memos
          </h2>
          <p className='text-muted-foreground'>
            Create and manage your memo requests
          </p>
        </div>
      </div>

      <Tabs defaultValue='list' className='w-full'>
        <TabsList className='grid w-full grid-cols-3'>
          <TabsTrigger value='list' className='flex items-center gap-2'>
            <List className='h-4 w-4' />
            My Memos
          </TabsTrigger>
          <TabsTrigger value='create' className='flex items-center gap-2'>
            <Plus className='h-4 w-4' />
            Create Memo
          </TabsTrigger>
          <TabsTrigger value='payment' className='flex items-center gap-2'>
            <DollarSign className='h-4 w-4' />
            Payment Request
          </TabsTrigger>
        </TabsList>

        <TabsContent value='list' className='space-y-4'>
          <MemoList />
        </TabsContent>

        <TabsContent value='create' className='space-y-4'>
          <AddMemo />
        </TabsContent>

        <TabsContent value='payment' className='space-y-4'>
          <MemoGeneration />
        </TabsContent>
      </Tabs>
    </div>
  )
}
