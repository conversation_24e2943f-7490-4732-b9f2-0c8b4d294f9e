import { supabase } from '@/integrations/supabase/client';
import type { ApiResponse, PaginatedResponse } from '@/types/api';
import type {
    Battery,
    BatteryLocation,
    BatteryMaintenance,
    BatteryReading,
    BatterySearchParams,
    BatteryStats,
    BatteryTransfer,
    BatteryType,
    CreateBatteryForm,
    CreateBatteryReadingForm,
    CreateMaintenanceForm,
    CreateTransferForm
} from '@/types/battery';

class BatteryService {
  // Battery CRUD operations
  async getBatteries(params?: BatterySearchParams): Promise<ApiResponse<PaginatedResponse<Battery>>> {
    try {
      let query = supabase
        .from('batteries')
        .select(`
          *,
          battery_type:battery_types(*),
          current_location:battery_locations(*)
        `);

      // Apply filters
      if (params?.status?.length) {
        query = query.in('status', params.status);
      }
      if (params?.condition?.length) {
        query = query.in('condition', params.condition);
      }
      if (params?.battery_type_id?.length) {
        query = query.in('battery_type_id', params.battery_type_id);
      }
      if (params?.location_id?.length) {
        query = query.in('current_location_id', params.location_id);
      }
      if (params?.search) {
        query = query.or(`serial_number.ilike.%${params.search}%,notes.ilike.%${params.search}%`);
      }

      // Apply sorting
      const sortBy = params?.sort_by || 'created_at';
      const sortOrder = params?.sort_order || 'desc';
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const page = params?.page || 1;
      const limit = params?.limit || 20;
      const from = (page - 1) * limit;
      const to = from + limit - 1;

      const { data, error, count } = await query.range(from, to);

      if (error) throw error;

      return {
        success: true,
        data: {
          data: data || [],
          total: count || 0,
          page,
          limit,
          totalPages: Math.ceil((count || 0) / limit)
        },
        error: null
      };
    } catch (error) {
      console.error('Error fetching batteries:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch batteries'
      };
    }
  }

  async getBattery(id: string): Promise<ApiResponse<Battery>> {
    try {
      const { data, error } = await supabase
        .from('batteries')
        .select(`
          *,
          battery_type:battery_types(*),
          current_location:battery_locations(*)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      return {
        success: true,
        data,
        error: null
      };
    } catch (error) {
      console.error('Error fetching battery:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch battery'
      };
    }
  }

  async createBattery(batteryData: CreateBatteryForm): Promise<ApiResponse<Battery>> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      
      const { data, error } = await supabase
        .from('batteries')
        .insert({
          ...batteryData,
          created_by: user?.id,
          profile_id: user?.id
        })
        .select(`
          *,
          battery_type:battery_types(*),
          current_location:battery_locations(*)
        `)
        .single();

      if (error) throw error;

      return {
        success: true,
        data,
        error: null
      };
    } catch (error) {
      console.error('Error creating battery:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create battery'
      };
    }
  }

  async updateBattery(id: string, updates: Partial<CreateBatteryForm>): Promise<ApiResponse<Battery>> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('batteries')
        .update({
          ...updates,
          updated_by: user?.id
        })
        .eq('id', id)
        .select(`
          *,
          battery_type:battery_types(*),
          current_location:battery_locations(*)
        `)
        .single();

      if (error) throw error;

      return {
        success: true,
        data,
        error: null
      };
    } catch (error) {
      console.error('Error updating battery:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to update battery'
      };
    }
  }

  async deleteBattery(id: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from('batteries')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return {
        success: true,
        data: undefined,
        error: null
      };
    } catch (error) {
      console.error('Error deleting battery:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to delete battery'
      };
    }
  }

  // Battery Types
  async getBatteryTypes(): Promise<ApiResponse<BatteryType[]>> {
    try {
      const { data, error } = await supabase
        .from('battery_types')
        .select('*')
        .order('name');

      if (error) throw error;

      return {
        success: true,
        data: data || [],
        error: null
      };
    } catch (error) {
      console.error('Error fetching battery types:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch battery types'
      };
    }
  }

  // Battery Locations
  async getBatteryLocations(): Promise<ApiResponse<BatteryLocation[]>> {
    try {
      const { data, error } = await supabase
        .from('battery_locations')
        .select('*')
        .order('name');

      if (error) throw error;

      return {
        success: true,
        data: data || [],
        error: null
      };
    } catch (error) {
      console.error('Error fetching battery locations:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch battery locations'
      };
    }
  }

  // Battery Readings
  async createBatteryReading(readingData: CreateBatteryReadingForm): Promise<ApiResponse<BatteryReading>> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('battery_readings')
        .insert({
          ...readingData,
          created_by: user?.id,
          profile_id: user?.id,
          generated_by: user?.email || 'system'
        })
        .select(`
          *,
          battery:batteries(*)
        `)
        .single();

      if (error) throw error;

      return {
        success: true,
        data,
        error: null
      };
    } catch (error) {
      console.error('Error creating battery reading:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create battery reading'
      };
    }
  }

  // Battery Maintenance
  async createMaintenance(maintenanceData: CreateMaintenanceForm): Promise<ApiResponse<BatteryMaintenance>> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('battery_maintenance')
        .insert({
          ...maintenanceData,
          created_by: user?.id,
          profile_id: user?.id
        })
        .select(`
          *,
          battery:batteries(*),
          technician:profiles(*)
        `)
        .single();

      if (error) throw error;

      return {
        success: true,
        data,
        error: null
      };
    } catch (error) {
      console.error('Error creating maintenance record:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create maintenance record'
      };
    }
  }

  // Battery Transfers
  async createTransfer(transferData: CreateTransferForm): Promise<ApiResponse<BatteryTransfer>> {
    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();

      const { data, error } = await supabase
        .from('battery_transfers')
        .insert({
          ...transferData,
          created_by: user?.id,
          profile_id: user?.id
        })
        .select(`
          *,
          battery:batteries(*),
          from_location:battery_locations!from_location_id(*),
          to_location:battery_locations!to_location_id(*)
        `)
        .single();

      if (error) throw error;

      // Update battery location
      if (data) {
        await this.updateBattery(transferData.battery_id, {
          current_location_id: transferData.to_location_id
        });
      }

      return {
        success: true,
        data,
        error: null
      };
    } catch (error) {
      console.error('Error creating transfer:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create transfer'
      };
    }
  }

  // Dashboard and Statistics
  async getBatteryStats(): Promise<ApiResponse<BatteryStats>> {
    try {
      // This would typically be a database function or complex query
      // For now, we'll simulate with multiple queries
      const { data: batteries } = await supabase
        .from('batteries')
        .select('status, condition, created_at, battery_type:battery_types(capacity_ah)');

      const { data: readings } = await supabase
        .from('battery_readings')
        .select('state_of_charge')
        .order('reading_date', { ascending: false })
        .limit(100);

      if (!batteries) throw new Error('Failed to fetch battery data');

      const stats: BatteryStats = {
        total_batteries: batteries.length,
        active_batteries: batteries.filter(b => b.status === 'active').length,
        maintenance_batteries: batteries.filter(b => b.status === 'maintenance').length,
        retired_batteries: batteries.filter(b => b.status === 'retired').length,
        average_age_months: 0, // Calculate based on created_at
        batteries_needing_maintenance: 0, // Calculate based on maintenance schedule
        total_capacity_ah: batteries.reduce((sum, b) => sum + (b.battery_type?.capacity_ah || 0), 0),
        average_state_of_charge: readings?.length ? 
          readings.reduce((sum, r) => sum + (r.state_of_charge || 0), 0) / readings.length : 0
      };

      return {
        success: true,
        data: stats,
        error: null
      };
    } catch (error) {
      console.error('Error fetching battery stats:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Failed to fetch battery stats'
      };
    }
  }
}

export const batteryService = new BatteryService();
