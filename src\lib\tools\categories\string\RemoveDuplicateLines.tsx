import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Filter, List, Shuffle } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const RemoveDuplicateLines: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(true);
  const [trimWhitespace, setTrimWhitespace] = useState(true);
  const [removeEmpty, setRemoveEmpty] = useState(true);
  const [sortResult, setSortResult] = useState('none');
  const [showDuplicates, setShowDuplicates] = useState(false);
  const [result, setResult] = useState('');
  const [duplicates, setDuplicates] = useState<string[]>([]);

  const sortOptions = [
    { value: 'none', label: 'Keep Original Order' },
    { value: 'asc', label: 'Sort A-Z' },
    { value: 'desc', label: 'Sort Z-A' },
    { value: 'length-asc', label: 'Sort by Length (Short to Long)' },
    { value: 'length-desc', label: 'Sort by Length (Long to Short)' }
  ];

  const processText = () => {
    if (!inputText.trim()) {
      setResult('');
      setDuplicates([]);
      return;
    }

    try {
      let lines = inputText.split('\n');

      // Apply preprocessing
      if (trimWhitespace) {
        lines = lines.map(line => line.trim());
      }

      if (removeEmpty) {
        lines = lines.filter(line => line.length > 0);
      }

      // Track duplicates and unique lines
      const seen = new Set<string>();
      const uniqueLines: string[] = [];
      const duplicateLines: string[] = [];

      lines.forEach(line => {
        const compareKey = caseSensitive ? line : line.toLowerCase();
        
        if (seen.has(compareKey)) {
          if (!duplicateLines.includes(line)) {
            duplicateLines.push(line);
          }
        } else {
          seen.add(compareKey);
          uniqueLines.push(line);
        }
      });

      // Apply sorting
      let finalLines = [...uniqueLines];
      switch (sortResult) {
        case 'asc':
          finalLines.sort((a, b) => caseSensitive ? a.localeCompare(b) : a.toLowerCase().localeCompare(b.toLowerCase()));
          break;
        case 'desc':
          finalLines.sort((a, b) => caseSensitive ? b.localeCompare(a) : b.toLowerCase().localeCompare(a.toLowerCase()));
          break;
        case 'length-asc':
          finalLines.sort((a, b) => a.length - b.length || a.localeCompare(b));
          break;
        case 'length-desc':
          finalLines.sort((a, b) => b.length - a.length || a.localeCompare(b));
          break;
        default:
          // Keep original order
          break;
      }

      setResult(finalLines.join('\n'));
      setDuplicates(duplicateLines);
    } catch (error) {
      toast.error('Error processing text');
      setResult('');
      setDuplicates([]);
    }
  };

  useEffect(() => {
    processText();
  }, [inputText, caseSensitive, trimWhitespace, removeEmpty, sortResult]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const copyDuplicates = async () => {
    try {
      await navigator.clipboard.writeText(duplicates.join('\n'));
      toast.success('Duplicates copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy duplicates');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
    setDuplicates([]);
  };

  const loadSampleData = () => {
    const sampleData = `Apple
Banana
Cherry
Apple
Date
Banana
Elderberry
Cherry
Fig
Apple
Grape
Date`;
    setInputText(sampleData);
  };

  const getStats = () => {
    const originalLines = inputText.split('\n').filter(line => 
      removeEmpty ? line.trim().length > 0 : true
    );
    const uniqueLines = result.split('\n').filter(line => line.length > 0);
    const duplicateCount = duplicates.length;
    const removedCount = originalLines.length - uniqueLines.length;
    
    return {
      originalCount: originalLines.length,
      uniqueCount: uniqueLines.length,
      duplicateCount,
      removedCount
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-red-50 via-pink-50 to-rose-50 border-red-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-red-800">
            <div className="p-2 bg-red-100 rounded-lg">
              <Filter className="h-6 w-6 text-red-600" />
            </div>
            {title || 'Remove Duplicate Lines'}
          </CardTitle>
          <p className="text-red-600 text-sm">
            Remove duplicate lines from text while preserving order or sorting
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Options */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="case-sensitive"
                checked={caseSensitive}
                onCheckedChange={setCaseSensitive}
              />
              <label htmlFor="case-sensitive" className="text-sm font-medium">
                Case Sensitive
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="trim-whitespace"
                checked={trimWhitespace}
                onCheckedChange={setTrimWhitespace}
              />
              <label htmlFor="trim-whitespace" className="text-sm font-medium">
                Trim Whitespace
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="remove-empty"
                checked={removeEmpty}
                onCheckedChange={setRemoveEmpty}
              />
              <label htmlFor="remove-empty" className="text-sm font-medium">
                Remove Empty
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="show-duplicates"
                checked={showDuplicates}
                onCheckedChange={setShowDuplicates}
              />
              <label htmlFor="show-duplicates" className="text-sm font-medium">
                Show Duplicates
              </label>
            </div>
          </div>

          {/* Sort Options */}
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Sort Result:</label>
            <Select value={sortResult} onValueChange={setSortResult}>
              <SelectTrigger className="border-2 border-red-200 focus:border-red-400">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {sortOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span>📝</span>
                Lines to Process:
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleData}
                className="flex items-center gap-2"
              >
                <List className="h-4 w-4" />
                Sample
              </Button>
            </div>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter lines of text (one per line)..."
              className="min-h-[140px] resize-none border-2 border-red-200 focus:border-red-400"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.originalCount} → {stats.uniqueCount} lines
              </Badge>
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                🗑️ {stats.removedCount} removed
              </Badge>
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                🔄 {stats.duplicateCount} unique duplicates
              </Badge>
            </div>
          )}

          {/* Result */}
          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  Unique Lines:
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[140px] bg-gray-50 border-2 border-gray-200"
              />
            </div>
          )}

          {/* Duplicates Section */}
          {showDuplicates && duplicates.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>🔄</span>
                  Found Duplicates ({duplicates.length}):
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyDuplicates}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy Duplicates
                </Button>
              </div>
              <div className="max-h-32 overflow-y-auto p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div className="space-y-1">
                  {duplicates.map((duplicate, index) => (
                    <div key={index} className="text-sm font-mono bg-white px-2 py-1 rounded border">
                      {duplicate}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Clear Button */}
          <div className="flex justify-center">
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Processing Options
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Case Sensitivity:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li><strong>On:</strong> "Apple" ≠ "apple"</li>
                <li><strong>Off:</strong> "Apple" = "apple"</li>
              </ul>
              
              <p><strong className="text-green-600">Trim Whitespace:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Removes leading/trailing spaces</li>
                <li>"  text  " becomes "text"</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-purple-600">Remove Empty:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Removes blank lines</li>
                <li>Removes whitespace-only lines</li>
              </ul>
              
              <p><strong className="text-orange-600">Sorting:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Alphabetical (A-Z or Z-A)</li>
                <li>By length (short to long)</li>
                <li>Keep original order</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RemoveDuplicateLines;
