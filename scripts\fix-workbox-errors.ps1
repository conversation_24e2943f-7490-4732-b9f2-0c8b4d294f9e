# PowerShell script to fix persistent Workbox errors
Write-Host "🔧 Fixing persistent Workbox and service worker errors..." -ForegroundColor Cyan

# 1. Clear all cache directories
Write-Host "`n1. 🗑️  Clearing all cache directories..." -ForegroundColor Blue
$cacheDirs = @(
    "node_modules\.cache",
    "node_modules\.vite",
    ".vite",
    "dist",
    ".next",
    ".nuxt",
    "build"
)

foreach ($dir in $cacheDirs) {
    if (Test-Path $dir) {
        Remove-Item -Recurse -Force $dir
        Write-Host "   ✅ Removed $dir" -ForegroundColor Green
    }
}

# 2. Clear npm cache
Write-Host "`n2. 🧹 Clearing npm cache..." -ForegroundColor Blue
npm cache clean --force
Write-Host "   ✅ npm cache cleared" -ForegroundColor Green

# 3. Search and remove any workbox files
Write-Host "`n3. 🔍 Searching for Workbox files..." -ForegroundColor Blue
$workboxFiles = Get-ChildItem -Recurse -Include "*workbox*" -ErrorAction SilentlyContinue
if ($workboxFiles) {
    foreach ($file in $workboxFiles) {
        Remove-Item -Force $file.FullName
        Write-Host "   ✅ Removed Workbox file: $($file.FullName)" -ForegroundColor Green
    }
} else {
    Write-Host "   ✅ No Workbox files found" -ForegroundColor Green
}

# 4. Search and remove any service worker files
Write-Host "`n4. 🔍 Searching for service worker files..." -ForegroundColor Blue
$swFiles = Get-ChildItem -Recurse -Include "*sw.js", "*service-worker.js", "*serviceworker.js" -ErrorAction SilentlyContinue
if ($swFiles) {
    foreach ($file in $swFiles) {
        Remove-Item -Force $file.FullName
        Write-Host "   ✅ Removed SW file: $($file.FullName)" -ForegroundColor Green
    }
} else {
    Write-Host "   ✅ No service worker files found" -ForegroundColor Green
}

# 5. Check package.json for workbox dependencies
Write-Host "`n5. 📦 Checking package.json for Workbox dependencies..." -ForegroundColor Blue
if (Test-Path "package.json") {
    $packageContent = Get-Content "package.json" -Raw
    if ($packageContent -match "workbox") {
        Write-Host "   ⚠️  Workbox dependencies found in package.json" -ForegroundColor Yellow
        Write-Host "   📝 Consider removing Workbox dependencies manually" -ForegroundColor Yellow
    } else {
        Write-Host "   ✅ No Workbox dependencies in package.json" -ForegroundColor Green
    }
}

# 6. Clear browser data instructions
Write-Host "`n6. 🌐 Browser cache clearing instructions:" -ForegroundColor Blue
Write-Host "   To completely fix Workbox errors, clear browser data:" -ForegroundColor White
Write-Host "   • Chrome: F12 > Application > Storage > Clear storage" -ForegroundColor White
Write-Host "   • Firefox: F12 > Storage > Clear All" -ForegroundColor White
Write-Host "   • Edge: F12 > Application > Storage > Clear storage" -ForegroundColor White
Write-Host "   • Or use Ctrl+Shift+Delete and clear All time" -ForegroundColor White

# 7. Rebuild project
Write-Host "`n7. 🔨 Rebuilding project..." -ForegroundColor Blue
Write-Host "   Installing dependencies..." -ForegroundColor White
npm install

if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Dependencies installed successfully" -ForegroundColor Green
    
    Write-Host "   Building project..." -ForegroundColor White
    npm run build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ Project built successfully" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  Build failed - check for errors" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ⚠️  Dependency installation failed" -ForegroundColor Yellow
}

Write-Host "`n✅ Workbox error fixing completed!" -ForegroundColor Green
Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "   • All cache directories cleared" -ForegroundColor White
Write-Host "   • npm cache cleared" -ForegroundColor White
Write-Host "   • Workbox files removed (if any)" -ForegroundColor White
Write-Host "   • Service worker files removed (if any)" -ForegroundColor White
Write-Host "   • Project rebuilt" -ForegroundColor White

Write-Host "`n🎯 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Clear browser cache completely (F12 > Application > Storage > Clear storage)" -ForegroundColor White
Write-Host "   2. Close all browser tabs" -ForegroundColor White
Write-Host "   3. Restart the development server: npm run dev" -ForegroundColor White
Write-Host "   4. Open application in incognito/private mode to test" -ForegroundColor White

Write-Host "`n⚠️  If errors persist:" -ForegroundColor Yellow
Write-Host "   • Try different browser" -ForegroundColor White
Write-Host "   • Clear DNS cache: ipconfig /flushdns" -ForegroundColor White
Write-Host "   • Restart computer if necessary" -ForegroundColor White
