import React, { LazyExoticComponent, JSXElementConstructor } from 'react';

export interface ToolMeta {
  path: string;
  component: LazyExoticComponent<JSXElementConstructor<ToolComponentProps>>;
  keywords: string[];
  icon: string;
  name: string;
  description: string;
  shortDescription: string;
  longDescription?: string;
}

export type ToolCategory =
  | 'string'
  | 'image-generic'
  | 'number'
  | 'video'
  | 'list'
  | 'json'
  | 'time'
  | 'csv'
  | 'pdf'
  | 'data';

export interface DefinedTool {
  type: ToolCategory;
  path: string;
  name: string;
  description: string;
  shortDescription: string;
  icon: string;
  keywords: string[];
  component: () => JSX.Element;
}

export interface ToolComponentProps {
  title: string;
  longDescription?: string;
}

export const defineTool = (
  basePath: ToolCategory,
  options: ToolMeta
): DefinedTool => {
  const {
    icon,
    path,
    name,
    description,
    keywords,
    component,
    shortDescription,
    longDescription
  } = options;
  
  const Component = component;
  
  return {
    type: basePath,
    path: `${basePath}/${path}`,
    name,
    icon,
    description,
    shortDescription,
    keywords,
    component: () => {
      return React.createElement(Component, { 
        title: name, 
        longDescription 
      });
    }
  };
};
