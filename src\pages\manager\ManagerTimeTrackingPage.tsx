import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Clock, Users, Calendar, CheckCircle } from 'lucide-react'

const ManagerTimeTrackingPage = () => {
  return (
    <div className='container mx-auto p-6 space-y-6'>
      <div className='flex justify-between items-center'>
        <div>
          <h1 className='text-3xl font-bold'>Time Tracking</h1>
          <p className='text-muted-foreground'>Monitor team attendance and working hours</p>
        </div>
        <Button>
          <Calendar className='h-4 w-4 mr-2' />
          Generate Report
        </Button>
      </div>

      <div className='grid md:grid-cols-4 gap-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active Today</CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>8</div>
            <p className='text-xs text-muted-foreground'>+2 from yesterday</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Hours</CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>64h</div>
            <p className='text-xs text-muted-foreground'>This week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completed Tasks</CardTitle>
            <CheckCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>23</div>
            <p className='text-xs text-muted-foreground'>+12% from last week</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Attendance Rate</CardTitle>
            <Badge variant='default' className='text-xs'>95%</Badge>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>95%</div>
            <p className='text-xs text-muted-foreground'>Above target</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Team Activity</CardTitle>
          <CardDescription>Real-time team member status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {[
              { name: 'John Doe', status: 'active', hours: '7.5h', location: 'Office' },
              { name: 'Jane Smith', status: 'break', hours: '6.2h', location: 'Remote' },
              { name: 'Mike Johnson', status: 'active', hours: '8.1h', location: 'Site A' },
              { name: 'Sarah Wilson', status: 'offline', hours: '8.0h', location: 'Office' }
            ].map((member) => (
              <div key={member.name} className='flex items-center justify-between p-3 border rounded-lg'>
                <div className='flex items-center gap-3'>
                  <div className={`w-3 h-3 rounded-full ${
                    member.status === 'active'
? 'bg-green-500'
                    : member.status === 'break' ? 'bg-yellow-500' : 'bg-gray-400'
                  }`}
                  />
                  <div>
                    <p className='font-medium'>{member.name}</p>
                    <p className='text-sm text-muted-foreground'>{member.location}</p>
                  </div>
                </div>
                <div className='text-right'>
                  <p className='font-medium'>{member.hours}</p>
                  <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>
                    {member.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ManagerTimeTrackingPage
