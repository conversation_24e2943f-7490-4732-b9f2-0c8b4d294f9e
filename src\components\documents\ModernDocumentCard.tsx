import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Trash2, 
  FileText, 
  File, 
  Image, 
  FileSpreadsheet,
  FileCode,
  Archive,
  Eye,
  Share2,
  MoreVertical
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface DocumentData {
  id: string;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  category: string;
  tags: string[];
  uploaded_by: string;
  created_at: string;
  uploader_name?: string;
  access_level: string;
}

interface ModernDocumentCardProps {
  document: DocumentData;
  onDownload: (document: DocumentData) => void;
  onDelete: (document: DocumentData) => void;
  onPreview?: (document: DocumentData) => void;
  onShare?: (document: DocumentData) => void;
  className?: string;
}

const getFileIcon = (fileType: string) => {
  if (fileType.includes('image')) return Image;
  if (fileType.includes('pdf')) return FileText;
  if (fileType.includes('spreadsheet') || fileType.includes('excel')) return FileSpreadsheet;
  if (fileType.includes('code') || fileType.includes('javascript') || fileType.includes('typescript')) return FileCode;
  if (fileType.includes('zip') || fileType.includes('rar')) return Archive;
  return File;
};

const getFileTypeGradient = (fileType: string) => {
  if (fileType.includes('image')) return 'from-purple-500 via-purple-600 to-purple-700';
  if (fileType.includes('pdf')) return 'from-red-500 via-red-600 to-red-700';
  if (fileType.includes('spreadsheet') || fileType.includes('excel')) return 'from-green-500 via-green-600 to-green-700';
  if (fileType.includes('code')) return 'from-blue-500 via-blue-600 to-blue-700';
  if (fileType.includes('zip') || fileType.includes('rar')) return 'from-orange-500 via-orange-600 to-orange-700';
  return 'from-red-500 via-red-600 to-red-700'; // Default CTNL brand color
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const ModernDocumentCard: React.FC<ModernDocumentCardProps> = ({
  document,
  onDownload,
  onDelete,
  onPreview,
  onShare,
  className
}) => {
  const FileIcon = getFileIcon(document.file_type);
  const gradientClass = getFileTypeGradient(document.file_type);

  return (
    <div className={cn(
      "modern-document-card group relative",
      "w-[230px] rounded-[20px] p-[5px] overflow-hidden",
      "bg-gradient-to-br from-gray-50 via-white to-gray-100",
      "dark:bg-gradient-to-br dark:from-gray-900 dark:via-gray-800 dark:to-gray-700",
      "shadow-[0_7px_20px_rgba(100,100,111,0.2)]",
      "dark:shadow-[0_7px_20px_rgba(0,0,0,0.3)]",
      "transition-transform duration-500 ease-[cubic-bezier(0.175,0.885,0.32,1.275)]",
      "hover:scale-105 cursor-pointer",
      "border border-red-500/10 hover:border-red-500/20",
      className
    )}>
      {/* Top Section with Gradient */}
      <div className={cn(
        "top-section relative h-[150px] rounded-[15px] flex flex-col",
        `bg-gradient-to-br ${gradientClass}`,
        "overflow-hidden"
      )}>
        {/* Decorative Border Element */}
        <div className="absolute top-0 left-0">
          <div className={cn(
            "border-element h-[30px] w-[130px] relative",
            "bg-gray-50 dark:bg-gray-900",
            "transform skew-x-[-40deg] origin-top-left",
            "shadow-[-10px_-10px_0_0] shadow-gray-50 dark:shadow-gray-900"
          )}>
            {/* Border pseudo-element */}
            <div className={cn(
              "absolute top-0 right-[-15px] w-[15px] h-[15px]",
              "bg-transparent rounded-tl-[10px]",
              "shadow-[-5px_-5px_0_2px] shadow-gray-50 dark:shadow-gray-900"
            )} />
          </div>
          
          {/* Top-left corner element */}
          <div className={cn(
            "absolute top-[30px] left-0 w-[15px] h-[15px]",
            "bg-transparent rounded-tl-[15px]",
            "shadow-[-5px_-5px_0_2px] shadow-gray-50 dark:shadow-gray-900"
          )} />
        </div>

        {/* Icons Section */}
        <div className="absolute top-0 w-full h-[30px] flex justify-between items-center z-10">
          {/* Logo/File Icon */}
          <div className="h-full aspect-square p-[7px_0_7px_15px]">
            <FileIcon className="h-full w-full text-gray-900 dark:text-gray-100" />
          </div>

          {/* Action Icons */}
          <div className="h-full px-[15px] flex items-center gap-[7px]">
            {onPreview && (
              <Button
                variant="ghost"
                size="sm"
                className="h-full w-auto p-1 text-gray-900 dark:text-gray-100 hover:text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  onPreview(document);
                }}
              >
                <Eye className="h-4 w-4" />
              </Button>
            )}
            {onShare && (
              <Button
                variant="ghost"
                size="sm"
                className="h-full w-auto p-1 text-gray-900 dark:text-gray-100 hover:text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  onShare(document);
                }}
              >
                <Share2 className="h-4 w-4" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-full w-auto p-1 text-gray-900 dark:text-gray-100 hover:text-white"
            >
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* File Type Badge */}
        <div className="absolute bottom-4 left-4 right-4">
          <Badge 
            variant="secondary" 
            className="bg-white/20 text-white border-white/30 text-xs"
          >
            {document.file_type.toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="bottom-section mt-[15px] p-[10px_5px]">
        {/* Title */}
        <div className="title block text-[17px] font-bold text-center tracking-[2px] mb-2">
          <span className="text-gray-900 dark:text-white line-clamp-2">
            {document.title || document.file_name}
          </span>
        </div>

        {/* Stats Row */}
        <div className="row flex justify-between mt-[20px]">
          {/* File Size */}
          <div className="item flex-[30%] text-center p-[5px] text-gray-600 dark:text-gray-300">
            <span className="big-text text-[12px] block font-semibold">
              {formatFileSize(document.file_size)}
            </span>
            <span className="regular-text text-[9px] opacity-70">
              Size
            </span>
          </div>

          {/* Upload Date */}
          <div className="item flex-[30%] text-center p-[5px] text-gray-600 dark:text-gray-300 border-l border-r border-gray-300/30 dark:border-gray-600/30">
            <span className="big-text text-[12px] block font-semibold">
              {formatDistanceToNow(new Date(document.created_at), { addSuffix: false })}
            </span>
            <span className="regular-text text-[9px] opacity-70">
              Ago
            </span>
          </div>

          {/* Access Level */}
          <div className="item flex-[30%] text-center p-[5px] text-gray-600 dark:text-gray-300">
            <span className="big-text text-[12px] block font-semibold capitalize">
              {document.access_level}
            </span>
            <span className="regular-text text-[9px] opacity-70">
              Access
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-4">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 h-8 text-xs"
            onClick={(e) => {
              e.stopPropagation();
              onDownload(document);
            }}
          >
            <Download className="h-3 w-3 mr-1" />
            Download
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(document);
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>

        {/* Tags */}
        {document.tags && document.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {document.tags.slice(0, 2).map((tag, index) => (
              <Badge 
                key={index} 
                variant="outline" 
                className="text-xs px-1 py-0 h-5"
              >
                {tag}
              </Badge>
            ))}
            {document.tags.length > 2 && (
              <Badge variant="outline" className="text-xs px-1 py-0 h-5">
                +{document.tags.length - 2}
              </Badge>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
