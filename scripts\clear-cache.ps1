# PowerShell script for comprehensive cache clearing
Write-Host "🧹 Starting comprehensive cache clearing..." -ForegroundColor Cyan

# Clear npm cache
Write-Host "`n1. 🗑️  Clearing npm cache..." -ForegroundColor Blue
npm cache clean --force
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ npm cache cleared" -ForegroundColor Green
} else {
    Write-Host "   ⚠️  npm cache clear failed" -ForegroundColor Yellow
}

# Clear node_modules cache directories
Write-Host "`n2. 🗂️  Clearing node_modules cache..." -ForegroundColor Blue
$cacheDirs = @(
    "node_modules\.cache",
    "node_modules\.vite",
    ".vite",
    "dist",
    ".next",
    ".nuxt"
)

foreach ($dir in $cacheDirs) {
    if (Test-Path $dir) {
        Remove-Item -Recurse -Force $dir
        Write-Host "   ✅ Removed $dir" -ForegroundColor Green
    }
}

# Clear Vite cache
Write-Host "`n3. ⚡ Clearing Vite cache..." -ForegroundColor Blue
$viteCacheDirs = @(
    "node_modules\.vite\deps",
    "node_modules\.vite\deps_temp*"
)

foreach ($dir in $viteCacheDirs) {
    if (Test-Path $dir) {
        Remove-Item -Recurse -Force $dir
        Write-Host "   ✅ Removed $dir" -ForegroundColor Green
    }
}

# Clear temporary files
Write-Host "`n4. 🗑️  Clearing temporary files..." -ForegroundColor Blue
$tempFiles = @(
    "*.log",
    "*.tmp",
    ".DS_Store",
    "Thumbs.db"
)

foreach ($pattern in $tempFiles) {
    $files = Get-ChildItem -Recurse -Force -Name $pattern -ErrorAction SilentlyContinue
    if ($files) {
        $files | ForEach-Object { Remove-Item -Force $_ }
        Write-Host "   ✅ Removed $pattern files" -ForegroundColor Green
    }
}

# Check for service worker files and remove them
Write-Host "`n5. 🔍 Checking for service worker files..." -ForegroundColor Blue
$swFiles = @(
    "public\sw.js",
    "public\service-worker.js",
    "src\sw.js",
    "src\service-worker.js"
)

foreach ($file in $swFiles) {
    if (Test-Path $file) {
        Remove-Item -Force $file
        Write-Host "   ✅ Removed $file" -ForegroundColor Green
    }
}

# Check for PWA manifest files and remove them
Write-Host "`n6. 📱 Checking for PWA manifest files..." -ForegroundColor Blue
$manifestFiles = @(
    "public\manifest.json",
    "public\manifest.webmanifest",
    "src\manifest.json"
)

foreach ($file in $manifestFiles) {
    if (Test-Path $file) {
        Remove-Item -Force $file
        Write-Host "   ✅ Removed $file" -ForegroundColor Green
    }
}

# Clear browser caches (instructions)
Write-Host "`n7. 🌐 Browser cache clearing instructions:" -ForegroundColor Blue
Write-Host "   • Chrome: Ctrl+Shift+Delete or F12 > Application > Storage > Clear storage" -ForegroundColor White
Write-Host "   • Firefox: Ctrl+Shift+Delete or F12 > Storage > Clear All" -ForegroundColor White
Write-Host "   • Edge: Ctrl+Shift+Delete or F12 > Application > Storage > Clear storage" -ForegroundColor White

Write-Host "`n✅ Cache clearing completed!" -ForegroundColor Green
Write-Host "`n📋 Summary:" -ForegroundColor Cyan
Write-Host "   • npm cache cleared" -ForegroundColor White
Write-Host "   • node_modules cache directories removed" -ForegroundColor White
Write-Host "   • Vite cache cleared" -ForegroundColor White
Write-Host "   • Temporary files removed" -ForegroundColor White
Write-Host "   • Service worker files removed (if any)" -ForegroundColor White
Write-Host "   • PWA manifest files removed (if any)" -ForegroundColor White

Write-Host "`n🎯 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Run: npm install" -ForegroundColor White
Write-Host "   2. Run: npm run dev" -ForegroundColor White
Write-Host "   3. Clear browser cache manually" -ForegroundColor White
Write-Host "   4. Test the application" -ForegroundColor White
