/**
 * Neumorphism Design System for CTNL AI Workboard
 * Modern soft UI effects with 8px border radius
 */

/* ============= NEUMORPHISM BASE STYLES ============= */
:root {
  /* Neumorphism colors */
  --neu-bg-light: #f0f0f3;
  --neu-bg-dark: #2d3748;
  --neu-shadow-light: rgba(255, 255, 255, 0.7);
  --neu-shadow-dark: rgba(0, 0, 0, 0.15);
  --neu-border-radius: 8px;
  
  /* AI Theme colors */
  --ai-primary: #ff1c04;
  --ai-secondary: #000000;
  --ai-accent: #ffffff;
  --ai-glow: rgba(255, 28, 4, 0.3);
}

/* ============= NEUMORPHISM COMPONENTS ============= */

/* Neumorphism Button */
.neumorphism-button {
  background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  box-shadow: 
    inset 2px 2px 4px rgba(0,0,0,0.1), 
    inset -2px -2px 4px rgba(255,255,255,0.1),
    0 4px 8px rgba(0,0,0,0.1);
  border-radius: var(--neu-border-radius);
  border: 1px solid rgba(255,255,255,0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.neumorphism-button:hover {
  background: linear-gradient(145deg, rgba(255,255,255,0.15), rgba(255,255,255,0.08));
  box-shadow: 
    inset 1px 1px 2px rgba(0,0,0,0.1), 
    inset -1px -1px 2px rgba(255,255,255,0.1),
    0 6px 12px rgba(0,0,0,0.15);
  transform: translateY(-1px);
}

.neumorphism-button:active {
  box-shadow: 
    inset 3px 3px 6px rgba(0,0,0,0.2), 
    inset -3px -3px 6px rgba(255,255,255,0.05);
  transform: translateY(0);
}

/* Active state for navigation */
.neumorphism-active {
  background: linear-gradient(145deg, var(--ai-primary), #e01a03);
  box-shadow: 
    inset 2px 2px 4px rgba(0,0,0,0.3), 
    inset -2px -2px 4px rgba(255,255,255,0.1),
    0 0 20px var(--ai-glow);
  color: white;
  border: 1px solid rgba(255,28,4,0.3);
}

/* Neumorphism Card */
.neumorphism-card {
  background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  box-shadow: 
    8px 8px 16px rgba(0,0,0,0.1),
    -8px -8px 16px rgba(255,255,255,0.1),
    inset 1px 1px 2px rgba(255,255,255,0.1);
  border-radius: var(--neu-border-radius);
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(15px);
  transition: all 0.3s ease;
}

.neumorphism-card:hover {
  box-shadow: 
    12px 12px 24px rgba(0,0,0,0.15),
    -12px -12px 24px rgba(255,255,255,0.15),
    inset 1px 1px 2px rgba(255,255,255,0.1);
  transform: translateY(-2px);
}

/* Neumorphism Sidebar */
.neumorphism-sidebar {
  background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  box-shadow: 
    inset 8px 8px 16px rgba(0,0,0,0.1), 
    inset -8px -8px 16px rgba(255,255,255,0.1), 
    0 8px 32px rgba(0,0,0,0.1);
  border-radius: 0 var(--neu-border-radius) var(--neu-border-radius) 0;
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255,255,255,0.1);
}

/* Neumorphism Input */
.neumorphism-input {
  background: linear-gradient(145deg, rgba(255,255,255,0.05), rgba(255,255,255,0.1));
  box-shadow: 
    inset 4px 4px 8px rgba(0,0,0,0.1),
    inset -4px -4px 8px rgba(255,255,255,0.05);
  border-radius: var(--neu-border-radius);
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.neumorphism-input:focus {
  box-shadow: 
    inset 4px 4px 8px rgba(0,0,0,0.15),
    inset -4px -4px 8px rgba(255,255,255,0.05),
    0 0 0 2px var(--ai-glow);
  outline: none;
}

/* Neumorphism Table Header */
.neumorphism-table-header {
  background: linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  box-shadow: 
    0 4px 8px rgba(0,0,0,0.1),
    inset 1px 1px 2px rgba(255,255,255,0.1);
  border-radius: var(--neu-border-radius) var(--neu-border-radius) 0 0;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255,255,255,0.1);
  border-bottom: none;
}

/* AI-specific neumorphism effects */
.ai-neumorphism-glow {
  box-shadow: 
    0 0 20px var(--ai-glow),
    inset 2px 2px 4px rgba(0,0,0,0.1),
    inset -2px -2px 4px rgba(255,255,255,0.1);
  border: 1px solid rgba(255,28,4,0.3);
}

/* ============= RESPONSIVE NEUMORPHISM ============= */

/* Mobile optimizations */
@media (max-width: 768px) {
  .neumorphism-button {
    min-height: 44px;
    min-width: 44px;
    box-shadow: 
      inset 1px 1px 2px rgba(0,0,0,0.1), 
      inset -1px -1px 2px rgba(255,255,255,0.1),
      0 2px 4px rgba(0,0,0,0.1);
  }
  
  .neumorphism-card {
    box-shadow: 
      4px 4px 8px rgba(0,0,0,0.1),
      -4px -4px 8px rgba(255,255,255,0.1),
      inset 1px 1px 2px rgba(255,255,255,0.1);
  }
  
  .neumorphism-sidebar {
    box-shadow: 
      inset 4px 4px 8px rgba(0,0,0,0.1), 
      inset -4px -4px 8px rgba(255,255,255,0.1), 
      0 4px 16px rgba(0,0,0,0.1);
  }
}

/* Tablet optimizations */
@media (min-width: 769px) and (max-width: 1024px) {
  .neumorphism-button {
    box-shadow: 
      inset 2px 2px 4px rgba(0,0,0,0.1), 
      inset -2px -2px 4px rgba(255,255,255,0.1),
      0 3px 6px rgba(0,0,0,0.1);
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  .neumorphism-card:hover {
    box-shadow: 
      16px 16px 32px rgba(0,0,0,0.15),
      -16px -16px 32px rgba(255,255,255,0.15),
      inset 1px 1px 2px rgba(255,255,255,0.1);
  }
}

/* ============= DARK MODE NEUMORPHISM ============= */
@media (prefers-color-scheme: dark) {
  .neumorphism-button {
    background: linear-gradient(145deg, rgba(45,55,72,0.8), rgba(45,55,72,0.6));
    box-shadow: 
      inset 2px 2px 4px rgba(0,0,0,0.3), 
      inset -2px -2px 4px rgba(255,255,255,0.05),
      0 4px 8px rgba(0,0,0,0.2);
  }
  
  .neumorphism-card {
    background: linear-gradient(145deg, rgba(45,55,72,0.8), rgba(45,55,72,0.6));
    box-shadow: 
      8px 8px 16px rgba(0,0,0,0.3),
      -8px -8px 16px rgba(255,255,255,0.05),
      inset 1px 1px 2px rgba(255,255,255,0.05);
  }
  
  .neumorphism-sidebar {
    background: linear-gradient(145deg, rgba(45,55,72,0.9), rgba(45,55,72,0.7));
    box-shadow: 
      inset 8px 8px 16px rgba(0,0,0,0.3), 
      inset -8px -8px 16px rgba(255,255,255,0.05), 
      0 8px 32px rgba(0,0,0,0.3);
  }
}

/* ============= ACCESSIBILITY ============= */
@media (prefers-reduced-motion: reduce) {
  .neumorphism-button,
  .neumorphism-card,
  .neumorphism-sidebar {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .neumorphism-button,
  .neumorphism-card,
  .neumorphism-sidebar {
    border: 2px solid currentColor;
    box-shadow: none;
  }
}

/* ============= UTILITY CLASSES ============= */
.neu-elevated {
  box-shadow: 
    12px 12px 24px rgba(0,0,0,0.15),
    -12px -12px 24px rgba(255,255,255,0.15);
}

.neu-pressed {
  box-shadow: 
    inset 4px 4px 8px rgba(0,0,0,0.2),
    inset -4px -4px 8px rgba(255,255,255,0.05);
}

.neu-glow-ai {
  box-shadow: 
    0 0 20px var(--ai-glow),
    0 0 40px var(--ai-glow),
    inset 2px 2px 4px rgba(0,0,0,0.1);
}

.border-radius-8 {
  border-radius: var(--neu-border-radius) !important;
}
