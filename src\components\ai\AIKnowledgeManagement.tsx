import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Upload, Search, Brain, FileText, MessageSquare, BarChart3 } from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/integrations/supabase/client'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/hooks/use-toast'

export const AIKnowledgeManagement = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [newDocument, setNewDocument] = useState({ title: '', content: '', category: 'general', tags: '' })
  const [chatQuery, setChatQuery] = useState('')
  const { toast } = useToast()

  const { data: aiDocuments, isLoading, refetch } = useQuery({
    queryKey: ['ai-documents'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('ai_documents')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return data
    }
  })

  const { data: documentAnalysis } = useQuery({
    queryKey: ['enhanced-document-analysis'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('enhanced_document_analysis')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) throw error
      return data
    }
  })

  const { data: documentQueries } = useQuery({
    queryKey: ['document-queries'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('document_queries')
        .select('*')
        .order('query_timestamp', { ascending: false })
        .limit(10)

      if (error) throw error
      return data
    }
  })

  const handleAddDocument = async () => {
    if (!newDocument.title || !newDocument.content) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      })
      return
    }

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const { error } = await supabase
        .from('ai_documents')
        .insert([{
          title: newDocument.title,
          content: newDocument.content,
          category: newDocument.category,
          tags: newDocument.tags.split(',').map(tag => tag.trim()).filter(Boolean),
          created_by: user.id
        }])

      if (error) throw error

      toast({
        title: 'Success',
        description: 'Document added to knowledge base'
      })

      setNewDocument({ title: '', content: '', category: 'general', tags: '' })
      refetch()
    } catch (error) {
      console.error('Error adding document:', error)
      toast({
        title: 'Error',
        description: 'Failed to add document',
        variant: 'destructive'
      })
    }
  }

  const handleChatQuery = async () => {
    if (!chatQuery.trim()) return

    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      // This would typically call an AI service
      const response = 'AI response based on knowledge base would appear here.'

      const { error } = await supabase
        .from('document_queries')
        .insert([{
          query_text: chatQuery,
          response_text: response,
          user_id: user.id
        }])

      if (error) throw error

      toast({
        title: 'Query Processed',
        description: 'AI response generated successfully'
      })

      setChatQuery('')
    } catch (error) {
      console.error('Error processing query:', error)
      toast({
        title: 'Error',
        description: 'Failed to process query',
        variant: 'destructive'
      })
    }
  }

  const filteredDocuments = aiDocuments?.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.content.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = [...new Set(aiDocuments?.map(doc => doc.category) || [])]

  return (
    <div className='space-y-6'>
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold'>AI & Knowledge Management</h2>
          <p className='text-muted-foreground'>Manage AI knowledge base and document analysis</p>
        </div>
        <Button>
          <Upload className='h-4 w-4 mr-2' />
          Upload Documents
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className='grid gap-4 md:grid-cols-4'>
        <Card>
          <CardContent className='flex items-center p-6'>
            <FileText className='h-8 w-8 text-blue-600' />
            <div className='ml-4'>
              <p className='text-sm font-medium text-muted-foreground'>Knowledge Base</p>
              <p className='text-2xl font-bold'>{aiDocuments?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='flex items-center p-6'>
            <Brain className='h-8 w-8 text-green-600' />
            <div className='ml-4'>
              <p className='text-sm font-medium text-muted-foreground'>Analyzed Docs</p>
              <p className='text-2xl font-bold'>{documentAnalysis?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='flex items-center p-6'>
            <MessageSquare className='h-8 w-8 text-purple-600' />
            <div className='ml-4'>
              <p className='text-sm font-medium text-muted-foreground'>AI Queries</p>
              <p className='text-2xl font-bold'>{documentQueries?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className='flex items-center p-6'>
            <BarChart3 className='h-8 w-8 text-orange-600' />
            <div className='ml-4'>
              <p className='text-sm font-medium text-muted-foreground'>Categories</p>
              <p className='text-2xl font-bold'>{categories.length}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='knowledge' className='space-y-4'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='knowledge'>Knowledge Base</TabsTrigger>
          <TabsTrigger value='chat'>AI Chat</TabsTrigger>
          <TabsTrigger value='analysis'>Document Analysis</TabsTrigger>
          <TabsTrigger value='insights'>AI Insights</TabsTrigger>
        </TabsList>

        <TabsContent value='knowledge' className='space-y-4'>
          <div className='grid gap-6 lg:grid-cols-3'>
            <div className='lg:col-span-2'>
              <Card>
                <CardHeader>
                  <CardTitle>Knowledge Base Documents</CardTitle>
                  <div className='flex gap-4'>
                    <div className='relative flex-1'>
                      <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                      <Input
                        placeholder='Search documents...'
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className='pl-10'
                      />
                    </div>
                    <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                      <SelectTrigger className='w-48'>
                        <SelectValue placeholder='Filter by category' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='all'>All Categories</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    {isLoading
                      ? (
                        <div className='text-center py-8'>Loading documents...</div>
                        )
                      : filteredDocuments?.length === 0
                        ? (
                          <div className='text-center py-8 text-muted-foreground'>
                            No documents found matching your criteria
                          </div>
                          )
                        : (
                            filteredDocuments?.map((document) => (
                              <Card key={document.id} className='p-4'>
                                <div className='space-y-2'>
                                  <h3 className='font-semibold'>{document.title}</h3>
                                  <p className='text-sm text-muted-foreground line-clamp-2'>
                                    {document.content}
                                  </p>
                                  <div className='flex gap-2 flex-wrap'>
                                    <Badge variant='outline'>{document.category}</Badge>
                                    {document.tags?.map((tag, index) => (
                                      <Badge key={index} variant='secondary'>
                                        {tag}
                                      </Badge>
                                    ))}
                                  </div>
                                  <p className='text-sm text-muted-foreground'>
                                    Created: {new Date(document.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </Card>
                            ))
                          )}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Add New Document</CardTitle>
                </CardHeader>
                <CardContent className='space-y-4'>
                  <Input
                    placeholder='Document title'
                    value={newDocument.title}
                    onChange={(e) => setNewDocument(prev => ({ ...prev, title: e.target.value }))}
                  />
                  <Select
                    value={newDocument.category}
                    onValueChange={(value) => setNewDocument(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='general'>General</SelectItem>
                      <SelectItem value='policy'>Policy</SelectItem>
                      <SelectItem value='procedure'>Procedure</SelectItem>
                      <SelectItem value='technical'>Technical</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    placeholder='Tags (comma separated)'
                    value={newDocument.tags}
                    onChange={(e) => setNewDocument(prev => ({ ...prev, tags: e.target.value }))}
                  />
                  <Textarea
                    placeholder='Document content'
                    value={newDocument.content}
                    onChange={(e) => setNewDocument(prev => ({ ...prev, content: e.target.value }))}
                    rows={8}
                  />
                  <Button onClick={handleAddDocument} className='w-full'>
                    Add to Knowledge Base
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value='chat' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>AI Assistant Chat</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex gap-2'>
                <Input
                  placeholder='Ask a question about your knowledge base...'
                  value={chatQuery}
                  onChange={(e) => setChatQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleChatQuery()}
                />
                <Button onClick={handleChatQuery}>
                  <MessageSquare className='h-4 w-4 mr-2' />
                  Ask AI
                </Button>
              </div>

              <div className='space-y-4 max-h-96 overflow-y-auto'>
                {documentQueries?.map((query) => (
                  <div key={query.id} className='border rounded-lg p-4'>
                    <div className='space-y-2'>
                      <div className='flex justify-between items-start'>
                        <p className='font-medium'>You:</p>
                        <span className='text-xs text-muted-foreground'>
                          {new Date(query.query_timestamp).toLocaleString()}
                        </span>
                      </div>
                      <p className='text-sm bg-blue-50 p-2 rounded'>{query.query_text}</p>
                      <p className='font-medium'>AI:</p>
                      <p className='text-sm bg-green-50 p-2 rounded'>{query.response_text}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='analysis' className='space-y-4'>
          <Card>
            <CardHeader>
              <CardTitle>Document Analysis Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                {documentAnalysis?.map((analysis) => (
                  <Card key={analysis.id} className='p-4'>
                    <div className='space-y-2'>
                      <h3 className='font-semibold'>{analysis.file_name}</h3>
                      <div className='grid grid-cols-2 gap-4 text-sm'>
                        <div>
                          <p className='text-muted-foreground'>File Type</p>
                          <p className='font-medium'>{analysis.file_type || 'Unknown'}</p>
                        </div>
                        <div>
                          <p className='text-muted-foreground'>File Size</p>
                          <p className='font-medium'>
                            {analysis.file_size ? `${(analysis.file_size / 1024).toFixed(2)} KB` : 'Unknown'}
                          </p>
                        </div>
                      </div>
                      <p className='text-sm text-muted-foreground'>
                        Analyzed: {new Date(analysis.created_at).toLocaleDateString()}
                      </p>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='insights' className='space-y-4'>
          <div className='grid gap-4 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>Knowledge Base Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex justify-between'>
                    <span>Most Active Category</span>
                    <span className='font-bold'>
                      {categories.length > 0 ? categories[0] : 'N/A'}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Recent Queries</span>
                    <span className='font-bold'>{documentQueries?.length || 0}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span>Analysis Success Rate</span>
                    <span className='font-bold text-green-600'>95%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>AI Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='p-3 border-l-4 border-green-500 bg-green-50'>
                    <p className='font-medium text-green-800'>High Accuracy</p>
                    <p className='text-sm text-green-600'>
                      AI responses are highly accurate based on knowledge base
                    </p>
                  </div>
                  <div className='p-3 border-l-4 border-blue-500 bg-blue-50'>
                    <p className='font-medium text-blue-800'>Fast Processing</p>
                    <p className='text-sm text-blue-600'>
                      Average response time: 2.3 seconds
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
