import { useToast } from '@/hooks/use-toast'
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport
} from '@/components/ui/toast'

export function Toaster () {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className='grid gap-2 flex-1 pr-8'>
              {title && (
                <ToastTitle className='flex items-center gap-2'>
                  {title.includes('Welcome') && (
                    <span className='text-lg'>👋</span>
                  )}
                  {title.includes('Success') && (
                    <span className='text-lg'>✅</span>
                  )}
                  {title.includes('Error') && (
                    <span className='text-lg'>❌</span>
                  )}
                  {title}
                </ToastTitle>
              )}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
