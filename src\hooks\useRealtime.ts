/**
 * React Hooks for Real-time Features
 * Provides easy-to-use hooks for real-time collaboration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { realtimeService, UserPresence, RealtimeEvent } from '@/lib/realtime/realtime-service';
import { documentCollaboration, CollaborativeDocument } from '@/lib/realtime/document-collaboration';
import { useAuth } from '@/hooks/useAuth';

/**
 * Hook for managing user presence
 */
export function usePresence() {
  const [onlineUsers, setOnlineUsers] = useState<UserPresence[]>([]);
  const [currentUser, setCurrentUser] = useState<UserPresence | null>(null);
  const { user } = useAuth();

  useEffect(() => {
    if (!user) return;

    // Initialize presence
    const initPresence = async () => {
      await realtimeService.initializePresence(
        user.id,
        user.user_metadata?.full_name || user.email || 'Unknown User',
        user.user_metadata?.avatar_url
      );
      setCurrentUser(realtimeService.getCurrentPresence());
    };

    initPresence();

    // Listen for presence events
    const handlePresenceSync = (event: RealtimeEvent) => {
      if (event.type === 'presence_sync') {
        setOnlineUsers(event.payload.users);
      }
    };

    const handleUserJoined = (event: RealtimeEvent) => {
      if (event.type === 'user_joined') {
        setOnlineUsers(prev => {
          const existing = prev.find(u => u.userId === event.payload.user.userId);
          if (existing) {
            return prev.map(u => u.userId === event.payload.user.userId ? event.payload.user : u);
          }
          return [...prev, event.payload.user];
        });
      }
    };

    const handleUserLeft = (event: RealtimeEvent) => {
      if (event.type === 'user_left') {
        setOnlineUsers(prev => prev.filter(u => u.userId !== event.payload.user.userId));
      }
    };

    realtimeService.addEventListener('presence_sync', handlePresenceSync);
    realtimeService.addEventListener('user_joined', handleUserJoined);
    realtimeService.addEventListener('user_left', handleUserLeft);

    return () => {
      realtimeService.removeEventListener('presence_sync', handlePresenceSync);
      realtimeService.removeEventListener('user_joined', handleUserJoined);
      realtimeService.removeEventListener('user_left', handleUserLeft);
    };
  }, [user]);

  const updateStatus = useCallback(async (status: UserPresence['status']) => {
    await realtimeService.updateStatus(status);
    setCurrentUser(realtimeService.getCurrentPresence());
  }, []);

  const updateCurrentPage = useCallback(async (page: string) => {
    await realtimeService.updateCurrentPage(page);
    setCurrentUser(realtimeService.getCurrentPresence());
  }, []);

  const setTyping = useCallback(async (isTyping: boolean, sessionId?: string) => {
    await realtimeService.setTyping(isTyping, sessionId);
    setCurrentUser(realtimeService.getCurrentPresence());
  }, []);

  return {
    onlineUsers,
    currentUser,
    updateStatus,
    updateCurrentPage,
    setTyping,
  };
}

/**
 * Hook for real-time document collaboration
 */
export function useDocumentCollaboration(documentId: string) {
  const [document, setDocument] = useState<CollaborativeDocument | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cursors, setCursors] = useState<Map<string, any>>(new Map());
  const [isLocked, setIsLocked] = useState(false);

  useEffect(() => {
    if (!documentId) return;

    const initDocument = async () => {
      try {
        setIsLoading(true);
        const doc = await documentCollaboration.initializeDocument(documentId);
        setDocument(doc);
        setCursors(doc.cursors);
        setIsLocked(doc.isLocked);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize document');
      } finally {
        setIsLoading(false);
      }
    };

    initDocument();

    // Listen for document updates
    const handleDocumentUpdate = (event: CustomEvent) => {
      if (event.detail.documentId === documentId) {
        const updatedDoc = event.detail.document as CollaborativeDocument;
        setDocument(updatedDoc);
        setCursors(new Map(updatedDoc.cursors));
        setIsLocked(updatedDoc.isLocked);
      }
    };

    window.addEventListener('documentUpdate', handleDocumentUpdate as EventListener);

    return () => {
      window.removeEventListener('documentUpdate', handleDocumentUpdate as EventListener);
      documentCollaboration.cleanup(documentId);
    };
  }, [documentId]);

  const applyOperation = useCallback(async (
    type: 'insert' | 'delete' | 'retain' | 'format',
    position: number,
    content?: string,
    length?: number,
    attributes?: Record<string, any>
  ) => {
    if (!document) return;

    await documentCollaboration.applyOperation(documentId, {
      type,
      position,
      content,
      length,
      attributes,
      userId: realtimeService.getCurrentPresence()?.userId || '',
    });
  }, [documentId, document]);

  const updateCursor = useCallback(async (
    position: number,
    selection?: { start: number; end: number }
  ) => {
    await documentCollaboration.updateCursor(documentId, position, selection);
  }, [documentId]);

  const addComment = useCallback(async (
    content: string,
    position: number,
    parentId?: string
  ) => {
    return await documentCollaboration.addComment(documentId, content, position, parentId);
  }, [documentId]);

  const resolveComment = useCallback(async (commentId: string) => {
    await documentCollaboration.resolveComment(documentId, commentId);
  }, [documentId]);

  const lockDocument = useCallback(async () => {
    return await documentCollaboration.lockDocument(documentId);
  }, [documentId]);

  const unlockDocument = useCallback(async () => {
    await documentCollaboration.unlockDocument(documentId);
  }, [documentId]);

  return {
    document,
    isLoading,
    error,
    cursors: Array.from(cursors.values()),
    isLocked,
    applyOperation,
    updateCursor,
    addComment,
    resolveComment,
    lockDocument,
    unlockDocument,
  };
}

/**
 * Hook for real-time table updates
 */
export function useRealtimeTable<T = any>(
  table: string,
  filter?: string,
  initialData?: T[]
) {
  const [data, setData] = useState<T[]>(initialData || []);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const handleTableChange = (event: RealtimeEvent) => {
      if (event.type.startsWith('table_') && event.payload.table === table) {
        const { eventType, record } = event.payload;
        
        setData(prevData => {
          switch (eventType) {
            case 'INSERT':
              return [...prevData, record];
            
            case 'UPDATE':
              return prevData.map(item => 
                (item as any).id === record.id ? record : item
              );
            
            case 'DELETE':
              return prevData.filter(item => 
                (item as any).id !== record.id
              );
            
            default:
              return prevData;
          }
        });
      }
    };

    // Subscribe to table changes
    realtimeService.subscribeToTableChanges(table, filter);
    realtimeService.addEventListener('table_INSERT', handleTableChange);
    realtimeService.addEventListener('table_UPDATE', handleTableChange);
    realtimeService.addEventListener('table_DELETE', handleTableChange);

    setIsLoading(false);

    return () => {
      realtimeService.removeEventListener('table_INSERT', handleTableChange);
      realtimeService.removeEventListener('table_UPDATE', handleTableChange);
      realtimeService.removeEventListener('table_DELETE', handleTableChange);
    };
  }, [table, filter]);

  return { data, isLoading };
}

/**
 * Hook for typing indicators
 */
export function useTypingIndicator(sessionId: string) {
  const [typingUsers, setTypingUsers] = useState<UserPresence[]>([]);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const handleTypingIndicator = (event: RealtimeEvent) => {
      if (event.type === 'typing_indicator' && event.sessionId === sessionId) {
        const { isTyping } = event.payload;
        const currentPresence = realtimeService.getCurrentPresence();
        
        // Don't show own typing indicator
        if (event.userId === currentPresence?.userId) return;

        setTypingUsers(prev => {
          if (isTyping) {
            // Add user to typing list
            const existingUser = prev.find(u => u.userId === event.userId);
            if (!existingUser) {
              // Get user info from online users
              return [...prev, {
                userId: event.userId,
                userName: 'User', // This would be populated from presence
                status: 'online' as const,
                lastSeen: new Date(),
                isTyping: true,
              }];
            }
            return prev;
          } else {
            // Remove user from typing list
            return prev.filter(u => u.userId !== event.userId);
          }
        });
      }
    };

    realtimeService.addEventListener('typing_indicator', handleTypingIndicator);

    return () => {
      realtimeService.removeEventListener('typing_indicator', handleTypingIndicator);
    };
  }, [sessionId]);

  const startTyping = useCallback(() => {
    realtimeService.setTyping(true, sessionId);
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Set timeout to stop typing after 3 seconds
    typingTimeoutRef.current = setTimeout(() => {
      realtimeService.setTyping(false, sessionId);
    }, 3000);
  }, [sessionId]);

  const stopTyping = useCallback(() => {
    realtimeService.setTyping(false, sessionId);
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  }, [sessionId]);

  return {
    typingUsers,
    startTyping,
    stopTyping,
  };
}

/**
 * Hook for collaborative sessions
 */
export function useCollaborativeSession(
  sessionId: string,
  type: 'document' | 'project' | 'task' | 'meeting',
  resourceId: string
) {
  const [participants, setParticipants] = useState<UserPresence[]>([]);
  const [isJoined, setIsJoined] = useState(false);

  useEffect(() => {
    const joinSession = async () => {
      await realtimeService.joinSession(sessionId, type, resourceId);
      setIsJoined(true);
    };

    joinSession();

    const handleSessionJoined = (event: RealtimeEvent) => {
      if (event.type === 'session_joined' && event.sessionId === sessionId) {
        setParticipants(event.payload.session.participants);
      }
    };

    const handleSessionLeft = (event: RealtimeEvent) => {
      if (event.type === 'session_left' && event.sessionId === sessionId) {
        setParticipants(prev => prev.filter(p => p.userId !== event.userId));
      }
    };

    realtimeService.addEventListener('session_joined', handleSessionJoined);
    realtimeService.addEventListener('session_left', handleSessionLeft);

    return () => {
      realtimeService.removeEventListener('session_joined', handleSessionJoined);
      realtimeService.removeEventListener('session_left', handleSessionLeft);
      realtimeService.leaveSession(sessionId);
      setIsJoined(false);
    };
  }, [sessionId, type, resourceId]);

  const broadcastEvent = useCallback(async (eventType: string, data: any) => {
    await realtimeService.broadcastToSession(sessionId, eventType, data);
  }, [sessionId]);

  return {
    participants,
    isJoined,
    broadcastEvent,
  };
}

/**
 * Hook for real-time notifications
 */
export function useRealtimeNotifications() {
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    const handleNotification = (event: RealtimeEvent) => {
      if (event.type === 'notification') {
        setNotifications(prev => [event.payload, ...prev].slice(0, 50)); // Keep last 50
      }
    };

    realtimeService.addEventListener('notification', handleNotification);

    return () => {
      realtimeService.removeEventListener('notification', handleNotification);
    };
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  return {
    notifications,
    clearNotifications,
    removeNotification,
  };
}
