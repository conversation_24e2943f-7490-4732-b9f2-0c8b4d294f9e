import { ActivityAnalyticsManagement } from "@/components/activity/ActivityAnalyticsManagement";
import { ActivityManagement } from "@/components/admin/ActivityManagement";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
    Activity,
    AlertCircle,
    BarChart3,
    Calendar,
    Clock,
    Database,
    Download,
    Eye,
    Filter,
    RefreshCw,
    Search,
    Settings,
    TrendingUp,
    User,
    Users
} from "lucide-react";
import { useEffect, useState } from 'react';

interface ActivityLog {
  id: string;
  user_id: string;
  activity_type: string;
  description: string;
  metadata: any;
  created_at: string;
  profiles?: {
    full_name: string;
    email: string;
    department?: {
      name: string;
    };
  };
}

interface ActivityStats {
  totalActivities: number;
  todayActivities: number;
  activeUsers: number;
  topActivity: string;
}

export const ActivitiesPage = () => {
  const { toast } = useToast();
  const [activities, setActivities] = useState<ActivityLog[]>([]);
  const [stats, setStats] = useState<ActivityStats>({
    totalActivities: 0,
    todayActivities: 0,
    activeUsers: 0,
    topActivity: 'N/A'
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterUser, setFilterUser] = useState('all');
  const [selectedActivity, setSelectedActivity] = useState<ActivityLog | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    loadActivities();
    loadStats();
  }, []);

  const loadActivities = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('system_activities')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email,
            department:department_id (
              name
            )
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      setActivities(data || []);
    } catch (error) {
      console.error('Error loading activities:', error);
      toast({
        title: "Error",
        description: "Failed to load activities",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      // Get total activities
      const { count: totalCount } = await supabase
        .from('system_activities')
        .select('id', { count: 'exact', head: true });

      // Get today's activities
      const today = new Date().toISOString().split('T')[0];
      const { count: todayCount } = await supabase
        .from('system_activities')
        .select('id', { count: 'exact', head: true })
        .gte('created_at', today);

      // Get active users (users with activities in last 24 hours)
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const { data: activeUsersData } = await supabase
        .from('system_activities')
        .select('user_id')
        .gte('created_at', yesterday);

      const uniqueUsers = new Set(activeUsersData?.map(a => a.user_id) || []);

      // Get top activity type
      const { data: activityTypes } = await supabase
        .from('system_activities')
        .select('activity_type')
        .gte('created_at', yesterday);

      const typeCounts = activityTypes?.reduce((acc, curr) => {
        acc[curr.activity_type] = (acc[curr.activity_type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      const topActivity = Object.entries(typeCounts).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A';

      setStats({
        totalActivities: totalCount || 0,
        todayActivities: todayCount || 0,
        activeUsers: uniqueUsers.size,
        topActivity
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    }
  };

  const filteredActivities = activities.filter(activity => {
    const matchesSearch = searchTerm === '' || 
      activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.profiles?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.activity_type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = filterType === 'all' || activity.activity_type === filterType;
    const matchesUser = filterUser === 'all' || activity.user_id === filterUser;

    return matchesSearch && matchesType && matchesUser;
  });

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_login':
      case 'user_logout':
        return <User className="h-4 w-4" />;
      case 'project_created':
      case 'project_updated':
        return <BarChart3 className="h-4 w-4" />;
      case 'database_query':
        return <Database className="h-4 w-4" />;
      case 'settings_updated':
        return <Settings className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getSeverityBadge = (type: string) => {
    const severity = type.includes('error') || type.includes('failed') ? 'destructive' :
                    type.includes('warning') ? 'secondary' :
                    type.includes('success') || type.includes('created') ? 'default' : 'outline';
    
    return (
      <Badge variant={severity} className="text-xs">
        {type.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const exportActivities = async () => {
    try {
      const csvContent = [
        ['Date', 'User', 'Activity Type', 'Description', 'Department'].join(','),
        ...filteredActivities.map(activity => [
          new Date(activity.created_at).toLocaleString(),
          activity.profiles?.full_name || 'System',
          activity.activity_type,
          `"${activity.description.replace(/"/g, '""')}"`,
          activity.profiles?.department?.name || 'N/A'
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `activities-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: "Export Successful",
        description: "Activities exported to CSV file",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export activities",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header with Back Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <PageHeaderBackButton
            to="/dashboard/admin"
            label="Back to Admin"
          />
          <div className="flex items-center gap-2">
            <Activity className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">System Activities</h1>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={loadActivities}
            disabled={loading}
            className="neumorphism-button border border-border/30 hover:bg-muted/30"
            style={{ borderRadius: '8px' }}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={exportActivities}
            className="neumorphism-button border border-border/30 hover:bg-muted/30"
            style={{ borderRadius: '8px' }}
          >
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Activities</p>
                <p className="text-2xl font-bold">{stats.totalActivities}</p>
              </div>
              <Activity className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card className="neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Today's Activities</p>
                <p className="text-2xl font-bold">{stats.todayActivities}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active Users (24h)</p>
                <p className="text-2xl font-bold">{stats.activeUsers}</p>
              </div>
              <Users className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Top Activity</p>
                <p className="text-lg font-bold">{stats.topActivity}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="neumorphism-card bg-background/50 border border-border/20" style={{ borderRadius: '8px' }}>
          <TabsTrigger value="overview" className="neumorphism-button" style={{ borderRadius: '8px' }}>
            <Activity className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="management" className="neumorphism-button" style={{ borderRadius: '8px' }}>
            <Settings className="h-4 w-4 mr-2" />
            Management
          </TabsTrigger>
          <TabsTrigger value="analytics" className="neumorphism-button" style={{ borderRadius: '8px' }}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Filters */}
          <Card className="neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Search
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Search</label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search activities..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 border border-border/30"
                      style={{ borderRadius: '8px' }}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Activity Type</label>
                  <Select value={filterType} onValueChange={setFilterType}>
                    <SelectTrigger className="border border-border/30" style={{ borderRadius: '8px' }}>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="user_login">User Login</SelectItem>
                      <SelectItem value="user_logout">User Logout</SelectItem>
                      <SelectItem value="project_created">Project Created</SelectItem>
                      <SelectItem value="project_updated">Project Updated</SelectItem>
                      <SelectItem value="database_query">Database Query</SelectItem>
                      <SelectItem value="settings_updated">Settings Updated</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">User</label>
                  <Select value={filterUser} onValueChange={setFilterUser}>
                    <SelectTrigger className="border border-border/30" style={{ borderRadius: '8px' }}>
                      <SelectValue placeholder="All users" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Users</SelectItem>
                      {Array.from(new Set(activities.map(a => a.user_id))).map(userId => {
                        const user = activities.find(a => a.user_id === userId)?.profiles;
                        return (
                          <SelectItem key={userId} value={userId}>
                            {user?.full_name || user?.email || 'Unknown User'}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activities Table */}
          <Card className="neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activities ({filteredActivities.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">Loading activities...</span>
                </div>
              ) : filteredActivities.length === 0 ? (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No activities found matching your criteria</p>
                </div>
              ) : (
                <ScrollArea className="h-[600px]">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Activity</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Department</TableHead>
                        <TableHead>Time</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredActivities.map((activity) => (
                        <TableRow key={activity.id} className="hover:bg-muted/50">
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="font-medium">{activity.profiles?.full_name || 'System'}</p>
                                <p className="text-xs text-muted-foreground">{activity.profiles?.email}</p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getActivityIcon(activity.activity_type)}
                              {getSeverityBadge(activity.activity_type)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <p className="max-w-xs truncate" title={activity.description}>
                              {activity.description}
                            </p>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="text-xs">
                              {activity.profiles?.department?.name || 'N/A'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Clock className="h-3 w-3" />
                              {new Date(activity.created_at).toLocaleString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedActivity(activity);
                                setShowDetails(true);
                              }}
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="management">
          <ActivityManagement />
        </TabsContent>

        <TabsContent value="analytics">
          <ActivityAnalyticsManagement />
        </TabsContent>
      </Tabs>

      {/* Activity Details Modal */}
      {showDetails && selectedActivity && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="w-full max-w-2xl max-h-[80vh] overflow-hidden neumorphism-card border border-border/20" style={{ borderRadius: '8px' }}>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Activity Details
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(false)}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[400px] space-y-4">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">User</label>
                    <p className="text-sm">{selectedActivity.profiles?.full_name || 'System'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email</label>
                    <p className="text-sm">{selectedActivity.profiles?.email || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Department</label>
                    <p className="text-sm">{selectedActivity.profiles?.department?.name || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Activity Type</label>
                    <div className="mt-1">
                      {getSeverityBadge(selectedActivity.activity_type)}
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="text-sm">{selectedActivity.description}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Timestamp</label>
                    <p className="text-sm">{new Date(selectedActivity.created_at).toLocaleString()}</p>
                  </div>
                  {selectedActivity.metadata && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Metadata</label>
                      <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(selectedActivity.metadata, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};
