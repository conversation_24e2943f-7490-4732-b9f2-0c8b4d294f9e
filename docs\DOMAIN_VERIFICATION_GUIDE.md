# 🔐 Domain Verification Guide for CTNL AI WORK-BOARD

## Current Status
- ✅ **Email System**: Fully functional with Resend API
- ✅ **App Name**: Updated to "CTNL AI WORK-BOARD" throughout the system
- ⚠️ **Domain**: Currently using `resend.dev` (testing mode)
- 🎯 **Target**: Verify `ai.ctnigeria.com` for production emails

## 📧 Current Email Configuration

### Working Configuration
```typescript
From: CTNL AI WORK-BOARD <<EMAIL>>
API Key: re_iEWVBukX_AM4BhzUNeyxxVTLdHNrLLGqs (Active)
Status: ✅ Functional (Testing Mode)
Recipient Limit: <EMAIL> only
```

### Target Configuration
```typescript
From: CTNL AI WORK-BOARD <<EMAIL>>
Domain: ai.ctnigeria.com
Status: ❌ Needs Verification
Recipient Limit: Unlimited (after verification)
```

## 🚀 Steps to Verify ai.ctnigeria.com Domain

### Step 1: Access Resend Dashboard
1. Go to [https://resend.com/domains](https://resend.com/domains)
2. Sign in with your Resend account
3. Click "Add Domain"

### Step 2: Add Domain
1. Enter domain: `ai.ctnigeria.com`
2. Click "Add Domain"
3. Resend will provide DNS records to add

### Step 3: DNS Configuration
Add these DNS records to your domain provider (where ai.ctnigeria.com is hosted):

#### Required DNS Records
```
Type: TXT
Name: @
Value: [Resend will provide this value]

Type: MX
Name: @
Value: feedback-smtp.us-east-1.amazonses.com
Priority: 10

Type: TXT
Name: _dmarc
Value: v=DMARC1; p=none;

Type: TXT
Name: resend._domainkey
Value: [Resend will provide this DKIM value]
```

### Step 4: Verification
1. After adding DNS records, wait 24-48 hours for propagation
2. Return to Resend dashboard
3. Click "Verify Domain" next to ai.ctnigeria.com
4. Resend will check DNS records and verify the domain

### Step 5: Update Email Configuration
Once verified, update the email configuration:

```typescript
// In supabase/functions/send-notification/index.ts
from: 'CTNL AI WORK-BOARD <<EMAIL>>',
```

## 🔧 Quick Domain Switch Script

Use this script to switch between testing and production domains:

```bash
# Switch to production domain (after verification)
npm run email:production

# Switch back to testing domain
npm run email:testing
```

## 📋 Verification Checklist

### Pre-Verification (Current State)
- [x] Resend API key configured
- [x] Email templates updated with new app name
- [x] Testing emails working with resend.dev
- [x] All email functions updated
- [x] User manual updated with new app name

### Post-Verification (After Domain Setup)
- [ ] ai.ctnigeria.com domain added to Resend
- [ ] DNS records configured
- [ ] Domain verification completed
- [ ] Email configuration updated to use custom domain
- [ ] Production email testing completed
- [ ] All recipients can receive emails

## 🧪 Testing Commands

### Test Current Configuration
```bash
npx tsx scripts/test-resend-direct.ts
```

### Test Supabase Function
```bash
npx tsx scripts/test-email-domain.ts
```

### Send Manual Notification
```bash
npx tsx scripts/send-manual-notification.ts
```

## 🚨 Important Notes

1. **Current Limitation**: In testing mode, emails can only be sent to `<EMAIL>`
2. **Domain Verification**: Required to send emails to any recipient
3. **DNS Propagation**: Can take 24-48 hours
4. **Backup Plan**: System will continue working with resend.dev if domain verification fails

## 📞 Support

If you encounter issues with domain verification:
- Check DNS propagation: [https://dnschecker.org](https://dnschecker.org)
- Contact Resend support: [https://resend.com/support](https://resend.com/support)
- Email technical support: <EMAIL>

## 🎯 Next Steps

1. **Immediate**: System is functional with current configuration
2. **Short-term**: Complete domain verification for ai.ctnigeria.com
3. **Long-term**: Monitor email delivery and performance

---

**Status**: ✅ Email system fully functional with CTNL AI WORK-BOARD branding
**Last Updated**: 2024-07-07
**Configuration**: Testing mode with resend.dev domain
