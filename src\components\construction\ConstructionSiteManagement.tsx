
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { MapPin, Users, AlertTriangle, CheckCircle, Plus, FileText, Edit } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { AddConstructionSiteDialog } from "./AddConstructionSiteDialog";

export const ConstructionSiteManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddSiteDialogOpen, setIsAddSiteDialogOpen] = useState(false);
  const [editingSite, setEditingSite] = useState<any>(null);
  const { toast } = useToast();

  const { data: sites, isLoading } = useQuery({
    queryKey: ['construction-sites'],
    queryFn: async () => {
      // Try the query first
      let { data, error } = await supabase
        .from('construction_sites')
        .select(`
          id,
          site_name,
          location,
          status,
          site_type,
          start_date,
          expected_completion,
          budget_allocated,
          budget_spent,
          contractor_name,
          safety_rating,
          created_at
        `)
        .order('created_at', { ascending: false });

      // If table doesn't exist, return empty array
      if (error && (error.status === 404 || error.code === 'PGRST106')) {
        console.log('construction_sites table not found, returning empty array');
        return [];
      }

      if (error) throw error;
      return data || [];
    },
  });

  const { data: recentReports } = useQuery({
    queryKey: ['recent-construction-reports'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('construction_reports')
        .select(`
          id,
          report_title,
          site_id,
          progress_percentage,
          created_at,
          profiles:created_by (full_name)
        `)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data;
    },
  });

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-500/20 text-green-500';
      case 'completed':
        return 'bg-blue-500/20 text-blue-500';
      case 'planning':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'on-hold':
        return 'bg-orange-500/20 text-orange-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const getSafetyColor = (rating: string) => {
    switch (rating?.toLowerCase()) {
      case 'excellent':
        return 'text-green-600';
      case 'good':
        return 'text-blue-600';
      case 'fair':
        return 'text-yellow-600';
      case 'poor':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const filteredSites = sites?.filter(site =>
    site.site_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    site.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Construction Site Management</h2>
          <p className="text-muted-foreground">Monitor and manage construction projects</p>
        </div>
        <Button onClick={() => setIsAddSiteDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Site
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <MapPin className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Sites</p>
              <p className="text-2xl font-bold">{sites?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Active Sites</p>
              <p className="text-2xl font-bold">
                {sites?.filter(site => site.status === 'active').length || 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Completed</p>
              <p className="text-2xl font-bold">
                {sites?.filter(site => site.status === 'completed').length || 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <AlertTriangle className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">On Hold</p>
              <p className="text-2xl font-bold">
                {sites?.filter(site => site.status === 'on-hold').length || 0}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Construction Sites</CardTitle>
              <div className="flex gap-4">
                <Input
                  placeholder="Search sites..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  <div className="text-center py-8">Loading construction sites...</div>
                ) : filteredSites?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No construction sites found
                  </div>
                ) : (
                  filteredSites?.map((site) => (
                    <Card key={site.id} className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <h3 className="font-semibold flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            {site.site_name}
                          </h3>
                          <p className="text-sm text-muted-foreground">{site.location}</p>
                          <div className="flex gap-2 flex-wrap">
                            <Badge className={getStatusColor(site.status)}>
                              {site.status}
                            </Badge>
                            <Badge variant="outline">{site.site_type}</Badge>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Contractor</p>
                              <p className="font-medium">{site.contractor_name || 'Not assigned'}</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Safety Rating</p>
                              <p className={`font-medium ${getSafetyColor(site.safety_rating)}`}>
                                {site.safety_rating || 'Not rated'}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Budget</p>
                              <p className="font-medium">
                                ₦{site.budget_spent?.toLocaleString()} / ₦{site.budget_allocated?.toLocaleString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Expected Completion</p>
                              <p className="font-medium">
                                {site.expected_completion ? new Date(site.expected_completion).toLocaleDateString() : 'TBD'}
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setEditingSite(site);
                              setIsAddSiteDialogOpen(true);
                            }}
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              toast({
                                title: "Site Details",
                                description: `Viewing details for ${site.site_name}`,
                              });
                            }}
                          >
                            <FileText className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Recent Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentReports?.map((report) => (
                  <div key={report.id} className="p-3 border rounded-lg">
                    <h4 className="font-medium text-sm">{report.report_title}</h4>
                    <p className="text-xs text-muted-foreground">
                      By: {report.profiles?.full_name || 'Unknown'}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(report.created_at).toLocaleDateString()}
                    </p>
                    {report.progress_percentage && (
                      <div className="mt-2">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full" 
                            style={{ width: `${report.progress_percentage}%` }}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground mt-1">
                          {report.progress_percentage}% complete
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Add Site Dialog */}
      <AddConstructionSiteDialog
        isOpen={isAddSiteDialogOpen}
        onOpenChange={setIsAddSiteDialogOpen}
        onSiteAdded={() => {
          setIsAddSiteDialogOpen(false);
          toast({
            title: "Success",
            description: "Construction site added successfully",
          });
        }}
        editingSite={editingSite}
      />
    </div>
  );
};
