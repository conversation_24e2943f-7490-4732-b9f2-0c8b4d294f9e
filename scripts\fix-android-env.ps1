# Android Environment Fix Script
# This script fixes common Android development environment issues

Write-Host "🔧 Fixing Android Development Environment..." -ForegroundColor Green

# Set Java Home
$javaPath = "C:\Program Files\Java\jdk-21"
if (Test-Path $javaPath) {
    $env:JAVA_HOME = $javaPath
    Write-Host "✅ JAVA_HOME set to: $javaPath" -ForegroundColor Green
} else {
    Write-Host "❌ Java JDK not found at: $javaPath" -ForegroundColor Red
    Write-Host "Please install Java JDK 21 or update the path in this script" -ForegroundColor Yellow
}

# Set Android Home
$androidPath = "C:\Users\<USER>\AppData\Local\Android\Sdk"
if (Test-Path $androidPath) {
    $env:ANDROID_HOME = $androidPath
    Write-Host "✅ ANDROID_HOME set to: $androidPath" -ForegroundColor Green
} else {
    Write-Host "❌ Android SDK not found at: $androidPath" -ForegroundColor Red
    Write-Host "Please install Android Studio or update the path in this script" -ForegroundColor Yellow
}

# Add Android tools to PATH
$env:PATH += ";$androidPath\platform-tools;$androidPath\tools"

Write-Host "🔄 Building mobile app..." -ForegroundColor Green
npm run mobile:build

Write-Host "📱 Opening Android Studio..." -ForegroundColor Green
npx cap open android

Write-Host "✅ Environment setup complete!" -ForegroundColor Green
Write-Host "📋 Next steps:" -ForegroundColor Cyan
Write-Host "1. Wait for Gradle sync to complete in Android Studio" -ForegroundColor White
Write-Host "2. Set up a device or emulator" -ForegroundColor White
Write-Host "3. Click the Run button (green play icon)" -ForegroundColor White
Write-Host "4. Test the mobile features" -ForegroundColor White 