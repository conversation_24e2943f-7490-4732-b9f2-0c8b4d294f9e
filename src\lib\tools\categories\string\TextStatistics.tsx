import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Co<PERSON>, RotateCcw, BarChart3, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

interface TextStats {
  characters: number;
  charactersNoSpaces: number;
  words: number;
  sentences: number;
  paragraphs: number;
  lines: number;
  averageWordsPerSentence: number;
  averageCharsPerWord: number;
  readingTime: number;
  speakingTime: number;
  mostCommonWords: Array<{ word: string; count: number }>;
  characterFrequency: Array<{ char: string; count: number; percentage: number }>;
  readabilityScore: number;
}

const TextStatistics: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [stats, setStats] = useState<TextStats | null>(null);

  const calculateStats = (text: string): TextStats => {
    if (!text.trim()) {
      return {
        characters: 0,
        charactersNoSpaces: 0,
        words: 0,
        sentences: 0,
        paragraphs: 0,
        lines: 0,
        averageWordsPerSentence: 0,
        averageCharsPerWord: 0,
        readingTime: 0,
        speakingTime: 0,
        mostCommonWords: [],
        characterFrequency: [],
        readabilityScore: 0
      };
    }

    // Basic counts
    const characters = text.length;
    const charactersNoSpaces = text.replace(/\s/g, '').length;
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
    const lines = text.split('\n');

    // Averages
    const averageWordsPerSentence = sentences.length > 0 ? words.length / sentences.length : 0;
    const averageCharsPerWord = words.length > 0 ? charactersNoSpaces / words.length : 0;

    // Reading and speaking time (average reading: 200 WPM, speaking: 150 WPM)
    const readingTime = words.length / 200;
    const speakingTime = words.length / 150;

    // Most common words
    const wordFreq: { [key: string]: number } = {};
    words.forEach(word => {
      const cleanWord = word.toLowerCase().replace(/[^\w]/g, '');
      if (cleanWord.length > 2) { // Only count words longer than 2 characters
        wordFreq[cleanWord] = (wordFreq[cleanWord] || 0) + 1;
      }
    });

    const mostCommonWords = Object.entries(wordFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));

    // Character frequency
    const charFreq: { [key: string]: number } = {};
    for (const char of text.toLowerCase()) {
      if (char.match(/[a-z]/)) {
        charFreq[char] = (charFreq[char] || 0) + 1;
      }
    }

    const totalLetters = Object.values(charFreq).reduce((sum, count) => sum + count, 0);
    const characterFrequency = Object.entries(charFreq)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([char, count]) => ({
        char: char.toUpperCase(),
        count,
        percentage: (count / totalLetters) * 100
      }));

    // Simple readability score (Flesch Reading Ease approximation)
    const avgSentenceLength = averageWordsPerSentence;
    const avgSyllablesPerWord = averageCharsPerWord / 2; // Rough approximation
    const readabilityScore = Math.max(0, Math.min(100, 
      206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
    ));

    return {
      characters,
      charactersNoSpaces,
      words: words.length,
      sentences: sentences.length,
      paragraphs: paragraphs.length,
      lines: lines.length,
      averageWordsPerSentence,
      averageCharsPerWord,
      readingTime,
      speakingTime,
      mostCommonWords,
      characterFrequency,
      readabilityScore
    };
  };

  useEffect(() => {
    setStats(calculateStats(inputText));
  }, [inputText]);

  const copyStatsToClipboard = async () => {
    if (!stats) return;

    const statsText = `Text Statistics:
Characters: ${stats.characters}
Characters (no spaces): ${stats.charactersNoSpaces}
Words: ${stats.words}
Sentences: ${stats.sentences}
Paragraphs: ${stats.paragraphs}
Lines: ${stats.lines}
Average words per sentence: ${stats.averageWordsPerSentence.toFixed(1)}
Average characters per word: ${stats.averageCharsPerWord.toFixed(1)}
Reading time: ${formatTime(stats.readingTime)}
Speaking time: ${formatTime(stats.speakingTime)}
Readability score: ${stats.readabilityScore.toFixed(1)}`;

    try {
      await navigator.clipboard.writeText(statsText);
      toast.success('Statistics copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy statistics');
    }
  };

  const clearAll = () => {
    setInputText('');
    setStats(null);
  };

  const loadSampleText = () => {
    const sampleText = `The quick brown fox jumps over the lazy dog. This pangram contains every letter of the alphabet at least once. It's commonly used for testing fonts and keyboards.

Pangrams are useful in typography and graphic design. They help designers see how different fonts look with a complete set of letters. The phrase has been used since the late 1800s.

Modern variations include "Pack my box with five dozen liquor jugs" and "Waltz, bad nymph, for quick jigs vex." Each serves the same purpose but with different character counts and complexity levels.`;
    setInputText(sampleText);
  };

  const formatTime = (minutes: number): string => {
    if (minutes < 1) {
      return `${Math.round(minutes * 60)} seconds`;
    } else if (minutes < 60) {
      return `${Math.round(minutes)} minutes`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = Math.round(minutes % 60);
      return `${hours}h ${mins}m`;
    }
  };

  const getReadabilityLevel = (score: number): { level: string; color: string } => {
    if (score >= 90) return { level: 'Very Easy', color: 'text-green-600' };
    if (score >= 80) return { level: 'Easy', color: 'text-green-500' };
    if (score >= 70) return { level: 'Fairly Easy', color: 'text-yellow-500' };
    if (score >= 60) return { level: 'Standard', color: 'text-orange-500' };
    if (score >= 50) return { level: 'Fairly Difficult', color: 'text-red-500' };
    if (score >= 30) return { level: 'Difficult', color: 'text-red-600' };
    return { level: 'Very Difficult', color: 'text-red-700' };
  };

  const readability = stats ? getReadabilityLevel(stats.readabilityScore) : null;

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50 border-indigo-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-indigo-800">
            <div className="p-2 bg-indigo-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-indigo-600" />
            </div>
            {title || 'Text Statistics'}
          </CardTitle>
          <p className="text-indigo-600 text-sm">
            Analyze text for detailed statistics and readability metrics
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Text */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span>📝</span>
                Text to Analyze:
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleText}
                className="flex items-center gap-2"
              >
                <TrendingUp className="h-4 w-4" />
                Sample
              </Button>
            </div>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter or paste your text here for analysis..."
              className="min-h-[160px] resize-none border-2 border-indigo-200 focus:border-indigo-400"
            />
          </div>

          {stats && stats.characters > 0 && (
            <>
              {/* Basic Statistics */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-blue-600">{stats.characters.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Characters</div>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-green-600">{stats.words.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Words</div>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-purple-600">{stats.sentences.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Sentences</div>
                </div>
                <div className="text-center p-3 bg-white rounded-lg border">
                  <div className="text-2xl font-bold text-orange-600">{stats.paragraphs.toLocaleString()}</div>
                  <div className="text-sm text-gray-600">Paragraphs</div>
                </div>
              </div>

              {/* Detailed Statistics */}
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Detailed Metrics</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Characters (no spaces):</span>
                      <span className="font-medium">{stats.charactersNoSpaces.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Lines:</span>
                      <span className="font-medium">{stats.lines.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg words/sentence:</span>
                      <span className="font-medium">{stats.averageWordsPerSentence.toFixed(1)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Avg chars/word:</span>
                      <span className="font-medium">{stats.averageCharsPerWord.toFixed(1)}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Time Estimates</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Reading time:</span>
                      <span className="font-medium">{formatTime(stats.readingTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Speaking time:</span>
                      <span className="font-medium">{formatTime(stats.speakingTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Readability:</span>
                      <span className={`font-medium ${readability?.color}`}>
                        {readability?.level} ({stats.readabilityScore.toFixed(1)})
                      </span>
                    </div>
                    <div className="space-y-1">
                      <Progress value={stats.readabilityScore} className="h-2" />
                      <div className="text-xs text-gray-500">Flesch Reading Ease Score</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Most Common Words */}
              {stats.mostCommonWords.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Most Common Words</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {stats.mostCommonWords.map(({ word, count }, index) => (
                        <Badge key={word} variant="secondary" className="text-sm">
                          #{index + 1} {word} ({count})
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Character Frequency */}
              {stats.characterFrequency.length > 0 && (
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg">Character Frequency</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {stats.characterFrequency.map(({ char, count, percentage }) => (
                        <div key={char} className="flex items-center gap-3">
                          <span className="font-mono font-bold w-6">{char}</span>
                          <div className="flex-1">
                            <Progress value={percentage} className="h-2" />
                          </div>
                          <span className="text-sm text-gray-600 w-16 text-right">
                            {count} ({percentage.toFixed(1)}%)
                          </span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 justify-center">
                <Button
                  variant="outline"
                  onClick={copyStatsToClipboard}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy Statistics
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              </div>
            </>
          )}

          {(!stats || stats.characters === 0) && (
            <div className="text-center py-12 text-gray-500">
              <div className="text-6xl mb-4">📊</div>
              <h3 className="text-lg font-medium text-gray-700 mb-2">Ready to Analyze</h3>
              <p className="text-sm">Enter some text above to see detailed statistics and analysis</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Understanding the Metrics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Reading Time:</strong> Based on 200 words per minute</p>
              <p><strong className="text-green-600">Speaking Time:</strong> Based on 150 words per minute</p>
              <p><strong className="text-purple-600">Readability Score:</strong> Flesch Reading Ease (0-100)</p>
            </div>
            <div className="space-y-2">
              <p><strong className="text-orange-600">90-100:</strong> Very Easy (5th grade)</p>
              <p><strong className="text-red-600">60-70:</strong> Standard (8th-9th grade)</p>
              <p><strong className="text-indigo-600">0-30:</strong> Very Difficult (graduate level)</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextStatistics;
