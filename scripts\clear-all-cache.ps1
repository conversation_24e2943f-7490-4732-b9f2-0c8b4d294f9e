# Comprehensive cache clearing script for CTNL AI Workboard
Write-Host "🧹 Clearing All Caches for CTNL AI Workboard..." -ForegroundColor Cyan

# Function to safely remove directories
function Remove-SafeDirectory {
    param($Path, $Description)
    if (Test-Path $Path) {
        Write-Host "   Removing $Description..." -ForegroundColor Yellow
        Remove-Item -Recurse -Force $Path -ErrorAction SilentlyContinue
        Write-Host "   ✅ $Description cleared" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️  $Description not found (already clean)" -ForegroundColor Gray
    }
}

Write-Host "`n1. 🗂️  Clearing Build Caches..." -ForegroundColor Blue
Remove-SafeDirectory "dist" "Build output directory"
Remove-SafeDirectory ".vite" "Vite cache"
Remove-SafeDirectory "node_modules/.vite" "Vite node_modules cache"
Remove-SafeDirectory "node_modules/.cache" "Node modules cache"

Write-Host "`n2. 📦 Clearing NPM Caches..." -ForegroundColor Blue
Write-Host "   Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force 2>$null
Write-Host "   ✅ NPM cache cleared" -ForegroundColor Green

Write-Host "`n3. 🔧 Clearing TypeScript Caches..." -ForegroundColor Blue
Remove-SafeDirectory "node_modules/.tsbuildinfo" "TypeScript build info"
Remove-SafeDirectory ".tsbuildinfo" "TypeScript build info"

Write-Host "`n4. 🎨 Clearing CSS/Style Caches..." -ForegroundColor Blue
Remove-SafeDirectory "node_modules/.postcss-cache" "PostCSS cache"
Remove-SafeDirectory ".postcss-cache" "PostCSS cache"

Write-Host "`n5. 🌐 Clearing Browser Caches..." -ForegroundColor Blue
Write-Host "   Browser cache clearing instructions:" -ForegroundColor Yellow
Write-Host "   - Chrome: Ctrl+Shift+Delete or F12 > Application > Storage > Clear" -ForegroundColor Gray
Write-Host "   - Firefox: Ctrl+Shift+Delete" -ForegroundColor Gray
Write-Host "   - Edge: Ctrl+Shift+Delete" -ForegroundColor Gray

Write-Host "`n6. 🔄 Reinstalling Dependencies..." -ForegroundColor Blue
if (Test-Path "package-lock.json") {
    Write-Host "   Removing package-lock.json..." -ForegroundColor Yellow
    Remove-Item "package-lock.json" -Force -ErrorAction SilentlyContinue
}

if (Test-Path "node_modules") {
    Write-Host "   Removing node_modules..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
    Write-Host "   ✅ node_modules removed" -ForegroundColor Green
}

Write-Host "   Installing fresh dependencies..." -ForegroundColor Yellow
npm install
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Dependencies installed successfully" -ForegroundColor Green
} else {
    Write-Host "   ❌ Dependency installation failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n7. 🏗️  Testing Clean Build..." -ForegroundColor Blue
Write-Host "   Building application..." -ForegroundColor Yellow
npm run build
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✅ Clean build successful" -ForegroundColor Green
} else {
    Write-Host "   ❌ Build failed" -ForegroundColor Red
    exit 1
}

Write-Host "`n✅ All Caches Cleared Successfully!" -ForegroundColor Green
Write-Host "`n📋 Cache Clearing Summary:" -ForegroundColor Cyan
Write-Host "   ✅ Build output cleared (dist/)" -ForegroundColor White
Write-Host "   ✅ Vite cache cleared" -ForegroundColor White
Write-Host "   ✅ NPM cache cleared" -ForegroundColor White
Write-Host "   ✅ TypeScript cache cleared" -ForegroundColor White
Write-Host "   ✅ Dependencies reinstalled" -ForegroundColor White
Write-Host "   ✅ Clean build verified" -ForegroundColor White

Write-Host "`n🚀 Next Steps:" -ForegroundColor Blue
Write-Host "   1. Clear browser cache manually (Ctrl+Shift+Delete)" -ForegroundColor White
Write-Host "   2. Run: npm run dev (for development)" -ForegroundColor White
Write-Host "   3. Run: npm run build (for production)" -ForegroundColor White
Write-Host "   4. Deploy with clean cache" -ForegroundColor White

Write-Host "`n🎯 Your CTNL AI Workboard is now cache-free and ready!" -ForegroundColor Green
