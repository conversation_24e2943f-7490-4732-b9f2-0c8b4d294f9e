-- ============================================================================
-- EMERGENCY RLS FIX - IMMEDIATE RESOLUTION
-- Run this immediately to fix the infinite recursion issue
-- ============================================================================

-- STEP 1: Disable RLS temporarily to stop the recursion
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- STEP 2: Drop all problematic policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;

-- STEP 3: Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- STEP 4: Create simple, safe policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id);

-- STEP 5: Allow service role full access (for admin operations)
CREATE POLICY "profiles_service_role" ON public.profiles
    FOR ALL
    USING (current_setting('role') = 'service_role');

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🚨 EMERGENCY FIX APPLIED!';
    RAISE NOTICE '✅ RLS infinite recursion resolved';
    RAISE NOTICE '✅ Safe policies created';
    RAISE NOTICE '🔐 Authentication should work now!';
END $$;
