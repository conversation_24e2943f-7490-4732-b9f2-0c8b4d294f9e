import { useTheme } from '@/components/theme-provider'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { Monitor, Moon, Sun } from 'lucide-react'
import { useEffect, useState } from 'react'

export const ThemeSwitcher = () => {
  const { theme, setTheme } = useTheme()
  const [showThemeDialog, setShowThemeDialog] = useState(false)

  useEffect(() => {
    const hasSeenThemeDialog = localStorage.getItem('hasSeenThemeDialog')
    if (!hasSeenThemeDialog) {
      setShowThemeDialog(true)
      localStorage.setItem('hasSeenThemeDialog', 'true')
    }
  }, [])

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <DropdownMenu>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant='outline'
                  size='icon'
                  className='w-10 h-10 rounded-full
                    bg-white/10 dark:bg-gray-800/30
                    hover:bg-gray-100/80 dark:hover:bg-gray-700/50
                    border border-gray-200/30 dark:border-gray-700/30
                    transition-colors duration-200'
                >
                  {theme === 'dark'
                    ? (
                      <Moon className='h-5 w-5 text-primary transition-all duration-200' />
                      )
                    : theme === 'light'
                      ? (
                        <Sun className='h-5 w-5 text-primary transition-all duration-200' />
                        )
                      : (
                        <Monitor className='h-5 w-5 text-primary transition-all duration-200' />
                        )}
                  <span className='sr-only'>Toggle theme</span>
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuItem onClick={() => setTheme('light')}>
                <Sun className='mr-2 h-4 w-4' />
                <span>Light</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('dark')}>
                <Moon className='mr-2 h-4 w-4' />
                <span>Dark</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('system')}>
                <Monitor className='mr-2 h-4 w-4' />
                <span>System</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <TooltipContent>
            <p>Change theme</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Dialog open={showThemeDialog} onOpenChange={setShowThemeDialog}>
        <DialogContent className='w-[90vw] max-w-[400px] mx-auto p-4 sm:p-6 max-h-[90vh] overflow-y-auto'>
          <DialogHeader className='space-y-3'>
            <DialogTitle className='text-lg sm:text-xl'>Theme Preference</DialogTitle>
            <DialogDescription className='text-sm sm:text-base leading-relaxed'>
              Try our dark mode for a more comfortable viewing experience!
              Click the theme switcher button in the top-right corner to toggle between light, dark, and system modes.
            </DialogDescription>
          </DialogHeader>

          {/* Theme Preview Buttons */}
          <div className='grid grid-cols-1 sm:grid-cols-3 gap-3 mt-6'>
            <button
              onClick={() => {
                setTheme('light')
                setShowThemeDialog(false)
              }}
              className='flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors'
            >
              <Sun className='h-5 w-5 text-yellow-500' />
              <div className='text-left'>
                <div className='font-medium text-sm'>Light</div>
                <div className='text-xs text-muted-foreground'>Bright and clean</div>
              </div>
            </button>

            <button
              onClick={() => {
                setTheme('dark')
                setShowThemeDialog(false)
              }}
              className='flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors'
            >
              <Moon className='h-5 w-5 text-blue-500' />
              <div className='text-left'>
                <div className='font-medium text-sm'>Dark</div>
                <div className='text-xs text-muted-foreground'>Easy on the eyes</div>
              </div>
            </button>

            <button
              onClick={() => {
                setTheme('system')
                setShowThemeDialog(false)
              }}
              className='flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors'
            >
              <Monitor className='h-5 w-5 text-gray-500' />
              <div className='text-left'>
                <div className='font-medium text-sm'>System</div>
                <div className='text-xs text-muted-foreground'>Follows device</div>
              </div>
            </button>
          </div>

          {/* Close Button */}
          <div className='flex justify-end mt-6'>
            <Button
              variant='outline'
              onClick={() => setShowThemeDialog(false)}
              className='px-6'
            >
              Got it!
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
