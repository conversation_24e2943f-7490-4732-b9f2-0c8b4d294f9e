import { useAuth } from "@/components/auth/AuthProvider";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { handleAIError } from "@/lib/ai/diagnostics";
import {
    Brain,
    Download,
    Eye,
    FileImage,
    FileSpreadsheet,
    FileText,
    Search,
    Tag,
    Trash2,
    Upload,
    Zap
} from "lucide-react";
import React, { useCallback, useRef, useState } from 'react';

interface DocumentAnalysis {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadTime: Date;
  status: 'processing' | 'completed' | 'error';
  analysis: {
    summary: string;
    keyPoints: string[];
    entities: Array<{ name: string; type: string; confidence: number }>;
    sentiment: { score: number; label: string };
    categories: string[];
    extractedText?: string;
    metadata?: any;
  };
  processingTime?: number;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  documentId?: string;
}

export const DocumentAnalysisInterface: React.FC = () => {
  const [documents, setDocuments] = useState<DocumentAnalysis[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentAnalysis | null>(null);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isChatting, setIsChatting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('upload');
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  const handleFileUpload = useCallback(async (files: FileList) => {
    setIsProcessing(true);
    setUploadProgress(0);

    for (const file of Array.from(files)) {
      // Create document analysis record outside try block so it's accessible in catch
      const newDoc: DocumentAnalysis = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        fileName: file.name,
        fileType: file.type || 'unknown',
        fileSize: file.size,
        uploadTime: new Date(),
        status: 'processing',
        analysis: {
          summary: '',
          keyPoints: [],
          entities: [],
          sentiment: { score: 0, label: 'neutral' },
          categories: []
        }
      };

      try {
        const startTime = Date.now();

        setDocuments(prev => [newDoc, ...prev]);
        setUploadProgress(25);

        // Read file content
        const fileContent = await readFileContent(file);
        setUploadProgress(50);

        // Call AI analysis function
        const { data, error } = await supabase.functions.invoke('ai-file-analyzer', {
          body: {
            fileName: file.name,
            fileType: file.type,
            content: fileContent,
            userId: userProfile?.id
          }
        });

        setUploadProgress(75);

        if (error) {
          console.error('AI File Analysis Error:', error);
          throw new Error(`AI Analysis failed: ${error.message || 'Service unavailable'}`);
        }

        // Mock analysis results for demonstration
        const mockAnalysis = {
          summary: `This document appears to be a ${getDocumentType(file.name)} containing important business information. The content includes strategic planning, operational procedures, and key performance indicators.`,
          keyPoints: [
            'Strategic objectives for Q4 2024',
            'Budget allocation and resource planning',
            'Performance metrics and KPIs',
            'Risk assessment and mitigation strategies',
            'Implementation timeline and milestones'
          ],
          entities: [
            { name: 'Q4 2024', type: 'DATE', confidence: 0.95 },
            { name: 'Budget Planning', type: 'CONCEPT', confidence: 0.88 },
            { name: 'Performance Metrics', type: 'CONCEPT', confidence: 0.92 },
            { name: 'Risk Management', type: 'CONCEPT', confidence: 0.85 }
          ],
          sentiment: { score: 0.7, label: 'positive' },
          categories: ['Business Planning', 'Strategy', 'Finance', 'Operations'],
          extractedText: fileContent.substring(0, 500) + '...',
          metadata: {
            wordCount: fileContent.split(' ').length,
            pageCount: Math.ceil(fileContent.length / 2000),
            language: 'en'
          }
        };

        const processingTime = Date.now() - startTime;
        setUploadProgress(100);

        // Update document with analysis
        setDocuments(prev => prev.map(doc => 
          doc.id === newDoc.id 
            ? { 
                ...doc, 
                status: 'completed', 
                analysis: mockAnalysis,
                processingTime 
              }
            : doc
        ));

        // Save to database
        await supabase.from('ai_documents').insert({
          user_id: userProfile?.id,
          file_name: file.name,
          file_type: file.type,
          file_size: file.size,
          analysis_result: mockAnalysis,
          processing_time: processingTime,
          status: 'completed'
        });

        toast({
          title: "📄 Document Analyzed",
          description: `${file.name} processed in ${processingTime}ms`,
        });

      } catch (error) {
        console.error('Document analysis error:', error);

        setDocuments(prev => prev.map(doc =>
          doc.id === newDoc.id
            ? { ...doc, status: 'error' }
            : doc
        ));

        const errorInfo = handleAIError(error);

        toast({
          title: errorInfo.title,
          description: errorInfo.message,
          variant: "destructive",
        });
      }
    }

    setIsProcessing(false);
    setUploadProgress(0);
  }, [userProfile?.id, toast]);

  const readFileContent = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string || '');
      reader.onerror = reject;
      reader.readAsText(file);
    });
  };

  const getDocumentType = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'pdf': return 'PDF document';
      case 'doc':
      case 'docx': return 'Word document';
      case 'xls':
      case 'xlsx': return 'Excel spreadsheet';
      case 'ppt':
      case 'pptx': return 'PowerPoint presentation';
      case 'txt': return 'text file';
      case 'csv': return 'CSV data file';
      default: return 'document';
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('image')) return <FileImage className="h-4 w-4" />;
    if (fileType.includes('spreadsheet') || fileType.includes('excel')) return <FileSpreadsheet className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const chatWithDocument = async () => {
    if (!chatInput.trim() || !selectedDocument || isChatting) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: chatInput,
      timestamp: new Date(),
      documentId: selectedDocument.id
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsChatting(true);

    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: chatInput,
          context: {
            task: 'document_chat',
            document: {
              fileName: selectedDocument.fileName,
              analysis: selectedDocument.analysis,
              extractedText: selectedDocument.analysis.extractedText
            },
            user_role: userProfile?.role
          }
        }
      });

      if (error) throw error;

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: data.response || 'I understand your question about the document.',
        timestamp: new Date(),
        documentId: selectedDocument.id
      };

      setChatMessages(prev => [...prev, assistantMessage]);

      // Log interaction
      await supabase.from('ai_interactions').insert({
        user_id: userProfile?.id,
        role: 'user',
        message: chatInput,
        type: 'document_chat',
        metadata: {
          document_id: selectedDocument.id,
          document_name: selectedDocument.fileName
        }
      });

    } catch (error) {
      console.error('Document chat error:', error);
      toast({
        title: "⚠️ Chat Error",
        description: "Failed to process your question",
        variant: "destructive",
      });
    } finally {
      setIsChatting(false);
    }
  };

  const exportAnalysis = (doc: DocumentAnalysis) => {
    const analysisData = {
      fileName: doc.fileName,
      uploadTime: doc.uploadTime,
      analysis: doc.analysis,
      processingTime: doc.processingTime
    };

    const blob = new Blob([JSON.stringify(analysisData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analysis_${doc.fileName}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const deleteDocument = (docId: string) => {
    setDocuments(prev => prev.filter(doc => doc.id !== docId));
    if (selectedDocument?.id === docId) {
      setSelectedDocument(null);
      setChatMessages([]);
    }
    toast({
      title: "🗑️ Document Deleted",
      description: "Document removed from analysis",
    });
  };

  return (
    <div className="space-y-6 text-green-400 font-mono">
      {/* Header */}
      <Card className="bg-black border-green-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-400">
            <FileText className="h-5 w-5" />
            DOCUMENT ANALYSIS SYSTEM
            <Badge variant="outline" className="border-green-500 text-green-400">
              AI POWERED
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Document List */}
        <div className="lg:col-span-1">
          <Card className="bg-black border-green-500">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-green-400 text-sm">
                DOCUMENTS
                <Badge variant="outline" className="border-green-500 text-green-400">
                  {documents.length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Upload Area */}
                <div 
                  className="border-2 border-dashed border-green-500/50 rounded-lg p-6 text-center cursor-pointer hover:border-green-400 transition-colors"
                  onClick={() => fileInputRef.current?.click()}
                  onDrop={(e) => {
                    e.preventDefault();
                    const files = e.dataTransfer.files;
                    if (files.length > 0) handleFileUpload(files);
                  }}
                  onDragOver={(e) => e.preventDefault()}
                >
                  <Upload className="h-8 w-8 mx-auto mb-2 text-green-400" />
                  <div className="text-sm text-green-400">
                    Click or drag files here
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    PDF, DOC, TXT, CSV, XLS
                  </div>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.txt,.csv,.xls,.xlsx"
                  onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                  className="hidden"
                />

                {isProcessing && (
                  <div className="space-y-2">
                    <div className="text-xs text-green-400">Processing...</div>
                    <Progress value={uploadProgress} className="h-2 bg-gray-800" />
                  </div>
                )}

                {/* Document List */}
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {documents.map((doc) => (
                      <div
                        key={doc.id}
                        className={`p-3 border rounded cursor-pointer transition-colors ${
                          selectedDocument?.id === doc.id
                            ? 'border-green-400 bg-green-500/10'
                            : 'border-green-500/30 hover:border-green-400'
                        }`}
                        onClick={() => setSelectedDocument(doc)}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getFileIcon(doc.fileType)}
                            <span className="text-xs font-semibold text-green-400 truncate">
                              {doc.fileName}
                            </span>
                          </div>
                          <div className="flex gap-1">
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                doc.status === 'completed' ? 'border-green-500 text-green-400' :
                                doc.status === 'processing' ? 'border-yellow-500 text-yellow-400' :
                                'border-red-500 text-red-400'
                              }`}
                            >
                              {doc.status.toUpperCase()}
                            </Badge>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={(e) => {
                                e.stopPropagation();
                                deleteDocument(doc.id);
                              }}
                              className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="text-xs text-gray-400">
                          {(doc.fileSize / 1024).toFixed(1)} KB • {doc.uploadTime.toLocaleTimeString()}
                        </div>
                        {doc.processingTime && (
                          <div className="text-xs text-green-300 mt-1">
                            Processed in {doc.processingTime}ms
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analysis Results */}
        <div className="lg:col-span-2">
          {selectedDocument ? (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="bg-black border border-green-500 mb-4">
                <TabsTrigger 
                  value="analysis" 
                  className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
                >
                  <Brain className="h-4 w-4 mr-2" />
                  ANALYSIS
                </TabsTrigger>
                <TabsTrigger 
                  value="chat" 
                  className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
                >
                  <Search className="h-4 w-4 mr-2" />
                  CHAT
                </TabsTrigger>
                <TabsTrigger 
                  value="extract" 
                  className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  EXTRACT
                </TabsTrigger>
              </TabsList>

              <TabsContent value="analysis">
                <Card className="bg-black border-green-500">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between text-green-400">
                      <span>ANALYSIS: {selectedDocument.fileName}</span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => exportAnalysis(selectedDocument)}
                        className="border-green-500 text-green-400 hover:bg-green-500/10"
                      >
                        <Download className="h-4 w-4 mr-1" />
                        EXPORT
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Summary */}
                    <div>
                      <h3 className="text-green-400 font-semibold mb-2">SUMMARY</h3>
                      <p className="text-green-300 text-sm">{selectedDocument.analysis.summary}</p>
                    </div>

                    {/* Key Points */}
                    <div>
                      <h3 className="text-green-400 font-semibold mb-2">KEY POINTS</h3>
                      <div className="space-y-1">
                        {selectedDocument.analysis.keyPoints.map((point, index) => (
                          <div key={index} className="flex items-start gap-2 text-sm text-green-300">
                            <div className="w-1 h-1 bg-green-400 rounded-full mt-2"></div>
                            {point}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Entities */}
                    <div>
                      <h3 className="text-green-400 font-semibold mb-2">ENTITIES</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedDocument.analysis.entities.map((entity, index) => (
                          <Badge 
                            key={index} 
                            variant="outline" 
                            className="border-green-500 text-green-400"
                          >
                            {entity.name} ({entity.type})
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Categories */}
                    <div>
                      <h3 className="text-green-400 font-semibold mb-2">CATEGORIES</h3>
                      <div className="flex flex-wrap gap-2">
                        {selectedDocument.analysis.categories.map((category, index) => (
                          <Badge 
                            key={index} 
                            variant="outline" 
                            className="border-green-500 text-green-400"
                          >
                            <Tag className="h-3 w-3 mr-1" />
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Sentiment */}
                    <div>
                      <h3 className="text-green-400 font-semibold mb-2">SENTIMENT</h3>
                      <div className="flex items-center gap-4">
                        <Badge 
                          variant="outline" 
                          className={`border-green-500 ${
                            selectedDocument.analysis.sentiment.score > 0.5 ? 'text-green-400' :
                            selectedDocument.analysis.sentiment.score < -0.5 ? 'text-red-400' :
                            'text-yellow-400'
                          }`}
                        >
                          {selectedDocument.analysis.sentiment.label.toUpperCase()}
                        </Badge>
                        <div className="text-sm text-green-300">
                          Score: {selectedDocument.analysis.sentiment.score.toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="chat">
                <Card className="bg-black border-green-500 h-96">
                  <CardHeader>
                    <CardTitle className="text-green-400 text-sm">
                      CHAT WITH DOCUMENT: {selectedDocument.fileName}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0 h-full flex flex-col">
                    <ScrollArea className="flex-1 p-4">
                      <div className="space-y-3">
                        {chatMessages.map((message) => (
                          <div 
                            key={message.id} 
                            className={`p-3 rounded ${
                              message.type === 'user' 
                                ? 'bg-green-500/10 border border-green-500/30 ml-8' 
                                : 'bg-blue-500/10 border border-blue-500/30 mr-8'
                            }`}
                          >
                            <div className="text-xs text-gray-400 mb-1">
                              {message.type === 'user' ? 'YOU' : 'AI'} • {message.timestamp.toLocaleTimeString()}
                            </div>
                            <div className="text-sm text-green-300">{message.content}</div>
                          </div>
                        ))}
                        {isChatting && (
                          <div className="flex items-center gap-2 text-yellow-400 text-sm">
                            <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                            AI is analyzing...
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                    <div className="border-t border-green-500 p-4">
                      <div className="flex gap-2">
                        <Input
                          value={chatInput}
                          onChange={(e) => setChatInput(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && chatWithDocument()}
                          placeholder="Ask questions about this document..."
                          disabled={isChatting}
                          className="bg-transparent border-green-500 text-green-400 placeholder-green-600"
                        />
                        <Button
                          onClick={chatWithDocument}
                          disabled={isChatting || !chatInput.trim()}
                          className="bg-green-500/20 border border-green-500 text-green-400 hover:bg-green-500/30"
                        >
                          <Zap className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="extract">
                <Card className="bg-black border-green-500">
                  <CardHeader>
                    <CardTitle className="text-green-400 text-sm">
                      EXTRACTED TEXT: {selectedDocument.fileName}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <div className="text-sm text-green-300 whitespace-pre-wrap font-mono">
                        {selectedDocument.analysis.extractedText || 'No text extracted'}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card className="bg-black border-green-500 h-96">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center text-gray-400">
                  <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <div>Select a document to view analysis</div>
                  <div className="text-sm mt-2">Upload documents to get started</div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
