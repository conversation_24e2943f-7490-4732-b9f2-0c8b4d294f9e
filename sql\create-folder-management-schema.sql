-- Custom Folder Management System
-- This replaces department-based folders with user-created custom folders

-- Drop existing folder-related tables if they exist
DROP TABLE IF EXISTS public.document_folders CASCADE;

-- Create custom_folders table for user-created folders
CREATE TABLE IF NOT EXISTS public.custom_folders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    parent_folder_id UUID REFERENCES public.custom_folders(id) ON DELETE CASCADE,
    folder_type TEXT DEFAULT 'custom' CHECK (folder_type IN ('custom', 'system', 'department', 'project')),
    access_level TEXT DEFAULT 'private' CHECK (access_level IN ('private', 'department', 'public')),
    
    -- Ownership and permissions
    created_by UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    shared_with UUID[] DEFAULT '{}', -- Array of user IDs who have access
    
    -- Metadata
    color TEXT DEFAULT '#3B82F6', -- Folder color for UI
    icon TEXT DEFAULT 'folder', -- Icon identifier
    is_archived BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE, -- System folders cannot be deleted
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT folder_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT no_self_parent CHECK (id != parent_folder_id)
);

-- Create folder_permissions table for granular access control
CREATE TABLE IF NOT EXISTS public.folder_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    folder_id UUID NOT NULL REFERENCES public.custom_folders(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    permission_type TEXT NOT NULL CHECK (permission_type IN ('read', 'write', 'admin')),
    granted_by UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate permissions
    UNIQUE(folder_id, user_id, permission_type)
);

-- Update document_archive to use custom_folders instead of departments
ALTER TABLE public.document_archive 
ADD COLUMN IF NOT EXISTS folder_id UUID REFERENCES public.custom_folders(id) ON DELETE SET NULL;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_custom_folders_created_by ON public.custom_folders(created_by);
CREATE INDEX IF NOT EXISTS idx_custom_folders_parent ON public.custom_folders(parent_folder_id);
CREATE INDEX IF NOT EXISTS idx_custom_folders_department ON public.custom_folders(department_id);
CREATE INDEX IF NOT EXISTS idx_custom_folders_access_level ON public.custom_folders(access_level);
CREATE INDEX IF NOT EXISTS idx_folder_permissions_folder ON public.folder_permissions(folder_id);
CREATE INDEX IF NOT EXISTS idx_folder_permissions_user ON public.folder_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_document_archive_folder ON public.document_archive(folder_id);

-- Enable RLS
ALTER TABLE public.custom_folders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.folder_permissions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for custom_folders

-- Users can view folders they created, have permissions for, or are shared with
CREATE POLICY "Users can view accessible folders" ON public.custom_folders
    FOR SELECT USING (
        -- Own folders
        auth.uid() = created_by OR
        
        -- Shared folders (user ID in shared_with array)
        auth.uid() = ANY(shared_with) OR
        
        -- Department folders (if user is in same department)
        (access_level = 'department' AND department_id IN (
            SELECT department_id FROM public.profiles WHERE id = auth.uid()
        )) OR
        
        -- Public folders
        access_level = 'public' OR
        
        -- Folders with explicit permissions
        EXISTS (
            SELECT 1 FROM public.folder_permissions fp 
            WHERE fp.folder_id = id AND fp.user_id = auth.uid()
        ) OR
        
        -- Admin/Manager access
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

-- Users can create folders
CREATE POLICY "Users can create folders" ON public.custom_folders
    FOR INSERT WITH CHECK (
        auth.uid() = created_by
    );

-- Users can update their own folders or folders they have admin permission for
CREATE POLICY "Users can update own folders" ON public.custom_folders
    FOR UPDATE USING (
        auth.uid() = created_by OR
        EXISTS (
            SELECT 1 FROM public.folder_permissions fp 
            WHERE fp.folder_id = id AND fp.user_id = auth.uid() AND fp.permission_type = 'admin'
        ) OR
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin')
        )
    );

-- Users can delete their own folders (except system folders)
CREATE POLICY "Users can delete own folders" ON public.custom_folders
    FOR DELETE USING (
        auth.uid() = created_by AND is_system = FALSE OR
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin')
        )
    );

-- RLS Policies for folder_permissions

-- Users can view permissions for folders they have access to
CREATE POLICY "Users can view folder permissions" ON public.folder_permissions
    FOR SELECT USING (
        -- Permission for folders they own
        EXISTS (
            SELECT 1 FROM public.custom_folders cf 
            WHERE cf.id = folder_id AND cf.created_by = auth.uid()
        ) OR
        
        -- Their own permissions
        user_id = auth.uid() OR
        
        -- Admin access
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin')
        )
    );

-- Users can grant permissions for folders they own or have admin permission for
CREATE POLICY "Users can manage folder permissions" ON public.folder_permissions
    FOR INSERT WITH CHECK (
        auth.uid() = granted_by AND (
            EXISTS (
                SELECT 1 FROM public.custom_folders cf 
                WHERE cf.id = folder_id AND cf.created_by = auth.uid()
            ) OR
            EXISTS (
                SELECT 1 FROM public.folder_permissions fp 
                WHERE fp.folder_id = folder_id AND fp.user_id = auth.uid() AND fp.permission_type = 'admin'
            )
        )
    );

-- Update document_archive RLS to work with new folder system
DROP POLICY IF EXISTS "document_archive_select" ON public.document_archive;

CREATE POLICY "Users can view accessible documents" ON public.document_archive
    FOR SELECT USING (
        -- Own documents
        auth.uid() = uploaded_by OR
        
        -- Documents in accessible folders
        (folder_id IS NOT NULL AND EXISTS (
            SELECT 1 FROM public.custom_folders cf 
            WHERE cf.id = folder_id AND (
                cf.created_by = auth.uid() OR
                auth.uid() = ANY(cf.shared_with) OR
                (cf.access_level = 'department' AND cf.department_id IN (
                    SELECT department_id FROM public.profiles WHERE id = auth.uid()
                )) OR
                cf.access_level = 'public' OR
                EXISTS (
                    SELECT 1 FROM public.folder_permissions fp 
                    WHERE fp.folder_id = cf.id AND fp.user_id = auth.uid()
                )
            )
        )) OR
        
        -- Legacy department-based access (for existing documents)
        (folder_id IS NULL AND department_id IN (
            SELECT department_id FROM public.profiles WHERE id = auth.uid()
        )) OR
        
        -- Admin/Manager access
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    );

-- Function to create default folders for new users
CREATE OR REPLACE FUNCTION create_default_user_folders()
RETURNS TRIGGER AS $$
BEGIN
    -- Create a personal folder for the new user
    INSERT INTO public.custom_folders (
        name, 
        description, 
        created_by, 
        access_level, 
        folder_type,
        color,
        icon
    ) VALUES (
        'My Documents',
        'Personal document folder for ' || NEW.full_name,
        NEW.id,
        'private',
        'custom',
        '#10B981',
        'user'
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create default folders for new users
DROP TRIGGER IF EXISTS create_user_default_folders ON public.profiles;
CREATE TRIGGER create_user_default_folders
    AFTER INSERT ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION create_default_user_folders();

-- Create some system folders (these will be created by admin)
INSERT INTO public.custom_folders (
    name, description, created_by, access_level, folder_type, is_system, color, icon
) 
SELECT 
    'Company Policies', 
    'Official company policies and procedures', 
    id, 
    'public', 
    'system', 
    TRUE,
    '#EF4444',
    'shield'
FROM public.profiles 
WHERE role = 'admin' 
LIMIT 1
ON CONFLICT DO NOTHING;

INSERT INTO public.custom_folders (
    name, description, created_by, access_level, folder_type, is_system, color, icon
) 
SELECT 
    'Templates', 
    'Document templates and forms', 
    id, 
    'public', 
    'system', 
    TRUE,
    '#8B5CF6',
    'template'
FROM public.profiles 
WHERE role = 'admin' 
LIMIT 1
ON CONFLICT DO NOTHING;
