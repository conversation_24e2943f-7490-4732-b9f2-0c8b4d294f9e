{"version": 2, "name": "ct-nigeria-ai-platform", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "routes": [{"src": "/sw.js", "headers": {"Cache-Control": "public, max-age=0, must-revalidate", "Service-Worker-Allowed": "/"}}, {"src": "/manifest.json", "headers": {"Content-Type": "application/manifest+json", "Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/assets/(.*)", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html", "headers": {"Cache-Control": "public, max-age=0, must-revalidate"}}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "env": {"NODE_ENV": "production"}, "buildCommand": "npm run build", "installCommand": "npm ci", "framework": null, "cleanUrls": true, "trailingSlash": false}