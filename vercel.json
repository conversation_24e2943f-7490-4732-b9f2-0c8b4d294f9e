{"version": 2, "name": "ct-nigeria-ai-platform", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "dist"}}], "rewrites": [{"source": "/((?!api|_next|_static|favicon.ico|sw.js|manifest.json|assets).*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "env": {"NODE_ENV": "production"}, "buildCommand": "npm run build", "installCommand": "npm ci", "framework": null}