import { supabase } from '@/integrations/supabase/client';

export interface ActivityLogData {
  action: string;
  description: string;
  metadata?: Record<string, any>;
  severity?: 'info' | 'warning' | 'error' | 'critical' | 'success';
  category?: 'general' | 'auth' | 'project' | 'user' | 'system' | 'database' | 'api' | 'security';
  user_id?: string;
}

export interface SystemActivity {
  id: string;
  user_id: string | null;
  action: string;
  description: string;
  entity_type?: string;
  entity_id?: string;
  metadata: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  severity: string;
  category: string;
  created_at: string;
  updated_at: string;
}

export class ActivityLogger {
  private static instance: ActivityLogger;
  private currentUser: any = null;

  private constructor() {}

  public static getInstance(): ActivityLogger {
    if (!ActivityLogger.instance) {
      ActivityLogger.instance = new ActivityLogger();
    }
    return ActivityLogger.instance;
  }

  public setCurrentUser(user: any) {
    this.currentUser = user;
  }

  /**
   * Log a system activity
   */
  public async logActivity(data: ActivityLogData): Promise<string | null> {
    try {
      // Get current user if not provided
      const userId = data.user_id || this.currentUser?.id || null;

      // Prepare activity data
      const activityData = {
        user_id: userId,
        action: data.action,
        description: data.description,
        metadata: data.metadata || {},
        severity: data.severity || 'info',
        category: data.category || 'general',
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent,
        session_id: this.getSessionId()
      };

      // Insert using Supabase client
      const { data: result, error } = await supabase
        .from('system_activities')
        .insert(activityData)
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log activity:', error);
        return null;
      }

      return result.id;
    } catch (error) {
      console.error('Activity logging error:', error);
      return null;
    }
  }

  /**
   * Log using the database function (alternative method)
   */
  public async logActivityWithFunction(data: ActivityLogData): Promise<string | null> {
    try {
      const userId = data.user_id || this.currentUser?.id || null;

      const { data: result, error } = await supabase.rpc('log_system_activity', {
        p_user_id: userId,
        p_action: data.action,
        p_description: data.description,
        p_metadata: data.metadata || {},
        p_severity: data.severity || 'info',
        p_category: data.category || 'general'
      });

      if (error) {
        console.error('Failed to log activity with function:', error);
        return null;
      }

      return result;
    } catch (error) {
      console.error('Activity logging function error:', error);
      return null;
    }
  }

  /**
   * Get activities for current user
   */
  public async getUserActivities(limit: number = 50): Promise<SystemActivity[]> {
    try {
      const { data, error } = await supabase
        .from('system_activities')
        .select(`
          *,
          profiles:user_id(full_name, email)
        `)
        .eq('user_id', this.currentUser?.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to fetch user activities:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching user activities:', error);
      return [];
    }
  }

  /**
   * Get all activities (admin only)
   */
  public async getAllActivities(limit: number = 100): Promise<SystemActivity[]> {
    try {
      const { data, error } = await supabase
        .from('system_activities')
        .select(`
          *,
          profiles:user_id(full_name, email)
        `)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Failed to fetch all activities:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching all activities:', error);
      return [];
    }
  }

  /**
   * Get activity statistics
   */
  public async getActivityStatistics(days: number = 30): Promise<any[]> {
    try {
      const { data, error } = await supabase.rpc('get_activity_statistics', {
        p_days: days
      });

      if (error) {
        console.error('Failed to fetch activity statistics:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching activity statistics:', error);
      return [];
    }
  }

  /**
   * Predefined activity types for common actions
   */
  public async logUserLogin(userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'user_login',
      description: 'User logged in successfully',
      category: 'auth',
      severity: 'success',
      user_id: userId
    });
  }

  public async logUserLogout(userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'user_logout',
      description: 'User logged out',
      category: 'auth',
      severity: 'info',
      user_id: userId
    });
  }

  public async logProfileUpdate(userId?: string, changes?: Record<string, any>): Promise<string | null> {
    return this.logActivity({
      action: 'profile_update',
      description: 'User profile updated',
      category: 'user',
      severity: 'info',
      metadata: { changes },
      user_id: userId
    });
  }

  public async logProjectAction(actionType: string, projectId: string, userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'project_action',
      description: `Project ${actionType}`,
      category: 'project',
      severity: 'info',
      metadata: { action: actionType, project_id: projectId },
      user_id: userId
    });
  }

  public async logSystemError(error: string, context?: Record<string, any>, userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'system_error',
      description: `System error: ${error}`,
      category: 'system',
      severity: 'error',
      metadata: { error, context },
      user_id: userId
    });
  }

  public async logSecurityEvent(event: string, details?: Record<string, any>, userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'security_event',
      description: `Security event: ${event}`,
      category: 'security',
      severity: 'warning',
      metadata: { event, details },
      user_id: userId
    });
  }

  public async logDatabaseAction(actionType: string, table: string, userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'database_action',
      description: `Database ${actionType} on ${table}`,
      category: 'database',
      severity: 'info',
      metadata: { action: actionType, table },
      user_id: userId
    });
  }

  public async logAPICall(endpoint: string, method: string, status: number, userId?: string): Promise<string | null> {
    return this.logActivity({
      action: 'api_call',
      description: `API ${method} ${endpoint} - ${status}`,
      category: 'api',
      severity: status >= 400 ? 'error' : 'info',
      metadata: { endpoint, method, status },
      user_id: userId
    });
  }

  // Helper methods
  private async getClientIP(): Promise<string | null> {
    try {
      // In a real application, you might want to get this from a service
      // For now, we'll return null and let the database handle it
      return null;
    } catch {
      return null;
    }
  }

  private getSessionId(): string | null {
    try {
      // Get session ID from localStorage or generate one
      let sessionId = localStorage.getItem('session_id');
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        localStorage.setItem('session_id', sessionId);
      }
      return sessionId;
    } catch {
      return null;
    }
  }
}

// Export singleton instance
export const activityLogger = ActivityLogger.getInstance();

// Export convenience functions
export const logActivity = (data: ActivityLogData) => activityLogger.logActivity(data);
export const logUserLogin = (userId?: string) => activityLogger.logUserLogin(userId);
export const logUserLogout = (userId?: string) => activityLogger.logUserLogout(userId);
export const logProfileUpdate = (userId?: string, changes?: Record<string, any>) => 
  activityLogger.logProfileUpdate(userId, changes);
export const logProjectAction = (action: string, projectId: string, userId?: string) => 
  activityLogger.logProjectAction(action, projectId, userId);
export const logSystemError = (error: string, context?: Record<string, any>, userId?: string) => 
  activityLogger.logSystemError(error, context, userId);
export const logSecurityEvent = (event: string, details?: Record<string, any>, userId?: string) => 
  activityLogger.logSecurityEvent(event, details, userId);
export const logDatabaseAction = (action: string, table: string, userId?: string) => 
  activityLogger.logDatabaseAction(action, table, userId);
export const logAPICall = (endpoint: string, method: string, status: number, userId?: string) => 
  activityLogger.logAPICall(endpoint, method, status, userId);

export default ActivityLogger;
