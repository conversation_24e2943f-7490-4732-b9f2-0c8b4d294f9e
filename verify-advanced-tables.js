// Verify advanced modules tables exist and are accessible
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function verifyAdvancedTables() {
  console.log('🚀 Verifying advanced modules tables...');
  
  const tables = [
    'ai_interactions',
    'ai_knowledge_base', 
    'system_logs',
    'user_activities',
    'time_logs',
    'tasks',
    'task_comments',
    'task_assignments'
  ];

  let allTablesExist = true;

  for (const tableName of tables) {
    try {
      logStep(`Checking table: ${tableName}`);
      
      // Try to query the table
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (error) {
        logError(`Table ${tableName} error: ${error.message}`);
        allTablesExist = false;
      } else {
        logSuccess(`Table ${tableName} exists and is accessible`);
      }
    } catch (err) {
      logError(`Table ${tableName} verification failed: ${err.message}`);
      allTablesExist = false;
    }
  }

  if (!allTablesExist) {
    console.log('\n🔧 Some tables are missing. Attempting to recreate...');
    
    try {
      // Recreate the tables
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql_text: `
          -- Drop existing tables if they exist (to avoid conflicts)
          DROP TABLE IF EXISTS public.ai_interactions CASCADE;
          DROP TABLE IF EXISTS public.ai_knowledge_base CASCADE;
          DROP TABLE IF EXISTS public.system_logs CASCADE;
          DROP TABLE IF EXISTS public.user_activities CASCADE;
          DROP TABLE IF EXISTS public.time_logs CASCADE;
          DROP TABLE IF EXISTS public.tasks CASCADE;
          DROP TABLE IF EXISTS public.task_comments CASCADE;
          DROP TABLE IF EXISTS public.task_assignments CASCADE;

          -- Create AI Interactions Table
          CREATE TABLE public.ai_interactions (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID,
            session_id UUID DEFAULT uuid_generate_v4(),
            role TEXT DEFAULT 'user' CHECK (role IN ('user', 'assistant', 'system')),
            message TEXT NOT NULL,
            response TEXT,
            type TEXT DEFAULT 'chat' CHECK (type IN ('chat', 'command', 'query', 'help', 'analysis')),
            context JSONB DEFAULT '{}',
            actions JSONB DEFAULT '[]',
            suggestions TEXT[],
            confidence_score DECIMAL(3,2) DEFAULT 0.0,
            processing_time_ms INTEGER DEFAULT 0,
            tokens_used INTEGER DEFAULT 0,
            model_used TEXT DEFAULT 'enhanced-ai',
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create AI Knowledge Base Table
          CREATE TABLE public.ai_knowledge_base (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            category TEXT NOT NULL,
            subcategory TEXT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            keywords TEXT[],
            tags TEXT[],
            context JSONB DEFAULT '{}',
            confidence_score DECIMAL(3,2) DEFAULT 1.0,
            usage_count INTEGER DEFAULT 0,
            last_used TIMESTAMP WITH TIME ZONE,
            created_by UUID,
            updated_by UUID,
            is_active BOOLEAN DEFAULT TRUE,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create System Logs Table
          CREATE TABLE public.system_logs (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID,
            level TEXT NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error', 'critical')),
            category TEXT NOT NULL DEFAULT 'general',
            subcategory TEXT,
            message TEXT NOT NULL,
            details TEXT,
            source TEXT,
            function_name TEXT,
            file_path TEXT,
            line_number INTEGER,
            stack_trace TEXT,
            request_id UUID,
            session_id UUID,
            ip_address INET,
            user_agent TEXT,
            endpoint TEXT,
            method TEXT,
            status_code INTEGER,
            response_time_ms INTEGER,
            error_code TEXT,
            error_type TEXT,
            context JSONB DEFAULT '{}',
            metadata JSONB DEFAULT '{}',
            resolved BOOLEAN DEFAULT FALSE,
            resolved_by UUID,
            resolved_at TIMESTAMP WITH TIME ZONE,
            resolution_notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create User Activities Table
          CREATE TABLE public.user_activities (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID,
            activity_type TEXT NOT NULL,
            action TEXT NOT NULL,
            description TEXT,
            entity_type TEXT,
            entity_id UUID,
            entity_name TEXT,
            old_values JSONB DEFAULT '{}',
            new_values JSONB DEFAULT '{}',
            changes JSONB DEFAULT '{}',
            impact_level TEXT DEFAULT 'low' CHECK (impact_level IN ('low', 'medium', 'high', 'critical')),
            category TEXT DEFAULT 'general',
            subcategory TEXT,
            tags TEXT[],
            ip_address INET,
            user_agent TEXT,
            location TEXT,
            device_info JSONB DEFAULT '{}',
            session_id UUID,
            request_id UUID,
            duration_ms INTEGER,
            success BOOLEAN DEFAULT TRUE,
            error_message TEXT,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create Time Logs Table
          CREATE TABLE public.time_logs (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            user_id UUID NOT NULL,
            project_id UUID,
            task_id UUID,
            activity_type TEXT NOT NULL DEFAULT 'work' CHECK (activity_type IN ('work', 'break', 'meeting', 'training', 'admin', 'other')),
            description TEXT,
            start_time TIMESTAMP WITH TIME ZONE NOT NULL,
            end_time TIMESTAMP WITH TIME ZONE,
            duration_minutes INTEGER,
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
            location TEXT,
            device_info JSONB DEFAULT '{}',
            is_billable BOOLEAN DEFAULT TRUE,
            hourly_rate DECIMAL(10,2),
            total_amount DECIMAL(10,2),
            approved BOOLEAN DEFAULT FALSE,
            approved_by UUID,
            approved_at TIMESTAMP WITH TIME ZONE,
            notes TEXT,
            tags TEXT[],
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create Tasks Table
          CREATE TABLE public.tasks (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            title TEXT NOT NULL,
            description TEXT,
            project_id UUID,
            assigned_to UUID,
            created_by UUID,
            parent_task_id UUID,
            status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'testing', 'done', 'cancelled', 'blocked')),
            priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'critical')),
            type TEXT DEFAULT 'task' CHECK (type IN ('task', 'bug', 'feature', 'improvement', 'research', 'documentation')),
            category TEXT,
            tags TEXT[],
            estimated_hours DECIMAL(5,2),
            actual_hours DECIMAL(5,2) DEFAULT 0,
            progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
            start_date DATE,
            due_date DATE,
            completed_date DATE,
            dependencies UUID[],
            blockers TEXT[],
            acceptance_criteria TEXT[],
            attachments JSONB DEFAULT '[]',
            comments_count INTEGER DEFAULT 0,
            watchers UUID[],
            labels TEXT[],
            custom_fields JSONB DEFAULT '{}',
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create Task Comments Table
          CREATE TABLE public.task_comments (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            task_id UUID NOT NULL,
            user_id UUID NOT NULL,
            comment TEXT NOT NULL,
            comment_type TEXT DEFAULT 'comment' CHECK (comment_type IN ('comment', 'status_change', 'assignment', 'time_log', 'attachment')),
            mentions UUID[],
            attachments JSONB DEFAULT '[]',
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create Task Assignments Table
          CREATE TABLE public.task_assignments (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            task_id UUID NOT NULL,
            assigned_to UUID NOT NULL,
            assigned_by UUID,
            role TEXT DEFAULT 'assignee' CHECK (role IN ('assignee', 'reviewer', 'watcher', 'collaborator')),
            status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'removed')),
            assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            completed_at TIMESTAMP WITH TIME ZONE,
            notes TEXT,
            metadata JSONB DEFAULT '{}'
          );

          -- Create indexes for performance
          CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON public.ai_interactions(user_id);
          CREATE INDEX IF NOT EXISTS idx_ai_interactions_session_id ON public.ai_interactions(session_id);
          CREATE INDEX IF NOT EXISTS idx_system_logs_level ON public.system_logs(level);
          CREATE INDEX IF NOT EXISTS idx_system_logs_category ON public.system_logs(category);
          CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON public.user_activities(user_id);
          CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
          CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON public.tasks(assigned_to);
          CREATE INDEX IF NOT EXISTS idx_task_comments_task_id ON public.task_comments(task_id);
          CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON public.task_assignments(task_id);

          -- Enable RLS
          ALTER TABLE public.ai_interactions ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.ai_knowledge_base ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.system_logs ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.user_activities ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
          ALTER TABLE public.task_assignments ENABLE ROW LEVEL SECURITY;

          -- Create RLS policies
          CREATE POLICY "ai_interactions_select_all" ON public.ai_interactions FOR SELECT USING (true);
          CREATE POLICY "ai_interactions_insert_all" ON public.ai_interactions FOR INSERT WITH CHECK (true);
          CREATE POLICY "system_logs_select_all" ON public.system_logs FOR SELECT USING (true);
          CREATE POLICY "system_logs_insert_all" ON public.system_logs FOR INSERT WITH CHECK (true);
          CREATE POLICY "user_activities_select_all" ON public.user_activities FOR SELECT USING (true);
          CREATE POLICY "user_activities_insert_all" ON public.user_activities FOR INSERT WITH CHECK (true);
          CREATE POLICY "time_logs_select_all" ON public.time_logs FOR SELECT USING (true);
          CREATE POLICY "time_logs_insert_all" ON public.time_logs FOR INSERT WITH CHECK (true);
          CREATE POLICY "tasks_select_all" ON public.tasks FOR SELECT USING (true);
          CREATE POLICY "tasks_insert_all" ON public.tasks FOR INSERT WITH CHECK (true);
          CREATE POLICY "task_comments_select_all" ON public.task_comments FOR SELECT USING (true);
          CREATE POLICY "task_comments_insert_all" ON public.task_comments FOR INSERT WITH CHECK (true);
          CREATE POLICY "task_assignments_select_all" ON public.task_assignments FOR SELECT USING (true);
          CREATE POLICY "task_assignments_insert_all" ON public.task_assignments FOR INSERT WITH CHECK (true);
          CREATE POLICY "ai_knowledge_base_select_all" ON public.ai_knowledge_base FOR SELECT USING (true);
          CREATE POLICY "ai_knowledge_base_insert_all" ON public.ai_knowledge_base FOR INSERT WITH CHECK (true);

          -- Grant permissions
          GRANT ALL ON public.ai_interactions TO authenticated;
          GRANT ALL ON public.ai_interactions TO anon;
          GRANT ALL ON public.ai_knowledge_base TO authenticated;
          GRANT ALL ON public.ai_knowledge_base TO anon;
          GRANT ALL ON public.system_logs TO authenticated;
          GRANT ALL ON public.system_logs TO anon;
          GRANT ALL ON public.user_activities TO authenticated;
          GRANT ALL ON public.user_activities TO anon;
          GRANT ALL ON public.time_logs TO authenticated;
          GRANT ALL ON public.time_logs TO anon;
          GRANT ALL ON public.tasks TO authenticated;
          GRANT ALL ON public.tasks TO anon;
          GRANT ALL ON public.task_comments TO authenticated;
          GRANT ALL ON public.task_comments TO anon;
          GRANT ALL ON public.task_assignments TO authenticated;
          GRANT ALL ON public.task_assignments TO anon;
        `
      });

      if (createError) {
        throw new Error('Table recreation failed: ' + createError.message);
      }

      logSuccess('Tables recreated successfully');

      // Wait a moment for schema cache to update
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Verify again
      console.log('\n🔍 Re-verifying tables...');
      for (const tableName of tables) {
        try {
          const { data, error } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);
          
          if (error) {
            logError(`Table ${tableName} still has issues: ${error.message}`);
          } else {
            logSuccess(`Table ${tableName} is now working`);
          }
        } catch (err) {
          logError(`Table ${tableName} verification failed: ${err.message}`);
        }
      }

    } catch (recreateError) {
      logError(`Table recreation failed: ${recreateError.message}`);
    }
  }

  // Test creating sample data
  console.log('\n🧪 Testing sample data creation...');
  
  try {
    // Test AI interaction
    const { data: aiTest, error: aiError } = await supabase
      .from('ai_interactions')
      .insert({
        role: 'user',
        message: 'Test message from verification script',
        type: 'chat',
        context: { test: true },
        metadata: { verification: true }
      })
      .select()
      .single();

    if (aiError) {
      logError(`AI interaction test failed: ${aiError.message}`);
    } else {
      logSuccess(`AI interaction created successfully (ID: ${aiTest.id})`);
    }

    // Test system log
    const { data: logTest, error: logError } = await supabase
      .from('system_logs')
      .insert({
        level: 'info',
        category: 'general',
        message: 'Test log from verification script',
        context: { test: true },
        metadata: { verification: true }
      })
      .select()
      .single();

    if (logError) {
      logError(`System log test failed: ${logError.message}`);
    } else {
      logSuccess(`System log created successfully (ID: ${logTest.id})`);
    }

    // Test user activity
    const { data: activityTest, error: activityError } = await supabase
      .from('user_activities')
      .insert({
        activity_type: 'create',
        action: 'Test Activity',
        description: 'Test activity from verification script',
        entity_type: 'system',
        impact_level: 'low',
        category: 'test',
        success: true,
        metadata: { verification: true }
      })
      .select()
      .single();

    if (activityError) {
      logError(`User activity test failed: ${activityError.message}`);
    } else {
      logSuccess(`User activity created successfully (ID: ${activityTest.id})`);
    }

    // Test time log
    const { data: timeTest, error: timeError } = await supabase
      .from('time_logs')
      .insert({
        user_id: '00000000-0000-0000-0000-000000000001',
        activity_type: 'work',
        description: 'Test time log from verification script',
        start_time: new Date().toISOString(),
        status: 'active',
        metadata: { verification: true }
      })
      .select()
      .single();

    if (timeError) {
      logError(`Time log test failed: ${timeError.message}`);
    } else {
      logSuccess(`Time log created successfully (ID: ${timeTest.id})`);
    }

    // Test task
    const { data: taskTest, error: taskError } = await supabase
      .from('tasks')
      .insert({
        title: 'Test Task from Verification Script',
        description: 'This is a test task',
        status: 'todo',
        priority: 'medium',
        type: 'task',
        progress_percentage: 0,
        comments_count: 0,
        metadata: { verification: true }
      })
      .select()
      .single();

    if (taskError) {
      logError(`Task test failed: ${taskError.message}`);
    } else {
      logSuccess(`Task created successfully (ID: ${taskTest.id})`);
    }

  } catch (testError) {
    logError(`Sample data test failed: ${testError.message}`);
  }

  console.log('\n🎉 Advanced modules tables verification completed!');
  return true;
}

// Run the verification
verifyAdvancedTables()
  .then((success) => {
    if (success) {
      console.log('\n✅ SUCCESS: Advanced modules tables verified and working!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Advanced modules tables verification encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
