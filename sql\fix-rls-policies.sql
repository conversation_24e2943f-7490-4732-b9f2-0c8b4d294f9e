-- ============================================================================
-- FIX RLS POLICIES - RESOLVE INFINITE RECURSION
-- This script fixes the infinite recursion issue in RLS policies
-- ============================================================================

-- First, drop all existing problematic policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;

-- Temporarily disable RLS to clear any stuck states
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- CREATE SAFE RLS POLICIES WITHOUT RECURSION
-- ============================================================================

-- Policy 1: Allow users to view their own profile
-- This uses auth.uid() directly without referencing the profiles table
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

-- Policy 2: Allow users to insert their own profile
-- This allows profile creation during signup
CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

-- Policy 3: Allow users to update their own profile
-- This uses auth.uid() directly without referencing the profiles table
CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Policy 4: Allow service role to do everything (for admin operations)
-- This is safe because it doesn't reference the profiles table
CREATE POLICY "profiles_service_role_all" ON public.profiles
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- ============================================================================
-- FIX OTHER TABLES WITH POTENTIAL RLS ISSUES
-- ============================================================================

-- Fix departments table policies
DROP POLICY IF EXISTS "Users can view departments" ON public.departments;
DROP POLICY IF EXISTS "Admins can manage departments" ON public.departments;

-- Simple department policies without recursion
CREATE POLICY "departments_select_all" ON public.departments
    FOR SELECT
    USING (true);  -- Everyone can view departments

CREATE POLICY "departments_admin_all" ON public.departments
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- Fix memos table policies
DROP POLICY IF EXISTS "Users can view published memos" ON public.memos;
DROP POLICY IF EXISTS "Users can view memos" ON public.memos;
DROP POLICY IF EXISTS "Users can create memos" ON public.memos;

-- Simple memo policies without recursion
CREATE POLICY "memos_select_own_or_published" ON public.memos
    FOR SELECT
    USING (
        created_by = auth.uid() OR 
        status = 'published' OR
        current_setting('role') = 'service_role'
    );

CREATE POLICY "memos_insert_authenticated" ON public.memos
    FOR INSERT
    WITH CHECK (auth.uid() = created_by);

CREATE POLICY "memos_update_own" ON public.memos
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- ============================================================================
-- FIX BATTERY MANAGEMENT POLICIES
-- ============================================================================

-- Fix battery tables if they exist
DO $$
BEGIN
    -- Check if batteries table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'batteries') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view batteries" ON public.batteries;
        DROP POLICY IF EXISTS "Users can create batteries" ON public.batteries;
        DROP POLICY IF EXISTS "Users can update own batteries" ON public.batteries;
        
        -- Create safe policies
        CREATE POLICY "batteries_select_authenticated" ON public.batteries
            FOR SELECT
            USING (
                created_by = auth.uid() OR 
                profile_id = auth.uid() OR
                current_setting('role') = 'service_role'
            );
            
        CREATE POLICY "batteries_insert_authenticated" ON public.batteries
            FOR INSERT
            WITH CHECK (auth.uid() = created_by);
            
        CREATE POLICY "batteries_update_own" ON public.batteries
            FOR UPDATE
            USING (created_by = auth.uid())
            WITH CHECK (created_by = auth.uid());
    END IF;
    
    -- Fix battery_types table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'battery_types') THEN
        CREATE POLICY "battery_types_select_all" ON public.battery_types
            FOR SELECT
            USING (true);
            
        CREATE POLICY "battery_types_admin_all" ON public.battery_types
            FOR ALL
            USING (current_setting('role') = 'service_role')
            WITH CHECK (current_setting('role') = 'service_role');
    END IF;
    
    -- Fix battery_locations table
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'battery_locations') THEN
        CREATE POLICY "battery_locations_select_all" ON public.battery_locations
            FOR SELECT
            USING (true);
            
        CREATE POLICY "battery_locations_admin_all" ON public.battery_locations
            FOR ALL
            USING (current_setting('role') = 'service_role')
            WITH CHECK (current_setting('role') = 'service_role');
    END IF;
END $$;

-- ============================================================================
-- CREATE HELPER FUNCTION FOR ADMIN CHECK (SAFE)
-- ============================================================================

-- Create a function that safely checks if user is admin without recursion
CREATE OR REPLACE FUNCTION is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    -- Use auth.jwt() to get user info without querying profiles table
    RETURN (auth.jwt() ->> 'role') = 'admin' OR 
           (auth.jwt() -> 'user_metadata' ->> 'role') = 'admin' OR
           current_setting('role') = 'service_role';
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- VERIFY POLICIES ARE WORKING
-- ============================================================================

-- Create a test function to verify policies
CREATE OR REPLACE FUNCTION test_rls_policies()
RETURNS TABLE(
    table_name TEXT,
    policy_count BIGINT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        (SELECT COUNT(*) FROM pg_policies p WHERE p.tablename = t.tablename),
        CASE 
            WHEN t.rowsecurity THEN '✅ RLS Enabled'
            ELSE '❌ RLS Disabled'
        END
    FROM pg_tables t
    WHERE t.schemaname = 'public'
    AND t.tablename IN ('profiles', 'departments', 'memos', 'batteries', 'battery_types', 'battery_locations')
    ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- SUCCESS MESSAGE
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '🎉 RLS POLICIES FIXED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Removed recursive policies';
    RAISE NOTICE '✅ Created safe policies using auth.uid()';
    RAISE NOTICE '✅ Added service role policies for admin operations';
    RAISE NOTICE '✅ Fixed profiles, departments, memos, and battery tables';
    RAISE NOTICE '';
    RAISE NOTICE 'Test with: SELECT * FROM test_rls_policies();';
    RAISE NOTICE '';
    RAISE NOTICE '🔐 Authentication should now work without infinite recursion!';
END $$;
