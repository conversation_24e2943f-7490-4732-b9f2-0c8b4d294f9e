/**
 * AI Visual Navigation System
 * Enables AI to visually navigate and interact with the UI as users watch
 */

import { supabase } from '@/integrations/supabase/client';
import { browserCompatibility, checkFeatureSupport } from '@/utils/browserCompatibility';

export interface VisualElement {
  id: string;
  type: 'button' | 'input' | 'link' | 'form' | 'card' | 'menu' | 'modal';
  selector: string;
  text: string;
  position: { x: number; y: number; width: number; height: number };
  isVisible: boolean;
  isClickable: boolean;
  attributes: Record<string, string>;
}

export interface NavigationStep {
  id: string;
  action: 'click' | 'type' | 'scroll' | 'wait' | 'highlight' | 'speak';
  target?: string;
  value?: string;
  duration?: number;
  description: string;
}

export interface AINavigationTask {
  id: string;
  name: string;
  description: string;
  steps: NavigationStep[];
  context: string;
  expectedOutcome: string;
}

class AIVisualNavigationSystem {
  private isActive = false;
  private currentTask: AINavigationTask | null = null;
  private highlightOverlay: HTMLElement | null = null;
  private speechSynthesis: SpeechSynthesis | null = null;
  private observers: MutationObserver[] = [];

  constructor() {
    this.speechSynthesis = window.speechSynthesis;
    this.setupHighlightOverlay();
  }

  /**
   * Initialize the visual navigation system
   */
  public async initialize(): Promise<void> {
    console.log('🤖 Initializing AI Visual Navigation System...');

    // Check browser compatibility
    const compatibility = browserCompatibility.generateCompatibilityReport();
    console.log('🔍 Browser compatibility:', compatibility);

    if (compatibility.overallCompatibility === 'poor') {
      console.warn('⚠️ Limited AI features available in this browser');
      throw new Error('AI Visual Navigation requires a modern browser with advanced features');
    }

    // Check specific feature support
    if (!checkFeatureSupport('aiNavigation')) {
      console.warn('⚠️ AI Navigation features may be limited');
    }

    // Setup DOM observers
    this.setupDOMObservers();

    // Initialize speech synthesis if supported
    if (this.speechSynthesis && browserCompatibility.getCapabilities().speechSynthesis) {
      this.speechSynthesis.cancel();
    }

    console.log('✅ AI Visual Navigation System ready');
  }

  /**
   * Scan the current page for interactive elements
   */
  public scanPageElements(): VisualElement[] {
    const elements: VisualElement[] = [];
    
    // Define selectors for different element types
    const selectors = {
      button: 'button, [role="button"], input[type="button"], input[type="submit"]',
      input: 'input:not([type="button"]):not([type="submit"]), textarea, select',
      link: 'a[href], [role="link"]',
      form: 'form',
      card: '[class*="card"], [data-testid*="card"]',
      menu: '[role="menu"], [class*="menu"], nav',
      modal: '[role="dialog"], [class*="modal"], [class*="popup"]'
    };

    Object.entries(selectors).forEach(([type, selector]) => {
      const foundElements = document.querySelectorAll(selector);
      
      foundElements.forEach((element, index) => {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.width > 0 && rect.height > 0 && 
                         window.getComputedStyle(element).visibility !== 'hidden';
        
        if (isVisible) {
          elements.push({
            id: `${type}_${index}`,
            type: type as VisualElement['type'],
            selector: this.generateSelector(element),
            text: this.getElementText(element),
            position: {
              x: rect.left,
              y: rect.top,
              width: rect.width,
              height: rect.height
            },
            isVisible,
            isClickable: this.isElementClickable(element),
            attributes: this.getElementAttributes(element)
          });
        }
      });
    });

    return elements;
  }

  /**
   * Execute an AI navigation task with visual feedback
   */
  public async executeNavigationTask(task: AINavigationTask): Promise<void> {
    if (this.isActive) {
      throw new Error('Navigation task already in progress');
    }

    this.isActive = true;
    this.currentTask = task;

    try {
      await this.speak(`Starting task: ${task.name}. ${task.description}`);
      
      for (let i = 0; i < task.steps.length; i++) {
        const step = task.steps[i];
        await this.executeStep(step, i + 1, task.steps.length);
        
        // Small delay between steps for visual clarity
        await this.wait(500);
      }
      
      await this.speak(`Task completed successfully: ${task.expectedOutcome}`);
      
    } catch (error) {
      await this.speak(`Task failed: ${error.message}`);
      throw error;
    } finally {
      this.isActive = false;
      this.currentTask = null;
      this.clearHighlight();
    }
  }

  /**
   * Execute a single navigation step
   */
  private async executeStep(step: NavigationStep, stepNumber: number, totalSteps: number): Promise<void> {
    await this.speak(`Step ${stepNumber} of ${totalSteps}: ${step.description}`);

    switch (step.action) {
      case 'click':
        await this.performClick(step.target!, step.description);
        break;
      
      case 'type':
        await this.performType(step.target!, step.value!, step.description);
        break;
      
      case 'scroll':
        await this.performScroll(step.target, step.value, step.description);
        break;
      
      case 'wait':
        await this.wait(step.duration || 1000);
        break;
      
      case 'highlight':
        await this.highlightElement(step.target!, step.duration || 2000);
        break;
      
      case 'speak':
        await this.speak(step.value || step.description);
        break;
    }
  }

  /**
   * Perform a click action with visual feedback
   */
  private async performClick(selector: string, description: string): Promise<void> {
    const element = document.querySelector(selector) as HTMLElement;
    
    if (!element) {
      throw new Error(`Element not found: ${selector}`);
    }

    // Highlight the element before clicking
    await this.highlightElement(selector, 1000);
    
    // Scroll element into view
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    await this.wait(500);
    
    // Simulate human-like click
    const rect = element.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create visual click indicator
    this.createClickIndicator(centerX, centerY);
    
    // Perform the actual click
    element.click();
    
    await this.wait(300);
  }

  /**
   * Perform typing with visual feedback
   */
  private async performType(selector: string, value: string, description: string): Promise<void> {
    const element = document.querySelector(selector) as HTMLInputElement;
    
    if (!element) {
      throw new Error(`Input element not found: ${selector}`);
    }

    // Highlight and focus the input
    await this.highlightElement(selector, 1000);
    element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    element.focus();
    
    // Clear existing value
    element.value = '';
    
    // Type character by character for visual effect
    for (let i = 0; i < value.length; i++) {
      element.value += value[i];
      
      // Trigger input events
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      await this.wait(50); // Typing speed
    }
  }

  /**
   * Highlight an element with visual overlay
   */
  private async highlightElement(selector: string, duration: number = 2000): Promise<void> {
    const element = document.querySelector(selector);
    
    if (!element) {
      console.warn(`Element not found for highlighting: ${selector}`);
      return;
    }

    const rect = element.getBoundingClientRect();
    
    if (this.highlightOverlay) {
      this.highlightOverlay.style.left = `${rect.left}px`;
      this.highlightOverlay.style.top = `${rect.top}px`;
      this.highlightOverlay.style.width = `${rect.width}px`;
      this.highlightOverlay.style.height = `${rect.height}px`;
      this.highlightOverlay.style.display = 'block';
      
      // Add pulsing animation
      this.highlightOverlay.style.animation = 'ai-highlight-pulse 1s ease-in-out infinite';
    }

    await this.wait(duration);
    this.clearHighlight();
  }

  /**
   * Create visual click indicator
   */
  private createClickIndicator(x: number, y: number): void {
    const indicator = document.createElement('div');
    indicator.style.cssText = `
      position: fixed;
      left: ${x - 10}px;
      top: ${y - 10}px;
      width: 20px;
      height: 20px;
      border: 2px solid #ff4444;
      border-radius: 50%;
      background: rgba(255, 68, 68, 0.2);
      pointer-events: none;
      z-index: 10000;
      animation: ai-click-ripple 0.6s ease-out forwards;
    `;
    
    document.body.appendChild(indicator);
    
    setTimeout(() => {
      document.body.removeChild(indicator);
    }, 600);
  }

  /**
   * Setup highlight overlay
   */
  private setupHighlightOverlay(): void {
    this.highlightOverlay = document.createElement('div');
    this.highlightOverlay.style.cssText = `
      position: fixed;
      border: 3px solid #00ff88;
      background: rgba(0, 255, 136, 0.1);
      pointer-events: none;
      z-index: 9999;
      display: none;
      border-radius: 4px;
      box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
    `;
    
    document.body.appendChild(this.highlightOverlay);
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
      @keyframes ai-highlight-pulse {
        0%, 100% { opacity: 0.3; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.02); }
      }
      
      @keyframes ai-click-ripple {
        0% { transform: scale(1); opacity: 1; }
        100% { transform: scale(3); opacity: 0; }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Clear highlight overlay
   */
  private clearHighlight(): void {
    if (this.highlightOverlay) {
      this.highlightOverlay.style.display = 'none';
      this.highlightOverlay.style.animation = '';
    }
  }

  /**
   * Speak text with AI personality
   */
  private async speak(text: string): Promise<void> {
    return new Promise((resolve) => {
      if (!this.speechSynthesis) {
        console.log(`🤖 AI: ${text}`);
        resolve();
        return;
      }

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 0.8;
      utterance.volume = 0.7;
      
      // Try to use a professional voice
      const voices = this.speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice => 
        voice.name.includes('Google') || 
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      );
      
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onend = () => resolve();
      utterance.onerror = () => resolve();
      
      this.speechSynthesis.speak(utterance);
    });
  }

  /**
   * Wait for specified duration
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generate CSS selector for element
   */
  private generateSelector(element: Element): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.length > 0);
      if (classes.length > 0) {
        return `.${classes[0]}`;
      }
    }
    
    return element.tagName.toLowerCase();
  }

  /**
   * Get element text content
   */
  private getElementText(element: Element): string {
    return element.textContent?.trim() || 
           (element as HTMLInputElement).placeholder || 
           (element as HTMLElement).title || 
           element.getAttribute('aria-label') || '';
  }

  /**
   * Check if element is clickable
   */
  private isElementClickable(element: Element): boolean {
    const clickableTypes = ['button', 'a', 'input', 'select', 'textarea'];
    return clickableTypes.includes(element.tagName.toLowerCase()) ||
           element.hasAttribute('onclick') ||
           element.getAttribute('role') === 'button';
  }

  /**
   * Get element attributes
   */
  private getElementAttributes(element: Element): Record<string, string> {
    const attrs: Record<string, string> = {};
    for (let i = 0; i < element.attributes.length; i++) {
      const attr = element.attributes[i];
      attrs[attr.name] = attr.value;
    }
    return attrs;
  }

  /**
   * Setup DOM observers for dynamic content
   */
  private setupDOMObservers(): void {
    const observer = new MutationObserver((mutations) => {
      // Handle dynamic content changes
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && this.isActive) {
          // Re-scan elements if needed
          console.log('🔄 DOM changed during navigation, re-scanning...');
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    this.observers.push(observer);
  }

  /**
   * Perform scroll action
   */
  private async performScroll(target?: string, value?: string, description?: string): Promise<void> {
    if (target) {
      const element = document.querySelector(target);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    } else if (value) {
      const scrollAmount = parseInt(value) || 0;
      window.scrollBy({ top: scrollAmount, behavior: 'smooth' });
    }
    
    await this.wait(1000); // Wait for scroll to complete
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    
    if (this.highlightOverlay && this.highlightOverlay.parentNode) {
      this.highlightOverlay.parentNode.removeChild(this.highlightOverlay);
    }
  }
}

// Export singleton instance
export const aiVisualNavigation = new AIVisualNavigationSystem();
