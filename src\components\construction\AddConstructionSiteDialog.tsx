import { useAuth } from "@/components/auth/AuthProvider";
import { Button } from "@/components/ui/button";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Building, Calendar, DollarSign, MapPin, User } from "lucide-react";
import { useState } from "react";

interface AddConstructionSiteDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSiteAdded?: () => void;
  editingSite?: any;
}

export const AddConstructionSiteDialog = ({ 
  isOpen, 
  onOpenChange, 
  onSiteAdded,
  editingSite 
}: AddConstructionSiteDialogProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [siteData, setSiteData] = useState({
    site_name: editingSite?.site_name || '',
    site_code: editingSite?.site_code || '',
    location: editingSite?.location || '',
    latitude: editingSite?.latitude || '',
    longitude: editingSite?.longitude || '',
    site_type: editingSite?.site_type || 'commercial',
    status: editingSite?.status || 'planning',
    start_date: editingSite?.start_date || '',
    expected_completion: editingSite?.expected_completion || '',
    site_manager_id: editingSite?.site_manager_id || '',
    contractor_name: editingSite?.contractor_name || '',
    contractor_contact: editingSite?.contractor_contact || '',
    budget_allocated: editingSite?.budget_allocated || '',
    description: editingSite?.description || '',
    safety_rating: editingSite?.safety_rating || 'good'
  });
  
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const queryClient = useQueryClient();

  // Fetch project managers for selection
  const { data: managers } = useQuery({
    queryKey: ['project-managers'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .in('role', ['admin', 'manager', 'staff-admin'])
        .order('full_name');

      if (error) throw error;
      return data;
    },
  });

  const handleInputChange = (field: string, value: any) => {
    setSiteData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const createSiteMutation = useMutation({
    mutationFn: async (data: any) => {
      const sitePayload = {
        ...data,
        budget_allocated: data.budget_allocated ? parseFloat(data.budget_allocated) : null,
        latitude: data.latitude ? parseFloat(data.latitude) : null,
        longitude: data.longitude ? parseFloat(data.longitude) : null,
        created_by: userProfile?.id
      };

      if (editingSite) {
        const { data: result, error } = await supabase
          .from('construction_sites')
          .update(sitePayload)
          .eq('id', editingSite.id)
          .select()
          .single();
        if (error) throw error;
        return result;
      } else {
        const { data: result, error } = await supabase
          .from('construction_sites')
          .insert([sitePayload])
          .select()
          .single();
        if (error) throw error;
        return result;
      }
    },
    onSuccess: () => {
      toast({
        title: editingSite ? "Site Updated" : "Site Created",
        description: `Construction site "${siteData.site_name}" has been ${editingSite ? 'updated' : 'created'} successfully`,
      });

      // Reset form
      setSiteData({
        site_name: '',
        site_code: '',
        location: '',
        latitude: '',
        longitude: '',
        site_type: 'commercial',
        status: 'planning',
        start_date: '',
        expected_completion: '',
        site_manager_id: '',
        contractor_name: '',
        contractor_contact: '',
        budget_allocated: '',
        description: '',
        safety_rating: 'good'
      });

      // Refresh queries
      queryClient.invalidateQueries({ queryKey: ['construction-sites'] });
      
      if (onSiteAdded) {
        onSiteAdded();
      }
      
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || `Failed to ${editingSite ? 'update' : 'create'} construction site`,
        variant: "destructive",
      });
    }
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!siteData.site_name || !siteData.location || !siteData.site_type) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Site Name, Location, Site Type)",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await createSiteMutation.mutateAsync(siteData);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            {editingSite ? 'Edit Construction Site' : 'Add New Construction Site'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="site_name">Site Name *</Label>
              <Input
                id="site_name"
                value={siteData.site_name}
                onChange={(e) => handleInputChange('site_name', e.target.value)}
                placeholder="Enter site name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="site_code">Site Code</Label>
              <Input
                id="site_code"
                value={siteData.site_code}
                onChange={(e) => handleInputChange('site_code', e.target.value)}
                placeholder="Enter unique site code"
              />
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <Label className="text-base font-medium">Location Information</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="location">Location *</Label>
                <Input
                  id="location"
                  value={siteData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  placeholder="Enter full address"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="site_type">Site Type *</Label>
                <Select value={siteData.site_type} onValueChange={(value) => handleInputChange('site_type', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select site type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="residential">Residential</SelectItem>
                    <SelectItem value="commercial">Commercial</SelectItem>
                    <SelectItem value="industrial">Industrial</SelectItem>
                    <SelectItem value="infrastructure">Infrastructure</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="latitude">Latitude</Label>
                <Input
                  id="latitude"
                  type="number"
                  step="any"
                  value={siteData.latitude}
                  onChange={(e) => handleInputChange('latitude', e.target.value)}
                  placeholder="Enter latitude"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="longitude">Longitude</Label>
                <Input
                  id="longitude"
                  type="number"
                  step="any"
                  value={siteData.longitude}
                  onChange={(e) => handleInputChange('longitude', e.target.value)}
                  placeholder="Enter longitude"
                />
              </div>
            </div>
          </div>

          {/* Project Details */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <Label className="text-base font-medium">Project Details</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={siteData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="planning">Planning</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="site_manager_id">Site Manager</Label>
                <Select value={siteData.site_manager_id} onValueChange={(value) => handleInputChange('site_manager_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select project manager" />
                  </SelectTrigger>
                  <SelectContent>
                    {managers?.map((manager) => (
                      <SelectItem key={manager.id} value={manager.id}>
                        {manager.full_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={siteData.start_date}
                  onChange={(e) => handleInputChange('start_date', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expected_completion">Expected Completion</Label>
                <Input
                  id="expected_completion"
                  type="date"
                  value={siteData.expected_completion}
                  onChange={(e) => handleInputChange('expected_completion', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Contractor Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <Label className="text-base font-medium">Contractor Information</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contractor_name">Contractor Name</Label>
                <Input
                  id="contractor_name"
                  value={siteData.contractor_name}
                  onChange={(e) => handleInputChange('contractor_name', e.target.value)}
                  placeholder="Enter contractor name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contractor_contact">Contractor Contact</Label>
                <Input
                  id="contractor_contact"
                  value={siteData.contractor_contact}
                  onChange={(e) => handleInputChange('contractor_contact', e.target.value)}
                  placeholder="Enter contact information"
                />
              </div>
            </div>
          </div>

          {/* Budget and Safety */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              <Label className="text-base font-medium">Budget & Safety</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="budget_allocated">Budget Allocated (₦)</Label>
                <Input
                  id="budget_allocated"
                  type="number"
                  step="0.01"
                  value={siteData.budget_allocated}
                  onChange={(e) => handleInputChange('budget_allocated', e.target.value)}
                  placeholder="Enter budget amount"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="safety_rating">Safety Rating</Label>
                <Select value={siteData.safety_rating} onValueChange={(value) => handleInputChange('safety_rating', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select safety rating" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="excellent">Excellent</SelectItem>
                    <SelectItem value="good">Good</SelectItem>
                    <SelectItem value="fair">Fair</SelectItem>
                    <SelectItem value="poor">Poor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={siteData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter project description"
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : editingSite ? 'Update Site' : 'Create Site'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
