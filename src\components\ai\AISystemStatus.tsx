import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, XCircle, AlertTriangle, RefreshCw } from "lucide-react";
import { AIDiagnostics, AIServiceStatus } from "@/lib/ai/diagnostics";
import { useToast } from "@/hooks/use-toast";

interface SystemStatus {
  overall: 'healthy' | 'degraded' | 'down';
  services: AIServiceStatus[];
  lastChecked: Date;
  issues: string[];
}

export const AISystemStatus: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const checkSystemStatus = async () => {
    setLoading(true);
    try {
      const services = await AIDiagnostics.checkAIServices();
      
      // Determine overall status
      const healthyServices = services.filter(s => s.status === 'healthy').length;
      const totalServices = services.length;
      
      let overall: 'healthy' | 'degraded' | 'down';
      if (healthyServices === totalServices) {
        overall = 'healthy';
      } else if (healthyServices > 0) {
        overall = 'degraded';
      } else {
        overall = 'down';
      }

      // Collect issues
      const issues = services
        .filter(s => s.status !== 'healthy')
        .map(s => `${s.service}: ${s.error || 'Service unavailable'}`);

      setStatus({
        overall,
        services,
        lastChecked: new Date(),
        issues
      });
    } catch (error) {
      toast({
        title: "Status Check Failed",
        description: "Unable to check AI system status",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkSystemStatus();
    // Check status every 5 minutes
    const interval = setInterval(checkSystemStatus, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'down':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-500';
      case 'degraded':
        return 'bg-yellow-500';
      case 'down':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Loader2 className="h-5 w-5 animate-spin" />
            Checking AI System Status...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (!status) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="text-red-600">System Status Unavailable</CardTitle>
          <CardDescription>Unable to retrieve system status</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={checkSystemStatus} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {getStatusIcon(status.overall)}
            AI System Status
            <Badge variant={status.overall === 'healthy' ? 'default' : 'destructive'}>
              {status.overall.toUpperCase()}
            </Badge>
          </CardTitle>
          <CardDescription>
            Last checked: {status.lastChecked.toLocaleString()}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Overall Status Indicator */}
          <div className="flex items-center gap-2">
            <div className={`w-3 h-3 rounded-full ${getStatusColor(status.overall)}`} />
            <span className="font-medium">
              {status.overall === 'healthy' && 'All AI services are operational'}
              {status.overall === 'degraded' && 'Some AI services are experiencing issues'}
              {status.overall === 'down' && 'AI services are currently unavailable'}
            </span>
          </div>

          {/* Service Details */}
          <div className="space-y-2">
            <h4 className="font-medium">Service Details:</h4>
            {status.services.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center gap-2">
                  {getStatusIcon(service.status)}
                  <span className="font-medium">{service.service}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={service.status === 'healthy' ? 'default' : 'destructive'}>
                    {service.status}
                  </Badge>
                  {service.environment.openAI === 'missing' && (
                    <Badge variant="outline">No API Key</Badge>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Issues */}
          {status.issues.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1">
                  <p className="font-medium">Current Issues:</p>
                  <ul className="list-disc list-inside space-y-1">
                    {status.issues.map((issue, index) => (
                      <li key={index} className="text-sm">{issue}</li>
                    ))}
                  </ul>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Refresh Button */}
          <Button onClick={checkSystemStatus} variant="outline" className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Status
          </Button>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common troubleshooting steps</CardDescription>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Button variant="outline" size="sm">
              Test AI Chat
            </Button>
            <Button variant="outline" size="sm">
              Test Document Analysis
            </Button>
            <Button variant="outline" size="sm">
              Check API Configuration
            </Button>
            <Button variant="outline" size="sm">
              View Error Logs
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
