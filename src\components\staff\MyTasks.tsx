
import { useAuth } from "@/components/auth/AuthProvider";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { Calendar, CheckCircle, CheckSquare, Clock, Loader2, Pause, Play, User } from "lucide-react";
import { useState } from "react";

interface Task {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to_id: string;
  created_by_id: string;
  due_date: string | null;
  estimated_hours: number | null;
  actual_hours: number | null;
  created_at: string;
  updated_at: string;
}

export const MyTasks = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [updateNote, setUpdateNote] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch user's assigned tasks
  const { data: tasks, isLoading } = useQuery({
    queryKey: ['my-tasks', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];

      // Try new schema first (assigned_to_id)
      let { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          creator:profiles!tasks_created_by_fkey(full_name, email)
        `)
        .eq('assigned_to_id', userProfile.id)
        .order('created_at', { ascending: false });

      // If foreign key error, try without the foreign key relationship
      if (error && (error.code === 'PGRST116' || error.message?.includes('foreign key'))) {
        console.log('Foreign key relationship failed, trying basic query...');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('tasks')
          .select('*')
          .eq('assigned_to_id', userProfile.id)
          .order('created_at', { ascending: false });

        data = fallbackData;
        error = fallbackError;
      }

      // If assigned_to_id doesn't exist, try assigned_to (old schema)
      if (error && error.code === 'PGRST116') {
        console.log('assigned_to_id column not found, trying assigned_to...');
        const { data: oldSchemaData, error: oldSchemaError } = await supabase
          .from('tasks')
          .select('*')
          .eq('assigned_to', userProfile.id)
          .order('created_at', { ascending: false });

        data = oldSchemaData;
        error = oldSchemaError;
      }

      if (error) throw error;
      return data as Task[];
    },
    enabled: !!userProfile?.id,
  });

  // Update task status mutation
  const updateTaskMutation = useMutation({
    mutationFn: async ({ taskId, status, note }: { taskId: string; status: string; note?: string }) => {
      const updateData: any = { 
        status,
        updated_at: new Date().toISOString()
      };
      
      if (status === 'completed') {
        updateData.actual_hours = 0; // Could be calculated from time tracking
      }

      const { data, error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .select()
        .single();

      if (error) throw error;

      // Log the task update activity
      if (note) {
        await supabase.from('activity_logs').insert({
          user_id: userProfile?.id,
          action: 'task_update',
          entity_type: 'task',
          entity_id: taskId,
          description: `Task status updated to ${status}: ${note}`,
          metadata: { status, note }
        });
      }

      return data;
    },
    onSuccess: () => {
      toast({
        title: "Task Updated",
        description: "Task status has been updated successfully.",
      });
      queryClient.invalidateQueries({ queryKey: ['my-tasks'] });
      setIsDialogOpen(false);
      setUpdateNote("");
      setSelectedTask(null);
    },
    onError: (error) => {
      toast({
        title: "Update Failed",
        description: "Failed to update task status. Please try again.",
        variant: "destructive",
      });
      console.error('Task update error:', error);
    },
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: "secondary",
      in_progress: "default",
      completed: "outline",
      cancelled: "destructive"
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || "secondary"}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: "text-green-600",
      medium: "text-yellow-600", 
      high: "text-orange-600",
      urgent: "text-red-600"
    } as const;
    
    return colors[priority as keyof typeof colors] || "text-gray-600";
  };

  const handleStatusUpdate = (task: Task, newStatus: string) => {
    setSelectedTask(task);
    setIsDialogOpen(true);
    
    // Auto-update for simple status changes
    if (newStatus === 'in_progress' && task.status === 'pending') {
      updateTaskMutation.mutate({ taskId: task.id, status: newStatus });
      return;
    }
    
    // For completion, we might want to add notes
    if (newStatus === 'completed') {
      // Dialog will handle this
      return;
    }
  };

  const getTaskProgress = (task: Task) => {
    switch (task.status) {
      case 'pending': return 0;
      case 'in_progress': return 50;
      case 'completed': return 100;
      case 'cancelled': return 0;
      default: return 0;
    }
  };

  const isOverdue = (dueDate: string | null) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading your tasks...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!tasks || tasks.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <CheckSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No Tasks Assigned</h3>
          <p className="text-muted-foreground mb-4">
            You don't have any tasks assigned at the moment. This could be because:
          </p>
          <ul className="text-sm text-muted-foreground text-left max-w-md mx-auto space-y-1">
            <li>• No tasks have been assigned to you yet</li>
            <li>• There might be a database connection issue</li>
            <li>• Your user profile might not be properly configured</li>
          </ul>
          <div className="mt-4 text-xs text-muted-foreground">
            User ID: {userProfile?.id || 'Not available'}
          </div>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => window.location.href = '/debug'}
          >
            Debug Tasks
          </Button>
        </CardContent>
      </Card>
    );
  }

  const pendingTasks = tasks?.filter(t => t.status === 'pending') || [];
  const inProgressTasks = tasks?.filter(t => t.status === 'in_progress') || [];
  const completedTasks = tasks?.filter(t => t.status === 'completed') || [];

  return (
    <div className="space-y-6">
      {/* Task Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending</p>
                <p className="text-2xl font-bold">{pendingTasks.length}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">In Progress</p>
                <p className="text-2xl font-bold">{inProgressTasks.length}</p>
              </div>
              <Play className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{completedTasks.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Task List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5" />
            My Tasks ({tasks?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {tasks && tasks.length > 0 ? (
            <div className="space-y-4">
              {tasks.map((task) => (
                <div key={task.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className="font-medium">{task.title}</h4>
                        {getStatusBadge(task.status)}
                        <span className={`text-xs font-medium uppercase ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </span>
                        {task.due_date && isOverdue(task.due_date) && task.status !== 'completed' && (
                          <Badge variant="destructive" className="text-xs">OVERDUE</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{task.description}</p>
                      
                      {/* Progress Bar */}
                      <div className="mb-3">
                        <div className="flex items-center justify-between text-xs mb-1">
                          <span>Progress</span>
                          <span>{getTaskProgress(task)}%</span>
                        </div>
                        <Progress value={getTaskProgress(task)} className="h-2" />
                      </div>

                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        {task.due_date && (
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            Due: {format(new Date(task.due_date), 'MMM dd, yyyy')}
                          </div>
                        )}
                        {task.estimated_hours && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            Est: {task.estimated_hours}h
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          Created: {format(new Date(task.created_at), 'MMM dd')}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    {task.status === 'pending' && (
                      <Button
                        size="sm"
                        onClick={() => handleStatusUpdate(task, 'in_progress')}
                        disabled={updateTaskMutation.isPending}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        Start Task
                      </Button>
                    )}
                    
                    {task.status === 'in_progress' && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleStatusUpdate(task, 'pending')}
                          disabled={updateTaskMutation.isPending}
                        >
                          <Pause className="h-4 w-4 mr-1" />
                          Pause
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            setSelectedTask(task);
                            setIsDialogOpen(true);
                          }}
                          disabled={updateTaskMutation.isPending}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Complete
                        </Button>
                      </>
                    )}

                    {task.status === 'completed' && (
                      <Badge variant="outline" className="text-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Completed
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No tasks assigned to you yet</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Task Completion Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Task</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">{selectedTask?.title}</h4>
              <p className="text-sm text-muted-foreground">{selectedTask?.description}</p>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Completion Notes (Optional)</label>
              <Textarea
                value={updateNote}
                onChange={(e) => setUpdateNote(e.target.value)}
                placeholder="Add any notes about task completion..."
                rows={3}
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={() => {
                  if (selectedTask) {
                    updateTaskMutation.mutate({
                      taskId: selectedTask.id,
                      status: 'completed',
                      note: updateNote
                    });
                  }
                }}
                disabled={updateTaskMutation.isPending}
              >
                {updateTaskMutation.isPending ? "Updating..." : "Mark as Completed"}
              </Button>
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
