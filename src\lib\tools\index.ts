import { stringTools } from './categories/string';
import { imageTools } from './categories/image';
import { numberTools } from './categories/number';
import { videoTools } from './categories/video';
import { listTools } from './categories/list';
import { jsonTools } from './categories/json';
import { timeTools } from './categories/time';
import { csvTools } from './categories/csv';
import { pdfTools } from './categories/pdf';
import { dataTools } from './categories/data';
import { DefinedTool, ToolCategory } from './defineTool';

const toolCategoriesOrder: ToolCategory[] = [
  'image-generic',
  'string',
  'json',
  'pdf',
  'video',
  'list',
  'csv',
  'number',
  'time',
  'data'
];

export const tools: DefinedTool[] = [
  ...imageTools,
  ...stringTools,
  ...jsonTools,
  ...pdfTools,
  ...listTools,
  ...csvTools,
  ...videoTools,
  ...numberTools,
  ...timeTools,
  ...dataTools
];

const categoriesConfig: {
  type: ToolCategory;
  value: string;
  title?: string;
  icon: string;
}[] = [
  {
    type: 'string',
    title: 'Text',
    icon: 'solar:text-bold-duotone',
    value: 'Tools for working with text – convert text cases, encode/decode, find and replace, split text, join text lines, and much more.'
  },
  {
    type: 'image-generic',
    title: 'Image',
    icon: 'material-symbols-light:image-outline-rounded',
    value: 'Tools for working with pictures – compress, resize, crop, convert formats, rotate, remove background and much more.'
  },
  {
    type: 'number',
    icon: 'lsicon:number-filled',
    value: 'Tools for working with numbers – generate sequences, convert numbers to words, sort, round, factor numbers, and much more.'
  },
  {
    type: 'list',
    icon: 'solar:list-bold-duotone',
    value: 'Tools for working with lists – sort, reverse, randomize lists, find unique and duplicate items, change separators, and much more.'
  },
  {
    type: 'json',
    icon: 'lets-icons:json-light',
    value: 'Tools for working with JSON data – prettify and minify JSON objects, flatten arrays, stringify values, analyze data, and much more'
  },
  {
    type: 'time',
    icon: 'mdi:clock-time-five',
    value: 'Tools for working with time and date – calculate differences, convert time zones, format dates, generate sequences, and much more.'
  },
  {
    type: 'csv',
    icon: 'material-symbols-light:csv-outline',
    value: 'Tools for working with CSV files - convert CSV to different formats, manipulate data, validate structure, and process files efficiently.'
  },
  {
    type: 'video',
    icon: 'lets-icons:video-light',
    value: 'Tools for working with videos – extract frames, create GIFs from videos, convert formats, compress videos, and much more.'
  },
  {
    type: 'pdf',
    icon: 'tabler:pdf',
    value: 'Tools for working with PDF files - extract text, convert PDFs to other formats, manipulate PDFs, merge and split documents.'
  },
  {
    type: 'data',
    icon: 'material-symbols:data-object',
    value: 'Tools for working with data formats - convert between formats, validate data, transform structures, and process files.'
  }
];

export const filterTools = (
  tools: DefinedTool[],
  query: string
): DefinedTool[] => {
  if (!query) return tools;

  const lowerCaseQuery = query.toLowerCase();

  return tools.filter(
    (tool) =>
      tool.name.toLowerCase().includes(lowerCaseQuery) ||
      tool.description.toLowerCase().includes(lowerCaseQuery) ||
      tool.shortDescription.toLowerCase().includes(lowerCaseQuery) ||
      tool.keywords.some((keyword) =>
        keyword.toLowerCase().includes(lowerCaseQuery)
      )
  );
};

export const getToolsByCategory = (): {
  title: string;
  rawTitle: string;
  description: string;
  icon: string;
  type: ToolCategory;
  example: { title: string; path: string };
  tools: DefinedTool[];
}[] => {
  const groupedByType: Partial<Record<ToolCategory, DefinedTool[]>> = {};
  
  // Group tools by type
  tools.forEach(tool => {
    if (!groupedByType[tool.type]) {
      groupedByType[tool.type] = [];
    }
    groupedByType[tool.type]!.push(tool);
  });

  return Object.entries(groupedByType)
    .map(([type, tools]) => {
      const categoryConfig = categoriesConfig.find(
        (config) => config.type === type
      );
      const capitalizeFirstLetter = (str: string) => str.charAt(0).toUpperCase() + str.slice(1);
      
      return {
        rawTitle: categoryConfig?.title ?? capitalizeFirstLetter(type),
        title: `${categoryConfig?.title ?? capitalizeFirstLetter(type)} Tools`,
        description: categoryConfig?.value ?? '',
        type: type as ToolCategory,
        icon: categoryConfig!.icon,
        tools: tools ?? [],
        example: tools && tools.length > 0
          ? { title: tools[0].name, path: tools[0].path }
          : { title: '', path: '' }
      };
    })
    .sort(
      (a, b) =>
        toolCategoriesOrder.indexOf(a.type) -
        toolCategoriesOrder.indexOf(b.type)
    );
};
