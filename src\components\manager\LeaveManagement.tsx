import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar, CheckCircle, XCircle, Download, Filter, Clock, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DocumentGenerator } from "@/lib/documentGenerator";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { format, differenceInDays } from "date-fns";

export const LeaveManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Fetch leave requests from database
  const { data: leaveRequests = [], isLoading, refetch } = useQuery({
    queryKey: ['manager-leave-requests'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('leave_requests')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email,
            department:department_id (name)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    }
  });

  // Approve leave request mutation
  const approveMutation = useMutation({
    mutationFn: async (requestId: string) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('leave_requests')
        .update({
          status: 'approved',
          approved_by: user.user.id,
          approved_at: new Date().toISOString(),
        })
        .eq('id', requestId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['manager-leave-requests'] });
      toast({
        title: "Leave Approved",
        description: "Leave request has been approved successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to approve leave request",
        variant: "destructive",
      });
    }
  });

  // Reject leave request mutation
  const rejectMutation = useMutation({
    mutationFn: async (requestId: string) => {
      const { data: user } = await supabase.auth.getUser();
      if (!user.user) throw new Error('Not authenticated');

      const { error } = await supabase
        .from('leave_requests')
        .update({
          status: 'rejected',
          approved_by: user.user.id,
          approved_at: new Date().toISOString(),
          rejection_reason: 'Rejected by manager',
        })
        .eq('id', requestId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['manager-leave-requests'] });
      toast({
        title: "Leave Rejected",
        description: "Leave request has been rejected.",
        variant: "destructive",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to reject leave request",
        variant: "destructive",
      });
    }
  });

  const filteredRequests = statusFilter === "all"
    ? leaveRequests
    : leaveRequests.filter((req: any) => req.status.toLowerCase() === statusFilter);

  const handleApprove = (id: string) => {
    approveMutation.mutate(id);
  };

  const handleReject = (id: string) => {
    rejectMutation.mutate(id);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <Badge variant="default" className="bg-green-500">Approved</Badge>;
      case 'rejected':
        return <Badge variant="destructive">Rejected</Badge>;
      case 'pending':
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  const calculateDays = (startDate: string, endDate: string) => {
    return differenceInDays(new Date(endDate), new Date(startDate)) + 1;
  };

  const exportToExcel = async () => {
    try {
      const formattedData = filteredRequests.map((req: any) => ({
        employee: req.profiles?.full_name || 'Unknown',
        type: req.leave_type,
        startDate: format(new Date(req.start_date), 'MMM dd, yyyy'),
        endDate: format(new Date(req.end_date), 'MMM dd, yyyy'),
        days: calculateDays(req.start_date, req.end_date),
        reason: req.reason,
        status: req.status,
        appliedOn: format(new Date(req.created_at), 'MMM dd, yyyy'),
      }));

      const exportData = DocumentGenerator.formatDataForExport(
        formattedData,
        {
          employee: 'Employee Name',
          type: 'Leave Type',
          startDate: 'Start Date',
          endDate: 'End Date',
          days: 'Days Requested',
          reason: 'Reason',
          status: 'Status',
          appliedOn: 'Applied On',
        },
        ['startDate', 'endDate', 'appliedOn'],
        ['days']
      );

      await DocumentGenerator.exportToExcel(exportData, {
        filename: 'leave-requests',
        title: 'Leave Requests Report',
        subtitle: 'Employee Leave Management System',
        companyName: 'Your Company',
      });

      toast({
        title: "Export Successful",
        description: "Leave requests exported to Excel file",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export to Excel",
        variant: "destructive",
      });
    }
  };

  const exportToPDF = async () => {
    try {
      const formattedData = filteredRequests.map((req: any) => ({
        employee: req.profiles?.full_name || 'Unknown',
        type: req.leave_type,
        startDate: format(new Date(req.start_date), 'MMM dd, yyyy'),
        endDate: format(new Date(req.end_date), 'MMM dd, yyyy'),
        days: calculateDays(req.start_date, req.end_date),
        status: req.status,
        appliedOn: format(new Date(req.created_at), 'MMM dd, yyyy'),
      }));

      const exportData = DocumentGenerator.formatDataForExport(
        formattedData,
        {
          employee: 'Employee Name',
          type: 'Leave Type',
          startDate: 'Start Date',
          endDate: 'End Date',
          days: 'Days Requested',
          status: 'Status',
          appliedOn: 'Applied On',
        },
        ['startDate', 'endDate', 'appliedOn'],
        ['days']
      );

      await DocumentGenerator.exportToPDF(exportData, {
        filename: 'leave-requests',
        title: 'Leave Requests Report',
        subtitle: 'Employee Leave Management System',
        companyName: 'Your Company',
        orientation: 'landscape',
      });

      toast({
        title: "Export Successful",
        description: "Leave requests exported to PDF file",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export to PDF",
        variant: "destructive",
      });
    }
  };

  const exportToCSV = async () => {
    try {
      const formattedData = filteredRequests.map((req: any) => ({
        employee: req.profiles?.full_name || 'Unknown',
        type: req.leave_type,
        startDate: format(new Date(req.start_date), 'MMM dd, yyyy'),
        endDate: format(new Date(req.end_date), 'MMM dd, yyyy'),
        days: calculateDays(req.start_date, req.end_date),
        reason: req.reason,
        status: req.status,
        appliedOn: format(new Date(req.created_at), 'MMM dd, yyyy'),
      }));

      const exportData = DocumentGenerator.formatDataForExport(
        formattedData,
        {
          employee: 'Employee Name',
          type: 'Leave Type',
          startDate: 'Start Date',
          endDate: 'End Date',
          days: 'Days Requested',
          reason: 'Reason',
          status: 'Status',
          appliedOn: 'Applied On',
        },
        ['startDate', 'endDate', 'appliedOn'],
        ['days']
      );

      await DocumentGenerator.exportToCSV(exportData, {
        filename: 'leave-requests',
        title: 'Leave Requests Report',
        subtitle: 'Employee Leave Management System',
        companyName: 'Your Company',
      });

      toast({
        title: "Export Successful",
        description: "Leave requests exported to CSV file",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export to CSV",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-500/20 text-green-700 border-green-500/20';
      case 'rejected':
        return 'bg-red-500/20 text-red-700 border-red-500/20';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/20';
      default:
        return 'bg-gray-500/20 text-gray-700 border-gray-500/20';
    }
  };

  const stats = {
    total: leaveRequests.length,
    pending: leaveRequests.filter((r: any) => r.status === 'pending').length,
    approved: leaveRequests.filter((r: any) => r.status === 'approved').length,
    rejected: leaveRequests.filter((r: any) => r.status === 'rejected').length,
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Leave Management</h2>
          <p className="text-muted-foreground">Review and manage employee leave requests</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => refetch()} variant="outline" size="sm" disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={exportToExcel} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button onClick={exportToPDF} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button onClick={exportToCSV} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Calendar className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Requests</p>
              <p className="text-2xl font-bold">{stats.total}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Clock className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Pending</p>
              <p className="text-2xl font-bold">{stats.pending}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Approved</p>
              <p className="text-2xl font-bold">{stats.approved}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <XCircle className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Rejected</p>
              <p className="text-2xl font-bold">{stats.rejected}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Leave Requests
            </CardTitle>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employee</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Days</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Applied On</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
                    Loading leave requests...
                  </TableCell>
                </TableRow>
              ) : filteredRequests.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                    No leave requests found
                  </TableCell>
                </TableRow>
              ) : (
                filteredRequests.map((request: any) => (
                  <TableRow key={request.id}>
                    <TableCell className="font-medium">{request.profiles?.full_name || 'Unknown'}</TableCell>
                    <TableCell className="capitalize">{request.leave_type}</TableCell>
                    <TableCell>{format(new Date(request.start_date), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>{format(new Date(request.end_date), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>{calculateDays(request.start_date, request.end_date)}</TableCell>
                    <TableCell>{getStatusBadge(request.status)}</TableCell>
                    <TableCell>{format(new Date(request.created_at), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>
                      {request.status === "pending" && (
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleApprove(request.id)}
                            disabled={approveMutation.isPending}
                          >
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReject(request.id)}
                            disabled={rejectMutation.isPending}
                          >
                            <XCircle className="h-4 w-4 text-red-500" />
                          </Button>
                        </div>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
          {filteredRequests.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No leave requests found</p>
              <p className="text-sm">Leave requests will appear here when submitted by employees</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}; 