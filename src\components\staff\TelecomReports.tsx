
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { Network, Plus, MapPin, Activity, Eye } from "lucide-react";
import { format } from "date-fns";

export const TelecomReports = () => {
  const { userProfile } = useAuth();
  const [selectedSite, setSelectedSite] = useState<any>(null);
  const [showNewReportDialog, setShowNewReportDialog] = useState(false);

  const { data: sites, isLoading } = useQuery({
    queryKey: ['telecom-sites'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('telecom_sites')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational': return 'default';
      case 'active': return 'default';
      case 'maintenance': return 'secondary';
      case 'offline': return 'destructive';
      case 'inactive': return 'destructive';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading telecom sites...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Telecom Site Reports</h2>
          <p className="text-muted-foreground">Monitor and report on telecom site status</p>
        </div>
        <Dialog open={showNewReportDialog} onOpenChange={setShowNewReportDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Report
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create New Telecom Report</DialogTitle>
            </DialogHeader>
            <div className="p-4 text-center">
              <p className="text-muted-foreground mb-4">
                Telecom report creation functionality will be implemented here.
              </p>
              <Button onClick={() => setShowNewReportDialog(false)}>
                Close
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sites && sites.length > 0 ? (
          sites.map((site) => (
            <Card key={site.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <Network className="h-4 w-4" />
                    {site.name || site.site_name}
                  </CardTitle>
                  <Badge variant={getStatusColor(site.status)}>
                    {site.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-3 w-3" />
                    {site.location}
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Activity className="h-3 w-3" />
                    Type: Telecom Site
                  </div>
                  {site.last_maintenance && (
                    <div className="text-xs text-muted-foreground">
                      Last maintenance: {format(new Date(site.last_maintenance), 'MMM dd, yyyy')}
                    </div>
                  )}
                </div>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="w-full mt-4" variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>{site.name || site.site_name} - Details</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Location</label>
                          <p className="text-sm">{site.location}</p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Status</label>
                          <p className="text-sm">
                            <Badge variant={getStatusColor(site.status)}>{site.status}</Badge>
                          </p>
                        </div>
                        {site.site_code && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Site Code</label>
                            <p className="text-sm">{site.site_code}</p>
                          </div>
                        )}
                        {site.region && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Region</label>
                            <p className="text-sm">{site.region}</p>
                          </div>
                        )}
                        {site.progress_percentage !== undefined && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Progress</label>
                            <p className="text-sm">{site.progress_percentage}%</p>
                          </div>
                        )}
                        {site.safety_rating && (
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Safety Rating</label>
                            <p className="text-sm">{site.safety_rating}</p>
                          </div>
                        )}
                      </div>
                      {site.notes && (
                        <div>
                          <label className="text-sm font-medium text-muted-foreground">Notes</label>
                          <p className="text-sm">{site.notes}</p>
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="col-span-full text-center py-8 text-muted-foreground">
            <Network className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No telecom sites found</p>
          </div>
        )}
      </div>
    </div>
  );
};
