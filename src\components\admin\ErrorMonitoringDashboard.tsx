/**
 * Error Monitoring Dashboard
 * Admin component for monitoring system errors and logs
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AlertTriangle, 
  Bug, 
  Activity, 
  TrendingUp, 
  RefreshCw, 
  Download,
  Filter,
  Search,
  Calendar,
  BarChart3
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { errorHandler, type AppError, type ErrorCategory, type ErrorSeverity } from '@/lib/error-handler';
import { logger, type LogEntry, type LogLevel, type LogCategory } from '@/lib/logger';
import { useErrorMonitoring } from '@/hooks/useErrorHandler';

interface ErrorStatsCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  trend?: number;
  severity?: ErrorSeverity;
}

const ErrorStatsCard: React.FC<ErrorStatsCardProps> = ({ title, value, icon, trend, severity }) => {
  const getSeverityColor = (severity?: ErrorSeverity) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <Card className={severity ? getSeverityColor(severity) : ''}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {trend !== undefined && (
              <p className={`text-xs ${trend > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {trend > 0 ? '↑' : '↓'} {Math.abs(trend)}% from last period
              </p>
            )}
          </div>
          <div className="text-muted-foreground">
            {icon}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

interface ErrorListProps {
  errors: AppError[];
  onErrorSelect?: (error: AppError) => void;
}

const ErrorList: React.FC<ErrorListProps> = ({ errors, onErrorSelect }) => {
  const getSeverityBadgeVariant = (severity: ErrorSeverity) => {
    switch (severity) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(timestamp);
  };

  return (
    <ScrollArea className="h-[400px]">
      <div className="space-y-2">
        {errors.map((error) => (
          <Card 
            key={error.id} 
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => onErrorSelect?.(error)}
          >
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge variant={getSeverityBadgeVariant(error.severity)}>
                      {error.severity}
                    </Badge>
                    <Badge variant="outline">{error.category}</Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatTimestamp(error.timestamp)}
                    </span>
                  </div>
                  <p className="text-sm font-medium truncate">{error.message}</p>
                  <p className="text-xs text-muted-foreground">
                    Code: {error.code} | ID: {error.id.slice(-8)}
                  </p>
                  {error.context?.component && (
                    <p className="text-xs text-muted-foreground">
                      Component: {error.context.component}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-1">
                  {error.retryable && (
                    <Badge variant="outline" className="text-xs">Retryable</Badge>
                  )}
                  {error.actionable && (
                    <Badge variant="outline" className="text-xs">Actionable</Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
        {errors.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Bug className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No errors found</p>
          </div>
        )}
      </div>
    </ScrollArea>
  );
};

interface LogListProps {
  logs: LogEntry[];
}

const LogList: React.FC<LogListProps> = ({ logs }) => {
  const getLogLevelColor = (level: LogLevel) => {
    switch (level) {
      case 'error': return 'text-red-600';
      case 'warn': return 'text-yellow-600';
      case 'info': return 'text-blue-600';
      case 'debug': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(timestamp);
  };

  return (
    <ScrollArea className="h-[400px]">
      <div className="space-y-1">
        {logs.map((log) => (
          <div key={log.id} className="p-3 border rounded-lg hover:bg-muted/50 transition-colors">
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <Badge variant="outline" className={getLogLevelColor(log.level)}>
                    {log.level.toUpperCase()}
                  </Badge>
                  <Badge variant="outline">{log.category}</Badge>
                  <span className="text-xs text-muted-foreground">
                    {formatTimestamp(log.timestamp)}
                  </span>
                </div>
                <p className="text-sm">{log.message}</p>
                {log.component && (
                  <p className="text-xs text-muted-foreground">
                    Component: {log.component}
                  </p>
                )}
                {log.duration && (
                  <p className="text-xs text-muted-foreground">
                    Duration: {log.duration.toFixed(2)}ms
                  </p>
                )}
              </div>
            </div>
          </div>
        ))}
        {logs.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No logs found</p>
          </div>
        )}
      </div>
    </ScrollArea>
  );
};

export const ErrorMonitoringDashboard: React.FC = () => {
  const { errorStats, clearErrorStats } = useErrorMonitoring();
  const [selectedError, setSelectedError] = useState<AppError | null>(null);
  const [errorFilter, setErrorFilter] = useState<{
    category?: ErrorCategory;
    severity?: ErrorSeverity;
    search?: string;
  }>({});
  const [logFilter, setLogFilter] = useState<{
    level?: LogLevel;
    category?: LogCategory;
    search?: string;
  }>({});

  // Get filtered errors
  const filteredErrors = errorStats.recentErrors.filter(error => {
    if (errorFilter.category && error.category !== errorFilter.category) return false;
    if (errorFilter.severity && error.severity !== errorFilter.severity) return false;
    if (errorFilter.search && !error.message.toLowerCase().includes(errorFilter.search.toLowerCase())) return false;
    return true;
  });

  // Get recent logs
  const [recentLogs, setRecentLogs] = useState<LogEntry[]>([]);
  
  useEffect(() => {
    const logs = logger.getLogs().slice(0, 50);
    setRecentLogs(logs);
  }, []);

  const filteredLogs = recentLogs.filter(log => {
    if (logFilter.level && log.level !== logFilter.level) return false;
    if (logFilter.category && log.category !== logFilter.category) return false;
    if (logFilter.search && !log.message.toLowerCase().includes(logFilter.search.toLowerCase())) return false;
    return true;
  });

  const handleRefresh = () => {
    const logs = logger.getLogs().slice(0, 50);
    setRecentLogs(logs);
  };

  const handleExportErrors = () => {
    const data = {
      timestamp: new Date().toISOString(),
      stats: errorStats,
      errors: filteredErrors,
      logs: filteredLogs,
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Error Monitoring Dashboard</h2>
          <p className="text-muted-foreground">Monitor system errors and application logs</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExportErrors}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="destructive" onClick={clearErrorStats}>
            Clear All
          </Button>
        </div>
      </div>

      {/* Error Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <ErrorStatsCard
          title="Total Errors"
          value={errorStats.totalErrors}
          icon={<Bug className="h-6 w-6" />}
        />
        <ErrorStatsCard
          title="Critical Errors"
          value={errorStats.errorsBySeverity.critical || 0}
          icon={<AlertTriangle className="h-6 w-6" />}
          severity="critical"
        />
        <ErrorStatsCard
          title="High Priority"
          value={errorStats.errorsBySeverity.high || 0}
          icon={<TrendingUp className="h-6 w-6" />}
          severity="high"
        />
        <ErrorStatsCard
          title="Recent Activity"
          value={recentLogs.length}
          icon={<Activity className="h-6 w-6" />}
        />
      </div>

      {/* Main Content */}
      <Tabs defaultValue="errors" className="space-y-4">
        <TabsList>
          <TabsTrigger value="errors">Errors</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="errors" className="space-y-4">
          {/* Error Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Error Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search errors..."
                    value={errorFilter.search || ''}
                    onChange={(e) => setErrorFilter(prev => ({ ...prev, search: e.target.value }))}
                  />
                </div>
                <Select
                  value={errorFilter.category || 'all'}
                  onValueChange={(value) => setErrorFilter(prev => ({
                    ...prev,
                    category: value === 'all' ? undefined : value as ErrorCategory
                  }))}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="authentication">Authentication</SelectItem>
                    <SelectItem value="authorization">Authorization</SelectItem>
                    <SelectItem value="validation">Validation</SelectItem>
                    <SelectItem value="network">Network</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="ui">UI</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={errorFilter.severity || 'all'}
                  onValueChange={(value) => setErrorFilter(prev => ({
                    ...prev,
                    severity: value === 'all' ? undefined : value as ErrorSeverity
                  }))}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Error List */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Errors ({filteredErrors.length})</CardTitle>
              <CardDescription>
                Latest errors and exceptions in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ErrorList 
                errors={filteredErrors} 
                onErrorSelect={setSelectedError}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          {/* Log Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Log Filters
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search logs..."
                    value={logFilter.search || ''}
                    onChange={(e) => setLogFilter(prev => ({ ...prev, search: e.target.value }))}
                  />
                </div>
                <Select
                  value={logFilter.level || ''}
                  onValueChange={(value) => setLogFilter(prev => ({ 
                    ...prev, 
                    level: value as LogLevel || undefined 
                  }))}
                >
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Levels</SelectItem>
                    <SelectItem value="debug">Debug</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="warn">Warn</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                    <SelectItem value="fatal">Fatal</SelectItem>
                  </SelectContent>
                </Select>
                <Select
                  value={logFilter.category || 'all'}
                  onValueChange={(value) => setLogFilter(prev => ({
                    ...prev,
                    category: value === 'all' ? undefined : value as LogCategory
                  }))}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="auth">Auth</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="database">Database</SelectItem>
                    <SelectItem value="ui">UI</SelectItem>
                    <SelectItem value="performance">Performance</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Log List */}
          <Card>
            <CardHeader>
              <CardTitle>System Logs ({filteredLogs.length})</CardTitle>
              <CardDescription>
                Recent system activity and application logs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LogList logs={filteredLogs} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Error Analytics
              </CardTitle>
              <CardDescription>
                Error trends and patterns analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Errors by Category */}
                <div>
                  <h4 className="font-medium mb-3">Errors by Category</h4>
                  <div className="space-y-2">
                    {Object.entries(errorStats.errorsByCategory).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{category}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Errors by Severity */}
                <div>
                  <h4 className="font-medium mb-3">Errors by Severity</h4>
                  <div className="space-y-2">
                    {Object.entries(errorStats.errorsBySeverity).map(([severity, count]) => (
                      <div key={severity} className="flex items-center justify-between">
                        <span className="text-sm capitalize">{severity}</span>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Error Detail Modal */}
      {selectedError && (
        <Card className="fixed inset-4 z-50 bg-background border shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Error Details</CardTitle>
              <Button variant="ghost" onClick={() => setSelectedError(null)}>
                ×
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <pre className="text-xs whitespace-pre-wrap">
                {JSON.stringify(selectedError, null, 2)}
              </pre>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
