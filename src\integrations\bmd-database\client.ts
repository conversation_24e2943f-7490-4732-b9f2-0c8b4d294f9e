/**
 * BMD TECH HUB Database Client
 * 
 * Copyright (c) 2024 BMD TECH HUB / IFEANYI OBIBI Technologies
 * Licensed under BMD TECH HUB Proprietary License
 * 
 * This software is the proprietary technology of BMD TECH HUB and IFEANYI OBIBI Technologies.
 * All rights reserved. Unauthorized copying, modification, distribution, or use is strictly prohibited.
 * 
 * Contact: <EMAIL>
 * Website: https://bmdtechhub.com
 */

import { createClient } from '@supabase/supabase-js';

// BMD TECH HUB Database Configuration
const BMD_DATABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const BMD_DATABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

/**
 * BMD TECH HUB Database Client
 * Proprietary database solution for CTN Nigeria platform
 * Powered by BMD TECH HUB / IFEANYI OBIBI Technologies
 */
export const bmdDatabase = createClient(BMD_DATABASE_URL, BMD_DATABASE_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'X-BMD-Client': 'CT-NIGERIA-LTD-Platform',
      'X-BMD-Version': '2.0.0',
      'X-BMD-Owner': 'IFEANYI-OBIBI-Technologies'
    }
  }
});

// Legacy compatibility - gradually replace all 'supabase' imports with 'bmdDatabase'
export const supabase = bmdDatabase;

/**
 * BMD Database Types
 * Custom type definitions for BMD TECH HUB database schema
 */
export interface BMDDatabaseSchema {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          user_id: string;
          full_name: string | null;
          email: string | null;
          phone: string | null;
          role: string | null;
          department: string | null;
          avatar_url: string | null;
          created_at: string;
          updated_at: string;
          metadata: Record<string, any> | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          full_name?: string | null;
          email?: string | null;
          phone?: string | null;
          role?: string | null;
          department?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
          metadata?: Record<string, any> | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          full_name?: string | null;
          email?: string | null;
          phone?: string | null;
          role?: string | null;
          department?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
          metadata?: Record<string, any> | null;
        };
      };
      projects: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          status: string;
          priority: string;
          start_date: string | null;
          end_date: string | null;
          budget_total: number | null;
          budget_used: number | null;
          created_by: string;
          created_at: string;
          updated_at: string;
          metadata: Record<string, any> | null;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          status?: string;
          priority?: string;
          start_date?: string | null;
          end_date?: string | null;
          budget_total?: number | null;
          budget_used?: number | null;
          created_by: string;
          created_at?: string;
          updated_at?: string;
          metadata?: Record<string, any> | null;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          status?: string;
          priority?: string;
          start_date?: string | null;
          end_date?: string | null;
          budget_total?: number | null;
          budget_used?: number | null;
          created_by?: string;
          created_at?: string;
          updated_at?: string;
          metadata?: Record<string, any> | null;
        };
      };
      // Add more table definitions as needed
    };
    Views: {
      // Define views here
    };
    Functions: {
      // Define functions here
    };
    Enums: {
      // Define enums here
    };
  };
}

/**
 * BMD Database Utilities
 * Helper functions for BMD TECH HUB database operations
 */
export class BMDDatabaseUtils {
  /**
   * Get current user profile from BMD database
   */
  static async getCurrentUser() {
    try {
      const { data: { user }, error } = await bmdDatabase.auth.getUser();
      if (error) throw error;
      return user;
    } catch (error) {
      console.error('BMD Database: Error getting current user:', error);
      return null;
    }
  }

  /**
   * Get user profile with additional data
   */
  static async getUserProfile(userId: string) {
    try {
      const { data, error } = await bmdDatabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('BMD Database: Error getting user profile:', error);
      return null;
    }
  }

  /**
   * Log BMD database operation
   */
  static async logOperation(operation: string, details: any) {
    try {
      const { error } = await bmdDatabase
        .from('system_logs')
        .insert({
          category: 'bmd_database',
          level: 'info',
          message: `BMD Database Operation: ${operation}`,
          details: JSON.stringify(details),
          metadata: {
            bmd_tech_hub: true,
            ifeanyi_obibi_technologies: true,
            timestamp: new Date().toISOString()
          }
        });

      if (error) {
        console.warn('BMD Database: Could not log operation:', error);
      }
    } catch (error) {
      console.warn('BMD Database: Logging error:', error);
    }
  }

  /**
   * Initialize BMD database connection
   */
  static async initialize() {
    try {
      console.log('🏢 Initializing BMD TECH HUB Database...');
      console.log('👨‍💻 Powered by IFEANYI OBIBI Technologies');
      
      // Test connection
      const { data, error } = await bmdDatabase
        .from('profiles')
        .select('count')
        .limit(1);

      if (error && !error.message.includes('permission')) {
        throw error;
      }

      await this.logOperation('database_initialization', {
        status: 'success',
        timestamp: new Date().toISOString()
      });

      console.log('✅ BMD TECH HUB Database initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ BMD Database initialization failed:', error);
      return false;
    }
  }

  /**
   * Get BMD database statistics
   */
  static async getDatabaseStats() {
    try {
      const stats = {
        company: 'BMD TECH HUB',
        owner: 'IFEANYI OBIBI Technologies',
        platform: 'CTN Nigeria',
        version: '2.0.0',
        license: 'BMD TECH HUB Proprietary License',
        contact: '<EMAIL>',
        website: 'https://bmdtechhub.com',
        initialized: true,
        timestamp: new Date().toISOString()
      };

      return stats;
    } catch (error) {
      console.error('BMD Database: Error getting stats:', error);
      return null;
    }
  }
}

/**
 * BMD Database Configuration
 */
export const BMD_CONFIG = {
  company: 'BMD TECH HUB',
  owner: 'IFEANYI OBIBI Technologies',
  platform: 'CT NIGERIA LTD',
  version: '2.0.0',
  license: 'BMD TECH HUB Proprietary License',
  contact: '<EMAIL>',
  website: 'https://bmdtechhub.com',
  copyright: '© 2024 BMD TECH HUB / IFEANYI OBIBI Technologies. All rights reserved.',
  database: {
    url: BMD_DATABASE_URL,
    provider: 'BMD TECH HUB Database Service',
    region: 'Global',
    encryption: 'AES-256',
    backup: 'Real-time',
    monitoring: '24/7'
  }
};

// Initialize BMD database on import
BMDDatabaseUtils.initialize();

export default bmdDatabase;
