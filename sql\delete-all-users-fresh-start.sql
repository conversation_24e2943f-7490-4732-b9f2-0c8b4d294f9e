-- ============================================================================
-- DELETE ALL USERS - FRESH START
-- This will completely clean the database of all user data
-- WARNING: This is irreversible - all user data will be lost
-- ============================================================================

-- STEP 1: Disable RLS on all user-related tables
ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.time_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.departments DISABLE ROW LEVEL SECURITY;

-- STEP 2: Show current user count before deletion
SELECT 'BEFORE DELETION:' as info;
SELECT 'Profiles count:' as table_name, COUNT(*) as count FROM public.profiles
UNION ALL
SELECT 'Auth users count:' as table_name, COUNT(*) as count FROM auth.users
UNION ALL
SELECT 'Time logs count:' as table_name, COUNT(*) as count FROM public.time_logs
UNION ALL
SELECT 'Projects count:' as table_name, COUNT(*) as count FROM public.projects;

-- STEP 3: Delete all user-related data (in correct order to avoid foreign key issues)

-- Delete time logs first (references profiles)
DELETE FROM public.time_logs;

-- Delete project assignments/memberships if they exist
DELETE FROM public.project_members WHERE TRUE;
DELETE FROM public.project_assignments WHERE TRUE;

-- Delete all profiles
DELETE FROM public.profiles;

-- Delete all authentication users (this is the main cleanup)
DELETE FROM auth.users;

-- Delete any sessions
DELETE FROM auth.sessions;

-- Delete any refresh tokens
DELETE FROM auth.refresh_tokens;

-- Delete any audit log entries related to auth
DELETE FROM auth.audit_log_entries;

-- Delete any identity data
DELETE FROM auth.identities;

-- STEP 4: Reset any sequences if they exist
-- This ensures new users start with clean IDs
SELECT setval(pg_get_serial_sequence('public.profiles', 'id'), 1, false) WHERE pg_get_serial_sequence('public.profiles', 'id') IS NOT NULL;
SELECT setval(pg_get_serial_sequence('public.time_logs', 'id'), 1, false) WHERE pg_get_serial_sequence('public.time_logs', 'id') IS NOT NULL;
SELECT setval(pg_get_serial_sequence('public.projects', 'id'), 1, false) WHERE pg_get_serial_sequence('public.projects', 'id') IS NOT NULL;

-- STEP 5: Clean up any orphaned data in other tables
-- Update any tables that might reference users to NULL or default values
UPDATE public.projects SET created_by = NULL WHERE created_by IS NOT NULL;
UPDATE public.projects SET updated_by = NULL WHERE updated_by IS NOT NULL;

-- STEP 6: Drop ALL existing RLS policies to start completely fresh
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_manager_department" ON public.profiles;
DROP POLICY IF EXISTS "profiles_hr_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_own" ON public.profiles;
DROP POLICY IF EXISTS "Staff-admin can view profiles" ON public.profiles;
DROP POLICY IF EXISTS "profiles_read_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_update" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;

-- STEP 7: Re-enable RLS on profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- STEP 8: Create clean, simple RLS policies for fresh start
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- STEP 9: Grant clean permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;
GRANT ALL ON public.profiles TO service_role;

-- STEP 10: Create/replace the profile creation trigger for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
    'active',
    NOW(),
    NOW()
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 11: Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- STEP 12: Verify the cleanup
SELECT 'AFTER DELETION:' as info;
SELECT 'Profiles count:' as table_name, COUNT(*) as count FROM public.profiles
UNION ALL
SELECT 'Auth users count:' as table_name, COUNT(*) as count FROM auth.users
UNION ALL
SELECT 'Time logs count:' as table_name, COUNT(*) as count FROM public.time_logs
UNION ALL
SELECT 'Projects count:' as table_name, COUNT(*) as count FROM public.projects;

-- STEP 13: Show RLS status
SELECT 'RLS STATUS:' as info;
SELECT schemaname, tablename, rowsecurity,
       (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public') as policy_count
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- STEP 14: List active policies
SELECT 'ACTIVE POLICIES:' as info;
SELECT policyname, cmd as operation
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;

SELECT 'DATABASE CLEANED - FRESH START READY!' as final_status;
SELECT 'You can now create new users without any conflicts.' as instruction;
