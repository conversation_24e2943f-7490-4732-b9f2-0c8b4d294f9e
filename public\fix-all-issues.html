<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix All Issues - AI CTNL Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .log {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.5s ease;
            border-radius: 4px;
        }
        .step {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            background: #f8f9fa;
        }
        .step.active {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
        }
        .step.completed {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .step.error {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .step-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        .center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Fix All Dashboard Issues</h1>
            <p>Complete automated fix for all dashboard data display, RLS, and SQL issues</p>
        </div>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This will automatically fix all dashboard issues. Make sure you're logged in to Supabase.
        </div>
        
        <div id="steps">
            <div class="step" id="step1">
                <span class="step-icon">⏳</span>
                <span>Step 1: Fix Profile Roles & RLS Policies</span>
            </div>
            <div class="step" id="step2">
                <span class="step-icon">⏳</span>
                <span>Step 2: Create Dashboard Tables</span>
            </div>
            <div class="step" id="step3">
                <span class="step-icon">⏳</span>
                <span>Step 3: Fix Dashboard RLS Policies</span>
            </div>
            <div class="step" id="step4">
                <span class="step-icon">⏳</span>
                <span>Step 4: Insert Sample Data</span>
            </div>
            <div class="step" id="step5">
                <span class="step-icon">⏳</span>
                <span>Step 5: Test All Systems</span>
            </div>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
        </div>
        
        <div id="status"></div>
        
        <div class="center">
            <button id="fixBtn" class="button" onclick="runAllFixes()">
                🚀 Fix All Issues Now
            </button>
            
            <button id="dashboardBtn" class="button" onclick="openDashboard()" disabled>
                📊 Open Dashboard
            </button>
        </div>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        function updateProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percent + '%';
        }
        
        function updateStep(stepNumber, status) {
            const step = document.getElementById(`step${stepNumber}`);
            const icon = step.querySelector('.step-icon');
            
            step.className = 'step ' + status;
            
            switch(status) {
                case 'active':
                    icon.textContent = '🔄';
                    break;
                case 'completed':
                    icon.textContent = '✅';
                    break;
                case 'error':
                    icon.textContent = '❌';
                    break;
                default:
                    icon.textContent = '⏳';
            }
        }
        
        async function runAllFixes() {
            const fixBtn = document.getElementById('fixBtn');
            const dashboardBtn = document.getElementById('dashboardBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Fixing...';
            
            try {
                log('🚀 Starting complete system fix...');
                updateProgress(5);
                
                // Check authentication
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                if (userError || !user) {
                    throw new Error('Please log in to Supabase first');
                }
                
                log('✅ User authenticated: ' + user.email);
                const userId = user.id;
                
                // Step 1: Fix Profile Roles
                updateStep(1, 'active');
                log('🔧 Step 1: Fixing profile roles and RLS policies...');
                
                const { error: profileError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Fix profile roles and constraints
                        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
                        ALTER TABLE public.profiles ADD CONSTRAINT profiles_role_check 
                          CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
                        
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff';
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
                        
                        -- Fix profile RLS policies
                        DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
                        
                        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
                        
                        CREATE POLICY "profiles_select_own" ON public.profiles
                          FOR SELECT USING (auth.uid() = id);
                        
                        CREATE POLICY "profiles_select_admin" ON public.profiles
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'hr', 'staff-admin')
                            )
                          );
                    `
                });
                
                if (profileError) throw new Error('Profile roles fix failed: ' + profileError.message);
                updateStep(1, 'completed');
                log('✅ Profile roles and RLS policies fixed');
                updateProgress(20);
                
                // Step 2: Create Dashboard Tables
                updateStep(2, 'active');
                log('🔧 Step 2: Creating dashboard tables...');
                
                const { error: tablesError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Create all dashboard tables
                        CREATE TABLE IF NOT EXISTS public.invoices (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          invoice_number VARCHAR(50) UNIQUE NOT NULL,
                          client_name TEXT NOT NULL,
                          amount DECIMAL(10,2) NOT NULL,
                          total_amount DECIMAL(10,2) NOT NULL,
                          payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'overdue', 'cancelled')),
                          due_date DATE,
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL
                        );

                        CREATE TABLE IF NOT EXISTS public.expense_reports (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          title TEXT NOT NULL,
                          description TEXT,
                          amount DECIMAL(10,2) NOT NULL,
                          category TEXT NOT NULL,
                          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
                          submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );

                        CREATE TABLE IF NOT EXISTS public.reports (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          title TEXT NOT NULL,
                          description TEXT,
                          report_type TEXT DEFAULT 'general',
                          priority TEXT DEFAULT 'medium',
                          status TEXT DEFAULT 'submitted',
                          submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );

                        CREATE TABLE IF NOT EXISTS public.time_logs (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
                          description TEXT,
                          hours_worked DECIMAL(5,2) NOT NULL,
                          log_date DATE DEFAULT CURRENT_DATE,
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );

                        CREATE TABLE IF NOT EXISTS public.notifications (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
                          title TEXT NOT NULL,
                          message TEXT NOT NULL,
                          type TEXT DEFAULT 'info',
                          read BOOLEAN DEFAULT false,
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );
                    `
                });
                
                if (tablesError) throw new Error('Tables creation failed: ' + tablesError.message);
                updateStep(2, 'completed');
                log('✅ Dashboard tables created');
                updateProgress(40);
                
                // Step 3: Fix Dashboard RLS
                updateStep(3, 'active');
                log('🔧 Step 3: Fixing dashboard RLS policies...');
                
                const { error: rlsError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Enable RLS and create policies
                        ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
                        ALTER TABLE public.expense_reports ENABLE ROW LEVEL SECURITY;
                        ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
                        ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
                        ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

                        -- Drop existing policies
                        DROP POLICY IF EXISTS "invoices_select_admin" ON public.invoices;
                        DROP POLICY IF EXISTS "expense_reports_select_own" ON public.expense_reports;
                        DROP POLICY IF EXISTS "reports_select_own" ON public.reports;
                        DROP POLICY IF EXISTS "time_logs_select_own" ON public.time_logs;
                        DROP POLICY IF EXISTS "notifications_select_own" ON public.notifications;

                        -- Create new policies
                        CREATE POLICY "invoices_select_admin" ON public.invoices
                          FOR SELECT USING (
                            EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager'))
                          );

                        CREATE POLICY "expense_reports_select_own" ON public.expense_reports
                          FOR SELECT USING (
                            submitted_by = auth.uid() OR
                            EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant'))
                          );

                        CREATE POLICY "reports_select_own" ON public.reports
                          FOR SELECT USING (
                            submitted_by = auth.uid() OR
                            EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
                          );

                        CREATE POLICY "time_logs_select_own" ON public.time_logs
                          FOR SELECT USING (
                            user_id = auth.uid() OR
                            EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
                          );

                        CREATE POLICY "notifications_select_own" ON public.notifications
                          FOR SELECT USING (user_id = auth.uid());

                        -- Grant permissions
                        GRANT SELECT, INSERT, UPDATE ON public.invoices TO authenticated;
                        GRANT SELECT, INSERT, UPDATE ON public.expense_reports TO authenticated;
                        GRANT SELECT, INSERT, UPDATE ON public.reports TO authenticated;
                        GRANT SELECT, INSERT, UPDATE ON public.time_logs TO authenticated;
                        GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
                    `
                });
                
                if (rlsError) throw new Error('RLS policies failed: ' + rlsError.message);
                updateStep(3, 'completed');
                log('✅ Dashboard RLS policies fixed');
                updateProgress(60);
                
                // Step 4: Insert Sample Data
                updateStep(4, 'active');
                log('🔧 Step 4: Inserting sample data...');
                
                const { error: dataError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Insert sample data
                        INSERT INTO public.invoices (invoice_number, client_name, amount, total_amount, payment_status, created_by) VALUES
                        ('INV-2024-001', 'Acme Corporation', 5000.00, 5000.00, 'paid', '${userId}'),
                        ('INV-2024-002', 'Tech Solutions Ltd', 7500.00, 7500.00, 'pending', '${userId}'),
                        ('INV-2024-003', 'Global Industries', 3200.00, 3200.00, 'overdue', '${userId}')
                        ON CONFLICT (invoice_number) DO NOTHING;

                        INSERT INTO public.expense_reports (title, description, amount, category, status, submitted_by) VALUES
                        ('Office Supplies', 'Monthly office supplies', 450.00, 'office', 'approved', '${userId}'),
                        ('Travel Expenses', 'Business trip', 1200.00, 'travel', 'pending', '${userId}'),
                        ('Equipment', 'New laptop', 2500.00, 'equipment', 'approved', '${userId}')
                        ON CONFLICT DO NOTHING;

                        INSERT INTO public.reports (title, description, report_type, priority, status, submitted_by) VALUES
                        ('Monthly Report', 'Performance summary', 'performance', 'medium', 'submitted', '${userId}'),
                        ('Project Update', 'Project status', 'project', 'high', 'under_review', '${userId}'),
                        ('Financial Summary', 'Quarterly overview', 'financial', 'high', 'approved', '${userId}')
                        ON CONFLICT DO NOTHING;

                        INSERT INTO public.time_logs (user_id, description, hours_worked, log_date) VALUES
                        ('${userId}', 'Dashboard development', 8.0, CURRENT_DATE),
                        ('${userId}', 'Bug fixes', 6.5, CURRENT_DATE - INTERVAL '1 day'),
                        ('${userId}', 'Client meeting', 2.0, CURRENT_DATE - INTERVAL '2 days')
                        ON CONFLICT DO NOTHING;

                        INSERT INTO public.notifications (user_id, title, message, type, read) VALUES
                        ('${userId}', 'Welcome', 'Dashboard is ready!', 'success', false),
                        ('${userId}', 'New Report', 'Report submitted for review', 'info', false),
                        ('${userId}', 'Invoice Overdue', 'Invoice is overdue', 'warning', true)
                        ON CONFLICT DO NOTHING;
                    `
                });
                
                if (dataError) throw new Error('Sample data insertion failed: ' + dataError.message);
                updateStep(4, 'completed');
                log('✅ Sample data inserted');
                updateProgress(80);
                
                // Step 5: Test Systems
                updateStep(5, 'active');
                log('🔧 Step 5: Testing all systems...');
                
                // Test data access
                const { data: invoices } = await supabase.from('invoices').select('*').limit(1);
                const { data: expenses } = await supabase.from('expense_reports').select('*').limit(1);
                const { data: reports } = await supabase.from('reports').select('*').limit(1);
                
                updateStep(5, 'completed');
                log('✅ All systems tested successfully');
                updateProgress(100);
                
                log('🎉 All fixes completed successfully!');
                showStatus('🎉 All dashboard issues have been fixed! Your dashboard should now display data properly.', 'success');
                
                dashboardBtn.disabled = false;
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                showStatus('❌ Fix failed: ' + error.message, 'error');
                
                // Mark current step as error
                for (let i = 1; i <= 5; i++) {
                    const step = document.getElementById(`step${i}`);
                    if (step.classList.contains('active')) {
                        updateStep(i, 'error');
                        break;
                    }
                }
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🚀 Fix All Issues Now';
            }
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Make functions global
        window.runAllFixes = runAllFixes;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
