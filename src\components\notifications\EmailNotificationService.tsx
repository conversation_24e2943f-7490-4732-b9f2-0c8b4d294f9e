﻿import { useState, useEffect } from "react";
import { <PERSON>, Mail, Settings, Check, X, AlertCircle, Send } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/components/auth/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { api } from "@/lib/api";

interface EmailNotification {
  id: string;
  type: string;
  recipients: string[];
  subject: string;
  status: 'sent' | 'failed' | 'pending';
  error_message?: string;
  created_at: string;
}

interface NotificationPreferences {
  email_enabled: boolean;
  notification_types: {
    leave_requests: boolean;
    task_assignments: boolean;
    memo_updates: boolean;
    project_updates: boolean;
    meeting_reminders: boolean;
    system_alerts: boolean;
    weekly_reports: boolean;
    payment_notifications: boolean;
  } | null;
}

export const EmailNotificationService = () => {
  const [emailHistory, setEmailHistory] = useState<EmailNotification[]>([]);
  const [preferences, setPreferences] = useState<NotificationPreferences>({
    email_enabled: true,
    notification_types: {
      leave_requests: true,
      task_assignments: true,
      memo_updates: true,
      project_updates: true,
      meeting_reminders: true,
      system_alerts: true,
      weekly_reports: true,
      payment_notifications: true,
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [testEmailSending, setTestEmailSending] = useState(false);
  const { toast } = useToast();
  const { userProfile } = useAuth();

  useEffect(() => {
    if (userProfile?.id) {
      loadEmailHistory();
      loadNotificationPreferences();
    }
  }, [userProfile?.id]);

  const loadEmailHistory = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('email_notifications')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;
      setEmailHistory(data || []);
    } catch (error) {
      console.error('Error loading email history:', error);
      toast({
        title: "Error",
        description: "Failed to load email history",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadNotificationPreferences = async () => {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userProfile?.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      
      if (data) {
        setPreferences({
          email_enabled: data.email_enabled,
          notification_types: data.notification_types || {
            leave_requests: true,
            task_assignments: true,
            memo_updates: true,
            project_updates: true,
            meeting_reminders: true,
            system_alerts: true,
            weekly_reports: true,
            payment_notifications: true,
          }
        });
      }
    } catch (error) {
      console.error('Error loading notification preferences:', error);
    }
  };

  const updateNotificationPreferences = async (newPreferences: NotificationPreferences) => {
    try {
      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userProfile?.id,
          email_enabled: newPreferences.email_enabled,
          notification_types: newPreferences.notification_types
        });

      if (error) throw error;

      setPreferences(newPreferences);
      toast({
        title: "Settings Updated",
        description: "Your notification preferences have been saved.",
      });
    } catch (error) {
      console.error('Error updating preferences:', error);
      toast({
        title: "Error",
        description: "Failed to update notification preferences",
        variant: "destructive",
      });
    }
  };

  const sendTestEmail = async () => {
    if (!userProfile?.email) {
      toast({
        title: "Error",
        description: "No email address found for your profile",
        variant: "destructive",
      });
      return;
    }

    try {
      setTestEmailSending(true);
      
      const response = await supabase.functions.invoke('send-notification', {
        body: {
          type: 'test_notification',
          recipients: [userProfile.email],
          data: {
            userName: userProfile.full_name || 'User',
            testMessage: 'This is a test email from WorkBoard AI to verify your email notification settings are working correctly.'
          }
        }
      });

      if (response.error) throw response.error;

      toast({
        title: "Test Email Sent",
        description: `A test email has been sent to ${userProfile.email}`,
      });

      // Refresh email history
      setTimeout(() => {
        loadEmailHistory();
      }, 2000);

    } catch (error) {
      console.error('Error sending test email:', error);
      toast({
        title: "Error",
        description: "Failed to send test email",
        variant: "destructive",
      });
    } finally {
      setTestEmailSending(false);
    }
  };

  const handlePreferenceToggle = (key: keyof NonNullable<NotificationPreferences['notification_types']>) => {
    if (!preferences.notification_types) return;

    const newPreferences = {
      ...preferences,
      notification_types: {
        ...preferences.notification_types,
        [key]: !preferences.notification_types[key]
      }
    };
    updateNotificationPreferences(newPreferences);
  };

  const handleEmailEnabledToggle = () => {
    const newPreferences = {
      ...preferences,
      email_enabled: !preferences.email_enabled
    };
    updateNotificationPreferences(newPreferences);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <X className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Mail className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-500/20 text-green-500';
      case 'failed':
        return 'bg-red-500/20 text-red-500';
      case 'pending':
        return 'bg-yellow-500/20 text-yellow-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const getNotificationTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      leave_requests: 'Leave Requests',
      task_assignments: 'Task Assignments', 
      memo_updates: 'Memo Updates',
      project_updates: 'Project Updates',
      meeting_reminders: 'Meeting Reminders',
      system_alerts: 'System Alerts',
      weekly_reports: 'Weekly Reports',
      payment_notifications: 'Payment Notifications'
    };
    return labels[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const sentEmails = emailHistory.filter(e => e.status === 'sent').length;
  const failedEmails = emailHistory.filter(e => e.status === 'failed').length;
  const pendingEmails = emailHistory.filter(e => e.status === 'pending').length;

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Mail className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Emails</p>
              <p className="text-2xl font-bold text-blue-600">{emailHistory.length}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <Check className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Sent Successfully</p>
              <p className="text-2xl font-bold text-green-600">{sentEmails}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <X className="h-8 w-8 text-red-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Failed</p>
              <p className="text-2xl font-bold text-red-600">{failedEmails}</p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="flex items-center p-6">
            <AlertCircle className="h-8 w-8 text-yellow-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{pendingEmails}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="preferences" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="history">Email History</TabsTrigger>
          <TabsTrigger value="test">Test Email</TabsTrigger>
        </TabsList>

        <TabsContent value="preferences" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Email Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Master Email Toggle */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <h4 className="font-medium">Email Notifications</h4>
                  <p className="text-sm text-muted-foreground">
                    Enable or disable all email notifications
                  </p>
                </div>
                <Switch
                  checked={preferences.email_enabled}
                  onCheckedChange={handleEmailEnabledToggle}
                />
              </div>

              {/* Individual Notification Types */}
              <div className="space-y-4">
                <h4 className="font-medium">Notification Types</h4>
                {preferences.notification_types && Object.entries(preferences.notification_types).map(([key, enabled]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h5 className="text-sm font-medium">{getNotificationTypeLabel(key)}</h5>
                      <p className="text-xs text-muted-foreground">
                        Receive email notifications for {getNotificationTypeLabel(key).toLowerCase()}
                      </p>
                    </div>
                    <Switch
                      checked={enabled && preferences.email_enabled}
                      onCheckedChange={() => handlePreferenceToggle(key as keyof NonNullable<NotificationPreferences['notification_types']>)}
                      disabled={!preferences.email_enabled}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email History
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadEmailHistory}
                  disabled={isLoading}
                >
                  Refresh
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {emailHistory.map((email) => (
                    <div key={email.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                      <div className="flex-shrink-0">
                        {getStatusIcon(email.status)}
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">{email.subject}</h4>
                          <Badge className={getStatusColor(email.status)}>
                            {email.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          To: {email.recipients.join(', ')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          Type: {getNotificationTypeLabel(email.type)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(email.created_at).toLocaleString()}
                        </p>
                        {email.error_message && (
                          <p className="text-xs text-red-500">
                            Error: {email.error_message}
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {emailHistory.length === 0 && !isLoading && (
                    <div className="text-center py-8 text-muted-foreground">
                      No email notifications found
                    </div>
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5" />
                Test Email Notifications
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Send a test email to verify your email notification settings are working correctly.
              </p>
              
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm">
                  <strong>Test email will be sent to:</strong> {userProfile?.email}
                </p>
              </div>
              
              <Button 
                onClick={sendTestEmail}
                disabled={testEmailSending || !preferences.email_enabled || !userProfile?.email}
                className="w-full"
              >
                {testEmailSending ? (
                  <>
                    <Send className="h-4 w-4 mr-2 animate-pulse" />
                    Sending Test Email...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Send Test Email
                  </>
                )}
              </Button>
              
              {!preferences.email_enabled && (
                <p className="text-sm text-muted-foreground text-center">
                  Email notifications are disabled. Enable them in the Preferences tab first.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
