# Security Headers for CTNL AI Work-Board
# These headers enhance security and protect against common attacks

/*
  # Content Security Policy (CSP)
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net; img-src 'self' data: https: blob:; media-src 'self' data: https: blob:; connect-src 'self' https://*.supabase.co https://api.openai.com https://api.anthropic.com https://api.resend.com wss://*.supabase.co; frame-src 'self' https://www.youtube.com https://player.vimeo.com; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;

  # Prevent clickjacking attacks
  X-Frame-Options: DENY

  # Prevent MIME type sniffing
  X-Content-Type-Options: nosniff

  # Enable XSS protection
  X-XSS-Protection: 1; mode=block

  # Referrer Policy
  Referrer-Policy: strict-origin-when-cross-origin

  # Permissions Policy (Feature Policy)
  Permissions-Policy: camera=(), microphone=(), geolocation=(self), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()

  # Strict Transport Security (HSTS)
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload

  # Prevent caching of sensitive content
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

  # Additional security headers
  X-Permitted-Cross-Domain-Policies: none
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: same-origin

# API routes - more permissive for functionality
/api/*
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://*.supabase.co https://api.openai.com https://api.anthropic.com https://api.resend.com wss://*.supabase.co; img-src 'self' data: https:;
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block

# Static assets - allow caching
/assets/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

# Images - allow caching
/images/*
  Cache-Control: public, max-age=86400
  X-Content-Type-Options: nosniff

# Fonts - allow caching
/fonts/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

# Service Worker Prevention
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  X-Content-Type-Options: nosniff

/service-worker.js
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  X-Content-Type-Options: nosniff

/workbox-*.js
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  X-Content-Type-Options: nosniff

/pwa-sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  X-Content-Type-Options: nosniff

# Manifest (removed to prevent PWA installation)
/manifest.json
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0
  X-Content-Type-Options: nosniff
