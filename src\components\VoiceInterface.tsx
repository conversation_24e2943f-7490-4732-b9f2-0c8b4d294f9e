import React, { useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, MicOff, Volume2, VolumeX, Settings, MessageCircle, Loader2, Play, Pause, Square } from 'lucide-react';
import { VoiceRecognitionService } from '@/services/voice-recognition-service';
import { AIVoiceAgentService } from '@/services/ai-voice-agent-service';
import { VoiceResponseService } from '@/services/voice-response-service';
import { VoiceCommandProcessor } from '@/services/voice-command-processor';

interface VoiceInterfaceProps {
  className?: string;
  onNavigate?: (route: string) => void;
  onAction?: (action: any) => void;
  compact?: boolean;
}

interface VoiceMessage {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  confidence?: number;
  actions?: any[];
}

export const VoiceInterface: React.FC<VoiceInterfaceProps> = ({
  className = '',
  onNavigate,
  onAction,
  compact = false
}) => {
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [messages, setMessages] = useState<VoiceMessage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showConversation, setShowConversation] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [waveformData, setWaveformData] = useState<number[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const waveformRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  // Initialize voice services
  useEffect(() => {
    initializeVoiceServices();
    return () => {
      cleanup();
    };
  }, []);

  // Auto-scroll messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Animate waveform
  useEffect(() => {
    if (isListening) {
      animateWaveform();
    } else {
      cancelAnimationFrame(animationRef.current!);
    }
  }, [isListening, waveformData]);

  const initializeVoiceServices = async () => {
    try {
      setError(null);
      
      // Initialize all voice services
      const [recognitionInit, agentInit, responseInit] = await Promise.all([
        VoiceRecognitionService.initialize(),
        AIVoiceAgentService.initialize(),
        VoiceResponseService.initialize()
      ]);

      if (recognitionInit && agentInit && responseInit) {
        setIsInitialized(true);
        console.log('✅ Voice interface initialized successfully');
      } else {
        throw new Error('Failed to initialize voice services');
      }
    } catch (error) {
      console.error('Voice initialization error:', error);
      setError(error.toString());
    }
  };

  const startListening = async () => {
    if (!isInitialized) {
      await initializeVoiceServices();
    }

    try {
      setError(null);
      const success = await VoiceRecognitionService.startListening({
        onResult: handleVoiceResult,
        onStart: () => {
          setIsListening(true);
          setCurrentTranscript('');
        },
        onEnd: () => {
          setIsListening(false);
        },
        onError: (error) => {
          setError(error);
          setIsListening(false);
        },
        onSpeechStart: () => {
          generateWaveform();
        },
        onSpeechEnd: () => {
          setWaveformData([]);
        }
      });

      if (!success) {
        setError('Failed to start voice recognition');
      }
    } catch (error) {
      setError(error.toString());
      setIsListening(false);
    }
  };

  const stopListening = async () => {
    await VoiceRecognitionService.stopListening();
    setIsListening(false);
    setCurrentTranscript('');
    setWaveformData([]);
  };

  const handleVoiceResult = async (result: any) => {
    setCurrentTranscript(result.transcript);

    if (result.isFinal) {
      // Add user message
      const userMessage: VoiceMessage = {
        id: crypto.randomUUID(),
        type: 'user',
        content: result.transcript,
        timestamp: new Date(),
        confidence: result.confidence
      };

      setMessages(prev => [...prev, userMessage]);
      setCurrentTranscript('');
      setIsProcessing(true);

      try {
        // Process with voice agent
        const agentResponse = await AIVoiceAgentService.processVoiceInput(
          result.transcript,
          { source: 'voice_interface' }
        );

        // Add agent response message
        const agentMessage: VoiceMessage = {
          id: crypto.randomUUID(),
          type: 'agent',
          content: agentResponse.text,
          timestamp: new Date(),
          confidence: agentResponse.confidence,
          actions: agentResponse.actions
        };

        setMessages(prev => [...prev, agentMessage]);

        // Execute actions
        if (agentResponse.actions) {
          for (const action of agentResponse.actions) {
            await executeAction(action);
          }
        }

        // Speak response if voice is enabled
        if (voiceEnabled) {
          await speakResponse(agentResponse.text);
        }

      } catch (error) {
        console.error('Error processing voice input:', error);
        setError('Failed to process voice command');
      } finally {
        setIsProcessing(false);
      }
    }
  };

  const speakResponse = async (text: string) => {
    try {
      setIsSpeaking(true);
      await VoiceResponseService.speakResponse({
        text,
        onStart: () => setIsSpeaking(true),
        onEnd: () => setIsSpeaking(false),
        onError: (error) => {
          console.error('Speech error:', error);
          setIsSpeaking(false);
        }
      });
    } catch (error) {
      console.error('Error speaking response:', error);
      setIsSpeaking(false);
    }
  };

  const executeAction = async (action: any) => {
    try {
      switch (action.type) {
        case 'navigate':
          onNavigate?.(action.target);
          break;
        case 'execute':
        case 'display':
        case 'help':
          onAction?.(action);
          break;
        default:
          console.log('Unknown action type:', action.type);
      }
    } catch (error) {
      console.error('Error executing action:', error);
    }
  };

  const generateWaveform = () => {
    // Simulate audio waveform data
    const data = Array.from({ length: 20 }, () => Math.random() * 100);
    setWaveformData(data);
  };

  const animateWaveform = () => {
    if (!waveformRef.current) return;

    const canvas = waveformRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;

    ctx.clearRect(0, 0, width, height);
    ctx.fillStyle = '#3b82f6';

    const barWidth = width / waveformData.length;
    
    waveformData.forEach((value, index) => {
      const barHeight = (value / 100) * height * 0.8;
      const x = index * barWidth;
      const y = (height - barHeight) / 2;
      
      ctx.fillRect(x, y, barWidth - 2, barHeight);
    });

    // Update waveform data for animation
    if (isListening) {
      setWaveformData(prev => 
        prev.map(() => Math.random() * 100)
      );
    }

    animationRef.current = requestAnimationFrame(animateWaveform);
  };

  const toggleVoice = () => {
    setVoiceEnabled(!voiceEnabled);
    if (isSpeaking) {
      VoiceResponseService.stop();
      setIsSpeaking(false);
    }
  };

  const stopSpeaking = () => {
    VoiceResponseService.stop();
    setIsSpeaking(false);
  };

  const cleanup = () => {
    VoiceRecognitionService.cleanup();
    VoiceResponseService.cleanup();
    cancelAnimationFrame(animationRef.current!);
  };

  if (compact) {
    return (
      <div className={`voice-interface-compact ${className}`}>
        <div className="flex items-center gap-2">
          <button
            onClick={isListening ? stopListening : startListening}
            disabled={!isInitialized}
            className={`p-2 rounded-full transition-all duration-200 ${
              isListening 
                ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse' 
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            } ${!isInitialized ? 'opacity-50 cursor-not-allowed' : ''}`}
            title={isListening ? 'Stop listening' : 'Start voice command'}
          >
            {isListening ? <MicOff size={20} /> : <Mic size={20} />}
          </button>

          {isProcessing && (
            <Loader2 className="animate-spin text-blue-500" size={16} />
          )}

          {currentTranscript && (
            <span className="text-sm text-gray-600 max-w-xs truncate">
              {currentTranscript}
            </span>
          )}

          {error && (
            <span className="text-sm text-red-500 max-w-xs truncate">
              {error}
            </span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`voice-interface ${className}`}>
      {/* Voice Interface Header */}
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <MessageCircle size={24} />
              </div>
              <div>
                <h3 className="font-semibold">AI Voice Assistant</h3>
                <p className="text-sm opacity-90">
                  {isInitialized ? 'Ready to help' : 'Initializing...'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={toggleVoice}
                className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                title={voiceEnabled ? 'Disable voice' : 'Enable voice'}
              >
                {voiceEnabled ? <Volume2 size={20} /> : <VolumeX size={20} />}
              </button>
              
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
                title="Voice settings"
              >
                <Settings size={20} />
              </button>
            </div>
          </div>
        </div>

        {/* Voice Controls */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-center gap-4">
            {/* Main Voice Button */}
            <button
              onClick={isListening ? stopListening : startListening}
              disabled={!isInitialized}
              className={`relative p-6 rounded-full transition-all duration-300 ${
                isListening 
                  ? 'bg-red-500 hover:bg-red-600 text-white scale-110 shadow-lg' 
                  : 'bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg'
              } ${!isInitialized ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isListening ? <MicOff size={32} /> : <Mic size={32} />}
              
              {isListening && (
                <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-ping"></div>
              )}
            </button>

            {/* Speaking Controls */}
            {isSpeaking && (
              <div className="flex items-center gap-2">
                <button
                  onClick={stopSpeaking}
                  className="p-3 bg-gray-500 hover:bg-gray-600 text-white rounded-full transition-colors"
                  title="Stop speaking"
                >
                  <Square size={20} />
                </button>
              </div>
            )}

            {/* Processing Indicator */}
            {isProcessing && (
              <div className="flex items-center gap-2 text-blue-500">
                <Loader2 className="animate-spin" size={24} />
                <span className="text-sm">Processing...</span>
              </div>
            )}
          </div>

          {/* Waveform Visualization */}
          {isListening && (
            <div className="mt-4 flex justify-center">
              <canvas
                ref={waveformRef}
                width={200}
                height={40}
                className="border border-gray-300 rounded"
              />
            </div>
          )}

          {/* Current Transcript */}
          {currentTranscript && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Listening:</strong> {currentTranscript}
              </p>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>Error:</strong> {error}
              </p>
            </div>
          )}
        </div>

        {/* Conversation Toggle */}
        <div className="p-4">
          <button
            onClick={() => setShowConversation(!showConversation)}
            className="w-full flex items-center justify-center gap-2 p-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <MessageCircle size={16} />
            <span className="text-sm">
              {showConversation ? 'Hide' : 'Show'} Conversation ({messages.length})
            </span>
          </button>
        </div>

        {/* Conversation History */}
        {showConversation && (
          <div className="border-t border-gray-200 bg-gray-50 max-h-96 overflow-y-auto">
            <div className="p-4 space-y-3">
              {messages.length === 0 ? (
                <p className="text-center text-gray-500 text-sm">
                  No conversation yet. Try saying "Hello" or "Help me navigate"
                </p>
              ) : (
                messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg text-sm ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-white text-gray-800 border border-gray-200'
                      }`}
                    >
                      <p>{message.content}</p>
                      {message.confidence && (
                        <p className="text-xs opacity-75 mt-1">
                          Confidence: {(message.confidence * 100).toFixed(0)}%
                        </p>
                      )}
                      <p className="text-xs opacity-75 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))
              )}
              <div ref={messagesEndRef} />
            </div>
          </div>
        )}

        {/* Quick Commands */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <p className="text-xs text-gray-600 mb-2">Try saying:</p>
          <div className="flex flex-wrap gap-2">
            {[
              "Go to dashboard",
              "Show my tasks", 
              "Create a project",
              "Help me navigate",
              "What can you do?"
            ].map((command) => (
              <button
                key={command}
                onClick={() => handleVoiceResult({ transcript: command, isFinal: true, confidence: 1.0 })}
                className="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-100 transition-colors"
              >
                "{command}"
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VoiceInterface;
