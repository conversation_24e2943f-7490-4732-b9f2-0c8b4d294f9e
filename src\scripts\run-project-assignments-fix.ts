import { supabase } from '../integrations/supabase/client'
import { readFileSync } from 'fs'
import { join } from 'path'

async function runProjectAssignmentsFix () {
  try {
    console.log('🔧 Starting project_assignments schema fix...')

    // Read the SQL file
    const sqlPath = join(__dirname, 'fix-project-assignments-schema.sql')
    const sqlContent = readFileSync(sqlPath, 'utf8')

    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent })

    if (error) {
      console.error('❌ Error executing schema fix:', error)

      // Try alternative approach - execute parts manually
      console.log('🔄 Trying manual schema updates...')

      // Check current schema
      const { data: columns } = await supabase
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'project_assignments')
        .eq('table_schema', 'public')

      console.log('📋 Current columns:', columns?.map(c => c.column_name))

      // Try to add project_id column if it doesn't exist
      try {
        await supabase.rpc('exec_sql', {
          sql: `
            ALTER TABLE public.project_assignments 
            ADD COLUMN IF NOT EXISTS project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE;
          `
        })
        console.log('✅ Added project_id column')
      } catch (err) {
        console.log('ℹ️ project_id column may already exist')
      }

      // Try to add other missing columns
      try {
        await supabase.rpc('exec_sql', {
          sql: `
            ALTER TABLE public.project_assignments 
            ADD COLUMN IF NOT EXISTS assigned_by UUID REFERENCES public.profiles(id),
            ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'team_member',
            ADD COLUMN IF NOT EXISTS hours_allocated INTEGER DEFAULT 40,
            ADD COLUMN IF NOT EXISTS hours_worked INTEGER DEFAULT 0,
            ADD COLUMN IF NOT EXISTS last_progress_update TIMESTAMP WITH TIME ZONE,
            ADD COLUMN IF NOT EXISTS notes TEXT;
          `
        })
        console.log('✅ Added additional columns')
      } catch (err) {
        console.log('ℹ️ Additional columns may already exist')
      }

      // Make project_name nullable
      try {
        await supabase.rpc('exec_sql', {
          sql: 'ALTER TABLE public.project_assignments ALTER COLUMN project_name DROP NOT NULL;'
        })
        console.log('✅ Made project_name nullable')
      } catch (err) {
        console.log('ℹ️ project_name may already be nullable')
      }
    } else {
      console.log('✅ Schema fix executed successfully')
      console.log('📊 Result:', data)
    }

    // Test the schema by trying a simple query
    console.log('🧪 Testing schema...')
    const { data: testData, error: testError } = await supabase
      .from('project_assignments')
      .select('id, project_id, project_name, assigned_to, status')
      .limit(1)

    if (testError) {
      console.error('❌ Schema test failed:', testError)
    } else {
      console.log('✅ Schema test passed')
      if (testData && testData.length > 0) {
        console.log('📋 Sample record structure:', Object.keys(testData[0]))
      }
    }
  } catch (error) {
    console.error('❌ Failed to run schema fix:', error)
  }
}

// Run if called directly
if (require.main === module) {
  runProjectAssignmentsFix().then(() => {
    console.log('🏁 Schema fix completed')
    process.exit(0)
  }).catch((error) => {
    console.error('💥 Schema fix failed:', error)
    process.exit(1)
  })
}

export { runProjectAssignmentsFix }
