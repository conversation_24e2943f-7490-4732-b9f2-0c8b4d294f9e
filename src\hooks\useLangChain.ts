/**
 * React Hook for LangChain Integration
 * Provides easy access to LangChain functionality from React components
 */

import { useState, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';

export interface LangChainMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  metadata?: {
    processingTime?: number;
    model?: string;
    confidence?: number;
    sources?: any[];
    enhanced?: boolean;
  };
}

export interface LangChainOptions {
  useRAG?: boolean;
  useMemory?: boolean;
  useAgent?: boolean;
  interface?: 'standard' | 'hacker_terminal' | 'futuristic' | 'enhanced';
  temperature?: number;
  maxTokens?: number;
}

export interface StreamingResponse {
  content: string;
  isComplete: boolean;
  metadata?: any;
}

export function useLangChain() {
  const [messages, setMessages] = useState<LangChainMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const sessionId = useRef(`session_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`);

  /**
   * Send a message to LangChain AI
   */
  const sendMessage = useCallback(async (
    content: string,
    options: LangChainOptions = {}
  ): Promise<LangChainMessage | null> => {
    if (!content.trim() || isLoading) return null;

    const userMessage: LangChainMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Try LangChain Edge Function first
      const { data, error } = await supabase.functions.invoke('langchain-ai-assistant', {
        body: {
          message: content.trim(),
          userId: userProfile?.id,
          sessionId: sessionId.current,
          context: {
            interface: options.interface || 'standard',
            role: userProfile?.role,
            department: userProfile?.department,
            previousMessages: messages.slice(-5),
          },
          options: {
            useRAG: options.useRAG,
            useMemory: options.useMemory,
            useAgent: options.useAgent,
            temperature: options.temperature || 0.7,
            maxTokens: options.maxTokens || 4096,
          },
        },
      });

      if (error) {
        console.warn('LangChain Edge Function error, trying fallback:', error);
        throw error;
      }

      const assistantMessage: LangChainMessage = {
        id: `assistant_${Date.now()}`,
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
        metadata: data.metadata,
      };

      setMessages(prev => [...prev, assistantMessage]);
      return assistantMessage;

    } catch (error: any) {
      console.error('LangChain message error:', error);

      // Try fallback to simple AI service
      try {
        console.log('🔄 Attempting fallback to simple AI service...');
        const { aiService } = await import('@/lib/ai-service');

        const fallbackResponse = await aiService.processRequest({
          message: content.trim(),
          userId: userProfile?.id,
          userRole: userProfile?.role,
          interface: options.interface || 'standard'
        });

        const fallbackMessage: LangChainMessage = {
          id: `fallback_${Date.now()}`,
          role: 'assistant',
          content: fallbackResponse.response,
          timestamp: new Date(),
          metadata: {
            fallback: true,
            enhanced: fallbackResponse.enhanced,
            source: 'ai-service'
          },
        };

        setMessages(prev => [...prev, fallbackMessage]);

        toast({
          title: "Using Fallback AI",
          description: "LangChain unavailable, using backup AI service",
          variant: "default",
        });

        return fallbackMessage;

      } catch (fallbackError) {
        console.error('Fallback AI service also failed:', fallbackError);

        const errorMessage: LangChainMessage = {
          id: `error_${Date.now()}`,
          role: 'assistant',
          content: 'I apologize, but I encountered an error processing your request. Please try again or check your connection.',
          timestamp: new Date(),
          metadata: { error: true, originalError: error.message },
        };

        setMessages(prev => [...prev, errorMessage]);

        toast({
          title: "AI Assistant Error",
          description: error.message || "Failed to get response from AI assistant",
          variant: "destructive",
        });

        return null;
      }
    } finally {
      setIsLoading(false);
    }
  }, [messages, isLoading, userProfile, toast]);

  /**
   * Stream a message response
   */
  const streamMessage = useCallback(async (
    content: string,
    options: LangChainOptions = {},
    onChunk?: (chunk: StreamingResponse) => void
  ): Promise<void> => {
    if (!content.trim() || isStreaming) return;

    const userMessage: LangChainMessage = {
      id: `user_${Date.now()}`,
      role: 'user',
      content: content.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsStreaming(true);

    const assistantMessageId = `assistant_${Date.now()}`;
    let accumulatedContent = '';

    // Add placeholder message
    const placeholderMessage: LangChainMessage = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
    };
    setMessages(prev => [...prev, placeholderMessage]);

    try {
      // Note: Streaming would require a different implementation
      // For now, we'll simulate streaming by calling the regular function
      const response = await sendMessage(content, options);
      
      if (response) {
        // Simulate streaming by updating content gradually
        const words = response.content.split(' ');
        for (let i = 0; i < words.length; i++) {
          accumulatedContent += (i > 0 ? ' ' : '') + words[i];
          
          setMessages(prev => prev.map(msg => 
            msg.id === assistantMessageId 
              ? { ...msg, content: accumulatedContent }
              : msg
          ));

          onChunk?.({
            content: accumulatedContent,
            isComplete: i === words.length - 1,
            metadata: response.metadata,
          });

          // Small delay to simulate streaming
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
    } finally {
      setIsStreaming(false);
    }
  }, [isStreaming, sendMessage]);

  /**
   * Process document with LangChain
   */
  const processDocument = useCallback(async (
    content: string,
    metadata: any = {}
  ): Promise<any> => {
    try {
      const { data, error } = await supabase.functions.invoke('langchain-document-processor', {
        body: {
          content,
          metadata: {
            ...metadata,
            uploadedBy: userProfile?.id,
            uploadedAt: new Date().toISOString(),
          },
          options: {
            addToVectorStore: true,
            generateSummary: true,
          },
          userId: userProfile?.id,
        },
      });

      if (error) throw error;

      toast({
        title: "Document Processed",
        description: `Document processed successfully with ${data.metadata.totalChunks} chunks`,
      });

      return data;
    } catch (error: any) {
      console.error('Document processing error:', error);
      toast({
        title: "Document Processing Error",
        description: error.message || "Failed to process document",
        variant: "destructive",
      });
      throw error;
    }
  }, [userProfile, toast]);

  /**
   * Search knowledge base using RAG
   */
  const searchKnowledge = useCallback(async (
    query: string,
    maxResults: number = 5
  ): Promise<any> => {
    try {
      const { data, error } = await supabase.functions.invoke('langchain-rag', {
        body: {
          question: query,
          userId: userProfile?.id,
          maxResults,
          includeMetadata: true,
        },
      });

      if (error) throw error;
      return data;
    } catch (error: any) {
      console.error('Knowledge search error:', error);
      toast({
        title: "Search Error",
        description: error.message || "Failed to search knowledge base",
        variant: "destructive",
      });
      throw error;
    }
  }, [userProfile, toast]);

  /**
   * Clear conversation history
   */
  const clearMessages = useCallback(() => {
    setMessages([]);
    sessionId.current = `session_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
  }, []);

  /**
   * Get conversation summary
   */
  const getConversationSummary = useCallback(async (): Promise<string> => {
    if (messages.length === 0) return 'No conversation to summarize.';

    try {
      const conversationText = messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      const response = await sendMessage(
        `Please provide a brief summary of our conversation: ${conversationText}`,
        { useMemory: false }
      );

      return response?.content || 'Failed to generate summary.';
    } catch (error) {
      console.error('Summary generation error:', error);
      return 'Failed to generate conversation summary.';
    }
  }, [messages, sendMessage]);

  return {
    // State
    messages,
    isLoading,
    isStreaming,
    sessionId: sessionId.current,

    // Actions
    sendMessage,
    streamMessage,
    processDocument,
    searchKnowledge,
    clearMessages,
    getConversationSummary,

    // Utilities
    messageCount: messages.length,
    lastMessage: messages[messages.length - 1] || null,
  };
}
