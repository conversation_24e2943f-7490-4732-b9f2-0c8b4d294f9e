import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';
import { supabase } from '@/integrations/supabase/client';
import { 
  Shield, 
  Users, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  RefreshCw,
  User,
  Settings
} from 'lucide-react';
import type { UserRole } from '@/types/auth';

interface RoleTestResult {
  role: UserRole;
  canAccess: boolean;
  error?: string;
}

export function RoleTestComponent() {
  const [testResults, setTestResults] = useState<RoleTestResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [profiles, setProfiles] = useState<any[]>([]);
  const { userProfile, isAdmin, is<PERSON>anager, isStaff, isAccountant, isHR, isStaffAdmin } = useAuth();
  const { toast } = useToast();

  const allRoles: UserRole[] = ['admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'];

  const loadProfiles = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role, status, department_id')
        .limit(10);

      if (error) {
        throw error;
      }

      setProfiles(data || []);
    } catch (error) {
      console.error('Error loading profiles:', error);
      toast({
        title: "Error",
        description: "Failed to load profiles",
        variant: "destructive",
      });
    }
  };

  const testRoleAccess = async () => {
    setLoading(true);
    const results: RoleTestResult[] = [];

    for (const role of allRoles) {
      try {
        // Test if we can query profiles with role-based access
        const { data, error } = await supabase
          .from('profiles')
          .select('id, role')
          .eq('role', role)
          .limit(1);

        results.push({
          role,
          canAccess: !error,
          error: error?.message
        });
      } catch (error) {
        results.push({
          role,
          canAccess: false,
          error: error.message
        });
      }
    }

    setTestResults(results);
    setLoading(false);
  };

  const testRoleFunctions = async () => {
    if (!userProfile?.id) return;

    try {
      // Test get_user_role function
      const { data: roleData, error: roleError } = await supabase
        .rpc('get_user_role', { user_id: userProfile.id });

      if (roleError) {
        throw roleError;
      }

      // Test user_has_role function
      const { data: hasRoleData, error: hasRoleError } = await supabase
        .rpc('user_has_role', { 
          user_id: userProfile.id, 
          required_role: userProfile.role 
        });

      if (hasRoleError) {
        throw hasRoleError;
      }

      toast({
        title: "Function Test Success",
        description: `Role: ${roleData}, Has Role: ${hasRoleData}`,
      });
    } catch (error) {
      toast({
        title: "Function Test Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    loadProfiles();
    testRoleAccess();
  }, []);

  const getRoleColor = (role: UserRole) => {
    const colors = {
      admin: 'bg-red-100 text-red-800 border-red-200',
      manager: 'bg-blue-100 text-blue-800 border-blue-200',
      'staff-admin': 'bg-purple-100 text-purple-800 border-purple-200',
      hr: 'bg-green-100 text-green-800 border-green-200',
      accountant: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      staff: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[role] || colors.staff;
  };

  const getRoleIcon = (role: UserRole) => {
    const icons = {
      admin: Shield,
      manager: Users,
      'staff-admin': Settings,
      hr: User,
      accountant: Settings,
      staff: User
    };
    const Icon = icons[role] || User;
    return <Icon className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Role System Test</h2>
          <p className="text-muted-foreground">Test and verify role-based access control</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={testRoleFunctions} variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Test Functions
          </Button>
          <Button onClick={testRoleAccess} disabled={loading} size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Test Access
          </Button>
        </div>
      </div>

      {/* Current User Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Current User
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium">Name</p>
              <p className="text-sm text-muted-foreground">{userProfile?.full_name || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Role</p>
              <Badge className={getRoleColor(userProfile?.role || 'staff')}>
                {getRoleIcon(userProfile?.role || 'staff')}
                <span className="ml-1">{userProfile?.role || 'N/A'}</span>
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Status</p>
              <p className="text-sm text-muted-foreground">{userProfile?.status || 'N/A'}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Permissions</p>
              <div className="flex flex-wrap gap-1">
                {isAdmin && <Badge variant="secondary" className="text-xs">Admin</Badge>}
                {isManager && <Badge variant="secondary" className="text-xs">Manager</Badge>}
                {isStaffAdmin && <Badge variant="secondary" className="text-xs">Staff-Admin</Badge>}
                {isHR && <Badge variant="secondary" className="text-xs">HR</Badge>}
                {isAccountant && <Badge variant="secondary" className="text-xs">Accountant</Badge>}
                {isStaff && <Badge variant="secondary" className="text-xs">Staff</Badge>}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role Access Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Role Access Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {testResults.map((result) => (
              <div
                key={result.role}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-2">
                  {getRoleIcon(result.role)}
                  <span className="font-medium">{result.role}</span>
                </div>
                <div className="flex items-center gap-2">
                  {result.canAccess ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm">
                    {result.canAccess ? 'Accessible' : 'Blocked'}
                  </span>
                </div>
              </div>
            ))}
          </div>
          
          {testResults.some(r => !r.canAccess) && (
            <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <AlertTriangle className="h-4 w-4" />
                <span className="font-medium">Access Issues Detected</span>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                Some roles cannot be accessed. This might be due to RLS policies or missing data.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Profiles List */}
      <Card>
        <CardHeader>
          <CardTitle>Accessible Profiles</CardTitle>
        </CardHeader>
        <CardContent>
          {profiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2" />
              <p>No profiles found or access denied</p>
            </div>
          ) : (
            <div className="space-y-3">
              {profiles.map((profile) => (
                <div
                  key={profile.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div>
                    <p className="font-medium">{profile.full_name || 'Unnamed'}</p>
                    <p className="text-sm text-muted-foreground">{profile.email}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getRoleColor(profile.role)}>
                      {getRoleIcon(profile.role)}
                      <span className="ml-1">{profile.role}</span>
                    </Badge>
                    {profile.status && (
                      <Badge variant="outline" className="text-xs">
                        {profile.status}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
