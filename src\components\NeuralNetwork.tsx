
import { useEffect, useRef } from 'react';

interface Node {
  x: number;
  y: number;
  vx: number;
  vy: number;
  connections: number[];
  energy: number;
  pulsePhase: number;
}

export const NeuralNetwork = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const nodesRef = useRef<Node[]>([]);
  const frameRef = useRef<number>();
  const timeRef = useRef<number>(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const initNodes = () => {
      const nodes: Node[] = [];
      const numNodes = 35; // Increased node count for more complex network
      
      for (let i = 0; i < numNodes; i++) {
        nodes.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.8,
          vy: (Math.random() - 0.5) * 0.8,
          connections: [],
          energy: Math.random(),
          pulsePhase: Math.random() * Math.PI * 2
        });
      }

      // Create more complex connection patterns
      nodes.forEach((node, i) => {
        const numConnections = Math.floor(Math.random() * 5) + 2;
        for (let j = 0; j < numConnections; j++) {
          const target = Math.floor(Math.random() * numNodes);
          if (target !== i && !node.connections.includes(target)) {
            node.connections.push(target);
          }
        }
      });

      return nodes;
    };

    const drawNode = (node: Node, time: number) => {
      const pulseIntensity = 0.5 + 0.5 * Math.sin(time * 0.003 + node.pulsePhase);
      const nodeSize = 6 + pulseIntensity * 8;
      
      // Outer glow
      const gradient = ctx.createRadialGradient(node.x, node.y, 0, node.x, node.y, nodeSize * 2);
      gradient.addColorStop(0, `rgba(56, 189, 248, ${pulseIntensity * 0.6})`); // cyan-400
      gradient.addColorStop(0.5, `rgba(34, 197, 94, ${pulseIntensity * 0.4})`); // green-600
              gradient.addColorStop(1, 'rgba(34, 197, 94, 0)');
      
      ctx.beginPath();
      ctx.arc(node.x, node.y, nodeSize * 2, 0, Math.PI * 2);
      ctx.fillStyle = gradient;
      ctx.fill();

      // Core node
      ctx.beginPath();
      ctx.arc(node.x, node.y, nodeSize, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(56, 189, 248, ${0.8 + pulseIntensity * 0.2})`; // cyan-400
      ctx.fill();

      // Inner core
      ctx.beginPath();
      ctx.arc(node.x, node.y, nodeSize * 0.4, 0, Math.PI * 2);
      ctx.fillStyle = `rgba(255, 255, 255, ${pulseIntensity})`;
      ctx.fill();
    };

    const drawConnection = (
      x1: number, y1: number, 
      x2: number, y2: number, 
      alpha: number,
      energy: number,
      time: number
    ) => {
      const distance = Math.hypot(x2 - x1, y2 - y1);
      const energyFlow = 0.5 + 0.5 * Math.sin(time * 0.005 + distance * 0.01);
      
      // Main connection line
      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      
      const lineAlpha = alpha * (0.3 + energyFlow * 0.7);
      ctx.strokeStyle = `rgba(56, 189, 248, ${lineAlpha})`; // cyan-400
      ctx.lineWidth = 1 + energyFlow * 2;
      ctx.stroke();

      // Energy pulse
      if (energyFlow > 0.7) {
        const pulsePos = (time * 0.01) % 1;
        const pulseX = x1 + (x2 - x1) * pulsePos;
        const pulseY = y1 + (y2 - y1) * pulsePos;
        
        ctx.beginPath();
        ctx.arc(pulseX, pulseY, 3, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(34, 197, 94, ${energyFlow})`; // green-600
        ctx.fill();
      }

      // Data packets
      if (Math.random() < 0.01) {
        const packetPos = Math.random();
        const packetX = x1 + (x2 - x1) * packetPos;
        const packetY = y1 + (y2 - y1) * packetPos;
        
        ctx.beginPath();
        ctx.arc(packetX, packetY, 2, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(34, 197, 94, 0.8)'; // green-500
        ctx.fill();
      }
    };

    const animate = (time: number) => {
      timeRef.current = time;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      nodesRef.current.forEach((node, i) => {
        // Static positions - no movement
        // Update energy with slower animation
        node.energy = 0.5 + 0.3 * Math.sin(time * 0.0005 + i);

        // Draw connections with reduced effects
        node.connections.forEach(targetIndex => {
          const target = nodesRef.current[targetIndex];
          if (!target) return;

          const distance = Math.hypot(target.x - node.x, target.y - node.y);
          const maxDistance = 300;

          if (distance < maxDistance) {
            const alpha = Math.max(0, 0.3 - distance / maxDistance);
            const combinedEnergy = (node.energy + target.energy) / 2;

            drawConnection(
              node.x, node.y,
              target.x, target.y,
              alpha * 0.5,
              combinedEnergy,
              time
            );
          }
        });

        // Draw node with reduced effects
        drawNode(node, time);
      });

      frameRef.current = requestAnimationFrame(animate);
    };

    resizeCanvas();
    nodesRef.current = initNodes();
    frameRef.current = requestAnimationFrame(animate);

    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (frameRef.current) {
        cancelAnimationFrame(frameRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute inset-0 w-full h-full"
      style={{ opacity: 0.6 }}
    />
  );
};
