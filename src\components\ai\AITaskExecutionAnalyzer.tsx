import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';
import { 
  Brain, 
  Mic, 
  Eye, 
  Zap, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Activity,
  Database,
  Settings,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react';
import { aiService } from '@/lib/ai-service';
import { voiceCommandSystem } from '@/lib/voice-command-system';
import { dbSchemaAnalyzer } from '@/scripts/database-schema-analyzer';

interface AITaskTest {
  id: string;
  name: string;
  description: string;
  category: 'voice' | 'ai_processing' | 'data_integration' | 'role_access';
  status: 'pending' | 'running' | 'success' | 'error';
  result?: any;
  error?: string;
  duration?: number;
}

export const AITaskExecutionAnalyzer: React.FC = () => {
  const [tests, setTests] = useState<AITaskTest[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const { toast } = useToast();
  const { userProfile } = useAuth();

  useEffect(() => {
    initializeTests();
  }, [userProfile]);

  const initializeTests = () => {
    const testSuite: AITaskTest[] = [
      // Voice Command Tests
      {
        id: 'voice_recognition',
        name: 'Voice Recognition Support',
        description: 'Test if browser supports speech recognition',
        category: 'voice',
        status: 'pending'
      },
      {
        id: 'voice_commands',
        name: 'Voice Command Processing',
        description: 'Test voice command system functionality',
        category: 'voice',
        status: 'pending'
      },
      {
        id: 'voice_navigation',
        name: 'Voice Navigation',
        description: 'Test voice-guided navigation capabilities',
        category: 'voice',
        status: 'pending'
      },

      // AI Processing Tests
      {
        id: 'ai_service_init',
        name: 'AI Service Initialization',
        description: 'Test AI service startup and configuration',
        category: 'ai_processing',
        status: 'pending'
      },
      {
        id: 'langchain_integration',
        name: 'LangChain Integration',
        description: 'Test LangChain service availability',
        category: 'ai_processing',
        status: 'pending'
      },
      {
        id: 'ai_response_generation',
        name: 'AI Response Generation',
        description: 'Test AI response generation with real data',
        category: 'ai_processing',
        status: 'pending'
      },

      // Data Integration Tests
      {
        id: 'data_fetching',
        name: 'Real Data Fetching',
        description: 'Test system data retrieval capabilities',
        category: 'data_integration',
        status: 'pending'
      },
      {
        id: 'context_building',
        name: 'Context Building',
        description: 'Test AI context enhancement with real data',
        category: 'data_integration',
        status: 'pending'
      },
      {
        id: 'data_caching',
        name: 'Data Caching System',
        description: 'Test data caching and refresh mechanisms',
        category: 'data_integration',
        status: 'pending'
      },

      // Role Access Tests
      {
        id: 'role_permissions',
        name: 'Role-Based Permissions',
        description: 'Test role-specific data access',
        category: 'role_access',
        status: 'pending'
      },
      {
        id: 'dashboard_access',
        name: 'Dashboard Access',
        description: 'Test role-specific dashboard functionality',
        category: 'role_access',
        status: 'pending'
      },
      {
        id: 'ai_features_access',
        name: 'AI Features Access',
        description: 'Test role-based AI feature availability',
        category: 'role_access',
        status: 'pending'
      }
    ];

    setTests(testSuite);
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setProgress(0);
    
    const totalTests = tests.length;
    let completedTests = 0;

    for (const test of tests) {
      setTests(prev => prev.map(t => 
        t.id === test.id ? { ...t, status: 'running' } : t
      ));

      try {
        const result = await runSingleTest(test);
        
        setTests(prev => prev.map(t => 
          t.id === test.id ? { 
            ...t, 
            status: result.success ? 'success' : 'error',
            result: result.data,
            error: result.error,
            duration: result.duration
          } : t
        ));
      } catch (error) {
        setTests(prev => prev.map(t => 
          t.id === test.id ? { 
            ...t, 
            status: 'error',
            error: `Test failed: ${error}`
          } : t
        ));
      }

      completedTests++;
      setProgress((completedTests / totalTests) * 100);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
    
    const successCount = tests.filter(t => t.status === 'success').length;
    const errorCount = tests.filter(t => t.status === 'error').length;
    
    toast({
      title: "AI Task Analysis Complete",
      description: `${successCount} passed, ${errorCount} failed`,
      variant: successCount === totalTests ? "default" : "destructive"
    });
  };

  const runSingleTest = async (test: AITaskTest): Promise<{
    success: boolean;
    data?: any;
    error?: string;
    duration: number;
  }> => {
    const startTime = Date.now();

    try {
      switch (test.id) {
        case 'voice_recognition':
          return {
            success: voiceCommandSystem.isSupported(),
            data: { supported: voiceCommandSystem.isSupported() },
            duration: Date.now() - startTime
          };

        case 'voice_commands':
          const commands = voiceCommandSystem.getAvailableCommands();
          return {
            success: commands.length > 0,
            data: { commandCount: commands.length },
            duration: Date.now() - startTime
          };

        case 'voice_navigation':
          voiceCommandSystem.setContext('dashboard', userProfile?.role || 'staff');
          return {
            success: true,
            data: { contextSet: true },
            duration: Date.now() - startTime
          };

        case 'ai_service_init':
          const healthStatus = await aiService.getSystemHealth();
          return {
            success: Array.isArray(healthStatus),
            data: { healthComponents: healthStatus.length },
            duration: Date.now() - startTime
          };

        case 'langchain_integration':
          // Test LangChain availability
          const testResponse = await aiService.processRequest({
            message: 'Test message',
            userId: userProfile?.id,
            userRole: userProfile?.role,
            interface: 'standard'
          });
          return {
            success: !!testResponse.response,
            data: { enhanced: testResponse.enhanced },
            duration: Date.now() - startTime
          };

        case 'ai_response_generation':
          const aiResponse = await aiService.processRequest({
            message: 'Show my tasks',
            userId: userProfile?.id,
            userRole: userProfile?.role,
            interface: 'standard'
          });
          return {
            success: !!aiResponse.response && !!aiResponse.data,
            data: { hasData: !!aiResponse.data },
            duration: Date.now() - startTime
          };

        case 'data_fetching':
          // Clear cache and test fresh data fetch
          aiService.clearCache();
          const dataResponse = await aiService.processRequest({
            message: 'System status',
            userId: userProfile?.id,
            userRole: userProfile?.role
          });
          return {
            success: !!dataResponse.metadata?.dataFetched,
            data: { dataFetched: dataResponse.metadata?.dataFetched },
            duration: Date.now() - startTime
          };

        case 'context_building':
          const contextResponse = await aiService.processRequest({
            message: 'Analyze my performance',
            userId: userProfile?.id,
            userRole: userProfile?.role,
            context: { detailed: true }
          });
          return {
            success: contextResponse.response.length > 50,
            data: { responseLength: contextResponse.response.length },
            duration: Date.now() - startTime
          };

        case 'data_caching':
          // Test cache functionality
          const firstCall = await aiService.processRequest({
            message: 'Test cache',
            userId: userProfile?.id
          });
          const secondCall = await aiService.processRequest({
            message: 'Test cache',
            userId: userProfile?.id
          });
          return {
            success: true,
            data: { cacheTested: true },
            duration: Date.now() - startTime
          };

        case 'role_permissions':
          const roleAccess = await dbSchemaAnalyzer.checkRoleAccess(userProfile?.role || 'staff');
          return {
            success: roleAccess.accessible.length > 0,
            data: { 
              accessible: roleAccess.accessible.length,
              inaccessible: roleAccess.inaccessible.length
            },
            duration: Date.now() - startTime
          };

        case 'dashboard_access':
          // Test dashboard-specific functionality
          return {
            success: !!userProfile?.role,
            data: { role: userProfile?.role },
            duration: Date.now() - startTime
          };

        case 'ai_features_access':
          // Test AI features based on role
          const hasAIAccess = ['admin', 'manager'].includes(userProfile?.role || '');
          return {
            success: true,
            data: { hasAIAccess, role: userProfile?.role },
            duration: Date.now() - startTime
          };

        default:
          throw new Error('Unknown test');
      }
    } catch (error) {
      return {
        success: false,
        error: `${error}`,
        duration: Date.now() - startTime
      };
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      running: 'secondary',
      pending: 'outline'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const filteredTests = selectedCategory === 'all' 
    ? tests 
    : tests.filter(test => test.category === selectedCategory);

  const categoryStats = {
    voice: tests.filter(t => t.category === 'voice'),
    ai_processing: tests.filter(t => t.category === 'ai_processing'),
    data_integration: tests.filter(t => t.category === 'data_integration'),
    role_access: tests.filter(t => t.category === 'role_access')
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-500" />
            AI Task Execution Analyzer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm text-muted-foreground">
                Comprehensive analysis of AI system capabilities and task execution
              </p>
              <p className="text-sm mt-1">
                Role: <span className="font-medium">{userProfile?.role}</span>
              </p>
            </div>
            <Button
              onClick={runAllTests}
              disabled={isRunning}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {isRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Running Tests...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Run All Tests
                </>
              )}
            </Button>
          </div>
          
          {isRunning && (
            <div className="mb-4">
              <Progress value={progress} className="w-full" />
              <p className="text-sm text-muted-foreground mt-2">
                Testing AI capabilities... {Math.round(progress)}%
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Category Tabs */}
      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Tests</TabsTrigger>
          <TabsTrigger value="voice">
            <Mic className="h-4 w-4 mr-2" />
            Voice ({categoryStats.voice.filter(t => t.status === 'success').length}/{categoryStats.voice.length})
          </TabsTrigger>
          <TabsTrigger value="ai_processing">
            <Brain className="h-4 w-4 mr-2" />
            AI ({categoryStats.ai_processing.filter(t => t.status === 'success').length}/{categoryStats.ai_processing.length})
          </TabsTrigger>
          <TabsTrigger value="data_integration">
            <Database className="h-4 w-4 mr-2" />
            Data ({categoryStats.data_integration.filter(t => t.status === 'success').length}/{categoryStats.data_integration.length})
          </TabsTrigger>
          <TabsTrigger value="role_access">
            <Settings className="h-4 w-4 mr-2" />
            Access ({categoryStats.role_access.filter(t => t.status === 'success').length}/{categoryStats.role_access.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value={selectedCategory} className="mt-6">
          <div className="grid gap-4">
            {filteredTests.map((test) => (
              <Card key={test.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(test.status)}
                      <div>
                        <h3 className="font-medium">{test.name}</h3>
                        <p className="text-sm text-muted-foreground">{test.description}</p>
                        {test.result && (
                          <p className="text-xs text-blue-600 mt-1">
                            Result: {JSON.stringify(test.result)}
                          </p>
                        )}
                        {test.error && (
                          <p className="text-xs text-red-600 mt-1">
                            Error: {test.error}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {test.duration && (
                        <span className="text-xs text-muted-foreground">
                          {test.duration}ms
                        </span>
                      )}
                      {getStatusBadge(test.status)}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
