/**
 * JWT Utilities for Token Inspection and Validation
 * Provides client-side JWT decoding, validation, and debugging tools
 */

export interface JWTPayload {
  iss?: string;           // Issuer
  sub?: string;           // Subject (user ID)
  aud?: string | string[]; // Audience
  exp?: number;           // Expiration time
  iat?: number;           // Issued at
  nbf?: number;           // Not before
  jti?: string;           // JWT ID
  email?: string;         // User email
  role?: string;          // User role
  user_metadata?: any;    // Custom user metadata
  app_metadata?: any;     // App metadata
  [key: string]: any;     // Additional claims
}

export interface JWTHeader {
  alg?: string;           // Algorithm
  typ?: string;           // Type
  kid?: string;           // Key ID
  [key: string]: any;     // Additional header fields
}

export interface DecodedJWT {
  header: JWTHeader;
  payload: JWTPayload;
  signature: string;
  raw: {
    header: string;
    payload: string;
    signature: string;
  };
}

export interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  timeToExpiry: number;
  errors: string[];
  warnings: string[];
  payload?: JWTPayload;
}

/**
 * JWT Utilities Class
 */
export class JWTUtils {
  /**
   * Decode a JWT token without verification
   * @param token - The JWT token to decode
   * @returns Decoded JWT or null if invalid
   */
  static decode(token: string): DecodedJWT | null {
    try {
      if (!token || typeof token !== 'string') {
        throw new Error('Invalid token format');
      }

      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('JWT must have 3 parts');
      }

      const [headerB64, payloadB64, signature] = parts;

      // Decode header
      const headerJson = this.base64UrlDecode(headerB64);
      const header: JWTHeader = JSON.parse(headerJson);

      // Decode payload
      const payloadJson = this.base64UrlDecode(payloadB64);
      const payload: JWTPayload = JSON.parse(payloadJson);

      return {
        header,
        payload,
        signature,
        raw: {
          header: headerB64,
          payload: payloadB64,
          signature
        }
      };
    } catch (error) {
      console.error('JWT decode error:', error);
      return null;
    }
  }

  /**
   * Check if a JWT token is expired
   * @param token - The JWT token to check
   * @returns True if expired, false otherwise
   */
  static isExpired(token: string): boolean {
    const decoded = this.decode(token);
    if (!decoded || !decoded.payload.exp) {
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return currentTime >= decoded.payload.exp;
  }

  /**
   * Get time remaining until token expiry
   * @param token - The JWT token to check
   * @returns Time in milliseconds until expiry (0 if expired)
   */
  static getTimeToExpiry(token: string): number {
    const decoded = this.decode(token);
    if (!decoded || !decoded.payload.exp) {
      return 0;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    const timeToExpiry = decoded.payload.exp - currentTime;
    return Math.max(0, timeToExpiry * 1000);
  }

  /**
   * Get human-readable time until expiry
   * @param token - The JWT token to check
   * @returns Formatted time string
   */
  static getFormattedTimeToExpiry(token: string): string {
    const timeMs = this.getTimeToExpiry(token);
    if (timeMs === 0) return 'Expired';

    const minutes = Math.floor(timeMs / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    return `${minutes}m`;
  }

  /**
   * Validate a JWT token structure and claims
   * @param token - The JWT token to validate
   * @returns Validation result with details
   */
  static validate(token: string): TokenValidationResult {
    const result: TokenValidationResult = {
      isValid: false,
      isExpired: false,
      timeToExpiry: 0,
      errors: [],
      warnings: []
    };

    try {
      // Basic format validation
      if (!token || typeof token !== 'string') {
        result.errors.push('Token is required and must be a string');
        return result;
      }

      const parts = token.split('.');
      if (parts.length !== 3) {
        result.errors.push('JWT must have exactly 3 parts (header.payload.signature)');
        return result;
      }

      // Decode token
      const decoded = this.decode(token);
      if (!decoded) {
        result.errors.push('Failed to decode JWT token');
        return result;
      }

      result.payload = decoded.payload;

      // Validate required claims
      if (!decoded.payload.sub) {
        result.errors.push('Missing subject (sub) claim');
      }

      if (!decoded.payload.iat) {
        result.warnings.push('Missing issued at (iat) claim');
      }

      if (!decoded.payload.exp) {
        result.errors.push('Missing expiration (exp) claim');
      } else {
        // Check expiration
        const currentTime = Math.floor(Date.now() / 1000);
        result.isExpired = currentTime >= decoded.payload.exp;
        result.timeToExpiry = this.getTimeToExpiry(token);

        if (result.isExpired) {
          result.errors.push('Token has expired');
        } else if (result.timeToExpiry < 5 * 60 * 1000) { // Less than 5 minutes
          result.warnings.push('Token expires soon (less than 5 minutes)');
        }
      }

      // Validate issuer for Supabase
      if (decoded.payload.iss !== 'supabase') {
        result.warnings.push('Token issuer is not Supabase');
      }

      // Validate audience
      if (!decoded.payload.aud) {
        result.warnings.push('Missing audience (aud) claim');
      }

      // Check if token is not yet valid
      if (decoded.payload.nbf) {
        const currentTime = Math.floor(Date.now() / 1000);
        if (currentTime < decoded.payload.nbf) {
          result.errors.push('Token is not yet valid (nbf claim)');
        }
      }

      // Token is valid if no errors
      result.isValid = result.errors.length === 0;

    } catch (error) {
      result.errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return result;
  }

  /**
   * Extract user information from JWT token
   * @param token - The JWT token
   * @returns User information or null
   */
  static extractUserInfo(token: string): {
    id: string;
    email?: string;
    role?: string;
    fullName?: string;
    metadata?: any;
  } | null {
    const decoded = this.decode(token);
    if (!decoded) return null;

    const { payload } = decoded;
    return {
      id: payload.sub || '',
      email: payload.email,
      role: payload.role || payload.user_metadata?.role,
      fullName: payload.user_metadata?.full_name,
      metadata: payload.user_metadata
    };
  }

  /**
   * Check if token needs refresh (within 5 minutes of expiry)
   * @param token - The JWT token to check
   * @returns True if token should be refreshed
   */
  static shouldRefresh(token: string): boolean {
    const timeToExpiry = this.getTimeToExpiry(token);
    return timeToExpiry > 0 && timeToExpiry < 5 * 60 * 1000; // Less than 5 minutes
  }

  /**
   * Get token age in milliseconds
   * @param token - The JWT token
   * @returns Age in milliseconds or 0 if invalid
   */
  static getTokenAge(token: string): number {
    const decoded = this.decode(token);
    if (!decoded || !decoded.payload.iat) return 0;

    const currentTime = Math.floor(Date.now() / 1000);
    return (currentTime - decoded.payload.iat) * 1000;
  }

  /**
   * Base64 URL decode (JWT uses base64url encoding)
   * @param str - Base64 URL encoded string
   * @returns Decoded string
   */
  private static base64UrlDecode(str: string): string {
    // Add padding if needed
    let padded = str;
    while (padded.length % 4) {
      padded += '=';
    }

    // Replace URL-safe characters
    const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
    
    try {
      return decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
    } catch (error) {
      throw new Error('Invalid base64url encoding');
    }
  }

  /**
   * Debug information for a JWT token
   * @param token - The JWT token
   * @returns Debug information object
   */
  static debug(token: string): {
    isValid: boolean;
    decoded: DecodedJWT | null;
    validation: TokenValidationResult;
    userInfo: any;
    timing: {
      age: string;
      timeToExpiry: string;
      shouldRefresh: boolean;
    };
  } {
    const decoded = this.decode(token);
    const validation = this.validate(token);
    const userInfo = this.extractUserInfo(token);

    return {
      isValid: validation.isValid,
      decoded,
      validation,
      userInfo,
      timing: {
        age: this.formatDuration(this.getTokenAge(token)),
        timeToExpiry: this.getFormattedTimeToExpiry(token),
        shouldRefresh: this.shouldRefresh(token)
      }
    };
  }

  /**
   * Format duration in milliseconds to human readable string
   * @param ms - Duration in milliseconds
   * @returns Formatted duration string
   */
  private static formatDuration(ms: number): string {
    if (ms === 0) return '0ms';

    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
}

// Export convenience functions
export const {
  decode: decodeJWT,
  isExpired: isJWTExpired,
  getTimeToExpiry: getJWTTimeToExpiry,
  validate: validateJWT,
  extractUserInfo: extractJWTUserInfo,
  shouldRefresh: shouldRefreshJWT,
  debug: debugJWT
} = JWTUtils;
