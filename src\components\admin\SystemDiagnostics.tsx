import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Settings, 
  Database, 
  Zap, 
  Brain, 
  Shield, 
  Activity,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Terminal,
  Network
} from 'lucide-react';
import { SystemHealthDashboard } from '@/components/system/SystemHealthDashboard';
import { useSystemCommunication } from '@/hooks/useSystemCommunication';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

export const SystemDiagnostics = () => {
  const [activeTests, setActiveTests] = useState<string[]>([]);
  const { runSystemHealthCheck, testAPIConnectivity, loading } = useSystemCommunication();
  const { toast } = useToast();

  const runTest = async (testName: string, testFunction: () => Promise<any>) => {
    setActiveTests(prev => [...prev, testName]);
    try {
      await testFunction();
      toast({
        title: `${testName} Completed`,
        description: "Test passed successfully",
      });
    } catch (error) {
      toast({
        title: `${testName} Failed`,
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setActiveTests(prev => prev.filter(t => t !== testName));
    }
  };

  const diagnosticTests = [
    {
      name: "Database Connectivity",
      description: "Test Supabase database connection and basic queries",
      icon: <Database className="h-5 w-5" />,
      action: () => runTest("Database Test", testAPIConnectivity),
    },
    {
      name: "Edge Functions",
      description: "Verify all edge functions are responding correctly",
      icon: <Zap className="h-5 w-5" />,
      action: () => runTest("Edge Functions Test", runSystemHealthCheck),
    },
    {
      name: "AI Services",
      description: "Test OpenAI integration and AI assistant functionality",
      icon: <Brain className="h-5 w-5" />,
      action: () => runTest("AI Services Test", async () => {
        try {
          // Test AI services specifically
          const { data, error } = await supabase.functions.invoke('ai-assistant', {
            body: {
              message: 'System diagnostic test',
              userId: 'test',
              context: { role: 'admin', department: 'IT' }
            }
          });

          if (error) {
            // Handle specific error cases
            if (error.message?.includes('OpenAI API key not configured')) {
              throw new Error('OpenAI API key not configured in edge functions environment. Please set OPENAI_API_KEY in supabase/.env.local');
            }
            throw error;
          }

          return data;
        } catch (err) {
          // Provide more helpful error messages
          if (err.message?.includes('500')) {
            throw new Error('Edge function returned 500 error. Check if OpenAI API key is configured and edge functions are running.');
          }
          throw err;
        }
      }),
    },
    {
      name: "Authentication",
      description: "Verify auth system and session management",
      icon: <Shield className="h-5 w-5" />,
      action: () => runTest("Authentication Test", async () => {
        const { data, error } = await supabase.auth.getSession();
        if (error) throw error;
        return data;
      }),
    },
    {
      name: "Real-time Features",
      description: "Test websocket connections and real-time updates",
      icon: <Network className="h-5 w-5" />,
      action: () => runTest("Real-time Test", async () => {
        // Test real-time functionality
        const channel = supabase
          .channel('diagnostic-test')
          .on('postgres_changes', { event: '*', schema: 'public', table: 'profiles' }, () => {})
          .subscribe();
        
        return new Promise((resolve) => {
          setTimeout(() => {
            supabase.removeChannel(channel);
            resolve(true);
          }, 2000);
        });
      }),
    },
    {
      name: "Performance Metrics",
      description: "Measure system performance and response times",
      icon: <Activity className="h-5 w-5" />,
      action: () => runTest("Performance Test", runSystemHealthCheck),
    },
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            System Diagnostics & Health Monitoring
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">
            Comprehensive system health monitoring and diagnostic tools for the CTNL AI Workflow Platform.
            Monitor all system components, API connectivity, and performance metrics in real-time.
          </p>
          
          <Tabs defaultValue="health" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="health">System Health</TabsTrigger>
              <TabsTrigger value="diagnostics">Diagnostic Tests</TabsTrigger>
              <TabsTrigger value="monitoring">Real-time Monitoring</TabsTrigger>
            </TabsList>

            <TabsContent value="health" className="space-y-4">
              <SystemHealthDashboard />
            </TabsContent>

            <TabsContent value="diagnostics" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Terminal className="h-5 w-5" />
                    System Diagnostic Tests
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {diagnosticTests.map((test, index) => (
                      <Card key={index} className="relative">
                        <CardHeader className="pb-3">
                          <CardTitle className="flex items-center gap-2 text-base">
                            {test.icon}
                            {test.name}
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm text-muted-foreground mb-4">
                            {test.description}
                          </p>
                          <Button
                            onClick={test.action}
                            disabled={activeTests.includes(test.name) || loading}
                            className="w-full"
                            size="sm"
                          >
                            {activeTests.includes(test.name) ? (
                              <>
                                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                                Running...
                              </>
                            ) : (
                              <>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Run Test
                              </>
                            )}
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <div className="mt-6 p-4 bg-muted rounded-lg">
                    <h4 className="font-semibold mb-2">Quick Actions</h4>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => runTest("Full System Check", runSystemHealthCheck)}
                        disabled={loading || activeTests.length > 0}
                      >
                        <Activity className="h-4 w-4 mr-2" />
                        Run All Tests
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => runTest("API Connectivity", testAPIConnectivity)}
                        disabled={loading || activeTests.length > 0}
                      >
                        <Network className="h-4 w-4 mr-2" />
                        Test API Connectivity
                      </Button>
                    </div>

                    <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg border border-blue-200 dark:border-blue-800">
                      <h5 className="font-medium text-blue-900 dark:text-blue-100 mb-1">System Status</h5>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        Edge functions are running on http://127.0.0.1:54321/functions/v1/
                      </p>
                      <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                        AI services require OpenAI API key configuration for full functionality.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="monitoring" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5" />
                    Real-time System Monitoring
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="p-4 border rounded-lg text-center">
                      <Database className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                      <h4 className="font-semibold">Database</h4>
                      <Badge variant="default" className="mt-1">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Connected
                      </Badge>
                    </div>
                    
                    <div className="p-4 border rounded-lg text-center">
                      <Zap className="h-8 w-8 mx-auto mb-2 text-yellow-500" />
                      <h4 className="font-semibold">Edge Functions</h4>
                      <Badge variant="default" className="mt-1">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Operational
                      </Badge>
                    </div>
                    
                    <div className="p-4 border rounded-lg text-center">
                      <Brain className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                      <h4 className="font-semibold">AI Services</h4>
                      <Badge variant="default" className="mt-1">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Active
                      </Badge>
                    </div>
                    
                    <div className="p-4 border rounded-lg text-center">
                      <Shield className="h-8 w-8 mx-auto mb-2 text-green-500" />
                      <h4 className="font-semibold">Security</h4>
                      <Badge variant="default" className="mt-1">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Secure
                      </Badge>
                    </div>
                  </div>
                  
                  <div className="mt-6 text-center text-sm text-muted-foreground">
                    Monitoring refreshes automatically every 30 seconds when enabled
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};