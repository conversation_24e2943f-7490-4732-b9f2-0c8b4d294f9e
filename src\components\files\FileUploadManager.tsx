import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useProjectFiles } from '@/hooks/useProjectFiles';
import { useProjects } from '@/hooks/useProjects';
import { supabase } from '@/integrations/supabase/client';
import { useEffect } from 'react';
import { Upload, Download, Trash2, FileText, File, FolderPlus, Grid, List, Search, Filter } from 'lucide-react';
import { ModernFileCard } from './ModernFileCard';
import { ModernFolderCard } from './ModernFolderCard';

export const FileUploadManager = () => {
  const { files, loading, uploading, uploadFile, downloadFile, deleteFile } = useProjectFiles();
  const { projects } = useProjects();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedProjectId, setSelectedProjectId] = useState<string>('');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');

  // Real folders data from database
  const [folders, setFolders] = useState<any[]>([]);
  const [foldersLoading, setFoldersLoading] = useState(false);

  // Load folders from database
  useEffect(() => {
    loadFolders();
  }, []);

  const loadFolders = async () => {
    setFoldersLoading(true);
    try {
      const { data, error } = await supabase
        .from('file_folders')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setFolders(data || []);
    } catch (error) {
      console.error('Error loading folders:', error);
      // Set empty array if no folders table exists yet
      setFolders([]);
    } finally {
      setFoldersLoading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Check file type
      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/msword',
        'application/vnd.ms-excel'
      ];
      
      if (!allowedTypes.includes(file.type)) {
        alert('Please select a PDF, DOCX, or XLSX file');
        return;
      }

      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    const result = await uploadFile(selectedFile, selectedProjectId || undefined);
    if (result.success) {
      setSelectedFile(null);
      setSelectedProjectId('');
      setShowUploadDialog(false);
      // Reset file input
      const fileInput = document.getElementById('file-upload') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return <FileText className="h-4 w-4 text-red-500" />;
    if (fileType.includes('word')) return <FileText className="h-4 w-4 text-blue-500" />;
    if (fileType.includes('sheet') || fileType.includes('excel')) return <File className="h-4 w-4 text-green-500" />;
    return <File className="h-4 w-4" />;
  };

  // Filter files based on search and category
  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = filterCategory === 'all' || file.type.includes(filterCategory);
    return matchesSearch && matchesCategory;
  });

  // Folder handlers
  const handleFolderOpen = (folder: any) => {
    console.log('Opening folder:', folder.name);
  };

  const handleFolderEdit = (folder: any) => {
    console.log('Editing folder:', folder.name);
  };

  const handleFolderDelete = (folder: any) => {
    console.log('Deleting folder:', folder.name);
  };

  const handleAddFileToFolder = (folder: any) => {
    console.log('Adding file to folder:', folder.name);
    setShowUploadDialog(true);
  };

  // File handlers for modern cards
  const handleFileDownload = (file: any) => {
    downloadFile(file.id, file.name);
  };

  const handleFileDelete = (file: any) => {
    if (confirm(`Are you sure you want to delete ${file.name}?`)) {
      deleteFile(file.id);
    }
  };

  const handleFilePreview = (file: any) => {
    console.log('Previewing file:', file.name);
  };

  return (
    <div className="space-y-6 p-4 md:p-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-red-500 to-red-600 bg-clip-text text-transparent">
            📁 File Management
          </h2>
          <p className="text-muted-foreground mt-1">Organize and manage your files with modern interface</p>
        </div>

        <div className="flex flex-wrap gap-2">
          <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700">
                <Upload className="h-4 w-4 mr-2" />
                Upload File
              </Button>
            </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Upload Project File</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="file-upload">Select File (PDF, DOCX, XLSX)</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".pdf,.docx,.xlsx,.doc,.xls"
                  onChange={handleFileSelect}
                />
                {selectedFile && (
                  <div className="text-sm text-muted-foreground">
                    Selected: {selectedFile.name} ({formatFileSize(selectedFile.size)})
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="project-select">Project (Optional)</Label>
                <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a project" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="no-project">No Project</SelectItem>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-2">
                <Button 
                  onClick={handleUpload} 
                  disabled={!selectedFile || uploading}
                >
                  {uploading ? 'Uploading...' : 'Upload'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowUploadDialog(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        <Button
          variant="outline"
          onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
        >
          {viewMode === 'grid' ? <List className="h-4 w-4" /> : <Grid className="h-4 w-4" />}
        </Button>
      </div>
      </div>

      {/* Search and Filter Section */}
      <div className="flex flex-col sm:flex-row gap-4 items-center">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search files..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={filterCategory} onValueChange={setFilterCategory}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Files</SelectItem>
            <SelectItem value="pdf">PDF Documents</SelectItem>
            <SelectItem value="image">Images</SelectItem>
            <SelectItem value="word">Word Documents</SelectItem>
            <SelectItem value="excel">Spreadsheets</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Main Content with Tabs */}
      <Tabs defaultValue="files" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="files" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Files ({filteredFiles.length})
          </TabsTrigger>
          <TabsTrigger value="folders" className="flex items-center gap-2">
            <FolderPlus className="h-4 w-4" />
            Folders ({folders.length})
          </TabsTrigger>
        </TabsList>

        {/* Files Tab */}
        <TabsContent value="files" className="space-y-6">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading files...</p>
            </div>
          ) : filteredFiles.length === 0 ? (
            <Card className="border-dashed border-2">
              <CardContent className="text-center py-12">
                <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
                <h3 className="text-lg font-semibold mb-2">No files found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm || filterCategory !== 'all'
                    ? 'Try adjusting your search or filter criteria'
                    : 'Upload your first file to get started'
                  }
                </p>
                <Button onClick={() => setShowUploadDialog(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload File
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className={viewMode === 'grid'
              ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
            }>
              {viewMode === 'grid' ? (
                filteredFiles.map((file) => (
                  <ModernFileCard
                    key={file.id}
                    file={{
                      id: file.id,
                      name: file.name,
                      size: file.size,
                      type: file.type,
                      uploaded_at: file.created_at,
                      project_name: projects.find(p => p.id === file.project_id)?.name,
                      category: file.type.split('/')[0]
                    }}
                    onDownload={handleFileDownload}
                    onDelete={handleFileDelete}
                    onPreview={handleFilePreview}
                  />
                ))
              ) : (
                <Card>
                  <CardContent>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>File</TableHead>
                            <TableHead>Project</TableHead>
                            <TableHead>Size</TableHead>
                            <TableHead>Uploaded</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredFiles.map((file) => {
                            const project = projects.find(p => p.id === file.project_id);
                            return (
                              <TableRow key={file.id}>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    {getFileIcon(file.file_type || file.type)}
                                    <div>
                                      <p className="font-medium">{file.file_name || file.name}</p>
                                      <p className="text-sm text-muted-foreground">
                                        {(file.file_type || file.type)?.split('/').pop()?.toUpperCase()}
                                      </p>
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {project ? (
                                    <Badge variant="outline">{project.name}</Badge>
                                  ) : (
                                    <span className="text-muted-foreground">No Project</span>
                                  )}
                                </TableCell>
                                <TableCell>{formatFileSize(file.file_size || file.size)}</TableCell>
                                <TableCell>
                                  {new Date(file.uploaded_at || file.created_at).toLocaleDateString()}
                                </TableCell>
                                <TableCell>
                                  <div className="flex gap-2">
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleFileDownload(file)}
                                    >
                                      <Download className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleFileDelete(file)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        {/* Folders Tab */}
        <TabsContent value="folders" className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Organize with Folders</h3>
            <Button variant="outline">
              <FolderPlus className="h-4 w-4 mr-2" />
              New Folder
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {folders.map((folder) => (
              <ModernFolderCard
                key={folder.id}
                folder={folder}
                onOpen={handleFolderOpen}
                onEdit={handleFolderEdit}
                onDelete={handleFolderDelete}
                onAddFile={handleAddFileToFolder}
              />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
