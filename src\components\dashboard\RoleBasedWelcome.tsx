
import { useAuth } from "@/components/auth/AuthProvider";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Building, Calendar, User } from "lucide-react";

export const RoleBasedWelcome = () => {
  const { userProfile } = useAuth();

  console.log('🎯 RoleBasedWelcome: Rendering with userProfile:', userProfile);

  if (!userProfile) {
    console.log('❌ RoleBasedWelcome: No userProfile, showing fallback');
    // Show a fallback welcome card instead of returning null
    return (
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Welcome to the Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-lg">
            <p className="text-sm">Please sign in to access your personalized dashboard.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRoleDescription = (role: string | null) => {
    switch (role) {
      case 'admin':
        return 'Full system access and user management capabilities';
      case 'manager':
        return 'Team management and project oversight responsibilities';
      case 'staff':
        return 'Task execution and reporting capabilities';
      case 'accountant':
        return 'Financial management and invoice processing';
      case 'staff-admin':
        return 'Administrative support and documentation';
      default:
        return 'Contact administrator for role assignment';
    }
  };

  const getRoleColor = (role: string | null) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'manager':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'staff':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'accountant':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'staff-admin':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <Card className="glass-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Welcome back, {userProfile.full_name || 'User'}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Badge className={getRoleColor(userProfile.role)}>
              {userProfile.role?.toUpperCase() || 'NO ROLE'}
            </Badge>
          </div>
          {userProfile.department?.name && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Building className="h-4 w-4" />
              <span>{userProfile.department.name}</span>
            </div>
          )}
        </div>
        
        <p className="text-sm text-muted-foreground">
          {getRoleDescription(userProfile.role)}
        </p>

        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Calendar className="h-3 w-3" />
          <span>Last login: {new Date().toLocaleDateString()}</span>
        </div>
      </CardContent>
    </Card>
  );
};
