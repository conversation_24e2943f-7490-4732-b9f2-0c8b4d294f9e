import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Database, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Zap,
  Shield
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const DatabaseFixComponent: React.FC = () => {
  const [isFixing, setIsFixing] = useState(false);
  const [fixStatus, setFixStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  const emergencyRLSFix = async () => {
    setIsFixing(true);
    setFixStatus('idle');
    setLogs([]);
    
    try {
      addLog('🚨 Starting emergency RLS fix...');
      
      // Step 1: Disable RLS
      addLog('1️⃣ Disabling RLS on profiles table...');
      await supabase.rpc('exec_sql', { 
        sql: 'ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;' 
      });
      
      // Step 2: Drop problematic policies
      addLog('2️⃣ Dropping problematic policies...');
      const dropPolicies = [
        'DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;',
        'DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;',
        'DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;',
        'DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;',
        'DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;',
        'DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;',
        'DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;',
        'DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;'
      ];
      
      for (const sql of dropPolicies) {
        await supabase.rpc('exec_sql', { sql });
      }
      
      // Step 3: Re-enable RLS
      addLog('3️⃣ Re-enabling RLS...');
      await supabase.rpc('exec_sql', { 
        sql: 'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;' 
      });
      
      // Step 4: Create safe policies
      addLog('4️⃣ Creating safe policies...');
      const safePolicies = [
        `CREATE POLICY "profiles_select_own" ON public.profiles
         FOR SELECT USING (auth.uid() = id);`,
        `CREATE POLICY "profiles_insert_own" ON public.profiles
         FOR INSERT WITH CHECK (auth.uid() = id);`,
        `CREATE POLICY "profiles_update_own" ON public.profiles
         FOR UPDATE USING (auth.uid() = id);`,
        `CREATE POLICY "profiles_service_role" ON public.profiles
         FOR ALL USING (current_setting('role') = 'service_role');`
      ];
      
      for (const sql of safePolicies) {
        await supabase.rpc('exec_sql', { sql });
      }
      
      addLog('✅ Emergency RLS fix completed successfully!');
      setFixStatus('success');
      toast.success('RLS policies fixed! Authentication should work now.');
      
    } catch (error) {
      console.error('Emergency fix error:', error);
      addLog(`❌ Emergency fix failed: ${error}`);
      setFixStatus('error');
      toast.error('Emergency fix failed. Check logs for details.');
    } finally {
      setIsFixing(false);
    }
  };

  const testDatabaseAccess = async () => {
    try {
      addLog('🔍 Testing database access...');
      
      // Test profiles table access
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
        
      if (error) {
        addLog(`❌ Database test failed: ${error.message}`);
        toast.error('Database access test failed');
      } else {
        addLog('✅ Database access test successful');
        toast.success('Database access is working');
      }
    } catch (err) {
      addLog(`❌ Database test exception: ${err}`);
      toast.error('Database test failed with exception');
    }
  };

  const createMissingTables = async () => {
    setIsFixing(true);
    try {
      addLog('🔨 Creating missing tables...');
      
      // Create departments table
      const createDepartments = `
        CREATE TABLE IF NOT EXISTS public.departments (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name TEXT NOT NULL UNIQUE,
          description TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `;
      
      await supabase.rpc('exec_sql', { sql: createDepartments });
      addLog('✅ Departments table created/verified');
      
      // Insert sample departments
      const insertDepartments = `
        INSERT INTO public.departments (name, description) VALUES
        ('IT Department', 'Information Technology'),
        ('HR Department', 'Human Resources'),
        ('Finance Department', 'Finance and Accounting'),
        ('Operations', 'Field Operations')
        ON CONFLICT (name) DO NOTHING;
      `;
      
      await supabase.rpc('exec_sql', { sql: insertDepartments });
      addLog('✅ Sample departments inserted');
      
      toast.success('Missing tables created successfully');
      
    } catch (error) {
      console.error('Table creation error:', error);
      addLog(`❌ Table creation failed: ${error}`);
      toast.error('Failed to create missing tables');
    } finally {
      setIsFixing(false);
    }
  };

  const getStatusIcon = () => {
    switch (fixStatus) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-500" />;
      default: return <Database className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (fixStatus) {
      case 'success': return <Badge className="bg-green-100 text-green-800">Fixed</Badge>;
      case 'error': return <Badge variant="destructive">Error</Badge>;
      default: return <Badge variant="outline">Ready</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getStatusIcon()}
          <div>
            <h2 className="text-2xl font-bold">Database Emergency Fix</h2>
            <p className="text-muted-foreground">Fix RLS infinite recursion and database issues</p>
          </div>
        </div>
        {getStatusBadge()}
      </div>

      {/* Issue Description */}
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <strong>Issue Detected:</strong> Infinite recursion in RLS policies for the profiles table. 
          This prevents user authentication and profile creation. The emergency fix will resolve this immediately.
        </AlertDescription>
      </Alert>

      {/* Fix Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Zap className="h-4 w-4 text-red-500" />
              Emergency Fix
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Immediately fix RLS infinite recursion issue
            </p>
            <Button 
              onClick={emergencyRLSFix} 
              disabled={isFixing}
              className="w-full"
              variant="destructive"
            >
              {isFixing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Fixing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Fix RLS Now
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="h-4 w-4 text-blue-500" />
              Test Database
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Test database connectivity and access
            </p>
            <Button 
              onClick={testDatabaseAccess} 
              variant="outline"
              className="w-full"
            >
              <Database className="h-4 w-4 mr-2" />
              Test Access
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Shield className="h-4 w-4 text-green-500" />
              Create Tables
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Create missing database tables
            </p>
            <Button 
              onClick={createMissingTables} 
              disabled={isFixing}
              variant="outline"
              className="w-full"
            >
              {isFixing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Create Tables
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Logs */}
      {logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Fix Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-xs max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">What This Fix Does</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <span className="text-red-500">1.</span>
            <span>Disables RLS temporarily to stop infinite recursion</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-orange-500">2.</span>
            <span>Removes all problematic recursive policies</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-yellow-500">3.</span>
            <span>Re-enables RLS with safe configuration</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-500">4.</span>
            <span>Creates new policies using auth.uid() without recursion</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-500">5.</span>
            <span>Adds service role policy for admin operations</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
