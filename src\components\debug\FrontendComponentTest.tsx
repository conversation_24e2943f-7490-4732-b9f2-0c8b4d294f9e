import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/components/auth/AuthProvider';
import { useDashboardData } from '@/hooks/useDashboardData';
import { useDepartments } from '@/hooks/useDepartments';
import { useFinancialData } from '@/hooks/useFinancialData';
import { useAIOperations } from '@/hooks/useAIOperations';
import { useAdminOperations } from '@/hooks/admin/useAdminOperations';
import { CheckCircle, XCircle, AlertCircle, Loader2 } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
  details?: any;
}

export const FrontendComponentTest = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { userProfile, isAuthenticated } = useAuth();

  const addResult = (result: TestResult) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date().toISOString() }]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  // Test Authentication Components
  const testAuthComponents = async () => {
    addResult({
      name: 'Authentication Hook',
      status: isAuthenticated ? 'success' : 'warning',
      message: isAuthenticated ? 'User authenticated successfully' : 'No active authentication',
      details: {
        userProfile: userProfile ? { role: userProfile.role, id: userProfile.id } : null,
        sessionActive: !!session,
        userEmail: user?.email || 'No user'
      }
    });

    if (userProfile) {
      addResult({
        name: 'User Profile Data',
        status: 'success',
        message: `Profile loaded for ${userProfile.role} user`,
        details: {
          role: userProfile.role,
          fullName: userProfile.full_name,
          email: userProfile.email,
          department: userProfile.department?.name || 'No department'
        }
      });
    }

    // Test role-based access flags
    addResult({
      name: 'Role-Based Access Flags',
      status: 'success',
      message: 'Role access flags evaluated',
      details: {
        isAdmin: isAdmin,
        isManager: isManager,
        isStaff: isStaff,
        currentRole: userProfile?.role || 'No role'
      }
    });
  };

  // Test Authentication Flows
  const testAuthFlows = async () => {
    addResult({
      name: 'Authentication Flow Test',
      status: 'info',
      message: 'Testing authentication flow capabilities'
    });

    // Test sign out if authenticated
    if (isAuthenticated) {
      addResult({
        name: 'Sign Out Available',
        status: 'success',
        message: 'Sign out function is available for authenticated user'
      });
    } else {
      addResult({
        name: 'Sign In Required',
        status: 'warning',
        message: 'No authenticated user - sign in required for full testing'
      });
    }

    // Test session persistence
    if (session) {
      const expiresAt = new Date(session.expires_at * 1000);
      const now = new Date();
      const isValid = expiresAt > now;

      addResult({
        name: 'Session Persistence',
        status: isValid ? 'success' : 'warning',
        message: isValid ? 'Session is valid and persistent' : 'Session expired or invalid',
        details: {
          expiresAt: expiresAt.toLocaleString(),
          isValid: isValid
        }
      });
    }
  };

  // Test Dashboard Components
  const testDashboardComponents = async () => {
    try {
      // Test dashboard data hook
      const dashboardHook = useDashboardData();

      if (dashboardHook.isLoading) {
        addResult({
          name: 'Dashboard Data Loading',
          status: 'loading',
          message: 'Dashboard data is loading...'
        });
      } else if (dashboardHook.error) {
        addResult({
          name: 'Dashboard Data Hook',
          status: 'error',
          message: `Dashboard data error: ${dashboardHook.error.message}`
        });
      } else {
        addResult({
          name: 'Dashboard Data Hook',
          status: 'success',
          message: 'Dashboard data loaded successfully',
          details: {
            hasStats: !!dashboardHook.data?.stats,
            hasChartData: !!dashboardHook.data?.chartData,
            hasPieChartData: !!dashboardHook.data?.pieChartData,
            hasRecentActivity: !!dashboardHook.data?.recentActivity,
            hasPerformanceMetrics: !!dashboardHook.data?.performanceMetrics,
            hasFinancialSummary: !!dashboardHook.data?.financialSummary
          }
        });

        // Test dashboard stats structure
        if (dashboardHook.data?.stats) {
          const stats = dashboardHook.data.stats;
          addResult({
            name: 'Dashboard Stats Structure',
            status: 'success',
            message: 'Dashboard statistics properly structured',
            details: {
              totalUsers: stats.totalUsers,
              activeProjects: stats.activeProjects,
              completedTasks: stats.completedTasks,
              systemHealth: stats.systemHealth,
              pendingApprovals: stats.pendingApprovals
            }
          });
        }

        // Test chart data availability
        if (dashboardHook.data?.chartData && dashboardHook.data.chartData.length > 0) {
          addResult({
            name: 'Chart Data Availability',
            status: 'success',
            message: `Chart data available with ${dashboardHook.data.chartData.length} data points`
          });
        } else {
          addResult({
            name: 'Chart Data Availability',
            status: 'warning',
            message: 'No chart data available - using fallback data'
          });
        }
      }
    } catch (error) {
      addResult({
        name: 'Dashboard Components',
        status: 'error',
        message: `Dashboard test failed: ${error.message}`
      });
    }
  };

  // Test Role-Specific Dashboard Components
  const testRoleSpecificDashboards = async () => {
    const currentRole = userProfile?.role;

    addResult({
      name: 'Role-Specific Dashboard Test',
      status: 'info',
      message: `Testing dashboard components for ${currentRole} role`
    });

    // Test role-based dashboard rendering
    if (currentRole === 'admin') {
      addResult({
        name: 'Admin Dashboard Components',
        status: 'success',
        message: 'Admin dashboard should include: User Management, System Diagnostics, AI Management, Department Management',
        details: {
          expectedComponents: ['UserManagement', 'SystemDiagnostics', 'AIManagement', 'DepartmentManagement'],
          dashboardType: 'UnifiedDashboard'
        }
      });
    } else if (currentRole === 'manager') {
      addResult({
        name: 'Manager Dashboard Components',
        status: 'success',
        message: 'Manager dashboard should include: Team Management, Project Oversight, Leave Management, Reports',
        details: {
          expectedComponents: ['TeamTimeManagement', 'WorkBoard', 'LeaveManagement', 'TeamOverview'],
          dashboardType: 'UnifiedDashboard'
        }
      });
    } else if (currentRole === 'staff') {
      addResult({
        name: 'Staff Dashboard Components',
        status: 'success',
        message: 'Staff dashboard should include: Personal Tasks, Performance Metrics, Leave Requests, Memos',
        details: {
          expectedComponents: ['EnhancedStaffDashboard', 'LeaveRequestWidget', 'MemoWidget'],
          dashboardType: 'EnhancedStaffDashboard'
        }
      });
    } else if (currentRole === 'accountant') {
      addResult({
        name: 'Accountant Dashboard Components',
        status: 'success',
        message: 'Accountant dashboard should include: Financial Overview, Invoice Management, Expense Tracking',
        details: {
          expectedComponents: ['AccountantDashboard', 'FinancialSummary', 'InvoiceManagement'],
          dashboardType: 'UnifiedDashboard'
        }
      });
    } else if (currentRole === 'staff-admin') {
      addResult({
        name: 'Staff-Admin Dashboard Components',
        status: 'success',
        message: 'Staff-Admin dashboard should include: Expense Management, Fleet Management, Asset Management',
        details: {
          expectedComponents: ['ExpenseManagement', 'FleetManagement', 'AssetInventoryManagement'],
          dashboardType: 'UnifiedDashboard'
        }
      });
    }

    // Test navigation accessibility for current role
    addResult({
      name: 'Role-Based Navigation',
      status: 'success',
      message: `Navigation should be accessible for ${currentRole} role`,
      details: {
        currentRole: currentRole,
        expectedRoutes: getRoleBasedRoutes(currentRole)
      }
    });
  };

  // Helper function to get expected routes for each role
  const getRoleBasedRoutes = (role: string) => {
    switch (role) {
      case 'admin':
        return ['/dashboard/admin', '/dashboard/admin/users', '/dashboard/admin/departments', '/dashboard/admin/projects'];
      case 'manager':
        return ['/dashboard/manager', '/dashboard/manager/team', '/dashboard/manager/projects'];
      case 'staff':
        return ['/dashboard/staff', '/dashboard/staff/tasks', '/dashboard/staff/leave'];
      case 'accountant':
        return ['/dashboard/accountant', '/dashboard/accountant/invoices', '/dashboard/accountant/expenses'];
      case 'staff-admin':
        return ['/dashboard/staff-admin', '/dashboard/staff-admin/expenses', '/dashboard/staff-admin/fleet'];
      default:
        return ['/dashboard'];
    }
  };

  // Test Department Components
  const testDepartmentComponents = async () => {
    try {
      const { departments, isLoading, error } = useDepartments();
      
      if (isLoading) {
        addResult({
          name: 'Departments Loading',
          status: 'loading',
          message: 'Departments are loading...'
        });
      } else if (error) {
        addResult({
          name: 'Departments Hook',
          status: 'error',
          message: `Departments error: ${error.message}`
        });
      } else {
        addResult({
          name: 'Departments Hook',
          status: 'success',
          message: `Loaded ${departments.length} departments`,
          details: { departmentCount: departments.length }
        });
      }
    } catch (error) {
      addResult({
        name: 'Department Components',
        status: 'error',
        message: `Department test failed: ${error.message}`
      });
    }
  };

  // Test Financial Components
  const testFinancialComponents = async () => {
    try {
      const financialData = useFinancialData();
      
      addResult({
        name: 'Financial Data Hook',
        status: financialData.invoicesLoading ? 'loading' : 'success',
        message: financialData.invoicesLoading ? 'Financial data loading...' : 'Financial data hook initialized',
        details: { hasInvoices: !!financialData.invoices }
      });
    } catch (error) {
      addResult({
        name: 'Financial Components',
        status: 'error',
        message: `Financial test failed: ${error.message}`
      });
    }
  };

  // Test AI Components
  const testAIComponents = async () => {
    try {
      const aiOps = useAIOperations();
      
      addResult({
        name: 'AI Operations Hook',
        status: 'success',
        message: 'AI operations hook initialized successfully',
        details: { hasAIResults: !!aiOps.useAIResults }
      });
    } catch (error) {
      addResult({
        name: 'AI Components',
        status: 'error',
        message: `AI test failed: ${error.message}`
      });
    }
  };

  // Test Admin Components (if user is admin)
  const testAdminComponents = async () => {
    if (userProfile?.role !== 'admin') {
      addResult({
        name: 'Admin Components',
        status: 'warning',
        message: 'Skipped - User is not admin'
      });
      return;
    }

    try {
      const adminOps = useAdminOperations();
      
      addResult({
        name: 'Admin Operations Hook',
        status: 'success',
        message: 'Admin operations hook initialized successfully'
      });
    } catch (error) {
      addResult({
        name: 'Admin Components',
        status: 'error',
        message: `Admin test failed: ${error.message}`
      });
    }
  };

  // Test Role-Based Access
  const testRoleBasedAccess = async () => {
    const userRole = userProfile?.role;
    
    if (!userRole) {
      addResult({
        name: 'Role-Based Access',
        status: 'error',
        message: 'No user role found'
      });
      return;
    }

    // Test role-based dashboard routes
    const roleRoutes = {
      admin: '/dashboard/admin',
      manager: '/dashboard/manager',
      staff: '/dashboard/staff',
      accountant: '/dashboard/accountant',
      'staff-admin': '/dashboard/staff-admin'
    };

    const expectedRoute = roleRoutes[userRole as keyof typeof roleRoutes];
    
    addResult({
      name: 'Role-Based Routing',
      status: 'success',
      message: `User role '${userRole}' maps to route '${expectedRoute}'`,
      details: { userRole, expectedRoute }
    });

    // Test role-based component access
    const rolePermissions = {
      admin: ['all_components', 'user_management', 'system_settings', 'financial_data'],
      manager: ['team_management', 'project_oversight', 'reports'],
      staff: ['task_management', 'time_tracking', 'personal_dashboard'],
      accountant: ['financial_data', 'invoicing', 'expense_tracking'],
      'staff-admin': ['staff_functions', 'admin_support']
    };

    const userPermissions = rolePermissions[userRole as keyof typeof rolePermissions] || [];
    
    addResult({
      name: 'Role-Based Permissions',
      status: 'success',
      message: `User has ${userPermissions.length} permission categories`,
      details: { permissions: userPermissions }
    });
  };

  // Run all component tests
  const runAllTests = async () => {
    setIsRunning(true);
    clearResults();

    try {
      await testAuthComponents();
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay for UX

      await testAuthFlows();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testDashboardComponents();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testRoleSpecificDashboards();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testDepartmentComponents();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testFinancialComponents();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testAIComponents();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testAdminComponents();
      await new Promise(resolve => setTimeout(resolve, 500));

      await testRoleBasedAccess();

    } catch (error) {
      addResult({
        name: 'Test Suite',
        status: 'error',
        message: `Test suite failed: ${error.message}`
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'loading':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      warning: 'secondary',
      loading: 'outline'
    };
    return variants[status as keyof typeof variants] || 'outline';
  };

  const successCount = testResults.filter(r => r.status === 'success').length;
  const errorCount = testResults.filter(r => r.status === 'error').length;
  const warningCount = testResults.filter(r => r.status === 'warning').length;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Frontend Component Testing</CardTitle>
          <CardDescription>
            Test all frontend components and hooks for each user role
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4 mb-4">
            <Button
              onClick={runAllTests}
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning && <Loader2 className="h-4 w-4 animate-spin" />}
              {isRunning ? 'Running Tests...' : 'Run All Component Tests'}
            </Button>
            <Button variant="outline" onClick={testAuthComponents}>
              Test Authentication
            </Button>
            <Button variant="outline" onClick={testAuthFlows}>
              Test Auth Flows
            </Button>
            <Button variant="outline" onClick={testRoleSpecificDashboards}>
              Test Role Dashboard
            </Button>
            <Button variant="outline" onClick={clearResults}>
              Clear Results
            </Button>
          </div>

          {/* Authentication Status Display */}
          <div className="mb-4 p-4 bg-blue-50 rounded-lg border">
            <h3 className="font-semibold mb-2 text-blue-800">Current Authentication Status</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium">User:</span>
                <p className={isAuthenticated ? 'text-green-600' : 'text-gray-500'}>
                  {user?.email || 'Not authenticated'}
                </p>
              </div>
              <div>
                <span className="font-medium">Role:</span>
                <p className="text-blue-600">
                  {userProfile?.role || 'No role'}
                </p>
              </div>
              <div>
                <span className="font-medium">Department:</span>
                <p className="text-purple-600">
                  {userProfile?.department?.name || 'No department'}
                </p>
              </div>
              <div>
                <span className="font-medium">Session:</span>
                <p className={session ? 'text-green-600' : 'text-gray-500'}>
                  {session ? 'Active' : 'None'}
                </p>
              </div>
            </div>
          </div>

          {testResults.length > 0 && (
            <div className="mb-4 p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Test Summary</h3>
              <div className="flex gap-4 text-sm">
                <span className="text-green-600">✅ Passed: {successCount}</span>
                <span className="text-yellow-600">⚠️ Warnings: {warningCount}</span>
                <span className="text-red-600">❌ Failed: {errorCount}</span>
                <span className="text-gray-600">📊 Total: {testResults.length}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  {getStatusIcon(result.status)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium">{result.name}</span>
                      <Badge variant={getStatusBadge(result.status)}>
                        {result.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{result.message}</p>
                    {result.details && (
                      <details className="mt-2">
                        <summary className="text-xs text-muted-foreground cursor-pointer">
                          View Details
                        </summary>
                        <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
