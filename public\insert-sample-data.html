<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Insert Sample Data</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Insert Sample Data</h1>
        <p>This tool will insert sample data that works with your existing table schema.</p>
        
        <div id="status"></div>
        
        <button id="insertBtn" class="button" onclick="insertSampleData()">
            📊 Insert Sample Data
        </button>
        
        <button id="dashboardBtn" class="button" onclick="openDashboard()" disabled>
            🚀 Open Dashboard
        </button>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        async function insertSampleData() {
            const insertBtn = document.getElementById('insertBtn');
            const dashboardBtn = document.getElementById('dashboardBtn');
            
            insertBtn.disabled = true;
            insertBtn.textContent = '🔄 Inserting...';
            
            try {
                log('📊 Starting sample data insertion...');
                
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('Please log in first');
                
                const userId = user.id;
                log('✅ User authenticated: ' + user.email);
                
                let totalInserted = 0;
                
                // Insert reports (this table seems to work)
                try {
                    const reportData = [
                        {
                            title: 'Q4 Financial Report',
                            description: 'Quarterly financial performance analysis',
                            submitted_by: userId
                        },
                        {
                            title: 'Team Performance Review',
                            description: 'Monthly team performance evaluation',
                            submitted_by: userId
                        },
                        {
                            title: 'Project Milestone Update',
                            description: 'Current project status and milestones',
                            submitted_by: userId
                        }
                    ];
                    
                    const { data: reportResult, error: reportError } = await supabase
                        .from('reports')
                        .insert(reportData)
                        .select();
                    
                    if (reportError) {
                        log(`⚠️ Reports: ${reportError.message}`);
                    } else {
                        log(`✅ Inserted ${reportResult?.length || 0} reports`);
                        totalInserted += reportResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Reports error: ${e.message}`);
                }
                
                // Insert time logs (this table seems to work)
                try {
                    const timeLogData = [
                        {
                            user_id: userId,
                            hours_worked: 8.0,
                            log_date: new Date().toISOString().split('T')[0]
                        },
                        {
                            user_id: userId,
                            hours_worked: 7.5,
                            log_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
                        },
                        {
                            user_id: userId,
                            hours_worked: 6.0,
                            log_date: new Date(Date.now() - 172800000).toISOString().split('T')[0]
                        }
                    ];
                    
                    const { data: timeLogResult, error: timeLogError } = await supabase
                        .from('time_logs')
                        .insert(timeLogData)
                        .select();
                    
                    if (timeLogError) {
                        log(`⚠️ Time logs: ${timeLogError.message}`);
                    } else {
                        log(`✅ Inserted ${timeLogResult?.length || 0} time logs`);
                        totalInserted += timeLogResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Time logs error: ${e.message}`);
                }
                
                // Try expense reports with required date field
                try {
                    const expenseData = [
                        {
                            title: 'Office Supplies Purchase',
                            description: 'Monthly office supplies and stationery',
                            category: 'office',
                            submitted_by: userId,
                            expense_date: new Date().toISOString().split('T')[0]
                        },
                        {
                            title: 'Business Travel',
                            description: 'Travel expenses for client meeting',
                            category: 'travel',
                            submitted_by: userId,
                            expense_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
                        }
                    ];
                    
                    const { data: expenseResult, error: expenseError } = await supabase
                        .from('expense_reports')
                        .insert(expenseData)
                        .select();
                    
                    if (expenseError) {
                        log(`⚠️ Expense reports: ${expenseError.message}`);
                    } else {
                        log(`✅ Inserted ${expenseResult?.length || 0} expense reports`);
                        totalInserted += expenseResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Expense reports error: ${e.message}`);
                }
                
                // Try invoices with minimal required fields
                try {
                    const invoiceData = [
                        {
                            invoice_number: 'INV-' + Date.now() + '-001',
                            client_name: 'Acme Corporation',
                            created_by: userId
                        },
                        {
                            invoice_number: 'INV-' + Date.now() + '-002',
                            client_name: 'Tech Solutions Ltd',
                            created_by: userId
                        }
                    ];
                    
                    const { data: invoiceResult, error: invoiceError } = await supabase
                        .from('invoices')
                        .insert(invoiceData)
                        .select();
                    
                    if (invoiceError) {
                        log(`⚠️ Invoices: ${invoiceError.message}`);
                    } else {
                        log(`✅ Inserted ${invoiceResult?.length || 0} invoices`);
                        totalInserted += invoiceResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Invoices error: ${e.message}`);
                }
                
                // Try notifications with minimal fields
                try {
                    const notificationData = [
                        {
                            user_id: userId,
                            title: 'Welcome to Dashboard',
                            message: 'Your dashboard has been set up successfully!'
                        },
                        {
                            user_id: userId,
                            title: 'Data Updated',
                            message: 'Sample data has been added to your dashboard'
                        }
                    ];
                    
                    const { data: notificationResult, error: notificationError } = await supabase
                        .from('notifications')
                        .insert(notificationData)
                        .select();
                    
                    if (notificationError) {
                        log(`⚠️ Notifications: ${notificationError.message}`);
                    } else {
                        log(`✅ Inserted ${notificationResult?.length || 0} notifications`);
                        totalInserted += notificationResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Notifications error: ${e.message}`);
                }
                
                log(`🎉 Sample data insertion completed!`);
                log(`📊 Total records inserted: ${totalInserted}`);
                
                if (totalInserted > 0) {
                    showStatus(`🎉 Successfully inserted ${totalInserted} sample records! Your dashboard should now display data.`, 'success');
                    dashboardBtn.disabled = false;
                } else {
                    showStatus('⚠️ No new records were inserted. Check the log for details.', 'error');
                }
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                showStatus('❌ Sample data insertion failed: ' + error.message, 'error');
            } finally {
                insertBtn.disabled = false;
                insertBtn.textContent = '📊 Insert Sample Data';
            }
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Make functions global
        window.insertSampleData = insertSampleData;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
