// ============================================================================
// EMERGENCY RLS FIX - BROWSER CONSOLE VERSION
// Copy and paste this into the browser console to fix RLS infinite recursion
// ============================================================================

window.emergencyRLSFix = async function () {
  console.log('🚨 Starting Emergency RLS Fix...')

  try {
    // Get Supabase client from window (if available)
    const supabase = window.supabase ||
                    (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED?.ReactCurrentOwner?.current?.memoizedProps?.supabase) ||
                    null

    if (!supabase) {
      console.error('❌ Supabase client not found. Please run this from the application page.')
      return
    }

    console.log('✅ Supabase client found')

    // Step 1: Disable RLS
    console.log('1️⃣ Disabling RLS on profiles table...')
    const disableRLS = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;'
    })

    if (disableRLS.error) {
      console.error('❌ Failed to disable RLS:', disableRLS.error)
      return
    }
    console.log('✅ RLS disabled')

    // Step 2: Drop problematic policies
    console.log('2️⃣ Dropping problematic policies...')
    const dropPolicies = [
      'DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;',
      'DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;',
      'DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;',
      'DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;',
      'DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;',
      'DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;',
      'DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;',
      'DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;'
    ]

    for (const sql of dropPolicies) {
      const result = await supabase.rpc('exec_sql', { sql })
      if (result.error) {
        console.warn('⚠️ Policy drop warning:', result.error.message)
      }
    }
    console.log('✅ Policies dropped')

    // Step 3: Re-enable RLS
    console.log('3️⃣ Re-enabling RLS...')
    const enableRLS = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;'
    })

    if (enableRLS.error) {
      console.error('❌ Failed to re-enable RLS:', enableRLS.error)
      return
    }
    console.log('✅ RLS re-enabled')

    // Step 4: Create safe policies
    console.log('4️⃣ Creating safe policies...')
    const safePolicies = [
      `CREATE POLICY "profiles_select_own" ON public.profiles
       FOR SELECT USING (auth.uid() = id);`,
      `CREATE POLICY "profiles_insert_own" ON public.profiles
       FOR INSERT WITH CHECK (auth.uid() = id);`,
      `CREATE POLICY "profiles_update_own" ON public.profiles
       FOR UPDATE USING (auth.uid() = id);`,
      `CREATE POLICY "profiles_service_role" ON public.profiles
       FOR ALL USING (current_setting('role') = 'service_role');`
    ]

    for (const sql of safePolicies) {
      const result = await supabase.rpc('exec_sql', { sql })
      if (result.error) {
        console.error('❌ Failed to create policy:', result.error)
        return
      }
    }
    console.log('✅ Safe policies created')

    // Step 5: Test database access
    console.log('5️⃣ Testing database access...')
    const testResult = await supabase
      .from('profiles')
      .select('count')
      .limit(1)

    if (testResult.error) {
      console.error('❌ Database test failed:', testResult.error)
    } else {
      console.log('✅ Database access test successful')
    }

    console.log('🎉 Emergency RLS fix completed successfully!')
    console.log('🔄 Please refresh the page and try authentication again.')

    return { success: true, message: 'RLS fix completed successfully' }
  } catch (error) {
    console.error('❌ Emergency fix failed:', error)
    return { success: false, error: error.message }
  }
}

// Instructions
console.log(`
🚨 EMERGENCY RLS FIX LOADED
============================

To fix the infinite recursion issue, run:
emergencyRLSFix()

This will:
1. Disable RLS temporarily
2. Remove problematic policies
3. Re-enable RLS
4. Create safe policies
5. Test database access

After running, refresh the page and try authentication again.
`)

// Auto-expose to window for easy access
if (typeof window !== 'undefined') {
  window.emergencyRLSFix = window.emergencyRLSFix
}
