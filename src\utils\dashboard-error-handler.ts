/**
 * Dashboard Error Handler
 * Comprehensive error handling for dashboard components
 */

import { toast } from '@/hooks/use-toast';

export interface DashboardError {
  component: string;
  error: any;
  fallbackData?: any;
  retryable: boolean;
}

export class DashboardErrorHandler {
  static handleComponentError(error: DashboardError): any {
    const { component, error: err, fallbackData, retryable } = error;
    
    console.error(`❌ Dashboard Error in ${component}:`, err);
    
    // Show user-friendly error message
    toast({
      title: `${component} Error`,
      description: retryable 
        ? 'There was an issue loading data. Please try again.'
        : 'Unable to load data. Please contact support if this persists.',
      variant: 'destructive',
    });
    
    // Return fallback data if available
    return fallbackData || this.getDefaultFallback(component);
  }
  
  static getDefaultFallback(component: string): any {
    switch (component) {
      case 'BatteryReports':
        return [];
      
      case 'StaffPerformance':
        return {
          completedTasks: 0,
          pendingTasks: 0,
          inProgressTasks: 0,
          totalTasks: 0,
          reportsSubmitted: 0,
          memosCreated: 0,
          weeklyData: Array.from({ length: 7 }, (_, index) => ({
            name: ['Mon','Tue','Wed','Thu','Fri','Sat','Sun'][index],
            tasks: 0,
            hours: 0,
            productivity: 50
          })),
          taskDistribution: [
            { name: 'No Data', value: 1, color: '#94a3b8' }
          ],
          productivity: 50,
          hoursWorked: 0
        };
      
      case 'LeaveRequests':
        return [];
      
      case 'Memos':
        return [];
      
      case 'TimeTracking':
        return {
          isTracking: false,
          currentSession: null,
          todayHours: 0,
          weekHours: 0
        };
      
      default:
        return null;
    }
  }
  
  static wrapAsyncOperation<T>(
    operation: () => Promise<T>,
    component: string,
    fallbackData?: any
  ): Promise<T> {
    return operation().catch((error) => {
      return this.handleComponentError({
        component,
        error,
        fallbackData,
        retryable: true
      });
    });
  }
  
  static createSafeQuery<T>(
    queryFn: () => Promise<T>,
    component: string,
    fallbackData?: any
  ) {
    return async (): Promise<T> => {
      try {
        return await queryFn();
      } catch (error) {
        console.warn(`⚠️ Query failed for ${component}, using fallback:`, error);
        return this.handleComponentError({
          component,
          error,
          fallbackData,
          retryable: true
        });
      }
    };
  }
}

// Export convenience functions
export const handleDashboardError = DashboardErrorHandler.handleComponentError;
export const wrapAsyncOperation = DashboardErrorHandler.wrapAsyncOperation;
export const createSafeQuery = DashboardErrorHandler.createSafeQuery;
