import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { useTheme } from 'next-themes';
import { 
  Sun, 
  Moon, 
  Palette, 
  Eye, 
  Zap, 
  Heart,
  Star,
  Settings,
  Download,
  Upload,
  Save,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';

export const ThemeBorderDemo: React.FC = () => {
  const { theme, setTheme } = useTheme();

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-purple-500" />
            Light Theme Border Enhancement Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <p className="text-sm text-muted-foreground">
              This demo shows the enhanced border lines for buttons and cards in light theme mode.
              Toggle between light and dark themes to see the difference.
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTheme('light')}
                className={theme === 'light' ? 'bg-primary text-primary-foreground' : ''}
              >
                <Sun className="h-4 w-4 mr-2" />
                Light
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTheme('dark')}
                className={theme === 'dark' ? 'bg-primary text-primary-foreground' : ''}
              >
                <Moon className="h-4 w-4 mr-2" />
                Dark
              </Button>
            </div>
          </div>
          
          <div className="p-4 rounded-lg bg-muted/50">
            <p className="text-sm">
              <strong>Current Theme:</strong> {theme || 'system'} 
              <Badge variant="outline" className="ml-2">
                {theme === 'light' ? 'Borders Visible' : 'Standard Mode'}
              </Badge>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Button Variants Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Button Border Variants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Primary Buttons */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Primary Buttons</h4>
              <div className="space-y-2">
                <Button className="w-full">
                  <Heart className="h-4 w-4 mr-2" />
                  Default Primary
                </Button>
                <Button size="sm" className="w-full">
                  <Star className="h-4 w-4 mr-2" />
                  Small Primary
                </Button>
                <Button size="lg" className="w-full">
                  <Zap className="h-4 w-4 mr-2" />
                  Large Primary
                </Button>
              </div>
            </div>

            {/* Secondary Buttons */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Secondary Buttons</h4>
              <div className="space-y-2">
                <Button variant="secondary" className="w-full">
                  <Settings className="h-4 w-4 mr-2" />
                  Secondary
                </Button>
                <Button variant="outline" className="w-full">
                  <Eye className="h-4 w-4 mr-2" />
                  Outline
                </Button>
                <Button variant="ghost" className="w-full">
                  <Edit className="h-4 w-4 mr-2" />
                  Ghost
                </Button>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Action Buttons</h4>
              <div className="space-y-2">
                <Button variant="destructive" className="w-full">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Destructive
                </Button>
                <Button variant="outline" className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button variant="outline" className="w-full">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                </Button>
              </div>
            </div>
          </div>

          {/* Icon Buttons */}
          <div className="mt-6">
            <h4 className="font-medium text-sm mb-3">Icon Buttons</h4>
            <div className="flex gap-2 flex-wrap">
              <Button size="icon" variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="outline">
                <Edit className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="outline">
                <Save className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="outline">
                <Download className="h-4 w-4" />
              </Button>
              <Button size="icon" variant="destructive">
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card Variants Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5 text-green-500" />
            Card Border Variants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Standard Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Standard Card</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  This is a standard card with enhanced borders in light theme.
                </p>
                <div className="mt-3">
                  <Badge variant="outline">Standard</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Elevated Card */}
            <Card className="card-hover">
              <CardHeader>
                <CardTitle className="text-lg">Hover Card</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Hover over this card to see enhanced border effects.
                </p>
                <div className="mt-3">
                  <Badge variant="secondary">Hover Effect</Badge>
                </div>
              </CardContent>
            </Card>

            {/* Glassmorphism Card */}
            <Card className="glassmorphism">
              <CardHeader>
                <CardTitle className="text-lg">Glass Card</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Glassmorphism effect with subtle borders.
                </p>
                <div className="mt-3">
                  <Badge variant="outline">Glassmorphism</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Stats Cards */}
          <div className="mt-6">
            <h4 className="font-medium text-sm mb-3">Stats Cards</h4>
            <div className="grid gap-4 md:grid-cols-4">
              <Card className="stats-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-600">1,234</div>
                  <div className="text-sm text-muted-foreground">Total Users</div>
                </CardContent>
              </Card>
              
              <Card className="stats-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-600">98.5%</div>
                  <div className="text-sm text-muted-foreground">Uptime</div>
                </CardContent>
              </Card>
              
              <Card className="stats-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-600">456</div>
                  <div className="text-sm text-muted-foreground">Projects</div>
                </CardContent>
              </Card>
              
              <Card className="stats-card">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-600">789</div>
                  <div className="text-sm text-muted-foreground">Tasks</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form Elements Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-orange-500" />
            Form Element Borders
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Input Fields</h4>
              <Input placeholder="Text input with borders" />
              <Input type="email" placeholder="Email input" />
              <Input type="password" placeholder="Password input" />
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Interactive Elements</h4>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">Cancel</Button>
                <Button size="sm">Save Changes</Button>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Badge variant="outline">Tag 1</Badge>
                <Badge variant="secondary">Tag 2</Badge>
                <Badge>Tag 3</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Border Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-pink-500" />
            Border Enhancement Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium text-sm mb-2">Light Theme Features:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Subtle border lines for better component definition</li>
                <li>• Enhanced box shadows with inset highlights</li>
                <li>• Improved hover states with border color changes</li>
                <li>• Responsive border adjustments for different screen sizes</li>
                <li>• Accessibility-compliant focus states</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium text-sm mb-2">Technical Implementation:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• CSS-only solution using :root:not(.dark) selectors</li>
                <li>• Preserves dark theme appearance unchanged</li>
                <li>• Uses rgba colors for subtle transparency</li>
                <li>• Includes high contrast mode support</li>
                <li>• Mobile-optimized border weights</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
