import { supabase } from '../integrations/supabase/client';

export const setupMissingTables = async () => {
  console.log('🔧 Checking and setting up missing database tables...');
  
  try {
    // First, let's check what tables exist and create essential ones
    await checkAndCreateEssentialTables();
    
  } catch (error) {
    console.error('Error setting up tables:', error);
  }
};

const checkAndCreateEssentialTables = async () => {
  console.log('🔍 Checking essential tables...');
  
  // Check if profiles table exists and is accessible
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('⚠️ Profiles table issue:', error.message);
      if (error.message.includes('relation "public.profiles" does not exist')) {
        await createProfilesTable();
      }
    } else {
      console.log('✅ Profiles table exists and is accessible');
    }
  } catch (error) {
    console.error('❌ Error checking profiles table:', error);
  }

  // Check departments table
  try {
    const { data, error } = await supabase
      .from('departments')
      .select('count')
      .limit(1);
    
    if (error) {
      console.log('⚠️ Departments table issue:', error.message);
      if (error.message.includes('relation "public.departments" does not exist')) {
        await createDepartmentsTable();
      }
    } else {
      console.log('✅ Departments table exists and is accessible');
    }
  } catch (error) {
    console.error('❌ Error checking departments table:', error);
  }

  // Check other essential tables
  await checkAndCreateTable('projects', createProjectsTable);
  await checkAndCreateTable('time_logs', createTimeLogsTable);
};

const checkAndCreateTable = async (tableName: string, createFunction: () => Promise<void>) => {
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('count')
      .limit(1);
    
    if (error && error.message.includes(`relation "public.${tableName}" does not exist`)) {
      console.log(`⚠️ ${tableName} table missing, attempting to create...`);
      await createFunction();
    } else if (error) {
      console.log(`⚠️ ${tableName} table issue:`, error.message);
    } else {
      console.log(`✅ ${tableName} table exists and is accessible`);
    }
  } catch (error) {
    console.error(`❌ Error checking ${tableName} table:`, error);
  }
};

const createProfilesTable = async () => {
  console.log('🔧 Creating profiles table...');
  
  try {
    // Since we can't use exec_sql, we'll try to insert a test record to trigger table creation
    // This assumes the table might exist but have RLS issues
    const { error } = await supabase
      .from('profiles')
      .upsert({
        id: '00000000-0000-0000-0000-000000000000', // Test UUID
        email: '<EMAIL>',
        full_name: 'Test User',
        role: 'staff',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, { onConflict: 'id' });

    if (error) {
      console.log('⚠️ Could not create/access profiles table:', error.message);
      // The table might exist but have RLS restrictions
    } else {
      console.log('✅ Profiles table is accessible');
      // Clean up test record
      await supabase.from('profiles').delete().eq('id', '00000000-0000-0000-0000-000000000000');
    }
  } catch (error) {
    console.error('❌ Error creating profiles table:', error);
  }
};

const createDepartmentsTable = async () => {
  console.log('🔧 Creating departments table...');
  
  try {
    // Try to insert default departments
    const defaultDepartments = [
      { id: '1', name: 'IT Department', description: 'Information Technology' },
      { id: '2', name: 'HR Department', description: 'Human Resources' },
      { id: '3', name: 'Finance Department', description: 'Finance and Accounting' },
      { id: '4', name: 'Operations', description: 'Operations Management' },
      { id: '5', name: 'Marketing', description: 'Marketing and Sales' }
    ];

    const { error } = await supabase
      .from('departments')
      .upsert(defaultDepartments, { onConflict: 'id' });

    if (error) {
      console.log('⚠️ Could not create/access departments table:', error.message);
    } else {
      console.log('✅ Departments table created with default data');
    }
  } catch (error) {
    console.error('❌ Error creating departments table:', error);
  }
};

const createProjectsTable = async () => {
  console.log('🔧 Checking projects table...');
  // For now, just log that we checked it
  console.log('ℹ️ Projects table check completed');
};

const createTimeLogsTable = async () => {
  console.log('🔧 Checking time_logs table...');
  // For now, just log that we checked it
  console.log('ℹ️ Time logs table check completed');
};

// Auto-run setup when imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  setupMissingTables().catch(console.error);
}
