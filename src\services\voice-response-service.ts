import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { SystemLogsService } from './system-logs-service';

/**
 * Voice Response Service
 * Text-to-speech functionality with natural voice responses
 * Similar to ChatGPT's voice output capabilities
 */

export interface VoiceSettings {
  voice: SpeechSynthesisVoice | null;
  rate: number;
  pitch: number;
  volume: number;
  language: string;
}

export interface VoiceResponse {
  text: string;
  audioUrl?: string;
  duration?: number;
  voice?: string;
  settings?: VoiceSettings;
}

export interface SpeechOptions {
  text: string;
  voice?: string;
  rate?: number;
  pitch?: number;
  volume?: number;
  interrupt?: boolean;
  onStart?: () => void;
  onEnd?: () => void;
  onError?: (error: string) => void;
  onPause?: () => void;
  onResume?: () => void;
}

export class VoiceResponseService {
  private static synthesis: SpeechSynthesis | null = null;
  private static currentUtterance: SpeechSynthesisUtterance | null = null;
  private static isInitialized: boolean = false;
  private static isSpeaking: boolean = false;
  private static isPaused: boolean = false;
  private static availableVoices: SpeechSynthesisVoice[] = [];
  private static defaultSettings: VoiceSettings = {
    voice: null,
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    language: 'en-US'
  };
  private static userSettings: VoiceSettings = { ...this.defaultSettings };
  private static responseQueue: SpeechOptions[] = [];
  private static isProcessingQueue: boolean = false;

  // Initialize voice response service
  static async initialize(): Promise<boolean> {
    try {
      console.log('🔊 Initializing Voice Response Service...');

      // Check browser support
      if (!this.isBrowserSupported()) {
        throw new Error('Speech synthesis not supported in this browser');
      }

      this.synthesis = window.speechSynthesis;

      // Load available voices
      await this.loadAvailableVoices();

      // Load user preferences
      await this.loadUserVoicePreferences();

      // Set up event listeners
      this.setupEventListeners();

      this.isInitialized = true;
      console.log('✅ Voice Response Service initialized successfully');

      await SystemLogsService.info('voice', 'Voice response service initialized', 'Text-to-speech system ready');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Voice Response Service:', error);
      await SystemLogsService.error('voice', 'Voice response initialization failed', error.toString());
      return false;
    }
  }

  // Check if browser supports speech synthesis
  static isBrowserSupported(): boolean {
    return !!(window.speechSynthesis && window.SpeechSynthesisUtterance);
  }

  // Speak text with options
  static async speak(options: SpeechOptions): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      if (!this.synthesis) {
        throw new Error('Speech synthesis not available');
      }

      // If interrupt is true, stop current speech
      if (options.interrupt && this.isSpeaking) {
        this.stop();
      }

      // Add to queue if currently speaking and not interrupting
      if (this.isSpeaking && !options.interrupt) {
        this.responseQueue.push(options);
        this.processQueue();
        return true;
      }

      return await this.speakNow(options);
    } catch (error) {
      console.error('Error speaking text:', error);
      options.onError?.(error.toString());
      return false;
    }
  }

  // Speak text immediately
  private static async speakNow(options: SpeechOptions): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (!this.synthesis) {
          resolve(false);
          return;
        }

        const utterance = new SpeechSynthesisUtterance(options.text);

        // Apply voice settings
        this.applyVoiceSettings(utterance, options);

        // Set up event handlers
        utterance.onstart = () => {
          this.isSpeaking = true;
          this.isPaused = false;
          this.currentUtterance = utterance;
          options.onStart?.();
          console.log('🔊 Speech started:', options.text.substring(0, 50) + '...');
        };

        utterance.onend = () => {
          this.isSpeaking = false;
          this.isPaused = false;
          this.currentUtterance = null;
          options.onEnd?.();
          console.log('🔊 Speech ended');
          
          // Process next item in queue
          this.processQueue();
          
          // Log the speech
          this.logSpeechEvent(options.text, 'completed');
          
          resolve(true);
        };

        utterance.onerror = (event) => {
          this.isSpeaking = false;
          this.isPaused = false;
          this.currentUtterance = null;
          const error = `Speech error: ${event.error}`;
          console.error('🔊', error);
          options.onError?.(error);
          
          this.logSpeechEvent(options.text, 'error', error);
          
          resolve(false);
        };

        utterance.onpause = () => {
          this.isPaused = true;
          options.onPause?.();
          console.log('🔊 Speech paused');
        };

        utterance.onresume = () => {
          this.isPaused = false;
          options.onResume?.();
          console.log('🔊 Speech resumed');
        };

        // Start speaking
        this.synthesis.speak(utterance);

      } catch (error) {
        console.error('Error in speakNow:', error);
        options.onError?.(error.toString());
        resolve(false);
      }
    });
  }

  // Apply voice settings to utterance
  private static applyVoiceSettings(utterance: SpeechSynthesisUtterance, options: SpeechOptions): void {
    // Set voice
    if (options.voice) {
      const voice = this.availableVoices.find(v => 
        v.name === options.voice || v.lang === options.voice
      );
      if (voice) {
        utterance.voice = voice;
      }
    } else if (this.userSettings.voice) {
      utterance.voice = this.userSettings.voice;
    }

    // Set speech parameters
    utterance.rate = options.rate ?? this.userSettings.rate;
    utterance.pitch = options.pitch ?? this.userSettings.pitch;
    utterance.volume = options.volume ?? this.userSettings.volume;
    utterance.lang = this.userSettings.language;
  }

  // Stop current speech
  static stop(): void {
    if (this.synthesis && this.isSpeaking) {
      this.synthesis.cancel();
      this.isSpeaking = false;
      this.isPaused = false;
      this.currentUtterance = null;
      this.responseQueue = []; // Clear queue
      console.log('🔊 Speech stopped');
    }
  }

  // Pause current speech
  static pause(): void {
    if (this.synthesis && this.isSpeaking && !this.isPaused) {
      this.synthesis.pause();
      console.log('🔊 Speech paused');
    }
  }

  // Resume paused speech
  static resume(): void {
    if (this.synthesis && this.isPaused) {
      this.synthesis.resume();
      console.log('🔊 Speech resumed');
    }
  }

  // Process speech queue
  private static async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.responseQueue.length === 0 || this.isSpeaking) {
      return;
    }

    this.isProcessingQueue = true;

    while (this.responseQueue.length > 0 && !this.isSpeaking) {
      const nextOptions = this.responseQueue.shift();
      if (nextOptions) {
        await this.speakNow(nextOptions);
        // Wait a bit between queue items
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    this.isProcessingQueue = false;
  }

  // Load available voices
  private static async loadAvailableVoices(): Promise<void> {
    return new Promise((resolve) => {
      const loadVoices = () => {
        this.availableVoices = this.synthesis?.getVoices() || [];
        
        if (this.availableVoices.length > 0) {
          // Set default voice (prefer English voices)
          const englishVoice = this.availableVoices.find(voice => 
            voice.lang.startsWith('en') && voice.default
          ) || this.availableVoices.find(voice => 
            voice.lang.startsWith('en')
          ) || this.availableVoices[0];

          if (englishVoice) {
            this.defaultSettings.voice = englishVoice;
            this.userSettings.voice = englishVoice;
          }

          console.log(`🔊 Loaded ${this.availableVoices.length} voices`);
          resolve();
        } else {
          // Voices might not be loaded yet, try again
          setTimeout(loadVoices, 100);
        }
      };

      // Some browsers load voices asynchronously
      if (this.synthesis) {
        this.synthesis.onvoiceschanged = loadVoices;
        loadVoices(); // Try immediately as well
      } else {
        resolve();
      }
    });
  }

  // Load user voice preferences
  private static async loadUserVoicePreferences(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return;

      const { data: preferences } = await supabase
        .from('voice_user_preferences')
        .select('*')
        .eq('user_id', currentUser.user_id)
        .single();

      if (preferences) {
        // Find the preferred voice
        const preferredVoice = this.availableVoices.find(voice => 
          voice.name === preferences.preferred_voice_name ||
          voice.lang === preferences.preferred_language
        );

        this.userSettings = {
          voice: preferredVoice || this.defaultSettings.voice,
          rate: preferences.voice_speed || this.defaultSettings.rate,
          pitch: preferences.voice_pitch || this.defaultSettings.pitch,
          volume: preferences.voice_volume || this.defaultSettings.volume,
          language: preferences.preferred_language || this.defaultSettings.language
        };

        console.log('🔊 User voice preferences loaded');
      }
    } catch (error) {
      console.warn('Could not load user voice preferences:', error);
    }
  }

  // Set up event listeners
  private static setupEventListeners(): void {
    // Listen for visibility changes to handle browser speech limitations
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.isSpeaking) {
        // Browser might pause speech when tab is hidden
        console.log('🔊 Tab hidden, speech might be affected');
      }
    });
  }

  // Update voice settings
  static updateSettings(newSettings: Partial<VoiceSettings>): void {
    this.userSettings = { ...this.userSettings, ...newSettings };
    console.log('🔊 Voice settings updated');
  }

  // Get current settings
  static getSettings(): VoiceSettings {
    return { ...this.userSettings };
  }

  // Get available voices
  static getAvailableVoices(): SpeechSynthesisVoice[] {
    return [...this.availableVoices];
  }

  // Get voices by language
  static getVoicesByLanguage(language: string): SpeechSynthesisVoice[] {
    return this.availableVoices.filter(voice => 
      voice.lang.startsWith(language.substring(0, 2))
    );
  }

  // Check if currently speaking
  static isCurrentlySpeaking(): boolean {
    return this.isSpeaking;
  }

  // Check if paused
  static isCurrentlyPaused(): boolean {
    return this.isPaused;
  }

  // Get queue length
  static getQueueLength(): number {
    return this.responseQueue.length;
  }

  // Clear queue
  static clearQueue(): void {
    this.responseQueue = [];
    console.log('🔊 Speech queue cleared');
  }

  // Speak response with smart formatting
  static async speakResponse(response: VoiceResponse): Promise<boolean> {
    const formattedText = this.formatTextForSpeech(response.text);
    
    return await this.speak({
      text: formattedText,
      voice: response.voice,
      interrupt: true,
      onStart: () => {
        console.log('🔊 Speaking response:', formattedText.substring(0, 50) + '...');
      },
      onEnd: () => {
        console.log('🔊 Response completed');
      },
      onError: (error) => {
        console.error('🔊 Response error:', error);
      }
    });
  }

  // Format text for better speech output
  private static formatTextForSpeech(text: string): string {
    return text
      // Remove markdown formatting
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/`(.*?)`/g, '$1')
      // Replace common abbreviations
      .replace(/\bCTN\b/g, 'C T N')
      .replace(/\bAPI\b/g, 'A P I')
      .replace(/\bUI\b/g, 'U I')
      .replace(/\bURL\b/g, 'U R L')
      // Add pauses for better flow
      .replace(/\. /g, '. ')
      .replace(/\? /g, '? ')
      .replace(/! /g, '! ')
      // Clean up extra spaces
      .replace(/\s+/g, ' ')
      .trim();
  }

  // Test speech synthesis
  static async testSpeech(): Promise<{
    supported: boolean;
    voicesAvailable: number;
    canSpeak: boolean;
    error?: string;
  }> {
    try {
      const supported = this.isBrowserSupported();
      const voicesAvailable = this.availableVoices.length;
      
      if (!supported) {
        return {
          supported: false,
          voicesAvailable: 0,
          canSpeak: false,
          error: 'Speech synthesis not supported'
        };
      }

      // Test speaking a short phrase
      const canSpeak = await this.speak({
        text: 'Voice test successful',
        volume: 0.1, // Low volume for test
        interrupt: true
      });

      return {
        supported,
        voicesAvailable,
        canSpeak,
        error: canSpeak ? undefined : 'Could not speak test phrase'
      };
    } catch (error) {
      return {
        supported: false,
        voicesAvailable: 0,
        canSpeak: false,
        error: error.toString()
      };
    }
  }

  // Log speech events
  private static async logSpeechEvent(text: string, status: string, error?: string): Promise<void> {
    try {
      await SystemLogsService.info(
        'voice',
        'Speech synthesis event',
        `Status: ${status} | Text: "${text.substring(0, 100)}..."`,
        {
          text_length: text.length,
          status,
          error,
          voice: this.userSettings.voice?.name,
          settings: {
            rate: this.userSettings.rate,
            pitch: this.userSettings.pitch,
            volume: this.userSettings.volume
          }
        }
      );
    } catch (logError) {
      console.error('Error logging speech event:', logError);
    }
  }

  // Save user voice preferences
  static async saveUserPreferences(): Promise<boolean> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return false;

      const { error } = await supabase
        .from('voice_user_preferences')
        .upsert({
          user_id: currentUser.user_id,
          preferred_language: this.userSettings.language,
          voice_speed: this.userSettings.rate,
          voice_pitch: this.userSettings.pitch,
          voice_volume: this.userSettings.volume,
          preferred_voice_name: this.userSettings.voice?.name,
          updated_at: new Date().toISOString()
        });

      if (error) {
        throw error;
      }

      console.log('🔊 Voice preferences saved');
      return true;
    } catch (error) {
      console.error('Error saving voice preferences:', error);
      return false;
    }
  }

  // Get speech statistics
  static async getSpeechStats(days: number = 7): Promise<any> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return null;

      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      const { data: logs } = await supabase
        .from('system_logs')
        .select('*')
        .eq('category', 'voice')
        .ilike('message', '%speech synthesis%')
        .gte('created_at', dateFrom.toISOString());

      if (logs) {
        const totalSpeechEvents = logs.length;
        const successfulEvents = logs.filter(log => 
          log.details?.includes('completed')
        ).length;
        const errorEvents = logs.filter(log => 
          log.details?.includes('error')
        ).length;

        return {
          totalEvents: totalSpeechEvents,
          successfulEvents,
          errorEvents,
          successRate: totalSpeechEvents > 0 ? (successfulEvents / totalSpeechEvents * 100).toFixed(1) : 0,
          period: `${days} days`
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting speech stats:', error);
      return null;
    }
  }

  // Cleanup resources
  static cleanup(): void {
    this.stop();
    this.clearQueue();
    this.isInitialized = false;
    this.synthesis = null;
    this.currentUtterance = null;
    console.log('🔊 Voice response service cleaned up');
  }
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    speechSynthesis: SpeechSynthesis;
    SpeechSynthesisUtterance: typeof SpeechSynthesisUtterance;
  }
}

export default VoiceResponseService;
