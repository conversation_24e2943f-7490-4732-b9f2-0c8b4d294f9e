-- Setup API Key for Enhanced <PERSON><PERSON>hain Features
-- Run this in your Supabase SQL Editor

-- Create api_keys table if it doesn't exist
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    provider TEXT UNIQUE NOT NULL,
    api_key TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on api_keys table
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access only
CREATE POLICY "Admin can manage API keys" ON api_keys
FOR ALL TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('admin', 'super_admin')
    )
);

-- Insert OpenAI API key (REPLACE WITH YOUR ACTUAL KEY)
INSERT INTO api_keys (provider, api_key, is_active) 
VALUES (
    'openai', 
    'sk-your-actual-openai-api-key-here', -- REPLACE THIS WITH YOUR REAL API KEY
    true
) ON CONFLICT (provider) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Verify the API key was inserted
SELECT provider, 
       LEFT(api_key, 10) || '...' as api_key_preview,
       is_active, 
       created_at 
FROM api_keys 
WHERE provider = 'openai';

-- Success message
SELECT 'API key setup complete! You can now use the enhanced AI features.' as status;
