-- 🧪 COMPREHENSIVE AUTHENTICATION SYSTEM TEST
-- Run this script to verify the authentication system is working

-- ========================================
-- TEST 1: VERIFY TABLES EXIST
-- ========================================

SELECT 'TEST 1: Checking if tables exist...' as test_name;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') 
        THEN '✅ profiles table exists'
        ELSE '❌ profiles table missing'
    END as profiles_status;

SELECT 
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'departments') 
        THEN '✅ departments table exists'
        ELSE '❌ departments table missing'
    END as departments_status;

-- ========================================
-- TEST 2: VERIFY DEPARTMENTS DATA
-- ========================================

SELECT 'TEST 2: Checking departments data...' as test_name;

SELECT 
    id,
    name,
    description,
    created_at
FROM public.departments
ORDER BY name;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM public.departments) >= 6 
        THEN '✅ Departments populated correctly'
        ELSE '❌ Missing departments data'
    END as departments_data_status;

-- ========================================
-- TEST 3: VERIFY RLS POLICIES
-- ========================================

SELECT 'TEST 3: Checking RLS policies...' as test_name;

SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename IN ('profiles', 'departments')
ORDER BY tablename, policyname;

-- ========================================
-- TEST 4: VERIFY TRIGGERS AND FUNCTIONS
-- ========================================

SELECT 'TEST 4: Checking triggers and functions...' as test_name;

SELECT 
    trigger_name,
    event_manipulation,
    action_timing,
    action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'on_auth_user_created';

SELECT 
    routine_name,
    routine_type,
    security_type
FROM information_schema.routines 
WHERE routine_name = 'handle_new_user';

-- ========================================
-- TEST 5: VERIFY EXISTING USERS
-- ========================================

SELECT 'TEST 5: Checking existing users...' as test_name;

SELECT 
    au.id,
    au.email,
    au.created_at as auth_created,
    p.full_name,
    p.role,
    p.status,
    d.name as department_name
FROM auth.users au
LEFT JOIN public.profiles p ON au.id = p.id
LEFT JOIN public.departments d ON p.department_id = d.id
ORDER BY au.created_at DESC;

-- ========================================
-- TEST 6: TEST PROFILE CREATION
-- ========================================

SELECT 'TEST 6: Testing profile creation function...' as test_name;

-- This would normally be triggered by user signup
-- We can test the function directly if needed

-- ========================================
-- TEST 7: VERIFY PERMISSIONS
-- ========================================

SELECT 'TEST 7: Checking table permissions...' as test_name;

SELECT 
    grantee,
    table_name,
    privilege_type,
    is_grantable
FROM information_schema.table_privileges 
WHERE table_name IN ('profiles', 'departments')
AND grantee IN ('authenticated', 'anon', 'service_role')
ORDER BY table_name, grantee, privilege_type;

-- ========================================
-- TEST 8: DEPARTMENT FETCH TEST
-- ========================================

SELECT 'TEST 8: Testing department fetch (simulating frontend query)...' as test_name;

-- This simulates the exact query used by the frontend
SELECT 
    id,
    name,
    description
FROM public.departments
ORDER BY name;

-- ========================================
-- TEST 9: PROFILE UPSERT TEST
-- ========================================

SELECT 'TEST 9: Testing profile upsert capability...' as test_name;

-- Check if we can insert/update profiles
SELECT 
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'profiles' 
            AND column_name = 'id'
        ) 
        THEN '✅ Profile table structure is correct'
        ELSE '❌ Profile table structure is incorrect'
    END as profile_structure_status;

-- ========================================
-- TEST 10: FINAL SYSTEM STATUS
-- ========================================

SELECT 'TEST 10: Final system status...' as test_name;

SELECT 
    'Authentication System Status' as component,
    CASE 
        WHEN (
            EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'profiles') AND
            EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'departments') AND
            (SELECT COUNT(*) FROM public.departments) >= 6 AND
            EXISTS (SELECT 1 FROM information_schema.triggers WHERE trigger_name = 'on_auth_user_created') AND
            EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'handle_new_user')
        )
        THEN '✅ SYSTEM READY - Authentication system is fully operational'
        ELSE '❌ SYSTEM ERROR - Authentication system needs attention'
    END as status;

-- ========================================
-- TROUBLESHOOTING QUERIES
-- ========================================

SELECT 'TROUBLESHOOTING: Common issues check...' as test_name;

-- Check for common issues
SELECT 
    'RLS Status' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles') > 0
        THEN '✅ RLS policies exist for profiles'
        ELSE '❌ Missing RLS policies for profiles'
    END as status
UNION ALL
SELECT 
    'Department Data' as check_type,
    CASE 
        WHEN (SELECT COUNT(*) FROM public.departments WHERE name = 'IT Department') > 0
        THEN '✅ Default departments exist'
        ELSE '❌ Missing default departments'
    END as status
UNION ALL
SELECT 
    'Trigger Function' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'handle_new_user')
        THEN '✅ Profile creation trigger function exists'
        ELSE '❌ Missing profile creation trigger function'
    END as status;
