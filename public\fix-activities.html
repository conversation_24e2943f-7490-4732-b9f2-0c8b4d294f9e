<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix System Activities Database</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Comprehensive RLS Database Fix</h1>
        <p>This tool will fix the system_activities table, departments table, and all RLS policies to resolve 403 Forbidden errors.</p>
        
        <div id="status"></div>
        
        <button id="fixBtn" class="button" onclick="runFix()">
            🚀 Fix Database
        </button>
        
        <button id="testBtn" class="button" onclick="testSystem()" disabled>
            🧪 Test System
        </button>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        async function runFix() {
            const fixBtn = document.getElementById('fixBtn');
            const testBtn = document.getElementById('testBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Fixing...';
            
            try {
                log('🚀 Starting system activities database fix...');
                
                // Step 1: Fix System Activities
                log('🗑️ Fixing system_activities table and RLS...');
                const { error: activitiesError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Drop existing policies to start fresh
                        DROP POLICY IF EXISTS "Admin can view all activities" ON system_activities;
                        DROP POLICY IF EXISTS "Users can view own activities" ON system_activities;
                        DROP POLICY IF EXISTS "Authenticated users can insert activities" ON system_activities;
                        DROP POLICY IF EXISTS "Admin can update activities" ON system_activities;
                        DROP POLICY IF EXISTS "Admin can delete activities" ON system_activities;
                        DROP POLICY IF EXISTS "Service role can manage activities" ON system_activities;
                        DROP POLICY IF EXISTS "System can insert activities" ON system_activities;

                        -- Drop and recreate table with correct schema
                        DROP TABLE IF EXISTS system_activities CASCADE;

                        -- Create system_activities table with standardized schema
                        CREATE TABLE system_activities (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
                          action VARCHAR(100) NOT NULL,
                          description TEXT NOT NULL,
                          entity_type VARCHAR(100),
                          entity_id UUID,
                          metadata JSONB DEFAULT '{}',
                          ip_address INET,
                          user_agent TEXT,
                          session_id VARCHAR(255),
                          severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical', 'success')),
                          category VARCHAR(50) DEFAULT 'general' CHECK (category IN ('general', 'auth', 'project', 'user', 'system', 'database', 'api', 'security')),
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );

                        -- Create indexes for performance
                        CREATE INDEX idx_system_activities_user_id ON system_activities(user_id);
                        CREATE INDEX idx_system_activities_action ON system_activities(action);
                        CREATE INDEX idx_system_activities_created_at ON system_activities(created_at DESC);
                        CREATE INDEX idx_system_activities_user_date ON system_activities(user_id, created_at DESC);
                        CREATE INDEX idx_system_activities_entity ON system_activities(entity_type, entity_id);

                        -- Enable RLS
                        ALTER TABLE system_activities ENABLE ROW LEVEL SECURITY;

                        -- Create comprehensive RLS policies
                        CREATE POLICY "system_activities_select_admin" ON system_activities
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND profiles.role = 'admin'
                            )
                          );

                        CREATE POLICY "system_activities_select_own" ON system_activities
                          FOR SELECT USING (user_id = auth.uid());

                        CREATE POLICY "system_activities_select_manager" ON system_activities
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND profiles.role IN ('manager', 'staff-admin')
                            )
                          );

                        CREATE POLICY "system_activities_insert_authenticated" ON system_activities
                          FOR INSERT WITH CHECK (
                            auth.uid() IS NOT NULL AND
                            (user_id = auth.uid() OR user_id IS NULL)
                          );

                        CREATE POLICY "system_activities_insert_service" ON system_activities
                          FOR INSERT WITH CHECK (
                            current_setting('role') = 'service_role' OR
                            auth.uid() IS NOT NULL
                          );

                        CREATE POLICY "system_activities_update_admin" ON system_activities
                          FOR UPDATE USING (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND profiles.role = 'admin'
                            )
                          );

                        CREATE POLICY "system_activities_delete_admin" ON system_activities
                          FOR DELETE USING (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND profiles.role = 'admin'
                            )
                          );

                        -- Grant permissions
                        GRANT SELECT, INSERT ON system_activities TO authenticated;
                        GRANT ALL ON system_activities TO service_role;
                    `
                });

                if (activitiesError) {
                    throw new Error('Failed to fix system activities: ' + activitiesError.message);
                }

                log('✅ System activities fixed successfully');

                // Step 2: Fix Departments
                log('🏢 Fixing departments table and RLS...');
                const { error: departmentsError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Drop existing department policies
                        DROP POLICY IF EXISTS "departments_select_all" ON departments;
                        DROP POLICY IF EXISTS "departments_admin_all" ON departments;
                        DROP POLICY IF EXISTS "departments_service_role" ON departments;
                        DROP POLICY IF EXISTS "departments_select" ON departments;
                        DROP POLICY IF EXISTS "departments_insert" ON departments;
                        DROP POLICY IF EXISTS "departments_update" ON departments;
                        DROP POLICY IF EXISTS "departments_delete" ON departments;

                        -- Create departments table if it doesn't exist
                        CREATE TABLE IF NOT EXISTS departments (
                          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
                          name TEXT NOT NULL UNIQUE,
                          description TEXT,
                          manager_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
                          budget DECIMAL DEFAULT 0,
                          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                        );

                        -- Enable RLS on departments
                        ALTER TABLE departments ENABLE ROW LEVEL SECURITY;

                        -- Create simple, non-recursive department policies
                        CREATE POLICY "departments_select_all" ON departments
                          FOR SELECT USING (true);

                        CREATE POLICY "departments_insert_admin_manager" ON departments
                          FOR INSERT WITH CHECK (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND profiles.role IN ('admin', 'manager')
                            )
                          );

                        CREATE POLICY "departments_update_admin_manager" ON departments
                          FOR UPDATE USING (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND (
                                profiles.role = 'admin' OR
                                profiles.id = departments.manager_id
                              )
                            )
                          );

                        CREATE POLICY "departments_delete_admin" ON departments
                          FOR DELETE USING (
                            EXISTS (
                              SELECT 1 FROM profiles
                              WHERE profiles.id = auth.uid()
                              AND profiles.role = 'admin'
                            )
                          );

                        CREATE POLICY "departments_service_role" ON departments
                          FOR ALL USING (current_setting('role') = 'service_role')
                          WITH CHECK (current_setting('role') = 'service_role');

                        -- Grant permissions
                        GRANT SELECT ON departments TO authenticated;
                        GRANT INSERT, UPDATE ON departments TO authenticated;
                        GRANT ALL ON departments TO service_role;

                        -- Insert default departments if they don't exist
                        INSERT INTO departments (name, description) VALUES
                          ('Engineering', 'Engineering and technical development'),
                          ('Operations', 'Operations and project management'),
                          ('Finance', 'Financial management and accounting'),
                          ('Human Resources', 'HR and staff management'),
                          ('Administration', 'General administration')
                        ON CONFLICT (name) DO NOTHING;
                    `
                });

                if (departmentsError) {
                    throw new Error('Failed to fix departments: ' + departmentsError.message);
                }

                log('✅ Departments fixed successfully');

                // Step 3: Create helper functions
                
                // Step 4: Create helper function
                log('⚙️ Creating helper functions...');
                const { error: functionError } = await supabase.rpc('exec_sql', {
                    sql: `
                        CREATE OR REPLACE FUNCTION log_system_activity(
                            p_user_id UUID DEFAULT NULL,
                            p_action VARCHAR(100) DEFAULT 'system_action',
                            p_description TEXT DEFAULT 'System activity',
                            p_metadata JSONB DEFAULT '{}',
                            p_severity VARCHAR(20) DEFAULT 'info',
                            p_category VARCHAR(50) DEFAULT 'general'
                        )
                        RETURNS UUID AS $$
                        DECLARE
                            activity_id UUID;
                        BEGIN
                            INSERT INTO system_activities (
                                user_id,
                                action,
                                description,
                                entity_type,
                                entity_id,
                                metadata,
                                severity,
                                category,
                                created_at,
                                updated_at
                            ) VALUES (
                                p_user_id,
                                p_action,
                                p_description,
                                NULL,
                                NULL,
                                p_metadata,
                                p_severity,
                                p_category,
                                NOW(),
                                NOW()
                            ) RETURNING id INTO activity_id;

                            RETURN activity_id;
                        END;
                        $$ LANGUAGE plpgsql SECURITY DEFINER;
                        
                        GRANT SELECT, INSERT ON system_activities TO authenticated;
                        GRANT EXECUTE ON FUNCTION log_system_activity TO authenticated;
                    `
                });
                
                if (functionError) {
                    throw new Error('Failed to create functions: ' + functionError.message);
                }
                
                log('✅ Database fix completed successfully!');
                showStatus('✅ Database fixed successfully! You can now test the system.', 'success');
                
                testBtn.disabled = false;
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                showStatus('❌ Fix failed: ' + error.message, 'error');
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🚀 Fix Database';
            }
        }
        
        async function testSystem() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            
            try {
                log('🧪 Testing system activities...');
                
                // Test 1: Insert activity
                const { data: insertData, error: insertError } = await supabase
                    .from('system_activities')
                    .insert({
                        action: 'test_activity',
                        description: 'Test activity from fix tool',
                        metadata: { test: true, timestamp: new Date().toISOString() },
                        severity: 'info',
                        category: 'system'
                    })
                    .select()
                    .single();
                
                if (insertError) {
                    throw new Error('Insert test failed: ' + insertError.message);
                }
                
                log('✅ Insert test passed: ' + insertData.id);
                
                // Test 2: Query activities
                const { data: queryData, error: queryError } = await supabase
                    .from('system_activities')
                    .select('*')
                    .limit(5);
                
                if (queryError) {
                    throw new Error('Query test failed: ' + queryError.message);
                }
                
                log('✅ Query test passed, found ' + queryData.length + ' activities');
                
                // Test 3: Use function
                const { data: functionData, error: functionError } = await supabase
                    .rpc('log_system_activity', {
                        p_action: 'function_test',
                        p_description: 'Testing log_system_activity function',
                        p_metadata: { function_test: true },
                        p_severity: 'success',
                        p_category: 'system'
                    });
                
                if (functionError) {
                    throw new Error('Function test failed: ' + functionError.message);
                }
                
                log('✅ Function test passed: ' + functionData);
                log('🎉 All tests passed! System is working correctly.');
                showStatus('🎉 All tests passed! System activities are working correctly.', 'success');
                
            } catch (error) {
                log('❌ Test failed: ' + error.message);
                showStatus('❌ Test failed: ' + error.message, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 Test System';
            }
        }
        
        // Make functions global
        window.runFix = runFix;
        window.testSystem = testSystem;
    </script>
</body>
</html>
