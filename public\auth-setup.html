<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Setup - CTN Nigeria</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2d3748;
            margin: 0;
            font-size: 2.5rem;
        }
        .auth-section {
            background: #f7fafc;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4299e1;
        }
        .button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
            width: 100%;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }
        .button.success {
            background: linear-gradient(135deg, #38a169, #2f855a);
        }
        .button.danger {
            background: linear-gradient(135deg, #e53e3e, #c53030);
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #2d3748;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success {
            background: #c6f6d5;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }
        .error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #fc8181;
        }
        .warning {
            background: #fefcbf;
            color: #744210;
            border: 1px solid #f6e05e;
        }
        .info {
            background: #bee3f8;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }
        .user-info {
            background: #e6fffa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #81e6d9;
            margin: 15px 0;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 Authentication Setup</h1>
            <p>Set up authentication for full system access</p>
        </div>

        <div id="authStatus" class="status info">
            <strong>Status:</strong> Checking authentication...
        </div>

        <div id="userInfo" class="user-info hidden">
            <h3>👤 Current User</h3>
            <div id="userDetails"></div>
        </div>

        <!-- Sign In Form -->
        <div id="signInSection" class="auth-section">
            <h3>🔑 Sign In</h3>
            <form id="signInForm">
                <div class="form-group">
                    <label for="signInEmail">Email:</label>
                    <input type="email" id="signInEmail" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label for="signInPassword">Password:</label>
                    <input type="password" id="signInPassword" placeholder="Enter your password" required>
                </div>
                <button type="submit" class="button">🔑 Sign In</button>
            </form>
        </div>

        <!-- Sign Up Form -->
        <div id="signUpSection" class="auth-section">
            <h3>📝 Create Account</h3>
            <form id="signUpForm">
                <div class="form-group">
                    <label for="signUpEmail">Email:</label>
                    <input type="email" id="signUpEmail" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label for="signUpPassword">Password:</label>
                    <input type="password" id="signUpPassword" placeholder="Create a password (min 6 characters)" required minlength="6">
                </div>
                <div class="form-group">
                    <label for="fullName">Full Name:</label>
                    <input type="text" id="fullName" placeholder="Enter your full name" required>
                </div>
                <div class="form-group">
                    <label for="role">Role:</label>
                    <select id="role" required>
                        <option value="">Select your role</option>
                        <option value="admin">Admin</option>
                        <option value="manager">Manager</option>
                        <option value="staff-admin">Staff Admin</option>
                        <option value="hr">HR</option>
                        <option value="accountant">Accountant</option>
                        <option value="staff">Staff</option>
                    </select>
                </div>
                <button type="submit" class="button">📝 Create Account</button>
            </form>
        </div>

        <!-- Quick Demo Account -->
        <div id="demoSection" class="auth-section">
            <h3>🚀 Quick Demo</h3>
            <p>Create a demo account for testing:</p>
            <button id="createDemoBtn" class="button success">🎯 Create Demo Admin Account</button>
        </div>

        <!-- Authenticated Actions -->
        <div id="authenticatedSection" class="auth-section hidden">
            <h3>✅ Authenticated Actions</h3>
            <button id="testFeaturesBtn" class="button">🧪 Test All Features</button>
            <button id="createProfileBtn" class="button">👤 Create/Update Profile</button>
            <button id="signOutBtn" class="button danger">🚪 Sign Out</button>
        </div>

        <div id="results" class="hidden">
            <h3>📊 Test Results</h3>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        let currentUser = null;
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('authStatus');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }
        
        function showUserInfo(user, profile = null) {
            const userInfo = document.getElementById('userInfo');
            const userDetails = document.getElementById('userDetails');
            
            userDetails.innerHTML = `
                <p><strong>Email:</strong> ${user.email}</p>
                <p><strong>User ID:</strong> ${user.id}</p>
                <p><strong>Created:</strong> ${new Date(user.created_at).toLocaleString()}</p>
                ${profile ? `
                    <p><strong>Name:</strong> ${profile.full_name}</p>
                    <p><strong>Role:</strong> ${profile.role}</p>
                    <p><strong>Status:</strong> ${profile.status}</p>
                ` : '<p><em>Profile not created yet</em></p>'}
            `;
            
            userInfo.classList.remove('hidden');
        }
        
        function toggleSections(isAuthenticated) {
            document.getElementById('signInSection').classList.toggle('hidden', isAuthenticated);
            document.getElementById('signUpSection').classList.toggle('hidden', isAuthenticated);
            document.getElementById('demoSection').classList.toggle('hidden', isAuthenticated);
            document.getElementById('authenticatedSection').classList.toggle('hidden', !isAuthenticated);
        }
        
        async function checkAuthStatus() {
            try {
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error) {
                    showStatus('❌ Authentication check failed: ' + error.message, 'error');
                    return;
                }
                
                if (user) {
                    currentUser = user;
                    showStatus('✅ Authenticated successfully!', 'success');
                    
                    // Try to get profile
                    const { data: profile } = await supabase
                        .from('profiles')
                        .select('*')
                        .eq('user_id', user.id)
                        .single();
                    
                    showUserInfo(user, profile);
                    toggleSections(true);
                } else {
                    showStatus('⚠️ Not authenticated. Please sign in or create an account.', 'warning');
                    toggleSections(false);
                }
            } catch (err) {
                showStatus('❌ Error checking authentication: ' + err.message, 'error');
            }
        }
        
        // Sign In
        document.getElementById('signInForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('signInEmail').value;
            const password = document.getElementById('signInPassword').value;
            
            showStatus('🔄 Signing in...', 'info');
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email,
                    password
                });
                
                if (error) {
                    showStatus('❌ Sign in failed: ' + error.message, 'error');
                } else {
                    showStatus('✅ Signed in successfully!', 'success');
                    await checkAuthStatus();
                }
            } catch (err) {
                showStatus('❌ Sign in error: ' + err.message, 'error');
            }
        });
        
        // Sign Up
        document.getElementById('signUpForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('signUpEmail').value;
            const password = document.getElementById('signUpPassword').value;
            const fullName = document.getElementById('fullName').value;
            const role = document.getElementById('role').value;
            
            showStatus('🔄 Creating account...', 'info');
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email,
                    password,
                    options: {
                        data: {
                            full_name: fullName,
                            role: role
                        }
                    }
                });
                
                if (error) {
                    showStatus('❌ Sign up failed: ' + error.message, 'error');
                } else {
                    showStatus('✅ Account created successfully! Please check your email for verification.', 'success');
                    
                    // Create profile
                    if (data.user) {
                        await createProfile(data.user, fullName, role);
                    }
                }
            } catch (err) {
                showStatus('❌ Sign up error: ' + err.message, 'error');
            }
        });
        
        // Create Demo Account
        document.getElementById('createDemoBtn').addEventListener('click', async () => {
            const demoEmail = `demo.admin.${Date.now()}@ctnigeria.com`;
            const demoPassword = 'demo123456';
            
            showStatus('🔄 Creating demo account...', 'info');
            
            try {
                const { data, error } = await supabase.auth.signUp({
                    email: demoEmail,
                    password: demoPassword,
                    options: {
                        data: {
                            full_name: 'Demo Admin User',
                            role: 'admin'
                        }
                    }
                });
                
                if (error) {
                    showStatus('❌ Demo account creation failed: ' + error.message, 'error');
                } else {
                    showStatus(`✅ Demo account created! Email: ${demoEmail}, Password: ${demoPassword}`, 'success');
                    
                    // Create profile
                    if (data.user) {
                        await createProfile(data.user, 'Demo Admin User', 'admin');
                    }
                    
                    // Auto sign in
                    setTimeout(async () => {
                        const { error: signInError } = await supabase.auth.signInWithPassword({
                            email: demoEmail,
                            password: demoPassword
                        });
                        
                        if (!signInError) {
                            await checkAuthStatus();
                        }
                    }, 2000);
                }
            } catch (err) {
                showStatus('❌ Demo account error: ' + err.message, 'error');
            }
        });
        
        // Create Profile
        async function createProfile(user, fullName, role) {
            try {
                const { error } = await supabase
                    .from('profiles')
                    .upsert({
                        user_id: user.id,
                        full_name: fullName,
                        email: user.email,
                        role: role,
                        account_type: role,
                        status: 'active',
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    });
                
                if (error) {
                    console.warn('Profile creation warning:', error.message);
                } else {
                    console.log('Profile created successfully');
                }
            } catch (err) {
                console.warn('Profile creation error:', err.message);
            }
        }
        
        // Test Features
        document.getElementById('testFeaturesBtn').addEventListener('click', async () => {
            showStatus('🧪 Testing authenticated features...', 'info');
            
            const results = document.getElementById('results');
            const resultsContent = document.getElementById('resultsContent');
            
            let testResults = [];
            
            // Test authenticated access to tables
            const tables = ['profiles', 'projects', 'memos', 'reports', 'departments'];
            
            for (const table of tables) {
                try {
                    const { data, error } = await supabase
                        .from(table)
                        .select('*')
                        .limit(5);
                    
                    if (error) {
                        testResults.push(`⚠️ ${table}: ${error.message}`);
                    } else {
                        testResults.push(`✅ ${table}: Accessible (${data?.length || 0} records)`);
                    }
                } catch (err) {
                    testResults.push(`❌ ${table}: ${err.message}`);
                }
            }
            
            // Test creating a project
            try {
                const { data, error } = await supabase.rpc('create_project', {
                    p_name: 'Test Project ' + Date.now(),
                    p_description: 'Test project created via authentication',
                    p_status: 'planning',
                    p_priority: 'low',
                    p_created_by: currentUser.id
                });
                
                if (error) {
                    testResults.push(`⚠️ Project Creation: ${error.message}`);
                } else {
                    testResults.push(`✅ Project Creation: Success (ID: ${data})`);
                }
            } catch (err) {
                testResults.push(`❌ Project Creation: ${err.message}`);
            }
            
            resultsContent.innerHTML = testResults.map(result => `<p>${result}</p>`).join('');
            results.classList.remove('hidden');
            
            showStatus('✅ Feature testing completed!', 'success');
        });
        
        // Create/Update Profile
        document.getElementById('createProfileBtn').addEventListener('click', async () => {
            if (!currentUser) return;
            
            showStatus('👤 Creating/updating profile...', 'info');
            
            try {
                const { error } = await supabase
                    .from('profiles')
                    .upsert({
                        user_id: currentUser.id,
                        full_name: currentUser.user_metadata?.full_name || 'User',
                        email: currentUser.email,
                        role: currentUser.user_metadata?.role || 'staff',
                        account_type: currentUser.user_metadata?.role || 'staff',
                        status: 'active',
                        updated_at: new Date().toISOString()
                    });
                
                if (error) {
                    showStatus('❌ Profile update failed: ' + error.message, 'error');
                } else {
                    showStatus('✅ Profile updated successfully!', 'success');
                    await checkAuthStatus();
                }
            } catch (err) {
                showStatus('❌ Profile update error: ' + err.message, 'error');
            }
        });
        
        // Sign Out
        document.getElementById('signOutBtn').addEventListener('click', async () => {
            showStatus('🔄 Signing out...', 'info');
            
            try {
                const { error } = await supabase.auth.signOut();
                
                if (error) {
                    showStatus('❌ Sign out failed: ' + error.message, 'error');
                } else {
                    showStatus('✅ Signed out successfully!', 'success');
                    currentUser = null;
                    document.getElementById('userInfo').classList.add('hidden');
                    document.getElementById('results').classList.add('hidden');
                    toggleSections(false);
                }
            } catch (err) {
                showStatus('❌ Sign out error: ' + err.message, 'error');
            }
        });
        
        // Initialize
        checkAuthStatus();
    </script>
</body>
</html>
