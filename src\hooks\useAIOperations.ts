import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';
import type { AIR<PERSON>ult, AIKnowledgeBase, DocumentAnalysis } from '@/types/ai';

export const useAIOperations = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { userProfile } = useAuth();

  // AI Results from the dedicated ai_results table
  const useAIResults = () => {
    return useQuery({
      queryKey: ['ai_results'],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('ai_results')
          .select('*')
          .order('created_at', { ascending: false });
        if (error) throw error;
        return data as AIResult[];
      },
    });
  };

  const useCreateAIResult = () => {
    return useMutation({
      mutationFn: async (newResult: Omit<AIResult, 'id' | 'created_at' | 'updated_at'>) => {
        const { data, error } = await supabase
          .from('ai_results')
          .insert({
            query_text: newResult.query_text,
            result_data: newResult.result_data,
            model_used: newResult.model_used,
            created_by: newResult.created_by || userProfile?.id
          })
          .select()
          .single();
        if (error) throw error;
        return data;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['ai_results'] });
        toast({
          title: 'Success',
          description: 'AI analysis saved successfully',
        });
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      },
    });
  };

  const useDeleteAIResult = () => {
    return useMutation({
      mutationFn: async (id: string) => {
        const { error } = await supabase
          .from('ai_results')
          .delete()
          .eq('id', id);
        if (error) throw error;
        return id;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['ai_results'] });
        toast({
          title: 'Success',
          description: 'AI result deleted successfully',
        });
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      },
    });
  };

  // Document Analysis from document_analysis table
  const useDocumentAnalysis = () => {
    return useQuery({
      queryKey: ['document_analysis'],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('document_analysis')
          .select('*')
          .order('created_at', { ascending: false });
        if (error) throw error;
        return data as DocumentAnalysis[];
      },
    });
  };

  const useCreateDocumentAnalysis = () => {
    return useMutation({
      mutationFn: async (newAnalysis: Omit<DocumentAnalysis, 'id' | 'created_at' | 'updated_at'>) => {
        const { data, error } = await supabase
          .from('document_analysis')
          .insert({
            file_path: newAnalysis.file_path,
            file_name: newAnalysis.file_name,
            file_type: newAnalysis.file_type,
            file_size: newAnalysis.file_size,
            analysis_status: newAnalysis.analysis_status || 'pending',
            analysis_result: newAnalysis.analysis_result,
            created_by: newAnalysis.created_by || userProfile?.id
          })
          .select()
          .single();
        if (error) throw error;
        return data;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['document_analysis'] });
        toast({
          title: 'Success',
          description: 'Document analysis saved successfully',
        });
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      },
    });
  };

  // Knowledge Base from ai_documents table
  const useKnowledgeBase = () => {
    return useQuery({
      queryKey: ['ai_knowledge_base'],
      queryFn: async () => {
        const { data, error } = await supabase
          .from('ai_documents')
          .select('*')
          .order('created_at', { ascending: false });
        if (error) throw error;
        return data as AIKnowledgeBase[];
      },
    });
  };

  const useCreateKnowledgeEntry = () => {
    return useMutation({
      mutationFn: async (newEntry: Omit<AIKnowledgeBase, 'id' | 'created_at' | 'updated_at'>) => {
        const { data, error } = await supabase
          .from('ai_documents')
          .insert({
            title: newEntry.title,
            content: newEntry.content,
            document_type: newEntry.category,
            category: newEntry.category,
            tags: newEntry.tags || [],
            analysis: { tags: newEntry.tags || [] },
            created_by: newEntry.created_by || userProfile?.id
          })
          .select()
          .single();
        if (error) throw error;
        return data;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['ai_knowledge_base'] });
        toast({
          title: 'Success',
          description: 'Knowledge base entry added successfully',
        });
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      },
    });
  };

  // AI Interactions for chat history
  const useAIInteractions = () => {
    return useQuery({
      queryKey: ['ai_interactions', userProfile?.id],
      queryFn: async () => {
        if (!userProfile?.id) return [];
        const { data, error } = await supabase
          .from('ai_interactions')
          .select('*')
          .eq('user_id', userProfile.id)
          .order('created_at', { ascending: false })
          .limit(50);
        if (error) throw error;
        return data;
      },
      enabled: !!userProfile?.id,
    });
  };

  const useCreateAIInteraction = () => {
    return useMutation({
      mutationFn: async (interaction: {
        role: 'user' | 'assistant' | 'system';
        message: string;
        type?: string;
        query?: string;
        response?: string;
        actions?: any[];
        metadata?: any;
      }) => {
        const { data, error } = await supabase
          .from('ai_interactions')
          .insert({
            user_id: userProfile?.id,
            role: interaction.role,
            message: interaction.message,
            type: interaction.type || 'chat',
            query: interaction.query,
            response: interaction.response,
            actions: interaction.actions || [],
            metadata: interaction.metadata || {}
          })
          .select()
          .single();
        if (error) throw error;
        return data;
      },
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['ai_interactions', userProfile?.id] });
      },
      onError: (error: Error) => {
        console.error('Error saving AI interaction:', error);
      },
    });
  };

  return {
    useAIResults,
    useCreateAIResult,
    useDeleteAIResult,
    useDocumentAnalysis,
    useCreateDocumentAnalysis,
    useKnowledgeBase,
    useCreateKnowledgeEntry,
    useAIInteractions,
    useCreateAIInteraction,
  };
};

