/**
 * Production Build Configuration for CTNL AI Workboard
 * Optimized build settings for deployment
 */

import react from '@vitejs/plugin-react-swc';
import path from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig } from 'vite';
import { compression } from 'vite-plugin-compression';

export default defineConfig({
  plugins: [
    react(),
    // Bundle analyzer
    visualizer({
      filename: 'dist/bundle-analysis.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
    // Gzip compression
    compression({
      algorithm: 'gzip',
      ext: '.gz',
    }),
    // Brotli compression
    compression({
      algorithm: 'brotliCompress',
      ext: '.br',
    }),
  ],
  
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  
  // Build optimization
  build: {
    target: 'es2020',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false, // Disable for production
    minify: 'terser',
    
    // Rollup options for optimization
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
          'query-vendor': ['@tanstack/react-query'],
          'supabase-vendor': ['@supabase/supabase-js'],
          'ai-vendor': ['openai'],
          
          // Feature chunks
          'dashboard': [
            './src/components/dashboard/UnifiedDashboard.tsx',
            './src/components/dashboard/RoleBasedWelcome.tsx'
          ],
          'ai-features': [
            './src/components/ai/FuturisticAIInterface.tsx',
            './src/components/ai/AIManagementSystem.tsx',
            './src/components/ai/HackerAIInterface.tsx'
          ],
          'navigation': [
            './src/components/navigation/EnhancedAppSidebar.tsx',
            './src/components/navigation/MainNavBar.tsx'
          ],
          'forms': [
            './src/components/procurement/ProcurementRequestForm.tsx',
            './src/components/tasks/TaskAssignmentForm.tsx'
          ]
        },
        
        // Asset naming for better caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop().replace('.tsx', '').replace('.ts', '') : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `images/[name]-[hash].${ext}`;
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name)) {
            return `fonts/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        },
        entryFileNames: 'js/[name]-[hash].js',
      },
      
      // External dependencies (if using CDN)
      external: [],
    },
    
    // Terser options for minification
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
      mangle: {
        safari10: true,
      },
      format: {
        comments: false,
      },
    },
    
    // Asset optimization
    assetsInlineLimit: 4096, // 4kb
    cssCodeSplit: true,
    
    // Chunk size warnings
    chunkSizeWarningLimit: 1000,
  },
  
  // Development server config
  server: {
    port: 8083,
    host: true,
    open: false,
    cors: true,
    hmr: {
      overlay: true,
    },
  },
  
  // Preview server config
  preview: {
    port: 8084,
    host: true,
    open: false,
  },
  
  // Environment variables
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
  },
  
  // CSS optimization
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`,
      },
    },
  },
  
  // Dependency optimization
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      '@supabase/supabase-js',
      'lucide-react',
      'aos',
      'sonner',
    ],
    exclude: [],
  },
  
  // ESBuild options
  esbuild: {
    target: 'es2020',
    drop: ['console', 'debugger'], // Remove in production
    legalComments: 'none',
  },
});

// Build script functions
export const buildCommands = {
  // Clean build directory
  clean: () => {
    const fs = require('fs');
    const path = require('path');
    const distPath = path.resolve(__dirname, 'dist');
    if (fs.existsSync(distPath)) {
      fs.rmSync(distPath, { recursive: true, force: true });
    }
    console.log('✅ Cleaned dist directory');
  },
  
  // Build for production
  build: async () => {
    console.log('🚀 Building for production...');
    const { build } = await import('vite');
    await build();
    console.log('✅ Production build complete');
  },
  
  // Analyze bundle
  analyze: async () => {
    console.log('📊 Analyzing bundle...');
    process.env.ANALYZE = 'true';
    const { build } = await import('vite');
    await build();
    console.log('✅ Bundle analysis complete - check dist/bundle-analysis.html');
  },
  
  // Preview build
  preview: async () => {
    console.log('👀 Starting preview server...');
    const { preview } = await import('vite');
    const server = await preview();
    console.log(`✅ Preview server running at http://localhost:${server.config.preview.port}`);
  },
  
  // Check build size
  checkSize: () => {
    const fs = require('fs');
    const path = require('path');
    const distPath = path.resolve(__dirname, 'dist');
    
    if (!fs.existsSync(distPath)) {
      console.log('❌ No build found. Run build first.');
      return;
    }
    
    const getDirectorySize = (dirPath) => {
      let totalSize = 0;
      const files = fs.readdirSync(dirPath);
      
      files.forEach(file => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          totalSize += getDirectorySize(filePath);
        } else {
          totalSize += stats.size;
        }
      });
      
      return totalSize;
    };
    
    const totalSize = getDirectorySize(distPath);
    const sizeInMB = (totalSize / (1024 * 1024)).toFixed(2);
    
    console.log(`📦 Total build size: ${sizeInMB} MB`);
    
    // Check individual asset sizes
    const jsDir = path.join(distPath, 'js');
    const cssDir = path.join(distPath, 'assets');
    
    if (fs.existsSync(jsDir)) {
      const jsSize = getDirectorySize(jsDir);
      console.log(`📄 JavaScript: ${(jsSize / (1024 * 1024)).toFixed(2)} MB`);
    }
    
    if (fs.existsSync(cssDir)) {
      const cssFiles = fs.readdirSync(cssDir).filter(f => f.endsWith('.css'));
      const cssSize = cssFiles.reduce((total, file) => {
        return total + fs.statSync(path.join(cssDir, file)).size;
      }, 0);
      console.log(`🎨 CSS: ${(cssSize / 1024).toFixed(2)} KB`);
    }
  }
};

// Export build configuration
export { buildCommands as commands };
