import { QueryClient } from '@tanstack/react-query';

export class CacheManager {
  private static instance: CacheManager;
  private queryClient: QueryClient | null = null;

  private constructor() {}

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  setQueryClient(client: QueryClient) {
    this.queryClient = client;
  }

  // Clear all cache with error handling
  clearAll() {
    try {
      if (this.queryClient) {
        console.log('🧹 Clearing all React Query cache');
        this.queryClient.clear();
        this.updateLastClearTime();
      } else {
        console.warn('QueryClient not available for cache clearing');
      }
    } catch (error: any) {
      console.error('Error clearing cache:', error);
      // Don't throw the error, just log it to prevent breaking the app
    }
  }

  // Clear cache for specific patterns with error handling
  clearByPattern(pattern: string) {
    try {
      if (this.queryClient) {
        console.log(`🧹 Clearing cache for pattern: ${pattern}`);
        this.queryClient.invalidateQueries({
          predicate: (query) => {
            try {
              return query.queryKey.some(key =>
                typeof key === 'string' && key.includes(pattern)
              );
            } catch (predicateError) {
              console.warn('Error in cache predicate:', predicateError);
              return false;
            }
          }
        });
      }
    } catch (error: any) {
      console.error(`Error clearing cache for pattern ${pattern}:`, error);
    }
  }

  // Clear dashboard-specific cache
  clearDashboardCache() {
    if (this.queryClient) {
      console.log('🧹 Clearing dashboard cache');
      const dashboardPatterns = [
        'admin-performance',
        'admin-stats',
        'manager-stats',
        'staff-tasks',
        'accountant-invoices',
        'battery-reports',
        'expense-reports'
      ];
      
      dashboardPatterns.forEach(pattern => {
        this.queryClient?.invalidateQueries({ queryKey: [pattern] });
      });
    }
  }

  // Clear user-specific cache
  clearUserCache(userId?: string) {
    if (this.queryClient) {
      console.log(`🧹 Clearing user cache${userId ? ` for user: ${userId}` : ''}`);
      this.queryClient.invalidateQueries({
        predicate: (query) => {
          return query.queryKey.some(key => 
            typeof key === 'string' && (
              key.includes('profile') ||
              key.includes('user') ||
              (userId && key.includes(userId))
            )
          );
        }
      });
    }
  }

  // Check if cache should be cleared based on time
  shouldClearCache(thresholdMinutes: number = 30): boolean {
    const lastClearTime = localStorage.getItem('lastCacheClear');
    if (!lastClearTime) return true;
    
    const currentTime = Date.now();
    const threshold = thresholdMinutes * 60 * 1000;
    
    return (currentTime - parseInt(lastClearTime)) > threshold;
  }

  // Update last clear time with error handling
  private updateLastClearTime() {
    try {
      localStorage.setItem('lastCacheClear', Date.now().toString());
    } catch (error) {
      console.warn('Error updating cache clear time:', error);
    }
  }

  // Clear cache on server restart detection
  handleServerRestart() {
    const serverStartTime = localStorage.getItem('serverStartTime');
    const currentTime = Date.now().toString();
    
    if (!serverStartTime || serverStartTime !== currentTime) {
      console.log('🔄 Server restart detected, clearing cache');
      this.clearAll();
      localStorage.setItem('serverStartTime', currentTime);
    }
  }

  // Clear stale cache periodically
  startPeriodicCleanup(intervalMinutes: number = 60) {
    setInterval(() => {
      if (this.shouldClearCache(intervalMinutes)) {
        console.log('⏰ Periodic cache cleanup triggered');
        this.clearAll();
      }
    }, intervalMinutes * 60 * 1000);
  }
}

// Export singleton instance
export const cacheManager = CacheManager.getInstance();

// Utility functions for common cache operations
export const clearAllCache = () => cacheManager.clearAll();
export const clearDashboardCache = () => cacheManager.clearDashboardCache();
export const clearUserCache = (userId?: string) => cacheManager.clearUserCache(userId);
export const clearCacheByPattern = (pattern: string) => cacheManager.clearByPattern(pattern);

// Manual cache clearing function for immediate use
export const forceClearCache = () => {
  console.log('🧹 Force clearing all cache manually...');
  cacheManager.clearAll();

  // Also clear browser cache storage
  if (typeof window !== 'undefined') {
    localStorage.removeItem('lastCacheClear');
    localStorage.removeItem('serverStartTime');
    sessionStorage.clear();

    // Clear any other app-specific storage
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('query') || key.includes('cache') || key.includes('data'))) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
  }

  console.log('✅ Cache cleared successfully!');
  return true;
};

// Comprehensive cache clearing to fix infinite reload issues
export const clearAllCacheAndReload = async () => {
  console.log('🧹 Starting comprehensive cache clear to fix reload issues...');

  try {
    // Clear React Query cache
    cacheManager.clearAll();

    if (typeof window !== 'undefined') {
      // Clear all localStorage except essential auth data
      const authKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('auth') || key.includes('sb-'))) {
          authKeys.push({ key, value: localStorage.getItem(key) });
        }
      }

      // Clear all storage
      localStorage.clear();
      sessionStorage.clear();

      // Clear all cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });



      // Clear all caches
      if ('caches' in window) {
        try {
          const cacheNames = await caches.keys();
          await Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)));
        } catch (error) {
          console.warn('Error clearing caches:', error);
        }
      }

      // Clear IndexedDB
      if ('indexedDB' in window) {
        try {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => {
              if (db.name) {
                return new Promise((resolve, reject) => {
                  const deleteReq = indexedDB.deleteDatabase(db.name!);
                  deleteReq.onsuccess = () => resolve(undefined);
                  deleteReq.onerror = () => reject(deleteReq.error);
                  deleteReq.onblocked = () => {
                    console.warn(`IndexedDB ${db.name} deletion blocked`);
                    resolve(undefined);
                  };
                });
              }
            })
          );
        } catch (error) {
          console.warn('Error clearing IndexedDB:', error);
        }
      }

      console.log('✅ Comprehensive cache clear completed!');
      return true;
    }
  } catch (error) {
    console.error('Error during comprehensive cache clear:', error);
    return false;
  }
};
