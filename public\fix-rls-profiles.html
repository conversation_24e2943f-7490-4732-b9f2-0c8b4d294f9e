<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Profiles RLS - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status.error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .status.warning {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .progress {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #cc0000);
            width: 0%;
            transition: width 0.3s ease;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .step-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .step-icon.pending {
            background: rgba(255, 255, 255, 0.2);
        }

        .step-icon.running {
            background: #ff0000;
            animation: pulse 1s infinite;
        }

        .step-icon.success {
            background: #28a745;
        }

        .step-icon.error {
            background: #dc3545;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Fix Profiles RLS</h1>
            <p>Comprehensive fix for Row Level Security policies on the profiles table</p>
        </div>

        <div class="card">
            <h3>🚨 Current Issues</h3>
            <p>The profiles table has conflicting RLS policies causing 403/406 errors. This tool will:</p>
            <ul style="margin-left: 1.5rem; margin-top: 0.5rem;">
                <li>Remove all conflicting RLS policies</li>
                <li>Create clean, non-recursive policies</li>
                <li>Fix profile creation during signup</li>
                <li>Enable proper access control</li>
            </ul>
        </div>

        <div class="card">
            <h3>🔄 Fix Progress</h3>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <div id="steps">
                <div class="step">
                    <div class="step-icon pending" id="step1">1</div>
                    <span>Disable RLS and drop existing policies</span>
                </div>
                <div class="step">
                    <div class="step-icon pending" id="step2">2</div>
                    <span>Re-enable RLS with clean state</span>
                </div>
                <div class="step">
                    <div class="step-icon pending" id="step3">3</div>
                    <span>Create new non-recursive policies</span>
                </div>
                <div class="step">
                    <div class="step-icon pending" id="step4">4</div>
                    <span>Set up automatic profile creation</span>
                </div>
                <div class="step">
                    <div class="step-icon pending" id="step5">5</div>
                    <span>Verify and test policies</span>
                </div>
            </div>
        </div>

        <div class="card">
            <button class="button" id="fixButton" onclick="fixProfilesRLS()">
                🔧 Fix Profiles RLS Policies
            </button>
            
            <button class="button" id="testButton" onclick="testProfileAccess()" style="background: rgba(255, 255, 255, 0.1);">
                🧪 Test Profile Access
            </button>
        </div>

        <div id="status" class="status info" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
        }

        function updateProgress(step, total) {
            const progressBar = document.getElementById('progressBar');
            const percentage = (step / total) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        function updateStep(stepNumber, status) {
            const stepIcon = document.getElementById(`step${stepNumber}`);
            stepIcon.className = `step-icon ${status}`;
            if (status === 'success') {
                stepIcon.innerHTML = '✓';
            } else if (status === 'error') {
                stepIcon.innerHTML = '✗';
            } else if (status === 'running') {
                stepIcon.innerHTML = '⟳';
            }
        }

        window.fixProfilesRLS = async function() {
            const fixButton = document.getElementById('fixButton');
            const testButton = document.getElementById('testButton');
            
            fixButton.disabled = true;
            testButton.disabled = true;
            
            try {
                showStatus('🔧 Starting comprehensive RLS fix...', 'info');
                
                // Step 1: Drop existing policies
                updateStep(1, 'running');
                updateProgress(1, 5);
                
                // Use the comprehensive SQL fix
                const { error: fixError } = await supabase.functions.invoke('exec-sql', {
                    body: {
                        sql: `
                            -- Disable RLS temporarily
                            ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

                            -- Drop all existing policies
                            DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
                            DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
                            DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
                            DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_service_role_all" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_delete_own" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
                            DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
                        `
                    }
                });
                if (fixError) throw fixError;
                
                updateStep(1, 'success');
                showStatus('✅ Step 1: Dropped existing policies', 'success');
                
                // Step 2: Re-enable RLS and create policies
                updateStep(2, 'running');
                updateProgress(2, 5);

                const { error: enableError } = await supabase.functions.invoke('exec-sql', {
                    body: {
                        sql: `
                            -- Re-enable RLS
                            ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

                            -- Create new policies
                            CREATE POLICY "profiles_select_own" ON public.profiles
                                FOR SELECT USING (auth.uid() = id);

                            CREATE POLICY "profiles_insert_own" ON public.profiles
                                FOR INSERT WITH CHECK (auth.uid() = id);

                            CREATE POLICY "profiles_update_own" ON public.profiles
                                FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

                            CREATE POLICY "profiles_service_role" ON public.profiles
                                FOR ALL USING (current_setting('role') = 'service_role')
                                WITH CHECK (current_setting('role') = 'service_role');

                            CREATE POLICY "profiles_select_authenticated" ON public.profiles
                                FOR SELECT USING (auth.role() = 'authenticated');
                        `
                    }
                });
                if (enableError) throw enableError;
                
                updateStep(2, 'success');
                showStatus('✅ Step 2: Re-enabled RLS', 'success');
                
                // Step 3: Create new policies
                updateStep(3, 'running');
                updateProgress(3, 5);
                
                const createPoliciesSQL = `
                    -- Policy 1: Users can view their own profile
                    CREATE POLICY "profiles_select_own" ON public.profiles
                        FOR SELECT USING (auth.uid() = id);
                    
                    -- Policy 2: Users can insert their own profile
                    CREATE POLICY "profiles_insert_own" ON public.profiles
                        FOR INSERT WITH CHECK (auth.uid() = id);
                    
                    -- Policy 3: Users can update their own profile
                    CREATE POLICY "profiles_update_own" ON public.profiles
                        FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);
                    
                    -- Policy 4: Service role full access
                    CREATE POLICY "profiles_service_role" ON public.profiles
                        FOR ALL USING (current_setting('role') = 'service_role')
                        WITH CHECK (current_setting('role') = 'service_role');
                    
                    -- Policy 5: Authenticated users can view profiles
                    CREATE POLICY "profiles_select_authenticated" ON public.profiles
                        FOR SELECT USING (auth.role() = 'authenticated');
                `;
                
                const { error: createError } = await supabase.rpc('exec_sql', { sql: createPoliciesSQL });
                if (createError) throw createError;
                
                updateStep(3, 'success');
                showStatus('✅ Step 3: Created new RLS policies', 'success');
                
                // Step 4: Set up profile creation function
                updateStep(4, 'running');
                updateProgress(4, 5);
                
                const functionSQL = `
                    CREATE OR REPLACE FUNCTION public.handle_new_user()
                    RETURNS TRIGGER AS $$
                    BEGIN
                      INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
                      VALUES (
                        NEW.id,
                        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
                        NEW.email,
                        COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
                        'active',
                        NOW(),
                        NOW()
                      );
                      RETURN NEW;
                    EXCEPTION
                      WHEN OTHERS THEN
                        RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
                        RETURN NEW;
                    END;
                    $$ LANGUAGE plpgsql SECURITY DEFINER;
                    
                    DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
                    CREATE TRIGGER on_auth_user_created
                      AFTER INSERT ON auth.users
                      FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
                `;
                
                const { error: functionError } = await supabase.rpc('exec_sql', { sql: functionSQL });
                if (functionError) throw functionError;
                
                updateStep(4, 'success');
                showStatus('✅ Step 4: Set up automatic profile creation', 'success');
                
                // Step 5: Verify setup
                updateStep(5, 'running');
                updateProgress(5, 5);
                
                // Test if we can query policies
                const { data: policies, error: verifyError } = await supabase
                    .from('pg_policies')
                    .select('*')
                    .eq('tablename', 'profiles');
                
                updateStep(5, 'success');
                updateProgress(5, 5);
                
                showStatus(`🎉 RLS fix completed successfully!\n\nCreated ${policies?.length || 'several'} new policies.\nProfile creation should now work properly.`, 'success');
                
            } catch (error) {
                console.error('Fix failed:', error);
                showStatus(`❌ Fix failed: ${error.message}`, 'error');
                
                // Mark current step as error
                for (let i = 1; i <= 5; i++) {
                    const stepIcon = document.getElementById(`step${i}`);
                    if (stepIcon.className.includes('running')) {
                        updateStep(i, 'error');
                        break;
                    }
                }
            } finally {
                fixButton.disabled = false;
                testButton.disabled = false;
            }
        };

        window.testProfileAccess = async function() {
            try {
                showStatus('🧪 Testing profile access...', 'info');
                
                // Test if we can read profiles
                const { data: profiles, error } = await supabase
                    .from('profiles')
                    .select('id, full_name, role')
                    .limit(1);
                
                if (error) {
                    showStatus(`❌ Profile access test failed: ${error.message}`, 'error');
                } else {
                    showStatus(`✅ Profile access test passed!\nFound ${profiles?.length || 0} profiles accessible.`, 'success');
                }
                
            } catch (error) {
                showStatus(`❌ Test failed: ${error.message}`, 'error');
            }
        };
    </script>
</body>
</html>
