/* Modern Document Card Styles */
@import "./styles/modern-document-card.css";
@import "./styles/neumorphism.css";
@import "./styles/mobile-responsive.css";


@tailwind base;
@tailwind components;
@tailwind utilities;


/* CSS Variables - Enhanced Light Theme with Neumorphism Support */
:root {
  --background: 0 0% 98%; /* Light gray background for neumorphism */
  --foreground: 0 0% 3.9%; /* Dark text */
  --card: 0 0% 98%; /* Light gray cards for neumorphism */
  --card-foreground: 0 0% 3.9%; /* Dark text on cards */
  --popover: 0 0% 98%; /* Light gray popover */
  --popover-foreground: 0 0% 3.9%; /* Dark text on popover */
  --primary: 0 100% 51%;  /* #ff1c04 - System red */
  --primary-foreground: 0 0% 98%; /* White text on red */
  --secondary: 0 0% 94%; /* Light gray for secondary */
  --secondary-foreground: 0 0% 9%; /* Dark text on secondary */
  --muted: 0 0% 94%; /* Light gray for muted elements */
  --muted-foreground: 0 0% 45.1%; /* Medium gray text */
  --accent: 0 0% 94%; /* Light gray accent */
  --accent-foreground: 0 0% 9%; /* Dark text on accent */
  --destructive: 0 84.2% 60.2%; /* Red destructive */
  --destructive-foreground: 0 0% 98%; /* White text on destructive */
  --border: 0 0% 85%; /* Darker borders for better contrast */
  --input: 0 0% 96%; /* Light gray input backgrounds */
  --input-foreground: 0 0% 3.9%; /* Dark input text */
  --ring: 0 100% 51%; /* Red focus rings */
  --radius: 0.5rem; /* 8px */

  /* Neumorphism specific variables */
  --neumorphism-light: 0 0% 100%; /* Light shadow color */
  --neumorphism-dark: 0 0% 85%; /* Dark shadow color */
  --neumorphism-bg: 0 0% 98%; /* Base background */
}

.dark {
  --background: 0 0% 8%; /* Dark gray background for neumorphism */
  --foreground: 210 40% 98%;
  --card: 0 0% 10%; /* Dark charcoal cards for neumorphism */
  --card-foreground: 210 40% 98%;
  --popover: 0 0% 10%;
  --popover-foreground: 210 40% 98%;
  --primary: 0 100% 51%;
  --primary-foreground: 210 40% 98%;
  --secondary: 0 0% 0%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 15%; /* Pure charcoal */
  --muted-foreground: 215 20.2% 75%; /* Lighter for better contrast */
  --accent: 0 0% 15%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 0 0% 20%; /* Lighter borders for better contrast */
  --input: 0 0% 12%; /* Slightly lighter input backgrounds */
  --input-foreground: 210 40% 98%; /* Explicit input text color for dark theme */
  --ring: 0 100% 51%;

  /* Dark theme neumorphism variables */
  --neumorphism-light: 0 0% 15%; /* Light shadow color for dark theme */
  --neumorphism-dark: 0 0% 0%; /* Dark shadow color for dark theme */
  --neumorphism-bg: 0 0% 8%; /* Base background for dark theme */
}

/* Enhanced Neumorphism Cards - Light Theme First */
.glass-card {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.15);
  border-radius: 8px;
  backdrop-filter: blur(20px);
  box-shadow:
    8px 8px 16px hsl(var(--neumorphism-dark) / 0.3),
    -8px -8px 16px hsl(var(--neumorphism-light) / 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glass-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 28, 4, 0.1) 50%,
    transparent 100%
  );
  transition: left 0.6s ease;
}

.dark .glass-card {
  background: hsl(var(--neumorphism-bg));
  border-color: rgba(255, 28, 4, 0.2);
  box-shadow:
    8px 8px 16px hsl(var(--neumorphism-dark) / 0.8),
    -8px -8px 16px hsl(var(--neumorphism-light) / 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.glass-card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow:
    12px 12px 24px hsl(var(--neumorphism-dark) / 0.4),
    -12px -12px 24px hsl(var(--neumorphism-light) / 0.9),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
    0 0 30px rgba(255, 28, 4, 0.1);
  border-color: rgba(255, 28, 4, 0.25);
}

.glass-card:hover::before {
  left: 100%;
}

.dark .glass-card:hover {
  box-shadow:
    12px 12px 24px hsl(var(--neumorphism-dark) / 0.9),
    -12px -12px 24px hsl(var(--neumorphism-light) / 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 0 30px rgba(255, 28, 4, 0.15);
  border-color: rgba(255, 28, 4, 0.3);
}

/* Enhanced Neumorphism Modal Cards */
.glass-card-modal {
  background: linear-gradient(145deg,
    #f8f9fa 0%,
    #ffffff 50%,
    #f8f9fa 100%
  );
  border: 1px solid rgba(255, 28, 4, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(25px);
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.1),
    -15px -15px 30px rgba(255, 255, 255, 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.glass-card-modal::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 28, 4, 0.6) 50%,
    transparent 100%
  );
}

.dark .glass-card-modal {
  background: linear-gradient(145deg,
    #1a1a1a 0%,
    #2a2a2a 50%,
    #1a1a1a 100%
  );
  border-color: rgba(255, 28, 4, 0.3);
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.8),
    -15px -15px 30px rgba(60, 60, 60, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.5);
}

.glass-card-modal:hover {
  /* Removed transform to prevent dialog movement */
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.15),
    -20px -20px 40px rgba(255, 255, 255, 0.9),
    inset 0 1px 0 rgba(255, 255, 255, 1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
    0 0 30px rgba(255, 28, 4, 0.1);
  border-color: rgba(255, 28, 4, 0.4);
}

.dark .glass-card-modal:hover {
  /* Removed transform to prevent dialog movement */
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.9),
    -20px -20px 40px rgba(80, 80, 80, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.6),
    0 0 30px rgba(255, 28, 4, 0.2);
}

/* ===============================================
   DIALOG AND MODAL POSITIONING FIXES
   =============================================== */

/* Ensure dialogs stay properly positioned */
.dialog-content-fixed {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  max-height: calc(100vh - 2rem) !important;
  max-width: calc(100vw - 2rem) !important;
  overflow-y: auto !important;
  z-index: 100 !important;
}

/* Prevent any transforms on dialog hover */
.dialog-content-fixed:hover {
  transform: translate(-50%, -50%) !important;
}

/* Ensure dialog overlay covers entire screen */
[data-radix-popper-content-wrapper] {
  z-index: 100 !important;
}

/* Fix for mobile dialog positioning */
@media (max-width: 640px) {
  .dialog-content-fixed {
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: calc(100vw - 1rem) !important;
    max-width: calc(100vw - 1rem) !important;
    margin: 0.5rem !important;
  }
}

/* Ensure popover and tooltip positioning */
[data-radix-popper-content-wrapper],
[data-radix-tooltip-content],
[data-radix-popover-content] {
  z-index: 200 !important;
}

/* Fix sheet positioning */
[data-radix-dialog-content] {
  position: fixed !important;
}

/* Additional positioning fixes for all modal types */
.popover-content-fixed {
  position: absolute !important;
  z-index: 200 !important;
}

.tooltip-content-fixed {
  position: absolute !important;
  z-index: 300 !important;
}

/* Prevent body scroll when dialog is open */
body:has([data-state="open"]) {
  overflow: hidden;
}

/* Ensure proper stacking context */
[data-radix-portal] {
  z-index: 1000 !important;
}

/* Fix for notification center positioning */
.notification-popover {
  position: fixed !important;
  z-index: 250 !important;
}

/* Prevent dialog content from being affected by parent transforms */
.dialog-content-fixed * {
  transform-style: flat !important;
}

/* Fix for mobile viewport issues */
@media (max-width: 768px) {
  .dialog-content-fixed {
    position: fixed !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: calc(100vw - 1rem) !important;
    max-width: calc(100vw - 1rem) !important;
    max-height: calc(100vh - 2rem) !important;
    margin: 0 !important;
  }

  /* Ensure mobile popovers stay on screen */
  .popover-content-fixed {
    max-width: calc(100vw - 2rem) !important;
    left: 1rem !important;
    right: 1rem !important;
  }

  /* Mobile tooltip positioning */
  .tooltip-content-fixed {
    max-width: calc(100vw - 2rem) !important;
  }
}

/* Fix for very small screens */
@media (max-width: 480px) {
  .dialog-content-fixed {
    width: calc(100vw - 0.5rem) !important;
    max-width: calc(100vw - 0.5rem) !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0.25rem !important;
  }
}

/* Enhanced Neumorphism Card Styles with CTNL AI Colors */
.modern-card {
  background: #1a1a1a;
  border-radius: 8px;
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.8),
    -15px -15px 30px rgba(60, 60, 60, 0.3),
    inset 0 0 0 1px rgba(255, 28, 4, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 28, 4, 0.5) 50%,
    transparent 100%
  );
}

.modern-card:hover {
  transform: translateY(-5px);
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.9),
    -20px -20px 40px rgba(80, 80, 80, 0.4),
    inset 0 0 0 1px rgba(255, 28, 4, 0.2),
    0 0 30px rgba(255, 28, 4, 0.1);
}

/* Enhanced Stats Cards with Pure Black Background */
.stats-card {
  background: linear-gradient(145deg,
    #000000 0%,
    #0a0a0a 50%,
    #000000 100%
  );
  border-radius: 8px;
  border: 1px solid rgba(255, 28, 4, 0.1);
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.8),
    -15px -15px 30px rgba(60, 60, 60, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  padding: 1.5rem;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 28, 4, 0.5) 50%,
    transparent 100%
  );
}

.stats-card::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(255, 28, 4, 0.05) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.9),
    -20px -20px 40px rgba(80, 80, 80, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 0 50px rgba(255, 28, 4, 0.2);
  border-color: rgba(255, 28, 4, 0.3);
}

.stats-card:hover::after {
  opacity: 1;
}

/* Light theme stats cards */
.light .stats-card {
  background: linear-gradient(145deg,
    #f8f9fa 0%,
    #ffffff 50%,
    #f8f9fa 100%
  );
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.1),
    -15px -15px 30px rgba(255, 255, 255, 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
}

.light .stats-card:hover {
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.15),
    -20px -20px 40px rgba(255, 255, 255, 0.9),
    inset 0 1px 0 rgba(255, 255, 255, 1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15),
    0 0 50px rgba(255, 28, 4, 0.1);
}

.modern-card:hover {
  box-shadow: 0 25px 50px -12px rgba(255, 28, 4, 0.2);
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 1);
}

.dark .modern-card:hover {
  background: rgba(35, 35, 35, 0.98);
}

/* Enhanced Dashboard Cards with Pure Black Background */
.dashboard-card {
  background: linear-gradient(145deg,
    #000000 0%,
    #0a0a0a 50%,
    #000000 100%
  );
  border-radius: 8px;
  border: 1px solid rgba(255, 28, 4, 0.1);
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.7),
    -15px -15px 30px rgba(60, 60, 60, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 28, 4, 0.08) 50%,
    transparent 100%
  );
  transition: left 0.8s ease;
}

.dashboard-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.8),
    -20px -20px 40px rgba(80, 80, 80, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 0 40px rgba(255, 28, 4, 0.15);
  border-color: rgba(255, 28, 4, 0.2);
}

.dashboard-card:hover::before {
  left: 100%;
}

/* Enhanced Time Card Styles */
.time-card {
  background: linear-gradient(145deg,
    #1f1f1f 0%,
    #2c2c2c 50%,
    #1f1f1f 100%
  );
  border-radius: 30px;
  border: 1px solid rgba(34, 197, 94, 0.2);
  box-shadow:
    15px 15px 30px rgba(0, 0, 0, 0.7),
    -15px -15px 30px rgba(60, 60, 60, 0.2),
    inset 0 1px 0 rgba(34, 197, 94, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.time-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.8),
    -20px -20px 40px rgba(60, 60, 60, 0.3),
    inset 0 1px 0 rgba(34, 197, 94, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 0 40px rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
}

/* Enhanced Neumorphism Stats Cards */
.stats-card {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  padding: 1.5rem;
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.1);
  box-shadow:
    8px 8px 16px hsl(var(--neumorphism-dark) / 0.25),
    -8px -8px 16px hsl(var(--neumorphism-light) / 0.7),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .stats-card {
  background: linear-gradient(145deg,
    #000000 0%,
    #0a0a0a 50%,
    #000000 100%
  );
  border-color: rgba(255, 28, 4, 0.15);
  box-shadow:
    8px 8px 16px rgba(0, 0, 0, 0.7),
    -8px -8px 16px rgba(60, 60, 60, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.05),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.stats-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow:
    12px 12px 24px hsl(var(--neumorphism-dark) / 0.35),
    -12px -12px 24px hsl(var(--neumorphism-light) / 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(0, 0, 0, 0.12),
    0 0 40px rgba(255, 28, 4, 0.08);
  border-color: rgba(255, 28, 4, 0.2);
}

.dark .stats-card:hover {
  box-shadow:
    12px 12px 24px hsl(var(--neumorphism-dark) / 0.8),
    -12px -12px 24px hsl(var(--neumorphism-light) / 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.08),
    inset 0 -1px 0 rgba(0, 0, 0, 0.4),
    0 0 40px rgba(255, 28, 4, 0.12);
  border-color: rgba(255, 28, 4, 0.25);
}

/* Modern Charts and Data Visualization with Pure Black Background */
.chart-container {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 30px;
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.dark .chart-container {
  background: linear-gradient(145deg,
    #000000 0%,
    #0a0a0a 50%,
    #000000 100%
  );
  border: 1px solid rgba(255, 28, 4, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Modern Typography */
.modern-heading {
  background: linear-gradient(to right, #ff1c04, #ff1c04, #000000);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-weight: bold;
  letter-spacing: -0.025em;
}

/* Modern Buttons with 8px radius */
.modern-btn {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(to right, #ff1c04, #e01703);
  color: white;
  font-weight: 600;
  box-shadow: 0 20px 25px -5px rgba(255, 28, 4, 0.3);
  transition: all 0.3s ease;
  transform: scale(1);
  border: none;
  cursor: pointer;
}

.modern-btn:hover {
  box-shadow: 0 25px 50px -12px rgba(255, 28, 4, 0.5);
  transform: scale(1.05);
}

/* Data Tables with 8px radius */
.modern-table {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  backdrop-filter: blur(20px);
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.dark .modern-table {
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(55, 65, 81, 0.2);
}

.modern-table th {
  background: linear-gradient(to right, rgba(255, 28, 4, 0.1), rgba(0, 0, 0, 0.1));
  color: #000000;
  font-weight: 600;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.75rem;
}

.dark .modern-table th {
  color: #f9fafb;
  border-bottom-color: rgba(55, 65, 81, 0.2);
}

.modern-table tr:hover {
  background: rgba(255, 255, 255, 0.2);
  transition: background-color 0.2s ease;
}

.dark .modern-table tr:hover {
  background: rgba(255, 255, 255, 0.05);
}

/* Modern Navigation Buttons */
.nav-item {
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item.active {
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(214, 177, 177, 0.3);
}

.nav-item:hover {
  box-shadow:
    0 2px 10px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
}

/* Enhanced 3D Neumorphism Cards */
.modern-3d-card {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.12);
  border-radius: 8px;
  backdrop-filter: blur(20px);
  box-shadow:
    8px 8px 16px hsl(var(--neumorphism-dark) / 0.3),
    -8px -8px 16px hsl(var(--neumorphism-light) / 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) rotateX(0);
  transform-style: preserve-3d;
}

.dark .modern-3d-card {
  background: hsl(var(--neumorphism-bg));
  border-color: rgba(255, 28, 4, 0.18);
  box-shadow:
    8px 8px 16px hsl(var(--neumorphism-dark) / 0.8),
    -8px -8px 16px hsl(var(--neumorphism-light) / 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.06),
    inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.modern-3d-card:hover {
  transform: translateY(-8px) rotateX(5deg);
  background: rgba(255, 255, 255, 1);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.18),
    0 8px 24px rgba(255, 28, 4, 0.15),
    0 4px 12px rgba(15, 160, 206, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.dark .modern-3d-card:hover {
  background: rgba(35, 35, 35, 0.98);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.6),
    0 8px 24px rgba(255, 28, 4, 0.25),
    0 4px 12px rgba(0, 0, 0, 0.18),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Subtle Sidebar Navigation Styles */
.sidebar-nav-button {
  background: transparent !important;
  color: rgba(255, 255, 255, 0.6) !important;
  border: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.sidebar-nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
  transition: left 0.5s ease;
}

.sidebar-nav-button:hover::before {
  left: 100%;
}

.sidebar-nav-button:hover {
  color: rgba(255, 255, 255, 0.85) !important;
  transform: translateX(3px) scale(1.01);
  background: rgba(255, 255, 255, 0.02) !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.sidebar-nav-button.active {
  color: rgba(255, 28, 4, 0.85) !important;
  background: rgba(255, 28, 4, 0.03) !important;
  border-left: 2px solid rgba(255, 28, 4, 0.4) !important;
  box-shadow: inset 0 0 10px rgba(255, 28, 4, 0.1);
}

/* Dark theme specific sidebar styles */
.dark .sidebar-nav-button {
  color: rgba(255, 255, 255, 0.5) !important;
}

.dark .sidebar-nav-button:hover {
  color: rgba(255, 255, 255, 0.8) !important;
  background: rgba(255, 255, 255, 0.01) !important;
}

.dark .sidebar-nav-button.active {
  color: rgba(255, 28, 4, 0.9) !important;
  background: rgba(255, 28, 4, 0.05) !important;
  border-left: 2px solid rgba(255, 28, 4, 0.6) !important;
}

/* Responsive Cascade Effects */
@media (max-width: 1024px) {
  .modern-3d-card,
  .glass-card,
  .modern-card,
  .stats-card {
    transform: scale(0.98);
    transition: all 0.3s ease;
  }

  .modern-3d-card:hover,
  .glass-card:hover,
  .modern-card:hover,
  .stats-card:hover {
    transform: scale(1.01) translateY(-2px);
  }
}

/* ===============================================
   COMPREHENSIVE NEUMORPHISM UTILITY CLASSES
   =============================================== */

/* Base Neumorphism Card */
.neumorphism-card {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.1);
  border-radius: 8px;
  box-shadow:
    6px 6px 12px hsl(var(--neumorphism-dark) / 0.25),
    -6px -6px 12px hsl(var(--neumorphism-light) / 0.7),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .neumorphism-card {
  box-shadow:
    6px 6px 12px hsl(var(--neumorphism-dark) / 0.6),
    -6px -6px 12px hsl(var(--neumorphism-light) / 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.03),
    inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.neumorphism-card:hover {
  transform: translateY(-2px);
  box-shadow:
    8px 8px 16px hsl(var(--neumorphism-dark) / 0.3),
    -8px -8px 16px hsl(var(--neumorphism-light) / 0.8),
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    inset 0 -1px 0 rgba(0, 0, 0, 0.08),
    0 0 20px rgba(255, 28, 4, 0.05);
  border-color: rgba(255, 28, 4, 0.15);
}

/* Neumorphism Table */
.neumorphism-table {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.08);
  border-radius: 8px;
  box-shadow:
    4px 4px 8px hsl(var(--neumorphism-dark) / 0.2),
    -4px -4px 8px hsl(var(--neumorphism-light) / 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  overflow: hidden;
}

.dark .neumorphism-table {
  box-shadow:
    4px 4px 8px hsl(var(--neumorphism-dark) / 0.5),
    -4px -4px 8px hsl(var(--neumorphism-light) / 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.02);
}

/* Neumorphism Button */
.neumorphism-btn {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.12);
  border-radius: 8px;
  box-shadow:
    3px 3px 6px hsl(var(--neumorphism-dark) / 0.2),
    -3px -3px 6px hsl(var(--neumorphism-light) / 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.2s ease;
  cursor: pointer;
}

.neumorphism-btn:hover {
  transform: translateY(-1px);
  box-shadow:
    4px 4px 8px hsl(var(--neumorphism-dark) / 0.25),
    -4px -4px 8px hsl(var(--neumorphism-light) / 0.7),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
}

.neumorphism-btn:active {
  transform: translateY(0);
  box-shadow:
    2px 2px 4px hsl(var(--neumorphism-dark) / 0.3),
    -2px -2px 4px hsl(var(--neumorphism-light) / 0.5),
    inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Enhanced Neumorphism Button for Sidebar */
.neumorphism-button {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.08);
  border-radius: 8px;
  box-shadow:
    2px 2px 4px hsl(var(--neumorphism-dark) / 0.15),
    -2px -2px 4px hsl(var(--neumorphism-light) / 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.dark .neumorphism-button {
  box-shadow:
    2px 2px 4px hsl(var(--neumorphism-dark) / 0.5),
    -2px -2px 4px hsl(var(--neumorphism-light) / 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.02);
}

.neumorphism-button:hover {
  transform: translateY(-1px);
  box-shadow:
    3px 3px 6px hsl(var(--neumorphism-dark) / 0.2),
    -3px -3px 6px hsl(var(--neumorphism-light) / 0.7),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 28, 4, 0.12);
}

.dark .neumorphism-button:hover {
  box-shadow:
    3px 3px 6px hsl(var(--neumorphism-dark) / 0.6),
    -3px -3px 6px hsl(var(--neumorphism-light) / 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.04);
}

.neumorphism-button:active {
  transform: translateY(0);
  box-shadow:
    1px 1px 2px hsl(var(--neumorphism-dark) / 0.25),
    -1px -1px 2px hsl(var(--neumorphism-light) / 0.5),
    inset 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Active state for navigation buttons */
.neumorphism-active {
  background: hsl(var(--primary) / 0.05);
  border-color: rgba(255, 28, 4, 0.2);
  box-shadow:
    inset 2px 2px 4px hsl(var(--neumorphism-dark) / 0.2),
    inset -2px -2px 4px hsl(var(--neumorphism-light) / 0.3),
    0 0 8px rgba(255, 28, 4, 0.1);
  color: hsl(var(--primary));
}

.dark .neumorphism-active {
  background: hsl(var(--primary) / 0.08);
  box-shadow:
    inset 2px 2px 4px hsl(var(--neumorphism-dark) / 0.4),
    inset -2px -2px 4px hsl(var(--neumorphism-light) / 0.05),
    0 0 8px rgba(255, 28, 4, 0.15);
}

/* Neumorphism Input */
.neumorphism-input {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.08);
  border-radius: 8px;
  box-shadow:
    inset 2px 2px 4px hsl(var(--neumorphism-dark) / 0.15),
    inset -2px -2px 4px hsl(var(--neumorphism-light) / 0.5);
  transition: all 0.2s ease;
}

.neumorphism-input:focus {
  border-color: rgba(255, 28, 4, 0.2);
  box-shadow:
    inset 3px 3px 6px hsl(var(--neumorphism-dark) / 0.2),
    inset -3px -3px 6px hsl(var(--neumorphism-light) / 0.6),
    0 0 0 2px rgba(255, 28, 4, 0.1);
}

/* Neumorphism List Item */
.neumorphism-list-item {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.05);
  border-radius: 8px;
  box-shadow:
    2px 2px 4px hsl(var(--neumorphism-dark) / 0.15),
    -2px -2px 4px hsl(var(--neumorphism-light) / 0.5);
  transition: all 0.2s ease;
  margin-bottom: 0.5rem;
}

.neumorphism-list-item:hover {
  transform: translateY(-1px);
  box-shadow:
    3px 3px 6px hsl(var(--neumorphism-dark) / 0.2),
    -3px -3px 6px hsl(var(--neumorphism-light) / 0.6);
  border-color: rgba(255, 28, 4, 0.1);
}

/* Neumorphism Sidebar */
.neumorphism-sidebar {
  background: hsl(var(--neumorphism-bg));
  border-right: 1px solid rgba(255, 28, 4, 0.08);
  box-shadow:
    4px 0 8px hsl(var(--neumorphism-dark) / 0.15),
    -2px 0 4px hsl(var(--neumorphism-light) / 0.3);
}

.dark .neumorphism-sidebar {
  box-shadow:
    4px 0 8px hsl(var(--neumorphism-dark) / 0.4),
    -2px 0 4px hsl(var(--neumorphism-light) / 0.05);
}

/* ===============================================
   RESPONSIVE CLOCK-IN PAGE UTILITIES
   =============================================== */

/* Clock-in page specific responsive utilities */
.clockin-card {
  max-width: 100%;
  width: 100%;
}

@media (max-width: 480px) {
  .clockin-card {
    margin: 0.5rem;
    padding: 0.75rem;
  }

  .clockin-card .card-content {
    padding: 0.75rem !important;
  }

  /* Reduce all text sizes on very small screens */
  .clockin-card h1 { font-size: 1.25rem !important; }
  .clockin-card h2 { font-size: 1.125rem !important; }
  .clockin-card h3 { font-size: 1rem !important; }
  .clockin-card .text-4xl { font-size: 1.5rem !important; }
  .clockin-card .text-3xl { font-size: 1.25rem !important; }
  .clockin-card .text-2xl { font-size: 1.125rem !important; }
  .clockin-card .text-xl { font-size: 1rem !important; }
  .clockin-card .text-lg { font-size: 0.875rem !important; }

  /* Reduce button sizes */
  .clockin-card .modern-btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
  }

  /* Reduce spacing */
  .clockin-card .mb-8 { margin-bottom: 1rem !important; }
  .clockin-card .mb-6 { margin-bottom: 0.75rem !important; }
  .clockin-card .mb-4 { margin-bottom: 0.5rem !important; }
  .clockin-card .p-6 { padding: 0.75rem !important; }
  .clockin-card .p-4 { padding: 0.5rem !important; }
}

@media (max-width: 640px) {
  /* Time tracking cards responsive */
  .time-card {
    padding: 0.75rem;
  }

  .time-card .text-2xl { font-size: 1.25rem; }
  .time-card .text-xl { font-size: 1.125rem; }
  .time-card .h-10 { height: 1.5rem; width: 1.5rem; }
  .time-card .h-8 { height: 1.25rem; width: 1.25rem; }
  .time-card .h-6 { height: 1rem; width: 1rem; }

  /* Reduce clock button sizes */
  .time-card .w-32 { width: 5rem !important; height: 5rem !important; }
  .time-card .w-24 { width: 4rem !important; height: 4rem !important; }

  /* Compact stats cards */
  .stats-card {
    padding: 0.5rem !important;
  }

  .stats-card .h-6 { height: 1rem; width: 1rem; }
  .stats-card .text-sm { font-size: 0.75rem; }
}

@media (max-width: 768px) {
  .modern-3d-card,
  .glass-card,
  .modern-card,
  .stats-card {
    transform: scale(0.96);
    margin: 0.5rem 0;
  }

  .modern-3d-card:hover,
  .glass-card:hover,
  .modern-card:hover,
  .stats-card:hover {
    transform: scale(0.98) translateY(-1px);
  }

  /* Cascade animation for mobile */
  .stats-card:nth-child(1) { animation-delay: 0.1s; }
  .stats-card:nth-child(2) { animation-delay: 0.2s; }
  .stats-card:nth-child(3) { animation-delay: 0.3s; }
  .stats-card:nth-child(4) { animation-delay: 0.4s; }
}

@media (max-width: 480px) {
  .modern-3d-card,
  .glass-card,
  .modern-card,
  .stats-card {
    transform: scale(0.94);
    margin: 0.75rem 0;
    border-radius: 20px;
  }

  .sidebar-nav-button {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
  }
}

/* Enhanced Neumorphism Body Background */
body {
  background: hsl(var(--neumorphism-bg));
  color: hsl(var(--foreground));
  min-height: 100vh;
  position: relative;
}

/* Subtle texture overlay for neumorphism effect */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 28, 4, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(15, 160, 206, 0.02) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

.dark body {
  background: hsl(var(--neumorphism-bg));
}

.dark body::before {
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 28, 4, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(15, 160, 206, 0.03) 0%, transparent 50%);
}

/* Cascade Animation Keyframes */
@keyframes cascadeIn {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes cascadeInMobile {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(0.96);
  }
}

/* Apply cascade animation */
.modern-3d-card,
.glass-card,
.modern-card,
.stats-card {
  animation: cascadeIn 0.6s ease-out forwards;
}

/* Enhanced Voice Button 3D Effects */
.voice-button-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.voice-button-3d:hover {
  transform: rotateY(15deg) rotateX(5deg) scale(1.1);
}

/* Glowing red voice button keyframes */
@keyframes voiceGlow {
  0%, 100% {
    box-shadow:
      0 0 20px rgba(239, 68, 68, 0.6),
      0 0 40px rgba(239, 68, 68, 0.4),
      0 0 60px rgba(239, 68, 68, 0.2),
      inset 0 2px 4px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 0 30px rgba(239, 68, 68, 0.8),
      0 0 60px rgba(239, 68, 68, 0.6),
      0 0 90px rgba(239, 68, 68, 0.4),
      inset 0 2px 4px rgba(255, 255, 255, 0.2);
  }
}

@keyframes voicePulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

/* Voice button floating particles */
@keyframes floatParticle {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .modern-3d-card,
  .glass-card,
  .modern-card,
  .stats-card {
    animation: cascadeInMobile 0.5s ease-out forwards;
  }
}

/* Enhanced Mobile Sidebar Styles */
@media (max-width: 768px) {
  .sidebar-nav-button {
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: 16px;
    font-size: 0.9rem;
  }

  .sidebar-nav-button:hover {
    transform: translateX(2px) scale(1.01);
  }
}

/* Grid responsive improvements */
@media (max-width: 1024px) {
  .grid {
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .grid {
    gap: 0.75rem;
  }

  .md\\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 480px) {
  .grid {
    gap: 0.5rem;
  }

  .lg\\:grid-cols-4,
  .md\\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

/* Enhanced Glassmorphism with Neumorphism - Non-transparent */
.glassmorphism {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.15);
  box-shadow:
    6px 6px 12px hsl(var(--neumorphism-dark) / 0.25),
    -6px -6px 12px hsl(var(--neumorphism-light) / 0.7),
    inset 0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .glassmorphism {
  background: hsl(var(--neumorphism-bg));
  border-color: rgba(255, 28, 4, 0.2);
  box-shadow:
    6px 6px 12px hsl(var(--neumorphism-dark) / 0.6),
    -6px -6px 12px hsl(var(--neumorphism-light) / 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Light Mode Text Contrast Improvements */
.light .text-muted-foreground,
:root .text-muted-foreground {
  color: hsl(0 0% 35%) !important; /* Darker for better contrast */
}

.light .text-gray-600,
:root .text-gray-600 {
  color: hsl(0 0% 25%) !important; /* Much darker for visibility */
}

.light .text-gray-500,
:root .text-gray-500 {
  color: hsl(0 0% 30%) !important; /* Darker for visibility */
}

/* Ensure proper contrast for card titles and content */
.light .card-title,
:root .card-title {
  color: hsl(0 0% 15%) !important;
}

.light .card-content,
:root .card-content {
  color: hsl(0 0% 10%) !important;
}

/* Improved scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 28, 4, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 28, 4, 0.5);
}

.dark ::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 28, 4, 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 28, 4, 0.6);
}

/* Floating animation */
.floating-animation {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Pulse glow animation */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 28, 4, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 28, 4, 0.6);
  }
}

/* Custom animations for success modal */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

@keyframes pulse-slow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

.animate-float {
  animation: float 4s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

/* Enhanced Input Field Styling with Neumorphism */
input, textarea, select {
  color: hsl(var(--input-foreground)) !important;
  background: hsl(var(--input)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 16px !important;
  box-shadow:
    inset 4px 4px 8px hsl(var(--neumorphism-dark) / 0.2),
    inset -4px -4px 8px hsl(var(--neumorphism-light) / 0.5) !important;
  transition: all 0.3s ease !important;
}

input:focus, textarea:focus, select:focus {
  outline: none !important;
  border-color: rgba(255, 28, 4, 0.3) !important;
  box-shadow:
    inset 4px 4px 8px hsl(var(--neumorphism-dark) / 0.3),
    inset -4px -4px 8px hsl(var(--neumorphism-light) / 0.6),
    0 0 0 2px rgba(255, 28, 4, 0.1) !important;
}

.dark input, .dark textarea, .dark select {
  color: hsl(var(--input-foreground)) !important;
  background: hsl(var(--input)) !important;
  border-color: hsl(var(--border)) !important;
  box-shadow:
    inset 4px 4px 8px hsl(var(--neumorphism-dark) / 0.6),
    inset -4px -4px 8px hsl(var(--neumorphism-light) / 0.08) !important;
}

.dark input:focus, .dark textarea:focus, .dark select:focus {
  border-color: rgba(255, 28, 4, 0.4) !important;
  box-shadow:
    inset 4px 4px 8px hsl(var(--neumorphism-dark) / 0.7),
    inset -4px -4px 8px hsl(var(--neumorphism-light) / 0.1),
    0 0 0 2px rgba(255, 28, 4, 0.15) !important;
}

/* Enhanced placeholder visibility */
input::placeholder, textarea::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8 !important;
}

.dark input::placeholder, .dark textarea::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7 !important;
}

/* Form component fixes */
.form-control, .form-input {
  color: hsl(var(--input-foreground)) !important;
  background: hsl(var(--input)) !important;
}

.dark .form-control, .dark .form-input {
  color: hsl(var(--input-foreground)) !important;
  background: hsl(var(--input)) !important;
}

/* Glassmorphism utility classes - Non-transparent Neumorphism */
.glassmorphism {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.12);
  border-radius: 8px;
  box-shadow:
    6px 6px 12px hsl(var(--neumorphism-dark) / 0.25),
    -6px -6px 12px hsl(var(--neumorphism-light) / 0.7),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .glassmorphism {
  background: hsl(var(--neumorphism-bg));
  border: 1px solid rgba(255, 28, 4, 0.18);
  box-shadow:
    6px 6px 12px hsl(var(--neumorphism-dark) / 0.6),
    -6px -6px 12px hsl(var(--neumorphism-light) / 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.03);
}

/* Pulse glow animation for time tracking components */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(255, 28, 4, 0.3), 0 0 40px rgba(255, 28, 4, 0.1);
  }
  to {
    box-shadow: 0 0 30px rgba(255, 28, 4, 0.5), 0 0 60px rgba(255, 28, 4, 0.2);
  }
}

/* Floating animation for background elements */
.floating-animation {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Ripple animation for clock-in circles */
@keyframes ripple {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.3;
  }
}

/* Pulse glow animation for time tracking cards */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(255, 28, 4, 0.3), 0 0 40px rgba(255, 28, 4, 0.1);
  }
  to {
    box-shadow: 0 0 30px rgba(255, 28, 4, 0.5), 0 0 60px rgba(255, 28, 4, 0.2);
  }
}

/* Floating animation for background elements */
.floating-animation {
  animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Ripple animation for clock-in circles */
@keyframes ripple {
  0% {
    transform: rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg);
    opacity: 0.3;
  }
}

/* Enhanced Mobile Responsiveness with Neumorphism */
@media (max-width: 768px) {
  .glass-card {
    border-radius: 20px !important;
    margin: 0.5rem;
    padding: 1rem;
    box-shadow:
      6px 6px 12px hsl(var(--neumorphism-dark) / 0.25),
      -6px -6px 12px hsl(var(--neumorphism-light) / 0.7) !important;
  }

  .glass-card-modal {
    border-radius: 20px !important;
    margin: 1rem;
    box-shadow:
      6px 6px 12px hsl(var(--neumorphism-dark) / 0.2),
      -6px -6px 12px hsl(var(--neumorphism-light) / 0.8) !important;
  }

  .modern-card {
    border-radius: 20px !important;
    margin: 0.5rem;
    box-shadow:
      6px 6px 12px hsl(var(--neumorphism-dark) / 0.3),
      -6px -6px 12px hsl(var(--neumorphism-light) / 0.7) !important;
  }

  .stats-card {
    border-radius: 20px !important;
    margin: 0.5rem 0;
    box-shadow:
      6px 6px 12px hsl(var(--neumorphism-dark) / 0.25),
      -6px -6px 12px hsl(var(--neumorphism-light) / 0.7) !important;
  }

  .modern-3d-card {
    border-radius: 20px !important;
    margin: 0.5rem 0;
    box-shadow:
      6px 6px 12px hsl(var(--neumorphism-dark) / 0.3),
      -6px -6px 12px hsl(var(--neumorphism-light) / 0.7) !important;
  }

  /* AI Chat Mobile Responsiveness */
  .container_chat_bot {
    max-width: 100% !important;
    margin: 0 !important;
  }

  .container_chat_bot .container-chat-options {
    border-radius: 16px !important;
    margin: 0.5rem !important;
  }

  .container_chat_bot .chat .chat-bot textarea {
    font-size: 16px !important; /* Prevent zoom on iOS */
    height: 45px !important;
    padding: 12px !important;
  }

  .container_chat_bot .chat .options {
    padding: 12px !important;
  }

  .container_chat_bot .chat .options .btns-add {
    gap: 8px !important;
  }

  .container_chat_bot .chat .options .btns-add button {
    padding: 6px !important;
  }

  .container_chat_bot .tags {
    padding: 12px 0 !important;
    gap: 6px !important;
  }

  .container_chat_bot .tags span {
    padding: 6px 10px !important;
    font-size: 11px !important;
  }

  /* Reduce font sizes on mobile */
  h1 { font-size: 1.5rem !important; }
  h2 { font-size: 1.25rem !important; }
  h3 { font-size: 1.125rem !important; }

  /* Stack elements vertically on mobile */
  .flex-row-mobile {
    flex-direction: column !important;
  }

  /* Full width buttons on mobile */
  .btn-mobile-full {
    width: 100% !important;
    margin-bottom: 0.5rem;
  }

  /* Smaller padding on mobile */
  .p-mobile-sm {
    padding: 0.75rem !important;
  }

  /* Hide on mobile */
  .hidden-mobile {
    display: none !important;
  }

  /* Mobile navigation adjustments */
  .sidebar-mobile {
    width: 100% !important;
    height: auto !important;
  }

  /* Mobile dashboard grid */
  .dashboard-grid-mobile {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }
}

@media (max-width: 480px) {
  .glass-card {
    border-radius: 15px !important;
    margin: 0.25rem;
    padding: 0.75rem;
  }

  .glass-card-modal {
    border-radius: 15px !important;
    margin: 0.5rem;
  }

  .modern-card {
    border-radius: 15px !important;
    margin: 0.25rem;
  }

  /* Even smaller text on very small screens */
  h1 { font-size: 1.25rem !important; }
  h2 { font-size: 1.125rem !important; }
  h3 { font-size: 1rem !important; }

  /* Smaller buttons on very small screens */
  .btn-sm-mobile {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* Tablet responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .glass-card {
    border-radius: 25px !important;
  }

  .glass-card-modal {
    border-radius: 25px !important;
  }

  .modern-card {
    border-radius: 25px !important;
  }
}

/* Enhanced 3D Toast Notifications */
.toast-3d {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 30px;
  backdrop-filter: blur(20px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(255, 28, 4, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) rotateX(0);
  transform-style: preserve-3d;
}

.dark .toast-3d {
  background: rgba(25, 25, 25, 0.96);
  border-color: rgba(255, 28, 4, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    0 4px 16px rgba(255, 28, 4, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.toast-3d:hover {
  transform: translateY(-4px) rotateX(2deg);
  box-shadow:
    0 16px 40px rgba(0, 0, 0, 0.18),
    0 8px 24px rgba(255, 28, 4, 0.15),
    0 4px 12px rgba(15, 160, 206, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

.dark .toast-3d:hover {
  box-shadow:
    0 16px 40px rgba(0, 0, 0, 0.6),
    0 8px 24px rgba(255, 28, 4, 0.25),
    0 4px 12px rgba(0, 0, 0, 0.18),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Toast Responsive Behavior */
@media (max-width: 768px) {
  .toast-3d {
    border-radius: 20px;
    transform: scale(0.95);
    margin: 0.5rem 0;
    max-width: calc(100vw - 2rem);
  }

  .toast-3d:hover {
    transform: scale(0.97) translateY(-2px);
  }

  /* Mobile toast viewport adjustments */
  [data-radix-toast-viewport] {
    padding: 1rem !important;
    max-width: 100% !important;
  }
}

@media (max-width: 480px) {
  .toast-3d {
    border-radius: 16px;
    transform: scale(0.92);
    margin: 0.75rem 0;
    max-width: calc(100vw - 1rem);
    padding: 1rem !important;
  }

  .toast-3d:hover {
    transform: scale(0.94) translateY(-1px);
  }

  /* Extra small screens */
  [data-radix-toast-viewport] {
    padding: 0.5rem !important;
  }
}

/* Enhanced toast animations */
@keyframes toast-slide-in {
  from {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes toast-slide-out {
  from {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
  to {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
}

.toast-3d[data-state="open"] {
  animation: toast-slide-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-3d[data-state="closed"] {
  animation: toast-slide-out 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced AI Chat Module Styles with CTNL AI Colors */
.container_chat_bot {
  display: flex;
  flex-direction: column;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}

.container_chat_bot .container-chat-options {
  position: relative;
  display: flex;
  background: linear-gradient(
    to bottom right,
    #ff1c04,
    #dc2626,
    #991b1b,
    #7f1d1d,
    #450a0a
  );
  border-radius: 20px;
  padding: 2px;
  overflow: hidden;
}

.container_chat_bot .container-chat-options::after {
  position: absolute;
  content: "";
  top: -15px;
  left: -15px;
  background: radial-gradient(
    ellipse at center,
    rgba(255, 28, 4, 0.8),
    rgba(255, 28, 4, 0.4),
    rgba(255, 28, 4, 0.2),
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0)
  );
  width: 40px;
  height: 40px;
  filter: blur(2px);
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% { opacity: 0.5; transform: scale(1); }
  100% { opacity: 1; transform: scale(1.1); }
}

.container_chat_bot .container-chat-options .chat {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(20, 20, 20, 0.95) 50%,
    rgba(0, 0, 0, 0.9) 100%
  );
  border-radius: 18px;
  width: 100%;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 28, 4, 0.2);
}

.container_chat_bot .container-chat-options .chat .chat-bot {
  position: relative;
  display: flex;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.container_chat_bot .chat .chat-bot textarea {
  background-color: transparent;
  border-radius: 16px;
  border: none;
  width: 100%;
  height: 50px;
  color: #ffffff;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 400;
  padding: 15px;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
}

.container_chat_bot .chat .chat-bot textarea::-webkit-scrollbar {
  width: 6px;
}

.container_chat_bot .chat .chat-bot textarea::-webkit-scrollbar-track {
  background: transparent;
}

.container_chat_bot .chat .chat-bot textarea::-webkit-scrollbar-thumb {
  background: rgba(255, 28, 4, 0.4);
  border-radius: 3px;
}

.container_chat_bot .chat .chat-bot textarea::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 28, 4, 0.6);
  cursor: pointer;
}

.container_chat_bot .chat .chat-bot textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
}

.container_chat_bot .chat .chat-bot textarea:focus::placeholder {
  color: rgba(255, 28, 4, 0.7);
}

.container_chat_bot .chat .options {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
}

.container_chat_bot .chat .options .btns-add {
  display: flex;
  gap: 10px;
}

.container_chat_bot .chat .options .btns-add button {
  display: flex;
  color: rgba(255, 255, 255, 0.4);
  background-color: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 8px;
}

.container_chat_bot .chat .options .btns-add button:hover {
  transform: translateY(-3px);
  color: #ffffff;
  background: rgba(255, 28, 4, 0.1);
  box-shadow: 0 4px 12px rgba(255, 28, 4, 0.3);
}

.container_chat_bot .chat .options .btn-submit {
  display: flex;
  padding: 3px;
  background: linear-gradient(
    135deg,
    #ff1c04 0%,
    #dc2626 25%,
    #991b1b 50%,
    #dc2626 75%,
    #ff1c04 100%
  );
  border-radius: 12px;
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.2),
    0 4px 12px rgba(255, 28, 4, 0.4);
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.container_chat_bot .chat .options .btn-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.container_chat_bot .chat .options .btn-submit:hover::before {
  left: 100%;
}

.container_chat_bot .chat .options .btn-submit svg {
  width: 32px;
  height: 32px;
  padding: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  backdrop-filter: blur(5px);
  color: #ffffff;
  transition: all 0.3s ease;
}

.container_chat_bot .chat .options .btn-submit:hover svg {
  color: #ffffff;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
  transform: scale(1.05);
}

.container_chat_bot .chat .options .btn-submit:focus svg {
  color: #ffffff;
  filter: drop-shadow(0 0 12px rgba(255, 255, 255, 1));
  transform: scale(1.1) rotate(15deg);
}

.container_chat_bot .chat .options .btn-submit:active {
  transform: scale(0.95);
  box-shadow:
    inset 0 2px 4px rgba(0, 0, 0, 0.3),
    0 2px 6px rgba(255, 28, 4, 0.3);
}

.container_chat_bot .tags {
  padding: 16px 0;
  display: flex;
  color: #ffffff;
  font-size: 12px;
  gap: 8px;
  flex-wrap: wrap;
  justify-content: center;
}

.container_chat_bot .tags span {
  padding: 8px 12px;
  background: linear-gradient(135deg,
    rgba(20, 20, 20, 0.8) 0%,
    rgba(40, 40, 40, 0.9) 100%
  );
  border: 1.5px solid rgba(255, 28, 4, 0.3);
  border-radius: 12px;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  font-weight: 500;
  backdrop-filter: blur(5px);
}

.container_chat_bot .tags span:hover {
  background: linear-gradient(135deg,
    rgba(255, 28, 4, 0.2) 0%,
    rgba(220, 38, 38, 0.3) 100%
  );
  border-color: rgba(255, 28, 4, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 28, 4, 0.3);
}

/* Custom Scrollbar for AI Chat */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg,
    rgba(255, 28, 4, 0.6) 0%,
    rgba(220, 38, 38, 0.8) 100%
  );
  border-radius: 3px;
  border: 1px solid rgba(255, 28, 4, 0.2);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg,
    rgba(255, 28, 4, 0.8) 0%,
    rgba(220, 38, 38, 1) 100%
  );
  box-shadow: 0 0 6px rgba(255, 28, 4, 0.5);
}

/* General Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 28, 4, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 28, 4, 0.5);
}

/* Enhanced Card Animations */
@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes cardPulse {
  0%, 100% {
    box-shadow:
      15px 15px 30px rgba(0, 0, 0, 0.7),
      -15px -15px 30px rgba(60, 60, 60, 0.2);
  }
  50% {
    box-shadow:
      20px 20px 40px rgba(0, 0, 0, 0.8),
      -20px -20px 40px rgba(80, 80, 80, 0.3);
  }
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes borderGlow {
  0%, 100% {
    border-color: rgba(255, 28, 4, 0.1);
    box-shadow: 0 0 0 rgba(255, 28, 4, 0);
  }
  50% {
    border-color: rgba(255, 28, 4, 0.3);
    box-shadow: 0 0 20px rgba(255, 28, 4, 0.1);
  }
}

/* Card Animation Classes */
.card-float {
  animation: cardFloat 3s ease-in-out infinite;
}

.card-pulse {
  animation: cardPulse 2s ease-in-out infinite;
}

.card-glow {
  animation: borderGlow 2s ease-in-out infinite;
}

/* Enhanced AI Chat Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typingDots {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.message-slide-in {
  animation: messageSlideIn 0.3s ease-out;
}

.typing-indicator::after {
  content: '...';
  animation: typingDots 1.4s infinite;
}

/* Card Hover Enhancement Classes */
.card-hover-lift:hover {
  transform: translateY(-12px) scale(1.03) !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.card-hover-glow:hover {
  box-shadow:
    20px 20px 40px rgba(0, 0, 0, 0.8),
    -20px -20px 40px rgba(80, 80, 80, 0.3),
    0 0 60px rgba(255, 28, 4, 0.2) !important;
}

.card-hover-border:hover {
  border-color: rgba(255, 28, 4, 0.4) !important;
}

/* Staggered Animation for Card Grids */
.card-grid > *:nth-child(1) { animation-delay: 0.1s; }
.card-grid > *:nth-child(2) { animation-delay: 0.2s; }
.card-grid > *:nth-child(3) { animation-delay: 0.3s; }
.card-grid > *:nth-child(4) { animation-delay: 0.4s; }
.card-grid > *:nth-child(5) { animation-delay: 0.5s; }
.card-grid > *:nth-child(6) { animation-delay: 0.6s; }
