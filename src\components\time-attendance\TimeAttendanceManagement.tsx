import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Clock, Users, Calendar, MapPin, Download } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const TimeAttendanceManagement = () => {
  const [dateFilter, setDateFilter] = useState("today");
  const [departmentFilter, setDepartmentFilter] = useState("all");

  const { data: timeLogs, isLoading } = useQuery({
    queryKey: ['time-logs', dateFilter],
    queryFn: async () => {
      let query = supabase
        .from('time_logs')
        .select(`
          *,
          profiles:user_id (
            full_name,
            department:department_id (
              name
            )
          )
        `)
        .order('clock_in', { ascending: false });

      if (dateFilter === 'today') {
        const today = new Date().toISOString().split('T')[0];
        query = query.gte('clock_in', today + 'T00:00:00');
      } else if (dateFilter === 'week') {
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
        query = query.gte('clock_in', weekAgo);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });

  const { data: departments } = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name')
        .order('name');

      if (error) throw error;
      return data;
    },
  });

  const { data: realtimeStatus } = useQuery({
    queryKey: ['realtime-clock-status'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_realtime_clock_status');
      if (error) throw error;
      return data;
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-500/20 text-green-500' : 'bg-gray-500/20 text-gray-500';
  };

  const formatDuration = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.floor((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  const filteredTimeLogs = timeLogs?.filter(log => {
    if (departmentFilter === 'all') return true;
    return log.profiles?.department?.name === departmentFilter;
  });

  const totalHoursToday = timeLogs?.reduce((sum, log) => sum + (log.total_hours || 0), 0) || 0;
  const activeEmployees = realtimeStatus?.filter(status => status.is_clocked_in).length || 0;
  const totalEmployees = realtimeStatus?.length || 0;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Time & Attendance Management</h2>
          <p className="text-muted-foreground">Monitor employee attendance and working hours</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Employees</p>
              <p className="text-2xl font-bold">{totalEmployees}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Clock className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Currently Active</p>
              <p className="text-2xl font-bold text-green-600">{activeEmployees}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Calendar className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Hours Today</p>
              <p className="text-2xl font-bold">{formatDuration(totalHoursToday)}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <MapPin className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Attendance Rate</p>
              <p className="text-2xl font-bold">
                {totalEmployees > 0 ? Math.round((activeEmployees / totalEmployees) * 100) : 0}%
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="realtime" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="realtime">Real-time Status</TabsTrigger>
          <TabsTrigger value="logs">Time Logs</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="realtime" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Current Employee Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {realtimeStatus?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No employee status data available
                  </div>
                ) : (
                  realtimeStatus?.map((status) => (
                    <Card key={status.user_id} className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <h3 className="font-semibold flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            {status.user_name}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Department: {status.department_name || 'Not assigned'}
                          </p>
                          <div className="flex gap-2">
                            <Badge className={getStatusColor(status.is_clocked_in)}>
                              {status.is_clocked_in ? 'Clocked In' : 'Clocked Out'}
                            </Badge>
                          </div>
                          {status.is_clocked_in && (
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <p className="text-muted-foreground">Clock In Time</p>
                                <p className="font-medium">
                                  {status.clock_in_time ? new Date(status.clock_in_time).toLocaleTimeString() : 'N/A'}
                                </p>
                              </div>
                              <div>
                                <p className="text-muted-foreground">Duration</p>
                                <p className="font-medium">
                                  {status.duration_hours ? formatDuration(status.duration_hours) : '0h 0m'}
                                </p>
                              </div>
                            </div>
                          )}
                          {status.location_address && (
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {status.location_address}
                            </p>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Time Logs</CardTitle>
              <div className="flex gap-4">
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by department" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments?.map((dept) => (
                      <SelectItem key={dept.id} value={dept.name}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  <div className="text-center py-8">Loading time logs...</div>
                ) : filteredTimeLogs?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No time logs found for the selected period
                  </div>
                ) : (
                  filteredTimeLogs?.map((log) => (
                    <Card key={log.id} className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <h3 className="font-semibold">
                            {log.profiles?.full_name || 'Unknown Employee'}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Department: {log.profiles?.department?.name || 'Not assigned'}
                          </p>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Clock In</p>
                              <p className="font-medium">
                                {new Date(log.clock_in).toLocaleString()}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Clock Out</p>
                              <p className="font-medium">
                                {log.clock_out
                                  ? new Date(log.clock_out).toLocaleString()
                                  : 'Still active'
                                }
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Total Hours</p>
                              <p className="font-medium">
                                {log.total_hours ? formatDuration(log.total_hours) : 'In progress'}
                              </p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Status</p>
                              <Badge className={getStatusColor(!log.clock_out_timestamp)}>
                                {log.clock_out_timestamp ? 'Completed' : 'Active'}
                              </Badge>
                            </div>
                          </div>
                          {log.location_address && (
                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {log.location_address}
                            </p>
                          )}
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Daily Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Clock-ins Today</span>
                    <span className="font-bold">
                      {timeLogs?.filter(log =>
                        new Date(log.clock_in).toDateString() === new Date().toDateString()
                      ).length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Hours per Employee</span>
                    <span className="font-bold">
                      {timeLogs?.length ? formatDuration(totalHoursToday / timeLogs.length) : '0h 0m'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Early Clock-ins</span>
                    <span className="font-bold">0</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Late Clock-outs</span>
                    <span className="font-bold">0</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Department Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {departments?.map((dept) => {
                    const deptLogs = timeLogs?.filter(log => 
                      log.profiles?.department?.name === dept.name
                    ) || [];
                    const deptHours = deptLogs.reduce((sum, log) => sum + (log.total_hours || 0), 0);
                    
                    return (
                      <div key={dept.id} className="flex justify-between">
                        <span>{dept.name}</span>
                        <span className="font-bold">{formatDuration(deptHours)}</span>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Attendance Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Detailed attendance analytics, trends, and insights will be displayed here.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
