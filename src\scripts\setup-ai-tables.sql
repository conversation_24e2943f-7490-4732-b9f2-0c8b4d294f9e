-- AI System Database Tables Setup
-- Run this script in your Supabase SQL editor to create all necessary AI tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Conversation History Table (for <PERSON><PERSON><PERSON><PERSON>)
CREATE TABLE IF NOT EXISTS public.conversation_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversation Analytics Table
CREATE TABLE IF NOT EXISTS public.conversation_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    human_message_length INTEGER,
    ai_message_length INTEGER,
    processing_time INTEGER, -- in milliseconds
    model_used TEXT,
    interface_type TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- LangChain Operations Monitoring Table
CREATE TABLE IF NOT EXISTS public.langchain_operations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('chat', 'rag', 'agent', 'document_processing', 'summarization')),
    duration INTEGER NOT NULL, -- in milliseconds
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    session_id TEXT,
    model TEXT,
    tokens_used INTEGER,
    input_length INTEGER,
    output_length INTEGER,
    interface_type TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Interactions Table (for general AI interactions)
CREATE TABLE IF NOT EXISTS public.ai_interactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'user',
    message TEXT NOT NULL,
    query TEXT,
    type TEXT DEFAULT 'general',
    interface_type TEXT DEFAULT 'standard',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Results Table (for storing AI analysis results)
CREATE TABLE IF NOT EXISTS public.ai_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    query_text TEXT NOT NULL,
    result_data JSONB NOT NULL,
    model_used TEXT DEFAULT 'gpt-4',
    confidence_score DECIMAL(3,2),
    processing_time INTEGER,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Voice Commands Table (for voice interaction logs)
CREATE TABLE IF NOT EXISTS public.voice_commands (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    command_text TEXT NOT NULL,
    recognized_intent TEXT,
    action_taken TEXT,
    success BOOLEAN DEFAULT TRUE,
    confidence_score DECIMAL(3,2),
    processing_time INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System Health Table (for self-healing monitoring)
CREATE TABLE IF NOT EXISTS public.system_health (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    component_name TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('healthy', 'warning', 'error', 'critical')),
    metrics JSONB DEFAULT '{}',
    error_details TEXT,
    auto_healing_attempted BOOLEAN DEFAULT FALSE,
    healing_actions JSONB DEFAULT '[]',
    last_check TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversation_history_user_session ON public.conversation_history(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_history_created_at ON public.conversation_history(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_analytics_user_id ON public.conversation_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_analytics_created_at ON public.conversation_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_langchain_operations_type ON public.langchain_operations(operation_type);
CREATE INDEX IF NOT EXISTS idx_langchain_operations_user_id ON public.langchain_operations(user_id);
CREATE INDEX IF NOT EXISTS idx_langchain_operations_created_at ON public.langchain_operations(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON public.ai_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_type ON public.ai_interactions(type);
CREATE INDEX IF NOT EXISTS idx_ai_interactions_created_at ON public.ai_interactions(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_results_created_by ON public.ai_results(created_by);
CREATE INDEX IF NOT EXISTS idx_ai_results_created_at ON public.ai_results(created_at);
CREATE INDEX IF NOT EXISTS idx_voice_commands_user_id ON public.voice_commands(user_id);
CREATE INDEX IF NOT EXISTS idx_voice_commands_created_at ON public.voice_commands(created_at);
CREATE INDEX IF NOT EXISTS idx_system_health_component ON public.system_health(component_name);
CREATE INDEX IF NOT EXISTS idx_system_health_status ON public.system_health(status);
CREATE INDEX IF NOT EXISTS idx_system_health_created_at ON public.system_health(created_at);

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.conversation_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.conversation_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.langchain_operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.voice_commands ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_health ENABLE ROW LEVEL SECURITY;

-- Conversation History Policies
CREATE POLICY "Users can view their own conversation history" ON public.conversation_history
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own conversation history" ON public.conversation_history
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all conversation history" ON public.conversation_history
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- Conversation Analytics Policies
CREATE POLICY "Users can view their own analytics" ON public.conversation_analytics
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own analytics" ON public.conversation_analytics
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all analytics" ON public.conversation_analytics
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- LangChain Operations Policies
CREATE POLICY "Users can view their own operations" ON public.langchain_operations
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert operations" ON public.langchain_operations
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Admins can view all operations" ON public.langchain_operations
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- AI Interactions Policies
CREATE POLICY "Users can manage their own AI interactions" ON public.ai_interactions
    FOR ALL TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all AI interactions" ON public.ai_interactions
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- AI Results Policies
CREATE POLICY "Users can view AI results they created" ON public.ai_results
    FOR SELECT TO authenticated
    USING (auth.uid() = created_by OR created_by IS NULL);

CREATE POLICY "Users can insert AI results" ON public.ai_results
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = created_by OR created_by IS NULL);

CREATE POLICY "Admins can manage all AI results" ON public.ai_results
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- Voice Commands Policies
CREATE POLICY "Users can manage their own voice commands" ON public.voice_commands
    FOR ALL TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Admins can view all voice commands" ON public.voice_commands
    FOR SELECT TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- System Health Policies
CREATE POLICY "All authenticated users can view system health" ON public.system_health
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Admins can manage system health" ON public.system_health
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'manager')
        )
    );

-- Insert initial system health records
INSERT INTO public.system_health (component_name, status, metrics) VALUES
('ai_system', 'healthy', '{"last_check": "' || NOW() || '", "uptime": "100%"}'),
('langchain_service', 'healthy', '{"last_check": "' || NOW() || '", "availability": "ready"}'),
('voice_recognition', 'healthy', '{"last_check": "' || NOW() || '", "browser_support": "available"}'),
('database_connection', 'healthy', '{"last_check": "' || NOW() || '", "response_time": "fast"}')
ON CONFLICT DO NOTHING;

-- Create a function to update system health
CREATE OR REPLACE FUNCTION update_system_health(
    component TEXT,
    new_status TEXT,
    health_metrics JSONB DEFAULT '{}'
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.system_health (component_name, status, metrics, last_check)
    VALUES (component, new_status, health_metrics, NOW())
    ON CONFLICT (component_name) 
    DO UPDATE SET 
        status = EXCLUDED.status,
        metrics = EXCLUDED.metrics,
        last_check = EXCLUDED.last_check;
END;
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION update_system_health TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'AI System tables created successfully! 🚀';
    RAISE NOTICE 'Tables created: conversation_history, conversation_analytics, langchain_operations, ai_interactions, ai_results, voice_commands, system_health';
    RAISE NOTICE 'RLS policies applied for security';
    RAISE NOTICE 'Indexes created for performance';
    RAISE NOTICE 'Ready for AI system integration!';
END $$;
