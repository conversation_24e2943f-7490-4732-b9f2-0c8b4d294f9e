import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  FileImage, 
  File,
  Calendar,
  Filter,
  Settings
} from 'lucide-react';
import { ProcurementExportService } from '@/lib/procurement-export';
import { useToast } from '@/hooks/use-toast';

interface ProcurementExportDialogProps {
  trigger?: React.ReactNode;
}

const EXPORT_FORMATS = [
  {
    value: 'csv',
    label: 'CSV',
    description: 'Comma-separated values for spreadsheet applications',
    icon: FileSpreadsheet,
    color: 'bg-green-100 text-green-800'
  },
  {
    value: 'xlsx',
    label: 'Excel',
    description: 'Microsoft Excel spreadsheet format',
    icon: FileSpreadsheet,
    color: 'bg-blue-100 text-blue-800'
  },
  {
    value: 'pdf',
    label: 'PDF',
    description: 'Portable Document Format for reports',
    icon: FileText,
    color: 'bg-red-100 text-red-800'
  },
  {
    value: 'docx',
    label: 'Word',
    description: 'Microsoft Word document format',
    icon: File,
    color: 'bg-purple-100 text-purple-800'
  }
];

const STATUS_OPTIONS = [
  'pending',
  'manager_review',
  'approved',
  'rejected',
  'admin_processing',
  'accountant_review',
  'invoice_generated',
  'procured',
  'cancelled'
];

const PRIORITY_OPTIONS = ['low', 'medium', 'high', 'urgent'];

export const ProcurementExportDialog: React.FC<ProcurementExportDialogProps> = ({ trigger }) => {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  
  // Export settings
  const [selectedFormat, setSelectedFormat] = useState<'csv' | 'xlsx' | 'pdf' | 'docx'>('csv');
  const [dateRange, setDateRange] = useState({
    from: '',
    to: ''
  });
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [priorityFilters, setPriorityFilters] = useState<string[]>([]);
  const [includeItems, setIncludeItems] = useState(true);
  const [includeApprovals, setIncludeApprovals] = useState(true);
  const [includeInvoices, setIncludeInvoices] = useState(true);

  const handleExport = async () => {
    setIsExporting(true);
    
    try {
      // Build filters object
      const filters: any = {};
      
      if (statusFilters.length > 0) {
        filters.status = statusFilters;
      }
      
      if (priorityFilters.length > 0) {
        filters.priority = priorityFilters;
      }
      
      if (dateRange.from) {
        filters.created_from = dateRange.from;
      }
      
      if (dateRange.to) {
        filters.created_to = dateRange.to;
      }

      // Add include options to filters
      filters.include_items = includeItems;
      filters.include_approvals = includeApprovals;
      filters.include_invoices = includeInvoices;

      // Perform export
      await ProcurementExportService.exportData(selectedFormat, filters);
      
      toast({
        title: "Export Successful",
        description: `Procurement data exported as ${selectedFormat.toUpperCase()}`,
      });
      
      setIsOpen(false);
    } catch (error: any) {
      toast({
        title: "Export Failed",
        description: error.message || "Failed to export data",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const toggleStatusFilter = (status: string) => {
    setStatusFilters(prev => 
      prev.includes(status) 
        ? prev.filter(s => s !== status)
        : [...prev, status]
    );
  };

  const togglePriorityFilter = (priority: string) => {
    setPriorityFilters(prev => 
      prev.includes(priority) 
        ? prev.filter(p => p !== priority)
        : [...prev, priority]
    );
  };

  const resetFilters = () => {
    setDateRange({ from: '', to: '' });
    setStatusFilters([]);
    setPriorityFilters([]);
    setIncludeItems(true);
    setIncludeApprovals(true);
    setIncludeInvoices(true);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Procurement Data
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Format Selection */}
          <div>
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-2 gap-3 mt-2">
              {EXPORT_FORMATS.map((format) => {
                const Icon = format.icon;
                return (
                  <Card 
                    key={format.value}
                    className={`cursor-pointer transition-all ${
                      selectedFormat === format.value 
                        ? 'ring-2 ring-primary bg-primary/5' 
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedFormat(format.value as any)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Icon className="h-5 w-5 mt-0.5 text-muted-foreground" />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{format.label}</span>
                            <Badge className={format.color}>{format.value.toUpperCase()}</Badge>
                          </div>
                          <p className="text-xs text-muted-foreground mt-1">
                            {format.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Date Range Filter */}
          <div>
            <Label className="text-base font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Date Range (Optional)
            </Label>
            <div className="grid grid-cols-2 gap-3 mt-2">
              <div>
                <Label htmlFor="date_from" className="text-sm">From</Label>
                <input
                  id="date_from"
                  type="date"
                  value={dateRange.from}
                  onChange={(e) => setDateRange({ ...dateRange, from: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <Label htmlFor="date_to" className="text-sm">To</Label>
                <input
                  id="date_to"
                  type="date"
                  value={dateRange.to}
                  onChange={(e) => setDateRange({ ...dateRange, to: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>

          {/* Status Filters */}
          <div>
            <Label className="text-base font-medium flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Status Filters (Optional)
            </Label>
            <div className="grid grid-cols-3 gap-2 mt-2">
              {STATUS_OPTIONS.map((status) => (
                <div key={status} className="flex items-center space-x-2">
                  <Checkbox
                    id={`status-${status}`}
                    checked={statusFilters.includes(status)}
                    onCheckedChange={() => toggleStatusFilter(status)}
                  />
                  <Label 
                    htmlFor={`status-${status}`} 
                    className="text-sm cursor-pointer"
                  >
                    {status.replace('_', ' ').toUpperCase()}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Priority Filters */}
          <div>
            <Label className="text-base font-medium">Priority Filters (Optional)</Label>
            <div className="grid grid-cols-4 gap-2 mt-2">
              {PRIORITY_OPTIONS.map((priority) => (
                <div key={priority} className="flex items-center space-x-2">
                  <Checkbox
                    id={`priority-${priority}`}
                    checked={priorityFilters.includes(priority)}
                    onCheckedChange={() => togglePriorityFilter(priority)}
                  />
                  <Label 
                    htmlFor={`priority-${priority}`} 
                    className="text-sm cursor-pointer"
                  >
                    {priority.toUpperCase()}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Include Options */}
          <div>
            <Label className="text-base font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Include in Export
            </Label>
            <div className="space-y-2 mt-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-items"
                  checked={includeItems}
                  onCheckedChange={setIncludeItems}
                />
                <Label htmlFor="include-items" className="text-sm cursor-pointer">
                  Item Details
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-approvals"
                  checked={includeApprovals}
                  onCheckedChange={setIncludeApprovals}
                />
                <Label htmlFor="include-approvals" className="text-sm cursor-pointer">
                  Approval History
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-invoices"
                  checked={includeInvoices}
                  onCheckedChange={setIncludeInvoices}
                />
                <Label htmlFor="include-invoices" className="text-sm cursor-pointer">
                  Invoice Information
                </Label>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button 
              onClick={resetFilters} 
              variant="outline" 
              className="flex-1"
            >
              Reset Filters
            </Button>
            
            <Button 
              onClick={handleExport} 
              disabled={isExporting}
              className="flex-1"
            >
              {isExporting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Export {selectedFormat.toUpperCase()}
                </>
              )}
            </Button>
          </div>

          {/* Export Summary */}
          {(statusFilters.length > 0 || priorityFilters.length > 0 || dateRange.from || dateRange.to) && (
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm font-medium text-blue-800 mb-2">Active Filters:</p>
              <div className="flex flex-wrap gap-1">
                {statusFilters.map(status => (
                  <Badge key={status} variant="secondary" className="text-xs">
                    Status: {status.replace('_', ' ')}
                  </Badge>
                ))}
                {priorityFilters.map(priority => (
                  <Badge key={priority} variant="secondary" className="text-xs">
                    Priority: {priority}
                  </Badge>
                ))}
                {dateRange.from && (
                  <Badge variant="secondary" className="text-xs">
                    From: {dateRange.from}
                  </Badge>
                )}
                {dateRange.to && (
                  <Badge variant="secondary" className="text-xs">
                    To: {dateRange.to}
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
