-- 🧪 CREATE TEST USER PROFILE
-- This script creates a profile for the existing test user

-- First, let's check if the user exists
SELECT 
    'Existing auth users:' as info,
    id,
    email,
    created_at
FROM auth.users
ORDER BY created_at DESC;

-- Create profile for the test user if it doesn't exist
INSERT INTO public.profiles (
    id,
    email,
    full_name,
    role,
    department_id,
    status,
    created_at,
    updated_at
)
SELECT 
    au.id,
    au.email,
    'Test Admin User',
    'admin',
    '11111111-1111-1111-1111-111111111111'::UUID, -- IT Department
    'active',
    NOW(),
    NOW()
FROM auth.users au
WHERE au.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    full_name = 'Test Admin User',
    role = 'admin',
    department_id = '11111111-1111-1111-1111-111111111111'::UUID,
    status = 'active',
    updated_at = NOW();

-- Verify the profile was created
SELECT 
    'Profile verification:' as info,
    p.id,
    p.email,
    p.full_name,
    p.role,
    p.status,
    d.name as department_name
FROM public.profiles p
LEFT JOIN public.departments d ON p.department_id = d.id
WHERE p.email = '<EMAIL>';

-- Show all departments for reference
SELECT 
    'Available departments:' as info,
    id,
    name,
    description
FROM public.departments
ORDER BY name;
