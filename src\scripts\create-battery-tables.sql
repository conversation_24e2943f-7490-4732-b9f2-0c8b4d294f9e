-- Battery Management System Tables
-- This script creates comprehensive battery tracking tables with audit columns

-- 1. Battery Types Table
CREATE TABLE IF NOT EXISTS battery_types (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  voltage DECIMAL(5,2) NOT NULL,
  capacity_ah DECIMAL(8,2) NOT NULL,
  chemistry VARCHAR(50) NOT NULL, -- Lead-acid, Lithium-ion, etc.
  manufacturer VARCHAR(100),
  model VARCHAR(100),
  specifications JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  updated_by UUID REFERENCES profiles(id)
);

-- 2. Battery Locations Table
CREATE TABLE IF NOT EXISTS battery_locations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(100) NOT NULL,
  description TEXT,
  location_type VARCHAR(50) NOT NULL, -- Site, Warehouse, Vehicle, etc.
  address TEXT,
  coordinates POINT,
  parent_location_id UUID REFERENCES battery_locations(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  updated_by UUID REFERENCES profiles(id)
);

-- 3. Batteries Table (Main inventory)
CREATE TABLE IF NOT EXISTS batteries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  serial_number VARCHAR(100) NOT NULL UNIQUE,
  battery_type_id UUID NOT NULL REFERENCES battery_types(id),
  current_location_id UUID REFERENCES battery_locations(id),
  status VARCHAR(50) NOT NULL DEFAULT 'new', -- new, active, maintenance, retired, disposed
  condition VARCHAR(50) NOT NULL DEFAULT 'excellent', -- excellent, good, fair, poor, failed
  purchase_date DATE,
  installation_date DATE,
  warranty_expiry_date DATE,
  last_maintenance_date DATE,
  next_maintenance_date DATE,
  purchase_cost DECIMAL(10,2),
  supplier VARCHAR(100),
  notes TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  updated_by UUID REFERENCES profiles(id),
  profile_id UUID REFERENCES profiles(id) -- For user-specific batteries
);

-- 4. Battery Readings Table (Performance tracking)
CREATE TABLE IF NOT EXISTS battery_readings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  battery_id UUID NOT NULL REFERENCES batteries(id) ON DELETE CASCADE,
  reading_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  voltage DECIMAL(5,2),
  current_amperage DECIMAL(8,2),
  temperature DECIMAL(5,2),
  state_of_charge DECIMAL(5,2), -- Percentage
  capacity_remaining DECIMAL(8,2),
  internal_resistance DECIMAL(8,4),
  reading_type VARCHAR(50) NOT NULL DEFAULT 'manual', -- manual, automatic, scheduled
  technician_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  generated_by VARCHAR(100), -- System, Device, or User identifier
  profile_id UUID REFERENCES profiles(id)
);

-- 5. Battery Maintenance Records
CREATE TABLE IF NOT EXISTS battery_maintenance (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  battery_id UUID NOT NULL REFERENCES batteries(id) ON DELETE CASCADE,
  maintenance_type VARCHAR(100) NOT NULL, -- inspection, cleaning, replacement, repair
  maintenance_date DATE NOT NULL,
  technician_id UUID REFERENCES profiles(id),
  description TEXT NOT NULL,
  parts_used TEXT,
  cost DECIMAL(10,2),
  next_maintenance_date DATE,
  status VARCHAR(50) DEFAULT 'completed', -- scheduled, in_progress, completed, cancelled
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  updated_by UUID REFERENCES profiles(id),
  profile_id UUID REFERENCES profiles(id)
);

-- 6. Battery Transfers/Movements
CREATE TABLE IF NOT EXISTS battery_transfers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  battery_id UUID NOT NULL REFERENCES batteries(id) ON DELETE CASCADE,
  from_location_id UUID REFERENCES battery_locations(id),
  to_location_id UUID NOT NULL REFERENCES battery_locations(id),
  transfer_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transferred_by UUID REFERENCES profiles(id),
  received_by UUID REFERENCES profiles(id),
  reason VARCHAR(200),
  notes TEXT,
  status VARCHAR(50) DEFAULT 'completed', -- pending, in_transit, completed, cancelled
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES profiles(id),
  profile_id UUID REFERENCES profiles(id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_batteries_serial_number ON batteries(serial_number);
CREATE INDEX IF NOT EXISTS idx_batteries_type ON batteries(battery_type_id);
CREATE INDEX IF NOT EXISTS idx_batteries_location ON batteries(current_location_id);
CREATE INDEX IF NOT EXISTS idx_batteries_status ON batteries(status);
CREATE INDEX IF NOT EXISTS idx_batteries_created_by ON batteries(created_by);
CREATE INDEX IF NOT EXISTS idx_batteries_profile_id ON batteries(profile_id);

CREATE INDEX IF NOT EXISTS idx_battery_readings_battery ON battery_readings(battery_id);
CREATE INDEX IF NOT EXISTS idx_battery_readings_date ON battery_readings(reading_date);
CREATE INDEX IF NOT EXISTS idx_battery_readings_created_by ON battery_readings(created_by);

CREATE INDEX IF NOT EXISTS idx_battery_maintenance_battery ON battery_maintenance(battery_id);
CREATE INDEX IF NOT EXISTS idx_battery_maintenance_date ON battery_maintenance(maintenance_date);
CREATE INDEX IF NOT EXISTS idx_battery_maintenance_technician ON battery_maintenance(technician_id);

CREATE INDEX IF NOT EXISTS idx_battery_transfers_battery ON battery_transfers(battery_id);
CREATE INDEX IF NOT EXISTS idx_battery_transfers_date ON battery_transfers(transfer_date);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_battery_types_updated_at BEFORE UPDATE ON battery_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_battery_locations_updated_at BEFORE UPDATE ON battery_locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_batteries_updated_at BEFORE UPDATE ON batteries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_battery_maintenance_updated_at BEFORE UPDATE ON battery_maintenance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample battery types
INSERT INTO battery_types (name, description, voltage, capacity_ah, chemistry, manufacturer, model) VALUES
('Deep Cycle 12V 100Ah', 'Standard deep cycle battery for telecom sites', 12.0, 100.0, 'Lead-acid', 'Trojan', 'T-105'),
('Lithium 48V 200Ah', 'High capacity lithium battery bank', 48.0, 200.0, 'Lithium-ion', 'Tesla', 'Powerwall'),
('UPS 12V 7Ah', 'Small UPS backup battery', 12.0, 7.0, 'Lead-acid', 'APC', 'RBC7'),
('Solar 6V 225Ah', 'Solar energy storage battery', 6.0, 225.0, 'Lead-acid', 'Crown', 'CR-225')
ON CONFLICT (name) DO NOTHING;

-- Insert sample locations
INSERT INTO battery_locations (name, description, location_type, address) VALUES
('Main Warehouse', 'Central battery storage facility', 'Warehouse', '123 Industrial Ave, Lagos'),
('Site Alpha', 'Telecom tower site Alpha', 'Site', 'Alpha Tower Location, Abuja'),
('Site Beta', 'Telecom tower site Beta', 'Site', 'Beta Tower Location, Port Harcourt'),
('Mobile Unit 1', 'Service vehicle battery storage', 'Vehicle', 'Mobile Service Unit'),
('Repair Shop', 'Battery maintenance and repair facility', 'Workshop', '456 Repair St, Lagos')
ON CONFLICT DO NOTHING;
