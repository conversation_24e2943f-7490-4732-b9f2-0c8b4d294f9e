import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Clock, 
  Target, 
  TrendingUp, 
  Save, 
  X,
  AlertCircle,
  CheckCircle2
} from "lucide-react";
import { useUpdateAssignmentProgress } from "@/hooks/useProjectProgress";
import { ProjectWithProgress, ProjectAssignment } from "@/lib/project-progress-api";
import { toast } from "sonner";

interface ProjectProgressUpdateFormProps {
  project: ProjectWithProgress;
  assignment: ProjectAssignment;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export const ProjectProgressUpdateForm: React.FC<ProjectProgressUpdateFormProps> = ({
  project,
  assignment,
  open,
  onOpenChange,
  onSuccess
}) => {
  const [progress, setProgress] = useState(assignment?.progress_percentage || 0);
  const [hoursWorked, setHoursWorked] = useState(assignment?.hours_worked || 0);
  const [status, setStatus] = useState(assignment?.status || 'assigned');
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateProgressMutation = useUpdateAssignmentProgress();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!assignment?.id) {
      toast.error('Assignment not found');
      return;
    }

    setIsSubmitting(true);

    try {
      await updateProgressMutation.mutateAsync({
        assignmentId: assignment.id,
        progressData: {
          progress_percentage: progress,
          hours_worked: hoursWorked,
          status,
          notes: notes.trim() || undefined
        }
      });

      onSuccess?.();
    } catch (error) {
      console.error('Failed to update progress:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleProgressChange = (value: number[]) => {
    setProgress(value[0]);
    
    // Auto-update status based on progress
    if (value[0] === 100 && status !== 'completed') {
      setStatus('completed');
    } else if (value[0] > 0 && status === 'assigned') {
      setStatus('in_progress');
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'text-green-600';
    if (progress >= 60) return 'text-blue-600';
    if (progress >= 40) return 'text-yellow-600';
    if (progress >= 20) return 'text-orange-600';
    return 'text-red-600';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
      case 'assigned': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const hoursRemaining = (assignment?.hours_allocated || 0) - hoursWorked;
  const progressDifference = progress - (assignment?.progress_percentage || 0);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Update Project Progress
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Project Info */}
          <Card className="glassmorphism">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">{project.name}</CardTitle>
              <div className="flex items-center gap-2">
                <Badge className={getStatusColor(assignment?.status || 'assigned')}>
                  {assignment?.status?.replace('_', ' ')}
                </Badge>
                <Badge variant="outline">
                  {assignment?.role?.replace('_', ' ')}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <p className="font-medium text-gray-600">Current Progress</p>
                  <p className={`text-2xl font-bold ${getProgressColor(assignment?.progress_percentage || 0)}`}>
                    {assignment?.progress_percentage || 0}%
                  </p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-600">Hours Worked</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {assignment?.hours_worked || 0}h
                  </p>
                </div>
                <div className="text-center">
                  <p className="font-medium text-gray-600">Hours Allocated</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {assignment?.hours_allocated || 0}h
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Progress Update */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-semibold mb-3 block">
                Update Progress ({progress}%)
              </Label>
              <div className="space-y-3">
                <Slider
                  value={[progress]}
                  onValueChange={handleProgressChange}
                  max={100}
                  step={5}
                  className="w-full"
                />
                <Progress value={progress} className="h-3" />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>0%</span>
                  <span className={`font-medium ${getProgressColor(progress)}`}>
                    {progress}%
                  </span>
                  <span>100%</span>
                </div>
              </div>
              
              {progressDifference !== 0 && (
                <div className="mt-2 p-2 rounded-lg bg-blue-50 border border-blue-200">
                  <p className="text-sm text-blue-700">
                    {progressDifference > 0 ? '↗️' : '↘️'} 
                    Progress {progressDifference > 0 ? 'increased' : 'decreased'} by {Math.abs(progressDifference)}%
                  </p>
                </div>
              )}
            </div>

            {/* Hours Worked */}
            <div>
              <Label htmlFor="hoursWorked" className="text-base font-semibold">
                Hours Worked
              </Label>
              <div className="mt-2 space-y-2">
                <Input
                  id="hoursWorked"
                  type="number"
                  min="0"
                  max={assignment?.hours_allocated || 1000}
                  step="0.5"
                  value={hoursWorked}
                  onChange={(e) => setHoursWorked(parseFloat(e.target.value) || 0)}
                  className="text-lg"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>Previous: {assignment?.hours_worked || 0}h</span>
                  <span className={hoursRemaining < 0 ? 'text-red-600 font-medium' : 'text-gray-600'}>
                    Remaining: {hoursRemaining}h
                  </span>
                </div>
                {hoursRemaining < 0 && (
                  <div className="flex items-center gap-2 p-2 rounded-lg bg-red-50 border border-red-200">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <p className="text-sm text-red-700">
                      Hours worked exceeds allocated hours by {Math.abs(hoursRemaining)}h
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Status Update */}
            <div>
              <Label className="text-base font-semibold">Status</Label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="assigned">Assigned</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Notes */}
            <div>
              <Label htmlFor="notes" className="text-base font-semibold">
                Progress Notes (Optional)
              </Label>
              <Textarea
                id="notes"
                placeholder="Describe what you've accomplished, any blockers, or next steps..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="mt-2 min-h-[100px]"
                maxLength={500}
              />
              <p className="text-sm text-gray-500 mt-1">
                {notes.length}/500 characters
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Updating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Update Progress
                </>
              )}
            </Button>
          </div>

          {/* Completion Celebration */}
          {progress === 100 && (
            <div className="p-4 rounded-lg bg-green-50 border border-green-200 text-center">
              <CheckCircle2 className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <p className="text-green-800 font-medium">
                🎉 Congratulations! You're about to complete this project!
              </p>
            </div>
          )}
        </form>
      </DialogContent>
    </Dialog>
  );
};
