import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';
const supabase = createClient(supabaseUrl, supabaseKey);

async function testEmailWithNewDomain() {
  try {
    console.log('🧪 Testing email functionality with verified domain ai.ctnigeria.com...');

    // Test the send-notification function with the new domain
    const { data, error } = await supabase.functions.invoke('send-notification', {
      body: {
        type: 'test_notification',
        recipients: ['<EMAIL>'],
        data: {
          userName: 'System Administrator',
          testMessage: 'This is a test email from CTNL AI WORK-BOARD using the verified domain ai.ctnigeria.com. The email system has been updated with the new app name and domain configuration.',
          systemName: 'CTNL AI WORK-BOARD',
          manualUrl: 'https://ai.ctnigeria.com/user-manual.html',
          dashboardUrl: 'https://ai.ctnigeria.com/dashboard'
        }
      }
    });

    if (error) {
      console.error('❌ Email test failed:', error);
      throw error;
    }

    console.log('✅ Email test successful!');
    console.log('📧 Email sent to: <EMAIL>');
    console.log('🏷️  From: CTNL AI WORK-BOARD <<EMAIL>>');
    console.log('🔗 Manual URL: https://ai.ctnigeria.com/user-manual.html');
    console.log('📊 Response:', data);

    return {
      success: true,
      message: 'Email test completed successfully',
      data
    };

  } catch (error) {
    console.error('💥 Email test failed:', error);
    throw error;
  }
}

// Execute the test
testEmailWithNewDomain()
  .then((result) => {
    console.log('🎉 Email test completed:', result);
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Email test failed:', error);
    process.exit(1);
  });
