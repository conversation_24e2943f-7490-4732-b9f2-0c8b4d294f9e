import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { createCorsResponse, withCors } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openAIKey = Deno.env.get('OPENAI_API_KEY') || Deno.env.get('VITE_OPENAI_API_KEY');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function buildEnhancedContext(context: any = {}) {
  const enhancedContext = { ...context };

  try {
    // Fetch user profile and recent activity if userId is provided
    if (context.userId) {
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('full_name, role, department, avatar_url')
        .eq('id', context.userId)
        .single();

      if (userProfile) {
        enhancedContext.userProfile = userProfile;
      }

      // Get recent user activity for context
      const { data: recentActivity } = await supabase
        .from('activity_logs')
        .select('action, entity_type, created_at')
        .eq('user_id', context.userId)
        .order('created_at', { ascending: false })
        .limit(5);

      if (recentActivity) {
        enhancedContext.recentActivity = recentActivity;
      }

      // Get user's current projects
      const { data: userProjects } = await supabase
        .from('project_members')
        .select(`
          projects (
            id, name, status, description
          )
        `)
        .eq('user_id', context.userId)
        .limit(5);

      if (userProjects) {
        enhancedContext.currentProjects = userProjects.map(pm => pm.projects);
      }

      // Get pending tasks
      const { data: pendingTasks } = await supabase
        .from('tasks')
        .select('id, title, status, priority, due_date')
        .eq('assigned_to', context.userId)
        .in('status', ['pending', 'in_progress'])
        .order('due_date', { ascending: true })
        .limit(5);

      if (pendingTasks) {
        enhancedContext.pendingTasks = pendingTasks;
      }
    }

    // Add system-wide context
    enhancedContext.systemInfo = {
      currentTime: new Date().toISOString(),
      platform: 'CTNL AI Work-Board',
      version: '2.0'
    };

  } catch (error) {
    console.error('Error building enhanced context:', error);
  }

  return enhancedContext;
}

function buildSystemPrompt(context: any) {
  const userInfo = context.userProfile ?
    `User: ${context.userProfile.full_name} (${context.userProfile.role} in ${context.userProfile.department})` :
    `User: ${context.role || 'User'} in ${context.department || 'General'}`;

  const currentProjects = context.currentProjects && context.currentProjects.length > 0 ?
    `\nCurrent Projects: ${context.currentProjects.map(p => `${p.name} (${p.status})`).join(', ')}` : '';

  const pendingTasks = context.pendingTasks && context.pendingTasks.length > 0 ?
    `\nPending Tasks: ${context.pendingTasks.map(t => `${t.title} (${t.priority} priority, due: ${t.due_date})`).join(', ')}` : '';

  const recentActivity = context.recentActivity && context.recentActivity.length > 0 ?
    `\nRecent Activity: ${context.recentActivity.map(a => `${a.action} on ${a.entity_type}`).join(', ')}` : '';

  return `You are an intelligent AI assistant for CTNL AI WORK-BOARD, a comprehensive workforce management system. You have access to real-time user data and system context.

SYSTEM CAPABILITIES:
- Time tracking and attendance management
- Project and task management with real-time updates
- Document analysis and AI-powered processing
- Team coordination and communication
- Advanced reporting and analytics
- System administration and automation

CURRENT CONTEXT:
${userInfo}${currentProjects}${pendingTasks}${recentActivity}
Current Time: ${context.systemInfo?.currentTime || new Date().toISOString()}

RESPONSE GUIDELINES:
- Provide contextual, personalized responses based on the user's role and current work
- Reference specific projects, tasks, or activities when relevant
- Offer actionable suggestions and next steps
- Be professional yet conversational
- If you need more information to help effectively, ask specific questions
- Always consider the user's role and permissions when suggesting actions

Remember: You have access to real-time data about the user's work, so use this context to provide highly relevant and personalized assistance.`;
}

async function generateAIResponse(message: string, context: any = {}) {
  // Enhanced context building
  const enhancedContext = await buildEnhancedContext(context);

  // Check if LangChain assistant is available
  const useLangChain = Deno.env.get('USE_LANGCHAIN') === 'true' && openAIKey && openAIKey !== '' && openAIKey !== 'your_openai_api_key_here';

  if (useLangChain) {
    try {
      // Forward to LangChain assistant with enhanced context
      const langchainResponse = await fetch(`${supabaseUrl}/functions/v1/langchain-ai-assistant`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${supabaseServiceKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          userId: context.userId,
          context: {
            ...enhancedContext,
            interface: 'standard'
          },
          options: {
            useMemory: true,
            temperature: 0.7
          }
        })
      });

      if (langchainResponse.ok) {
        const langchainData = await langchainResponse.json();
        return langchainData.response;
      } else {
        console.warn('LangChain assistant returned error, falling back to basic response');
      }
    } catch (error) {
      console.warn('LangChain assistant unavailable, falling back to basic response:', error);
    }
  }

  if (!openAIKey || openAIKey === '' || openAIKey === 'your_openai_api_key_here') {
    // Return intelligent mock responses based on message content
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('help') || lowerMessage.includes('assist')) {
      return `I'm your AI assistant for CTNL AI WORK-BOARD! I can help you with:

🔹 **Time Management**: Clock in/out, view attendance records
🔹 **Project Management**: Create tasks, track progress, manage deadlines
🔹 **Document Analysis**: Upload and analyze documents with AI
🔹 **Team Coordination**: Check team status, send notifications
🔹 **Reports & Analytics**: Generate insights and performance reports
🔹 **System Commands**: Execute administrative tasks

What would you like help with today?`;
    }
    
    if (lowerMessage.includes('status') || lowerMessage.includes('system')) {
      return `🟢 **CTNL AI WORK-BOARD System Status**

**Core Services**: ✅ All systems operational
**AI Analysis**: ✅ Document processing active
**Database**: ✅ Real-time sync enabled
**Notifications**: ✅ Email system functional
**User Role**: ${context.role || 'User'}
**Department**: ${context.department || 'General'}

All systems are running smoothly! How can I assist you?`;
    }
    
    if (lowerMessage.includes('time') || lowerMessage.includes('clock')) {
      return `⏰ **Time Tracking Assistant**

I can help you with:
- Clock in/out for your shift
- View your attendance history
- Check team attendance status
- Generate time reports
- Set reminders for breaks

Would you like me to help you clock in/out or check your time records?`;
    }
    
    if (lowerMessage.includes('project') || lowerMessage.includes('task')) {
      return `📋 **Project Management Assistant**

I can help you:
- Create new projects and tasks
- Update task status and progress
- Assign team members to projects
- Set deadlines and priorities
- Generate project reports

What project management task would you like assistance with?`;
    }
    
    if (lowerMessage.includes('analyze') || lowerMessage.includes('document')) {
      return `📄 **Document Analysis Assistant**

I can help you:
- Upload and analyze documents
- Extract key information from files
- Summarize document content
- Identify important patterns
- Generate insights and recommendations

Upload a document or paste text content for analysis!`;
    }
    
    // Default intelligent response
    return `I understand you're asking about "${message}". As your AI assistant for CTNL AI WORK-BOARD, I'm here to help with:

✨ **Quick Actions**:
- Time tracking and attendance
- Project and task management
- Document analysis and insights
- Team coordination
- System administration

Could you provide more details about what you'd like to accomplish? I'm ready to assist!`;
  }

  try {
    // Build enhanced system prompt with all available context
    const systemPrompt = buildSystemPrompt(enhancedContext);

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: message
          }
        ],
        max_tokens: 800,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const result = await response.json();
    return result.choices[0]?.message?.content || 'I apologize, but I could not generate a response at this time.';
  } catch (error) {
    console.error('OpenAI API error:', error);
    return `I'm experiencing some technical difficulties with my AI processing. However, I can still help you with basic tasks in CTNL AI WORK-BOARD. What would you like assistance with?`;
  }
}

const handler = async (req: Request): Promise<Response> => {
  try {
    const body = await req.json();

    // Handle health check requests
    if (body.healthCheck) {
      const openAIConfigured = openAIKey && openAIKey !== '' && openAIKey !== 'your_openai_api_key_here';
      const supabaseConfigured = supabaseUrl && supabaseServiceKey;

      return createCorsResponse({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: {
          openAI: openAIConfigured ? 'configured' : 'missing',
          supabase: supabaseConfigured ? 'configured' : 'missing'
        }
      });
    }

    const { message, userId, context, conversation_history } = body;

    if (!message) {
      return createCorsResponse(
        { error: 'Missing required field: message' },
        400
      );
    }

    // Generate AI response
    const response = await generateAIResponse(message, context);

    // Log the conversation if userId is provided
    if (userId) {
      try {
        await supabase
          .from('activity_logs')
          .insert({
            user_id: userId,
            action: 'ai_assistant_query',
            entity_type: 'ai_conversation',
            entity_id: `conversation_${Date.now()}`,
            metadata: {
              message: message.substring(0, 200),
              response: response.substring(0, 200),
              context: context
            }
          });
      } catch (logError) {
        console.error('Failed to log conversation:', logError);
        // Continue with response even if logging fails
      }
    }

    return createCorsResponse({
      success: true,
      response,
      timestamp: new Date().toISOString(),
      context
    });

  } catch (error) {
    console.error('AI Assistant error:', error);
    
    return createCorsResponse(
      { 
        error: 'AI Assistant failed', 
        details: error.message,
        success: false
      },
      500
    );
  }
};

Deno.serve(withCors(handler));
