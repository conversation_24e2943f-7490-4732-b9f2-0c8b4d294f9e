-- ============================================================================
-- CLEAN RLS FIX - NO SYNTAX ERRORS
-- This script fixes infinite recursion in RLS policies
-- Copy and paste this ENTIRE script into Supabase SQL Editor
-- ============================================================================

-- STEP 1: Fix profiles table (main issue)
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies on profiles
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;

-- Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create safe policies for profiles
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_service_role" ON public.profiles
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- STEP 2: Fix time_logs table (if it exists)
-- Check if time_logs table exists and fix it
CREATE OR REPLACE FUNCTION fix_time_logs_rls()
RETURNS TEXT AS $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'time_logs' AND table_schema = 'public') THEN
        -- Disable RLS
        EXECUTE 'ALTER TABLE public.time_logs DISABLE ROW LEVEL SECURITY';
        
        -- Drop existing policies
        EXECUTE 'DROP POLICY IF EXISTS "Users can view own time logs" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "Users can insert own time logs" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "Users can update own time logs" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "Enable read access for users based on user_id" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "time_logs_select_own" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "time_logs_insert_own" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "time_logs_update_own" ON public.time_logs';
        EXECUTE 'DROP POLICY IF EXISTS "time_logs_service_role" ON public.time_logs';
        
        -- Re-enable RLS
        EXECUTE 'ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY';
        
        -- Create safe policies
        EXECUTE 'CREATE POLICY "time_logs_select_own" ON public.time_logs FOR SELECT USING (user_id = auth.uid())';
        EXECUTE 'CREATE POLICY "time_logs_insert_own" ON public.time_logs FOR INSERT WITH CHECK (user_id = auth.uid())';
        EXECUTE 'CREATE POLICY "time_logs_update_own" ON public.time_logs FOR UPDATE USING (user_id = auth.uid()) WITH CHECK (user_id = auth.uid())';
        EXECUTE 'CREATE POLICY "time_logs_service_role" ON public.time_logs FOR ALL USING (current_setting(''role'') = ''service_role'') WITH CHECK (current_setting(''role'') = ''service_role'')';
        
        RETURN 'time_logs table RLS policies fixed';
    ELSE
        RETURN 'time_logs table does not exist - skipped';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Execute the time_logs fix
SELECT fix_time_logs_rls();

-- Drop the temporary function
DROP FUNCTION fix_time_logs_rls();

-- STEP 3: Create departments table if missing
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on departments
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;

-- Drop existing department policies
DROP POLICY IF EXISTS "departments_select_all" ON public.departments;
DROP POLICY IF EXISTS "departments_admin_all" ON public.departments;
DROP POLICY IF EXISTS "departments_service_role" ON public.departments;

-- Create safe department policies
CREATE POLICY "departments_select_all" ON public.departments
    FOR SELECT
    USING (true);

CREATE POLICY "departments_service_role" ON public.departments
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- Insert sample departments
INSERT INTO public.departments (name, description) VALUES
    ('IT Department', 'Information Technology and Systems'),
    ('HR Department', 'Human Resources and Administration'),
    ('Finance Department', 'Finance and Accounting'),
    ('Operations', 'Field Operations and Maintenance'),
    ('Engineering', 'Technical Engineering and Design')
ON CONFLICT (name) DO NOTHING;

-- STEP 4: Fix memos table (if it exists)
CREATE OR REPLACE FUNCTION fix_memos_rls()
RETURNS TEXT AS $$
BEGIN
    -- Check if table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'memos' AND table_schema = 'public') THEN
        -- Drop existing policies
        EXECUTE 'DROP POLICY IF EXISTS "Users can view published memos" ON public.memos';
        EXECUTE 'DROP POLICY IF EXISTS "Users can view memos" ON public.memos';
        EXECUTE 'DROP POLICY IF EXISTS "Users can create memos" ON public.memos';
        EXECUTE 'DROP POLICY IF EXISTS "memos_select_own_or_published" ON public.memos';
        EXECUTE 'DROP POLICY IF EXISTS "memos_insert_authenticated" ON public.memos';
        EXECUTE 'DROP POLICY IF EXISTS "memos_update_own" ON public.memos';
        
        -- Create safe policies
        EXECUTE 'CREATE POLICY "memos_select_own_or_published" ON public.memos FOR SELECT USING (created_by = auth.uid() OR status = ''published'' OR current_setting(''role'') = ''service_role'')';
        EXECUTE 'CREATE POLICY "memos_insert_authenticated" ON public.memos FOR INSERT WITH CHECK (auth.uid() = created_by)';
        EXECUTE 'CREATE POLICY "memos_update_own" ON public.memos FOR UPDATE USING (created_by = auth.uid()) WITH CHECK (created_by = auth.uid())';
        
        RETURN 'memos table RLS policies fixed';
    ELSE
        RETURN 'memos table does not exist - skipped';
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Execute the memos fix
SELECT fix_memos_rls();

-- Drop the temporary function
DROP FUNCTION fix_memos_rls();

-- STEP 5: Verification query
SELECT 
    'RLS FIX COMPLETED SUCCESSFULLY' as status,
    'Profiles: FIXED' as profiles_status,
    'Time_logs: ' || CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'time_logs') 
        THEN 'FIXED' 
        ELSE 'NOT FOUND' 
    END as time_logs_status,
    'Departments: READY' as departments_status,
    'Memos: ' || CASE 
        WHEN EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'memos') 
        THEN 'FIXED' 
        ELSE 'NOT FOUND' 
    END as memos_status,
    COUNT(*) as department_count
FROM public.departments;

-- Final message
SELECT 'REFRESH YOUR APPLICATION AND TRY AUTHENTICATION AGAIN' as next_step;
