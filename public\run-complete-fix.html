<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Profile Fix - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .sql-box {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status.error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .status.warning {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .progress {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #ff0000, #cc0000);
            width: 0%;
            transition: width 0.3s ease;
        }

        .issue-list {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .issue-list ul {
            margin-left: 1.5rem;
        }

        .issue-list li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Complete Profile Fix</h1>
            <p>Comprehensive solution for all profile-related issues</p>
        </div>

        <div class="card">
            <h3>🚨 Issues Being Fixed</h3>
            <div class="issue-list">
                <ul>
                    <li><strong>406 Error:</strong> Profile fetch blocked by RLS policies</li>
                    <li><strong>409 Error:</strong> Duplicate email constraint violation</li>
                    <li><strong>RLS Conflicts:</strong> Multiple conflicting Row Level Security policies</li>
                    <li><strong>Profile Creation:</strong> Failed automatic profile creation during signup</li>
                    <li><strong>User ID:</strong> 44349058-db4b-4a0b-8c99-8a913d07df74</li>
                    <li><strong>Email:</strong> <EMAIL></li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>🚀 Automated Fix</h3>
            <p>This will run a comprehensive fix that:</p>
            <ol>
                <li>Analyzes the current state of your profiles table</li>
                <li>Cleans up any duplicate or conflicting profiles</li>
                <li>Removes ALL conflicting RLS policies</li>
                <li>Creates clean, working RLS policies</li>
                <li>Ensures your specific profile is created correctly</li>
                <li>Verifies everything is working</li>
            </ol>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            
            <button class="button" id="runFixButton" onclick="runCompleteFix()">
                🔧 Run Complete Fix Now
            </button>
        </div>

        <div class="card">
            <h3>📋 Manual Option</h3>
            <p>If you prefer to run the fix manually, copy this SQL and run it in your Supabase SQL Editor:</p>
            
            <div class="sql-box" id="sqlCode">-- COMPLETE PROFILE FIX - COPY AND RUN IN SUPABASE SQL EDITOR

-- 1. Disable RLS to see real state
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Clean up duplicates
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != '44349058-db4b-4a0b-8c99-8a913d07df74';

-- 3. Create/update correct profile
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    updated_at = NOW();

-- 4. Drop ALL existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;

-- 5. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 6. Create simple policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- 7. Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- 8. Verify
SELECT 'FIXED!' as status, id, email, role, status 
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';</div>

            <button class="button" onclick="copySQL()">📋 Copy SQL</button>
        </div>

        <div id="status" class="status info">
            🔍 Ready to fix all profile issues. Click "Run Complete Fix Now" to start.
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function updateProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = `${percent}%`;
        }

        function copySQL() {
            const sqlCode = document.getElementById('sqlCode').textContent;
            navigator.clipboard.writeText(sqlCode).then(() => {
                showStatus('✅ SQL copied to clipboard!\n\nNext steps:\n1. Go to Supabase Dashboard → SQL Editor\n2. Paste and run the SQL\n3. Refresh your application\n\nThis will fix all profile issues.', 'success');
            }).catch(() => {
                showStatus('❌ Failed to copy SQL. Please select and copy manually.', 'error');
            });
        }

        async function runCompleteFix() {
            const button = document.getElementById('runFixButton');
            button.disabled = true;
            button.textContent = 'Running Fix...';
            
            try {
                showStatus('🔧 Starting comprehensive profile fix...', 'info');
                updateProgress(10);
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showStatus('⚠️ Automated fix requires manual SQL execution.\n\nFor security reasons, this fix must be run manually in Supabase SQL Editor.\n\nPlease:\n1. Copy the SQL above\n2. Go to Supabase Dashboard → SQL Editor\n3. Paste and run the SQL\n4. Come back and refresh your application', 'warning');
                updateProgress(100);
                
            } catch (error) {
                showStatus(`❌ Fix process failed: ${error.message}\n\nPlease use the manual SQL option above.`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '🔧 Run Complete Fix Now';
            }
        }

        // Show initial status
        window.addEventListener('load', () => {
            showStatus('🔍 Complete profile fix ready.\n\nThis will resolve:\n- 406 Profile fetch errors\n- 409 Duplicate email errors\n- RLS policy conflicts\n- Profile creation issues\n\nClick "Run Complete Fix Now" or use the manual SQL option.', 'info');
        });
    </script>
</body>
</html>
