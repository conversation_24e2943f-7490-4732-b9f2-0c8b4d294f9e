
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AlertTriangle, Battery, CheckCircle, Plus, TrendingUp, Zap } from "lucide-react";
import { useState } from "react";

export const BatterySystemManagement = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newBattery, setNewBattery] = useState({
    manufacturer: "",
    model_name: "",
    voltage: "",
    capacity_kwh: "",
    condition: "new",
    status: "available",
    location: "",
    serial_number: "",
    purchase_date: "",
    purchase_price: ""
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: batteryInventory, isLoading: inventoryLoading } = useQuery({
    queryKey: ['battery-inventory'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('battery_inventory')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
  });

  const { data: batteryReports, isLoading: reportsLoading } = useQuery({
    queryKey: ['battery-reports'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('battery_reports')
        .select(`
          id,
          battery_id,
          report_date,
          charge_level,
          voltage_reading,
          temperature,
          status,
          maintenance_notes,
          issues_found,
          created_at,
          battery_inventory!inner(
            battery_id,
            battery_model,
            manufacturer,
            location
          )
        `)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data;
    },
  });

  const { data: batterySales } = useQuery({
    queryKey: ['battery-sales'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('battery_sales')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data;
    },
  });

  const addBatteryMutation = useMutation({
    mutationFn: async (batteryData: typeof newBattery) => {
      const { data, error } = await supabase
        .from('battery_inventory')
        .insert([{
          manufacturer: batteryData.manufacturer,
          battery_model: batteryData.model_name,
          voltage: parseFloat(batteryData.voltage),
          capacity_kwh: parseFloat(batteryData.capacity_kwh),
          status: batteryData.status === 'available' ? 'operational' : batteryData.status,
          location: batteryData.location,
          battery_id: batteryData.serial_number,
          installation_date: batteryData.purchase_date || new Date().toISOString().split('T')[0],
          purchase_cost: parseFloat(batteryData.purchase_price)
        }])
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['battery-inventory'] });
      toast({
        title: "Success",
        description: "Battery added successfully",
      });
      setIsAddDialogOpen(false);
      setNewBattery({
        manufacturer: "",
        model_name: "",
        voltage: "",
        capacity_kwh: "",
        condition: "new",
        status: "available",
        location: "",
        serial_number: "",
        purchase_date: "",
        purchase_price: ""
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to add battery: " + error.message,
        variant: "destructive",
      });
    },
  });

  const handleAddBattery = (e: React.FormEvent) => {
    e.preventDefault();
    addBatteryMutation.mutate(newBattery);
  };

  const handleViewBattery = (battery: any) => {
    toast({
      title: "Battery Details",
      description: `Viewing details for ${battery.manufacturer} ${battery.battery_model} (ID: ${battery.battery_id})`,
    });
    // TODO: Open detailed view modal or navigate to battery detail page
  };

  const handleRunTest = async (battery: any) => {
    try {
      toast({
        title: "Running Battery Test",
        description: `Initiating diagnostic test for battery ${battery.battery_id}`,
      });

      // Create a test report entry
      const { error } = await supabase
        .from('battery_reports')
        .insert({
          battery_id: battery.id,
          charge_level: Math.floor(Math.random() * 100), // Simulated test result
          voltage_reading: battery.voltage + (Math.random() - 0.5) * 2, // Simulated reading
          temperature: 25 + Math.random() * 10, // Simulated temperature
          status: 'operational',
          maintenance_notes: 'Automated diagnostic test completed',
          reported_by: (await supabase.auth.getUser()).data.user?.id
        });

      if (error) throw error;

      toast({
        title: "Test Complete",
        description: `Diagnostic test completed for battery ${battery.battery_id}`,
      });

      // Refresh reports
      queryClient.invalidateQueries({ queryKey: ['battery-reports'] });
    } catch (error: any) {
      toast({
        title: "Test Failed",
        description: error.message || "Failed to run battery test",
        variant: "destructive",
      });
    }
  };

  const getHealthStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'excellent':
        return 'bg-green-500/20 text-green-500';
      case 'good':
        return 'bg-blue-500/20 text-blue-500';
      case 'fair':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'poor':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'available':
        return 'bg-green-500/20 text-green-500';
      case 'deployed':
        return 'bg-blue-500/20 text-blue-500';
      case 'maintenance':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'retired':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const filteredBatteries = batteryInventory?.filter(battery =>
    battery.manufacturer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    battery.model_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Battery System Management</h2>
          <p className="text-muted-foreground">Monitor battery inventory, health, and performance</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Battery
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Battery</DialogTitle>
              <DialogDescription>
                Add a new battery to the inventory system with detailed specifications and tracking information.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleAddBattery} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="manufacturer">Manufacturer</Label>
                  <Input
                    id="manufacturer"
                    value={newBattery.manufacturer}
                    onChange={(e) => setNewBattery({...newBattery, manufacturer: e.target.value})}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="model_name">Model Name</Label>
                  <Input
                    id="model_name"
                    value={newBattery.model_name}
                    onChange={(e) => setNewBattery({...newBattery, model_name: e.target.value})}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="voltage">Voltage (V)</Label>
                  <Input
                    id="voltage"
                    type="number"
                    step="0.1"
                    value={newBattery.voltage}
                    onChange={(e) => setNewBattery({...newBattery, voltage: e.target.value})}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capacity_kwh">Capacity (kWh)</Label>
                  <Input
                    id="capacity_kwh"
                    type="number"
                    step="0.1"
                    value={newBattery.capacity_kwh}
                    onChange={(e) => setNewBattery({...newBattery, capacity_kwh: e.target.value})}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="condition">Condition</Label>
                  <Select value={newBattery.condition} onValueChange={(value) => setNewBattery({...newBattery, condition: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">New</SelectItem>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="fair">Fair</SelectItem>
                      <SelectItem value="poor">Poor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select value={newBattery.status} onValueChange={(value) => setNewBattery({...newBattery, status: value})}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="available">Available</SelectItem>
                      <SelectItem value="deployed">Deployed</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="sold">Sold</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="serial_number">Serial Number</Label>
                  <Input
                    id="serial_number"
                    value={newBattery.serial_number}
                    onChange={(e) => setNewBattery({...newBattery, serial_number: e.target.value})}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={newBattery.location}
                    onChange={(e) => setNewBattery({...newBattery, location: e.target.value})}
                    placeholder="e.g., Warehouse A"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="purchase_date">Purchase Date</Label>
                  <Input
                    id="purchase_date"
                    type="date"
                    value={newBattery.purchase_date}
                    onChange={(e) => setNewBattery({...newBattery, purchase_date: e.target.value})}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="purchase_price">Purchase Price (₦)</Label>
                  <Input
                    id="purchase_price"
                    type="number"
                    step="0.01"
                    value={newBattery.purchase_price}
                    onChange={(e) => setNewBattery({...newBattery, purchase_price: e.target.value})}
                  />
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={addBatteryMutation.isPending}>
                  {addBatteryMutation.isPending ? "Adding..." : "Add Battery"}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Battery className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Batteries</p>
              <p className="text-2xl font-bold">{batteryInventory?.length || 0}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Available</p>
              <p className="text-2xl font-bold">
                {batteryInventory?.filter(b => b.status === 'available').length || 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Zap className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Deployed</p>
              <p className="text-2xl font-bold">
                {batteryInventory?.filter(b => b.status === 'deployed').length || 0}
              </p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <AlertTriangle className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Maintenance</p>
              <p className="text-2xl font-bold">
                {batteryReports?.filter(r => r.maintenance_required).length || 0}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="inventory" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="reports">Health Reports</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Battery Inventory</CardTitle>
              <Input
                placeholder="Search batteries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {inventoryLoading ? (
                  <div className="col-span-2 text-center py-8">Loading battery inventory...</div>
                ) : filteredBatteries?.length === 0 ? (
                  <div className="col-span-2 text-center py-8 text-muted-foreground">
                    No batteries found
                  </div>
                ) : (
                  filteredBatteries?.map((battery) => (
                    <Card key={battery.id} className="p-4">
                      <div className="space-y-3">
                        <div className="flex justify-between items-start">
                          <h3 className="font-semibold flex items-center gap-2">
                            <Battery className="h-4 w-4" />
                            {battery.manufacturer} {battery.battery_model}
                          </h3>
                          <Badge className={getStatusColor(battery.status)}>
                            {battery.status}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <p className="text-muted-foreground">Voltage</p>
                            <p className="font-medium">{battery.voltage}V</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Capacity</p>
                            <p className="font-medium">{battery.capacity_kwh} kWh</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Battery ID</p>
                            <p className="font-medium">{battery.battery_id}</p>
                          </div>
                          <div>
                            <p className="text-muted-foreground">Location</p>
                            <p className="font-medium">{battery.location || 'Warehouse'}</p>
                          </div>
                        </div>
                        <div className="flex gap-2 mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => handleViewBattery(battery)}
                          >
                            View Details
                          </Button>
                          <Button
                            size="sm"
                            className="flex-1"
                            onClick={() => handleRunTest(battery)}
                          >
                            Run Test
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Battery Health Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportsLoading ? (
                  <div className="text-center py-8">Loading battery reports...</div>
                ) : batteryReports?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No battery reports found
                  </div>
                ) : (
                  batteryReports?.map((report) => (
                    <Card key={report.id} className="p-4">
                      <div className="flex justify-between items-start">
                        <div className="space-y-2">
                          <h3 className="font-semibold">{report.site_name}</h3>
                          <p className="text-sm text-muted-foreground">
                            Serial: {report.battery_serial_number}
                          </p>
                          <div className="flex gap-2">
                            <Badge className={getHealthStatusColor(report.health_status)}>
                              {report.health_status}
                            </Badge>
                            {report.maintenance_required && (
                              <Badge variant="destructive">Maintenance Required</Badge>
                            )}
                          </div>
                          <div className="grid grid-cols-3 gap-4 text-sm">
                            <div>
                              <p className="text-muted-foreground">Capacity</p>
                              <p className="font-medium">{report.current_capacity}%</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Voltage</p>
                              <p className="font-medium">{report.battery_voltage}V</p>
                            </div>
                            <div>
                              <p className="text-muted-foreground">Temperature</p>
                              <p className="font-medium">{report.temperature}°C</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-muted-foreground">
                            {new Date(report.report_date).toLocaleDateString()}
                          </p>
                          <Button variant="outline" size="sm" className="mt-2">
                            View Report
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Battery Sales</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {batterySales?.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No battery sales recorded
                  </div>
                ) : (
                  batterySales?.map((sale) => (
                    <Card key={sale.id} className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold">Client: {sale.client_id}</h3>
                          <p className="text-sm text-muted-foreground">
                            Sale Date: {new Date(sale.sale_date).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold">₦{sale.sale_price.toLocaleString()}</p>
                        </div>
                      </div>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Average Health Score</span>
                    <span className="font-bold">85%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Batteries Needing Maintenance</span>
                    <span className="font-bold text-orange-600">
                      {batteryReports?.filter(r => r.maintenance_required).length || 0}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Sales This Month</span>
                    <span className="font-bold text-green-600">
                      ₦{batterySales?.reduce((sum, sale) => sum + sale.sale_price, 0).toLocaleString() || '0'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Maintenance scheduling and alerts will be displayed here.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};
