// Battery Management Types
export interface BatteryType {
  id: string;
  name: string;
  description?: string;
  voltage: number;
  capacity_ah: number;
  chemistry: string;
  manufacturer?: string;
  model?: string;
  specifications?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface BatteryLocation {
  id: string;
  name: string;
  description?: string;
  location_type: string;
  address?: string;
  coordinates?: { x: number; y: number };
  parent_location_id?: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

export interface Battery {
  id: string;
  serial_number: string;
  battery_type_id: string;
  battery_type?: BatteryType;
  current_location_id?: string;
  current_location?: BatteryLocation;
  status: BatteryStatus;
  condition: BatteryCondition;
  purchase_date?: string;
  installation_date?: string;
  warranty_expiry_date?: string;
  last_maintenance_date?: string;
  next_maintenance_date?: string;
  purchase_cost?: number;
  supplier?: string;
  notes?: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  profile_id?: string;
}

export interface BatteryReading {
  id: string;
  battery_id: string;
  battery?: Battery;
  reading_date: string;
  voltage?: number;
  current_amperage?: number;
  temperature?: number;
  state_of_charge?: number;
  capacity_remaining?: number;
  internal_resistance?: number;
  reading_type: ReadingType;
  technician_notes?: string;
  created_at: string;
  created_by?: string;
  generated_by?: string;
  profile_id?: string;
}

export interface BatteryMaintenance {
  id: string;
  battery_id: string;
  battery?: Battery;
  maintenance_type: string;
  maintenance_date: string;
  technician_id?: string;
  technician?: any;
  description: string;
  parts_used?: string;
  cost?: number;
  next_maintenance_date?: string;
  status: MaintenanceStatus;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
  profile_id?: string;
}

export interface BatteryTransfer {
  id: string;
  battery_id: string;
  battery?: Battery;
  from_location_id?: string;
  from_location?: BatteryLocation;
  to_location_id: string;
  to_location?: BatteryLocation;
  transfer_date: string;
  transferred_by?: string;
  received_by?: string;
  reason?: string;
  notes?: string;
  status: TransferStatus;
  created_at: string;
  created_by?: string;
  profile_id?: string;
}

// Enums
export type BatteryStatus = 'new' | 'active' | 'maintenance' | 'retired' | 'disposed';
export type BatteryCondition = 'excellent' | 'good' | 'fair' | 'poor' | 'failed';
export type ReadingType = 'manual' | 'automatic' | 'scheduled';
export type MaintenanceStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
export type TransferStatus = 'pending' | 'in_transit' | 'completed' | 'cancelled';

// Form types
export interface CreateBatteryForm {
  serial_number: string;
  battery_type_id: string;
  current_location_id?: string;
  status?: BatteryStatus;
  condition?: BatteryCondition;
  purchase_date?: string;
  installation_date?: string;
  warranty_expiry_date?: string;
  purchase_cost?: number;
  supplier?: string;
  notes?: string;
}

export interface CreateBatteryReadingForm {
  battery_id: string;
  voltage?: number;
  current_amperage?: number;
  temperature?: number;
  state_of_charge?: number;
  capacity_remaining?: number;
  internal_resistance?: number;
  reading_type?: ReadingType;
  technician_notes?: string;
}

export interface CreateMaintenanceForm {
  battery_id: string;
  maintenance_type: string;
  maintenance_date: string;
  technician_id?: string;
  description: string;
  parts_used?: string;
  cost?: number;
  next_maintenance_date?: string;
}

export interface CreateTransferForm {
  battery_id: string;
  from_location_id?: string;
  to_location_id: string;
  transferred_by?: string;
  received_by?: string;
  reason?: string;
  notes?: string;
}

// API Response types
export interface BatteryStats {
  total_batteries: number;
  active_batteries: number;
  maintenance_batteries: number;
  retired_batteries: number;
  average_age_months: number;
  batteries_needing_maintenance: number;
  total_capacity_ah: number;
  average_state_of_charge: number;
}

export interface BatteryDashboardData {
  stats: BatteryStats;
  recent_readings: BatteryReading[];
  upcoming_maintenance: BatteryMaintenance[];
  recent_transfers: BatteryTransfer[];
  battery_health_distribution: Array<{
    condition: BatteryCondition;
    count: number;
  }>;
  location_distribution: Array<{
    location: string;
    count: number;
  }>;
}

// Filter and search types
export interface BatteryFilters {
  status?: BatteryStatus[];
  condition?: BatteryCondition[];
  battery_type_id?: string[];
  location_id?: string[];
  search?: string;
  date_range?: {
    start: string;
    end: string;
  };
}

export interface BatterySearchParams extends BatteryFilters {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}
