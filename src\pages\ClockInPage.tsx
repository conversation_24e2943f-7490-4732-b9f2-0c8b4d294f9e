import { ThemeSwitcher } from '@/components/ThemeSwitcher'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { SmallPin3D } from '@/components/ui/SmallPin3D'
import { VoiceNavigationButton } from '@/components/voice/VoiceNavigationButton'
import AOS from 'aos'
import { BookOpen, Calendar, Clock, ExternalLink } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'

const ClockInPage = () => {
  const { user: userProfile, loading } = useSupabaseAuth()
  const isAuthenticated = !!userProfile
  const navigate = useNavigate()
  const [currentTime, setCurrentTime] = useState(new Date())
  const [isAnimating, setIsAnimating] = useState(false)

  // Debug auth state
  useEffect(() => {
    console.log('🏠 ClockInPage: Auth state:', {
      isAuthenticated,
      userProfile: userProfile ? { id: userProfile.id, role: userProfile.role } : null,
      loading
    })
  }, [isAuthenticated, userProfile, loading])

  useEffect(() => {
    AOS.init({
      duration: 1200,
      easing: 'ease-out-cubic',
      once: false,
      mirror: true
    })

    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const handleClockIn = () => {
    console.log('🏠 ClockInPage: Clock-in button clicked!')
    setIsAnimating(true)
    setTimeout(() => {
      console.log('🏠 ClockInPage: Redirecting to auth page...')
      // Always redirect to auth page for login first
      navigate('/auth')
    }, 1500)
  }

  return (
    <div className='dashboard-container relative overflow-hidden'>
      {/* Video Background */}
      <div className='absolute inset-0 overflow-hidden'>
        <video
          autoPlay
          muted
          loop
          playsInline
          className='absolute inset-0 w-full h-full object-cover'
          style={{ filter: 'brightness(0.4) contrast(1.2)' }}
        >
          <source src='/videos/herobg3.mp4' type='video/mp4' />
          Your browser does not support the video tag.
        </video>

        {/* Video Overlay for better text readability */}
        <div className='absolute inset-0 bg-black/40' />

        {/* Additional Animated Elements on top of video */}
        <div className='absolute inset-0 overflow-hidden'>
          {/* Gradient Orbs */}
          <div className='absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-[#ff1c04]/20 to-[#000000]/10 rounded-full blur-3xl animate-pulse floating-animation' data-aos='fade-in' data-aos-delay='500' />
          <div className='absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-[#000000]/20 to-[#ff1c04]/10 rounded-full blur-3xl animate-pulse floating-animation' data-aos='fade-in' data-aos-delay='700' style={{ animationDelay: '1s' }} />

          {/* Geometric Patterns */}
          <div className='absolute top-20 left-10 w-32 h-32 border border-[#ff1c04]/30 rounded-full animate-spin' style={{ animationDuration: '20s' }} />
          <div className='absolute bottom-20 right-10 w-24 h-24 border border-white/20 rounded-full animate-spin' style={{ animationDuration: '15s', animationDirection: 'reverse' }} />
        </div>
      </div>

      {/* User Manual Link - Top Left */}
      <div className='fixed top-6 left-6 z-50' data-aos='fade-right'>
        <Button
          variant='ghost'
          size='sm'
          className='text-white hover:text-[#ff1c04] hover:bg-white/10 transition-all duration-300 backdrop-blur-md border border-white/30 bg-black/20'
          onClick={() => window.open('/comprehensive-manual.html', '_blank')}
        >
          <BookOpen className='h-4 w-4 mr-2' />
          Comprehensive Manual
          <ExternalLink className='h-3 w-3 ml-2' />
        </Button>
      </div>

      {/* Theme Switcher */}
      <div className='fixed top-6 right-6 z-50' data-aos='fade-left'>
        <ThemeSwitcher />
      </div>

      {/* AI Voice Assistant */}
      <VoiceNavigationButton
        position='bottom-right'
        size='lg'
        showTooltip
      />

      {/* Main Content */}
      <div className='relative z-10 min-h-screen flex flex-col items-center justify-center px-2 py-4'>
        {/* Logo Section - Reduced */}
        <div className='mb-4 sm:mb-6' data-aos='zoom-in' data-aos-duration='1000'>
          <div className='relative'>
            <img
              src='/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png'
              alt='CTNL Logo'
              className='h-12 sm:h-16 w-auto mx-auto drop-shadow-2xl'
            />
            <div className='absolute inset-0 bg-gradient-to-r from-[#ff1c04]/20 to-[#000000]/20 blur-xl -z-10 animate-pulse' />
          </div>
        </div>

        {/* Main Clock-In Interface - Responsive */}
        <div className='w-full max-w-xs sm:max-w-sm md:max-w-md'>
          <Card className='clockin-card glass-card shadow-2xl backdrop-blur-lg bg-black/30 border border-white/20' data-aos='fade-up' data-aos-delay='200'>
            <CardContent className='p-4 sm:p-6 md:p-8 text-center text-white'>
              {/* 3D Pin Location Indicator - Smaller */}
              <div className='absolute top-2 right-2 z-10' data-aos='zoom-in' data-aos-delay='300'>
                <SmallPin3D
                  location='Current Location'
                  size='sm'
                  variant='success'
                />
              </div>

              {/* 3D Circle Animation with Layers - Responsive */}
              <div className='relative mb-4 sm:mb-6 flex items-center justify-center'>
                <div className={`relative w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 ${isAnimating ? 'animate-spin' : ''}`}>
                  {/* Outer Ring */}
                  <div className='absolute inset-0 rounded-full bg-gradient-to-r from-[#ff1c04]/30 via-[#000000]/20 to-[#ff1c04]/30 animate-pulse blur-sm' />

                  {/* Middle Ring */}
                  <div className='absolute inset-1 sm:inset-2 rounded-full bg-gradient-to-r from-[#000000]/40 via-[#ff1c04]/30 to-[#000000]/40 animate-pulse' style={{ animationDelay: '0.5s' }} />

                  {/* Inner Circle - Main Button - Responsive */}
                  <div
                    className='absolute inset-4 sm:inset-6 rounded-full bg-gradient-to-br from-[#ff1c04] via-[#e01703] to-[#cc1502] shadow-xl hover:shadow-2xl cursor-pointer flex items-center justify-center transform hover:scale-105 transition-all duration-500 pulse-glow'
                    onClick={handleClockIn}
                  >
                    <div className='text-center text-white relative z-10'>
                      <Clock className='h-6 w-6 sm:h-8 sm:w-8 mx-auto mb-1 sm:mb-2 drop-shadow-lg' />
                      <span className='text-xs sm:text-sm md:text-base font-bold tracking-wide'>CLOCK IN</span>
                    </div>

                    {/* Inner Glow Effect */}
                    <div className='absolute inset-1 rounded-full bg-gradient-to-br from-white/20 to-transparent' />
                  </div>

                  {/* Rotating Border - Thinner */}
                  <div className='absolute inset-0 rounded-full border border-dashed border-[#ff1c04]/60 animate-spin' style={{ animationDuration: '12s' }} />
                  <div className='absolute inset-2 sm:inset-3 rounded-full border border-dashed border-[#000000]/40 animate-spin' style={{ animationDuration: '8s', animationDirection: 'reverse' }} />
                </div>
              </div>

              {/* Enhanced Time Display - Compact */}
              <div className='mb-4 sm:mb-6 p-3 sm:p-4 glassmorphism rounded-xl' data-aos='fade-up' data-aos-delay='400'>
                <div className='text-xl sm:text-2xl md:text-3xl font-bold modern-heading mb-1'>
                  {currentTime.toLocaleTimeString()}
                </div>
                <div className='text-muted-foreground text-xs sm:text-sm font-medium'>
                  {currentTime.toLocaleDateString('en-US', {
                    weekday: 'short',
                    month: 'short',
                    day: 'numeric'
                  })}
                </div>
              </div>

              {/* Enhanced Quick Stats - Compact */}
              <div className='grid grid-cols-2 gap-2 sm:gap-3 mb-4 sm:mb-6' data-aos='fade-up' data-aos-delay='600'>
                <div className='stats-card text-center p-2 sm:p-3'>
                  <Calendar className='h-4 w-4 sm:h-5 sm:w-5 mx-auto mb-1 text-[#ff1c04]' />
                  <div className='text-xs sm:text-sm font-semibold'>Today</div>
                  <div className='text-xs text-muted-foreground hidden sm:block'>Ready to start</div>
                </div>
                <div className='stats-card text-center p-2 sm:p-3'>
                  <div className='flex justify-center mb-1'>
                    <SmallPin3D
                      location='HQ Office'
                      size='sm'
                      variant='default'
                    />
                  </div>
                  <div className='text-xs sm:text-sm font-semibold'>Location</div>
                  <div className='text-xs text-muted-foreground hidden sm:block'>HQ Office</div>
                </div>
              </div>

              {/* Enhanced Action Button - Compact */}
              <Button
                onClick={handleClockIn}
                className='modern-btn w-full text-sm sm:text-base py-2 sm:py-3 rounded-xl'
                data-aos='fade-up'
                data-aos-delay='700'
              >
                Continue to Login
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Compact Footer - Smaller */}
        <div className='mt-4 sm:mt-6 text-center' data-aos='fade-up' data-aos-delay='800'>
          <div className='glassmorphism rounded-lg p-2 sm:p-3 inline-block backdrop-blur-md bg-black/20 border border-white/20'>
            <h3 className='text-sm sm:text-base font-bold modern-heading mb-0.5 text-white'>CTNL Work-Board</h3>
            <p className='text-xs text-gray-300'>Secure • Reliable • Efficient</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ClockInPage
