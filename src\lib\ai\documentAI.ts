import { supabase } from '@/integrations/supabase/client';

interface DocumentAnalysisResult {
  id: string;
  file_name: string;
  analysis_result: {
    summary: string;
    keyPoints: string[];
    suggestedActions: string[];
    categories?: string[];
  } | null;
  status: string;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export const analyzeDocument = async (content: string, fileName: string = 'Analyzed Document'): Promise<DocumentAnalysisResult> => {
  try {
    const { data, error } = await supabase.functions.invoke('analyze-document', {
      body: { content, fileName }
    });

    if (error) {
      console.error('Error analyzing document:', error);
      throw error;
    }

    // Create a document analysis record
    const { data: analysisRecord, error: dbError } = await supabase
      .from('document_analysis')
      .insert({
        file_name: fileName,
        analysis_result: data,
        status: 'completed',
        created_by: (await supabase.auth.getUser()).data.user?.id
      })
      .select()
      .single();

    if (dbError) {
      console.error('Error saving analysis:', dbError);
      throw dbError;
    }

    return analysisRecord as DocumentAnalysisResult;
  } catch (error) {
    console.error('Error in analyzeDocument:', error);
    throw error;
  }
};

export type { DocumentAnalysisResult, DocumentAnalysisResult };
