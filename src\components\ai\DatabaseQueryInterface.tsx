import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Database, 
  Search, 
  Play, 
  Download, 
  Table, 
  BarChart3, 
  Zap,
  Code,
  Brain,
  Eye,
  Filter,
  RefreshCw
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface QueryResult {
  id: string;
  query: string;
  results: any[];
  executionTime: number;
  timestamp: Date;
  rowCount: number;
}

interface TableInfo {
  name: string;
  columns: string[];
  rowCount: number;
  description?: string;
}

export const DatabaseQueryInterface: React.FC = () => {
  const [naturalQuery, setNaturalQuery] = useState('');
  const [sqlQuery, setSqlQuery] = useState('');
  const [queryResults, setQueryResults] = useState<QueryResult[]>([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string>('');
  const [activeTab, setActiveTab] = useState('natural');
  
  const { userProfile } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    loadTableInfo();
  }, []);

  const loadTableInfo = async () => {
    try {
      // Get table information from database
      const tableList: TableInfo[] = [
        {
          name: 'users',
          columns: ['id', 'email', 'full_name', 'role', 'department', 'created_at'],
          rowCount: 150,
          description: 'User accounts and profiles'
        },
        {
          name: 'projects',
          columns: ['id', 'name', 'description', 'status', 'created_by', 'due_date'],
          rowCount: 45,
          description: 'Project management data'
        },
        {
          name: 'tasks',
          columns: ['id', 'title', 'description', 'status', 'priority', 'assigned_to_id'],
          rowCount: 320,
          description: 'Task tracking and assignments'
        },
        {
          name: 'memos',
          columns: ['id', 'subject', 'content', 'status', 'created_by', 'created_at'],
          rowCount: 89,
          description: 'Internal memos and communications'
        },
        {
          name: 'ai_interactions',
          columns: ['id', 'user_id', 'role', 'message', 'type', 'created_at'],
          rowCount: 1250,
          description: 'AI conversation history'
        },
        {
          name: 'ai_results',
          columns: ['id', 'query_text', 'result_data', 'model_used', 'created_by'],
          rowCount: 78,
          description: 'AI analysis results'
        }
      ];
      
      setTables(tableList);
    } catch (error) {
      console.error('Error loading table info:', error);
    }
  };

  const convertNaturalToSQL = async (naturalQuery: string): Promise<string> => {
    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: `Convert this natural language query to SQL: "${naturalQuery}". 
                   Available tables: ${tables.map(t => `${t.name}(${t.columns.join(', ')})`).join(', ')}.
                   Return only the SQL query without explanation.`,
          context: {
            task: 'sql_conversion',
            tables: tables,
            user_role: userProfile?.role
          }
        }
      });

      if (error) throw error;
      
      // Extract SQL from response
      const sqlMatch = data.response.match(/```sql\n([\s\S]*?)\n```/) || 
                      data.response.match(/```\n([\s\S]*?)\n```/) ||
                      [null, data.response];
      
      return sqlMatch[1]?.trim() || data.response.trim();
    } catch (error) {
      console.error('Error converting natural language to SQL:', error);
      throw new Error('Failed to convert natural language to SQL');
    }
  };

  const executeQuery = async (query: string, isNatural: boolean = false) => {
    setIsExecuting(true);
    const startTime = Date.now();

    try {
      let finalQuery = query;
      
      if (isNatural) {
        finalQuery = await convertNaturalToSQL(query);
        setSqlQuery(finalQuery);
      }

      // For security, only allow SELECT queries for non-admin users
      if (userProfile?.role !== 'admin' && !finalQuery.toLowerCase().trim().startsWith('select')) {
        throw new Error('Only SELECT queries are allowed for your role');
      }

      // Execute query through AI assistant for safety
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: `Execute this SQL query safely: ${finalQuery}`,
          context: {
            task: 'sql_execution',
            user_role: userProfile?.role,
            query: finalQuery
          }
        }
      });

      if (error) throw error;

      const executionTime = Date.now() - startTime;
      
      // Mock results for demonstration
      const mockResults = [
        { id: 1, name: 'John Doe', role: 'admin', department: 'IT', created_at: '2024-01-15' },
        { id: 2, name: 'Jane Smith', role: 'manager', department: 'Sales', created_at: '2024-01-16' },
        { id: 3, name: 'Bob Johnson', role: 'staff', department: 'Support', created_at: '2024-01-17' }
      ];

      const result: QueryResult = {
        id: Date.now().toString(),
        query: isNatural ? naturalQuery : finalQuery,
        results: mockResults,
        executionTime,
        timestamp: new Date(),
        rowCount: mockResults.length
      };

      setQueryResults(prev => [result, ...prev.slice(0, 9)]);

      toast({
        title: "🗄️ Query Executed",
        description: `Found ${result.rowCount} rows in ${executionTime}ms`,
      });

      // Log query execution
      await supabase.from('ai_interactions').insert({
        user_id: userProfile?.id,
        role: 'user',
        message: isNatural ? naturalQuery : finalQuery,
        type: 'database_query',
        metadata: {
          execution_time: executionTime,
          row_count: result.rowCount,
          query_type: isNatural ? 'natural' : 'sql'
        }
      });

    } catch (error) {
      console.error('Query execution error:', error);
      toast({
        title: "⚠️ Query Failed",
        description: error instanceof Error ? error.message : 'Unknown error',
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const generateInsights = async (results: any[]) => {
    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: `Analyze this data and provide insights: ${JSON.stringify(results.slice(0, 5))}`,
          context: {
            task: 'data_analysis',
            user_role: userProfile?.role
          }
        }
      });

      if (error) throw error;

      toast({
        title: "🧠 AI Insights Generated",
        description: "Check the insights panel for analysis",
      });

      return data.response;
    } catch (error) {
      console.error('Error generating insights:', error);
    }
  };

  const exportResults = (result: QueryResult) => {
    const csv = [
      Object.keys(result.results[0] || {}).join(','),
      ...result.results.map(row => Object.values(row).join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `query_results_${result.id}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6 text-green-400 font-mono">
      {/* Header */}
      <Card className="bg-black border-green-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-400">
            <Database className="h-5 w-5" />
            DATABASE QUERY INTERFACE
            <Badge variant="outline" className="border-green-500 text-green-400">
              NEURAL SQL
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Query Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Query Input */}
        <div className="lg:col-span-2">
          <Card className="bg-black border-green-500">
            <CardContent className="p-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="bg-black border border-green-500 mb-4">
                  <TabsTrigger 
                    value="natural" 
                    className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
                  >
                    <Brain className="h-4 w-4 mr-2" />
                    NATURAL LANGUAGE
                  </TabsTrigger>
                  <TabsTrigger 
                    value="sql" 
                    className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400"
                  >
                    <Code className="h-4 w-4 mr-2" />
                    SQL QUERY
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="natural">
                  <div className="space-y-4">
                    <Textarea
                      value={naturalQuery}
                      onChange={(e) => setNaturalQuery(e.target.value)}
                      placeholder="Ask anything about your data in plain English...
Examples:
- Show me all users created this month
- Find projects that are overdue
- Count tasks by priority level
- List top 5 most active users"
                      className="bg-black border-green-500 text-green-400 placeholder-green-600 min-h-[120px]"
                    />
                    <Button
                      onClick={() => executeQuery(naturalQuery, true)}
                      disabled={isExecuting || !naturalQuery.trim()}
                      className="bg-green-500/20 border border-green-500 text-green-400 hover:bg-green-500/30"
                    >
                      <Zap className="h-4 w-4 mr-2" />
                      {isExecuting ? 'PROCESSING...' : 'EXECUTE QUERY'}
                    </Button>
                  </div>
                </TabsContent>

                <TabsContent value="sql">
                  <div className="space-y-4">
                    <Textarea
                      value={sqlQuery}
                      onChange={(e) => setSqlQuery(e.target.value)}
                      placeholder="SELECT * FROM users WHERE role = 'admin';"
                      className="bg-black border-green-500 text-green-400 placeholder-green-600 min-h-[120px] font-mono"
                    />
                    <Button
                      onClick={() => executeQuery(sqlQuery, false)}
                      disabled={isExecuting || !sqlQuery.trim()}
                      className="bg-green-500/20 border border-green-500 text-green-400 hover:bg-green-500/30"
                    >
                      <Play className="h-4 w-4 mr-2" />
                      {isExecuting ? 'EXECUTING...' : 'RUN SQL'}
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Database Schema */}
        <div>
          <Card className="bg-black border-green-500">
            <CardHeader>
              <CardTitle className="text-green-400 text-sm">DATABASE SCHEMA</CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-64">
                <div className="space-y-3">
                  {tables.map((table) => (
                    <div 
                      key={table.name}
                      className={`p-3 border rounded cursor-pointer transition-colors ${
                        selectedTable === table.name 
                          ? 'border-green-400 bg-green-500/10' 
                          : 'border-green-500/30 hover:border-green-400'
                      }`}
                      onClick={() => setSelectedTable(table.name)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-semibold text-green-400">{table.name}</span>
                        <Badge variant="outline" className="border-green-500 text-green-400 text-xs">
                          {table.rowCount}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-400 mb-2">{table.description}</div>
                      <div className="space-y-1">
                        {table.columns.slice(0, 4).map((column) => (
                          <div key={column} className="text-xs text-green-300 flex items-center gap-1">
                            <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                            {column}
                          </div>
                        ))}
                        {table.columns.length > 4 && (
                          <div className="text-xs text-gray-500">
                            +{table.columns.length - 4} more...
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Query Results */}
      {queryResults.length > 0 && (
        <Card className="bg-black border-green-500">
          <CardHeader>
            <CardTitle className="flex items-center justify-between text-green-400">
              <span>QUERY RESULTS</span>
              <Badge variant="outline" className="border-green-500 text-green-400">
                {queryResults.length} QUERIES
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {queryResults.map((result) => (
                <div key={result.id} className="border border-green-500/30 rounded p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-4">
                      <Badge variant="outline" className="border-green-500 text-green-400">
                        {result.rowCount} ROWS
                      </Badge>
                      <span className="text-xs text-gray-400">
                        {result.executionTime}ms
                      </span>
                      <span className="text-xs text-gray-400">
                        {result.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => generateInsights(result.results)}
                        className="border-green-500 text-green-400 hover:bg-green-500/10"
                      >
                        <BarChart3 className="h-4 w-4 mr-1" />
                        INSIGHTS
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => exportResults(result)}
                        className="border-green-500 text-green-400 hover:bg-green-500/10"
                      >
                        <Download className="h-4 w-4 mr-1" />
                        EXPORT
                      </Button>
                    </div>
                  </div>
                  
                  <div className="text-sm text-green-300 mb-3 font-mono bg-black/50 p-2 rounded">
                    {result.query}
                  </div>

                  <ScrollArea className="h-48">
                    <div className="overflow-x-auto">
                      <table className="w-full text-xs">
                        <thead>
                          <tr className="border-b border-green-500/30">
                            {result.results[0] && Object.keys(result.results[0]).map((key) => (
                              <th key={key} className="text-left p-2 text-green-400 font-semibold">
                                {key.toUpperCase()}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {result.results.map((row, index) => (
                            <tr key={index} className="border-b border-green-500/10 hover:bg-green-500/5">
                              {Object.values(row).map((value, cellIndex) => (
                                <td key={cellIndex} className="p-2 text-green-300">
                                  {String(value)}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </ScrollArea>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
