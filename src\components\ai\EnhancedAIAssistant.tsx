/**
 * Enhanced AI Assistant with LangChain Integration
 * Features memory, RAG, agents, and advanced conversation capabilities
 */

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Bot, 
  User, 
  Send, 
  Loader2, 
  Brain, 
  Database, 
  FileText, 
  Settings,
  MessageSquare,
  Zap,
  Clock,
  CheckCircle
} from 'lucide-react';
import { langChainAgent } from '@/lib/langchain/agent-system';
import { lang<PERSON>hainMemory } from '@/lib/langchain/memory';
import { langChainRAG } from '@/lib/langchain/rag-system';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  confidence?: number;
  sources?: any[];
  actions?: any[];
  reasoning?: string;
  executionTime?: number;
}

interface EnhancedAIAssistantProps {
  className?: string;
  sessionId?: string;
  showSources?: boolean;
  showActions?: boolean;
  showReasoning?: boolean;
  enableRAG?: boolean;
  enableAgents?: boolean;
}

export function EnhancedAIAssistant({
  className,
  sessionId,
  showSources = true,
  showActions = true,
  showReasoning = false,
  enableRAG = true,
  enableAgents = true,
}: EnhancedAIAssistantProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(sessionId || null);
  const [ragStats, setRagStats] = useState<any>(null);
  const [availableTools, setAvailableTools] = useState<any[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Initialize session and load conversation history
  useEffect(() => {
    if (!user) return;

    const initializeSession = async () => {
      try {
        let sessionToUse = currentSessionId;
        
        if (!sessionToUse) {
          // Create new session
          sessionToUse = await langChainMemory.createSession(user.id, 'AI Assistant Chat');
          setCurrentSessionId(sessionToUse);
        }

        // Load conversation history
        const history = await langChainMemory.getConversationHistory(sessionToUse);
        const formattedMessages: Message[] = history.map(msg => ({
          id: msg.id,
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp,
          confidence: msg.metadata?.confidence,
          sources: msg.metadata?.sources,
          actions: msg.metadata?.actions,
          reasoning: msg.metadata?.reasoning,
          executionTime: msg.metadata?.executionTime,
        }));
        
        setMessages(formattedMessages);

        // Load RAG statistics
        if (enableRAG) {
          const stats = await langChainRAG.getStatistics();
          setRagStats(stats);
        }

        // Load available tools
        if (enableAgents) {
          const tools = langChainAgent.getAvailableTools();
          setAvailableTools(tools);
        }
      } catch (error) {
        console.error('Failed to initialize AI session:', error);
      }
    };

    initializeSession();
  }, [user, currentSessionId, enableRAG, enableAgents]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle message submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isLoading || !user || !currentSessionId) return;

    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Process with LangChain agent system
      const response = await langChainAgent.processQuery(
        userMessage.content,
        user.id,
        currentSessionId
      );

      const assistantMessage: Message = {
        id: `msg_${Date.now()}_assistant`,
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        confidence: response.confidence,
        sources: response.sources,
        actions: response.actions,
        reasoning: response.reasoning,
        executionTime: response.executionTime,
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Failed to process message:', error);
      
      const errorMessage: Message = {
        id: `msg_${Date.now()}_error`,
        role: 'assistant',
        content: 'I apologize, but I encountered an error processing your request. Please try again.',
        timestamp: new Date(),
        confidence: 0.1,
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  }, [input, isLoading, user, currentSessionId]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === '/' && e.ctrlKey) {
        e.preventDefault();
        inputRef.current?.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const getConfidenceColor = (confidence?: number) => {
    if (!confidence) return 'bg-gray-500';
    if (confidence >= 0.8) return 'bg-green-500';
    if (confidence >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getConfidenceText = (confidence?: number) => {
    if (!confidence) return 'Unknown';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.6) return 'Medium';
    return 'Low';
  };

  return (
    <TooltipProvider>
      <Card className={cn("flex flex-col h-[600px]", className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Bot className="h-5 w-5" />
              <span>Enhanced AI Assistant</span>
              {enableRAG && (
                <Badge variant="secondary" className="text-xs">
                  <Database className="h-3 w-3 mr-1" />
                  RAG
                </Badge>
              )}
              {enableAgents && (
                <Badge variant="secondary" className="text-xs">
                  <Zap className="h-3 w-3 mr-1" />
                  Agents
                </Badge>
              )}
            </CardTitle>
            
            <div className="flex items-center space-x-2">
              {ragStats && (
                <Tooltip>
                  <TooltipTrigger>
                    <Badge variant="outline" className="text-xs">
                      <FileText className="h-3 w-3 mr-1" />
                      {ragStats.totalDocuments} docs
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm">
                      <div>Documents: {ragStats.totalDocuments}</div>
                      <div>Chunks: {ragStats.totalChunks}</div>
                      <div>Recent queries: {ragStats.recentQueries}</div>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
              
              {availableTools.length > 0 && (
                <Tooltip>
                  <TooltipTrigger>
                    <Badge variant="outline" className="text-xs">
                      <Settings className="h-3 w-3 mr-1" />
                      {availableTools.length} tools
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm">
                      <div className="font-medium">Available Tools:</div>
                      {availableTools.slice(0, 5).map(tool => (
                        <div key={tool.name} className="text-xs">
                          • {tool.name}
                        </div>
                      ))}
                      {availableTools.length > 5 && (
                        <div className="text-xs">
                          ... and {availableTools.length - 5} more
                        </div>
                      )}
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="flex-1 flex flex-col p-0">
          {/* Messages */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Welcome to Enhanced AI Assistant</p>
                  <p className="text-sm">
                    I can help you with tasks, answer questions using our knowledge base,
                    and perform actions using available tools.
                  </p>
                  <div className="mt-4 text-xs">
                    Press <kbd className="px-1 py-0.5 bg-muted rounded">Ctrl + /</kbd> to focus input
                  </div>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={cn(
                    "flex space-x-3",
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  )}
                >
                  {message.role === 'assistant' && (
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>
                        <Bot className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}

                  <div className={cn(
                    "max-w-[80%] space-y-2",
                    message.role === 'user' ? 'items-end' : 'items-start'
                  )}>
                    <div className={cn(
                      "rounded-lg px-3 py-2",
                      message.role === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    )}>
                      <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    </div>

                    {/* Message metadata */}
                    <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                      <span>{message.timestamp.toLocaleTimeString()}</span>
                      
                      {message.confidence !== undefined && (
                        <Tooltip>
                          <TooltipTrigger>
                            <div className="flex items-center space-x-1">
                              <div className={cn(
                                "h-2 w-2 rounded-full",
                                getConfidenceColor(message.confidence)
                              )} />
                              <span>{getConfidenceText(message.confidence)}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            Confidence: {Math.round((message.confidence || 0) * 100)}%
                          </TooltipContent>
                        </Tooltip>
                      )}

                      {message.executionTime && (
                        <Tooltip>
                          <TooltipTrigger>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-3 w-3" />
                              <span>{message.executionTime}ms</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            Processing time
                          </TooltipContent>
                        </Tooltip>
                      )}
                    </div>

                    {/* Actions */}
                    {showActions && message.actions && message.actions.length > 0 && (
                      <div className="space-y-1">
                        <div className="text-xs font-medium text-muted-foreground">Actions:</div>
                        {message.actions.map((action, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {action.tool}
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Sources */}
                    {showSources && message.sources && message.sources.length > 0 && (
                      <div className="space-y-1">
                        <div className="text-xs font-medium text-muted-foreground">Sources:</div>
                        <div className="space-y-1">
                          {message.sources.slice(0, 3).map((source, index) => (
                            <div key={index} className="text-xs bg-background border rounded p-2">
                              <div className="font-medium">{source.title || 'Document'}</div>
                              <div className="text-muted-foreground truncate">
                                {source.content?.substring(0, 100)}...
                              </div>
                              {source.similarity && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  Relevance: {Math.round(source.similarity * 100)}%
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Reasoning */}
                    {showReasoning && message.reasoning && (
                      <div className="space-y-1">
                        <div className="text-xs font-medium text-muted-foreground">Reasoning:</div>
                        <div className="text-xs bg-background border rounded p-2">
                          {message.reasoning}
                        </div>
                      </div>
                    )}
                  </div>

                  {message.role === 'user' && (
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.user_metadata?.avatar_url} />
                      <AvatarFallback>
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                  )}
                </div>
              ))}

              {isLoading && (
                <div className="flex space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-muted rounded-lg px-3 py-2">
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      <span className="text-sm">Thinking...</span>
                    </div>
                  </div>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          <Separator />

          {/* Input */}
          <form onSubmit={handleSubmit} className="p-4">
            <div className="flex space-x-2">
              <Input
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Ask me anything... (Ctrl+/ to focus)"
                disabled={isLoading}
                className="flex-1"
              />
              <Button type="submit" disabled={isLoading || !input.trim()}>
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
