import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { notificationService } from '@/services/notificationService';
import {
    AttendanceStats,
    ClockInRequest,
    ClockOutRequest,
    RealtimeClockStatus,
    TeamTimeLog,
    TimeLogEntry,
    TimeTrackingActions,
    TimeTrackingHookOptions,
    TimeTrackingState,
} from '@/types/timeTracking';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';

export const useTimeTracking = (options: TimeTrackingHookOptions = {}) => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [state, setState] = useState<TimeTrackingState>({
    isLoading: false,
    error: null,
  });

  // Get current user's active session
  const { data: currentSession, isLoading: sessionLoading } = useQuery({
    queryKey: ['current-time-session', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return null;

      try {
        // Use a more robust query without .single() to avoid 406 errors
        const { data, error } = await supabase
          .from('time_logs')
          .select('*')
          .eq('user_id', userProfile.id)
          .is('clock_out', null)
          .order('clock_in', { ascending: false })
          .limit(1);

        if (error) {
          console.log('Error fetching current session:', error);

          // If it's a table not found error, return null
          if (error.status === 404 || error.code === 'PGRST106') {
            console.log('time_logs table not found, returning null');
            return null;
          }

          // For other errors, throw to trigger error boundary
          throw error;
        }

        // Return the first result or null if no active session
        return (data && data.length > 0 ? data[0] : null) as TimeLogEntry | null;
      } catch (error: any) {
        console.error('Failed to fetch current session:', error);

        // Return null for common errors to prevent app crashes
        if (error.status === 406 || error.status === 404 || error.code === 'PGRST116') {
          return null;
        }

        throw error;
      }
    },
    enabled: !!userProfile?.id,
    refetchInterval: options.realtime ? (options.refetchInterval || 30000) : false,
  });

  // Get realtime status for all users (admin/manager view)
  const { data: realtimeStatus, isLoading: statusLoading } = useQuery({
    queryKey: ['realtime-clock-status'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_realtime_clock_status');
      if (error) throw error;
      return data as RealtimeClockStatus[];
    },
    enabled: ['admin', 'manager', 'staff-admin', 'accountant'].includes(userProfile?.role || ''),
    refetchInterval: options.realtime ? (options.refetchInterval || 30000) : false,
  });

  // Get team time logs (for managers)
  const { data: teamLogs, isLoading: teamLoading } = useQuery({
    queryKey: ['team-time-logs', userProfile?.id, options.dateRange],
    queryFn: async () => {
      if (!userProfile?.id) return [];

      const startDate = options.dateRange?.start || new Date();
      const endDate = options.dateRange?.end || new Date();

      try {
        const { data, error } = await supabase.rpc('get_team_time_logs', {
          manager_user_id: userProfile.id,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0],
        });

        if (error) throw error;
        return data as TeamTimeLog[];
      } catch (error: any) {
        // If RPC function doesn't exist (404 error), fall back to direct query
        if (error.status === 404 || error.code === 'PGRST202') {
          console.log('get_team_time_logs RPC function not found, using fallback query');

          const { data: fallbackData, error: fallbackError } = await supabase
            .from('time_logs')
            .select(`
              *,
              profiles:user_id (
                full_name,
                department_id
              )
            `)
            .gte('clock_in', startDate.toISOString().split('T')[0] + 'T00:00:00')
            .lte('clock_in', endDate.toISOString().split('T')[0] + 'T23:59:59')
            .order('clock_in', { ascending: false });

          if (fallbackError && (fallbackError.status === 404 || fallbackError.code === 'PGRST106')) {
            console.log('time_logs table not found, returning empty array');
            return [];
          }

          if (fallbackError) throw fallbackError;
          return fallbackData || [];
        }
        throw error;
      }
    },
    enabled: !!userProfile?.id && ['admin', 'manager', 'staff-admin', 'accountant'].includes(userProfile?.role || ''),
  });

  // Get attendance statistics
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['attendance-stats', options.departmentId],
    queryFn: async () => {
      try {
        const { data, error } = await supabase.rpc('get_attendance_stats', {
          target_date: new Date().toISOString().split('T')[0],
          department_id: options.departmentId || null,
        });

        if (error) throw error;
        return data[0] as AttendanceStats;
      } catch (error: any) {
        // If RPC function doesn't exist (404 error), fall back to manual calculation
        if (error.status === 404 || error.code === 'PGRST202') {
          console.log('get_attendance_stats RPC function not found, using fallback calculation');

          const today = new Date().toISOString().split('T')[0];
          const { data: todayLogs, error: fallbackError } = await supabase
            .from('time_logs')
            .select('user_id, clock_in, clock_out')
            .gte('clock_in', today + 'T00:00:00')
            .lte('clock_in', today + 'T23:59:59');

          if (fallbackError && (fallbackError.status === 404 || fallbackError.code === 'PGRST106')) {
            console.log('time_logs table not found, returning default stats');
            return {
              total_present: 0,
              total_absent: 0,
              attendance_rate: 0
            } as AttendanceStats;
          }

          if (fallbackError) throw fallbackError;

          // Calculate stats manually
          const totalPresent = todayLogs?.filter(log => log.clock_in && !log.clock_out).length || 0;
          const totalCompleted = todayLogs?.filter(log => log.clock_in && log.clock_out).length || 0;
          const totalActive = totalPresent + totalCompleted;

          return {
            total_present: totalPresent,
            total_absent: 0, // Can't calculate without knowing total employees
            attendance_rate: totalActive > 0 ? (totalActive / Math.max(totalActive, 1)) * 100 : 0
          } as AttendanceStats;
        }
        throw error;
      }
    },
    enabled: ['admin', 'manager', 'staff-admin', 'accountant'].includes(userProfile?.role || ''),
  });

  // Clock in mutation
  const clockInMutation = useMutation({
    mutationFn: async (request: ClockInRequest) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('time_logs')
        .insert({
          user_id: userProfile.id,
          clock_in: new Date().toISOString(),
          clock_in_timestamp: new Date().toISOString(),
          latitude: request.location.latitude,
          longitude: request.location.longitude,
          location_address: request.location.address,
          location_accuracy: request.location.accuracy,
          location_method: request.location.method || 'gps',
          device_info: request.device,
          device_type: request.device.type,
          browser_info: request.device.browser,
          ip_address: request.device.ipAddress,
          timezone: request.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone,
          clock_in_method: request.method || 'manual',
          project_id: request.project_id,
          task_id: request.task_id,
          notes: request.notes,
          date: new Date().toISOString().split('T')[0],
        })
        .select();

      if (error) throw error;
      return (data && data.length > 0 ? data[0] : null) as TimeLogEntry;
    },
    onSuccess: async (data) => {
      toast({
        title: "Clocked In Successfully",
        description: `Location: ${data.location_address}`,
      });

      // Send notification
      try {
        await notificationService.notifyClockIn(
          userProfile!.id,
          userProfile!.full_name || userProfile!.email || 'User',
          data.location_address || 'Unknown location',
          new Date(data.clock_in!)
        );
      } catch (error) {
        console.error('Failed to send clock-in notification:', error);
      }

      queryClient.invalidateQueries({ queryKey: ['current-time-session'] });
      queryClient.invalidateQueries({ queryKey: ['realtime-clock-status'] });
      queryClient.invalidateQueries({ queryKey: ['team-time-logs'] });
      queryClient.invalidateQueries({ queryKey: ['attendance-stats'] });
    },
    onError: (error) => {
      toast({
        title: "Clock In Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Clock out mutation
  const clockOutMutation = useMutation({
    mutationFn: async ({ sessionId, request }: { sessionId: string; request?: ClockOutRequest }) => {
      const clockOutTime = new Date().toISOString();
      
      const updateData: any = {
        clock_out: clockOutTime,
        clock_out_timestamp: clockOutTime,
        clock_out_method: request?.method || 'manual',
      };

      if (request?.location) {
        updateData.location_latitude = request.location.latitude;
        updateData.location_longitude = request.location.longitude;
      }

      if (request?.notes) {
        updateData.notes = request.notes;
      }

      if (request?.break_duration) {
        updateData.break_duration = request.break_duration;
      }

      if (request?.overtime_hours) {
        updateData.overtime_hours = request.overtime_hours;
      }

      const { data, error } = await supabase
        .from('time_logs')
        .update(updateData)
        .eq('id', sessionId)
        .select();

      if (error) throw error;
      return (data && data.length > 0 ? data[0] : null) as TimeLogEntry;
    },
    onSuccess: async (data) => {
      toast({
        title: "Clocked Out Successfully",
        description: "Your work session has been recorded.",
      });

      // Send notification
      try {
        const clockInTime = new Date(data.clock_in!);
        const clockOutTime = new Date(data.clock_out!);
        const duration = Math.round((clockOutTime.getTime() - clockInTime.getTime()) / (1000 * 60)); // minutes

        await notificationService.notifyClockOut(
          userProfile!.id,
          userProfile!.full_name || userProfile!.email || 'User',
          data.location_address || 'Unknown location',
          clockOutTime,
          duration
        );
      } catch (error) {
        console.error('Failed to send clock-out notification:', error);
      }

      queryClient.invalidateQueries({ queryKey: ['current-time-session'] });
      queryClient.invalidateQueries({ queryKey: ['realtime-clock-status'] });
      queryClient.invalidateQueries({ queryKey: ['team-time-logs'] });
      queryClient.invalidateQueries({ queryKey: ['attendance-stats'] });
    },
    onError: (error) => {
      toast({
        title: "Clock Out Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update status mutation
  const updateStatusMutation = useMutation({
    mutationFn: async ({ sessionId, status }: { sessionId: string; status: TimeLogEntry['status'] }) => {
      const { error } = await supabase
        .from('time_logs')
        .update({ status })
        .eq('id', sessionId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['current-time-session'] });
      queryClient.invalidateQueries({ queryKey: ['realtime-clock-status'] });
    },
  });

  // Actions object
  const actions: TimeTrackingActions = {
    clockIn: clockInMutation.mutateAsync,
    clockOut: (sessionId: string, request?: ClockOutRequest) => 
      clockOutMutation.mutateAsync({ sessionId, request }),
    updateStatus: (sessionId: string, status: TimeLogEntry['status']) =>
      updateStatusMutation.mutateAsync({ sessionId, status }),
    addBreak: async (sessionId: string, duration: number) => {
      const { error } = await supabase
        .from('time_logs')
        .update({ break_duration: duration })
        .eq('id', sessionId);
      
      if (error) throw error;
      queryClient.invalidateQueries({ queryKey: ['current-time-session'] });
    },
    addNote: async (sessionId: string, note: string) => {
      const { error } = await supabase
        .from('time_logs')
        .update({ notes: note })
        .eq('id', sessionId);
      
      if (error) throw error;
      queryClient.invalidateQueries({ queryKey: ['current-time-session'] });
    },
    refreshData: async () => {
      await queryClient.invalidateQueries({ queryKey: ['current-time-session'] });
      await queryClient.invalidateQueries({ queryKey: ['realtime-clock-status'] });
      await queryClient.invalidateQueries({ queryKey: ['team-time-logs'] });
      await queryClient.invalidateQueries({ queryKey: ['attendance-stats'] });
    },
  };

  // Update state
  useEffect(() => {
    setState({
      currentSession,
      isLoading: sessionLoading || statusLoading || teamLoading || statsLoading,
      error: null,
      realtimeStatus,
      teamLogs,
      stats,
    });
  }, [currentSession, sessionLoading, statusLoading, teamLoading, statsLoading, realtimeStatus, teamLogs, stats]);

  // Set up real-time subscriptions
  useEffect(() => {
    if (!options.realtime) return;

    const subscription = supabase
      .channel('time_tracking_updates')
      .on('postgres_changes', 
        { 
          event: '*', 
          schema: 'public', 
          table: 'time_logs' 
        }, 
        () => {
          actions.refreshData();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [options.realtime]);

  return {
    ...state,
    actions,
    isClockingIn: clockInMutation.isPending,
    isClockingOut: clockOutMutation.isPending,
    isUpdatingStatus: updateStatusMutation.isPending,
  };
};
