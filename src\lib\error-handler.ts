/**
 * Comprehensive Error Handling System
 * Centralized error handling, logging, and user notification system
 */

import { toast } from 'sonner';

// Error types and categories
export type ErrorCategory = 
  | 'authentication' 
  | 'authorization' 
  | 'validation' 
  | 'network' 
  | 'database' 
  | 'api' 
  | 'ui' 
  | 'system' 
  | 'integration'
  | 'performance'
  | 'unknown';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface AppError {
  id: string;
  code: string;
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  timestamp: Date;
  context?: Record<string, any>;
  stack?: string;
  userMessage?: string;
  actionable?: boolean;
  retryable?: boolean;
  suggestion?: string;
  actionUrl?: string;
}

export interface ErrorContext {
  component?: string;
  function?: string;
  userId?: string;
  userRole?: string;
  url?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
  metadata?: Record<string, any>;
}

// Error code mappings
export const ERROR_CODES = {
  // Authentication errors
  AUTH_INVALID_CREDENTIALS: 'AUTH_001',
  AUTH_SESSION_EXPIRED: 'AUTH_002',
  AUTH_INSUFFICIENT_PERMISSIONS: 'AUTH_003',
  AUTH_ACCOUNT_LOCKED: 'AUTH_004',
  AUTH_MFA_REQUIRED: 'AUTH_005',

  // Database errors
  DB_CONNECTION_FAILED: 'DB_001',
  DB_QUERY_FAILED: 'DB_002',
  DB_CONSTRAINT_VIOLATION: 'DB_003',
  DB_TIMEOUT: 'DB_004',
  DB_MIGRATION_FAILED: 'DB_005',

  // API errors
  API_RATE_LIMIT_EXCEEDED: 'API_001',
  API_INVALID_REQUEST: 'API_002',
  API_SERVICE_UNAVAILABLE: 'API_003',
  API_TIMEOUT: 'API_004',
  API_INVALID_RESPONSE: 'API_005',

  // Validation errors
  VALIDATION_REQUIRED_FIELD: 'VAL_001',
  VALIDATION_INVALID_FORMAT: 'VAL_002',
  VALIDATION_OUT_OF_RANGE: 'VAL_003',
  VALIDATION_DUPLICATE_VALUE: 'VAL_004',

  // Network errors
  NETWORK_CONNECTION_LOST: 'NET_001',
  NETWORK_TIMEOUT: 'NET_002',
  NETWORK_DNS_FAILED: 'NET_003',

  // System errors
  SYSTEM_OUT_OF_MEMORY: 'SYS_001',
  SYSTEM_DISK_FULL: 'SYS_002',
  SYSTEM_SERVICE_DOWN: 'SYS_003',

  // Integration errors
  INTEGRATION_API_KEY_INVALID: 'INT_001',
  INTEGRATION_QUOTA_EXCEEDED: 'INT_002',
  INTEGRATION_SERVICE_DOWN: 'INT_003',

  // Performance errors
  PERF_SLOW_QUERY: 'PERF_001',
  PERF_MEMORY_LEAK: 'PERF_002',
  PERF_HIGH_CPU: 'PERF_003',

  // Generic errors
  UNKNOWN_ERROR: 'UNK_001',
} as const;

// Error severity mappings
const ERROR_SEVERITY_MAP: Record<string, ErrorSeverity> = {
  // Critical errors that break core functionality
  AUTH_SESSION_EXPIRED: 'critical',
  DB_CONNECTION_FAILED: 'critical',
  SYSTEM_OUT_OF_MEMORY: 'critical',
  SYSTEM_SERVICE_DOWN: 'critical',

  // High severity errors that impact user experience
  AUTH_INVALID_CREDENTIALS: 'high',
  API_SERVICE_UNAVAILABLE: 'high',
  DB_QUERY_FAILED: 'high',
  NETWORK_CONNECTION_LOST: 'high',

  // Medium severity errors that cause inconvenience
  API_RATE_LIMIT_EXCEEDED: 'medium',
  VALIDATION_REQUIRED_FIELD: 'medium',
  API_TIMEOUT: 'medium',
  INTEGRATION_QUOTA_EXCEEDED: 'medium',

  // Low severity errors that are recoverable
  VALIDATION_INVALID_FORMAT: 'low',
  API_INVALID_REQUEST: 'low',
  PERF_SLOW_QUERY: 'low',
};

class ErrorHandler {
  private static instance: ErrorHandler;
  private errorLog: AppError[] = [];
  private maxLogSize = 1000;
  private errorReportingEnabled = true;

  private constructor() {}

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  // Create a standardized error object
  createError(
    code: string,
    message: string,
    category: ErrorCategory,
    context?: ErrorContext,
    originalError?: Error
  ): AppError {
    const severity = ERROR_SEVERITY_MAP[code] || 'medium';
    const error: AppError = {
      id: this.generateErrorId(),
      code,
      message,
      category,
      severity,
      timestamp: new Date(),
      context,
      stack: originalError?.stack,
      userMessage: this.generateUserMessage(code, message),
      actionable: this.isActionable(code),
      retryable: this.isRetryable(code),
      suggestion: this.getSuggestion(code),
      actionUrl: this.getActionUrl(code),
    };

    return error;
  }

  // Handle and log errors
  handleError(
    error: Error | AppError | string,
    context?: ErrorContext,
    showToast = true
  ): AppError {
    let appError: AppError;

    if (typeof error === 'string') {
      appError = this.createError(
        ERROR_CODES.UNKNOWN_ERROR,
        error,
        'unknown',
        context
      );
    } else if (error instanceof Error) {
      appError = this.parseError(error, context);
    } else {
      appError = error;
    }

    // Log the error
    this.logError(appError);

    // Show user notification if requested
    if (showToast) {
      this.showErrorToast(appError);
    }

    // Report to external service in production
    if (process.env.NODE_ENV === 'production' && this.errorReportingEnabled) {
      this.reportError(appError);
    }

    return appError;
  }

  // Parse native errors into AppError format
  private parseError(error: Error, context?: ErrorContext): AppError {
    const message = error.message.toLowerCase();
    let code = ERROR_CODES.UNKNOWN_ERROR;
    let category: ErrorCategory = 'unknown';

    // Authentication errors
    if (message.includes('unauthorized') || message.includes('authentication')) {
      code = ERROR_CODES.AUTH_INVALID_CREDENTIALS;
      category = 'authentication';
    } else if (message.includes('session') && message.includes('expired')) {
      code = ERROR_CODES.AUTH_SESSION_EXPIRED;
      category = 'authentication';
    } else if (message.includes('permission') || message.includes('forbidden')) {
      code = ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS;
      category = 'authorization';
    }
    // Database errors
    else if (message.includes('connection') && message.includes('database')) {
      code = ERROR_CODES.DB_CONNECTION_FAILED;
      category = 'database';
    } else if (message.includes('query') || message.includes('sql')) {
      code = ERROR_CODES.DB_QUERY_FAILED;
      category = 'database';
    } else if (message.includes('constraint') || message.includes('unique')) {
      code = ERROR_CODES.DB_CONSTRAINT_VIOLATION;
      category = 'database';
    }
    // Network errors
    else if (message.includes('network') || message.includes('fetch')) {
      code = ERROR_CODES.NETWORK_CONNECTION_LOST;
      category = 'network';
    } else if (message.includes('timeout')) {
      code = ERROR_CODES.NETWORK_TIMEOUT;
      category = 'network';
    }
    // API errors
    else if (message.includes('rate limit')) {
      code = ERROR_CODES.API_RATE_LIMIT_EXCEEDED;
      category = 'api';
    } else if (message.includes('api key')) {
      code = ERROR_CODES.INTEGRATION_API_KEY_INVALID;
      category = 'integration';
    }

    return this.createError(code, error.message, category, context, error);
  }

  // Log error to internal storage
  private logError(error: AppError): void {
    this.errorLog.unshift(error);
    
    // Maintain log size limit
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Console logging with appropriate level
    const logLevel = this.getLogLevel(error.severity);
    const logData = {
      id: error.id,
      code: error.code,
      message: error.message,
      category: error.category,
      severity: error.severity,
      context: error.context,
      stack: error.stack,
    };

    switch (logLevel) {
      case 'error':
        console.error('🚨 Error:', logData);
        break;
      case 'warn':
        console.warn('⚠️ Warning:', logData);
        break;
      case 'info':
        console.info('ℹ️ Info:', logData);
        break;
      default:
        console.log('📝 Log:', logData);
    }
  }

  // Show user-friendly error toast
  private showErrorToast(error: AppError): void {
    const title = this.getToastTitle(error);
    let description = error.userMessage || error.message;

    // Add suggestion to description if available
    if (error.suggestion) {
      description += `\n\n💡 ${error.suggestion}`;
    }

    // Add action link if available
    if (error.actionUrl) {
      description += `\n\n🔗 Take Action: ${error.actionUrl}`;
    }

    const toastConfig = {
      description,
      duration: this.getToastDuration(error.severity),
    };

    switch (error.severity) {
      case 'critical':
        toast.error(title, toastConfig);
        break;
      case 'high':
        toast.error(title, toastConfig);
        break;
      case 'medium':
        toast.warning(title, toastConfig);
        break;
      case 'low':
        toast.info(title, toastConfig);
        break;
    }
  }

  // Report error to external service
  private reportError(error: AppError): void {
    // In production, integrate with error reporting services like:
    // - Sentry
    // - LogRocket
    // - Bugsnag
    // - Custom logging service

    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Error would be reported to external service:', error);
    }

    // Example Sentry integration:
    // Sentry.captureException(new Error(error.message), {
    //   tags: {
    //     category: error.category,
    //     severity: error.severity,
    //     code: error.code,
    //   },
    //   contexts: {
    //     error: error.context,
    //   },
    // });
  }

  // Utility methods
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateUserMessage(code: string, message: string): string {
    const userMessages: Record<string, string> = {
      [ERROR_CODES.AUTH_INVALID_CREDENTIALS]: 'Invalid email or password. Please check your credentials and try again.',
      [ERROR_CODES.AUTH_SESSION_EXPIRED]: 'Your session has expired. Please log in again.',
      [ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS]: 'You don\'t have permission to perform this action.',
      [ERROR_CODES.DB_CONNECTION_FAILED]: 'Unable to connect to the database. Please try again later.',
      [ERROR_CODES.NETWORK_CONNECTION_LOST]: 'Network connection lost. Please check your internet connection.',
      [ERROR_CODES.API_RATE_LIMIT_EXCEEDED]: 'Too many requests. Please wait a moment and try again.',
      [ERROR_CODES.VALIDATION_REQUIRED_FIELD]: 'Please fill in all required fields.',
    };

    return userMessages[code] || message;
  }

  private isActionable(code: string): boolean {
    const actionableCodes = [
      ERROR_CODES.AUTH_INVALID_CREDENTIALS,
      ERROR_CODES.VALIDATION_REQUIRED_FIELD,
      ERROR_CODES.VALIDATION_INVALID_FORMAT,
      ERROR_CODES.INTEGRATION_API_KEY_INVALID,
    ];
    return actionableCodes.includes(code);
  }

  private isRetryable(code: string): boolean {
    const retryableCodes = [
      ERROR_CODES.NETWORK_TIMEOUT,
      ERROR_CODES.API_TIMEOUT,
      ERROR_CODES.DB_TIMEOUT,
      ERROR_CODES.API_SERVICE_UNAVAILABLE,
    ];
    return retryableCodes.includes(code);
  }

  private getSuggestion(code: string): string | undefined {
    const suggestions: Record<string, string> = {
      [ERROR_CODES.AUTH_INVALID_CREDENTIALS]: 'Double-check your email and password, or use the "Forgot Password" link.',
      [ERROR_CODES.NETWORK_CONNECTION_LOST]: 'Check your internet connection and try again.',
      [ERROR_CODES.API_RATE_LIMIT_EXCEEDED]: 'Wait a few minutes before making more requests.',
      [ERROR_CODES.INTEGRATION_API_KEY_INVALID]: 'Contact your administrator to configure valid API keys.',
    };
    return suggestions[code];
  }

  private getActionUrl(code: string): string | undefined {
    const actionUrls: Record<string, string> = {
      [ERROR_CODES.INTEGRATION_API_KEY_INVALID]: '/dashboard/admin/api-keys',
      [ERROR_CODES.AUTH_INSUFFICIENT_PERMISSIONS]: '/dashboard/profile',
    };
    return actionUrls[code];
  }

  private getLogLevel(severity: ErrorSeverity): string {
    switch (severity) {
      case 'critical':
      case 'high':
        return 'error';
      case 'medium':
        return 'warn';
      case 'low':
        return 'info';
      default:
        return 'log';
    }
  }

  private getToastTitle(error: AppError): string {
    switch (error.severity) {
      case 'critical':
        return '🚨 Critical Error';
      case 'high':
        return '❌ Error';
      case 'medium':
        return '⚠️ Warning';
      case 'low':
        return 'ℹ️ Notice';
      default:
        return 'Error';
    }
  }

  private getToastDuration(severity: ErrorSeverity): number {
    switch (severity) {
      case 'critical':
        return 10000; // 10 seconds
      case 'high':
        return 8000;  // 8 seconds
      case 'medium':
        return 6000;  // 6 seconds
      case 'low':
        return 4000;  // 4 seconds
      default:
        return 5000;
    }
  }

  // Public methods for accessing error data
  getErrorLog(): AppError[] {
    return [...this.errorLog];
  }

  getErrorsByCategory(category: ErrorCategory): AppError[] {
    return this.errorLog.filter(error => error.category === category);
  }

  getErrorsBySeverity(severity: ErrorSeverity): AppError[] {
    return this.errorLog.filter(error => error.severity === severity);
  }

  clearErrorLog(): void {
    this.errorLog = [];
  }

  setErrorReporting(enabled: boolean): void {
    this.errorReportingEnabled = enabled;
  }
}

// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();

// Convenience functions
export const handleError = (
  error: Error | AppError | string,
  context?: ErrorContext,
  showToast = true
): AppError => {
  return errorHandler.handleError(error, context, showToast);
};

export const createError = (
  code: string,
  message: string,
  category: ErrorCategory,
  context?: ErrorContext
): AppError => {
  return errorHandler.createError(code, message, category, context);
};

// Error boundary helper
export const withErrorHandling = <T extends (...args: any[]) => any>(
  fn: T,
  context?: ErrorContext
): T => {
  return ((...args: any[]) => {
    try {
      const result = fn(...args);
      if (result instanceof Promise) {
        return result.catch((error) => {
          handleError(error, context);
          throw error;
        });
      }
      return result;
    } catch (error) {
      handleError(error as Error, context);
      throw error;
    }
  }) as T;
};
