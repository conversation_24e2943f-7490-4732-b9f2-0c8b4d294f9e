import { useState } from "react";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { User, Lock, Mail, LogOut, Settings, Database, Shield } from "lucide-react";
import { useAuth } from "@/components/auth/AuthProvider";

export const AdminSettings = () => {
  const [email, setEmail] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);
  const [showDatabaseDialog, setShowDatabaseDialog] = useState(false);
  const [showEmailDialog, setShowEmailDialog] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { signOut } = useAuth();

  // Role permissions configuration
  const [rolePermissions, setRolePermissions] = useState({
    admin: {
      canManageUsers: true,
      canManageProjects: true,
      canViewReports: true,
      canManageSystem: true,
      canManageDatabase: true,
      canManageAPIKeys: true
    },
    manager: {
      canManageUsers: false,
      canManageProjects: true,
      canViewReports: true,
      canManageSystem: false,
      canManageDatabase: false,
      canManageAPIKeys: false
    },
    staff: {
      canManageUsers: false,
      canManageProjects: false,
      canViewReports: false,
      canManageSystem: false,
      canManageDatabase: false,
      canManageAPIKeys: false
    },
    accountant: {
      canManageUsers: false,
      canManageProjects: false,
      canViewReports: true,
      canManageSystem: false,
      canManageDatabase: false,
      canManageAPIKeys: false
    }
  });

  const handleUpdateEmail = async () => {
    try {
      // Implement email update logic here
      toast({
        title: "Email Updated",
        description: "Your email has been updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update email",
        variant: "destructive",
      });
    }
  };

  const handleUpdatePassword = async () => {
    if (newPassword !== confirmPassword) {
      toast({
        title: "Error",
        description: "New passwords do not match",
        variant: "destructive",
      });
      return;
    }

    try {
      // Implement password update logic here
      toast({
        title: "Password Updated",
        description: "Your password has been updated successfully",
      });
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update password",
        variant: "destructive",
      });
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      navigate("/login");
      toast({
        title: "Logged Out",
        description: "You have been logged out successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to log out",
        variant: "destructive",
      });
    }
  };

  const handlePermissionChange = (role: string, permission: string, value: boolean) => {
    setRolePermissions(prev => ({
      ...prev,
      [role]: {
        ...prev[role as keyof typeof prev],
        [permission]: value
      }
    }));
  };

  const savePermissions = () => {
    // In a real implementation, this would save to the database
    toast({
      title: "Permissions Updated",
      description: "Role permissions have been updated successfully",
    });
    setShowPermissionsDialog(false);
  };

  const handleDatabaseConfiguration = () => {
    toast({
      title: "Database Configuration",
      description: "Database settings would be configured here",
    });
    setShowDatabaseDialog(false);
  };

  const handleEmailConfiguration = () => {
    toast({
      title: "Email Configuration",
      description: "Email settings would be configured here",
    });
    setShowEmailDialog(false);
  };

  return (
    <div className="p-6" data-aos="fade-in">
      <div className="mb-6">
        <h1 className="text-3xl font-bold tracking-tight">Admin Settings</h1>
      </div>
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* System Configuration */}
        <div className="col-span-full">
          <h2 className="text-xl font-semibold mb-4">System Configuration</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Database Settings</h3>
              <p className="text-sm text-muted-foreground mb-4">Manage database connections and backups</p>
              <Button
                onClick={() => setShowDatabaseDialog(true)}
                className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90"
              >
                <Database className="h-4 w-4 mr-2" />
                Configure
              </Button>
            </div>
            
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">API Keys</h3>
              <p className="text-sm text-muted-foreground mb-4">Manage third-party integrations</p>
              <Button
                onClick={() => navigate('/dashboard/admin/api-keys')}
                className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90"
              >
                Manage Keys
              </Button>
            </div>
            
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Email Settings</h3>
              <p className="text-sm text-muted-foreground mb-4">Configure SMTP and notifications</p>
              <Button
                onClick={() => setShowEmailDialog(true)}
                className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90"
              >
                <Mail className="h-4 w-4 mr-2" />
                Setup Email
              </Button>
            </div>
          </div>
        </div>

        {/* User Management */}
        <div className="col-span-full">
          <h2 className="text-xl font-semibold mb-4">User Management</h2>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Role Permissions</h3>
              <p className="text-sm text-muted-foreground mb-4">Define what each role can access</p>
              <Button
                onClick={() => setShowPermissionsDialog(true)}
                className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90"
              >
                <Shield className="h-4 w-4 mr-2" />
                Edit Permissions
              </Button>
            </div>
            
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Security Policies</h3>
              <p className="text-sm text-muted-foreground mb-4">Password requirements and session management</p>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90">
                Update Policies
              </button>
            </div>
          </div>
        </div>

        {/* System Monitoring */}
        <div className="col-span-full">
          <h2 className="text-xl font-semibold mb-4">System Monitoring</h2>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Audit Logs</h3>
              <p className="text-sm text-muted-foreground mb-4">View system activity and user actions</p>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90">
                View Logs
              </button>
            </div>
            
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Performance Metrics</h3>
              <p className="text-sm text-muted-foreground mb-4">Monitor system performance and uptime</p>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90">
                View Metrics
              </button>
            </div>
            
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Backup & Recovery</h3>
              <p className="text-sm text-muted-foreground mb-4">Manage data backups and disaster recovery</p>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90">
                Configure
              </button>
            </div>
          </div>
        </div>

        {/* Integration Settings */}
        <div className="col-span-full">
          <h2 className="text-xl font-semibold mb-4">Integration Settings</h2>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">External APIs</h3>
              <p className="text-sm text-muted-foreground mb-4">Manage connections to external services</p>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90">
                Manage APIs
              </button>
            </div>
            
            <div className="bg-card rounded-lg p-6 border">
              <h3 className="font-medium mb-2">Webhooks</h3>
              <p className="text-sm text-muted-foreground mb-4">Configure incoming and outgoing webhooks</p>
              <button className="bg-primary text-primary-foreground px-4 py-2 rounded-md text-sm hover:bg-primary/90">
                Setup Webhooks
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Role Permissions Dialog */}
      <Dialog open={showPermissionsDialog} onOpenChange={setShowPermissionsDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Role Permissions</DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {Object.entries(rolePermissions).map(([role, permissions]) => (
              <div key={role} className="border rounded-lg p-4">
                <h3 className="font-semibold text-lg mb-3 capitalize">{role}</h3>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(permissions).map(([permission, value]) => (
                    <div key={permission} className="flex items-center space-x-2">
                      <Checkbox
                        id={`${role}-${permission}`}
                        checked={value}
                        onCheckedChange={(checked) =>
                          handlePermissionChange(role, permission, checked as boolean)
                        }
                      />
                      <Label htmlFor={`${role}-${permission}`} className="text-sm">
                        {permission.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            ))}
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowPermissionsDialog(false)}>
                Cancel
              </Button>
              <Button onClick={savePermissions}>
                Save Permissions
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Database Configuration Dialog */}
      <Dialog open={showDatabaseDialog} onOpenChange={setShowDatabaseDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Database Configuration</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="db-host">Database Host</Label>
              <Input id="db-host" placeholder="localhost" />
            </div>
            <div>
              <Label htmlFor="db-port">Port</Label>
              <Input id="db-port" placeholder="5432" />
            </div>
            <div>
              <Label htmlFor="db-name">Database Name</Label>
              <Input id="db-name" placeholder="aiworkboard" />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowDatabaseDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleDatabaseConfiguration}>
                Save Configuration
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Email Configuration Dialog */}
      <Dialog open={showEmailDialog} onOpenChange={setShowEmailDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Email Configuration</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="smtp-host">SMTP Host</Label>
              <Input id="smtp-host" placeholder="smtp.gmail.com" />
            </div>
            <div>
              <Label htmlFor="smtp-port">SMTP Port</Label>
              <Input id="smtp-port" placeholder="587" />
            </div>
            <div>
              <Label htmlFor="smtp-user">Username</Label>
              <Input id="smtp-user" placeholder="<EMAIL>" />
            </div>
            <div>
              <Label htmlFor="smtp-pass">Password</Label>
              <Input id="smtp-pass" type="password" placeholder="Your app password" />
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowEmailDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleEmailConfiguration}>
                Save Configuration
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminSettings;
