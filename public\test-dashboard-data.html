<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Data Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .data-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .data-section h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .data-list {
            font-family: monospace;
            font-size: 14px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Dashboard Data Test</h1>
        <p>Test if the dashboard can now fetch and display data correctly after fixing the table references.</p>
        
        <div id="status"></div>
        
        <button id="testBtn" class="button" onclick="testDashboardData()">
            🧪 Test Dashboard Data
        </button>
        
        <button id="dashboardBtn" class="button" onclick="openDashboard()" disabled>
            📊 Open Dashboard
        </button>
        
        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        async function testDashboardData() {
            const testBtn = document.getElementById('testBtn');
            const dashboardBtn = document.getElementById('dashboardBtn');
            const resultsDiv = document.getElementById('results');
            
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            
            try {
                showStatus('🧪 Testing dashboard data access...', 'info');
                
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('Please log in first');
                
                let resultsHtml = '';
                let totalRecords = 0;
                let workingTables = 0;
                
                // Test each table
                const tables = [
                    { name: 'invoices', columns: 'subtotal, balance_amount, total_amount, status, created_at' },
                    { name: 'expense_reports', columns: 'amount, category, status, created_at' },
                    { name: 'time_logs', columns: 'clock_in, clock_out, total_hours, created_at' },
                    { name: 'notifications', columns: 'title, message, is_read, created_at' },
                    { name: 'reports', columns: 'title, report_type, status, created_at' },
                    { name: 'projects', columns: 'name, status, progress_percentage, created_at' },
                    { name: 'tasks', columns: 'title, status, priority, created_at' }
                ];
                
                for (const table of tables) {
                    try {
                        const { data, error, count } = await supabase
                            .from(table.name)
                            .select(table.columns, { count: 'exact' })
                            .limit(5);
                        
                        if (error) {
                            resultsHtml += `
                                <div class="data-section" style="border-left-color: #dc3545;">
                                    <h4>${table.name} ❌</h4>
                                    <div class="data-list" style="color: #dc3545;">Error: ${error.message}</div>
                                </div>
                            `;
                        } else {
                            workingTables++;
                            const recordCount = data?.length || 0;
                            totalRecords += recordCount;
                            
                            resultsHtml += `
                                <div class="data-section" style="border-left-color: #28a745;">
                                    <h4>${table.name} ✅</h4>
                                    <div class="data-list">
                                        Records found: ${recordCount}<br>
                                        Sample data: ${recordCount > 0 ? 'Available' : 'Empty table'}
                                        ${recordCount > 0 ? '<br>Columns: ' + Object.keys(data[0]).join(', ') : ''}
                                    </div>
                                </div>
                            `;
                        }
                    } catch (e) {
                        resultsHtml += `
                            <div class="data-section" style="border-left-color: #dc3545;">
                                <h4>${table.name} ❌</h4>
                                <div class="data-list" style="color: #dc3545;">Exception: ${e.message}</div>
                            </div>
                        `;
                    }
                }
                
                // Add summary
                resultsHtml = `
                    <div class="data-section" style="border-left-color: #007bff;">
                        <h4>📊 Test Summary</h4>
                        <div class="data-list">
                            Working Tables: ${workingTables}/${tables.length}<br>
                            Total Records: ${totalRecords}<br>
                            Dashboard Status: ${workingTables >= 5 ? '✅ Ready' : '⚠️ Partial'}
                        </div>
                    </div>
                ` + resultsHtml;
                
                resultsDiv.innerHTML = resultsHtml;
                
                if (workingTables >= 5) {
                    showStatus(`🎉 Dashboard data test successful! ${workingTables}/${tables.length} tables working with ${totalRecords} total records.`, 'success');
                    dashboardBtn.disabled = false;
                } else {
                    showStatus(`⚠️ Partial success: ${workingTables}/${tables.length} tables working. Dashboard should be partially functional.`, 'warning');
                    dashboardBtn.disabled = false;
                }
                
            } catch (error) {
                showStatus('❌ Dashboard data test failed: ' + error.message, 'error');
                console.error('Dashboard test error:', error);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 Test Dashboard Data';
            }
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Make functions global
        window.testDashboardData = testDashboardData;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
