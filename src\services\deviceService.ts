import { DeviceInfo, DeviceService } from '@/types/timeTracking';

class DeviceServiceImpl implements DeviceService {
  getDeviceInfo(): DeviceInfo {
    const userAgent = navigator.userAgent;
    const platform = this.detectPlatform();
    const deviceDetails = this.getDetailedDeviceInfo(userAgent);

    return {
      type: this.detectDeviceType(userAgent),
      browser: this.getBrowserInfo(),
      userAgent,
      platform,
      brand: deviceDetails.brand,
      model: deviceDetails.model,
      os: deviceDetails.os,
    };
  }

  detectDeviceType(userAgent: string): DeviceInfo['type'] {
    if (!userAgent) return 'unknown';

    // Mobile detection
    if (/iPhone|iPad|iPod|Android|BlackBerry|Windows Phone|Mobile/i.test(userAgent)) {
      if (/iPad/i.test(userAgent)) {
        return 'tablet';
      }
      return 'mobile';
    }
    
    // Tablet detection
    if (/Tablet|PlayBook/i.test(userAgent)) {
      return 'tablet';
    }
    
    // Desktop detection
    return 'desktop';
  }

  getBrowserInfo(): string {
    const userAgent = navigator.userAgent;
    
    // Chrome
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      const match = userAgent.match(/Chrome\/(\d+)/);
      return match ? `Chrome ${match[1]}` : 'Chrome';
    }
    
    // Edge
    if (userAgent.includes('Edg')) {
      const match = userAgent.match(/Edg\/(\d+)/);
      return match ? `Edge ${match[1]}` : 'Edge';
    }
    
    // Firefox
    if (userAgent.includes('Firefox')) {
      const match = userAgent.match(/Firefox\/(\d+)/);
      return match ? `Firefox ${match[1]}` : 'Firefox';
    }
    
    // Safari
    if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      const match = userAgent.match(/Version\/(\d+)/);
      return match ? `Safari ${match[1]}` : 'Safari';
    }
    
    // Opera
    if (userAgent.includes('Opera') || userAgent.includes('OPR')) {
      const match = userAgent.match(/(?:Opera|OPR)\/(\d+)/);
      return match ? `Opera ${match[1]}` : 'Opera';
    }
    
    return 'Unknown Browser';
  }

  detectPlatform(): string {
    const userAgent = navigator.userAgent;
    const platform = navigator.platform;

    // Windows
    if (/Win/i.test(platform) || /Windows/i.test(userAgent)) {
      if (/Windows NT 10/i.test(userAgent)) return 'Windows 10/11';
      if (/Windows NT 6.3/i.test(userAgent)) return 'Windows 8.1';
      if (/Windows NT 6.2/i.test(userAgent)) return 'Windows 8';
      if (/Windows NT 6.1/i.test(userAgent)) return 'Windows 7';
      return 'Windows';
    }

    // macOS
    if (/Mac/i.test(platform) || /Macintosh/i.test(userAgent)) {
      return 'macOS';
    }

    // iOS
    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      const match = userAgent.match(/OS (\d+_\d+)/);
      return match ? `iOS ${match[1].replace('_', '.')}` : 'iOS';
    }

    // Android
    if (/Android/i.test(userAgent)) {
      const match = userAgent.match(/Android (\d+\.?\d*)/);
      return match ? `Android ${match[1]}` : 'Android';
    }

    // Linux
    if (/Linux/i.test(platform) || /Linux/i.test(userAgent)) {
      return 'Linux';
    }

    return platform || 'Unknown Platform';
  }

  getDetailedDeviceInfo(userAgent: string): { brand: string; model: string; os: string } {
    let brand = 'Unknown';
    let model = 'Unknown';
    let os = 'Unknown';

    // iOS Devices
    if (/iPhone/i.test(userAgent)) {
      brand = 'Apple';
      model = 'iPhone';
      const osMatch = userAgent.match(/OS (\d+_\d+)/);
      os = osMatch ? `iOS ${osMatch[1].replace('_', '.')}` : 'iOS';
    } else if (/iPad/i.test(userAgent)) {
      brand = 'Apple';
      model = 'iPad';
      const osMatch = userAgent.match(/OS (\d+_\d+)/);
      os = osMatch ? `iOS ${osMatch[1].replace('_', '.')}` : 'iOS';
    } else if (/iPod/i.test(userAgent)) {
      brand = 'Apple';
      model = 'iPod';
      const osMatch = userAgent.match(/OS (\d+_\d+)/);
      os = osMatch ? `iOS ${osMatch[1].replace('_', '.')}` : 'iOS';
    }

    // Android Devices
    else if (/Android/i.test(userAgent)) {
      const osMatch = userAgent.match(/Android (\d+\.?\d*)/);
      os = osMatch ? `Android ${osMatch[1]}` : 'Android';

      // Samsung
      if (/Samsung|SM-|Galaxy/i.test(userAgent)) {
        brand = 'Samsung';
        const modelMatch = userAgent.match(/SM-([A-Z0-9]+)|Galaxy ([A-Z0-9\s]+)/i);
        model = modelMatch ? (modelMatch[1] || modelMatch[2]) : 'Galaxy';
      }
      // Google Pixel
      else if (/Pixel/i.test(userAgent)) {
        brand = 'Google';
        const modelMatch = userAgent.match(/Pixel ([A-Z0-9\s]+)/i);
        model = modelMatch ? `Pixel ${modelMatch[1]}` : 'Pixel';
      }
      // Huawei
      else if (/Huawei|Honor/i.test(userAgent)) {
        brand = 'Huawei';
        const modelMatch = userAgent.match(/([A-Z0-9-]+)\s+Build/i);
        model = modelMatch ? modelMatch[1] : 'Android Device';
      }
      // OnePlus
      else if (/OnePlus/i.test(userAgent)) {
        brand = 'OnePlus';
        const modelMatch = userAgent.match(/OnePlus ([A-Z0-9\s]+)/i);
        model = modelMatch ? modelMatch[1] : 'OnePlus';
      }
      // LG
      else if (/LG/i.test(userAgent)) {
        brand = 'LG';
        const modelMatch = userAgent.match(/LG-([A-Z0-9]+)/i);
        model = modelMatch ? modelMatch[1] : 'LG Device';
      }
      // Sony
      else if (/Sony/i.test(userAgent)) {
        brand = 'Sony';
        const modelMatch = userAgent.match(/([A-Z0-9-]+)\s+Build/i);
        model = modelMatch ? modelMatch[1] : 'Xperia';
      }
      // Generic Android
      else {
        brand = 'Android';
        const modelMatch = userAgent.match(/([A-Z0-9-]+)\s+Build/i);
        model = modelMatch ? modelMatch[1] : 'Android Device';
      }
    }

    // Desktop/Laptop
    else if (/Windows/i.test(userAgent)) {
      brand = 'PC';
      model = 'Windows Computer';
      if (/Windows NT 10/i.test(userAgent)) os = 'Windows 10/11';
      else if (/Windows NT 6.3/i.test(userAgent)) os = 'Windows 8.1';
      else if (/Windows NT 6.2/i.test(userAgent)) os = 'Windows 8';
      else if (/Windows NT 6.1/i.test(userAgent)) os = 'Windows 7';
      else os = 'Windows';
    } else if (/Mac/i.test(userAgent)) {
      brand = 'Apple';
      model = 'Mac';
      os = 'macOS';
    } else if (/Linux/i.test(userAgent)) {
      brand = 'PC';
      model = 'Linux Computer';
      os = 'Linux';
    }

    return { brand, model, os };
  }

  async getIPAddress(): Promise<string> {
    try {
      // Try multiple IP detection services
      const services = [
        'https://api.ipify.org?format=json',
        'https://ipapi.co/ip/',
        'https://api.my-ip.io/ip',
      ];

      for (const service of services) {
        try {
          const response = await fetch(service, { timeout: 5000 } as any);
          
          if (service.includes('ipify')) {
            const data = await response.json();
            return data.ip;
          } else {
            return await response.text();
          }
        } catch (error) {
          continue; // Try next service
        }
      }
      
      throw new Error('All IP services failed');
    } catch (error) {
      console.warn('Failed to get IP address:', error);
      return 'Unknown';
    }
  }

  // Get screen information
  getScreenInfo() {
    return {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth,
      orientation: screen.orientation?.type || 'unknown',
    };
  }

  // Get timezone information
  getTimezoneInfo() {
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const offset = new Date().getTimezoneOffset();
    
    return {
      timezone,
      offset: -offset, // Convert to positive for UTC+
      offsetString: this.formatTimezoneOffset(-offset),
    };
  }

  private formatTimezoneOffset(offset: number): string {
    const hours = Math.floor(Math.abs(offset) / 60);
    const minutes = Math.abs(offset) % 60;
    const sign = offset >= 0 ? '+' : '-';
    
    return `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  // Get network information (if available)
  getNetworkInfo() {
    const connection = (navigator as any).connection || 
                     (navigator as any).mozConnection || 
                     (navigator as any).webkitConnection;
    
    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      };
    }
    
    return null;
  }

  // Check if device supports various features
  getCapabilities() {
    return {
      geolocation: 'geolocation' in navigator,
      camera: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      notifications: 'Notification' in window,

      webRTC: 'RTCPeerConnection' in window,
      webGL: this.hasWebGL(),
      localStorage: this.hasLocalStorage(),
      sessionStorage: this.hasSessionStorage(),
      indexedDB: 'indexedDB' in window,
      webWorkers: 'Worker' in window,
    };
  }

  private hasWebGL(): boolean {
    try {
      const canvas = document.createElement('canvas');
      return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
    } catch {
      return false;
    }
  }

  private hasLocalStorage(): boolean {
    try {
      const test = 'test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  private hasSessionStorage(): boolean {
    try {
      const test = 'test';
      sessionStorage.setItem(test, test);
      sessionStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  // Get comprehensive device fingerprint
  async getDeviceFingerprint(): Promise<string> {
    const deviceInfo = this.getDeviceInfo();
    const screenInfo = this.getScreenInfo();
    const timezoneInfo = this.getTimezoneInfo();
    const capabilities = this.getCapabilities();
    
    const fingerprint = {
      userAgent: deviceInfo.userAgent,
      platform: deviceInfo.platform,
      screen: `${screenInfo.width}x${screenInfo.height}`,
      timezone: timezoneInfo.timezone,
      language: navigator.language,
      languages: navigator.languages?.join(','),
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      capabilities: Object.entries(capabilities)
        .filter(([, value]) => value)
        .map(([key]) => key)
        .join(','),
    };
    
    // Create a simple hash of the fingerprint
    const fingerprintString = JSON.stringify(fingerprint);
    let hash = 0;
    
    for (let i = 0; i < fingerprintString.length; i++) {
      const char = fingerprintString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }
}

export const deviceService = new DeviceServiceImpl();
