import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { SystemLogsService } from './system-logs-service';
import { UserActivitiesService } from './user-activities-service';

/**
 * Voice Recognition Service
 * Advanced speech-to-text functionality with real-time voice input processing
 * Similar to ChatGPT's voice recognition capabilities
 */

export interface VoiceRecognitionConfig {
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  sensitivity: number;
  noiseReduction: boolean;
  echoCancellation: boolean;
  autoGainControl: boolean;
}

export interface VoiceRecognitionResult {
  transcript: string;
  confidence: number;
  isFinal: boolean;
  alternatives: Array<{
    transcript: string;
    confidence: number;
  }>;
  duration: number;
  language: string;
  timestamp: string;
}

export interface VoiceSession {
  id: string;
  sessionToken: string;
  status: 'active' | 'paused' | 'ended' | 'error';
  language: string;
  startTime: string;
  totalInteractions: number;
  conversationHistory: any[];
}

export class VoiceRecognitionService {
  private static recognition: SpeechRecognition | null = null;
  private static isListening: boolean = false;
  private static currentSession: VoiceSession | null = null;
  private static config: VoiceRecognitionConfig = {
    language: 'en-GB', // UK English as default
    continuous: true,
    interimResults: true,
    maxAlternatives: 3,
    sensitivity: 0.8,
    noiseReduction: true,
    echoCancellation: true,
    autoGainControl: true
  };
  private static callbacks: {
    onResult?: (result: VoiceRecognitionResult) => void;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: string) => void;
    onSpeechStart?: () => void;
    onSpeechEnd?: () => void;
  } = {};

  // Initialize voice recognition service
  static async initialize(): Promise<boolean> {
    try {
      console.log('🎤 Initializing Voice Recognition Service...');

      // Check browser support
      if (!this.isBrowserSupported()) {
        throw new Error('Speech recognition not supported in this browser');
      }

      // Initialize speech recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();

      // Configure recognition
      this.configureRecognition();

      // Set up event listeners
      this.setupEventListeners();

      // Load user preferences
      await this.loadUserPreferences();

      console.log('✅ Voice Recognition Service initialized successfully');
      
      await SystemLogsService.info('voice', 'Voice recognition service initialized', 'Voice recognition system ready');
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Voice Recognition Service:', error);
      await SystemLogsService.error('voice', 'Voice recognition initialization failed', error.toString());
      return false;
    }
  }

  // Check if browser supports speech recognition
  static isBrowserSupported(): boolean {
    return !!(window.SpeechRecognition || window.webkitSpeechRecognition);
  }

  // Configure speech recognition settings
  private static configureRecognition(): void {
    if (!this.recognition) return;

    this.recognition.continuous = this.config.continuous;
    this.recognition.interimResults = this.config.interimResults;
    this.recognition.lang = this.config.language;
    this.recognition.maxAlternatives = this.config.maxAlternatives;
  }

  // Set up event listeners for speech recognition
  private static setupEventListeners(): void {
    if (!this.recognition) return;

    this.recognition.onstart = () => {
      console.log('🎤 Voice recognition started');
      this.isListening = true;
      this.callbacks.onStart?.();
    };

    this.recognition.onend = () => {
      console.log('🎤 Voice recognition ended');
      this.isListening = false;
      this.callbacks.onEnd?.();
    };

    this.recognition.onerror = (event) => {
      console.error('🎤 Voice recognition error:', event.error);
      this.isListening = false;
      this.callbacks.onError?.(event.error);
      
      SystemLogsService.error('voice', 'Voice recognition error', event.error, {
        error_type: event.error,
        session_id: this.currentSession?.id
      });
    };

    this.recognition.onspeechstart = () => {
      console.log('🎤 Speech detected');
      this.callbacks.onSpeechStart?.();
    };

    this.recognition.onspeechend = () => {
      console.log('🎤 Speech ended');
      this.callbacks.onSpeechEnd?.();
    };

    this.recognition.onresult = (event) => {
      this.handleRecognitionResult(event);
    };
  }

  // Handle speech recognition results
  private static handleRecognitionResult(event: SpeechRecognitionEvent): void {
    const results: VoiceRecognitionResult[] = [];

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i];
      const alternatives = [];

      for (let j = 0; j < result.length; j++) {
        alternatives.push({
          transcript: result[j].transcript,
          confidence: result[j].confidence
        });
      }

      const recognitionResult: VoiceRecognitionResult = {
        transcript: result[0].transcript,
        confidence: result[0].confidence,
        isFinal: result.isFinal,
        alternatives,
        duration: 0, // Will be calculated
        language: this.config.language,
        timestamp: new Date().toISOString()
      };

      results.push(recognitionResult);

      // Call callback with result
      this.callbacks.onResult?.(recognitionResult);

      // Log final results
      if (result.isFinal) {
        this.logVoiceCommand(recognitionResult);
      }
    }
  }

  // Start voice recognition
  static async startListening(callbacks?: {
    onResult?: (result: VoiceRecognitionResult) => void;
    onStart?: () => void;
    onEnd?: () => void;
    onError?: (error: string) => void;
    onSpeechStart?: () => void;
    onSpeechEnd?: () => void;
  }): Promise<boolean> {
    try {
      if (!this.recognition) {
        throw new Error('Voice recognition not initialized');
      }

      if (this.isListening) {
        console.warn('Voice recognition already active');
        return true;
      }

      // Set callbacks
      this.callbacks = callbacks || {};

      // Request microphone permission
      const hasPermission = await this.requestMicrophonePermission();
      if (!hasPermission) {
        throw new Error('Microphone permission denied');
      }

      // Start or resume session
      if (!this.currentSession) {
        await this.startSession();
      }

      // Start recognition
      this.recognition.start();

      await UserActivitiesService.logActivity(
        'create',
        'Started Voice Recognition',
        'User started voice recognition session',
        'voice',
        this.currentSession?.id,
        'Voice Session'
      );

      return true;
    } catch (error) {
      console.error('Failed to start voice recognition:', error);
      this.callbacks.onError?.(error.toString());
      return false;
    }
  }

  // Stop voice recognition
  static async stopListening(): Promise<void> {
    try {
      if (this.recognition && this.isListening) {
        this.recognition.stop();
      }

      if (this.currentSession) {
        await this.endSession();
      }

      await UserActivitiesService.logActivity(
        'complete',
        'Stopped Voice Recognition',
        'User stopped voice recognition session',
        'voice',
        this.currentSession?.id,
        'Voice Session'
      );

      console.log('🎤 Voice recognition stopped');
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
    }
  }

  // Pause voice recognition
  static pauseListening(): void {
    if (this.recognition && this.isListening) {
      this.recognition.stop();
      console.log('🎤 Voice recognition paused');
    }
  }

  // Resume voice recognition
  static async resumeListening(): Promise<boolean> {
    try {
      if (this.recognition && !this.isListening) {
        this.recognition.start();
        console.log('🎤 Voice recognition resumed');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error resuming voice recognition:', error);
      return false;
    }
  }

  // Request microphone permission
  private static async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: this.config.echoCancellation,
          noiseSuppression: this.config.noiseReduction,
          autoGainControl: this.config.autoGainControl
        } 
      });
      
      // Stop the stream immediately as we just needed permission
      stream.getTracks().forEach(track => track.stop());
      
      return true;
    } catch (error) {
      console.error('Microphone permission denied:', error);
      return false;
    }
  }

  // Start a new voice session
  private static async startSession(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      const sessionToken = crypto.randomUUID();

      const { data: session, error } = await supabase
        .from('voice_sessions')
        .insert({
          user_id: currentUser?.user_id,
          session_token: sessionToken,
          status: 'active',
          language: this.config.language,
          voice_settings: this.config,
          context: {
            user_agent: navigator.userAgent,
            timestamp: new Date().toISOString()
          }
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      this.currentSession = {
        id: session.id,
        sessionToken: session.session_token,
        status: session.status,
        language: session.language,
        startTime: session.started_at,
        totalInteractions: 0,
        conversationHistory: []
      };

      console.log('🎤 Voice session started:', this.currentSession.id);
    } catch (error) {
      console.error('Error starting voice session:', error);
      throw error;
    }
  }

  // End current voice session
  private static async endSession(): Promise<void> {
    try {
      if (!this.currentSession) return;

      await supabase
        .from('voice_sessions')
        .update({
          status: 'ended',
          ended_at: new Date().toISOString(),
          total_interactions: this.currentSession.totalInteractions,
          conversation_history: this.currentSession.conversationHistory
        })
        .eq('id', this.currentSession.id);

      console.log('🎤 Voice session ended:', this.currentSession.id);
      this.currentSession = null;
    } catch (error) {
      console.error('Error ending voice session:', error);
    }
  }

  // Log voice command to database
  private static async logVoiceCommand(result: VoiceRecognitionResult): Promise<void> {
    try {
      if (!this.currentSession) return;

      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();

      await supabase.from('voice_commands').insert({
        session_id: this.currentSession.id,
        user_id: currentUser?.user_id,
        command_text: result.transcript,
        processed_text: result.transcript.trim().toLowerCase(),
        confidence_score: result.confidence,
        language_detected: result.language,
        status: 'completed',
        audio_duration_seconds: result.duration,
        context: {
          alternatives: result.alternatives,
          timestamp: result.timestamp,
          is_final: result.isFinal
        },
        metadata: {
          browser: navigator.userAgent,
          language_config: this.config.language
        }
      });

      // Update session interaction count
      this.currentSession.totalInteractions++;
      this.currentSession.conversationHistory.push({
        type: 'voice_input',
        content: result.transcript,
        confidence: result.confidence,
        timestamp: result.timestamp
      });

    } catch (error) {
      console.error('Error logging voice command:', error);
    }
  }

  // Load user voice preferences
  private static async loadUserPreferences(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return;

      const { data: preferences } = await supabase
        .from('voice_user_preferences')
        .select('*')
        .eq('user_id', currentUser.user_id)
        .single();

      if (preferences) {
        this.config = {
          ...this.config,
          language: preferences.preferred_language || this.config.language,
          continuous: true,
          interimResults: true,
          maxAlternatives: 3,
          sensitivity: 0.8,
          noiseReduction: true,
          echoCancellation: true,
          autoGainControl: true
        };

        this.configureRecognition();
        console.log('🎤 User voice preferences loaded');
      }
    } catch (error) {
      console.warn('Could not load user voice preferences:', error);
    }
  }

  // Update voice configuration
  static updateConfig(newConfig: Partial<VoiceRecognitionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.configureRecognition();
    console.log('🎤 Voice configuration updated');
  }

  // Get current configuration
  static getConfig(): VoiceRecognitionConfig {
    return { ...this.config };
  }

  // Get current session
  static getCurrentSession(): VoiceSession | null {
    return this.currentSession;
  }

  // Check if currently listening
  static isCurrentlyListening(): boolean {
    return this.isListening;
  }

  // Get supported languages with Nigerian languages
  static getSupportedLanguages(): string[] {
    return [
      'en-GB', 'en-US', 'en-AU', 'en-CA', 'en-IN',
      'ig-NG', 'ha-NG', 'yo-NG', // Nigerian languages: Igbo, Hausa, Yoruba
      'es-ES', 'es-MX', 'fr-FR', 'fr-CA', 'de-DE',
      'it-IT', 'pt-BR', 'pt-PT', 'ru-RU', 'ja-JP',
      'ko-KR', 'zh-CN', 'zh-TW', 'ar-SA', 'hi-IN'
    ];
  }

  // Get Nigerian languages specifically
  static getNigerianLanguages(): Array<{code: string, name: string, nativeName: string}> {
    return [
      { code: 'en-GB', name: 'English (UK)', nativeName: 'English (United Kingdom)' },
      { code: 'ig-NG', name: 'Igbo', nativeName: 'Asụsụ Igbo' },
      { code: 'ha-NG', name: 'Hausa', nativeName: 'Harshen Hausa' },
      { code: 'yo-NG', name: 'Yoruba', nativeName: 'Èdè Yorùbá' }
    ];
  }

  // Get language display name
  static getLanguageDisplayName(code: string): string {
    const languageNames: Record<string, string> = {
      'en-GB': 'English (UK)',
      'en-US': 'English (US)',
      'ig-NG': 'Igbo (Asụsụ Igbo)',
      'ha-NG': 'Hausa (Harshen Hausa)',
      'yo-NG': 'Yoruba (Èdè Yorùbá)',
      'fr-FR': 'French',
      'es-ES': 'Spanish',
      'de-DE': 'German',
      'pt-BR': 'Portuguese',
      'ar-SA': 'Arabic',
      'hi-IN': 'Hindi',
      'zh-CN': 'Chinese (Simplified)',
      'ja-JP': 'Japanese',
      'ko-KR': 'Korean',
      'ru-RU': 'Russian'
    };

    return languageNames[code] || code;
  }

  // Test microphone and speech recognition
  static async testVoiceRecognition(): Promise<{
    microphoneAccess: boolean;
    speechRecognition: boolean;
    error?: string;
  }> {
    try {
      // Test microphone access
      const microphoneAccess = await this.requestMicrophonePermission();
      
      // Test speech recognition
      const speechRecognition = this.isBrowserSupported();

      return {
        microphoneAccess,
        speechRecognition,
        error: !microphoneAccess ? 'Microphone access denied' : 
               !speechRecognition ? 'Speech recognition not supported' : undefined
      };
    } catch (error) {
      return {
        microphoneAccess: false,
        speechRecognition: false,
        error: error.toString()
      };
    }
  }

  // Get voice recognition statistics
  static async getVoiceStats(days: number = 7): Promise<any> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return null;

      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      const { data: commands } = await supabase
        .from('voice_commands')
        .select('*')
        .eq('user_id', currentUser.user_id)
        .gte('created_at', dateFrom.toISOString());

      const { data: sessions } = await supabase
        .from('voice_sessions')
        .select('*')
        .eq('user_id', currentUser.user_id)
        .gte('started_at', dateFrom.toISOString());

      return {
        totalCommands: commands?.length || 0,
        totalSessions: sessions?.length || 0,
        averageConfidence: commands?.reduce((sum, cmd) => sum + (cmd.confidence_score || 0), 0) / (commands?.length || 1),
        successfulCommands: commands?.filter(cmd => cmd.status === 'completed').length || 0,
        totalDuration: sessions?.reduce((sum, session) => {
          if (session.started_at && session.ended_at) {
            return sum + (new Date(session.ended_at).getTime() - new Date(session.started_at).getTime());
          }
          return sum;
        }, 0) || 0
      };
    } catch (error) {
      console.error('Error getting voice stats:', error);
      return null;
    }
  }

  // Clean up resources
  static cleanup(): void {
    if (this.recognition) {
      this.recognition.abort();
      this.recognition = null;
    }
    this.isListening = false;
    this.currentSession = null;
    this.callbacks = {};
    console.log('🎤 Voice recognition service cleaned up');
  }
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}

export default VoiceRecognitionService;
