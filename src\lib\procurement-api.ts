import { supabase } from '@/integrations/supabase/client';

export interface ProcurementRequest {
  id: string;
  request_number: string;
  title: string;
  description?: string;
  justification: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: string;
  department_id?: string;
  project_id?: string;
  requested_by: string;
  estimated_total_cost: number;
  actual_total_cost?: number;
  currency: string;
  required_by_date?: string;
  status: 'pending' | 'manager_review' | 'approved' | 'rejected' | 'admin_processing' | 'accountant_review' | 'invoice_generated' | 'procured' | 'cancelled';
  approval_status: 'pending' | 'approved' | 'rejected';
  procurement_status: 'not_started' | 'in_progress' | 'completed' | 'cancelled';
  notes?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface ProcurementItem {
  id: string;
  procurement_request_id: string;
  item_name: string;
  description?: string;
  category?: string;
  brand_preference?: string;
  model_specification?: string;
  quantity: number;
  unit_of_measurement: string;
  estimated_unit_cost: number;
  estimated_total_cost: number;
  actual_unit_cost?: number;
  actual_total_cost?: number;
  vendor_name?: string;
  vendor_contact?: string;
  procurement_status: 'pending' | 'quoted' | 'ordered' | 'received' | 'cancelled';
  notes?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface ProcurementApproval {
  id: string;
  procurement_request_id: string;
  approver_id: string;
  approver_role: 'manager' | 'admin' | 'accountant' | 'staff-admin';
  approval_step: 'manager_review' | 'admin_processing' | 'accountant_review' | 'final_approval';
  status: 'pending' | 'approved' | 'rejected' | 'delegated';
  comments?: string;
  approved_amount?: number;
  conditions?: string;
  approval_date?: string;
  created_at: string;
}

export interface ProcurementInvoice {
  id: string;
  procurement_request_id: string;
  invoice_number: string;
  vendor_name: string;
  vendor_address?: string;
  vendor_contact?: string;
  vendor_email?: string;
  vendor_tax_id?: string;
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  payment_terms?: string;
  payment_method?: string;
  payment_status: 'pending' | 'paid' | 'partial' | 'overdue' | 'cancelled';
  payment_date?: string;
  due_date?: string;
  invoice_date: string;
  notes?: string;
  generated_by?: string;
  approved_by?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export class ProcurementAPI {
  // ============= PROCUREMENT REQUESTS =============
  
  static async createProcurementRequest(requestData: Partial<ProcurementRequest>): Promise<{ data: ProcurementRequest | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('procurement_requests')
        .insert({
          ...requestData,
          requested_by: user.id,
          status: 'pending',
          approval_status: 'pending',
          procurement_status: 'not_started'
        })
        .select(`
          *,
          requested_by_profile:profiles!requested_by(id, full_name, email, role),
          department:departments(id, name),
          project:projects(id, name)
        `)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async getProcurementRequests(filters?: {
    status?: string;
    requested_by?: string;
    department_id?: string;
    project_id?: string;
  }): Promise<{ data: ProcurementRequest[] | null; error: any }> {
    try {
      let query = supabase
        .from('procurement_requests')
        .select(`
          *,
          requested_by_profile:profiles!requested_by(id, full_name, email, role),
          department:departments(id, name),
          project:projects(id, name),
          procurement_items(id, item_name, quantity, estimated_total_cost, procurement_status),
          procurement_approvals(id, approver_role, status, approval_date)
        `);

      if (filters?.status) query = query.eq('status', filters.status);
      if (filters?.requested_by) query = query.eq('requested_by', filters.requested_by);
      if (filters?.department_id) query = query.eq('department_id', filters.department_id);
      if (filters?.project_id) query = query.eq('project_id', filters.project_id);

      const { data, error } = await query.order('created_at', { ascending: false });

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async getProcurementRequestById(id: string): Promise<{ data: ProcurementRequest | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_requests')
        .select(`
          *,
          requested_by_profile:profiles!requested_by(id, full_name, email, role),
          department:departments(id, name),
          project:projects(id, name),
          procurement_items(*),
          procurement_approvals(*),
          procurement_invoices(*),
          procurement_documents(*)
        `)
        .eq('id', id)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async updateProcurementRequest(id: string, updates: Partial<ProcurementRequest>): Promise<{ data: ProcurementRequest | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_requests')
        .update(updates)
        .eq('id', id)
        .select(`
          *,
          requested_by_profile:profiles!requested_by(id, full_name, email, role),
          department:departments(id, name),
          project:projects(id, name)
        `)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // ============= PROCUREMENT ITEMS =============

  static async addProcurementItem(itemData: Partial<ProcurementItem>): Promise<{ data: ProcurementItem | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_items')
        .insert(itemData)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async updateProcurementItem(id: string, updates: Partial<ProcurementItem>): Promise<{ data: ProcurementItem | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_items')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async deleteProcurementItem(id: string): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_items')
        .delete()
        .eq('id', id);

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // ============= PROCUREMENT APPROVALS =============

  static async createApproval(approvalData: Partial<ProcurementApproval>): Promise<{ data: ProcurementApproval | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('procurement_approvals')
        .insert({
          ...approvalData,
          approver_id: user.id
        })
        .select(`
          *,
          approver:profiles!approver_id(id, full_name, email, role)
        `)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async updateApproval(id: string, updates: Partial<ProcurementApproval>): Promise<{ data: ProcurementApproval | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_approvals')
        .update({
          ...updates,
          approval_date: updates.status === 'approved' || updates.status === 'rejected' ? new Date().toISOString() : undefined
        })
        .eq('id', id)
        .select(`
          *,
          approver:profiles!approver_id(id, full_name, email, role)
        `)
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // ============= PROCUREMENT INVOICES =============

  static async createInvoice(invoiceData: Partial<ProcurementInvoice>): Promise<{ data: ProcurementInvoice | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('procurement_invoices')
        .insert({
          ...invoiceData,
          generated_by: user.id,
          invoice_date: new Date().toISOString().split('T')[0]
        })
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async updateInvoice(id: string, updates: Partial<ProcurementInvoice>): Promise<{ data: ProcurementInvoice | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('procurement_invoices')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // ============= WORKFLOW METHODS =============

  static async submitForApproval(requestId: string): Promise<{ data: any; error: any }> {
    try {
      // Update request status to manager_review
      const { error: updateError } = await supabase
        .from('procurement_requests')
        .update({ status: 'manager_review' })
        .eq('id', requestId);

      if (updateError) throw updateError;

      // Create manager approval record
      const { data, error } = await this.createApproval({
        procurement_request_id: requestId,
        approver_role: 'manager',
        approval_step: 'manager_review',
        status: 'pending'
      });

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async approveRequest(requestId: string, approvalData: {
    comments?: string;
    approved_amount?: number;
    conditions?: string;
  }): Promise<{ data: any; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get user profile to determine role
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();

      if (!profile) throw new Error('User profile not found');

      // Update approval record
      const { error: approvalError } = await supabase
        .from('procurement_approvals')
        .update({
          status: 'approved',
          ...approvalData,
          approval_date: new Date().toISOString()
        })
        .eq('procurement_request_id', requestId)
        .eq('approver_id', user.id);

      if (approvalError) throw approvalError;

      // Determine next status based on role
      let nextStatus = 'approved';
      if (profile.role === 'manager') {
        nextStatus = 'admin_processing';
      }

      // Update request status
      const { data, error } = await supabase
        .from('procurement_requests')
        .update({ 
          status: nextStatus,
          approval_status: 'approved'
        })
        .eq('id', requestId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async rejectRequest(requestId: string, comments: string): Promise<{ data: any; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Update approval record
      const { error: approvalError } = await supabase
        .from('procurement_approvals')
        .update({
          status: 'rejected',
          comments,
          approval_date: new Date().toISOString()
        })
        .eq('procurement_request_id', requestId)
        .eq('approver_id', user.id);

      if (approvalError) throw approvalError;

      // Update request status
      const { data, error } = await supabase
        .from('procurement_requests')
        .update({ 
          status: 'rejected',
          approval_status: 'rejected'
        })
        .eq('id', requestId)
        .select()
        .single();

      return { data, error };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  static async markAsProcured(requestId: string): Promise<{ data: any; error: any }> {
    try {
      // Update request status
      const { error: requestError } = await supabase
        .from('procurement_requests')
        .update({ 
          status: 'procured',
          procurement_status: 'completed'
        })
        .eq('id', requestId);

      if (requestError) throw requestError;

      // Get request details for expense creation
      const { data: request } = await supabase
        .from('procurement_requests')
        .select(`
          *,
          procurement_items(*),
          procurement_invoices(*)
        `)
        .eq('id', requestId)
        .single();

      if (!request) throw new Error('Request not found');

      // Create expense entry
      const expenseData = {
        title: `Procurement: ${request.title}`,
        description: request.description || `Procured items for ${request.title}`,
        amount: request.actual_total_cost || request.estimated_total_cost,
        category: request.category || 'procurement',
        expense_date: new Date().toISOString().split('T')[0],
        vendor_name: request.procurement_invoices?.[0]?.vendor_name || 'Various Vendors',
        payment_method: request.procurement_invoices?.[0]?.payment_method || 'company_account',
        created_by: request.requested_by,
        status: 'approved',
        metadata: {
          procurement_request_id: requestId,
          procurement_items: request.procurement_items?.map(item => ({
            name: item.item_name,
            quantity: item.quantity,
            cost: item.actual_total_cost || item.estimated_total_cost
          }))
        }
      };

      const { data: expense, error: expenseError } = await supabase
        .from('expenses')
        .insert([expenseData])
        .select()
        .single();

      return { data: { request, expense }, error: expenseError };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  // ============= EXPORT METHODS =============

  static async exportProcurementData(format: 'pdf' | 'xlsx' | 'docx' | 'csv', filters?: any): Promise<{ data: any; error: any }> {
    try {
      // This would integrate with export libraries
      // For now, return the data that would be exported
      const { data: requests, error } = await this.getProcurementRequests(filters);
      
      if (error) throw error;

      return { 
        data: {
          format,
          requests,
          exportedAt: new Date().toISOString()
        }, 
        error: null 
      };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }
}
