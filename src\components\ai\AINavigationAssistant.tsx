import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { useEnhancedVoiceCommands } from '@/hooks/useEnhancedVoiceCommands';
import { aiVisualNavigation } from '@/lib/ai-visual-navigation';
import {
    Bot,
    Brain,
    Eye,
    Keyboard,
    Mic,
    MicOff,
    Monitor,
    MousePointer,
    Navigation,
    Play,
    Volume2,
    VolumeX,
    Zap
} from 'lucide-react';
import { useEffect, useState } from 'react';

export const AINavigationAssistant = () => {
  const {
    isListening,
    isProcessing,
    lastCommand,
    confidence,
    aiMode,
    setAiMode,
    startListening,
    stopListening,
    speak,
    processVoiceCommand
  } = useEnhancedVoiceCommands();

  const [isVisible, setIsVisible] = useState(false);
  const [visualMode, setVisualMode] = useState(false);
  const [autoSpeak, setAutoSpeak] = useState(true);
  const [currentElements, setCurrentElements] = useState([]);
  const [demoMode, setDemoMode] = useState(false);
  const [manualCommand, setManualCommand] = useState('');

  // Scan page elements periodically
  useEffect(() => {
    const scanInterval = setInterval(() => {
      if (visualMode) {
        const elements = aiVisualNavigation.scanPageElements();
        setCurrentElements(elements.slice(0, 10)); // Show top 10 elements
      }
    }, 2000);

    return () => clearInterval(scanInterval);
  }, [visualMode]);

  // Handle manual command execution
  const executeManualCommand = async () => {
    if (manualCommand.trim()) {
      await processVoiceCommand(manualCommand);
      setManualCommand('');
    }
  };

  // Start AI demonstration
  const startDemo = async () => {
    setDemoMode(true);
    
    await speak("Welcome to the AI Navigation Assistant demonstration. I'll show you how I can interact with this interface.");
    
    // Demo sequence
    const demoSteps = [
      "First, I can scan all interactive elements on the page",
      "I can highlight any element you want to interact with",
      "I can click buttons, fill forms, and navigate between pages",
      "Watch as I demonstrate by highlighting the navigation menu"
    ];
    
    for (const step of demoSteps) {
      await speak(step);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // Highlight navigation
    const elements = aiVisualNavigation.scanPageElements();
    const navElement = elements.find(el => el.type === 'menu' || el.selector.includes('nav'));
    
    if (navElement) {
      await aiVisualNavigation.executeNavigationTask({
        id: 'demo-highlight',
        name: 'Demo Highlight',
        description: 'Demonstrating element highlighting',
        steps: [
          {
            id: 'demo-1',
            action: 'highlight',
            target: navElement.selector,
            duration: 3000,
            description: 'Highlighting navigation menu'
          }
        ],
        context: 'demo',
        expectedOutcome: 'Navigation highlighted'
      });
    }
    
    await speak("This is just a small example of what I can do. Try giving me voice commands or typing them manually!");
    setDemoMode(false);
  };

  const getModeIcon = () => {
    switch (aiMode) {
      case 'navigator': return <Navigation className="h-4 w-4" />;
      case 'executor': return <Zap className="h-4 w-4" />;
      default: return <Brain className="h-4 w-4" />;
    }
  };

  const getModeColor = () => {
    switch (aiMode) {
      case 'navigator': return 'bg-blue-100 text-blue-800';
      case 'executor': return 'bg-purple-100 text-purple-800';
      default: return 'bg-green-100 text-green-800';
    }
  };

  if (!isVisible) {
    return (
      <Button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-20 z-50 rounded-full w-12 h-12 p-0 shadow-lg hover:shadow-xl transition-all duration-300"
        style={{ borderRadius: '8px' }}
        variant="default"
      >
        <Bot className="h-6 w-6" />
      </Button>
    );
  }

  return (
    <Card className="fixed bottom-4 right-20 z-50 w-96 max-h-[80vh] overflow-y-auto shadow-2xl border-2" style={{ borderRadius: '8px' }}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">AI Navigation Assistant</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
            className="h-8 w-8 p-0"
          >
            ×
          </Button>
        </div>
        <CardDescription>
          AI-powered visual navigation and task execution
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* AI Mode Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">AI Mode</Label>
          <div className="flex gap-2">
            {(['assistant', 'navigator', 'executor'] as const).map((mode) => (
              <Button
                key={mode}
                variant={aiMode === mode ? 'default' : 'outline'}
                size="sm"
                onClick={() => setAiMode(mode)}
                className="flex-1"
              >
                {getModeIcon()}
                <span className="ml-1 capitalize">{mode}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Status Display */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Badge className={getModeColor()}>
              {getModeIcon()}
              <span className="ml-1 capitalize">{aiMode} Mode</span>
            </Badge>
            
            {isListening && (
              <Badge variant="secondary" className="animate-pulse">
                <Mic className="h-3 w-3 mr-1" />
                Listening
              </Badge>
            )}
            
            {isProcessing && (
              <Badge variant="secondary" className="animate-pulse">
                <Brain className="h-3 w-3 mr-1" />
                Processing
              </Badge>
            )}
          </div>

          {lastCommand && (
            <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
              Last: "{lastCommand}" 
              {confidence > 0 && (
                <span className="ml-2">({Math.round(confidence * 100)}% confidence)</span>
              )}
            </div>
          )}
        </div>

        {/* Voice Controls */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="voice-control">Voice Control</Label>
            <div className="flex gap-2">
              <Button
                variant={isListening ? "destructive" : "default"}
                size="sm"
                onClick={isListening ? stopListening : startListening}
                disabled={isProcessing}
              >
                {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                {isListening ? 'Stop' : 'Start'}
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="auto-speak"
              checked={autoSpeak}
              onCheckedChange={setAutoSpeak}
            />
            <Label htmlFor="auto-speak" className="text-sm">
              {autoSpeak ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
              Auto-speak responses
            </Label>
          </div>
        </div>

        {/* Manual Command Input */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Manual Command</Label>
          <div className="flex gap-2">
            <Textarea
              placeholder="Type a command... (e.g., 'AI navigate to tasks')"
              value={manualCommand}
              onChange={(e) => setManualCommand(e.target.value)}
              className="min-h-[60px] text-sm"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  executeManualCommand();
                }
              }}
            />
          </div>
          <Button 
            onClick={executeManualCommand} 
            disabled={!manualCommand.trim() || isProcessing}
            size="sm"
            className="w-full"
          >
            <Keyboard className="h-4 w-4 mr-2" />
            Execute Command
          </Button>
        </div>

        {/* Visual Mode Toggle */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <Switch
              id="visual-mode"
              checked={visualMode}
              onCheckedChange={setVisualMode}
            />
            <Label htmlFor="visual-mode" className="text-sm">
              <Eye className="h-4 w-4 mr-1 inline" />
              Visual Element Detection
            </Label>
          </div>

          {visualMode && (
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">
                Detected Elements ({currentElements.length})
              </Label>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {currentElements.map((element, index) => (
                  <div
                    key={index}
                    className="text-xs p-2 bg-muted rounded cursor-pointer hover:bg-muted/80"
                    onClick={() => {
                      processVoiceCommand(`AI highlight ${element.text || element.type}`);
                    }}
                  >
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {element.type}
                      </Badge>
                      <span className="truncate">
                        {element.text || element.selector}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Demo and Quick Actions */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Quick Actions</Label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={startDemo}
              disabled={isProcessing || demoMode}
            >
              <Play className="h-4 w-4 mr-1" />
              {demoMode ? 'Running...' : 'Demo'}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => processVoiceCommand('help')}
              disabled={isProcessing}
            >
              <Monitor className="h-4 w-4 mr-1" />
              Help
            </Button>
          </div>
        </div>

        {/* Quick Command Buttons */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Quick Commands</Label>
          <div className="grid grid-cols-1 gap-1">
            {[
              'AI navigate to dashboard',
              'AI navigate to tasks',
              'AI show me how to create a task',
              'AI scan this page'
            ].map((command) => (
              <Button
                key={command}
                variant="ghost"
                size="sm"
                onClick={() => processVoiceCommand(command)}
                disabled={isProcessing}
                className="justify-start text-xs h-8"
              >
                <MousePointer className="h-3 w-3 mr-2" />
                {command}
              </Button>
            ))}
          </div>
        </div>

        {/* Status Footer */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          {isProcessing ? (
            <span className="flex items-center justify-center gap-1">
              <Brain className="h-3 w-3 animate-pulse" />
              AI is working...
            </span>
          ) : isListening ? (
            <span className="flex items-center justify-center gap-1">
              <Mic className="h-3 w-3 animate-pulse" />
              Listening for commands...
            </span>
          ) : (
            <span>Ready for commands</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
