-- WORKING Migration for LangChain & Real-time Features
-- This version handles the api_keys table issue completely
-- Run this in Supabase Dashboard SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Step 1: Handle api_keys table structure completely in one block
DO $$
DECLARE
    table_exists BOOLEAN := FALSE;
    has_provider BOOLEAN := FALSE;
    has_api_key BOOLEAN := FALSE;
    has_is_active BOOLEAN := FALSE;
    has_key_column BOOLEAN := FALSE;
BEGIN
    -- Check if api_keys table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'api_keys'
    ) INTO table_exists;
    
    IF table_exists THEN
        RAISE NOTICE 'api_keys table exists, checking structure...';
        
        -- Check existing columns
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'api_keys' AND column_name = 'provider'
        ) INTO has_provider;
        
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'api_keys' AND column_name = 'api_key'
        ) INTO has_api_key;
        
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'api_keys' AND column_name = 'is_active'
        ) INTO has_is_active;
        
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'api_keys' AND column_name = 'key'
        ) INTO has_key_column;
        
        -- Add missing columns
        IF NOT has_provider THEN
            ALTER TABLE api_keys ADD COLUMN provider TEXT;
            RAISE NOTICE 'Added provider column';
        END IF;
        
        IF NOT has_api_key THEN
            IF has_key_column THEN
                ALTER TABLE api_keys RENAME COLUMN key TO api_key;
                RAISE NOTICE 'Renamed key column to api_key';
            ELSE
                ALTER TABLE api_keys ADD COLUMN api_key TEXT;
                RAISE NOTICE 'Added api_key column';
            END IF;
        END IF;
        
        IF NOT has_is_active THEN
            ALTER TABLE api_keys ADD COLUMN is_active BOOLEAN DEFAULT true;
            RAISE NOTICE 'Added is_active column';
        END IF;
        
        -- Add unique constraint if provider column exists
        IF has_provider OR NOT has_provider THEN
            BEGIN
                ALTER TABLE api_keys ADD CONSTRAINT api_keys_provider_unique UNIQUE (provider);
                RAISE NOTICE 'Added unique constraint on provider';
            EXCEPTION
                WHEN duplicate_object THEN
                    RAISE NOTICE 'Unique constraint already exists';
                WHEN others THEN
                    RAISE NOTICE 'Could not add unique constraint: %', SQLERRM;
            END;
        END IF;
        
        -- Now insert/update the OpenAI API key
        -- First delete any existing openai entries
        DELETE FROM api_keys WHERE provider = 'openai';
        
        -- Insert the new API key
        -- REPLACE 'your-openai-api-key-here' WITH YOUR ACTUAL OPENAI API KEY
        INSERT INTO api_keys (provider, api_key, is_active) 
        VALUES ('openai', 'your-openai-api-key-here', true);
        
        RAISE NOTICE 'OpenAI API key configured successfully';
        
    ELSE
        -- Create api_keys table from scratch
        CREATE TABLE api_keys (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            provider TEXT UNIQUE NOT NULL,
            api_key TEXT NOT NULL,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Insert the OpenAI API key
        INSERT INTO api_keys (provider, api_key, is_active) 
        VALUES ('openai', 'your-openai-api-key-here', true);
        
        RAISE NOTICE 'Created api_keys table and added OpenAI key';
    END IF;
    
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Error handling api_keys table: %', SQLERRM;
END $$;

-- Step 2: Create LangChain tables
DO $$
BEGIN
    -- LangChain Conversations Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_conversations') THEN
        CREATE TABLE langchain_conversations (
            id TEXT PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT,
            messages JSONB DEFAULT '[]'::jsonb,
            context JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_langchain_conversations_user_id ON langchain_conversations(user_id);
        CREATE INDEX idx_langchain_conversations_updated_at ON langchain_conversations(updated_at);
        
        ALTER TABLE langchain_conversations ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Users can manage their own conversations" ON langchain_conversations
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
        
        RAISE NOTICE 'Created langchain_conversations table';
    END IF;

    -- LangChain Documents Table (Vector Store)
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_documents') THEN
        CREATE TABLE langchain_documents (
            id TEXT PRIMARY KEY,
            content TEXT NOT NULL,
            metadata JSONB DEFAULT '{}'::jsonb,
            embedding vector(1536),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_langchain_documents_embedding ON langchain_documents USING ivfflat (embedding vector_cosine_ops);
        CREATE INDEX idx_langchain_documents_metadata ON langchain_documents USING gin(metadata);
        
        ALTER TABLE langchain_documents ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Authenticated users can read documents" ON langchain_documents
        FOR SELECT TO authenticated USING (true);
        CREATE POLICY "Authenticated users can insert documents" ON langchain_documents
        FOR INSERT TO authenticated WITH CHECK (true);
        
        RAISE NOTICE 'Created langchain_documents table';
    END IF;

    -- User Presence Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_presence') THEN
        CREATE TABLE user_presence (
            user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            status TEXT CHECK (status IN ('online', 'away', 'busy', 'offline')) DEFAULT 'offline',
            last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            current_page TEXT,
            is_typing BOOLEAN DEFAULT FALSE,
            metadata JSONB DEFAULT '{}'::jsonb,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_user_presence_status ON user_presence(status);
        CREATE INDEX idx_user_presence_last_seen ON user_presence(last_seen);
        
        ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Users can manage their own presence" ON user_presence
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
        CREATE POLICY "Users can view all presence data" ON user_presence
        FOR SELECT TO authenticated USING (true);
        
        RAISE NOTICE 'Created user_presence table';
    END IF;

    -- Collaborative Sessions Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'collaborative_sessions') THEN
        CREATE TABLE collaborative_sessions (
            id TEXT PRIMARY KEY,
            type TEXT CHECK (type IN ('document', 'project', 'task', 'meeting')) NOT NULL,
            resource_id TEXT NOT NULL,
            participants JSONB DEFAULT '[]'::jsonb,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_collaborative_sessions_type ON collaborative_sessions(type);
        CREATE INDEX idx_collaborative_sessions_resource_id ON collaborative_sessions(resource_id);
        
        ALTER TABLE collaborative_sessions ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Authenticated users can manage sessions" ON collaborative_sessions
        FOR ALL TO authenticated USING (true) WITH CHECK (true);
        
        RAISE NOTICE 'Created collaborative_sessions table';
    END IF;

    -- Document Comments Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'document_comments') THEN
        CREATE TABLE document_comments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            document_id TEXT NOT NULL,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            parent_id UUID REFERENCES document_comments(id) ON DELETE CASCADE,
            content TEXT NOT NULL,
            position INTEGER NOT NULL,
            resolved BOOLEAN DEFAULT FALSE,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_document_comments_document_id ON document_comments(document_id);
        CREATE INDEX idx_document_comments_user_id ON document_comments(user_id);
        
        ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Users can manage their own comments" ON document_comments
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
        CREATE POLICY "Users can read all comments" ON document_comments
        FOR SELECT TO authenticated USING (true);
        
        RAISE NOTICE 'Created document_comments table';
    END IF;

    -- Real-time Notifications Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'realtime_notifications') THEN
        CREATE TABLE realtime_notifications (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            type TEXT NOT NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            data JSONB DEFAULT '{}'::jsonb,
            read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_realtime_notifications_user_id ON realtime_notifications(user_id);
        CREATE INDEX idx_realtime_notifications_read ON realtime_notifications(read);
        
        ALTER TABLE realtime_notifications ENABLE ROW LEVEL SECURITY;
        CREATE POLICY "Users can manage their own notifications" ON realtime_notifications
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
        
        RAISE NOTICE 'Created realtime_notifications table';
    END IF;
END $$;

-- Step 3: Create utility functions
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5
)
RETURNS TABLE (
    id text,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        langchain_documents.id,
        langchain_documents.content,
        langchain_documents.metadata,
        1 - (langchain_documents.embedding <=> query_embedding) AS similarity
    FROM langchain_documents
    WHERE 1 - (langchain_documents.embedding <=> query_embedding) > match_threshold
    ORDER BY similarity DESC
    LIMIT match_count;
$$;

CREATE OR REPLACE FUNCTION update_user_presence(
    p_user_id UUID,
    p_status TEXT DEFAULT NULL,
    p_current_page TEXT DEFAULT NULL,
    p_is_typing BOOLEAN DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO user_presence (user_id, status, current_page, is_typing, last_seen, updated_at)
    VALUES (p_user_id, COALESCE(p_status, 'online'), p_current_page, COALESCE(p_is_typing, FALSE), NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        status = COALESCE(p_status, user_presence.status),
        current_page = COALESCE(p_current_page, user_presence.current_page),
        is_typing = COALESCE(p_is_typing, user_presence.is_typing),
        last_seen = NOW(),
        updated_at = NOW();
END;
$$;

-- Step 4: Final verification
SELECT 
    'SUCCESS: LangChain and Real-time collaboration features installed!' as status,
    COUNT(*) as new_tables_created
FROM information_schema.tables 
WHERE table_name IN (
    'langchain_conversations',
    'langchain_documents', 
    'user_presence',
    'collaborative_sessions',
    'document_comments',
    'realtime_notifications'
);

-- Show API key status
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'OpenAI API Key: Configured ✅'
        ELSE 'OpenAI API Key: Not found ❌'
    END as api_key_status
FROM api_keys 
WHERE provider = 'openai' AND api_key IS NOT NULL AND api_key != 'your-openai-api-key-here';
