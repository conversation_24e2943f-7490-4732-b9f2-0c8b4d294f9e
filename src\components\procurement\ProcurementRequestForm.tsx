import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, Save, Send, Calculator } from 'lucide-react';
import { useProcurementMutations } from '@/hooks/useProcurement';
import { useProjects } from '@/hooks/useProjects';
import { useDepartments } from '@/hooks/useDepartments';
import { useAuth } from '@/components/auth/AuthProvider';

interface ProcurementItem {
  id?: string;
  item_name: string;
  description: string;
  category: string;
  brand_preference: string;
  model_specification: string;
  quantity: number;
  unit_of_measurement: string;
  estimated_unit_cost: number;
  estimated_total_cost: number;
  vendor_name: string;
  vendor_contact: string;
  notes: string;
}

interface ProcurementRequestFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

const PROCUREMENT_CATEGORIES = [
  'Office Supplies',
  'IT Equipment',
  'Furniture',
  'Software',
  'Tools & Equipment',
  'Maintenance',
  'Utilities',
  'Professional Services',
  'Travel & Transportation',
  'Marketing Materials',
  'Other'
];

const PRIORITY_OPTIONS = [
  { value: 'low', label: 'Low', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
];

const UNIT_OPTIONS = ['pcs', 'kg', 'lbs', 'meters', 'feet', 'liters', 'gallons', 'hours', 'days', 'months', 'licenses', 'subscriptions'];

export const ProcurementRequestForm: React.FC<ProcurementRequestFormProps> = ({ onSuccess, onCancel }) => {
  const { userProfile } = useAuth();
  const { projects } = useProjects();
  const { departments } = useDepartments();
  const { createRequest, isCreatingRequest } = useProcurementMutations();

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    justification: '',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    category: '',
    department_id: userProfile?.department_id || '',
    project_id: '',
    required_by_date: '',
    notes: ''
  });

  const [items, setItems] = useState<ProcurementItem[]>([{
    item_name: '',
    description: '',
    category: '',
    brand_preference: '',
    model_specification: '',
    quantity: 1,
    unit_of_measurement: 'pcs',
    estimated_unit_cost: 0,
    estimated_total_cost: 0,
    vendor_name: '',
    vendor_contact: '',
    notes: ''
  }]);

  // Calculate total estimated cost
  const totalEstimatedCost = items.reduce((sum, item) => sum + item.estimated_total_cost, 0);

  // Update item and recalculate total
  const updateItem = (index: number, field: keyof ProcurementItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate total cost for this item
    if (field === 'quantity' || field === 'estimated_unit_cost') {
      updatedItems[index].estimated_total_cost = updatedItems[index].quantity * updatedItems[index].estimated_unit_cost;
    }
    
    setItems(updatedItems);
  };

  // Add new item
  const addItem = () => {
    setItems([...items, {
      item_name: '',
      description: '',
      category: '',
      brand_preference: '',
      model_specification: '',
      quantity: 1,
      unit_of_measurement: 'pcs',
      estimated_unit_cost: 0,
      estimated_total_cost: 0,
      vendor_name: '',
      vendor_contact: '',
      notes: ''
    }]);
  };

  // Remove item
  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent, submitForApproval = false) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      alert('Please enter a title for the procurement request');
      return;
    }

    if (!formData.justification.trim()) {
      alert('Please provide justification for this procurement request');
      return;
    }

    if (items.some(item => !item.item_name.trim())) {
      alert('Please fill in all item names');
      return;
    }

    try {
      const requestData = {
        ...formData,
        estimated_total_cost: totalEstimatedCost,
        status: submitForApproval ? 'manager_review' : 'pending'
      };

      createRequest(requestData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        justification: '',
        priority: 'medium',
        category: '',
        department_id: userProfile?.department_id || '',
        project_id: '',
        required_by_date: '',
        notes: ''
      });
      
      setItems([{
        item_name: '',
        description: '',
        category: '',
        brand_preference: '',
        model_specification: '',
        quantity: 1,
        unit_of_measurement: 'pcs',
        estimated_unit_cost: 0,
        estimated_total_cost: 0,
        vendor_name: '',
        vendor_contact: '',
        notes: ''
      }]);

      onSuccess?.();
    } catch (error) {
      console.error('Error creating procurement request:', error);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            New Procurement Request
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="title">Request Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Brief description of what you need"
                  required
                />
              </div>

              <div>
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {PROCUREMENT_CATEGORIES.map((category) => (
                      <SelectItem key={category} value={category}>
                        {category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value: any) => setFormData({ ...formData, priority: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PRIORITY_OPTIONS.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <Badge className={option.color}>{option.label}</Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="department">Department</Label>
                <Select value={formData.department_id} onValueChange={(value) => setFormData({ ...formData, department_id: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select department" />
                  </SelectTrigger>
                  <SelectContent>
                    {departments.map((dept) => (
                      <SelectItem key={dept.id} value={dept.id}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="project">Project (Optional)</Label>
                <Select value={formData.project_id} onValueChange={(value) => setFormData({ ...formData, project_id: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select project" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Project</SelectItem>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="required_by_date">Required By Date</Label>
                <Input
                  id="required_by_date"
                  type="date"
                  value={formData.required_by_date}
                  onChange={(e) => setFormData({ ...formData, required_by_date: e.target.value })}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Detailed description of the procurement request"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="justification">Business Justification *</Label>
              <Textarea
                id="justification"
                value={formData.justification}
                onChange={(e) => setFormData({ ...formData, justification: e.target.value })}
                placeholder="Explain why this procurement is necessary and how it benefits the organization"
                rows={3}
                required
              />
            </div>

            <Separator />

            {/* Items Section */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Items to Procure</h3>
                <Button type="button" onClick={addItem} variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Item
                </Button>
              </div>

              <div className="space-y-4">
                {items.map((item, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Item {index + 1}</h4>
                      {items.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeItem(index)}
                          variant="ghost"
                          size="sm"
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label>Item Name *</Label>
                        <Input
                          value={item.item_name}
                          onChange={(e) => updateItem(index, 'item_name', e.target.value)}
                          placeholder="Name of the item"
                          required
                        />
                      </div>

                      <div>
                        <Label>Category</Label>
                        <Select value={item.category} onValueChange={(value) => updateItem(index, 'category', value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            {PROCUREMENT_CATEGORIES.map((category) => (
                              <SelectItem key={category} value={category}>
                                {category}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Brand Preference</Label>
                        <Input
                          value={item.brand_preference}
                          onChange={(e) => updateItem(index, 'brand_preference', e.target.value)}
                          placeholder="Preferred brand"
                        />
                      </div>

                      <div className="md:col-span-3">
                        <Label>Description</Label>
                        <Textarea
                          value={item.description}
                          onChange={(e) => updateItem(index, 'description', e.target.value)}
                          placeholder="Detailed description of the item"
                          rows={2}
                        />
                      </div>

                      <div>
                        <Label>Quantity *</Label>
                        <Input
                          type="number"
                          min="1"
                          value={item.quantity}
                          onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                          required
                        />
                      </div>

                      <div>
                        <Label>Unit</Label>
                        <Select value={item.unit_of_measurement} onValueChange={(value) => updateItem(index, 'unit_of_measurement', value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {UNIT_OPTIONS.map((unit) => (
                              <SelectItem key={unit} value={unit}>
                                {unit}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Estimated Unit Cost ($)</Label>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          value={item.estimated_unit_cost}
                          onChange={(e) => updateItem(index, 'estimated_unit_cost', parseFloat(e.target.value) || 0)}
                        />
                      </div>

                      <div>
                        <Label>Vendor Name</Label>
                        <Input
                          value={item.vendor_name}
                          onChange={(e) => updateItem(index, 'vendor_name', e.target.value)}
                          placeholder="Preferred vendor"
                        />
                      </div>

                      <div>
                        <Label>Vendor Contact</Label>
                        <Input
                          value={item.vendor_contact}
                          onChange={(e) => updateItem(index, 'vendor_contact', e.target.value)}
                          placeholder="Phone/Email"
                        />
                      </div>

                      <div>
                        <Label>Total Cost</Label>
                        <Input
                          value={`$${item.estimated_total_cost.toFixed(2)}`}
                          disabled
                          className="bg-gray-50"
                        />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="font-semibold">Total Estimated Cost:</span>
                  <span className="text-xl font-bold text-blue-600">
                    ${totalEstimatedCost.toFixed(2)}
                  </span>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                placeholder="Any additional information or special requirements"
                rows={3}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4 pt-4">
              <Button
                type="submit"
                disabled={isCreatingRequest}
                className="flex-1"
              >
                <Save className="h-4 w-4 mr-2" />
                {isCreatingRequest ? 'Saving...' : 'Save as Draft'}
              </Button>
              
              <Button
                type="button"
                onClick={(e) => handleSubmit(e, true)}
                disabled={isCreatingRequest}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                <Send className="h-4 w-4 mr-2" />
                {isCreatingRequest ? 'Submitting...' : 'Submit for Approval'}
              </Button>
              
              {onCancel && (
                <Button type="button" onClick={onCancel} variant="outline">
                  Cancel
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};
