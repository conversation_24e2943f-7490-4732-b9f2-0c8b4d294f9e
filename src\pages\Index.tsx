import { useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/components/auth/AuthProvider";
import { Loader } from "@/components/ui/Loader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeSwitcher } from "@/components/ThemeSwitcher";
import { BookOpen, ExternalLink } from "lucide-react";

const Index = () => {
  const { isAuthenticated, userProfile, loading, initialized } = useAuth();
  const navigate = useNavigate();

  const handleRoleRedirect = useCallback((role: string) => {
    switch (role) {
      case 'admin':
        navigate('/dashboard/admin');
        break;
      case 'manager':
        navigate('/dashboard/manager');
        break;
      case 'staff':
        navigate('/dashboard/staff');
        break;
      case 'accountant':
        navigate('/dashboard/accountant');
        break;
      case 'staff-admin':
        navigate('/dashboard/staff-admin');
        break;
      default:
        navigate('/dashboard/staff');
    }
  }, [navigate]);

  const handleUserManual = useCallback(() => {
    // Open user manual in new tab
    window.open('/user-manual.html', '_blank');
  }, []);

  useEffect(() => {
    if (initialized && !loading && isAuthenticated && userProfile) {
      // Redirect based on user role to correct dashboard routes
      const role = userProfile.role || userProfile.account_type;
      handleRoleRedirect(role);
    }
  }, [isAuthenticated, userProfile, loading, initialized, handleRoleRedirect]);

  if (loading || !initialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background px-4 py-8 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-secondary/5 to-primary/5 animate-pulse-glow" />
      <div className="absolute inset-0 backdrop-blur-3xl" />
      
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-3">
        {/* User Manual Button */}
        <Button
          onClick={handleUserManual}
          variant="outline"
          size="sm"
          className="glassmorphism border-white/20 hover:border-primary/50 bg-background/80 backdrop-blur-md hover:bg-primary/10 transition-all duration-300 group"
        >
          <BookOpen className="h-4 w-4 mr-2 group-hover:text-primary transition-colors" />
          <span className="hidden sm:inline">User Manual</span>
          <span className="sm:hidden">Manual</span>
          <ExternalLink className="h-3 w-3 ml-1 opacity-60" />
        </Button>

        {/* Theme Switcher */}
        <ThemeSwitcher />
      </div>
      
      <div className="relative z-10 w-full max-w-4xl text-center">
        <img 
          src="/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png" 
          alt="CT Communication Towers Logo" 
          className="h-20 w-auto mx-auto mb-8"
        />
        
        <Card className="glass-card border border-white/10 dark:border-white/5 shadow-2xl">
          <CardHeader>
            <CardTitle className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              CTNL AI Workflow Platform
            </CardTitle>
            <CardDescription className="text-lg text-muted-foreground">
              Advanced workflow automation for telecom operations, project management, and AI-powered insights
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-8">
              <div className="text-center space-y-2">
                <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
                  <span className="text-2xl">🏢</span>
                </div>
                <h3 className="font-semibold">Role-Based Access</h3>
                <p className="text-sm text-muted-foreground">
                  Admin, Manager, and Staff dashboards with tailored features
                </p>
              </div>
              <div className="text-center space-y-2">
                <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="font-semibold">Mobile-First Design</h3>
                <p className="text-sm text-muted-foreground">
                  Location-based clock-in, task management, and real-time updates
                </p>
              </div>
              <div className="text-center space-y-2">
                <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto">
                  <span className="text-2xl">🤖</span>
                </div>
                <h3 className="font-semibold">AI-Powered Insights</h3>
                <p className="text-sm text-muted-foreground">
                  Document analysis, task optimization, and intelligent automation
                </p>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                <Button
                  onClick={() => navigate('/auth')}
                  size="lg"
                  className="w-full sm:w-auto px-8 py-3 text-lg font-semibold"
                >
                  Access Platform
                </Button>
                <Button
                  onClick={handleUserManual}
                  variant="outline"
                  size="lg"
                  className="w-full sm:w-auto px-6 py-3 text-lg font-semibold glassmorphism border-white/20 hover:border-primary/50 bg-background/80 backdrop-blur-md hover:bg-primary/10 transition-all duration-300 group"
                >
                  <BookOpen className="h-5 w-5 mr-2 group-hover:text-primary transition-colors" />
                  User Manual
                  <ExternalLink className="h-4 w-4 ml-2 opacity-60" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Secure authentication with role-based access control
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
