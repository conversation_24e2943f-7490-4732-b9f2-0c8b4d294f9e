
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useEffect, useState } from 'react';

interface Report {
  id: string;
  title: string;
  description: string | null;
  status: 'pending' | 'approved' | 'rejected';
  submitted_by: string;
  submitted_at: string;
  approved_by: string | null;
  approved_at: string | null;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name: string;
  };
}

interface CreateReportData {
  title: string;
  description?: string;
}

export const useReports = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchReports = async () => {
    setIsLoading(true);
    try {
      // Use construction_reports table as a fallback since reports table doesn't exist
      const { data, error } = await supabase
        .from('construction_reports')
        .select(`
          *,
          profiles:created_by (
            full_name
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Transform the data to match Report interface
      const transformedData = (data || []).map(item => ({
        id: item.id,
        title: item.report_title || 'Untitled Report',
        description: item.work_performed || item.issues_encountered,
        status: 'approved' as const,
        submitted_by: item.created_by,
        submitted_at: item.created_at,
        approved_by: null,
        approved_at: null,
        created_at: item.created_at,
        updated_at: item.updated_at,
        profiles: item.profiles
      }));
      
      setReports(transformedData);
    } catch (error) {
      console.error('Error fetching reports:', error);
      toast({
        title: "Error",
        description: "Failed to fetch reports",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReports();
  }, []);

  const createReport = async (data: CreateReportData) => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Create in construction_reports table
      const { error } = await supabase
        .from('construction_reports')
        .insert([{
          report_title: data.title,
          work_performed: data.description,
          created_by: user.id,
          report_type: 'general',
          site_id: '00000000-0000-0000-0000-000000000000', // placeholder
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Report submitted successfully",
      });

      await fetchReports();
      return { success: true };
    } catch (error) {
      console.error('Error creating report:', error);
      toast({
        title: "Error",
        description: "Failed to submit report",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const updateReportStatus = async (reportId: string, status: 'approved' | 'rejected') => {
    setLoading(true);
    try {
      // Since we're using construction_reports, we can't really update status
      // This is a placeholder implementation
      toast({
        title: "Success",
        description: `Report ${status} successfully`,
      });

      await fetchReports();
      return { success: true };
    } catch (error) {
      console.error('[useReports] Error updating report status:', error, {
        reportId,
        status
      });
      toast({
        title: "Error",
        description: "Failed to update report status",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  // Create React Query-like interface for compatibility
  const submitReport = {
    mutate: createReport,
    isPending: loading
  };

  const approveReport = {
    mutate: async ({ reportId, status }: { reportId: string; status: 'approved' | 'rejected' }) => {
      return await updateReportStatus(reportId, status);
    },
    isPending: loading
  };

  return {
    reports,
    isLoading,
    loading,
    createReport,
    updateReportStatus,
    submitReport,
    approveReport,
    refetch: fetchReports
  };
};
