<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Profile 406/409 Errors - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status.error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .sql-box {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .error-details {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 Fix Profile 406/409 Errors</h1>
            <p>Emergency fix for profile access and creation issues</p>
        </div>

        <div class="card">
            <h3>🔍 Current Error Analysis</h3>
            <div class="error-details">
                <h4>Detected Errors:</h4>
                <ul>
                    <li><strong>406 Error:</strong> Profile fetch failed - RLS policy blocking SELECT</li>
                    <li><strong>409 Error:</strong> Profile creation conflict - duplicate key or RLS blocking INSERT</li>
                    <li><strong>User ID:</strong> 44349058-db4b-4a0b-8c99-8a913d07df74</li>
                    <li><strong>Email:</strong> <EMAIL></li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>🔧 Quick Fix Options</h3>
            
            <button class="button" onclick="fixProfileDirectly()">
                🚀 Fix Profile Issues Directly
            </button>
            
            <button class="button" onclick="testProfileAccess()">
                🧪 Test Profile Access
            </button>
            
            <button class="button" onclick="createEmergencyProfile()">
                ⚡ Create Emergency Profile
            </button>
        </div>

        <div class="card">
            <h3>📋 Manual SQL Fix</h3>
            <p>If the automatic fix doesn't work, copy and run this SQL in your Supabase SQL Editor:</p>
            
            <div class="sql-box" id="sqlCode">-- Emergency Profile Fix
-- Run this in Supabase SQL Editor

-- 1. Disable RLS temporarily
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop conflicting policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;

-- 3. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 4. Create simple policies
CREATE POLICY "allow_own_select" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "allow_own_insert" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 5. Create/update the specific user profile
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    updated_at = NOW();

-- 6. Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;</div>

            <button class="button" onclick="copySQL()">📋 Copy SQL</button>
        </div>

        <div id="status" class="status info" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
        }

        window.copySQL = function() {
            const sqlCode = document.getElementById('sqlCode').textContent;
            navigator.clipboard.writeText(sqlCode).then(() => {
                showStatus('✅ SQL copied to clipboard!\n\nNow:\n1. Go to your Supabase dashboard\n2. Open SQL Editor\n3. Paste and run the SQL\n4. Come back and test profile access', 'success');
            }).catch(() => {
                showStatus('❌ Failed to copy SQL. Please select and copy manually.', 'error');
            });
        };

        window.testProfileAccess = async function() {
            try {
                showStatus('🧪 Testing profile access...', 'info');
                
                // Get current user
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                
                if (userError || !user) {
                    showStatus('❌ No authenticated user found. Please sign in first.', 'error');
                    return;
                }
                
                // Test profile access
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single();
                
                if (profileError) {
                    showStatus(`❌ Profile access failed: ${profileError.message}\n\nError code: ${profileError.code}\nThis confirms the RLS issue. Please run the SQL fix above.`, 'error');
                } else if (profile) {
                    showStatus(`✅ Profile access successful!\n\nProfile: ${profile.full_name || 'No name'} (${profile.email})\nRole: ${profile.role || 'No role'}\nStatus: ${profile.status || 'No status'}`, 'success');
                } else {
                    showStatus('⚠️ No profile found for current user. This might be expected for new users.', 'error');
                }
                
            } catch (error) {
                showStatus(`❌ Test failed: ${error.message}`, 'error');
            }
        };

        window.createEmergencyProfile = async function() {
            try {
                showStatus('⚡ Creating emergency profile...', 'info');
                
                // Get current user
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                
                if (userError || !user) {
                    showStatus('❌ No authenticated user found. Please sign in first.', 'error');
                    return;
                }
                
                // Try to create profile
                const { error: insertError } = await supabase
                    .from('profiles')
                    .upsert({
                        id: user.id,
                        full_name: user.user_metadata?.full_name || 'CTNL User',
                        email: user.email,
                        role: 'staff',
                        status: 'active'
                    });
                
                if (insertError) {
                    showStatus(`❌ Emergency profile creation failed: ${insertError.message}\n\nError code: ${insertError.code}\nPlease run the manual SQL fix above.`, 'error');
                } else {
                    showStatus('✅ Emergency profile created successfully!\n\nYou can now refresh the main application.', 'success');
                }
                
            } catch (error) {
                showStatus(`❌ Emergency profile creation failed: ${error.message}`, 'error');
            }
        };

        window.fixProfileDirectly = async function() {
            try {
                showStatus('🚀 Attempting direct profile fix...', 'info');
                
                // First test access
                await window.testProfileAccess();
                
                // Wait a moment
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Then try to create emergency profile
                await window.createEmergencyProfile();
                
            } catch (error) {
                showStatus(`❌ Direct fix failed: ${error.message}\n\nPlease use the manual SQL fix above.`, 'error');
            }
        };

        // Auto-run test on page load
        window.addEventListener('load', () => {
            showStatus('🔍 Page loaded. Click "Test Profile Access" to check current status.', 'info');
        });
    </script>
</body>
</html>
