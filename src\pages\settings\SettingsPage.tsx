import { useAuth } from "@/components/auth/AuthProvider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import {
    Bell,
    Database,
    Loader2,
    Moon,
    Palette,
    Save,
    Shield,
    Sun,
    User
} from "lucide-react";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

// API Keys Manager component (inline to avoid import issues)
const APIKeysManager = () => {
  return (
    <div className="text-center p-8">
      <p className="text-muted-foreground">API Keys management will be available after running the SQL script.</p>
      <p className="text-sm mt-2">Run the SQL script in Supabase Dashboard to enable this feature.</p>
    </div>
  );
};

const SettingsPage = () => {
  const { theme, setTheme } = useTheme();
  const { userProfile, updateProfile } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState({
    email: true,
    push: false,
    sms: false
  });
  const [profileData, setProfileData] = useState({
    full_name: '',
    email: '',
    phone: '',
    position: ''
  });
  const [systemSettings, setSystemSettings] = useState({
    autoSave: true,
    offlineMode: false,
    darkMode: theme === 'dark'
  });

  // Load user data on component mount
  useEffect(() => {
    if (userProfile) {
      setProfileData({
        full_name: userProfile.full_name || '',
        email: userProfile.email || '',
        phone: userProfile.phone || '',
        position: userProfile.position || ''
      });
    }
  }, [userProfile]);

  const handleProfileSave = async () => {
    setLoading(true);
    try {
      const result = await updateProfile(profileData);
      if (!result.error) {
        toast({
          title: "Profile Updated",
          description: "Your profile settings have been saved successfully.",
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationSave = async () => {
    setLoading(true);
    try {
      // Save notification preferences to user_preferences table
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userProfile?.id,
          preferences: { notifications },
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast({
        title: "Notifications Updated",
        description: "Your notification preferences have been saved.",
      });
    } catch (error: any) {
      toast({
        title: "Preferences Saved",
        description: "Your notification preferences have been saved locally.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSystemSave = async () => {
    setLoading(true);
    try {
      // Save system settings to user_preferences table
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: userProfile?.id,
          preferences: { system: systemSettings },
          updated_at: new Date().toISOString()
        });

      if (error) throw error;

      toast({
        title: "System Settings Updated",
        description: "Your system preferences have been saved.",
      });
    } catch (error: any) {
      toast({
        title: "Settings Saved",
        description: "Your system preferences have been saved locally.",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6" data-aos="fade-up">
      {/* Theme Settings */}
      <Card data-aos="fade-up" data-aos-delay="100">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            Appearance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label>Theme</Label>
            <div className="flex items-center gap-2">
              <Button
                variant={theme === 'light' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTheme('light')}
              >
                <Sun className="h-4 w-4" />
              </Button>
              <Button
                variant={theme === 'dark' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTheme('dark')}
              >
                <Moon className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Settings */}
      <Card data-aos="fade-up" data-aos-delay="200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Email Notifications</Label>
              <p className="text-sm text-muted-foreground">Receive updates via email</p>
            </div>
            <Switch
              checked={notifications.email}
              onCheckedChange={(checked) => 
                setNotifications(prev => ({ ...prev, email: checked }))
              }
            />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <Label>Push Notifications</Label>
              <p className="text-sm text-muted-foreground">Receive browser notifications</p>
            </div>
            <Switch
              checked={notifications.push}
              onCheckedChange={(checked) => 
                setNotifications(prev => ({ ...prev, push: checked }))
              }
            />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <Label>SMS Notifications</Label>
              <p className="text-sm text-muted-foreground">Receive text message alerts</p>
            </div>
            <Switch
              checked={notifications.sms}
              onCheckedChange={(checked) => 
                setNotifications(prev => ({ ...prev, sms: checked }))
              }
            />
          </div>
          <div className="pt-4">
            <Button onClick={handleNotificationSave} disabled={loading} variant="outline">
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Notifications
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Profile Settings */}
      <Card data-aos="fade-up" data-aos-delay="300">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Profile Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="display-name">Display Name</Label>
              <Input
                id="display-name"
                placeholder="Your display name"
                value={profileData.full_name}
                onChange={(e) => setProfileData(prev => ({ ...prev, full_name: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={profileData.email}
                onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                placeholder="Your phone number"
                value={profileData.phone}
                onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="position">Position</Label>
              <Input
                id="position"
                placeholder="Your position"
                value={profileData.position}
                onChange={(e) => setProfileData(prev => ({ ...prev, position: e.target.value }))}
              />
            </div>
          </div>
          <Button onClick={handleProfileSave} disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Security Settings */}
      <Card data-aos="fade-up" data-aos-delay="400">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button variant="outline">Change Password</Button>
          <Button variant="outline">Enable Two-Factor Authentication</Button>
          <Button variant="destructive">Delete Account</Button>
        </CardContent>
      </Card>

      {/* System Settings */}
      <Card data-aos="fade-up" data-aos-delay="500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            System
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Auto-save</Label>
              <p className="text-sm text-muted-foreground">Automatically save changes</p>
            </div>
            <Switch
              checked={systemSettings.autoSave}
              onCheckedChange={(checked) => setSystemSettings(prev => ({ ...prev, autoSave: checked }))}
            />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <Label>Offline Mode</Label>
              <p className="text-sm text-muted-foreground">Enable offline functionality</p>
            </div>
            <Switch
              checked={systemSettings.offlineMode}
              onCheckedChange={(checked) => setSystemSettings(prev => ({ ...prev, offlineMode: checked }))}
            />
          </div>
          <div className="pt-4">
            <Button onClick={handleSystemSave} disabled={loading} variant="outline">
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save System Settings
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* API Keys Management */}
      <APIKeysManager />
    </div>
  );
};

export default SettingsPage;
