import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>ead<PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Sheet, Upload, Sync, Settings, Plus, Trash2, RefreshCw, AlertCircle, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface Spreadsheet {
  id: string;
  name: string;
  url: string;
  sheet_id: string;
  lastModified: string;
  sheets: string[];
  rows: number;
  sync_status: 'connected' | 'synced' | 'pending' | 'error';
  last_sync?: string;
  error_message?: string;
  created_at: string;
}

interface SyncMapping {
  id?: string;
  spreadsheet_id: string;
  sheet_name: string;
  table_name: string;
  mapping: Record<string, string>;
  is_active: boolean;
}

export const GoogleSheetsIntegration = () => {
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const [spreadsheets, setSpreadsheets] = useState<Spreadsheet[]>([]);
  const [mappings, setMappings] = useState<SyncMapping[]>([]);
  const [loading, setLoading] = useState(false);
  const [syncing, setSyncing] = useState<string | null>(null);
  const [newSheetUrl, setNewSheetUrl] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  const [mappingDialog, setMappingDialog] = useState(false);
  const [selectedSpreadsheet, setSelectedSpreadsheet] = useState<Spreadsheet | null>(null);
  const [newMapping, setNewMapping] = useState<Partial<SyncMapping>>({
    sheet_name: '',
    table_name: '',
    mapping: {},
    is_active: true
  });

  useEffect(() => {
    loadSpreadsheets();
    loadMappings();
  }, []);

  const loadSpreadsheets = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('google_sheets_integrations')
        .select('*')
        .eq('user_id', userProfile?.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setSpreadsheets(data || []);
    } catch (error: any) {
      console.error('Error loading spreadsheets:', error);
      toast({
        title: "Error Loading Spreadsheets",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadMappings = async () => {
    try {
      const { data, error } = await supabase
        .from('sync_mappings')
        .select('*')
        .eq('user_id', userProfile?.id);

      if (error) throw error;

      setMappings(data || []);
    } catch (error: any) {
      console.error('Error loading mappings:', error);
    }
  };

  const extractSheetId = (url: string): string | null => {
    const match = url.match(/\/spreadsheets\/d\/([a-zA-Z0-9-_]+)/);
    return match ? match[1] : null;
  };

  const connectSpreadsheet = async () => {
    if (!newSheetUrl.trim()) {
      toast({
        title: "URL Required",
        description: "Please enter a Google Sheets URL",
        variant: "destructive",
      });
      return;
    }

    const sheetId = extractSheetId(newSheetUrl);
    if (!sheetId) {
      toast({
        title: "Invalid URL",
        description: "Please enter a valid Google Sheets URL",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      // Call integration manager function to connect the sheet
      const { data, error } = await supabase.functions.invoke('integration-manager', {
        body: {
          action: 'connect_google_sheet',
          sheet_url: newSheetUrl,
          sheet_id: sheetId,
          user_id: userProfile?.id
        }
      });

      if (error) throw error;

      // Save to our database
      const { error: dbError } = await supabase
        .from('google_sheets_integrations')
        .insert({
          name: data.name || `Sheet ${Date.now()}`,
          url: newSheetUrl,
          sheet_id: sheetId,
          sheets: data.sheets || [],
          rows: data.rows || 0,
          sync_status: 'connected',
          user_id: userProfile?.id,
          lastModified: new Date().toISOString()
        });

      if (dbError) throw dbError;

      await loadSpreadsheets();
      setNewSheetUrl('');
      setDialogOpen(false);

      toast({
        title: "Spreadsheet Connected",
        description: "Google Sheets has been connected successfully",
      });
    } catch (error: any) {
      toast({
        title: "Connection Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const syncSpreadsheet = async (spreadsheet: Spreadsheet) => {
    setSyncing(spreadsheet.id);
    try {
      const { data, error } = await supabase.functions.invoke('integration-manager', {
        body: {
          action: 'sync_google_sheet',
          spreadsheet_id: spreadsheet.id,
          sheet_id: spreadsheet.sheet_id,
          user_id: userProfile?.id
        }
      });

      if (error) throw error;

      // Update sync status
      await supabase
        .from('google_sheets_integrations')
        .update({
          sync_status: 'synced',
          last_sync: new Date().toISOString(),
          rows: data.rows_synced || 0
        })
        .eq('id', spreadsheet.id);

      await loadSpreadsheets();

      toast({
        title: "Sync Completed",
        description: `Synced ${data.rows_synced || 0} rows successfully`,
      });
    } catch (error: any) {
      await supabase
        .from('google_sheets_integrations')
        .update({
          sync_status: 'error',
          error_message: error.message
        })
        .eq('id', spreadsheet.id);

      toast({
        title: "Sync Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setSyncing(null);
    }
  };

  const deleteSpreadsheet = async (id: string) => {
    try {
      const { error } = await supabase
        .from('google_sheets_integrations')
        .delete()
        .eq('id', id);

      if (error) throw error;

      await loadSpreadsheets();

      toast({
        title: "Spreadsheet Removed",
        description: "Google Sheets integration has been removed",
      });
    } catch (error: any) {
      toast({
        title: "Delete Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const createMapping = async () => {
    try {
      const { error } = await supabase
        .from('sync_mappings')
        .insert({
          ...newMapping,
          spreadsheet_id: selectedSpreadsheet?.id,
          user_id: userProfile?.id
        });

      if (error) throw error;

      await loadMappings();
      setMappingDialog(false);
      setNewMapping({
        sheet_name: '',
        table_name: '',
        mapping: {},
        is_active: true
      });

      toast({
        title: "Mapping Created",
        description: "Field mapping has been created successfully",
      });
    } catch (error: any) {
      toast({
        title: "Mapping Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'synced':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Synced</Badge>;
      case 'connected':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><Settings className="h-3 w-3 mr-1" />Connected</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Sync className="h-3 w-3 mr-1" />Pending</Badge>;
      case 'error':
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Error</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Sheet className="h-6 w-6 text-green-600" />
          <h2 className="text-2xl font-bold">Google Sheets Integration</h2>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadSpreadsheets} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Connect Sheet
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Connect Google Sheets</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="sheetUrl">Google Sheets URL</Label>
                  <Input
                    id="sheetUrl"
                    value={newSheetUrl}
                    onChange={(e) => setNewSheetUrl(e.target.value)}
                    placeholder="https://docs.google.com/spreadsheets/d/..."
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button onClick={connectSpreadsheet} disabled={loading} className="flex-1">
                    {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : null}
                    Connect
                  </Button>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Connected Spreadsheets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sheet className="h-5 w-5" />
            Connected Spreadsheets ({spreadsheets.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading spreadsheets...</span>
            </div>
          ) : spreadsheets.length === 0 ? (
            <div className="text-center py-8">
              <Sheet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No spreadsheets connected</p>
              <Button onClick={() => setDialogOpen(true)} className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Connect First Sheet
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Sheets</TableHead>
                    <TableHead>Rows</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Sync</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {spreadsheets.map((sheet) => (
                    <TableRow key={sheet.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{sheet.name}</p>
                          <p className="text-xs text-gray-500">
                            {sheet.url.substring(0, 50)}...
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{sheet.sheets.length}</Badge>
                      </TableCell>
                      <TableCell>{sheet.rows}</TableCell>
                      <TableCell>{getStatusBadge(sheet.sync_status)}</TableCell>
                      <TableCell>
                        {sheet.last_sync 
                          ? new Date(sheet.last_sync).toLocaleString()
                          : 'Never'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => syncSpreadsheet(sheet)}
                            disabled={syncing === sheet.id}
                          >
                            {syncing === sheet.id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <Sync className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedSpreadsheet(sheet);
                              setMappingDialog(true);
                            }}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteSpreadsheet(sheet.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Mapping Dialog */}
      <Dialog open={mappingDialog} onOpenChange={setMappingDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Configure Field Mapping</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Sheet Name</Label>
              <Select value={newMapping.sheet_name} onValueChange={(value) => setNewMapping({...newMapping, sheet_name: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select sheet" />
                </SelectTrigger>
                <SelectContent>
                  {selectedSpreadsheet?.sheets.map((sheet) => (
                    <SelectItem key={sheet} value={sheet}>
                      {sheet}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Database Table</Label>
              <Select value={newMapping.table_name} onValueChange={(value) => setNewMapping({...newMapping, table_name: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select table" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="profiles">Users</SelectItem>
                  <SelectItem value="projects">Projects</SelectItem>
                  <SelectItem value="tasks">Tasks</SelectItem>
                  <SelectItem value="departments">Departments</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2 pt-4">
              <Button onClick={createMapping} className="flex-1">
                Create Mapping
              </Button>
              <Button variant="outline" onClick={() => setMappingDialog(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <Sheet className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Connected Sheets</p>
                <p className="text-2xl font-bold">{spreadsheets.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Sync className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Active Syncs</p>
                <p className="text-2xl font-bold">
                  {spreadsheets.filter(s => s.sync_status === 'synced').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <Upload className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Rows</p>
                <p className="text-2xl font-bold">
                  {spreadsheets.reduce((sum, s) => sum + s.rows, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};