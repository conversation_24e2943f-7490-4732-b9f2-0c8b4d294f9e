import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Co<PERSON>, RotateCcw, Type } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const TextCaseConverter: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const { toast } = useToast();

  const convertToCase = (text: string, caseType: string): string => {
    switch (caseType) {
      case 'uppercase':
        return text.toUpperCase();
      case 'lowercase':
        return text.toLowerCase();
      case 'titlecase':
        return text.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        );
      case 'sentencecase':
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
      case 'camelcase':
        return text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => 
          index === 0 ? word.toLowerCase() : word.toUpperCase()
        ).replace(/\s+/g, '');
      case 'pascalcase':
        return text.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => 
          word.toUpperCase()
        ).replace(/\s+/g, '');
      case 'snakecase':
        return text.toLowerCase().replace(/\s+/g, '_');
      case 'kebabcase':
        return text.toLowerCase().replace(/\s+/g, '-');
      case 'alternating':
        return text.split('').map((char, index) => 
          index % 2 === 0 ? char.toLowerCase() : char.toUpperCase()
        ).join('');
      case 'inverse':
        return text.split('').map(char => 
          char === char.toUpperCase() ? char.toLowerCase() : char.toUpperCase()
        ).join('');
      default:
        return text;
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "Text copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy text",
        variant: "destructive",
      });
    }
  };

  const clearText = () => {
    setInputText('');
  };

  const caseTypes = [
    { id: 'uppercase', name: 'UPPERCASE', description: 'ALL CAPS' },
    { id: 'lowercase', name: 'lowercase', description: 'all lowercase' },
    { id: 'titlecase', name: 'Title Case', description: 'First Letter Of Each Word' },
    { id: 'sentencecase', name: 'Sentence case', description: 'First letter only' },
    { id: 'camelcase', name: 'camelCase', description: 'firstWordLowercase' },
    { id: 'pascalcase', name: 'PascalCase', description: 'FirstWordUppercase' },
    { id: 'snakecase', name: 'snake_case', description: 'words_separated_by_underscores' },
    { id: 'kebabcase', name: 'kebab-case', description: 'words-separated-by-hyphens' },
    { id: 'alternating', name: 'aLtErNaTiNg', description: 'Alternating case' },
    { id: 'inverse', name: 'iNVERSE', description: 'Flip existing case' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-blue-500 rounded-lg">
          <Type className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">Text Case Converter</h2>
          <p className="text-muted-foreground">Convert text between different cases</p>
        </div>
      </div>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Input Text
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={clearText}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Enter your text here..."
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="min-h-[120px]"
          />
          <div className="flex items-center justify-between mt-2">
            <Badge variant="outline">
              {inputText.length} characters
            </Badge>
            <Badge variant="outline">
              {inputText.split(/\s+/).filter(word => word.length > 0).length} words
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Output Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {caseTypes.map((caseType) => {
          const convertedText = inputText ? convertToCase(inputText, caseType.id) : '';
          
          return (
            <Card key={caseType.id} className="group hover:shadow-md transition-shadow">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center justify-between">
                  {caseType.name}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(convertedText)}
                    disabled={!convertedText}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </CardTitle>
                <p className="text-sm text-muted-foreground">{caseType.description}</p>
              </CardHeader>
              <CardContent>
                <div className="bg-muted/50 rounded-md p-3 min-h-[80px] font-mono text-sm">
                  {convertedText || (
                    <span className="text-muted-foreground italic">
                      Enter text above to see conversion...
                    </span>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Usage Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Usage Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Programming Cases:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• <strong>camelCase:</strong> JavaScript variables</li>
                <li>• <strong>PascalCase:</strong> Class names</li>
                <li>• <strong>snake_case:</strong> Python variables</li>
                <li>• <strong>kebab-case:</strong> CSS classes, URLs</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Text Formatting:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• <strong>Title Case:</strong> Headlines, titles</li>
                <li>• <strong>Sentence case:</strong> Regular sentences</li>
                <li>• <strong>UPPERCASE:</strong> Emphasis, constants</li>
                <li>• <strong>lowercase:</strong> Casual text, emails</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextCaseConverter;
