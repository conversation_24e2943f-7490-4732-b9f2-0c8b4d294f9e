-- Manual Migration for LangChain & Real-time Features
-- Run this in Supabase Dashboard SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Check if tables already exist before creating them
DO $$
BEGIN
    -- Lang<PERSON>hain Conversations Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_conversations') THEN
        CREATE TABLE langchain_conversations (
            id TEXT PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT,
            messages JSONB DEFAULT '[]'::jsonb,
            context JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_langchain_conversations_user_id ON langchain_conversations(user_id);
        CREATE INDEX idx_langchain_conversations_updated_at ON langchain_conversations(updated_at);
    END IF;

    -- LangChain Documents Table (Vector Store)
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_documents') THEN
        CREATE TABLE langchain_documents (
            id TEXT PRIMARY KEY,
            content TEXT NOT NULL,
            metadata JSONB DEFAULT '{}'::jsonb,
            embedding vector(1536), -- OpenAI text-embedding-3-small dimensions
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_langchain_documents_embedding ON langchain_documents USING ivfflat (embedding vector_cosine_ops);
        CREATE INDEX idx_langchain_documents_metadata ON langchain_documents USING gin(metadata);
    END IF;

    -- LangChain Document Metadata Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_document_metadata') THEN
        CREATE TABLE langchain_document_metadata (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            metadata JSONB DEFAULT '{}'::jsonb,
            chunk_count INTEGER DEFAULT 0,
            processing_time INTEGER DEFAULT 0,
            total_tokens INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;

    -- User Presence Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_presence') THEN
        CREATE TABLE user_presence (
            user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            status TEXT CHECK (status IN ('online', 'away', 'busy', 'offline')) DEFAULT 'offline',
            last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            current_page TEXT,
            is_typing BOOLEAN DEFAULT FALSE,
            metadata JSONB DEFAULT '{}'::jsonb,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_user_presence_status ON user_presence(status);
        CREATE INDEX idx_user_presence_last_seen ON user_presence(last_seen);
    END IF;

    -- Collaborative Sessions Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'collaborative_sessions') THEN
        CREATE TABLE collaborative_sessions (
            id TEXT PRIMARY KEY,
            type TEXT CHECK (type IN ('document', 'project', 'task', 'meeting')) NOT NULL,
            resource_id TEXT NOT NULL,
            participants JSONB DEFAULT '[]'::jsonb,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_collaborative_sessions_type ON collaborative_sessions(type);
        CREATE INDEX idx_collaborative_sessions_resource_id ON collaborative_sessions(resource_id);
        CREATE INDEX idx_collaborative_sessions_last_activity ON collaborative_sessions(last_activity);
    END IF;

    -- Document Comments Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'document_comments') THEN
        CREATE TABLE document_comments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            document_id TEXT NOT NULL,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            parent_id UUID REFERENCES document_comments(id) ON DELETE CASCADE,
            content TEXT NOT NULL,
            position INTEGER NOT NULL,
            resolved BOOLEAN DEFAULT FALSE,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_document_comments_document_id ON document_comments(document_id);
        CREATE INDEX idx_document_comments_user_id ON document_comments(user_id);
        CREATE INDEX idx_document_comments_parent_id ON document_comments(parent_id);
    END IF;

    -- Real-time Notifications Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'realtime_notifications') THEN
        CREATE TABLE realtime_notifications (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            type TEXT NOT NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            data JSONB DEFAULT '{}'::jsonb,
            read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_realtime_notifications_user_id ON realtime_notifications(user_id);
        CREATE INDEX idx_realtime_notifications_read ON realtime_notifications(read);
    END IF;

    -- API Keys Table (if it doesn't exist)
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'api_keys') THEN
        CREATE TABLE api_keys (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            provider TEXT UNIQUE NOT NULL,
            api_key TEXT NOT NULL,
            is_active BOOLEAN DEFAULT true,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
    END IF;
END $$;

-- Vector similarity search function
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5
)
RETURNS TABLE (
    id text,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        langchain_documents.id,
        langchain_documents.content,
        langchain_documents.metadata,
        1 - (langchain_documents.embedding <=> query_embedding) AS similarity
    FROM langchain_documents
    WHERE 1 - (langchain_documents.embedding <=> query_embedding) > match_threshold
    ORDER BY similarity DESC
    LIMIT match_count;
$$;

-- Function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
    p_user_id UUID,
    p_status TEXT DEFAULT NULL,
    p_current_page TEXT DEFAULT NULL,
    p_is_typing BOOLEAN DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO user_presence (user_id, status, current_page, is_typing, last_seen, updated_at)
    VALUES (p_user_id, COALESCE(p_status, 'online'), p_current_page, COALESCE(p_is_typing, FALSE), NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        status = COALESCE(p_status, user_presence.status),
        current_page = COALESCE(p_current_page, user_presence.current_page),
        is_typing = COALESCE(p_is_typing, user_presence.is_typing),
        last_seen = NOW(),
        updated_at = NOW();
END;
$$;

-- Enable RLS on new tables
ALTER TABLE langchain_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE langchain_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE langchain_document_metadata ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE collaborative_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE realtime_notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage their own conversations" ON langchain_conversations
FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Authenticated users can read documents" ON langchain_documents
FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can insert documents" ON langchain_documents
FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Authenticated users can read document metadata" ON langchain_document_metadata
FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can manage their own presence" ON user_presence
FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view all presence data" ON user_presence
FOR SELECT TO authenticated USING (true);

CREATE POLICY "Authenticated users can manage sessions" ON collaborative_sessions
FOR ALL TO authenticated USING (true) WITH CHECK (true);

CREATE POLICY "Users can manage their own comments" ON document_comments
FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can read all comments" ON document_comments
FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can manage their own notifications" ON realtime_notifications
FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Admin-only access to API keys
CREATE POLICY "Admin can manage API keys" ON api_keys
FOR ALL TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('admin', 'super_admin')
    )
);

-- Insert OpenAI API key (REPLACE WITH YOUR ACTUAL KEY)
INSERT INTO api_keys (provider, api_key, is_active) 
VALUES (
    'openai', 
    'sk-your-actual-openai-api-key-here', -- REPLACE THIS WITH YOUR REAL API KEY
    true
) ON CONFLICT (provider) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Success message
SELECT 'LangChain and Real-time collaboration features have been successfully installed!' as status;
