import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Copy, RotateCcw, Search, Replace } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const TextReplacer: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [searchText, setSearchText] = useState('');
  const [replaceText, setReplaceText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [useRegex, setUseRegex] = useState(false);
  const [globalReplace, setGlobalReplace] = useState(true);
  const [matchCount, setMatchCount] = useState(0);

  const performReplace = () => {
    if (!inputText || !searchText) {
      setOutputText(inputText);
      setMatchCount(0);
      return;
    }

    try {
      let result = inputText;
      let count = 0;

      if (useRegex) {
        const flags = `${globalReplace ? 'g' : ''}${caseSensitive ? '' : 'i'}`;
        const regex = new RegExp(searchText, flags);
        const matches = inputText.match(regex);
        count = matches ? matches.length : 0;
        result = inputText.replace(regex, replaceText);
      } else {
        const searchValue = caseSensitive ? searchText : searchText.toLowerCase();
        const textToSearch = caseSensitive ? inputText : inputText.toLowerCase();
        
        if (globalReplace) {
          let index = 0;
          while ((index = textToSearch.indexOf(searchValue, index)) !== -1) {
            count++;
            result = result.substring(0, index) + replaceText + result.substring(index + searchText.length);
            index += replaceText.length;
          }
        } else {
          const index = textToSearch.indexOf(searchValue);
          if (index !== -1) {
            count = 1;
            result = result.substring(0, index) + replaceText + result.substring(index + searchText.length);
          }
        }
      }

      setOutputText(result);
      setMatchCount(count);
    } catch (error) {
      toast.error('Invalid regular expression');
      setOutputText(inputText);
      setMatchCount(0);
    }
  };

  useEffect(() => {
    performReplace();
  }, [inputText, searchText, replaceText, caseSensitive, useRegex, globalReplace]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setSearchText('');
    setReplaceText('');
    setOutputText('');
    setMatchCount(0);
  };

  const swapSearchReplace = () => {
    const temp = searchText;
    setSearchText(replaceText);
    setReplaceText(temp);
  };

  const getStats = () => {
    const originalLength = inputText.length;
    const newLength = outputText.length;
    const difference = newLength - originalLength;
    
    return {
      originalLength,
      newLength,
      difference,
      matchCount
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 border-orange-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-orange-800">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Replace className="h-6 w-6 text-orange-600" />
            </div>
            {title || 'Text Replacer'}
          </CardTitle>
          <p className="text-orange-600 text-sm">
            Find and replace text with support for regex and case sensitivity
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Search and Replace Controls */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <Search className="h-4 w-4" />
                Find:
              </label>
              <Input
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                placeholder="Enter text to find..."
                className="border-2 border-orange-200 focus:border-orange-400"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <Replace className="h-4 w-4" />
                Replace with:
              </label>
              <Input
                value={replaceText}
                onChange={(e) => setReplaceText(e.target.value)}
                placeholder="Enter replacement text..."
                className="border-2 border-orange-200 focus:border-orange-400"
              />
            </div>
          </div>

          {/* Options */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="case-sensitive"
                checked={caseSensitive}
                onCheckedChange={setCaseSensitive}
              />
              <label htmlFor="case-sensitive" className="text-sm font-medium">
                Case Sensitive
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="use-regex"
                checked={useRegex}
                onCheckedChange={setUseRegex}
              />
              <label htmlFor="use-regex" className="text-sm font-medium">
                Use Regex
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="global-replace"
                checked={globalReplace}
                onCheckedChange={setGlobalReplace}
              />
              <label htmlFor="global-replace" className="text-sm font-medium">
                Replace All
              </label>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={swapSearchReplace}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Swap
            </Button>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <span>📝</span>
              Input Text:
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter your text here..."
              className="min-h-[140px] resize-none border-2 border-orange-200 focus:border-orange-400"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.originalLength} → {stats.newLength} chars
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                🔍 {stats.matchCount} matches
              </Badge>
              {stats.difference !== 0 && (
                <Badge variant="secondary" className={stats.difference > 0 ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"}>
                  {stats.difference > 0 ? '📈' : '📉'} {stats.difference > 0 ? '+' : ''}{stats.difference}
                </Badge>
              )}
            </div>
          )}

          {/* Output Text */}
          {outputText && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  Result:
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(outputText)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={outputText}
                readOnly
                className="min-h-[140px] bg-gray-50 border-2 border-gray-200"
              />
            </div>
          )}

          {/* Clear Button */}
          <div className="flex justify-center">
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Usage Tips
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Regular Expressions:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li><code>\d+</code> - Match numbers</li>
                <li><code>\w+</code> - Match words</li>
                <li><code>^</code> - Start of line</li>
                <li><code>$</code> - End of line</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Common Patterns:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Remove extra spaces: <code>\s+</code> → <code> </code></li>
                <li>Remove line breaks: <code>\n</code> → <code> </code></li>
                <li>Extract emails: <code>\S+@\S+\.\S+</code></li>
                <li>Remove HTML tags: <code>&lt;[^&gt;]*&gt;</code> → <code></code></li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextReplacer;
