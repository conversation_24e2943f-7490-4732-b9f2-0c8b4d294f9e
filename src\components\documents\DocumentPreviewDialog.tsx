import { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Download, 
  FileText, 
  Calendar, 
  User, 
  HardDrive, 
  Tag,
  ExternalLink,
  AlertCircle,
  Eye,
  X
} from "lucide-react";
import { formatBytes } from "@/lib/utils";
import { api } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";

interface DocumentType {
  id: string;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  category: string;
  tags: string[] | null;
  created_at: string;
  updated_at: string;
  file_path: string;
  uploaded_by: string;
  access_level: string;
  uploader_name?: string;
}

interface DocumentPreviewDialogProps {
  document: DocumentType | null;
  isOpen: boolean;
  onClose: () => void;
}

export const DocumentPreviewDialog = ({ document, isOpen, onClose }: DocumentPreviewDialogProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  if (!document) return null;

  const handleDownload = async () => {
    setIsLoading(true);
    try {
      const result = await api.documents.getDownloadUrl(document.id);
      if (result.success && result.data) {
        toast({
          title: "Download Ready",
          description: `${result.data.fileName} is ready for download`,
        });
        
        // For now, show a message since we don't have actual file storage
        console.log('Download would start for:', result.data.fileName);
      } else {
        throw new Error('Failed to get download URL');
      }
    } catch (error) {
      console.error('Download failed:', error);
      toast({
        title: "Download Failed",
        description: "Unable to download the document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('spreadsheet') || fileType.includes('excel')) return '📊';
    if (fileType.includes('word') || fileType.includes('doc')) return '📝';
    if (fileType.includes('text')) return '📃';
    return '📁';
  };

  const isPreviewable = (fileType: string) => {
    return fileType.includes('pdf') || 
           fileType.includes('image') || 
           fileType.includes('text');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <span className="text-2xl">{getFileIcon(document.file_type)}</span>
            {document.title}
          </DialogTitle>
          <DialogDescription>
            Document details and preview
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Document Info Card */}
          <Card>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">File Name:</span>
                    <span className="text-sm">{document.file_name}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">File Size:</span>
                    <span className="text-sm">{formatBytes(document.file_size)}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Tag className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Category:</span>
                    <Badge variant="secondary">{document.category}</Badge>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Uploaded by:</span>
                    <span className="text-sm">{document.uploader_name || 'Unknown'}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Created:</span>
                    <span className="text-sm">{new Date(document.created_at).toLocaleDateString()}</span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Access Level:</span>
                    <Badge variant={document.access_level === 'public' ? 'default' : 'outline'}>
                      {document.access_level}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Tags */}
              {document.tags && document.tags.length > 0 && (
                <div className="mt-4">
                  <span className="text-sm font-medium mb-2 block">Tags:</span>
                  <div className="flex flex-wrap gap-2">
                    {document.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Preview Section */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Document Preview
              </h3>
              
              {isPreviewable(document.file_type) ? (
                <div className="text-center py-12 bg-muted rounded-lg">
                  <FileText className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h4 className="text-lg font-medium mb-2">Preview Coming Soon</h4>
                  <p className="text-muted-foreground mb-4">
                    Document preview functionality is being developed
                  </p>
                  <p className="text-sm text-muted-foreground">
                    For now, you can download the document to view it
                  </p>
                </div>
              ) : (
                <div className="text-center py-12 bg-muted rounded-lg">
                  <AlertCircle className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                  <h4 className="text-lg font-medium mb-2">Preview Not Available</h4>
                  <p className="text-muted-foreground mb-4">
                    This file type cannot be previewed in the browser
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Download the document to view it with the appropriate application
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-between items-center">
            <Button variant="outline" onClick={onClose}>
              <X className="h-4 w-4 mr-2" />
              Close
            </Button>
            
            <Button onClick={handleDownload} disabled={isLoading}>
              <Download className="h-4 w-4 mr-2" />
              {isLoading ? 'Preparing...' : 'Download Document'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
