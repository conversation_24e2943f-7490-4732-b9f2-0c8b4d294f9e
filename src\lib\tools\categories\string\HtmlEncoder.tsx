import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Code, ArrowUpDown, FileText } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const HtmlEncoder: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [mode, setMode] = useState('encode');
  const [encodingType, setEncodingType] = useState('basic');
  const [useNumericEntities, setUseNumericEntities] = useState(false);
  const [result, setResult] = useState('');

  const encodingTypes = [
    { value: 'basic', label: 'Basic HTML', description: 'Encode basic HTML characters (&, <, >, ", \')' },
    { value: 'all', label: 'All Special', description: 'Encode all non-ASCII characters' },
    { value: 'attribute', label: 'Attribute Safe', description: 'Safe for HTML attributes' },
    { value: 'text', label: 'Text Content', description: 'Safe for HTML text content' }
  ];

  // HTML entity mappings
  const htmlEntities: { [key: string]: string } = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '©': '&copy;',
    '®': '&reg;',
    '™': '&trade;',
    '€': '&euro;',
    '£': '&pound;',
    '¥': '&yen;',
    '¢': '&cent;',
    '§': '&sect;',
    '¶': '&para;',
    '†': '&dagger;',
    '‡': '&Dagger;',
    '•': '&bull;',
    '…': '&hellip;',
    '′': '&prime;',
    '″': '&Prime;',
    '‹': '&lsaquo;',
    '›': '&rsaquo;',
    '«': '&laquo;',
    '»': '&raquo;',
    ''': '&lsquo;',
    ''': '&rsquo;',
    '"': '&ldquo;',
    '"': '&rdquo;',
    '–': '&ndash;',
    '—': '&mdash;',
    ' ': '&nbsp;',
    '¡': '&iexcl;',
    '¿': '&iquest;',
    'À': '&Agrave;',
    'Á': '&Aacute;',
    'Â': '&Acirc;',
    'Ã': '&Atilde;',
    'Ä': '&Auml;',
    'Å': '&Aring;',
    'Æ': '&AElig;',
    'Ç': '&Ccedil;',
    'È': '&Egrave;',
    'É': '&Eacute;',
    'Ê': '&Ecirc;',
    'Ë': '&Euml;'
  };

  // Reverse mapping for decoding
  const reverseEntities: { [key: string]: string } = {};
  Object.entries(htmlEntities).forEach(([char, entity]) => {
    reverseEntities[entity] = char;
  });

  const performOperation = () => {
    if (!inputText.trim()) {
      setResult('');
      return;
    }

    try {
      let output = '';

      if (mode === 'encode') {
        switch (encodingType) {
          case 'basic':
            output = inputText
              .replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&#39;');
            break;

          case 'all':
            output = inputText.split('').map(char => {
              if (htmlEntities[char]) {
                return useNumericEntities ? `&#${char.charCodeAt(0)};` : htmlEntities[char];
              } else if (char.charCodeAt(0) > 127) {
                return `&#${char.charCodeAt(0)};`;
              }
              return char;
            }).join('');
            break;

          case 'attribute':
            output = inputText
              .replace(/&/g, '&amp;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&#39;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;');
            break;

          case 'text':
            output = inputText
              .replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;');
            break;

          default:
            output = inputText;
        }
      } else {
        // Decode mode
        output = inputText;

        // Decode named entities
        Object.entries(reverseEntities).forEach(([entity, char]) => {
          const regex = new RegExp(entity.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
          output = output.replace(regex, char);
        });

        // Decode numeric entities (&#123; and &#x7B;)
        output = output.replace(/&#(\d+);/g, (match, num) => {
          return String.fromCharCode(parseInt(num, 10));
        });

        output = output.replace(/&#x([0-9a-fA-F]+);/g, (match, hex) => {
          return String.fromCharCode(parseInt(hex, 16));
        });
      }

      setResult(output);
    } catch (err) {
      toast.error('Error processing HTML entities');
      setResult('');
    }
  };

  useEffect(() => {
    performOperation();
  }, [inputText, mode, encodingType, useNumericEntities]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
  };

  const swapInputOutput = () => {
    if (result) {
      setInputText(result);
      setMode(mode === 'encode' ? 'decode' : 'encode');
    }
  };

  const loadSampleData = () => {
    const samples = {
      encode: [
        '<div class="example">Hello & welcome to "HTML" encoding!</div>',
        'Copyright © 2024 • All rights reserved',
        'Price: €50 (was £45)',
        'Smart quotes: "Hello" & 'world'',
        '<script>alert("XSS attempt");</script>',
        'Math: 5 < 10 && 10 > 5'
      ],
      decode: [
        '&lt;div class=&quot;example&quot;&gt;Hello &amp; welcome to &quot;HTML&quot; encoding!&lt;/div&gt;',
        'Copyright &copy; 2024 &bull; All rights reserved',
        'Price: &euro;50 (was &pound;45)',
        'Smart quotes: &ldquo;Hello&rdquo; &amp; &lsquo;world&rsquo;',
        '&lt;script&gt;alert(&quot;XSS attempt&quot;);&lt;/script&gt;',
        'Math: 5 &lt; 10 &amp;&amp; 10 &gt; 5'
      ]
    };

    const sampleArray = samples[mode as keyof typeof samples];
    const randomSample = sampleArray[Math.floor(Math.random() * sampleArray.length)];
    setInputText(randomSample);
  };

  const getStats = () => {
    const inputLength = inputText.length;
    const outputLength = result.length;
    const entityCount = mode === 'encode' 
      ? (result.match(/&[a-zA-Z0-9#]+;/g) || []).length
      : (inputText.match(/&[a-zA-Z0-9#]+;/g) || []).length;
    
    return {
      inputLength,
      outputLength,
      entityCount,
      operation: mode === 'encode' ? 'Encoding' : 'Decoding'
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-amber-50 via-yellow-50 to-orange-50 border-amber-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-amber-800">
            <div className="p-2 bg-amber-100 rounded-lg">
              <Code className="h-6 w-6 text-amber-600" />
            </div>
            {title || 'HTML Encoder/Decoder'}
          </CardTitle>
          <p className="text-amber-600 text-sm">
            Encode or decode HTML entities and special characters for web development
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Mode and Type Selection */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Operation:</label>
              <Select value={mode} onValueChange={setMode}>
                <SelectTrigger className="border-2 border-amber-200 focus:border-amber-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="encode">
                    <div className="flex items-center gap-2">
                      <span>🔒</span>
                      <span>Encode</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="decode">
                    <div className="flex items-center gap-2">
                      <span>🔓</span>
                      <span>Decode</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Encoding Type:</label>
              <Select value={encodingType} onValueChange={setEncodingType}>
                <SelectTrigger className="border-2 border-amber-200 focus:border-amber-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {encodingTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-gray-500">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Options */}
          <div className="flex items-center space-x-2">
            <Switch
              id="numeric-entities"
              checked={useNumericEntities}
              onCheckedChange={setUseNumericEntities}
              disabled={mode === 'decode' || encodingType === 'basic'}
            />
            <label htmlFor="numeric-entities" className="text-sm font-medium">
              Use Numeric Entities (&#123;) instead of Named Entities (&amp;)
            </label>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span>📝</span>
                {mode === 'encode' ? 'HTML to Encode:' : 'HTML to Decode:'}
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleData}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                Sample
              </Button>
            </div>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={mode === 'encode' 
                ? "Enter HTML content to encode..." 
                : "Enter HTML entities to decode..."
              }
              className="min-h-[120px] resize-none border-2 border-amber-200 focus:border-amber-400 font-mono text-sm"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.operation}
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                📏 {stats.inputLength} → {stats.outputLength} chars
              </Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                🏷️ {stats.entityCount} entities
              </Badge>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              onClick={swapInputOutput}
              disabled={!result}
              className="flex items-center gap-2"
            >
              <ArrowUpDown className="h-4 w-4" />
              Swap & Switch Mode
            </Button>
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* Result */}
          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  {mode === 'encode' ? 'Encoded HTML:' : 'Decoded HTML:'}
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[120px] bg-gray-50 border-2 border-gray-200 font-mono text-sm"
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Common HTML Entities
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Basic Characters:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2 font-mono text-xs">
                <li>&amp; → &amp;amp;</li>
                <li>&lt; → &amp;lt;</li>
                <li>&gt; → &amp;gt;</li>
                <li>" → &amp;quot;</li>
                <li>' → &amp;#39;</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Special Symbols:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2 font-mono text-xs">
                <li>© → &amp;copy;</li>
                <li>® → &amp;reg;</li>
                <li>™ → &amp;trade;</li>
                <li>€ → &amp;euro;</li>
                <li>£ → &amp;pound;</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-purple-600">Typography:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2 font-mono text-xs">
                <li>" → &amp;ldquo;</li>
                <li>" → &amp;rdquo;</li>
                <li>— → &amp;mdash;</li>
                <li>… → &amp;hellip;</li>
                <li>• → &amp;bull;</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default HtmlEncoder;
