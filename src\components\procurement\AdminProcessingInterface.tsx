import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  DollarSign, 
  Package, 
  CheckCircle, 
  Send, 
  Eye,
  Calendar,
  User,
  Building,
  Receipt,
  Truck
} from 'lucide-react';
import { useProcurementRequests, useProcurementMutations } from '@/hooks/useProcurement';
import { ProcurementRequestDetails } from './ProcurementRequestDetails';
import { useA<PERSON> } from '@/components/auth/AuthProvider';

interface InvoiceFormProps {
  request: any;
  onSubmit: (invoiceData: any) => void;
  isLoading: boolean;
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ request, onSubmit, isLoading }) => {
  const [formData, setFormData] = useState({
    vendor_name: '',
    vendor_address: '',
    vendor_contact: '',
    vendor_email: '',
    vendor_tax_id: '',
    payment_terms: '30 days',
    payment_method: 'bank_transfer',
    due_date: '',
    notes: '',
    tax_rate: 0,
    discount_amount: 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const invoiceData = {
      procurement_request_id: request.id,
      ...formData,
      subtotal: request.estimated_total_cost,
      due_date: formData.due_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    };
    
    onSubmit(invoiceData);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateTotal = () => {
    const subtotal = request.estimated_total_cost;
    const taxAmount = subtotal * (formData.tax_rate / 100);
    return subtotal + taxAmount - formData.discount_amount;
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="vendor_name">Vendor Name *</Label>
          <Input
            id="vendor_name"
            value={formData.vendor_name}
            onChange={(e) => setFormData({ ...formData, vendor_name: e.target.value })}
            required
          />
        </div>

        <div>
          <Label htmlFor="vendor_contact">Vendor Contact</Label>
          <Input
            id="vendor_contact"
            value={formData.vendor_contact}
            onChange={(e) => setFormData({ ...formData, vendor_contact: e.target.value })}
            placeholder="Phone or email"
          />
        </div>

        <div className="md:col-span-2">
          <Label htmlFor="vendor_address">Vendor Address</Label>
          <Textarea
            id="vendor_address"
            value={formData.vendor_address}
            onChange={(e) => setFormData({ ...formData, vendor_address: e.target.value })}
            rows={2}
          />
        </div>

        <div>
          <Label htmlFor="vendor_email">Vendor Email</Label>
          <Input
            id="vendor_email"
            type="email"
            value={formData.vendor_email}
            onChange={(e) => setFormData({ ...formData, vendor_email: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="vendor_tax_id">Vendor Tax ID</Label>
          <Input
            id="vendor_tax_id"
            value={formData.vendor_tax_id}
            onChange={(e) => setFormData({ ...formData, vendor_tax_id: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="payment_terms">Payment Terms</Label>
          <Select value={formData.payment_terms} onValueChange={(value) => setFormData({ ...formData, payment_terms: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="immediate">Immediate</SelectItem>
              <SelectItem value="15 days">15 days</SelectItem>
              <SelectItem value="30 days">30 days</SelectItem>
              <SelectItem value="45 days">45 days</SelectItem>
              <SelectItem value="60 days">60 days</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="payment_method">Payment Method</Label>
          <Select value={formData.payment_method} onValueChange={(value) => setFormData({ ...formData, payment_method: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
              <SelectItem value="check">Check</SelectItem>
              <SelectItem value="credit_card">Credit Card</SelectItem>
              <SelectItem value="cash">Cash</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="due_date">Due Date</Label>
          <Input
            id="due_date"
            type="date"
            value={formData.due_date}
            onChange={(e) => setFormData({ ...formData, due_date: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="tax_rate">Tax Rate (%)</Label>
          <Input
            id="tax_rate"
            type="number"
            step="0.01"
            min="0"
            max="100"
            value={formData.tax_rate}
            onChange={(e) => setFormData({ ...formData, tax_rate: parseFloat(e.target.value) || 0 })}
          />
        </div>

        <div>
          <Label htmlFor="discount_amount">Discount Amount ($)</Label>
          <Input
            id="discount_amount"
            type="number"
            step="0.01"
            min="0"
            value={formData.discount_amount}
            onChange={(e) => setFormData({ ...formData, discount_amount: parseFloat(e.target.value) || 0 })}
          />
        </div>
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
          placeholder="Additional notes for the invoice..."
          rows={3}
        />
      </div>

      {/* Invoice Summary */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium mb-2">Invoice Summary</h4>
        <div className="space-y-1 text-sm">
          <div className="flex justify-between">
            <span>Subtotal:</span>
            <span>{formatCurrency(request.estimated_total_cost)}</span>
          </div>
          <div className="flex justify-between">
            <span>Tax ({formData.tax_rate}%):</span>
            <span>{formatCurrency(request.estimated_total_cost * (formData.tax_rate / 100))}</span>
          </div>
          <div className="flex justify-between">
            <span>Discount:</span>
            <span>-{formatCurrency(formData.discount_amount)}</span>
          </div>
          <div className="flex justify-between font-medium border-t pt-1">
            <span>Total:</span>
            <span>{formatCurrency(calculateTotal())}</span>
          </div>
        </div>
      </div>

      <Button type="submit" disabled={isLoading} className="w-full">
        <Receipt className="h-4 w-4 mr-2" />
        {isLoading ? 'Generating Invoice...' : 'Generate Invoice'}
      </Button>
    </form>
  );
};

export const AdminProcessingInterface: React.FC = () => {
  const { userProfile } = useAuth();
  const isAccountant = userProfile?.role === 'accountant';
  
  // Get requests based on role
  const statusFilter = isAccountant ? 'accountant_review' : 'admin_processing';
  const { requests, loading } = useProcurementRequests({ status: statusFilter });
  const { createInvoice, markAsProcured, isCreatingInvoice, isMarkingAsProcured } = useProcurementMutations();
  
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [showInvoiceDialog, setShowInvoiceDialog] = useState(false);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [activeTab, setActiveTab] = useState('pending');

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const handleCreateInvoice = (invoiceData: any) => {
    createInvoice(invoiceData);
    setShowInvoiceDialog(false);
  };

  const handleMarkAsProcured = (requestId: string) => {
    markAsProcured(requestId);
  };

  const openInvoiceDialog = (request: any) => {
    setSelectedRequest(request);
    setShowInvoiceDialog(true);
  };

  const openDetailsDialog = (request: any) => {
    setSelectedRequest(request);
    setShowDetailsDialog(true);
  };

  // Filter requests by status for tabs
  const pendingRequests = requests.filter(r => r.status === statusFilter);
  const invoiceGeneratedRequests = requests.filter(r => r.status === 'invoice_generated');

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">
          {isAccountant ? 'Procurement Accounting' : 'Procurement Processing'}
        </h1>
        <p className="text-muted-foreground">
          {isAccountant 
            ? 'Generate invoices and manage procurement payments' 
            : 'Process approved procurement requests and coordinate with accounting'
          }
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {isAccountant ? 'Awaiting Invoice' : 'For Processing'}
                </p>
                <p className="text-2xl font-bold">{pendingRequests.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Invoice Generated</p>
                <p className="text-2xl font-bold">{invoiceGeneratedRequests.length}</p>
              </div>
              <Receipt className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(requests.reduce((sum, r) => sum + r.estimated_total_cost, 0))}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Procurement Requests</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="pending">
                {isAccountant ? 'Awaiting Invoice' : 'For Processing'} ({pendingRequests.length})
              </TabsTrigger>
              <TabsTrigger value="invoiced">
                Invoice Generated ({invoiceGeneratedRequests.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="pending" className="mt-4">
              {pendingRequests.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
                  <p className="text-muted-foreground">No requests pending processing</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {pendingRequests.map((request) => (
                    <div key={request.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{request.title}</h3>
                            <Badge className="bg-purple-100 text-purple-800">
                              {isAccountant ? 'ACCOUNTANT REVIEW' : 'ADMIN PROCESSING'}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-2">
                            Request #{request.request_number} • {request.requested_by_profile?.full_name}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(request.created_at)}
                            </span>
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              {formatCurrency(request.estimated_total_cost)}
                            </span>
                            <span className="flex items-center gap-1">
                              <Package className="h-4 w-4" />
                              {request.procurement_items?.length || 0} items
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openDetailsDialog(request)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                          
                          {isAccountant && (
                            <Button
                              size="sm"
                              onClick={() => openInvoiceDialog(request)}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              <Receipt className="h-4 w-4 mr-1" />
                              Create Invoice
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="invoiced" className="mt-4">
              {invoiceGeneratedRequests.length === 0 ? (
                <div className="text-center py-8">
                  <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-muted-foreground">No invoices generated yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {invoiceGeneratedRequests.map((request) => (
                    <div key={request.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{request.title}</h3>
                            <Badge className="bg-indigo-100 text-indigo-800">
                              INVOICE GENERATED
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-2">
                            Request #{request.request_number} • {request.requested_by_profile?.full_name}
                          </p>
                          
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              {formatDate(request.created_at)}
                            </span>
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              {formatCurrency(request.estimated_total_cost)}
                            </span>
                            <span className="flex items-center gap-1">
                              <Package className="h-4 w-4" />
                              {request.procurement_items?.length || 0} items
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openDetailsDialog(request)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Details
                          </Button>
                          
                          <Button
                            size="sm"
                            onClick={() => handleMarkAsProcured(request.id)}
                            disabled={isMarkingAsProcured}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            <Truck className="h-4 w-4 mr-1" />
                            Mark Procured
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Invoice Dialog */}
      <Dialog open={showInvoiceDialog} onOpenChange={setShowInvoiceDialog}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Generate Invoice</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <InvoiceForm
              request={selectedRequest}
              onSubmit={handleCreateInvoice}
              isLoading={isCreatingInvoice}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Details Dialog */}
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Procurement Request Details</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <ProcurementRequestDetails 
              request={selectedRequest}
              onClose={() => setShowDetailsDialog(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
