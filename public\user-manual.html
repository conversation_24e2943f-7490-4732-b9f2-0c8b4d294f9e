<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTNL AI WORK-BOARD - User Manual</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            padding: 2rem 0;
            border-bottom: 1px solid #333;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            color: #888;
            max-width: 600px;
        }

        /* Navigation */
        .nav {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            margin: 2rem 0;
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .nav-toggle {
            display: none;
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            padding: 0.75rem;
            color: #ffffff;
            cursor: pointer;
            margin-bottom: 1rem;
            width: 100%;
            text-align: center;
            font-weight: 500;
        }

        .nav-toggle:hover {
            background: rgba(220, 38, 38, 0.2);
        }

        .nav-content {
            transition: all 0.3s ease;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            align-items: stretch;
        }

        .nav-item {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #ffffff;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(220, 38, 38, 0.2);
            border-color: rgba(220, 38, 38, 0.5);
            transform: translateY(-2px);
        }

        /* Role Cards */
        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
            align-items: start;
        }

        .role-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        }

        .role-card:hover {
            transform: translateY(-5px);
            border-color: rgba(220, 38, 38, 0.3);
            box-shadow: 0 20px 40px rgba(220, 38, 38, 0.1);
        }

        .role-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .role-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }

        .role-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ffffff;
        }

        .role-description {
            color: #888;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        .features-list {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .features-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ccc;
            font-size: 0.9rem;
        }

        .features-list li:last-child {
            border-bottom: none;
        }

        .features-list li::before {
            content: '✓';
            color: #dc2626;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        .access-button {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            width: 100%;
            text-align: center;
        }

        .access-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 38, 38, 0.3);
        }

        /* Feature Sections */
        .feature-section {
            margin: 4rem 0;
            padding: 3rem 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
            align-items: start;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: rgba(220, 38, 38, 0.3);
            transform: translateY(-3px);
        }

        .feature-card h3 {
            color: #ffffff;
            margin-bottom: 0.5rem;
            font-size: 1.2rem;
        }

        .feature-card p {
            color: #888;
            font-size: 0.9rem;
        }

        /* Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
            align-items: stretch;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #dc2626;
            display: block;
        }

        .stat-label {
            color: #888;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* Footer */
        .footer {
            background: rgba(255, 255, 255, 0.05);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 3rem 0;
            margin-top: 4rem;
            text-align: center;
        }

        .footer-content {
            max-width: 600px;
            margin: 0 auto;
        }

        .footer h3 {
            color: #ffffff;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .footer p {
            color: #888;
            margin-bottom: 1.5rem;
        }

        .support-button {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            color: #ffffff;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .support-button:hover {
            background: rgba(220, 38, 38, 0.2);
            border-color: rgba(220, 38, 38, 0.5);
        }

        /* Enhanced Responsive Design */

        /* Large Desktop (1200px+) */
        @media (min-width: 1200px) {
            .container {
                max-width: 1400px;
                padding: 0 40px;
            }

            .roles-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 3rem;
            }

            .nav-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }

        /* Tablet Landscape (992px - 1199px) */
        @media (max-width: 1199px) and (min-width: 992px) {
            .container {
                padding: 0 30px;
            }

            .roles-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 2rem;
            }

            .nav-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .header h1 {
                font-size: 2.5rem;
            }
        }

        /* Tablet Portrait (768px - 991px) */
        @media (max-width: 991px) and (min-width: 768px) {
            .container {
                padding: 0 25px;
            }

            .roles-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .nav-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .header h1 {
                font-size: 2.2rem;
            }

            .role-card {
                padding: 1.5rem;
            }

            .nav {
                position: relative;
                top: 0;
            }
        }

        /* Mobile Landscape (576px - 767px) */
        @media (max-width: 767px) and (min-width: 576px) {
            .container {
                padding: 0 20px;
            }

            .header {
                padding: 1.5rem 0;
            }

            .header h1 {
                font-size: 2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .roles-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin: 2rem 0;
            }

            .nav-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .nav-item {
                padding: 1rem;
                font-size: 0.95rem;
            }

            .role-card {
                padding: 1.5rem;
            }

            .role-icon {
                width: 40px;
                height: 40px;
                font-size: 1.25rem;
            }

            .role-title {
                font-size: 1.25rem;
            }

            .feature-title {
                font-size: 1.8rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .nav {
                position: relative;
                top: 0;
                margin: 1.5rem 0;
            }

            .nav-toggle {
                display: block;
            }

            .nav-content.collapsed {
                display: none;
            }
        }

        /* Mobile Portrait (up to 575px) */
        @media (max-width: 575px) {
            .container {
                padding: 0 15px;
            }

            .header {
                padding: 1rem 0;
                text-align: center;
            }

            .header h1 {
                font-size: 1.75rem;
                margin-bottom: 0.75rem;
            }

            .header p {
                font-size: 0.9rem;
                margin: 0 auto;
            }

            .roles-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
                margin: 1.5rem 0;
            }

            .nav-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .nav-item {
                padding: 0.875rem;
                font-size: 0.9rem;
                border-radius: 6px;
            }

            .role-card {
                padding: 1.25rem;
                border-radius: 12px;
            }

            .role-header {
                flex-direction: column;
                text-align: center;
                margin-bottom: 1rem;
            }

            .role-icon {
                width: 36px;
                height: 36px;
                font-size: 1.1rem;
                margin: 0 0 0.75rem 0;
            }

            .role-title {
                font-size: 1.1rem;
            }

            .role-description {
                font-size: 0.85rem;
                text-align: center;
            }

            .features-list li {
                font-size: 0.85rem;
                padding: 0.4rem 0;
            }

            .access-button {
                padding: 0.875rem 1rem;
                font-size: 0.9rem;
                border-radius: 6px;
            }

            .feature-title {
                font-size: 1.5rem;
                text-align: center;
            }

            .feature-section {
                margin: 2rem 0;
                padding: 2rem 0;
            }

            .feature-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .feature-card {
                padding: 1rem;
            }

            .feature-card h3 {
                font-size: 1.1rem;
            }

            .feature-card p {
                font-size: 0.85rem;
            }

            .nav {
                position: relative;
                top: 0;
                margin: 1rem 0;
                padding: 0.75rem;
            }
        }

        /* Extra Small Mobile (up to 375px) */
        @media (max-width: 375px) {
            .container {
                padding: 0 10px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .header p {
                font-size: 0.8rem;
            }

            .nav-item {
                padding: 0.75rem;
                font-size: 0.85rem;
            }

            .role-card {
                padding: 1rem;
            }

            .role-title {
                font-size: 1rem;
            }

            .role-description {
                font-size: 0.8rem;
            }

            .features-list li {
                font-size: 0.8rem;
            }

            .access-button {
                padding: 0.75rem;
                font-size: 0.85rem;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .role-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .role-card:nth-child(2) { animation-delay: 0.1s; }
        .role-card:nth-child(3) { animation-delay: 0.2s; }
        .role-card:nth-child(4) { animation-delay: 0.3s; }
        .role-card:nth-child(5) { animation-delay: 0.4s; }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .nav-item,
            .access-button,
            .support-button {
                min-height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .role-card:hover,
            .feature-card:hover,
            .nav-item:hover,
            .access-button:hover,
            .support-button:hover {
                transform: none;
            }

            .role-card:active,
            .feature-card:active {
                transform: scale(0.98);
            }

            .nav-item:active,
            .access-button:active,
            .support-button:active {
                transform: scale(0.95);
                opacity: 0.8;
            }
        }

        /* Smooth scrolling for anchor links */
        html {
            scroll-behavior: smooth;
        }

        /* Focus styles for accessibility */
        .nav-item:focus,
        .access-button:focus,
        .support-button:focus {
            outline: 2px solid #dc2626;
            outline-offset: 2px;
        }

        /* Print styles */
        @media print {
            .nav,
            .access-button,
            .support-button {
                display: none;
            }

            .role-card,
            .feature-card,
            .stat-card {
                break-inside: avoid;
                page-break-inside: avoid;
            }

            body {
                background: white;
                color: black;
            }

            .header,
            .feature-section {
                border: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>CTNL AI WORK-BOARD</h1>
            <p>Comprehensive User Manual - Your complete guide to the AI-powered workforce management system</p>
            <div style="margin-top: 1.5rem; text-align: center;">
                <a href="comprehensive-manual.html" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 1rem 2rem; border-radius: 12px; text-decoration: none; font-weight: 600; display: inline-block; box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3); transition: all 0.3s ease;">
                    🚀 NEW: Complete System Manual with Voice Commands & AI Features
                </a>
                <p style="color: #888; margin-top: 0.5rem; font-size: 0.9rem;">
                    Includes voice command system, AI integration, self-healing features, and comprehensive guides
                </p>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Quick Navigation -->
        <nav class="nav">
            <button class="nav-toggle" onclick="toggleNav()">📱 Navigation Menu</button>
            <div class="nav-content" id="navContent">
                <div class="nav-grid">
                    <a href="#admin" class="nav-item">👑 Admin Dashboard</a>
                    <a href="#manager" class="nav-item">👥 Manager Portal</a>
                    <a href="#accountant" class="nav-item">💰 Accountant Tools</a>
                    <a href="#staff-admin" class="nav-item">📋 Staff Admin</a>
                    <a href="#staff" class="nav-item">⚡ Staff Portal</a>
                    <a href="#features" class="nav-item">🚀 Key Features</a>
                </div>
            </div>
        </nav>

        <!-- System Overview -->
        <div class="feature-section">
            <h2 class="feature-title">System Overview</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">5</span>
                    <div class="stat-label">User Roles</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">27</span>
                    <div class="stat-label">Database Tables</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">15+</span>
                    <div class="stat-label">AI Functions</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">Mobile Responsive</div>
                </div>
            </div>
        </div>

        <!-- Role-Based Access -->
        <div class="roles-grid">
            <!-- Admin Role -->
            <div class="role-card" id="admin">
                <div class="role-header">
                    <div class="role-icon">👑</div>
                    <div class="role-title">Admin Dashboard</div>
                </div>
                <div class="role-description">
                    Full system access with comprehensive management capabilities for all organizational operations.
                </div>
                <ul class="features-list">
                    <li>Complete user management and role assignment</li>
                    <li>Department creation and management</li>
                    <li>Project oversight and resource allocation</li>
                    <li>AI system configuration and monitoring</li>
                    <li>Financial data access and reporting</li>
                    <li>System activities and audit logs</li>
                    <li>Communication center management</li>
                    <li>Time tracking and attendance monitoring</li>
                    <li>Construction and fleet management</li>
                    <li>Battery and asset management</li>
                    <li>Notification system administration</li>
                </ul>
                <a href="https://ai.ctnigeria.com/dashboard/admin" class="access-button">Access Admin Dashboard</a>
            </div>

            <!-- Manager Role -->
            <div class="role-card" id="manager">
                <div class="role-header">
                    <div class="role-icon">👥</div>
                    <div class="role-title">Manager Portal</div>
                </div>
                <div class="role-description">
                    Team leadership tools with project management and departmental oversight capabilities.
                </div>
                <ul class="features-list">
                    <li>Team management and supervision</li>
                    <li>Project planning and tracking</li>
                    <li>Task assignment and monitoring</li>
                    <li>Performance analytics and reporting</li>
                    <li>Meeting scheduling and management</li>
                    <li>Invoice and expense oversight</li>
                    <li>Time attendance monitoring</li>
                    <li>Construction project management</li>
                    <li>Fleet operations oversight</li>
                    <li>Financial reporting access</li>
                </ul>
                <a href="https://ai.ctnigeria.com/dashboard/manager" class="access-button">Access Manager Portal</a>
            </div>

            <!-- Accountant Role -->
            <div class="role-card" id="accountant">
                <div class="role-header">
                    <div class="role-icon">💰</div>
                    <div class="role-title">Accountant Tools</div>
                </div>
                <div class="role-description">
                    Financial management suite with comprehensive accounting and reporting capabilities.
                </div>
                <ul class="features-list">
                    <li>Invoice creation and management</li>
                    <li>Expense tracking and categorization</li>
                    <li>Financial reporting and analytics</li>
                    <li>Asset management and valuation</li>
                    <li>Budget monitoring and forecasting</li>
                    <li>Payment processing oversight</li>
                    <li>Tax preparation assistance</li>
                    <li>Audit trail maintenance</li>
                    <li>Cost center analysis</li>
                    <li>Financial dashboard insights</li>
                </ul>
                <a href="https://ai.ctnigeria.com/dashboard/accountant" class="access-button">Access Accountant Tools</a>
            </div>

            <!-- Staff Admin Role -->
            <div class="role-card" id="staff-admin">
                <div class="role-header">
                    <div class="role-icon">📋</div>
                    <div class="role-title">Staff Admin</div>
                </div>
                <div class="role-description">
                    Administrative support functions with specialized access to staff operations and documentation.
                </div>
                <ul class="features-list">
                    <li>Staff documentation management</li>
                    <li>Administrative task coordination</li>
                    <li>Expense report processing</li>
                    <li>Fleet administration support</li>
                    <li>Document archive management</li>
                    <li>Communication facilitation</li>
                    <li>Meeting coordination</li>
                    <li>Resource allocation support</li>
                    <li>Compliance monitoring</li>
                    <li>Administrative reporting</li>
                </ul>
                <a href="https://ai.ctnigeria.com/dashboard/staff-admin" class="access-button">Access Staff Admin</a>
            </div>

            <!-- Staff Role -->
            <div class="role-card" id="staff">
                <div class="role-header">
                    <div class="role-icon">⚡</div>
                    <div class="role-title">Staff Portal</div>
                </div>
                <div class="role-description">
                    Personal workspace for task management, time tracking, and individual productivity tools.
                </div>
                <ul class="features-list">
                    <li>Personal task management</li>
                    <li>Time clock-in/clock-out system</li>
                    <li>Project progress tracking</li>
                    <li>Task status updates</li>
                    <li>Personal reporting dashboard</li>
                    <li>Battery report submissions</li>
                    <li>TOOLZ access for utilities</li>
                    <li>Memo and communication access</li>
                    <li>Leave request submissions</li>
                    <li>Personal profile management</li>
                </ul>
                <a href="https://ai.ctnigeria.com/dashboard/staff" class="access-button">Access Staff Portal</a>
            </div>
        </div>
        </div>

        <!-- Key Features Section -->
        <div class="feature-section" id="features">
            <h2 class="feature-title">Key Features</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🤖 AI Assistant</h3>
                    <p>Advanced AI-powered assistant for document analysis, task automation, and intelligent insights across all operations.</p>
                </div>
                <div class="feature-card">
                    <h3>⏰ Time Tracking</h3>
                    <p>Comprehensive time attendance system with GPS location tracking, device information, and real-time clock-in/out capabilities.</p>
                </div>
                <div class="feature-card">
                    <h3>📊 Project Management</h3>
                    <p>Full project lifecycle management with Kanban boards, progress tracking, team assignments, and milestone monitoring.</p>
                </div>
                <div class="feature-card">
                    <h3>💼 Financial Management</h3>
                    <p>Complete financial suite including invoicing, expense tracking, budget management, and comprehensive reporting.</p>
                </div>
                <div class="feature-card">
                    <h3>🚗 Fleet Management</h3>
                    <p>Vehicle tracking, maintenance scheduling, fuel monitoring, and comprehensive fleet analytics.</p>
                </div>
                <div class="feature-card">
                    <h3>🏗️ Construction Management</h3>
                    <p>Construction project oversight, resource allocation, progress monitoring, and safety compliance tracking.</p>
                </div>
                <div class="feature-card">
                    <h3>🔋 Battery Management</h3>
                    <p>Battery monitoring system for telecom sites with performance analytics and maintenance scheduling.</p>
                </div>
                <div class="feature-card">
                    <h3>📱 Mobile Responsive</h3>
                    <p>Fully responsive design optimized for all devices with native mobile app experience.</p>
                </div>
                <div class="feature-card">
                    <h3>🔔 Notifications</h3>
                    <p>Real-time email and in-app notifications with customizable preferences and delivery tracking.</p>
                </div>
                <div class="feature-card">
                    <h3>🛠️ TOOLZ Integration</h3>
                    <p>Comprehensive omni-tools integration for utilities, calculators, and productivity enhancements.</p>
                </div>
                <div class="feature-card">
                    <h3>📄 Document Management</h3>
                    <p>AI-powered document analysis, categorization, and intelligent search capabilities.</p>
                </div>
                <div class="feature-card">
                    <h3>🔐 Security & Access Control</h3>
                    <p>Role-based access control with comprehensive security measures and audit trails.</p>
                </div>
            </div>
        </div>

        <!-- Getting Started Guide -->
        <div class="feature-section">
            <h2 class="feature-title">Getting Started</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>1. Account Access</h3>
                    <p>Visit <strong>ai.ctnigeria.com</strong> and sign in with your assigned credentials. Your role determines your dashboard access level.</p>
                </div>
                <div class="feature-card">
                    <h3>2. Dashboard Navigation</h3>
                    <p>Use the sidebar navigation to access different modules. Each role has customized menu items based on permissions.</p>
                </div>
                <div class="feature-card">
                    <h3>3. Time Tracking</h3>
                    <p>Clock in/out using the time card on your dashboard. Location and device information are automatically captured.</p>
                </div>
                <div class="feature-card">
                    <h3>4. Task Management</h3>
                    <p>View assigned tasks, update progress, and submit reports through your role-specific task management interface.</p>
                </div>
                <div class="feature-card">
                    <h3>5. AI Assistant</h3>
                    <p>Access the AI assistant for document analysis, task automation, and intelligent insights to enhance productivity.</p>
                </div>
                <div class="feature-card">
                    <h3>6. Notifications</h3>
                    <p>Configure email and in-app notification preferences in your settings to stay updated on important activities.</p>
                </div>
            </div>
        </div>

        <!-- Support & Contact -->
        <div class="footer">
            <div class="footer-content">
                <h3>Need Help?</h3>
                <p>Our support team is here to assist you with any questions or technical issues you may encounter while using the CTNL AI WORK-BOARD system.</p>
                <a href="mailto:<EMAIL>" class="support-button">Contact Support</a>
                <div style="margin-top: 2rem; padding-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1);">
                    <p style="color: #666; font-size: 0.9rem;">
                        © 2024 CTNL AI WORK-BOARD. All rights reserved.<br>
                        Powered by advanced AI technology for workforce management excellence.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Mobile navigation toggle
        function toggleNav() {
            const navContent = document.getElementById('navContent');
            const navToggle = document.querySelector('.nav-toggle');

            if (navContent.classList.contains('collapsed')) {
                navContent.classList.remove('collapsed');
                navToggle.innerHTML = '📱 Hide Menu';
            } else {
                navContent.classList.add('collapsed');
                navToggle.innerHTML = '📱 Navigation Menu';
            }
        }

        // Auto-collapse navigation on mobile when clicking nav items
        document.querySelectorAll('.nav-item').forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 767) {
                    const navContent = document.getElementById('navContent');
                    const navToggle = document.querySelector('.nav-toggle');
                    navContent.classList.add('collapsed');
                    navToggle.innerHTML = '📱 Navigation Menu';
                }
            });
        });

        // Responsive navigation handling
        function handleResize() {
            const navContent = document.getElementById('navContent');
            const navToggle = document.querySelector('.nav-toggle');

            if (window.innerWidth > 767) {
                navContent.classList.remove('collapsed');
                navToggle.innerHTML = '📱 Navigation Menu';
            }
        }

        window.addEventListener('resize', handleResize);

        // Smooth scrolling for navigation links
        document.querySelectorAll('.nav-item').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation to access buttons
        document.querySelectorAll('.access-button').forEach(button => {
            button.addEventListener('click', function() {
                this.style.opacity = '0.7';
                this.innerHTML = 'Loading...';
            });
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards and stat cards
        document.querySelectorAll('.feature-card, .stat-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // Performance optimization: Lazy load images if any
        if ('loading' in HTMLImageElement.prototype) {
            const images = document.querySelectorAll('img[data-src]');
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        }

        // Add keyboard navigation support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const navContent = document.getElementById('navContent');
                const navToggle = document.querySelector('.nav-toggle');
                if (window.innerWidth <= 767 && !navContent.classList.contains('collapsed')) {
                    navContent.classList.add('collapsed');
                    navToggle.innerHTML = '📱 Navigation Menu';
                }
            }
        });

        // Initialize responsive behavior on load
        document.addEventListener('DOMContentLoaded', function() {
            handleResize();
        });
    </script>
</body>
</html>
