import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyMigration() {
  console.log('🚀 Applying missing tables migration...\n');

  try {
    // Read the migration file
    const migrationPath = join(process.cwd(), 'supabase/migrations/20250707130000_sync_missing_tables.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf8');

    console.log('📄 Migration file loaded successfully');
    console.log(`📏 Migration size: ${migrationSQL.length} characters\n`);

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`🔧 Found ${statements.length} SQL statements to execute\n`);

    // Execute each statement
    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';
      
      try {
        console.log(`⏳ Executing statement ${i + 1}/${statements.length}...`);
        
        // Use rpc to execute raw SQL
        const { data, error } = await supabase.rpc('exec_sql', { 
          sql_query: statement 
        });

        if (error) {
          // If rpc doesn't work, try direct execution for specific statements
          if (statement.includes('CREATE TABLE') || statement.includes('CREATE INDEX') || statement.includes('ALTER TABLE')) {
            console.log(`⚠️  RPC failed, attempting direct execution: ${error.message}`);
            
            // For table creation, we can use the from() method with a dummy query to test if table exists
            if (statement.includes('CREATE TABLE IF NOT EXISTS')) {
              const tableName = statement.match(/CREATE TABLE IF NOT EXISTS public\.(\w+)/)?.[1];
              if (tableName) {
                try {
                  await supabase.from(tableName).select('*').limit(1);
                  console.log(`✅ Table '${tableName}' already exists`);
                  successCount++;
                  continue;
                } catch (tableError) {
                  console.log(`❌ Table '${tableName}' needs to be created but cannot execute DDL`);
                  errorCount++;
                  continue;
                }
              }
            }
          }
          
          console.log(`❌ Error in statement ${i + 1}: ${error.message}`);
          errorCount++;
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`);
          successCount++;
        }
      } catch (err: any) {
        console.log(`❌ Exception in statement ${i + 1}: ${err.message}`);
        errorCount++;
      }
    }

    console.log(`\n📊 Migration Summary:`);
    console.log(`✅ Successful statements: ${successCount}`);
    console.log(`❌ Failed statements: ${errorCount}`);
    console.log(`📈 Success rate: ${((successCount / statements.length) * 100).toFixed(1)}%`);

    if (errorCount > 0) {
      console.log('\n⚠️  Some statements failed. This might be expected for DDL operations.');
      console.log('💡 The database might need manual schema updates via Supabase Dashboard.');
    }

  } catch (error: any) {
    console.error('💥 Migration failed:', error.message);
    process.exit(1);
  }
}

async function checkTablesAfterMigration() {
  console.log('\n🔍 Checking table status after migration...\n');

  const requiredTables = [
    'project_progress_updates',
    'email_notifications', 
    'battery_management',
    'recent_activity_logs'
  ];

  for (const table of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error) {
        console.log(`❌ Table '${table}': ${error.message}`);
      } else {
        console.log(`✅ Table '${table}': OK`);
      }
    } catch (err) {
      console.log(`❌ Table '${table}': Error checking table`);
    }
  }
}

async function main() {
  await applyMigration();
  await checkTablesAfterMigration();
  
  console.log('\n🏁 Migration process completed');
  console.log('💡 If tables are still missing, please apply the migration manually via Supabase Dashboard');
  process.exit(0);
}

main().catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
