import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { AdvancedAIService } from './advanced-ai-service';
import { VoiceRecognitionService } from './voice-recognition-service';
import { SystemLogsService } from './system-logs-service';
import { UserActivitiesService } from './user-activities-service';

/**
 * AI Voice Agent Service
 * Intelligent voice agent with full system navigation and feature assistance
 * Provides comprehensive help and guidance through voice interaction
 */

export interface VoiceAgentResponse {
  text: string;
  audioUrl?: string;
  actions?: Array<{
    type: 'navigate' | 'execute' | 'display' | 'help';
    target: string;
    data?: any;
    description: string;
  }>;
  suggestions?: string[];
  visualFeedback?: {
    type: 'highlight' | 'tooltip' | 'modal' | 'guide';
    target: string;
    content: string;
  };
  followUp?: string[];
  confidence: number;
  processingTime: number;
}

export interface NavigationContext {
  currentPage: string;
  userRole: string;
  availableFeatures: string[];
  recentActions: any[];
  systemState: any;
}

export interface VoiceAgentCapability {
  name: string;
  description: string;
  category: string;
  examples: string[];
  requiredPermissions?: string[];
  supportedLanguages: string[];
}

export class AIVoiceAgentService {
  private static isInitialized: boolean = false;
  private static currentContext: NavigationContext | null = null;
  private static capabilities: VoiceAgentCapability[] = [];
  private static knowledgeBase: Map<string, any> = new Map();
  private static sessionId: string = '';

  // Initialize AI Voice Agent
  static async initialize(): Promise<boolean> {
    try {
      console.log('🤖 Initializing AI Voice Agent Service...');

      this.sessionId = crypto.randomUUID();

      // Initialize dependencies
      await AdvancedAIService.initialize();
      await VoiceRecognitionService.initialize();

      // Load navigation context
      await this.loadNavigationContext();

      // Build knowledge base
      await this.buildVoiceAgentKnowledge();

      // Initialize capabilities
      this.initializeCapabilities();

      // Load voice agent knowledge from database
      await this.loadVoiceKnowledgeBase();

      this.isInitialized = true;
      console.log('✅ AI Voice Agent Service initialized successfully');

      await SystemLogsService.info('voice', 'AI Voice Agent initialized', 'Voice agent ready for assistance');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize AI Voice Agent:', error);
      await SystemLogsService.error('voice', 'AI Voice Agent initialization failed', error.toString());
      return false;
    }
  }

  // Process voice input and provide intelligent response
  static async processVoiceInput(input: string, context?: any): Promise<VoiceAgentResponse> {
    const startTime = Date.now();

    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log('🎤 Processing voice input:', input);

      // Analyze intent and extract entities
      const analysis = await this.analyzeVoiceInput(input);

      // Generate response based on intent
      const response = await this.generateVoiceResponse(analysis, context);

      // Log interaction
      await this.logVoiceInteraction(input, response, analysis);

      // Calculate processing time
      response.processingTime = Date.now() - startTime;

      return response;
    } catch (error) {
      console.error('Error processing voice input:', error);
      
      return {
        text: "I'm sorry, I encountered an error processing your request. Please try again or ask for help.",
        confidence: 0.1,
        processingTime: Date.now() - startTime,
        suggestions: [
          "Try rephrasing your request",
          "Ask for help with navigation",
          "Say 'show me what I can do'"
        ]
      };
    }
  }

  // Analyze voice input for intent and entities
  private static async analyzeVoiceInput(input: string): Promise<any> {
    const lowerInput = input.toLowerCase().trim();

    // Navigation intents
    if (this.matchesPatterns(lowerInput, [
      'go to', 'navigate to', 'open', 'show me', 'take me to', 'switch to'
    ])) {
      return {
        intent: 'navigation',
        confidence: 0.9,
        entities: this.extractNavigationTargets(lowerInput),
        category: 'navigation'
      };
    }

    // Help and assistance intents
    if (this.matchesPatterns(lowerInput, [
      'help', 'how do i', 'how to', 'what is', 'explain', 'guide me', 'tutorial'
    ])) {
      return {
        intent: 'help',
        confidence: 0.9,
        entities: this.extractHelpTopics(lowerInput),
        category: 'assistance'
      };
    }

    // Action intents
    if (this.matchesPatterns(lowerInput, [
      'create', 'add', 'new', 'start', 'begin', 'make'
    ])) {
      return {
        intent: 'action_create',
        confidence: 0.8,
        entities: this.extractActionTargets(lowerInput),
        category: 'action'
      };
    }

    // Query intents
    if (this.matchesPatterns(lowerInput, [
      'show', 'list', 'find', 'search', 'get', 'display'
    ])) {
      return {
        intent: 'query',
        confidence: 0.8,
        entities: this.extractQueryTargets(lowerInput),
        category: 'query'
      };
    }

    // System status intents
    if (this.matchesPatterns(lowerInput, [
      'status', 'dashboard', 'overview', 'summary', 'stats'
    ])) {
      return {
        intent: 'system_status',
        confidence: 0.9,
        entities: [],
        category: 'system'
      };
    }

    // Default to general conversation
    return {
      intent: 'conversation',
      confidence: 0.6,
      entities: [],
      category: 'general'
    };
  }

  // Generate intelligent voice response
  private static async generateVoiceResponse(analysis: any, context?: any): Promise<VoiceAgentResponse> {
    const { intent, entities, confidence, category } = analysis;

    switch (intent) {
      case 'navigation':
        return this.handleNavigationRequest(entities, context);

      case 'help':
        return this.handleHelpRequest(entities, context);

      case 'action_create':
        return this.handleActionRequest(entities, context);

      case 'query':
        return this.handleQueryRequest(entities, context);

      case 'system_status':
        return this.handleSystemStatusRequest(context);

      case 'conversation':
        return this.handleConversationRequest(entities, context);

      default:
        return this.generateDefaultResponse();
    }
  }

  // Handle navigation requests
  private static async handleNavigationRequest(entities: any[], context?: any): Promise<VoiceAgentResponse> {
    const navigationMap = {
      'dashboard': { route: '/', description: 'main dashboard' },
      'projects': { route: '/projects', description: 'projects page' },
      'tasks': { route: '/tasks', description: 'tasks management' },
      'team': { route: '/team', description: 'team members' },
      'reports': { route: '/reports', description: 'reports section' },
      'memos': { route: '/memos', description: 'memos and announcements' },
      'profile': { route: '/profile', description: 'user profile' },
      'settings': { route: '/settings', description: 'system settings' },
      'time tracking': { route: '/time-tracking', description: 'time tracking' },
      'analytics': { route: '/analytics', description: 'analytics dashboard' }
    };

    // Find matching navigation target
    const target = entities.find(entity => navigationMap[entity]);
    
    if (target && navigationMap[target]) {
      const nav = navigationMap[target];
      
      return {
        text: `Navigating to ${nav.description}. I'll take you there now.`,
        actions: [{
          type: 'navigate',
          target: nav.route,
          data: { source: 'voice_agent' },
          description: `Navigate to ${nav.description}`
        }],
        suggestions: [
          "What can I do here?",
          "Show me the main features",
          "Help me get started"
        ],
        confidence: 0.95,
        processingTime: 0
      };
    }

    // If no specific target found, provide navigation options
    return {
      text: "I can help you navigate to different parts of the system. Where would you like to go?",
      suggestions: [
        "Go to dashboard",
        "Open projects",
        "Show my tasks",
        "View team members",
        "Check reports"
      ],
      visualFeedback: {
        type: 'modal',
        target: 'navigation-help',
        content: 'Available navigation options'
      },
      confidence: 0.8,
      processingTime: 0
    };
  }

  // Handle help requests
  private static async handleHelpRequest(entities: any[], context?: any): Promise<VoiceAgentResponse> {
    const helpTopics = {
      'project': 'I can help you create, manage, and track projects. You can assign team members, set deadlines, and monitor progress.',
      'task': 'Tasks help you break down projects into manageable pieces. You can assign tasks, set priorities, and track completion.',
      'team': 'Team management lets you add members, assign roles, and organize departments for better collaboration.',
      'time tracking': 'Time tracking helps you log work hours, track project time, and generate time reports for billing.',
      'reports': 'Reports provide insights into project progress, team performance, and system usage analytics.',
      'memo': 'Memos are for company announcements, policy updates, and important communications to team members.'
    };

    const topic = entities.find(entity => helpTopics[entity]);
    
    if (topic && helpTopics[topic]) {
      return {
        text: helpTopics[topic],
        actions: [{
          type: 'help',
          target: topic,
          description: `Show detailed help for ${topic}`
        }],
        suggestions: [
          `How do I create a ${topic}?`,
          `Show me ${topic} examples`,
          `What are ${topic} best practices?`
        ],
        confidence: 0.9,
        processingTime: 0
      };
    }

    // General help response
    return {
      text: "I'm your AI assistant for CTN Nigeria. I can help you navigate the system, create projects and tasks, manage your team, track time, and much more. What would you like help with?",
      suggestions: [
        "How do I create a project?",
        "Show me my tasks",
        "Help with team management",
        "Explain time tracking",
        "What can you do?"
      ],
      visualFeedback: {
        type: 'guide',
        target: 'main-features',
        content: 'System capabilities overview'
      },
      confidence: 0.85,
      processingTime: 0
    };
  }

  // Handle action requests (create, add, etc.)
  private static async handleActionRequest(entities: any[], context?: any): Promise<VoiceAgentResponse> {
    const actionMap = {
      'project': {
        text: "I'll help you create a new project. You'll need to provide a project name, description, timeline, and team assignments.",
        route: '/projects/new',
        guide: 'project-creation'
      },
      'task': {
        text: "Let's create a new task. I'll guide you through setting the title, description, priority, and assignment.",
        route: '/tasks/new',
        guide: 'task-creation'
      },
      'memo': {
        text: "I'll help you create a new memo. You can write announcements, policy updates, or important communications.",
        route: '/memos/new',
        guide: 'memo-creation'
      },
      'report': {
        text: "Let's create a new report. I'll help you choose the type and gather the necessary information.",
        route: '/reports/new',
        guide: 'report-creation'
      }
    };

    const target = entities.find(entity => actionMap[entity]);
    
    if (target && actionMap[target]) {
      const action = actionMap[target];
      
      return {
        text: action.text,
        actions: [
          {
            type: 'navigate',
            target: action.route,
            description: `Navigate to ${target} creation`
          },
          {
            type: 'help',
            target: action.guide,
            description: `Show ${target} creation guide`
          }
        ],
        suggestions: [
          "Guide me through the process",
          "What information do I need?",
          "Show me examples"
        ],
        confidence: 0.9,
        processingTime: 0
      };
    }

    return {
      text: "I can help you create projects, tasks, memos, reports, and more. What would you like to create?",
      suggestions: [
        "Create a new project",
        "Add a task",
        "Write a memo",
        "Generate a report"
      ],
      confidence: 0.7,
      processingTime: 0
    };
  }

  // Handle query requests
  private static async handleQueryRequest(entities: any[], context?: any): Promise<VoiceAgentResponse> {
    try {
      // Get current user context
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (entities.includes('tasks') || entities.includes('my tasks')) {
        return {
          text: `Let me show you your current tasks. You can view them by status, priority, or project.`,
          actions: [{
            type: 'navigate',
            target: '/tasks/my',
            description: 'Show my tasks'
          }],
          suggestions: [
            "Show overdue tasks",
            "Filter by priority",
            "Group by project"
          ],
          confidence: 0.9,
          processingTime: 0
        };
      }

      if (entities.includes('projects') || entities.includes('my projects')) {
        return {
          text: `Here are your projects. I can show you active projects, completed ones, or filter by status.`,
          actions: [{
            type: 'navigate',
            target: '/projects',
            description: 'Show projects'
          }],
          suggestions: [
            "Show active projects",
            "Filter by status",
            "View project analytics"
          ],
          confidence: 0.9,
          processingTime: 0
        };
      }

      return {
        text: "I can help you find information about projects, tasks, team members, reports, and more. What are you looking for?",
        suggestions: [
          "Show my tasks",
          "List active projects",
          "Find team members",
          "View recent reports"
        ],
        confidence: 0.7,
        processingTime: 0
      };
    } catch (error) {
      console.error('Error handling query request:', error);
      return this.generateDefaultResponse();
    }
  }

  // Handle system status requests
  private static async handleSystemStatusRequest(context?: any): Promise<VoiceAgentResponse> {
    try {
      // Get organization overview
      const orgData = await AdvancedAIService.getOrganizationInsights();
      
      return {
        text: `Here's your system overview: You have ${orgData.context?.organization?.projects?.length || 0} projects, ${orgData.context?.organization?.members?.length || 0} team members, and ${orgData.context?.organization?.recentActivities?.length || 0} recent activities. Everything looks good!`,
        actions: [{
          type: 'navigate',
          target: '/',
          description: 'View dashboard'
        }],
        suggestions: [
          "Show detailed analytics",
          "View recent activities",
          "Check project status"
        ],
        confidence: 0.9,
        processingTime: 0
      };
    } catch (error) {
      return {
        text: "I can show you the system status and overview. Let me take you to the dashboard where you can see all the key metrics.",
        actions: [{
          type: 'navigate',
          target: '/',
          description: 'View dashboard'
        }],
        confidence: 0.8,
        processingTime: 0
      };
    }
  }

  // Handle general conversation
  private static async handleConversationRequest(entities: any[], context?: any): Promise<VoiceAgentResponse> {
    // Use the advanced AI service for general conversation
    const aiResponse = await AdvancedAIService.processMessage(entities.join(' '));
    
    return {
      text: aiResponse.message,
      actions: aiResponse.actions,
      suggestions: aiResponse.suggestions || [
        "What can you help me with?",
        "Show me around the system",
        "Help me get started"
      ],
      confidence: aiResponse.confidence || 0.8,
      processingTime: aiResponse.processingTime || 0
    };
  }

  // Generate default response
  private static generateDefaultResponse(): VoiceAgentResponse {
    return {
      text: "I'm your AI assistant for CTN Nigeria. I can help you navigate the system, manage projects and tasks, work with your team, and much more. How can I assist you today?",
      suggestions: [
        "Show me what you can do",
        "Help me navigate",
        "Create a new project",
        "Show my tasks"
      ],
      confidence: 0.7,
      processingTime: 0
    };
  }

  // Helper methods
  private static matchesPatterns(input: string, patterns: string[]): boolean {
    return patterns.some(pattern => input.includes(pattern));
  }

  private static extractNavigationTargets(input: string): string[] {
    const targets = ['dashboard', 'projects', 'tasks', 'team', 'reports', 'memos', 'profile', 'settings', 'time tracking', 'analytics'];
    return targets.filter(target => input.includes(target));
  }

  private static extractHelpTopics(input: string): string[] {
    const topics = ['project', 'task', 'team', 'time tracking', 'reports', 'memo', 'navigation', 'settings'];
    return topics.filter(topic => input.includes(topic));
  }

  private static extractActionTargets(input: string): string[] {
    const targets = ['project', 'task', 'memo', 'report', 'team member', 'department'];
    return targets.filter(target => input.includes(target));
  }

  private static extractQueryTargets(input: string): string[] {
    const targets = ['tasks', 'projects', 'team', 'reports', 'memos', 'activities', 'analytics'];
    return targets.filter(target => input.includes(target));
  }

  // Load navigation context
  private static async loadNavigationContext(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      this.currentContext = {
        currentPage: window.location.pathname,
        userRole: currentUser?.role || 'staff',
        availableFeatures: this.getAvailableFeatures(currentUser?.role),
        recentActions: [],
        systemState: {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        }
      };
    } catch (error) {
      console.warn('Could not load navigation context:', error);
    }
  }

  private static getAvailableFeatures(role?: string): string[] {
    const baseFeatures = ['dashboard', 'projects', 'tasks', 'team', 'reports', 'memos', 'profile'];
    
    if (role === 'admin') {
      return [...baseFeatures, 'settings', 'analytics', 'user-management'];
    }
    
    if (role === 'manager') {
      return [...baseFeatures, 'analytics', 'team-management'];
    }
    
    return baseFeatures;
  }

  // Build voice agent knowledge
  private static async buildVoiceAgentKnowledge(): Promise<void> {
    // This would typically load from the knowledge base
    this.knowledgeBase.set('navigation', {
      routes: {
        '/': 'Dashboard - Main overview and statistics',
        '/projects': 'Projects - Manage and track projects',
        '/tasks': 'Tasks - Task management and assignment',
        '/team': 'Team - Team members and organization',
        '/reports': 'Reports - Generate and view reports',
        '/memos': 'Memos - Company announcements and communications'
      }
    });

    this.knowledgeBase.set('capabilities', this.capabilities);
  }

  // Initialize capabilities
  private static initializeCapabilities(): void {
    this.capabilities = [
      {
        name: 'Navigation Assistant',
        description: 'Help users navigate through the system',
        category: 'Navigation',
        examples: ['Go to projects', 'Open dashboard', 'Show my tasks'],
        supportedLanguages: ['en-US', 'en-GB']
      },
      {
        name: 'Project Management Helper',
        description: 'Assist with project creation and management',
        category: 'Project Management',
        examples: ['Create a new project', 'Show project status', 'Assign team members'],
        supportedLanguages: ['en-US', 'en-GB']
      },
      {
        name: 'Task Management Assistant',
        description: 'Help with task creation, assignment, and tracking',
        category: 'Task Management',
        examples: ['Create a task', 'Show my tasks', 'Update task status'],
        supportedLanguages: ['en-US', 'en-GB']
      },
      {
        name: 'System Guide',
        description: 'Provide tutorials and help with system features',
        category: 'Help & Support',
        examples: ['How do I create a project?', 'Explain time tracking', 'Show me around'],
        supportedLanguages: ['en-US', 'en-GB']
      }
    ];
  }

  // Load voice knowledge base from database
  private static async loadVoiceKnowledgeBase(): Promise<void> {
    try {
      const { data: knowledge } = await supabase
        .from('voice_agent_knowledge')
        .select('*')
        .eq('is_active', true);

      if (knowledge) {
        knowledge.forEach(item => {
          this.knowledgeBase.set(`knowledge_${item.id}`, item);
        });
      }
    } catch (error) {
      console.warn('Could not load voice knowledge base:', error);
    }
  }

  // Log voice interaction
  private static async logVoiceInteraction(input: string, response: VoiceAgentResponse, analysis: any): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();

      await supabase.from('voice_agent_interactions').insert({
        session_id: this.sessionId,
        user_id: currentUser?.user_id,
        interaction_type: analysis.category || 'conversation',
        user_input: input,
        agent_response: response.text,
        intent_recognized: analysis.intent,
        confidence_score: response.confidence,
        context_used: this.currentContext,
        actions_suggested: response.actions || [],
        processing_time_ms: response.processingTime,
        metadata: {
          entities: analysis.entities,
          suggestions: response.suggestions,
          visual_feedback: response.visualFeedback
        }
      });

      await UserActivitiesService.logActivity(
        'create',
        'Voice Agent Interaction',
        `User interacted with voice agent: ${input.substring(0, 50)}...`,
        'voice',
        this.sessionId,
        'Voice Agent'
      );
    } catch (error) {
      console.error('Error logging voice interaction:', error);
    }
  }

  // Get voice agent capabilities
  static getCapabilities(): VoiceAgentCapability[] {
    return this.capabilities;
  }

  // Get current context
  static getCurrentContext(): NavigationContext | null {
    return this.currentContext;
  }

  // Update context
  static updateContext(updates: Partial<NavigationContext>): void {
    if (this.currentContext) {
      this.currentContext = { ...this.currentContext, ...updates };
    }
  }

  // Check if initialized
  static isReady(): boolean {
    return this.isInitialized;
  }
}

export default AIVoiceAgentService;
