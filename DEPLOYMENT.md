# Deployment Checklist

## Pre-deployment Steps
- [ ] Environment variables configured
- [ ] Build runs successfully locally
- [ ] All tests pass
- [ ] No console errors in production build

## Deployment Commands

### For Vercel:
```bash
npm run build:prod
npm run deploy:vercel
```

### For Netlify:
```bash
npm run build:prod
npm run deploy:netlify
```

### Manual Deployment:
```bash
npm run build:prod
# Upload dist/ folder to your hosting provider
```

## Environment Variables to Set:
- VITE_SUPABASE_URL
- VITE_SUPABASE_ANON_KEY

## Common Issues:
1. **White screen**: Check browser console for errors
2. **404 on refresh**: Ensure SPA redirects are configured
3. **Assets not loading**: Check base URL configuration
4. **Environment variables**: Ensure they start with VITE_
