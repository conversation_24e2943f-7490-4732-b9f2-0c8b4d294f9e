#!/usr/bin/env node

/**
 * Production Build Script for CTNL AI Workboard
 * Comprehensive build process with optimization and validation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Build configuration
const BUILD_CONFIG = {
  outputDir: 'dist',
  sourceDir: 'src',
  publicDir: 'public',
  nodeModulesDir: 'node_modules',
  
  // Build steps
  steps: [
    'clean',
    'validate',
    'install',
    'build',
    'optimize',
    'compress',
    'validate-build',
    'report'
  ],
  
  // File size limits (in MB)
  limits: {
    totalSize: 50,
    jsChunk: 5,
    cssFile: 2,
    imageFile: 1
  }
};

// Utility functions
const log = {
  info: (msg) => console.log(`ℹ️  ${msg}`),
  success: (msg) => console.log(`✅ ${msg}`),
  warning: (msg) => console.log(`⚠️  ${msg}`),
  error: (msg) => console.log(`❌ ${msg}`),
  step: (step, msg) => console.log(`\n🔄 Step ${step}: ${msg}`)
};

const exec = (command, options = {}) => {
  try {
    return execSync(command, { 
      stdio: 'inherit', 
      encoding: 'utf8',
      ...options 
    });
  } catch (error) {
    log.error(`Command failed: ${command}`);
    throw error;
  }
};

const getFileSize = (filePath) => {
  const stats = fs.statSync(filePath);
  return stats.size / (1024 * 1024); // Size in MB
};

const getDirectorySize = (dirPath) => {
  let totalSize = 0;
  
  if (!fs.existsSync(dirPath)) return 0;
  
  const files = fs.readdirSync(dirPath);
  
  files.forEach(file => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isDirectory()) {
      totalSize += getDirectorySize(filePath);
    } else {
      totalSize += stats.size;
    }
  });
  
  return totalSize / (1024 * 1024); // Size in MB
};

// Build steps
const buildSteps = {
  // Step 1: Clean previous build
  clean: () => {
    log.step(1, 'Cleaning previous build');
    
    if (fs.existsSync(BUILD_CONFIG.outputDir)) {
      fs.rmSync(BUILD_CONFIG.outputDir, { recursive: true, force: true });
      log.success('Cleaned dist directory');
    } else {
      log.info('No previous build found');
    }
  },
  
  // Step 2: Validate environment
  validate: () => {
    log.step(2, 'Validating environment');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
      throw new Error(`Node.js 16+ required. Current version: ${nodeVersion}`);
    }
    log.success(`Node.js version: ${nodeVersion}`);
    
    // Check required files
    const requiredFiles = [
      'package.json',
      'vite.config.ts',
      'tsconfig.json',
      'src/main.tsx',
      'src/index.css'
    ];
    
    requiredFiles.forEach(file => {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    });
    log.success('All required files present');
    
    // Check environment variables
    const requiredEnvVars = [
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_ANON_KEY'
    ];
    
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      log.warning(`Missing environment variables: ${missingEnvVars.join(', ')}`);
      log.info('Build will continue, but some features may not work');
    } else {
      log.success('Environment variables configured');
    }
  },
  
  // Step 3: Install dependencies
  install: () => {
    log.step(3, 'Installing dependencies');
    
    if (!fs.existsSync(BUILD_CONFIG.nodeModulesDir)) {
      log.info('Installing dependencies...');
      exec('npm ci');
    } else {
      log.info('Dependencies already installed');
    }
    
    log.success('Dependencies ready');
  },
  
  // Step 4: Build application
  build: () => {
    log.step(4, 'Building application');
    
    // Set production environment
    process.env.NODE_ENV = 'production';
    
    // Run build command
    exec('npm run build');
    
    log.success('Application built successfully');
  },
  
  // Step 5: Optimize build
  optimize: () => {
    log.step(5, 'Optimizing build');
    
    const distPath = path.resolve(BUILD_CONFIG.outputDir);
    
    if (!fs.existsSync(distPath)) {
      throw new Error('Build directory not found');
    }
    
    // Check for large files
    const checkLargeFiles = (dir, basePath = '') => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const relativePath = path.join(basePath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          checkLargeFiles(filePath, relativePath);
        } else {
          const sizeInMB = stats.size / (1024 * 1024);
          
          if (file.endsWith('.js') && sizeInMB > BUILD_CONFIG.limits.jsChunk) {
            log.warning(`Large JS file: ${relativePath} (${sizeInMB.toFixed(2)} MB)`);
          }
          
          if (file.endsWith('.css') && sizeInMB > BUILD_CONFIG.limits.cssFile) {
            log.warning(`Large CSS file: ${relativePath} (${sizeInMB.toFixed(2)} MB)`);
          }
          
          if (/\.(png|jpg|jpeg|gif|svg)$/i.test(file) && sizeInMB > BUILD_CONFIG.limits.imageFile) {
            log.warning(`Large image file: ${relativePath} (${sizeInMB.toFixed(2)} MB)`);
          }
        }
      });
    };
    
    checkLargeFiles(distPath);
    log.success('Build optimization complete');
  },
  
  // Step 6: Compress assets
  compress: () => {
    log.step(6, 'Compressing assets');
    
    // This step is handled by Vite plugins during build
    // Just verify compression worked
    const distPath = path.resolve(BUILD_CONFIG.outputDir);
    const compressedFiles = [];
    
    const findCompressedFiles = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isDirectory()) {
          findCompressedFiles(filePath);
        } else if (file.endsWith('.gz') || file.endsWith('.br')) {
          compressedFiles.push(file);
        }
      });
    };
    
    findCompressedFiles(distPath);
    
    if (compressedFiles.length > 0) {
      log.success(`Found ${compressedFiles.length} compressed files`);
    } else {
      log.warning('No compressed files found');
    }
  },
  
  // Step 7: Validate build
  validateBuild: () => {
    log.step(7, 'Validating build');
    
    const distPath = path.resolve(BUILD_CONFIG.outputDir);
    
    // Check if index.html exists
    const indexPath = path.join(distPath, 'index.html');
    if (!fs.existsSync(indexPath)) {
      throw new Error('index.html not found in build');
    }
    
    // Check total build size
    const totalSize = getDirectorySize(distPath);
    if (totalSize > BUILD_CONFIG.limits.totalSize) {
      log.warning(`Build size (${totalSize.toFixed(2)} MB) exceeds limit (${BUILD_CONFIG.limits.totalSize} MB)`);
    } else {
      log.success(`Build size: ${totalSize.toFixed(2)} MB`);
    }
    
    // Check for required assets
    const requiredAssets = ['js', 'css'];
    const assetsDir = path.join(distPath, 'assets');
    
    if (fs.existsSync(assetsDir)) {
      const assetFiles = fs.readdirSync(assetsDir);
      
      requiredAssets.forEach(assetType => {
        const hasAsset = assetFiles.some(file => file.includes(assetType));
        if (hasAsset) {
          log.success(`${assetType.toUpperCase()} assets found`);
        } else {
          log.warning(`No ${assetType.toUpperCase()} assets found`);
        }
      });
    }
    
    log.success('Build validation complete');
  },
  
  // Step 8: Generate report
  report: () => {
    log.step(8, 'Generating build report');
    
    const distPath = path.resolve(BUILD_CONFIG.outputDir);
    const totalSize = getDirectorySize(distPath);
    
    const report = {
      buildTime: new Date().toISOString(),
      totalSize: `${totalSize.toFixed(2)} MB`,
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development',
      outputDirectory: BUILD_CONFIG.outputDir
    };
    
    // Write report to file
    const reportPath = path.join(distPath, 'build-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // Display summary
    console.log('\n📊 Build Summary:');
    console.log(`   Total Size: ${report.totalSize}`);
    console.log(`   Build Time: ${report.buildTime}`);
    console.log(`   Output: ${report.outputDirectory}`);
    
    log.success('Build report generated');
  }
};

// Main build function
async function build() {
  const startTime = Date.now();
  
  console.log('🚀 Starting production build for CTNL AI Workboard\n');
  
  try {
    // Execute build steps
    for (const step of BUILD_CONFIG.steps) {
      const stepFunction = buildSteps[step.replace('-', '')];
      if (stepFunction) {
        await stepFunction();
      } else {
        log.warning(`Unknown build step: ${step}`);
      }
    }
    
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    
    console.log('\n🎉 Build completed successfully!');
    console.log(`⏱️  Total time: ${duration}s`);
    console.log(`📁 Output: ${path.resolve(BUILD_CONFIG.outputDir)}`);
    
  } catch (error) {
    log.error(`Build failed: ${error.message}`);
    process.exit(1);
  }
}

// Run build if called directly
if (require.main === module) {
  build();
}

module.exports = { build, buildSteps, BUILD_CONFIG };
