import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Smartphone,
  Wifi,
  Camera,
  Mic,
  MapPin,
  Vibrate
} from "lucide-react";

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning' | 'testing';
  message: string;
  details?: string;
}

const MobileTest: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    const testResults: TestResult[] = [];

    // Test 1: Touch Support
    testResults.push({
      name: 'Touch Support',
      status: 'ontouchstart' in window ? 'pass' : 'fail',
      message: 'ontouchstart' in window ? 'Touch events supported' : 'Touch events not supported',
    });

    // Test 2: Screen Size
    const isMobileSize = window.innerWidth <= 768;
    testResults.push({
      name: 'Mobile Screen Size',
      status: isMobileSize ? 'pass' : 'warning',
      message: isMobileSize ? 'Mobile screen size detected' : 'Desktop screen size detected',
      details: `${window.innerWidth} × ${window.innerHeight}`,
    });

    // Test 3: Device Orientation
    try {
      const orientation = window.screen.orientation?.type || 'unknown';
      testResults.push({
        name: 'Device Orientation',
        status: orientation !== 'unknown' ? 'pass' : 'warning',
        message: `Orientation: ${orientation}`,
      });
    } catch (error) {
      testResults.push({
        name: 'Device Orientation',
        status: 'fail',
        message: 'Orientation API not supported',
      });
    }

    // Test 4: Network Status
    testResults.push({
      name: 'Network Status',
      status: navigator.onLine ? 'pass' : 'fail',
      message: navigator.onLine ? 'Device is online' : 'Device is offline',
    });

    // Test 5: Geolocation API
    if ('geolocation' in navigator) {
      testResults.push({
        name: 'Geolocation API',
        status: 'pass',
        message: 'Geolocation API available',
      });
    } else {
      testResults.push({
        name: 'Geolocation API',
        status: 'fail',
        message: 'Geolocation API not supported',
      });
    }

    // Test 6: Camera API
    if ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices) {
      testResults.push({
        name: 'Camera API',
        status: 'pass',
        message: 'Camera API available',
      });
    } else {
      testResults.push({
        name: 'Camera API',
        status: 'fail',
        message: 'Camera API not supported',
      });
    }

    // Test 7: Vibration API
    if ('vibrate' in navigator) {
      testResults.push({
        name: 'Vibration API',
        status: 'pass',
        message: 'Vibration API available',
      });
    } else {
      testResults.push({
        name: 'Vibration API',
        status: 'warning',
        message: 'Vibration API not supported',
      });
    }

    // Test 8: Local Storage
    try {
      localStorage.setItem('mobile-test', 'test');
      localStorage.removeItem('mobile-test');
      testResults.push({
        name: 'Local Storage',
        status: 'pass',
        message: 'Local Storage working',
      });
    } catch (error) {
      testResults.push({
        name: 'Local Storage',
        status: 'fail',
        message: 'Local Storage not available',
      });
    }

    // Test 9: Service Worker
    if ('serviceWorker' in navigator) {
      testResults.push({
        name: 'Service Worker',
        status: 'pass',
        message: 'Service Worker supported',
      });
    } else {
      testResults.push({
        name: 'Service Worker',
        status: 'warning',
        message: 'Service Worker not supported',
      });
    }

    // Test 10: WebGL Support
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      testResults.push({
        name: 'WebGL Support',
        status: gl ? 'pass' : 'warning',
        message: gl ? 'WebGL supported' : 'WebGL not supported',
      });
    } catch (error) {
      testResults.push({
        name: 'WebGL Support',
        status: 'fail',
        message: 'WebGL test failed',
      });
    }

    setTests(testResults);
    setIsRunning(false);
  };

  useEffect(() => {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTestMode = localStorage.getItem('mobile-test') === 'true';
    setIsVisible(isMobile || isTestMode);

    if (isVisible) {
      runTests();
    }
  }, [isVisible]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'testing':
        return <TestTube className="h-4 w-4 text-blue-500 animate-pulse" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTestIcon = (testName: string) => {
    switch (testName) {
      case 'Camera API':
        return <Camera className="h-3 w-3" />;
      case 'Geolocation API':
        return <MapPin className="h-3 w-3" />;
      case 'Vibration API':
        return <Vibrate className="h-3 w-3" />;
      case 'Network Status':
        return <Wifi className="h-3 w-3" />;
      default:
        return <Smartphone className="h-3 w-3" />;
    }
  };

  const testSummary = {
    total: tests.length,
    passed: tests.filter(t => t.status === 'pass').length,
    failed: tests.filter(t => t.status === 'fail').length,
    warnings: tests.filter(t => t.status === 'warning').length,
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md">
      <Card className="bg-black/90 border-red-500/30 text-white">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <TestTube className="h-4 w-4 text-red-500" />
            Mobile Compatibility Test
            <Button
              variant="ghost"
              size="sm"
              onClick={runTests}
              disabled={isRunning}
              className="ml-auto"
            >
              {isRunning ? 'Testing...' : 'Retest'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Test Summary */}
          <div className="flex gap-2 text-xs">
            <Badge variant="default" className="bg-green-600">
              ✓ {testSummary.passed}
            </Badge>
            <Badge variant="destructive">
              ✗ {testSummary.failed}
            </Badge>
            <Badge variant="secondary" className="bg-yellow-600">
              ⚠ {testSummary.warnings}
            </Badge>
          </div>

          {/* Test Results */}
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {tests.map((test, index) => (
              <div key={index} className="flex items-start gap-2 p-2 rounded border border-red-500/20">
                <div className="flex items-center gap-1 min-w-0 flex-1">
                  {getTestIcon(test.name)}
                  <span className="text-xs font-medium truncate">{test.name}</span>
                </div>
                <div className="flex items-center gap-1">
                  {getStatusIcon(test.status)}
                </div>
              </div>
            ))}
          </div>

          {/* Overall Status */}
          {testSummary.failed > 0 && (
            <Alert className="border-red-500/30 bg-red-500/10">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {testSummary.failed} critical features not supported. App may not work properly on this device.
              </AlertDescription>
            </Alert>
          )}

          {testSummary.failed === 0 && testSummary.warnings > 0 && (
            <Alert className="border-yellow-500/30 bg-yellow-500/10">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {testSummary.warnings} optional features not supported. Core functionality should work.
              </AlertDescription>
            </Alert>
          )}

          {testSummary.failed === 0 && testSummary.warnings === 0 && (
            <Alert className="border-green-500/30 bg-green-500/10">
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                All tests passed! This device is fully compatible.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MobileTest;
