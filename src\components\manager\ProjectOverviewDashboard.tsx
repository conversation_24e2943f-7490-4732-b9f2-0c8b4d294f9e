import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Target, 
  Users, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle2,
  PlayCircle,
  PauseCircle,
  Calendar,
  DollarSign,
  Eye,
  Edit3,
  Plus,
  Filter
} from "lucide-react";
import { useAllProjectsWithProgress, useProjectColors } from "@/hooks/useProjectProgress";
import { ProjectWithProgress } from "@/lib/project-progress-api";
import { format } from "date-fns";

interface ProjectOverviewDashboardProps {
  userRole: 'admin' | 'manager' | 'accountant' | 'staff-admin';
  showCreateButton?: boolean;
}

export const ProjectOverviewDashboard: React.FC<ProjectOverviewDashboardProps> = ({
  userRole,
  showCreateButton = true
}) => {
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'cards' | 'table'>('cards');
  
  const { data: projects, isLoading, error } = useAllProjectsWithProgress({
    status: statusFilter === 'all' ? undefined : statusFilter
  });
  
  const { getProjectColor, getProgressColor, getStatusColor } = useProjectColors();

  // Calculate dashboard statistics
  const stats = {
    totalProjects: projects?.length || 0,
    activeProjects: projects?.filter(p => p.status === 'active').length || 0,
    completedProjects: projects?.filter(p => p.status === 'completed').length || 0,
    onHoldProjects: projects?.filter(p => p.status === 'on_hold').length || 0,
    totalTeamMembers: projects?.reduce((sum, p) => sum + (p.team_size || 0), 0) || 0,
    averageProgress: projects?.length 
      ? Math.round(projects.reduce((sum, p) => sum + (p.overall_progress || 0), 0) / projects.length)
      : 0,
    totalBudget: projects?.reduce((sum, p) => sum + (p.budget || 0), 0) || 0,
    totalSpent: projects?.reduce((sum, p) => sum + (p.budget_spent || 0), 0) || 0,
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="h-4 w-4" />;
      case 'active': return <PlayCircle className="h-4 w-4" />;
      case 'in_progress': return <PlayCircle className="h-4 w-4" />;
      case 'on_hold': return <PauseCircle className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="glassmorphism">
              <CardContent className="p-4">
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-4 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Card className="glassmorphism">
          <CardContent className="p-6">
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Dashboard Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" data-aos="fade-up">
        <Card className="glassmorphism border-blue-200 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Projects</p>
                <p className="text-2xl font-bold text-blue-700">{stats.totalProjects}</p>
              </div>
              <Target className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glassmorphism border-green-200 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Active Projects</p>
                <p className="text-2xl font-bold text-green-700">{stats.activeProjects}</p>
              </div>
              <PlayCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glassmorphism border-purple-200 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">Team Members</p>
                <p className="text-2xl font-bold text-purple-700">{stats.totalTeamMembers}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="glassmorphism border-orange-200 hover:shadow-lg transition-all duration-300">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Avg Progress</p>
                <p className="text-2xl font-bold text-orange-700">{stats.averageProgress}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold modern-heading">Project Overview</h2>
          <Badge variant="outline" className="text-sm">
            {projects?.length || 0} Projects
          </Badge>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <Filter className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="planning">Planning</SelectItem>
              <SelectItem value="on_hold">On Hold</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
          
          {showCreateButton && (userRole === 'admin' || userRole === 'manager') && (
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </Button>
          )}
        </div>
      </div>

      {/* Projects Display */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'cards' | 'table')}>
        <TabsList className="grid w-full max-w-md grid-cols-2">
          <TabsTrigger value="cards">Card View</TabsTrigger>
          <TabsTrigger value="table">Table View</TabsTrigger>
        </TabsList>

        <TabsContent value="cards" className="mt-6">
          {!projects || projects.length === 0 ? (
            <Card className="glassmorphism">
              <CardContent className="p-8 text-center">
                <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">No Projects Found</h3>
                <p className="text-gray-500">
                  {statusFilter === 'all' 
                    ? "No projects have been created yet." 
                    : `No projects with status "${statusFilter}" found.`}
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project, index) => (
                <Card 
                  key={project.id} 
                  className="glassmorphism hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4"
                  style={{ borderLeftColor: getProjectColor(project).replace('bg-', '#') }}
                  data-aos="fade-up"
                  data-aos-delay={index * 100}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-bold mb-2 line-clamp-2">
                          {project.name}
                        </CardTitle>
                        <div className="flex items-center gap-2 mb-2">
                          <Badge className={`${getStatusColor(project.status)} text-xs`}>
                            {getStatusIcon(project.status)}
                            <span className="ml-1 capitalize">{project.status}</span>
                          </Badge>
                          {project.priority && (
                            <Badge className={`${getPriorityColor(project.priority)} text-xs`}>
                              {project.priority}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    {project.description && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {project.description}
                      </p>
                    )}
                  </CardHeader>

                  <CardContent className="space-y-4">
                    {/* Progress Section */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Overall Progress</span>
                        <span className={`font-bold ${getProgressColor(project.overall_progress || 0)}`}>
                          {project.overall_progress || 0}%
                        </span>
                      </div>
                      <Progress 
                        value={project.overall_progress || 0} 
                        className="h-2 bg-gray-200"
                      />
                    </div>

                    {/* Project Details */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="font-medium">{project.team_size || 0}</p>
                          <p className="text-xs text-muted-foreground">Team Members</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="font-medium">{project.actual_hours || 0}h</p>
                          <p className="text-xs text-muted-foreground">of {project.estimated_hours || 0}h</p>
                        </div>
                      </div>
                    </div>

                    {/* Timeline */}
                    {project.end_date && (
                      <div className="flex items-center gap-2 text-sm">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <span>Due {format(new Date(project.end_date), 'MMM dd, yyyy')}</span>
                      </div>
                    )}

                    {/* Budget (for admin/accountant) */}
                    {(userRole === 'admin' || userRole === 'accountant') && project.budget && (
                      <div className="flex items-center gap-2 text-sm">
                        <DollarSign className="h-4 w-4 text-gray-500" />
                        <span>${project.budget_spent || 0} / ${project.budget}</span>
                      </div>
                    )}

                    {/* Manager Info */}
                    {project.manager && (
                      <div className="text-sm">
                        <span className="text-muted-foreground">Manager: </span>
                        <span className="font-medium">{project.manager.full_name}</span>
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => {
                          // Navigate to project details
                          console.log('View project details:', project.id);
                        }}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      {(userRole === 'admin' || userRole === 'manager') && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            // Navigate to edit project
                            console.log('Edit project:', project.id);
                          }}
                        >
                          <Edit3 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="table" className="mt-6">
          <Card className="glassmorphism">
            <CardContent className="p-0">
              <ScrollArea className="h-[600px]">
                <div className="min-w-full">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Project
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Progress
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Team
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Due Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {projects?.map((project) => (
                        <tr key={project.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {project.name}
                              </div>
                              <div className="text-sm text-gray-500 line-clamp-1">
                                {project.description}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <Badge className={`${getStatusColor(project.status)} text-xs`}>
                              {getStatusIcon(project.status)}
                              <span className="ml-1 capitalize">{project.status}</span>
                            </Badge>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="w-20">
                              <Progress value={project.overall_progress || 0} className="h-2" />
                              <span className="text-xs text-gray-500 mt-1">
                                {project.overall_progress || 0}%
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {project.team_size || 0} members
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {project.end_date 
                              ? format(new Date(project.end_date), 'MMM dd, yyyy')
                              : 'No due date'
                            }
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex gap-2">
                              <Button size="sm" variant="ghost">
                                <Eye className="h-4 w-4" />
                              </Button>
                              {(userRole === 'admin' || userRole === 'manager') && (
                                <Button size="sm" variant="ghost">
                                  <Edit3 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
