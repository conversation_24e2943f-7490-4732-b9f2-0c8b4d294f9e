# 🚀 AI CTNL Dashboard - Complete Fix Guide

This guide provides comprehensive solutions for all dashboard data display issues, RLS problems, SQL syntax errors, and Supabase edge function issues.

## 🔧 Quick Start

### Option 1: Automated Installation (Recommended)

**Windows (PowerShell):**
```powershell
.\scripts\install-and-start.ps1
```

**Linux/Mac (Bash):**
```bash
chmod +x scripts/install-and-start.sh
./scripts/install-and-start.sh
```

### Option 2: Manual Installation

```bash
# Clean install
rm -rf node_modules package-lock.json .vite dist
npm install

# Start development server
npm run dev
```

## 🛠️ Fix Tools Available

After starting the server, access these fix tools in your browser:

### 1. Complete Dashboard Fix
**URL:** `http://localhost:5173/fix-dashboard-complete.html`

**What it fixes:**
- ✅ Creates missing database tables (invoices, expense_reports, reports, time_logs, notifications)
- ✅ Fixes RLS policies for proper role-based access
- ✅ Inserts sample data for dashboard charts
- ✅ Tests data access and permissions
- ✅ Provides reset functionality

### 2. Profile Roles Fix
**URL:** `http://localhost:5173/fix-profile-roles.html`

**What it fixes:**
- ✅ Updates profile role constraints (admin, manager, staff, accountant, hr, staff-admin)
- ✅ Fixes RLS policies for profile access
- ✅ Creates role management functions
- ✅ Tests role-based permissions

### 3. Activities Fix
**URL:** `http://localhost:5173/fix-activities.html`

**What it fixes:**
- ✅ Creates system_activities table
- ✅ Fixes activity logging functions
- ✅ Updates RLS policies for activity logs
- ✅ Tests activity tracking

## 📊 Dashboard Issues Fixed

### Data Display Problems
- **Empty Charts:** Fixed by creating proper data tables and inserting sample data
- **Missing Financial Data:** Created invoices and expense_reports tables
- **No Activity Logs:** Created system_activities and notifications tables
- **Time Tracking Issues:** Created time_logs table with proper structure

### RLS (Row Level Security) Issues
- **Access Denied Errors:** Fixed with proper role-based policies
- **Permission Recursion:** Eliminated circular policy references
- **Role Hierarchy:** Implemented proper role-based access control

### SQL Syntax Errors
- **Column Name Mismatches:** Fixed table schemas and column references
- **Table Not Found:** Created all missing tables with proper relationships
- **Foreign Key Errors:** Added proper foreign key constraints

## 🔐 Role-Based Access Control

### Supported Roles
- **Admin:** Full system access, can manage all users and data
- **Manager:** Department management, team oversight, project coordination
- **Staff-Admin:** Administrative support, user management assistance
- **HR:** Human resources, employee management, recruitment
- **Accountant:** Financial management, budgeting, expense tracking
- **Staff:** General employees, task execution, basic access

### Permission Matrix
| Feature | Admin | Manager | Staff-Admin | HR | Accountant | Staff |
|---------|-------|---------|-------------|----|-----------| ------|
| View All Profiles | ✅ | ✅ (Dept) | ✅ | ✅ | ❌ | ❌ |
| Manage Users | ✅ | ✅ (Dept) | ✅ | ✅ | ❌ | ❌ |
| Financial Data | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ |
| Reports | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ (Own) |
| Time Logs | ✅ | ✅ (Team) | ❌ | ❌ | ❌ | ✅ (Own) |

## 🗃️ Database Schema

### Core Tables Created
```sql
-- Financial Management
invoices (id, invoice_number, client_name, amount, payment_status, ...)
expense_reports (id, title, amount, category, status, submitted_by, ...)

-- Report Management  
reports (id, title, report_type, priority, status, submitted_by, ...)

-- Time Tracking
time_logs (id, user_id, hours_worked, log_date, billable, ...)

-- Notifications
notifications (id, user_id, title, message, type, read, ...)

-- System Activities
system_activities (id, user_id, action, description, severity, ...)
```

### Indexes for Performance
```sql
-- Optimized queries for dashboard
CREATE INDEX idx_invoices_payment_status ON invoices(payment_status);
CREATE INDEX idx_expense_reports_status ON expense_reports(status);
CREATE INDEX idx_reports_submitted_by ON reports(submitted_by);
CREATE INDEX idx_time_logs_user_id ON time_logs(user_id);
CREATE INDEX idx_notifications_read ON notifications(read);
```

## 🔄 Sample Data Included

The fix tools insert realistic sample data for testing:

- **5 Sample Invoices** (paid, pending, overdue statuses)
- **5 Sample Expense Reports** (various categories and approval states)
- **5 Sample Reports** (different types and priorities)
- **7 Sample Time Logs** (recent work entries)
- **5 Sample Notifications** (various types and read states)

## 🧪 Testing & Verification

### Automated Tests
Each fix tool includes comprehensive testing:
- ✅ Database connectivity
- ✅ Table existence and structure
- ✅ RLS policy functionality
- ✅ Data access permissions
- ✅ Role-based restrictions

### Manual Verification
After running fixes, verify:
1. Dashboard displays charts with data
2. Role-based navigation works
3. Reports can be submitted
4. Time tracking functions
5. Notifications appear

## 🚨 Troubleshooting

### Common Issues

**1. "Please log in first" Error**
- Solution: Ensure you're logged into Supabase
- Check: Browser has valid authentication session

**2. "Permission denied" Errors**
- Solution: Run the Profile Roles Fix tool
- Check: User has appropriate role assigned

**3. "Table does not exist" Errors**
- Solution: Run the Complete Dashboard Fix tool
- Check: All required tables are created

**4. Charts not displaying data**
- Solution: Run Dashboard Fix to insert sample data
- Check: RLS policies allow data access

**5. Edge Function Errors**
- Solution: Check Supabase project settings
- Check: Edge functions are deployed and accessible

### Reset Options
If issues persist, use the reset functionality in the fix tools:
1. **Soft Reset:** Recreates tables and policies
2. **Hard Reset:** Drops all tables and starts fresh
3. **Role Reset:** Resets only role-related configurations

## 📝 Environment Configuration

Ensure your `.env` file contains:
```env
# Supabase Configuration
VITE_SUPABASE_URL=https://dvflgnqwbsjityrowatf.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=AI CTNL Dashboard
NODE_ENV=development
```

## 🔗 Additional Resources

- **Supabase Dashboard:** [https://supabase.com/dashboard](https://supabase.com/dashboard)
- **Project URL:** [https://dvflgnqwbsjityrowatf.supabase.co](https://dvflgnqwbsjityrowatf.supabase.co)
- **Documentation:** Check the `docs/` folder for detailed guides

## 📞 Support

If you encounter issues not covered by this guide:
1. Check the browser console for error messages
2. Review the fix tool logs for detailed information
3. Verify your Supabase project permissions
4. Ensure all environment variables are correctly set

---

**Last Updated:** January 2025  
**Version:** 2.0.0  
**Status:** ✅ All major dashboard issues resolved
