<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete All Users - Fresh Start - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
            font-size: 2rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .warning-card {
            background: rgba(220, 53, 69, 0.1);
            border: 2px solid rgba(220, 53, 69, 0.5);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .button.danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .button.danger:hover {
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
        }

        .sql-box {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 1rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.warning {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .checklist {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .checklist ul {
            margin-left: 1.5rem;
        }

        .checklist li {
            margin-bottom: 0.5rem;
        }

        .confirmation {
            background: rgba(220, 53, 69, 0.2);
            border: 2px solid rgba(220, 53, 69, 0.5);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .confirmation input[type="checkbox"] {
            margin-right: 0.5rem;
            transform: scale(1.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ Delete All Users - Fresh Start</h1>
            <p>Complete database cleanup for a fresh beginning</p>
        </div>

        <div class="warning-card">
            <h3>⚠️ CRITICAL WARNING</h3>
            <p><strong>This action is IRREVERSIBLE and will permanently delete:</strong></p>
            <ul style="margin-left: 1.5rem; margin-top: 0.5rem;">
                <li>All user accounts and authentication data</li>
                <li>All user profiles and personal information</li>
                <li>All time logs and work records</li>
                <li>All user-created projects and assignments</li>
                <li>All sessions and login tokens</li>
            </ul>
            <p style="margin-top: 1rem;"><strong>Only proceed if you want to completely start over with a clean database!</strong></p>
        </div>

        <div class="card">
            <h3>📋 What This Will Do</h3>
            <div class="checklist">
                <ul>
                    <li>Delete all users from auth.users table</li>
                    <li>Delete all profiles from public.profiles table</li>
                    <li>Delete all time logs and work records</li>
                    <li>Delete all sessions and refresh tokens</li>
                    <li>Clean up all RLS policies and recreate them fresh</li>
                    <li>Reset the profile creation trigger</li>
                    <li>Prepare the database for new user registrations</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h3>🔧 Cleanup SQL</h3>
            <p>This SQL will perform the complete cleanup:</p>
            
            <div class="sql-box" id="sqlCode">-- COMPLETE USER DELETION WITH FOREIGN KEY HANDLING
-- WARNING: This deletes ALL users and related data

-- 1. Disable RLS on all tables
ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.document_analysis DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.time_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.ai_conversations DISABLE ROW LEVEL SECURITY;

-- 2. Delete all data that references users (correct order for foreign keys)
DELETE FROM public.document_analysis;
DELETE FROM public.ai_conversations;
DELETE FROM public.notifications;
DELETE FROM public.time_logs;
DELETE FROM public.project_members;
DELETE FROM public.project_assignments;
DELETE FROM public.memos;
DELETE FROM public.expenses;
DELETE FROM public.invoices;
DELETE FROM public.assets;

-- 3. Update tables to remove user references
UPDATE public.projects SET created_by = NULL WHERE created_by IS NOT NULL;
UPDATE public.projects SET updated_by = NULL WHERE updated_by IS NOT NULL;
UPDATE public.projects SET assigned_to = NULL WHERE assigned_to IS NOT NULL;
UPDATE public.departments SET manager_id = NULL WHERE manager_id IS NOT NULL;

-- 4. Delete profiles and auth data
DELETE FROM public.profiles;
DELETE FROM auth.sessions;
DELETE FROM auth.refresh_tokens;
DELETE FROM auth.identities;
DELETE FROM auth.users;

-- 5. Drop ALL existing RLS policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;

-- 6. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 7. Create fresh, clean policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- 8. Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- 9. Recreate profile creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
    'active',
    NOW(),
    NOW()
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. Recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 11. Verify cleanup
SELECT 'CLEANUP COMPLETE' as status;
SELECT COUNT(*) as remaining_users FROM auth.users;
SELECT COUNT(*) as remaining_profiles FROM public.profiles;
SELECT COUNT(*) as remaining_documents FROM public.document_analysis;</div>

            <button class="button" onclick="copySQL()">📋 Copy Cleanup SQL</button>
        </div>

        <div class="card">
            <h3>✅ Confirmation Required</h3>
            <div class="confirmation">
                <label>
                    <input type="checkbox" id="confirmCheckbox" onchange="toggleDeleteButton()">
                    I understand this will permanently delete ALL users and data, and I want to proceed with a fresh start
                </label>
            </div>
            
            <button class="button danger" id="deleteButton" onclick="proceedWithDeletion()" disabled>
                🗑️ PROCEED WITH COMPLETE DELETION
            </button>
        </div>

        <div id="status" class="status warning">
            ⚠️ Ready to perform complete user deletion. Please read all warnings carefully before proceeding.
        </div>
    </div>

    <script>
        function toggleDeleteButton() {
            const checkbox = document.getElementById('confirmCheckbox');
            const button = document.getElementById('deleteButton');
            button.disabled = !checkbox.checked;
        }

        function copySQL() {
            const sqlCode = document.getElementById('sqlCode').textContent;
            navigator.clipboard.writeText(sqlCode).then(() => {
                showStatus('✅ Cleanup SQL copied to clipboard!\n\nNext steps:\n1. Go to Supabase Dashboard → SQL Editor\n2. Paste and run the SQL\n3. Wait for completion\n4. Database will be completely clean\n\nAfter this, you can create new users without any conflicts.', 'success');
            }).catch(() => {
                alert('Failed to copy SQL. Please select and copy manually.');
            });
        }

        function proceedWithDeletion() {
            const checkbox = document.getElementById('confirmCheckbox');
            if (!checkbox.checked) {
                alert('Please confirm that you understand this action is irreversible.');
                return;
            }

            if (!confirm('FINAL CONFIRMATION: This will permanently delete ALL users and data. Are you absolutely sure?')) {
                return;
            }

            showStatus('⚠️ To proceed with deletion:\n\n1. Copy the SQL above\n2. Go to Supabase Dashboard → SQL Editor\n3. Paste and run the SQL\n4. Wait for completion\n\nThis must be done manually for security reasons.', 'warning');
        }

        function showStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        // Show initial warning
        window.addEventListener('load', () => {
            showStatus('⚠️ DANGER ZONE: This will delete ALL users and data permanently.\n\nOnly proceed if you want a completely fresh start.\n\nRead all warnings and check the confirmation box to proceed.', 'warning');
        });
    </script>
</body>
</html>
