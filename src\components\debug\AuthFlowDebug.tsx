import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  User, 
  Database, 
  Shield, 
  CheckCircle, 
  AlertCircle, 
  XCircle,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';
import { supabase } from '@/integrations/supabase/client';

export const AuthFlowDebug: React.FC = () => {
  const auth = useAuth();
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(true);
  const [profileData, setProfileData] = useState<any>(null);
  const [profileError, setProfileError] = useState<string | null>(null);

  // Add log entry
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  // Test profile fetch
  const testProfileFetch = async () => {
    if (!auth.user?.id) {
      addLog('❌ No user ID available for profile fetch');
      return;
    }

    addLog('🔍 Testing profile fetch...');
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', auth.user.id)
        .single();

      if (error) {
        addLog(`❌ Profile fetch error: ${error.message}`);
        setProfileError(error.message);
        setProfileData(null);
      } else {
        addLog(`✅ Profile fetch successful: ${JSON.stringify(data)}`);
        setProfileData(data);
        setProfileError(null);
      }
    } catch (err) {
      addLog(`❌ Profile fetch exception: ${err}`);
      setProfileError(String(err));
      setProfileData(null);
    }
  };

  // Test profile creation
  const testProfileCreation = async () => {
    if (!auth.user?.id) {
      addLog('❌ No user ID available for profile creation');
      return;
    }

    addLog('🔨 Testing profile creation...');
    try {
      const profileData = {
        id: auth.user.id,
        full_name: auth.user.user_metadata?.full_name || 'Test User',
        email: auth.user.email,
        role: 'staff',
        status: 'active'
      };

      addLog(`📝 Attempting to insert: ${JSON.stringify(profileData)}`);

      const { data, error } = await supabase
        .from('profiles')
        .insert(profileData)
        .select()
        .single();

      if (error) {
        addLog(`❌ Profile creation error: ${error.message}`);
        setProfileError(error.message);
      } else {
        addLog(`✅ Profile created successfully: ${JSON.stringify(data)}`);
        setProfileData(data);
        setProfileError(null);
      }
    } catch (err) {
      addLog(`❌ Profile creation exception: ${err}`);
      setProfileError(String(err));
    }
  };

  // Check database connection
  const testDatabaseConnection = async () => {
    addLog('🔌 Testing database connection...');
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);

      if (error) {
        addLog(`❌ Database connection error: ${error.message}`);
      } else {
        addLog('✅ Database connection successful');
      }
    } catch (err) {
      addLog(`❌ Database connection exception: ${err}`);
    }
  };

  // Monitor auth state changes
  useEffect(() => {
    addLog('🔄 Auth state monitor initialized');
    
    if (auth.loading) {
      addLog('⏳ Auth loading...');
    } else if (auth.user) {
      addLog(`✅ User authenticated: ${auth.user.email}`);
      addLog(`👤 User ID: ${auth.user.id}`);
      addLog(`📧 User metadata: ${JSON.stringify(auth.user.user_metadata)}`);
      
      if (auth.userProfile) {
        addLog(`👤 Profile loaded: ${JSON.stringify(auth.userProfile)}`);
      } else {
        addLog('⚠️ No profile loaded');
      }
    } else {
      addLog('❌ No user authenticated');
    }
  }, [auth.loading, auth.user, auth.userProfile]);

  const getStatusIcon = (condition: boolean) => {
    return condition ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={condition ? "default" : "destructive"}>
        {condition ? trueText : falseText}
      </Badge>
    );
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Shield className="h-6 w-6" />
          Authentication Flow Debug
        </h2>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowLogs(!showLogs)}
        >
          {showLogs ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          {showLogs ? 'Hide' : 'Show'} Logs
        </Button>
      </div>

      {/* Auth Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <User className="h-4 w-4" />
              Authentication
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Loading:</span>
              {getStatusIcon(!auth.loading)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">User:</span>
              {getStatusIcon(!!auth.user)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Session:</span>
              {getStatusIcon(!!auth.session)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="h-4 w-4" />
              Profile
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Profile:</span>
              {getStatusIcon(!!auth.userProfile)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Role:</span>
              {getStatusIcon(!!auth.userProfile?.role)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Status:</span>
              {getStatusIcon(auth.userProfile?.status === 'active')}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Permissions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Authenticated:</span>
              {getStatusIcon(auth.isAuthenticated)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Admin:</span>
              {getStatusIcon(auth.isAdmin)}
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Can Manage:</span>
              {getStatusIcon(auth.canManage)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* User Details */}
      {auth.user && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">User Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Email:</strong> {auth.user.email}
              </div>
              <div>
                <strong>ID:</strong> {auth.user.id}
              </div>
              <div>
                <strong>Created:</strong> {new Date(auth.user.created_at).toLocaleString()}
              </div>
              <div>
                <strong>Last Sign In:</strong> {auth.user.last_sign_in_at ? new Date(auth.user.last_sign_in_at).toLocaleString() : 'Never'}
              </div>
            </div>
            {auth.userProfile && (
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <strong>Profile Data:</strong>
                <pre className="text-xs mt-2 overflow-auto">
                  {JSON.stringify(auth.userProfile, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Test Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Test Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex flex-wrap gap-2">
            <Button size="sm" onClick={testDatabaseConnection}>
              <Database className="h-4 w-4 mr-2" />
              Test DB Connection
            </Button>
            <Button size="sm" onClick={testProfileFetch} disabled={!auth.user}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Test Profile Fetch
            </Button>
            <Button size="sm" onClick={testProfileCreation} disabled={!auth.user}>
              <User className="h-4 w-4 mr-2" />
              Test Profile Creation
            </Button>
          </div>

          {profileError && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Profile Error:</strong> {profileError}
              </AlertDescription>
            </Alert>
          )}

          {profileData && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Profile Data:</strong>
                <pre className="text-xs mt-2 overflow-auto">
                  {JSON.stringify(profileData, null, 2)}
                </pre>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Logs */}
      {showLogs && (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-sm">Authentication Logs</CardTitle>
            <Button size="sm" variant="outline" onClick={() => setLogs([])}>
              Clear Logs
            </Button>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-xs max-h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <div className="text-gray-500">No logs yet...</div>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
