
import { useAuth } from "@/components/auth/AuthProvider";
import { TokenExpiryWarning } from "@/components/auth/TokenExpiryWarning";
import { EnhancedAppSidebar } from "@/components/navigation/EnhancedAppSidebar";
import { MainNavBar } from "@/components/navigation/MainNavBar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { VoiceNavigationButton } from "@/components/voice/VoiceNavigationButton";
import { cn } from "@/lib/utils";
import AOS from "aos";
import { ReactNode, useEffect, useState } from "react";

interface EnhancedAppLayoutProps {
  children: ReactNode;
  title?: string;
}

export function EnhancedAppLayout({ children, title }: EnhancedAppLayoutProps) {
  const { isAuthenticated } = useAuth();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    AOS.refresh();

    // Check if device is mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <SidebarProvider>
      <div className={cn(
        "dashboard-container min-h-screen flex w-full",
        "mobile-viewport", // Add mobile viewport support
        isMobile && "mobile-layout" // Add mobile-specific class
      )}>
        <EnhancedAppSidebar />
        <SidebarInset className="flex-1">
          <MainNavBar />
          <main className={cn(
            "content-wrapper flex-1",
            // Mobile-responsive padding
            "p-3 sm:p-4 md:p-6",
            // Mobile-responsive top padding for navbar
            "pt-16 sm:pt-20 md:pt-24",
            // Add safe area support
            "pb-safe-bottom",
            // Mobile content optimization
            isMobile && "mobile-content-area"
          )} data-aos="fade-up" data-aos-duration="600">
            {/* Token Expiry Warning */}
            {isAuthenticated && (
              <div className="mb-4" data-aos="fade-down" data-aos-delay="100">
                <TokenExpiryWarning />
              </div>
            )}

            {title && (
              <div className={cn(
                "glassmorphism rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8",
                // Mobile-specific styling
                isMobile && "mobile-card rounded-lg p-3 mb-4"
              )} data-aos="fade-down" data-aos-delay="200">
                <h1 className={cn(
                  "text-2xl sm:text-3xl md:text-4xl font-bold modern-heading",
                  // Mobile-responsive text sizing
                  isMobile && "text-xl font-semibold"
                )}>
                  {title}
                </h1>
              </div>
            )}
            <div className={cn(
              // Mobile content wrapper
              isMobile && "mobile-container"
            )} data-aos="fade-in" data-aos-delay="400">
              {children}
            </div>
          </main>
        </SidebarInset>

        {/* AI Floating Buttons Container - Side by side positioning */}
        <div className="ai-floating-container">
          <VoiceNavigationButton
            position="bottom-right"
            size="md"
            showTooltip={true}
          />
        </div>
      </div>
    </SidebarProvider>
  );
}
