// Direct test of Resend API with the new domain
const RESEND_API_KEY = 're_iEWVBukX_AM4BhzUNeyxxVTLdHNrLLGqs';

async function testResendDirect() {
  try {
    console.log('🧪 Testing Resend API directly with verified domain...');

    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'CTNL AI WORK-BOARD <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: '🧪 Direct Resend API Test - CTNL AI WORK-BOARD',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #dc2626;">🚀 CTNL AI WORK-BOARD</h2>
            <p>This is a direct test of the Resend API using the verified domain <strong>ai.ctnigeria.com</strong>.</p>
            
            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>✅ Test Results</h3>
              <ul>
                <li>✅ Domain: ai.ctnigeria.com (verified)</li>
                <li>✅ From: CTNL AI WORK-BOARD &lt;<EMAIL>&gt;</li>
                <li>✅ API Key: Configured</li>
                <li>✅ Email Service: Active</li>
              </ul>
            </div>
            
            <p>If you receive this email, the Resend integration is working correctly with the new domain and app name.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="https://ai.ctnigeria.com/user-manual.html" style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                📖 View User Manual
              </a>
            </div>
            
            <p style="color: #666; font-size: 12px; margin-top: 30px;">
              © 2024 CTNL AI WORK-BOARD. All rights reserved.
            </p>
          </div>
        `,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Resend API error: ${response.status} - ${error}`);
    }

    const result = await response.json();
    
    console.log('✅ Direct Resend API test successful!');
    console.log('📧 Email ID:', result.id);
    console.log('📊 Full response:', result);

    return {
      success: true,
      emailId: result.id,
      message: 'Direct Resend API test completed successfully'
    };

  } catch (error) {
    console.error('❌ Direct Resend API test failed:', error);
    throw error;
  }
}

// Execute the test
testResendDirect()
  .then((result) => {
    console.log('🎉 Direct API test completed:', result);
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Direct API test failed:', error);
    process.exit(1);
  });
