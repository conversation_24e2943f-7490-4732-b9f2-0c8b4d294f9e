import { toast } from 'sonner';

export interface AIError {
  code: string;
  message: string;
  type: 'api_key_missing' | 'quota_exceeded' | 'network_error' | 'invalid_request' | 'unknown';
  suggestion?: string;
  actionUrl?: string;
}

export class AIErrorHandler {
  static parseError(error: any): AIError {
    const errorMessage = error?.message || error?.error || error || 'Unknown error occurred';
    const errorString = typeof errorMessage === 'string' ? errorMessage.toLowerCase() : '';

    // API Key related errors
    if (errorString.includes('openai api key not configured') || 
        errorString.includes('api key not configured') ||
        errorString.includes('invalid api key') ||
        errorString.includes('authentication failed')) {
      return {
        code: 'API_KEY_MISSING',
        message: 'OpenAI API key is not configured or invalid',
        type: 'api_key_missing',
        suggestion: 'Please contact your administrator to configure the OpenAI API key in the system settings.',
        actionUrl: '/dashboard/admin/api-keys'
      };
    }

    // Quota/Rate limit errors
    if (errorString.includes('quota') || 
        errorString.includes('rate limit') ||
        errorString.includes('insufficient_quota')) {
      return {
        code: 'QUOTA_EXCEEDED',
        message: 'API quota exceeded or rate limit reached',
        type: 'quota_exceeded',
        suggestion: 'Please try again later or contact your administrator to upgrade the API plan.'
      };
    }

    // Network errors
    if (errorString.includes('network') || 
        errorString.includes('connection') ||
        errorString.includes('timeout') ||
        errorString.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: 'Network connection error',
        type: 'network_error',
        suggestion: 'Please check your internet connection and try again.'
      };
    }

    // Invalid request errors
    if (errorString.includes('invalid request') || 
        errorString.includes('bad request') ||
        errorString.includes('validation')) {
      return {
        code: 'INVALID_REQUEST',
        message: 'Invalid request format or parameters',
        type: 'invalid_request',
        suggestion: 'Please try rephrasing your request or contact support if the issue persists.'
      };
    }

    // Default unknown error
    return {
      code: 'UNKNOWN_ERROR',
      message: errorMessage,
      type: 'unknown',
      suggestion: 'An unexpected error occurred. Please try again or contact support if the issue persists.'
    };
  }

  static handleError(error: any, context?: string): AIError {
    const parsedError = this.parseError(error);
    
    console.error(`AI Error in ${context || 'unknown context'}:`, {
      originalError: error,
      parsedError
    });

    // Show appropriate toast notification
    this.showErrorToast(parsedError, context);

    return parsedError;
  }

  static showErrorToast(error: AIError, context?: string) {
    const title = context ? `AI Error - ${context}` : 'AI Operation Failed';
    
    switch (error.type) {
      case 'api_key_missing':
        toast.error(title, {
          description: (
            <div className="space-y-2">
              <p className="text-sm">{error.message}</p>
              <p className="text-xs text-muted-foreground">{error.suggestion}</p>
              {error.actionUrl && (
                <p className="text-xs">
                  <a 
                    href={error.actionUrl} 
                    className="text-blue-500 hover:underline"
                    onClick={() => toast.dismiss()}
                  >
                    Go to API Key Settings →
                  </a>
                </p>
              )}
            </div>
          ),
          duration: 8000,
        });
        break;

      case 'quota_exceeded':
        toast.error(title, {
          description: (
            <div className="space-y-2">
              <p className="text-sm">{error.message}</p>
              <p className="text-xs text-muted-foreground">{error.suggestion}</p>
            </div>
          ),
          duration: 6000,
        });
        break;

      case 'network_error':
        toast.error(title, {
          description: (
            <div className="space-y-2">
              <p className="text-sm">{error.message}</p>
              <p className="text-xs text-muted-foreground">{error.suggestion}</p>
            </div>
          ),
          duration: 5000,
        });
        break;

      default:
        toast.error(title, {
          description: (
            <div className="space-y-2">
              <p className="text-sm">{error.message}</p>
              {error.suggestion && (
                <p className="text-xs text-muted-foreground">{error.suggestion}</p>
              )}
            </div>
          ),
          duration: 5000,
        });
        break;
    }
  }

  static createErrorMessage(error: AIError): JSX.Element {
    return (
      <div className="p-4 border border-destructive/50 bg-destructive/10 rounded-lg">
        <div className="flex items-start gap-3">
          <div className="w-6 h-6 rounded-full bg-destructive/20 flex items-center justify-center flex-shrink-0 mt-0.5">
            <span className="text-destructive text-sm font-bold">!</span>
          </div>
          <div className="flex-1 space-y-2">
            <div className="font-medium text-destructive">
              {error.type === 'api_key_missing' ? 'API Key Required' : 'AI Service Error'}
            </div>
            <p className="text-sm text-muted-foreground">{error.message}</p>
            {error.suggestion && (
              <p className="text-xs text-muted-foreground bg-muted/50 p-2 rounded">
                💡 {error.suggestion}
              </p>
            )}
            {error.actionUrl && (
              <div className="pt-2">
                <a 
                  href={error.actionUrl}
                  className="inline-flex items-center gap-1 text-xs text-blue-600 hover:text-blue-700 hover:underline"
                >
                  Configure API Keys →
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Utility method to wrap AI operations with error handling
  static async wrapAIOperation<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<{ data: T | null; error: AIError | null }> {
    try {
      const data = await operation();
      return { data, error: null };
    } catch (error) {
      const aiError = this.handleError(error, context);
      return { data: null, error: aiError };
    }
  }
}

// Hook for using AI error handling in components
export const useAIErrorHandler = () => {
  const handleAIError = (error: any, context?: string) => {
    return AIErrorHandler.handleError(error, context);
  };

  const wrapAIOperation = async <T>(
    operation: () => Promise<T>,
    context: string
  ) => {
    return AIErrorHandler.wrapAIOperation(operation, context);
  };

  const createErrorMessage = (error: AIError) => {
    return AIErrorHandler.createErrorMessage(error);
  };

  return {
    handleAIError,
    wrapAIOperation,
    createErrorMessage,
    parseError: AIErrorHandler.parseError
  };
}; 