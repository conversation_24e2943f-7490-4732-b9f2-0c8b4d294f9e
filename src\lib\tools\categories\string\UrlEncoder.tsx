import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Link, ArrowUpDown, Globe } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const UrlEncoder: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [mode, setMode] = useState('encode');
  const [encodingType, setEncodingType] = useState('component');
  const [result, setResult] = useState('');
  const [error, setError] = useState('');

  const encodingTypes = [
    { value: 'component', label: 'URI Component', description: 'Encode for use in URL parameters' },
    { value: 'uri', label: 'Full URI', description: 'Encode complete URI' },
    { value: 'form', label: 'Form Data', description: 'Encode for form submission (application/x-www-form-urlencoded)' }
  ];

  const performOperation = () => {
    if (!inputText.trim()) {
      setResult('');
      setError('');
      return;
    }

    try {
      setError('');
      let output = '';

      if (mode === 'encode') {
        switch (encodingType) {
          case 'component':
            output = encodeURIComponent(inputText);
            break;
          case 'uri':
            output = encodeURI(inputText);
            break;
          case 'form':
            // Form encoding: spaces become +, special chars become %XX
            output = inputText
              .replace(/[!'()*]/g, (c) => '%' + c.charCodeAt(0).toString(16).toUpperCase())
              .replace(/%20/g, '+')
              .replace(/\s/g, '+');
            output = encodeURIComponent(inputText).replace(/%20/g, '+');
            break;
          default:
            output = encodeURIComponent(inputText);
        }
      } else {
        // Decode mode
        try {
          switch (encodingType) {
            case 'component':
              output = decodeURIComponent(inputText);
              break;
            case 'uri':
              output = decodeURI(inputText);
              break;
            case 'form':
              // Form decoding: + becomes space, %XX becomes char
              const formDecoded = inputText.replace(/\+/g, ' ');
              output = decodeURIComponent(formDecoded);
              break;
            default:
              output = decodeURIComponent(inputText);
          }
        } catch (decodeError) {
          throw new Error('Invalid encoded string. Please check your input.');
        }
      }

      setResult(output);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during processing');
      setResult('');
    }
  };

  useEffect(() => {
    performOperation();
  }, [inputText, mode, encodingType]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
    setError('');
  };

  const swapInputOutput = () => {
    if (result && !error) {
      setInputText(result);
      setMode(mode === 'encode' ? 'decode' : 'encode');
    }
  };

  const loadSampleData = () => {
    const samples = {
      encode: [
        'Hello World!',
        '<EMAIL>',
        'https://example.com/path?param=value&other=test',
        'Special chars: !@#$%^&*()+={}[]|\\:";\'<>?,./~`',
        'Multi-line\ntext with\ttabs and spaces'
      ],
      decode: [
        'Hello%20World%21',
        'user%40example.com',
        'https%3A//example.com/path%3Fparam%3Dvalue%26other%3Dtest',
        'Special%20chars%3A%20%21%40%23%24%25%5E%26*%28%29%2B%3D%7B%7D%5B%5D%7C%5C%3A%22%3B%27%3C%3E%3F%2C./%7E%60',
        'Multi-line%0Atext%20with%09tabs%20and%20spaces'
      ]
    };

    const sampleArray = samples[mode as keyof typeof samples];
    const randomSample = sampleArray[Math.floor(Math.random() * sampleArray.length)];
    setInputText(randomSample);
  };

  const getStats = () => {
    const inputLength = inputText.length;
    const outputLength = result.length;
    const compressionRatio = inputLength > 0 ? ((outputLength - inputLength) / inputLength * 100) : 0;
    
    return {
      inputLength,
      outputLength,
      compressionRatio: compressionRatio.toFixed(1),
      operation: mode === 'encode' ? 'Encoding' : 'Decoding'
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 border-emerald-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-emerald-800">
            <div className="p-2 bg-emerald-100 rounded-lg">
              <Globe className="h-6 w-6 text-emerald-600" />
            </div>
            {title || 'URL Encoder/Decoder'}
          </CardTitle>
          <p className="text-emerald-600 text-sm">
            Encode or decode URLs and URI components for web development
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Mode and Type Selection */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Operation:</label>
              <Select value={mode} onValueChange={setMode}>
                <SelectTrigger className="border-2 border-emerald-200 focus:border-emerald-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="encode">
                    <div className="flex items-center gap-2">
                      <span>🔒</span>
                      <span>Encode</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="decode">
                    <div className="flex items-center gap-2">
                      <span>🔓</span>
                      <span>Decode</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Encoding Type:</label>
              <Select value={encodingType} onValueChange={setEncodingType}>
                <SelectTrigger className="border-2 border-emerald-200 focus:border-emerald-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {encodingTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      <div>
                        <div className="font-medium">{type.label}</div>
                        <div className="text-xs text-gray-500">{type.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span>📝</span>
                {mode === 'encode' ? 'Text to Encode:' : 'Text to Decode:'}
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleData}
                className="flex items-center gap-2"
              >
                <Link className="h-4 w-4" />
                Sample
              </Button>
            </div>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder={mode === 'encode' 
                ? "Enter text or URL to encode..." 
                : "Enter encoded text to decode..."
              }
              className="min-h-[120px] resize-none border-2 border-emerald-200 focus:border-emerald-400"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.operation}
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                📏 {stats.inputLength} → {stats.outputLength} chars
              </Badge>
              <Badge variant="secondary" className={`${
                parseFloat(stats.compressionRatio) > 0 
                  ? 'bg-orange-100 text-orange-800' 
                  : 'bg-purple-100 text-purple-800'
              }`}>
                📈 {stats.compressionRatio}% change
              </Badge>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              onClick={swapInputOutput}
              disabled={!result || !!error}
              className="flex items-center gap-2"
            >
              <ArrowUpDown className="h-4 w-4" />
              Swap & Switch Mode
            </Button>
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-800">
                <span>❌</span>
                <span className="font-medium">Error:</span>
              </div>
              <p className="text-red-700 text-sm mt-1">{error}</p>
            </div>
          )}

          {/* Result */}
          {result && !error && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  {mode === 'encode' ? 'Encoded Result:' : 'Decoded Result:'}
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[120px] bg-gray-50 border-2 border-gray-200 font-mono text-sm"
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Encoding Types Guide
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">URI Component:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>For URL parameters</li>
                <li>Encodes all special chars</li>
                <li>Safe for query strings</li>
                <li>Most restrictive</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Full URI:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>For complete URLs</li>
                <li>Preserves :/?#[]@</li>
                <li>Encodes other specials</li>
                <li>Less restrictive</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-purple-600">Form Data:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>For form submissions</li>
                <li>Spaces become +</li>
                <li>application/x-www-form-urlencoded</li>
                <li>Legacy format</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UrlEncoder;
