// Create database tables for AI, System Logs, User Activities, Time Log, and Task Assignment
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function createAdvancedModulesTables() {
  console.log('🚀 Starting advanced modules tables creation...');
  
  try {
    // Create all advanced module tables
    logStep('Creating advanced modules database tables...');
    
    const { error: tablesError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- AI Interactions Table (Enhanced)
        CREATE TABLE IF NOT EXISTS public.ai_interactions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          session_id UUID DEFAULT uuid_generate_v4(),
          role TEXT DEFAULT 'user' CHECK (role IN ('user', 'assistant', 'system')),
          message TEXT NOT NULL,
          response TEXT,
          type TEXT DEFAULT 'chat' CHECK (type IN ('chat', 'command', 'query', 'help', 'analysis')),
          context JSONB DEFAULT '{}',
          actions JSONB DEFAULT '[]',
          suggestions TEXT[],
          confidence_score DECIMAL(3,2) DEFAULT 0.0,
          processing_time_ms INTEGER DEFAULT 0,
          tokens_used INTEGER DEFAULT 0,
          model_used TEXT DEFAULT 'enhanced-ai',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- System Logs Table
        CREATE TABLE IF NOT EXISTS public.system_logs (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          level TEXT NOT NULL CHECK (level IN ('debug', 'info', 'warn', 'error', 'critical')),
          category TEXT NOT NULL DEFAULT 'general',
          subcategory TEXT,
          message TEXT NOT NULL,
          details TEXT,
          source TEXT,
          function_name TEXT,
          file_path TEXT,
          line_number INTEGER,
          stack_trace TEXT,
          request_id UUID,
          session_id UUID,
          ip_address INET,
          user_agent TEXT,
          endpoint TEXT,
          method TEXT,
          status_code INTEGER,
          response_time_ms INTEGER,
          error_code TEXT,
          error_type TEXT,
          context JSONB DEFAULT '{}',
          metadata JSONB DEFAULT '{}',
          resolved BOOLEAN DEFAULT FALSE,
          resolved_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          resolved_at TIMESTAMP WITH TIME ZONE,
          resolution_notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- User Activities Table (Enhanced)
        CREATE TABLE IF NOT EXISTS public.user_activities (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          activity_type TEXT NOT NULL,
          action TEXT NOT NULL,
          description TEXT,
          entity_type TEXT,
          entity_id UUID,
          entity_name TEXT,
          old_values JSONB DEFAULT '{}',
          new_values JSONB DEFAULT '{}',
          changes JSONB DEFAULT '{}',
          impact_level TEXT DEFAULT 'low' CHECK (impact_level IN ('low', 'medium', 'high', 'critical')),
          category TEXT DEFAULT 'general',
          subcategory TEXT,
          tags TEXT[],
          ip_address INET,
          user_agent TEXT,
          location TEXT,
          device_info JSONB DEFAULT '{}',
          session_id UUID,
          request_id UUID,
          duration_ms INTEGER,
          success BOOLEAN DEFAULT TRUE,
          error_message TEXT,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Time Logs Table
        CREATE TABLE IF NOT EXISTS public.time_logs (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES public.profiles(user_id) ON DELETE CASCADE,
          project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
          task_id UUID,
          activity_type TEXT NOT NULL DEFAULT 'work' CHECK (activity_type IN ('work', 'break', 'meeting', 'training', 'admin', 'other')),
          description TEXT,
          start_time TIMESTAMP WITH TIME ZONE NOT NULL,
          end_time TIMESTAMP WITH TIME ZONE,
          duration_minutes INTEGER,
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
          location TEXT,
          device_info JSONB DEFAULT '{}',
          is_billable BOOLEAN DEFAULT TRUE,
          hourly_rate DECIMAL(10,2),
          total_amount DECIMAL(10,2),
          approved BOOLEAN DEFAULT FALSE,
          approved_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          approved_at TIMESTAMP WITH TIME ZONE,
          notes TEXT,
          tags TEXT[],
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Tasks Table
        CREATE TABLE IF NOT EXISTS public.tasks (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          description TEXT,
          project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
          assigned_to UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          created_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          parent_task_id UUID REFERENCES public.tasks(id) ON DELETE CASCADE,
          status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'testing', 'done', 'cancelled', 'blocked')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'critical')),
          type TEXT DEFAULT 'task' CHECK (type IN ('task', 'bug', 'feature', 'improvement', 'research', 'documentation')),
          category TEXT,
          tags TEXT[],
          estimated_hours DECIMAL(5,2),
          actual_hours DECIMAL(5,2) DEFAULT 0,
          progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
          start_date DATE,
          due_date DATE,
          completed_date DATE,
          dependencies UUID[],
          blockers TEXT[],
          acceptance_criteria TEXT[],
          attachments JSONB DEFAULT '[]',
          comments_count INTEGER DEFAULT 0,
          watchers UUID[],
          labels TEXT[],
          custom_fields JSONB DEFAULT '{}',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Task Comments Table
        CREATE TABLE IF NOT EXISTS public.task_comments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
          user_id UUID NOT NULL REFERENCES public.profiles(user_id) ON DELETE CASCADE,
          comment TEXT NOT NULL,
          comment_type TEXT DEFAULT 'comment' CHECK (comment_type IN ('comment', 'status_change', 'assignment', 'time_log', 'attachment')),
          mentions UUID[],
          attachments JSONB DEFAULT '[]',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Task Assignments Table (for tracking assignment history)
        CREATE TABLE IF NOT EXISTS public.task_assignments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          task_id UUID NOT NULL REFERENCES public.tasks(id) ON DELETE CASCADE,
          assigned_to UUID NOT NULL REFERENCES public.profiles(user_id) ON DELETE CASCADE,
          assigned_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          role TEXT DEFAULT 'assignee' CHECK (role IN ('assignee', 'reviewer', 'watcher', 'collaborator')),
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'removed')),
          assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          completed_at TIMESTAMP WITH TIME ZONE,
          notes TEXT,
          metadata JSONB DEFAULT '{}'
        );
        
        -- AI Knowledge Base Table
        CREATE TABLE IF NOT EXISTS public.ai_knowledge_base (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          category TEXT NOT NULL,
          subcategory TEXT,
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          keywords TEXT[],
          tags TEXT[],
          context JSONB DEFAULT '{}',
          confidence_score DECIMAL(3,2) DEFAULT 1.0,
          usage_count INTEGER DEFAULT 0,
          last_used TIMESTAMP WITH TIME ZONE,
          created_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          updated_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          is_active BOOLEAN DEFAULT TRUE,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON public.ai_interactions(user_id);
        CREATE INDEX IF NOT EXISTS idx_ai_interactions_session_id ON public.ai_interactions(session_id);
        CREATE INDEX IF NOT EXISTS idx_ai_interactions_type ON public.ai_interactions(type);
        CREATE INDEX IF NOT EXISTS idx_ai_interactions_created_at ON public.ai_interactions(created_at);
        
        CREATE INDEX IF NOT EXISTS idx_system_logs_level ON public.system_logs(level);
        CREATE INDEX IF NOT EXISTS idx_system_logs_category ON public.system_logs(category);
        CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON public.system_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON public.system_logs(created_at);
        CREATE INDEX IF NOT EXISTS idx_system_logs_resolved ON public.system_logs(resolved);
        
        CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON public.user_activities(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_activities_activity_type ON public.user_activities(activity_type);
        CREATE INDEX IF NOT EXISTS idx_user_activities_entity_type ON public.user_activities(entity_type);
        CREATE INDEX IF NOT EXISTS idx_user_activities_created_at ON public.user_activities(created_at);
        
        CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_time_logs_project_id ON public.time_logs(project_id);
        CREATE INDEX IF NOT EXISTS idx_time_logs_start_time ON public.time_logs(start_time);
        CREATE INDEX IF NOT EXISTS idx_time_logs_status ON public.time_logs(status);
        
        CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON public.tasks(assigned_to);
        CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
        CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);
        CREATE INDEX IF NOT EXISTS idx_tasks_priority ON public.tasks(priority);
        CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON public.tasks(due_date);
        CREATE INDEX IF NOT EXISTS idx_tasks_created_by ON public.tasks(created_by);
        
        CREATE INDEX IF NOT EXISTS idx_task_comments_task_id ON public.task_comments(task_id);
        CREATE INDEX IF NOT EXISTS idx_task_comments_user_id ON public.task_comments(user_id);
        
        CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON public.task_assignments(task_id);
        CREATE INDEX IF NOT EXISTS idx_task_assignments_assigned_to ON public.task_assignments(assigned_to);
        
        CREATE INDEX IF NOT EXISTS idx_ai_knowledge_base_category ON public.ai_knowledge_base(category);
        CREATE INDEX IF NOT EXISTS idx_ai_knowledge_base_keywords ON public.ai_knowledge_base USING GIN(keywords);
        CREATE INDEX IF NOT EXISTS idx_ai_knowledge_base_tags ON public.ai_knowledge_base USING GIN(tags);
      `
    });

    if (tablesError) {
      throw new Error('Advanced modules tables creation failed: ' + tablesError.message);
    }

    logSuccess('Advanced modules tables created successfully');

    // Enable RLS on all tables
    logStep('Enabling RLS on advanced modules tables...');
    
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Enable RLS on all advanced module tables
        ALTER TABLE public.ai_interactions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.system_logs ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.user_activities ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.task_comments ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.task_assignments ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.ai_knowledge_base ENABLE ROW LEVEL SECURITY;
        
        -- Create RLS policies for AI interactions
        CREATE POLICY "ai_interactions_select_all" ON public.ai_interactions FOR SELECT USING (true);
        CREATE POLICY "ai_interactions_insert_own" ON public.ai_interactions FOR INSERT WITH CHECK (true);
        CREATE POLICY "ai_interactions_update_own" ON public.ai_interactions FOR UPDATE USING (true);
        
        -- Create RLS policies for system logs
        CREATE POLICY "system_logs_select_all" ON public.system_logs FOR SELECT USING (true);
        CREATE POLICY "system_logs_insert_all" ON public.system_logs FOR INSERT WITH CHECK (true);
        CREATE POLICY "system_logs_update_admin" ON public.system_logs FOR UPDATE USING (true);
        
        -- Create RLS policies for user activities
        CREATE POLICY "user_activities_select_all" ON public.user_activities FOR SELECT USING (true);
        CREATE POLICY "user_activities_insert_all" ON public.user_activities FOR INSERT WITH CHECK (true);
        
        -- Create RLS policies for time logs
        CREATE POLICY "time_logs_select_all" ON public.time_logs FOR SELECT USING (true);
        CREATE POLICY "time_logs_insert_own" ON public.time_logs FOR INSERT WITH CHECK (true);
        CREATE POLICY "time_logs_update_own" ON public.time_logs FOR UPDATE USING (true);
        
        -- Create RLS policies for tasks
        CREATE POLICY "tasks_select_all" ON public.tasks FOR SELECT USING (true);
        CREATE POLICY "tasks_insert_all" ON public.tasks FOR INSERT WITH CHECK (true);
        CREATE POLICY "tasks_update_all" ON public.tasks FOR UPDATE USING (true);
        
        -- Create RLS policies for task comments
        CREATE POLICY "task_comments_select_all" ON public.task_comments FOR SELECT USING (true);
        CREATE POLICY "task_comments_insert_all" ON public.task_comments FOR INSERT WITH CHECK (true);
        CREATE POLICY "task_comments_update_own" ON public.task_comments FOR UPDATE USING (true);
        
        -- Create RLS policies for task assignments
        CREATE POLICY "task_assignments_select_all" ON public.task_assignments FOR SELECT USING (true);
        CREATE POLICY "task_assignments_insert_all" ON public.task_assignments FOR INSERT WITH CHECK (true);
        CREATE POLICY "task_assignments_update_all" ON public.task_assignments FOR UPDATE USING (true);
        
        -- Create RLS policies for AI knowledge base
        CREATE POLICY "ai_knowledge_base_select_all" ON public.ai_knowledge_base FOR SELECT USING (true);
        CREATE POLICY "ai_knowledge_base_insert_all" ON public.ai_knowledge_base FOR INSERT WITH CHECK (true);
        CREATE POLICY "ai_knowledge_base_update_all" ON public.ai_knowledge_base FOR UPDATE USING (true);
        
        -- Grant permissions
        GRANT ALL ON public.ai_interactions TO authenticated;
        GRANT ALL ON public.ai_interactions TO anon;
        GRANT ALL ON public.system_logs TO authenticated;
        GRANT ALL ON public.system_logs TO anon;
        GRANT ALL ON public.user_activities TO authenticated;
        GRANT ALL ON public.user_activities TO anon;
        GRANT ALL ON public.time_logs TO authenticated;
        GRANT ALL ON public.time_logs TO anon;
        GRANT ALL ON public.tasks TO authenticated;
        GRANT ALL ON public.tasks TO anon;
        GRANT ALL ON public.task_comments TO authenticated;
        GRANT ALL ON public.task_comments TO anon;
        GRANT ALL ON public.task_assignments TO authenticated;
        GRANT ALL ON public.task_assignments TO anon;
        GRANT ALL ON public.ai_knowledge_base TO authenticated;
        GRANT ALL ON public.ai_knowledge_base TO anon;
      `
    });

    if (rlsError) {
      console.warn('RLS setup warning:', rlsError.message);
    } else {
      logSuccess('RLS policies created successfully');
    }

    logSuccess('🎉 ADVANCED MODULES TABLES CREATION COMPLETED!');
    console.log('\n🚀 CREATED TABLES:');
    console.log('✅ ai_interactions - Enhanced AI chat and interactions');
    console.log('✅ system_logs - Comprehensive system logging');
    console.log('✅ user_activities - Detailed user activity tracking');
    console.log('✅ time_logs - Time tracking and logging');
    console.log('✅ tasks - Task management and assignment');
    console.log('✅ task_comments - Task comments and discussions');
    console.log('✅ task_assignments - Task assignment tracking');
    console.log('✅ ai_knowledge_base - AI knowledge and context');
    console.log('\n🎯 All advanced modules are now ready for implementation!');
    
    return true;

  } catch (error) {
    logError(`Advanced modules tables creation failed: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
}

// Run the creation
createAdvancedModulesTables()
  .then((success) => {
    if (success) {
      console.log('\n🎉 SUCCESS: Advanced modules tables created successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Advanced modules tables creation encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
