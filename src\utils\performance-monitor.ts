// Performance monitoring and analytics for CT Nigeria Platform

interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  timeToInteractive: number;
  totalBlockingTime: number;
}

interface UserSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  pageViews: string[];
  interactions: number;
  errors: Array<{
    message: string;
    stack?: string;
    timestamp: number;
    url: string;
  }>;
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private session: UserSession;
  private observer?: PerformanceObserver;

  constructor() {
    this.session = {
      sessionId: this.generateSessionId(),
      startTime: Date.now(),
      pageViews: [],
      interactions: 0,
      errors: []
    };

    this.init();
  }

  private init() {
    // Monitor Core Web Vitals
    this.observeWebVitals();
    
    // Monitor page navigation
    this.observeNavigation();
    
    // Monitor errors
    this.observeErrors();
    
    // Monitor user interactions
    this.observeInteractions();
    
    // Send metrics periodically
    this.startPeriodicReporting();
  }

  private observeWebVitals() {
    // Largest Contentful Paint (LCP)
    this.observeMetric('largest-contentful-paint', (entry) => {
      this.metrics.largestContentfulPaint = entry.startTime;
    });

    // First Input Delay (FID)
    this.observeMetric('first-input', (entry) => {
      this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
    });

    // Cumulative Layout Shift (CLS)
    this.observeMetric('layout-shift', (entry) => {
      if (!entry.hadRecentInput) {
        this.metrics.cumulativeLayoutShift = 
          (this.metrics.cumulativeLayoutShift || 0) + entry.value;
      }
    });

    // First Contentful Paint (FCP)
    this.observeMetric('paint', (entry) => {
      if (entry.name === 'first-contentful-paint') {
        this.metrics.firstContentfulPaint = entry.startTime;
      }
    });

    // Navigation timing
    this.observeMetric('navigation', (entry) => {
      this.metrics.pageLoadTime = entry.loadEventEnd - entry.fetchStart;
      this.metrics.timeToInteractive = entry.domInteractive - entry.fetchStart;
      this.metrics.totalBlockingTime = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
    });
  }

  private observeMetric(type: string, callback: (entry: any) => void) {
    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback);
      });
      
      observer.observe({ type, buffered: true });
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error);
    }
  }

  private observeNavigation() {
    // Track page views
    const trackPageView = () => {
      const path = window.location.pathname;
      this.session.pageViews.push(path);
      this.logEvent('page_view', { path, timestamp: Date.now() });
    };

    // Initial page view
    trackPageView();

    // Track navigation changes (for SPA)
    let currentPath = window.location.pathname;
    const checkPathChange = () => {
      if (window.location.pathname !== currentPath) {
        currentPath = window.location.pathname;
        trackPageView();
      }
    };

    // Check for path changes periodically
    setInterval(checkPathChange, 1000);
  }

  private observeErrors() {
    // JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        timestamp: Date.now(),
        url: event.filename || window.location.href
      });
    });

    // Promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        timestamp: Date.now(),
        url: window.location.href
      });
    });

    // Resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.logError({
          message: `Resource failed to load: ${(event.target as any)?.src || (event.target as any)?.href}`,
          timestamp: Date.now(),
          url: window.location.href
        });
      }
    }, true);
  }

  private observeInteractions() {
    const interactionEvents = ['click', 'keydown', 'scroll', 'touchstart'];
    
    interactionEvents.forEach(eventType => {
      document.addEventListener(eventType, () => {
        this.session.interactions++;
      }, { passive: true });
    });
  }

  private startPeriodicReporting() {
    // Send metrics every 30 seconds
    setInterval(() => {
      this.sendMetrics();
    }, 30000);

    // Send metrics before page unload
    window.addEventListener('beforeunload', () => {
      this.sendMetrics(true);
    });

    // Send metrics when page becomes hidden
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.sendMetrics(true);
      }
    });
  }

  private logError(error: UserSession['errors'][0]) {
    this.session.errors.push(error);
    console.error('Performance Monitor - Error logged:', error);
  }

  private logEvent(eventName: string, data: any) {
    console.log(`Performance Monitor - ${eventName}:`, data);
  }

  private async sendMetrics(isBeacon = false) {
    const payload = {
      sessionId: this.session.sessionId,
      userId: this.session.userId,
      metrics: this.metrics,
      session: {
        duration: Date.now() - this.session.startTime,
        pageViews: this.session.pageViews.length,
        interactions: this.session.interactions,
        errors: this.session.errors.length
      },
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    try {
      if (isBeacon && navigator.sendBeacon) {
        // Use beacon for reliable delivery during page unload
        navigator.sendBeacon('/api/analytics/performance', JSON.stringify(payload));
      } else {
        // Regular fetch for periodic reporting
        await fetch('/api/analytics/performance', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });
      }
    } catch (error) {
      console.warn('Failed to send performance metrics:', error);
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  public setUserId(userId: string) {
    this.session.userId = userId;
  }

  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics };
  }

  public getSession(): UserSession {
    return { ...this.session };
  }

  // Manual event tracking
  public trackEvent(eventName: string, properties?: Record<string, any>) {
    this.logEvent(eventName, {
      ...properties,
      timestamp: Date.now(),
      sessionId: this.session.sessionId
    });
  }

  // Performance timing helpers
  public measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    
    this.trackEvent('function_performance', {
      functionName: name,
      duration,
      timestamp: Date.now()
    });
    
    return result;
  }

  public async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    
    this.trackEvent('async_function_performance', {
      functionName: name,
      duration,
      timestamp: Date.now()
    });
    
    return result;
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export utility functions
export const trackEvent = (eventName: string, properties?: Record<string, any>) => 
  performanceMonitor.trackEvent(eventName, properties);

export const measureFunction = <T>(name: string, fn: () => T): T => 
  performanceMonitor.measureFunction(name, fn);

export const measureAsyncFunction = <T>(name: string, fn: () => Promise<T>): Promise<T> => 
  performanceMonitor.measureAsyncFunction(name, fn);

export const setUserId = (userId: string) => 
  performanceMonitor.setUserId(userId);

export const getPerformanceMetrics = () => 
  performanceMonitor.getMetrics();
