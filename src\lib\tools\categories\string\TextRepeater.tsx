import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Repeat, Plus, Minus } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const TextRepeater: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [repeatCount, setRepeatCount] = useState(3);
  const [separator, setSeparator] = useState('');
  const [addNumbers, setAddNumbers] = useState(false);
  const [numberFormat, setNumberFormat] = useState('1. ');
  const [addLineBreaks, setAddLineBreaks] = useState(false);
  const [result, setResult] = useState('');

  const presetSeparators = [
    { value: '', label: 'No Separator' },
    { value: ' ', label: 'Space' },
    { value: ', ', label: 'Comma + Space' },
    { value: '\n', label: 'New Line' },
    { value: ' | ', label: 'Pipe ( | )' },
    { value: ' - ', label: 'Dash ( - )' },
    { value: '\t', label: 'Tab' },
    { value: 'custom', label: 'Custom' }
  ];

  const numberFormats = [
    { value: '1. ', label: '1. 2. 3.' },
    { value: '1) ', label: '1) 2) 3)' },
    { value: '(1) ', label: '(1) (2) (3)' },
    { value: '[1] ', label: '[1] [2] [3]' },
    { value: '1: ', label: '1: 2: 3:' },
    { value: '1 - ', label: '1 - 2 - 3 -' },
    { value: '#1 ', label: '#1 #2 #3' },
    { value: 'custom', label: 'Custom' }
  ];

  const performRepeat = () => {
    if (!inputText.trim() || repeatCount <= 0) {
      setResult('');
      return;
    }

    try {
      const count = Math.min(Math.max(1, repeatCount), 10000); // Limit to prevent browser freeze
      const repeatedItems: string[] = [];

      for (let i = 1; i <= count; i++) {
        let item = inputText;

        // Add numbering if enabled
        if (addNumbers) {
          let format = numberFormat;
          if (format === 'custom') {
            format = '1. '; // fallback
          }
          
          const formattedNumber = format.replace('1', i.toString());
          item = formattedNumber + item;
        }

        repeatedItems.push(item);
      }

      // Join with separator
      let joinedResult = '';
      if (separator === '\n' || addLineBreaks) {
        joinedResult = repeatedItems.join('\n');
      } else if (separator === '\t') {
        joinedResult = repeatedItems.join('\t');
      } else {
        joinedResult = repeatedItems.join(separator);
      }

      setResult(joinedResult);
    } catch (error) {
      toast.error('Error repeating text');
      setResult('');
    }
  };

  useEffect(() => {
    performRepeat();
  }, [inputText, repeatCount, separator, addNumbers, numberFormat, addLineBreaks]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
  };

  const adjustCount = (delta: number) => {
    const newCount = Math.max(1, Math.min(10000, repeatCount + delta));
    setRepeatCount(newCount);
  };

  const loadSampleText = () => {
    const samples = [
      'Hello World!',
      '⭐',
      '🎉 Celebration',
      'Lorem ipsum dolor sit amet',
      'Test line',
      '• Bullet point',
      '---',
      'Row data'
    ];
    const randomSample = samples[Math.floor(Math.random() * samples.length)];
    setInputText(randomSample);
  };

  const getStats = () => {
    const originalLength = inputText.length;
    const resultLength = result.length;
    const actualRepeats = result ? result.split(inputText).length - 1 : 0;
    
    return {
      originalLength,
      resultLength,
      actualRepeats,
      targetRepeats: repeatCount
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-violet-50 via-purple-50 to-fuchsia-50 border-violet-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-violet-800">
            <div className="p-2 bg-violet-100 rounded-lg">
              <Repeat className="h-6 w-6 text-violet-600" />
            </div>
            {title || 'Text Repeater'}
          </CardTitle>
          <p className="text-violet-600 text-sm">
            Repeat text multiple times with custom separators and numbering
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Repeat Count */}
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Repeat Count:</label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => adjustCount(-1)}
                disabled={repeatCount <= 1}
                className="h-10 w-10 p-0"
              >
                <Minus className="h-4 w-4" />
              </Button>
              <Input
                type="number"
                min="1"
                max="10000"
                value={repeatCount}
                onChange={(e) => setRepeatCount(parseInt(e.target.value) || 1)}
                className="text-center border-2 border-violet-200 focus:border-violet-400"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => adjustCount(1)}
                disabled={repeatCount >= 10000}
                className="h-10 w-10 p-0"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-xs text-gray-500">Maximum: 10,000 repetitions</p>
          </div>

          {/* Separator and Numbering */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Separator:</label>
              <Select value={separator} onValueChange={setSeparator}>
                <SelectTrigger className="border-2 border-violet-200 focus:border-violet-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {presetSeparators.map(preset => (
                    <SelectItem key={preset.value} value={preset.value}>
                      {preset.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {separator === 'custom' && (
                <Input
                  placeholder="Enter custom separator..."
                  onChange={(e) => setSeparator(e.target.value)}
                  className="border-2 border-violet-200 focus:border-violet-400"
                />
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Number Format:</label>
              <Select value={numberFormat} onValueChange={setNumberFormat} disabled={!addNumbers}>
                <SelectTrigger className="border-2 border-violet-200 focus:border-violet-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {numberFormats.map(format => (
                    <SelectItem key={format.value} value={format.value}>
                      {format.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Options */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="add-numbers"
                checked={addNumbers}
                onCheckedChange={setAddNumbers}
              />
              <label htmlFor="add-numbers" className="text-sm font-medium">
                Add Numbers
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="add-line-breaks"
                checked={addLineBreaks}
                onCheckedChange={setAddLineBreaks}
                disabled={separator === '\n'}
              />
              <label htmlFor="add-line-breaks" className="text-sm font-medium">
                Force Line Breaks
              </label>
            </div>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                <span>📝</span>
                Text to Repeat:
              </label>
              <Button
                variant="outline"
                size="sm"
                onClick={loadSampleText}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Sample
              </Button>
            </div>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter the text you want to repeat..."
              className="min-h-[100px] resize-none border-2 border-violet-200 focus:border-violet-400"
            />
          </div>

          {/* Quick Repeat Buttons */}
          <div className="flex gap-2 flex-wrap">
            <span className="text-sm font-medium text-gray-700">Quick repeat:</span>
            {[2, 5, 10, 25, 50, 100].map(count => (
              <Button
                key={count}
                variant="outline"
                size="sm"
                onClick={() => setRepeatCount(count)}
                className="h-8 px-3 text-xs"
              >
                {count}x
              </Button>
            ))}
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.targetRepeats} repeats
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                📏 {stats.originalLength} → {stats.resultLength} chars
              </Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                📈 {stats.resultLength > 0 ? Math.round(stats.resultLength / stats.originalLength) : 0}x size
              </Badge>
            </div>
          )}

          {/* Result */}
          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  Repeated Text:
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[160px] max-h-96 bg-gray-50 border-2 border-gray-200 font-mono text-sm"
              />
              
              {result.length > 10000 && (
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 text-yellow-800">
                    <span>⚠️</span>
                    <span className="font-medium">Large Output Warning</span>
                  </div>
                  <p className="text-yellow-700 text-sm mt-1">
                    The output is very large ({result.length.toLocaleString()} characters). 
                    Consider reducing the repeat count for better performance.
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Clear Button */}
          <div className="flex justify-center">
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Use Cases & Examples
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Testing & Development:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Generate test data</li>
                <li>Create dummy content</li>
                <li>Fill forms with sample text</li>
                <li>Stress test applications</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Content Creation:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Create numbered lists</li>
                <li>Generate patterns</li>
                <li>Duplicate templates</li>
                <li>Build repetitive content</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextRepeater;
