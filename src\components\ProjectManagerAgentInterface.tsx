import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  MessageCircle, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Users, 
  Target,
  Lightbulb,
  Settings,
  BarChart3,
  Zap,
  Shield,
  Mic,
  MicOff
} from 'lucide-react';
import { EnhancedProjectManagerAgent, ProjectMetrics, ProjectInsight, ProjectRecommendation } from '@/services/enhanced-project-manager-agent';
import { LanguageSelectionService } from '@/services/language-selection-service';
import { VoiceRecognitionService } from '@/services/voice-recognition-service';
import { VoiceResponseService } from '@/services/voice-response-service';

interface ProjectManagerAgentInterfaceProps {
  projectId?: string;
  className?: string;
  compact?: boolean;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  language?: string;
  actions?: Array<{
    type: string;
    label: string;
    action: () => void;
  }>;
}

export const ProjectManagerAgentInterface: React.FC<ProjectManagerAgentInterfaceProps> = ({
  projectId,
  className = '',
  compact = false
}) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'insights' | 'metrics' | 'recommendations'>('chat');
  const [projectMetrics, setProjectMetrics] = useState<ProjectMetrics | null>(null);
  const [insights, setInsights] = useState<ProjectInsight[]>([]);
  const [recommendations, setRecommendations] = useState<ProjectRecommendation[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en-GB');
  const [agentPersonality, setAgentPersonality] = useState<'professional' | 'friendly' | 'technical'>('friendly');

  useEffect(() => {
    initializeAgent();
  }, []);

  useEffect(() => {
    if (projectId && isInitialized) {
      loadProjectData();
    }
  }, [projectId, isInitialized]);

  const initializeAgent = async () => {
    try {
      await EnhancedProjectManagerAgent.initialize();
      await LanguageSelectionService.initialize();
      
      setCurrentLanguage(LanguageSelectionService.getCurrentLanguage());
      setIsInitialized(true);
      
      // Add welcome message
      addAgentMessage(getLocalizedText('welcome_message'));
    } catch (error) {
      console.error('Error initializing Project Manager Agent:', error);
    }
  };

  const loadProjectData = async () => {
    if (!projectId) return;

    try {
      setIsProcessing(true);
      
      // Load project metrics
      const metrics = await EnhancedProjectManagerAgent.analyzeProjectHealth(projectId);
      setProjectMetrics(metrics);
      
      // Load insights
      const projectInsights = EnhancedProjectManagerAgent.getProjectInsights(projectId);
      setInsights(projectInsights);
      
      // Load recommendations
      const projectRecommendations = await EnhancedProjectManagerAgent.generateProjectRecommendations(projectId);
      setRecommendations(projectRecommendations);
      
      // Add project analysis message
      addAgentMessage(getLocalizedText('project_analysis_complete', { 
        healthScore: metrics.healthScore,
        insightsCount: projectInsights.length,
        recommendationsCount: projectRecommendations.length
      }));
      
    } catch (error) {
      console.error('Error loading project data:', error);
      addAgentMessage(getLocalizedText('error_loading_project'));
    } finally {
      setIsProcessing(false);
    }
  };

  const addAgentMessage = (content: string, actions?: any[]) => {
    const message: ChatMessage = {
      id: crypto.randomUUID(),
      type: 'agent',
      content,
      timestamp: new Date(),
      language: currentLanguage,
      actions
    };
    
    setChatMessages(prev => [...prev, message]);
    
    // Speak the message if voice is enabled
    VoiceResponseService.speak({
      text: content,
      interrupt: false
    });
  };

  const addUserMessage = (content: string) => {
    const message: ChatMessage = {
      id: crypto.randomUUID(),
      type: 'user',
      content,
      timestamp: new Date(),
      language: currentLanguage
    };
    
    setChatMessages(prev => [...prev, message]);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isProcessing) return;

    const userMessage = inputMessage.trim();
    setInputMessage('');
    addUserMessage(userMessage);
    setIsProcessing(true);

    try {
      // Process the message and generate response
      const response = await processUserMessage(userMessage);
      addAgentMessage(response.text, response.actions);
    } catch (error) {
      console.error('Error processing message:', error);
      addAgentMessage(getLocalizedText('error_processing_message'));
    } finally {
      setIsProcessing(false);
    }
  };

  const processUserMessage = async (message: string): Promise<{ text: string; actions?: any[] }> => {
    const lowerMessage = message.toLowerCase();
    
    // Project analysis requests
    if (lowerMessage.includes('analyze') || lowerMessage.includes('health') || lowerMessage.includes('status')) {
      if (!projectId) {
        return { text: getLocalizedText('no_project_selected') };
      }
      
      const metrics = projectMetrics || await EnhancedProjectManagerAgent.analyzeProjectHealth(projectId);
      return {
        text: getLocalizedText('project_analysis_result', {
          healthScore: metrics.healthScore,
          riskScore: metrics.riskScore,
          completionRate: Math.round((metrics.completedTasks / metrics.totalTasks) * 100)
        }),
        actions: [
          {
            type: 'view_insights',
            label: getLocalizedText('view_insights'),
            action: () => setActiveTab('insights')
          },
          {
            type: 'view_recommendations',
            label: getLocalizedText('view_recommendations'),
            action: () => setActiveTab('recommendations')
          }
        ]
      };
    }
    
    // Default response
    return { text: getLocalizedText('default_response') };
  };

  const changeLanguage = async (languageCode: string) => {
    try {
      await LanguageSelectionService.setLanguage(languageCode);
      setCurrentLanguage(languageCode);
      addAgentMessage(getLocalizedText('language_changed'));
    } catch (error) {
      console.error('Error changing language:', error);
      addAgentMessage(getLocalizedText('error_changing_language'));
    }
  };

  const startVoiceInput = async () => {
    try {
      setIsListening(true);
      await VoiceRecognitionService.startListening({
        onResult: (result) => {
          if (result.isFinal) {
            setInputMessage(result.transcript);
            setIsListening(false);
          }
        },
        onEnd: () => setIsListening(false),
        onError: (error) => {
          console.error('Voice recognition error:', error);
          setIsListening(false);
        }
      });
    } catch (error) {
      console.error('Error starting voice input:', error);
      setIsListening(false);
    }
  };

  const stopVoiceInput = () => {
    VoiceRecognitionService.stopListening();
    setIsListening(false);
  };

  const getLocalizedText = (key: string, params?: Record<string, any>): string => {
    const texts: Record<string, Record<string, string>> = {
      'welcome_message': {
        'en-GB': 'Hello! I\'m your AI Project Manager Assistant. I can help you analyze projects, identify risks, and provide recommendations. How can I assist you today?',
        'ig-NG': 'Nnọọ! Abụ m onye inyeaka AI Project Manager gị. Enwere m ike inyere gị aka nyochaa ọrụ, chọpụta ihe ize ndụ, ma nye ndụmọdụ. Kedu ka m ga-esi nyere gị aka taa?',
        'ha-NG': 'Sannu! Ni ne mai taimako AI Project Manager naku. Zan iya taimaka muku nazarin ayyuka, gano hadari, da ba da shawarwari. Ta yaya zan iya taimaka muku yau?',
        'yo-NG': 'Bawo! Emi ni oluranlowo AI Project Manager yin. Mo le ran yin lowo lati se ayewo awon ise, mo awon ewu, ati pese awon imoran. Bawo ni mo se le ran yin lowo loni?'
      },
      'project_analysis_complete': {
        'en-GB': `Project analysis complete! Health Score: ${params?.healthScore}/100. I found ${params?.insightsCount} insights and ${params?.recommendationsCount} recommendations for improvement.`,
        'ig-NG': `Nyocha ọrụ zuru oke! Akara Ahụike: ${params?.healthScore}/100. Achọtara m nghọta ${params?.insightsCount} na ndụmọdụ ${params?.recommendationsCount} maka nkwalite.`,
        'ha-NG': `Nazarin aikin ya kammala! Maki Lafiya: ${params?.healthScore}/100. Na sami fahimta ${params?.insightsCount} da shawarwari ${params?.recommendationsCount} don ingantawa.`,
        'yo-NG': `Ayewo ise ti pari! Ami Ilera: ${params?.healthScore}/100. Mo ri awon oye ${params?.insightsCount} ati awon imoran ${params?.recommendationsCount} fun ilọsiwaju.`
      },
      'default_response': {
        'en-GB': 'I understand. I can help you with project analysis, risk assessment, team management, and optimization recommendations. What would you like to explore?',
        'ig-NG': 'Aghọtara m. Enwere m ike inyere gị aka na nyocha ọrụ, nyocha ihe ize ndụ, njikwa otu, na ndụmọdụ nkwalite. Kedu ihe ị chọrọ inyocha?',
        'ha-NG': 'Na gane. Zan iya taimaka muku da nazarin aiki, kimanta hadari, sarrafa kungiya, da shawarwarin ingantawa. Me kuke son ku bincika?',
        'yo-NG': 'Mo ye mi. Mo le ran yin lowo pelu ayewo ise, ayewo ewu, isakoso egbe, ati awon imoran ilọsiwaju. Kini e fe wo?'
      }
    };
    
    return texts[key]?.[currentLanguage] || texts[key]?.['en-GB'] || key;
  };

  if (compact) {
    return (
      <div className={`project-manager-agent-compact ${className}`}>
        <div className="flex items-center gap-2 p-3 bg-white border border-gray-200 rounded-lg shadow-sm">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Brain size={20} className="text-blue-600" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-sm">AI Project Manager</div>
            <div className="text-xs text-gray-600">
              {projectMetrics ? `Health: ${projectMetrics.healthScore}/100` : 'Ready to assist'}
            </div>
          </div>
          <button
            onClick={() => setActiveTab('chat')}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Open AI Assistant"
          >
            <MessageCircle size={16} />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`project-manager-agent-interface ${className}`}>
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <Brain size={24} />
              </div>
              <div>
                <h3 className="font-semibold text-lg">AI Project Manager</h3>
                <p className="text-sm opacity-90">
                  {getLocalizedText('agent_subtitle')}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <div className="text-xs bg-white/20 px-2 py-1 rounded">
                {LanguageSelectionService.getLanguageDisplayName(currentLanguage)}
              </div>
              <button
                onClick={() => {/* Settings */}}
                className="p-2 bg-white/20 rounded-lg hover:bg-white/30 transition-colors"
              >
                <Settings size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex">
            {[
              { id: 'chat', label: 'Chat', icon: MessageCircle },
              { id: 'insights', label: 'Insights', icon: Lightbulb },
              { id: 'metrics', label: 'Metrics', icon: BarChart3 },
              { id: 'recommendations', label: 'Recommendations', icon: Target }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                <tab.icon size={16} />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content Area */}
        <div className="h-96 overflow-hidden">
          {activeTab === 'chat' && (
            <div className="h-full flex flex-col">
              {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3">
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg text-sm ${
                        message.type === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      <p>{message.content}</p>
                      {message.actions && (
                        <div className="mt-2 space-y-1">
                          {message.actions.map((action, index) => (
                            <button
                              key={index}
                              onClick={action.action}
                              className="block w-full text-left px-2 py-1 text-xs bg-white/20 rounded hover:bg-white/30 transition-colors"
                            >
                              {action.label}
                            </button>
                          ))}
                        </div>
                      )}
                      <p className="text-xs opacity-75 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
                
                {isProcessing && (
                  <div className="flex justify-start">
                    <div className="bg-gray-100 text-gray-800 px-3 py-2 rounded-lg text-sm">
                      <div className="flex items-center gap-2">
                        <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                        Processing...
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Chat Input */}
              <div className="border-t border-gray-200 p-4">
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder={getLocalizedText('type_message')}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={isProcessing}
                  />
                  
                  <button
                    onClick={isListening ? stopVoiceInput : startVoiceInput}
                    className={`p-2 rounded-lg transition-colors ${
                      isListening 
                        ? 'bg-red-500 text-white animate-pulse' 
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                    title={isListening ? 'Stop listening' : 'Start voice input'}
                  >
                    {isListening ? <MicOff size={16} /> : <Mic size={16} />}
                  </button>
                  
                  <button
                    onClick={handleSendMessage}
                    disabled={!inputMessage.trim() || isProcessing}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Send
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'insights' && (
            <div className="p-4 space-y-3 overflow-y-auto h-full">
              <h4 className="font-semibold text-gray-800">Project Insights</h4>
              {insights.length > 0 ? (
                insights.map((insight) => (
                  <div
                    key={insight.id}
                    className={`p-3 rounded-lg border-l-4 ${
                      insight.severity === 'critical' ? 'border-red-500 bg-red-50' :
                      insight.severity === 'high' ? 'border-orange-500 bg-orange-50' :
                      insight.severity === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                      'border-blue-500 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      <AlertTriangle 
                        size={16} 
                        className={
                          insight.severity === 'critical' ? 'text-red-500' :
                          insight.severity === 'high' ? 'text-orange-500' :
                          insight.severity === 'medium' ? 'text-yellow-500' :
                          'text-blue-500'
                        }
                      />
                      <div className="flex-1">
                        <h5 className="font-medium text-sm">{insight.title}</h5>
                        <p className="text-xs text-gray-600 mt-1">{insight.description}</p>
                        <p className="text-xs font-medium mt-2">Recommendation:</p>
                        <p className="text-xs text-gray-600">{insight.recommendation}</p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-8">No insights available. Analyze a project to generate insights.</p>
              )}
            </div>
          )}

          {activeTab === 'metrics' && (
            <div className="p-4 space-y-4 overflow-y-auto h-full">
              <h4 className="font-semibold text-gray-800">Project Metrics</h4>
              {projectMetrics ? (
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Shield className="text-blue-500" size={16} />
                      <span className="text-sm font-medium">Health Score</span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600">{projectMetrics.healthScore}/100</div>
                  </div>
                  
                  <div className="bg-red-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="text-red-500" size={16} />
                      <span className="text-sm font-medium">Risk Score</span>
                    </div>
                    <div className="text-2xl font-bold text-red-600">{projectMetrics.riskScore}/10</div>
                  </div>
                  
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="text-green-500" size={16} />
                      <span className="text-sm font-medium">Completed</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600">{projectMetrics.completedTasks}/{projectMetrics.totalTasks}</div>
                  </div>
                  
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Clock className="text-orange-500" size={16} />
                      <span className="text-sm font-medium">Overdue</span>
                    </div>
                    <div className="text-2xl font-bold text-orange-600">{projectMetrics.overdueTasks}</div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">No metrics available. Select a project to view metrics.</p>
              )}
            </div>
          )}

          {activeTab === 'recommendations' && (
            <div className="p-4 space-y-3 overflow-y-auto h-full">
              <h4 className="font-semibold text-gray-800">Recommendations</h4>
              {recommendations.length > 0 ? (
                recommendations.map((recommendation) => (
                  <div
                    key={recommendation.id}
                    className={`p-3 rounded-lg border-l-4 ${
                      recommendation.priority === 'urgent' ? 'border-red-500 bg-red-50' :
                      recommendation.priority === 'high' ? 'border-orange-500 bg-orange-50' :
                      recommendation.priority === 'medium' ? 'border-yellow-500 bg-yellow-50' :
                      'border-green-500 bg-green-50'
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      <Target 
                        size={16} 
                        className={
                          recommendation.priority === 'urgent' ? 'text-red-500' :
                          recommendation.priority === 'high' ? 'text-orange-500' :
                          recommendation.priority === 'medium' ? 'text-yellow-500' :
                          'text-green-500'
                        }
                      />
                      <div className="flex-1">
                        <h5 className="font-medium text-sm">{recommendation.title}</h5>
                        <p className="text-xs text-gray-600 mt-1">{recommendation.description}</p>
                        <div className="mt-2">
                          <p className="text-xs font-medium">Benefits:</p>
                          <ul className="text-xs text-gray-600 list-disc list-inside">
                            {recommendation.benefits.slice(0, 2).map((benefit, index) => (
                              <li key={index}>{benefit}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-8">No recommendations available. Analyze a project to generate recommendations.</p>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectManagerAgentInterface;
