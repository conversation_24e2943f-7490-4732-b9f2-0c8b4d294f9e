import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Resend with API key
const resendApiKey = 're_iEWVBukX_AM4BhzUNeyxxVTLdHNrLLGqs';
const resend = new Resend(resendApiKey);

async function testEmailNotifications() {
  console.log('📧 Testing Email Notifications...\n');

  try {
    // Test 1: Send a test email using Resend
    console.log('⏳ Sending test email via Resend API...');
    
    const emailResult = await resend.emails.send({
      from: 'AI Workboard <<EMAIL>>', // Use verified domain
      to: ['<EMAIL>'],
      subject: '🧪 Test Email - AI Workboard Notification System',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc2626;">🚀 AI Workboard Test Email</h2>
          <p>This is a test email to verify that the notification system is working correctly.</p>
          
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>✅ System Status</h3>
            <ul>
              <li>✅ Database: Fully synchronized (27/27 tables)</li>
              <li>✅ Email API: Connected via Resend</li>
              <li>✅ Notifications: Active</li>
              <li>✅ Project Management: Operational</li>
            </ul>
          </div>
          
          <p style="color: #6b7280;">
            Sent at: ${new Date().toLocaleString()}<br>
            From: AI Workboard Notification System<br>
            Test ID: ${Math.random().toString(36).substr(2, 9)}
          </p>
        </div>
      `,
    });

    if (emailResult.error) {
      console.log(`❌ Email sending failed: ${emailResult.error.message}`);
      return false;
    } else {
      console.log(`✅ Email sent successfully! ID: ${emailResult.data?.id}`);
    }

    // Test 2: Log email notification in database
    console.log('\n⏳ Logging email notification in database...');
    
    const { data: emailLog, error: emailLogError } = await supabase
      .from('email_notifications')
      .insert({
        type: 'test_notification',
        recipients: ['<EMAIL>'],
        subject: '🧪 Test Email - AI Workboard Notification System',
        body: 'Test email notification sent via Resend API',
        status: 'sent',
        sent_at: new Date().toISOString()
      })
      .select();

    if (emailLogError) {
      console.log(`❌ Failed to log email: ${emailLogError.message}`);
    } else {
      console.log(`✅ Email notification logged in database`);
    }

    return true;

  } catch (error: any) {
    console.log(`💥 Email test failed: ${error.message}`);
    return false;
  }
}

async function testInAppNotifications() {
  console.log('\n🔔 Testing In-App Notifications...\n');

  try {
    // Test 1: Get a test user
    console.log('⏳ Getting test user...');
    
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profileError || !profiles || profiles.length === 0) {
      console.log('❌ No profiles found, creating test notifications without user');
      return false;
    }

    const testUser = profiles[0];
    console.log(`✅ Using test user: ${testUser.full_name || testUser.email || testUser.id}`);

    // Test 2: Create in-app notifications
    console.log('\n⏳ Creating test in-app notifications...');

    const testNotifications = [
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '🎉 Welcome to AI Workboard',
        message: 'Your notification system is working perfectly!',
        type: 'info',
        read: false
      },
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '📊 Project Management Ready',
        message: 'All 27 database tables are synchronized and ready.',
        type: 'success',
        read: false
      },
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '⏰ Time Tracking Active',
        message: 'Time tracking system is operational.',
        type: 'info',
        read: false
      },
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '🔋 Battery System Online',
        message: 'Battery management system is ready.',
        type: 'success',
        read: false
      }
    ];

    const { data: notifications, error: notificationError } = await supabase
      .from('notifications')
      .insert(testNotifications)
      .select();

    if (notificationError) {
      console.log(`❌ Failed to create notifications: ${notificationError.message}`);
      return false;
    } else {
      console.log(`✅ Created ${notifications?.length || 0} test notifications`);
    }

    // Test 3: Retrieve notifications
    console.log('\n⏳ Retrieving notifications for user...');
    
    const { data: userNotifications, error: retrieveError } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', testUser.id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (retrieveError) {
      console.log(`❌ Failed to retrieve notifications: ${retrieveError.message}`);
    } else {
      console.log(`✅ Retrieved ${userNotifications?.length || 0} notifications`);
      
      if (userNotifications && userNotifications.length > 0) {
        console.log('\n📋 Recent Notifications:');
        userNotifications.slice(0, 4).forEach((notif, index) => {
          const readStatus = notif.read ? '✅' : '🔔';
          console.log(`   ${readStatus} ${notif.title}`);
          console.log(`      ${notif.message}`);
          console.log(`      Type: ${notif.type} | Created: ${new Date(notif.created_at).toLocaleString()}`);
        });
      }
    }

    // Test 4: Log activity
    console.log('\n⏳ Testing activity logging...');
    
    const { data: activity, error: activityError } = await supabase
      .from('recent_activity_logs')
      .insert({
        user_id: testUser.id,
        action: 'notification_test',
        description: 'Tested notification system functionality',
        entity_type: 'system',
        metadata: {
          test_type: 'notification_system',
          timestamp: new Date().toISOString(),
          notifications_created: notifications?.length || 0,
          email_sent: true
        }
      })
      .select();

    if (activityError) {
      console.log(`❌ Failed to log activity: ${activityError.message}`);
    } else {
      console.log(`✅ Activity logged successfully`);
    }

    return true;

  } catch (error: any) {
    console.log(`💥 In-app notification test failed: ${error.message}`);
    return false;
  }
}

async function testNotificationStats() {
  console.log('\n📊 Testing Notification Statistics...\n');

  try {
    // Get notification counts
    const { data: allNotifications, error: notifError } = await supabase
      .from('notifications')
      .select('type, read');

    if (notifError) {
      console.log(`❌ Failed to get notifications: ${notifError.message}`);
    } else {
      const stats = {
        total: allNotifications?.length || 0,
        unread: allNotifications?.filter(n => !n.read).length || 0,
        byType: allNotifications?.reduce((acc: any, n) => {
          acc[n.type] = (acc[n.type] || 0) + 1;
          return acc;
        }, {}) || {}
      };

      console.log('📈 In-App Notification Statistics:');
      console.log(`   📧 Total notifications: ${stats.total}`);
      console.log(`   🔔 Unread notifications: ${stats.unread}`);
      console.log(`   📊 By type:`, stats.byType);
    }

    // Get email notification stats
    const { data: emailNotifications, error: emailError } = await supabase
      .from('email_notifications')
      .select('status');

    if (emailError) {
      console.log(`❌ Failed to get email stats: ${emailError.message}`);
    } else {
      const emailStats = emailNotifications?.reduce((acc: any, n) => {
        acc[n.status] = (acc[n.status] || 0) + 1;
        return acc;
      }, {}) || {};

      console.log('\n📧 Email Notification Statistics:');
      console.log(`   📊 By status:`, emailStats);
    }

    // Get activity logs
    const { data: activities, error: activityError } = await supabase
      .from('recent_activity_logs')
      .select('action')
      .limit(10);

    if (activityError) {
      console.log(`❌ Failed to get activity logs: ${activityError.message}`);
    } else {
      console.log(`\n📝 Recent Activity Logs: ${activities?.length || 0} entries`);
    }

    return true;

  } catch (error: any) {
    console.log(`💥 Stats test failed: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🧪 AI Workboard Notification System Test\n');
  console.log('=' .repeat(60));
  
  const emailSuccess = await testEmailNotifications();
  const inAppSuccess = await testInAppNotifications();
  const statsSuccess = await testNotificationStats();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Notification Testing Results:');
  console.log(`   📧 Email Notifications: ${emailSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   🔔 In-App Notifications: ${inAppSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   📊 Statistics: ${statsSuccess ? '✅ PASSED' : '❌ FAILED'}`);
  
  if (emailSuccess && inAppSuccess && statsSuccess) {
    console.log('\n🎉 ALL TESTS PASSED! Notification system is fully operational.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.');
  }
  
  console.log('\n💡 Check your email (<EMAIL>) for the test notification!');
}

main().catch((error) => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
