import React, { useState } from 'react';
import { Bell, Clock, MapPin, User, CheckCircle, AlertTriangle, Info, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useNotifications } from '@/hooks/useNotifications';
import { TimeNotification } from '@/services/notificationService';
import { formatDistanceToNow } from 'date-fns';

interface NotificationCenterProps {
  className?: string;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ className }) => {
  const { 
    notifications, 
    unreadCount, 
    isLoading, 
    markAsRead, 
    markAllAsRead,
    refreshNotifications 
  } = useNotifications();
  const [isOpen, setIsOpen] = useState(false);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'clock_in':
      case 'clock_out':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'overtime_alert':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'late_arrival':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'break_start':
      case 'break_end':
        return <Info className="h-4 w-4 text-green-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'clock_in':
        return 'bg-green-50 border-green-200';
      case 'clock_out':
        return 'bg-blue-50 border-blue-200';
      case 'overtime_alert':
        return 'bg-orange-50 border-orange-200';
      case 'late_arrival':
        return 'bg-red-50 border-red-200';
      case 'break_start':
      case 'break_end':
        return 'bg-purple-50 border-purple-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const handleNotificationClick = (notification: TimeNotification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
  };

  const formatNotificationTime = (createdAt: string) => {
    try {
      return formatDistanceToNow(new Date(createdAt), { addSuffix: true });
    } catch (error) {
      return 'Recently';
    }
  };

  const renderNotificationData = (data: any) => {
    if (!data || typeof data !== 'object') return null;

    return (
      <div className="mt-2 space-y-1 text-xs text-gray-600">
        {data.location && (
          <div className="flex items-center gap-1">
            <MapPin className="h-3 w-3" />
            <span>{data.location}</span>
          </div>
        )}
        {data.time && (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>{new Date(data.time).toLocaleTimeString()}</span>
          </div>
        )}
        {data.duration && (
          <div className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            <span>Duration: {data.duration}</span>
          </div>
        )}
        {data.device_type && (
          <div className="flex items-center gap-1">
            <User className="h-3 w-3" />
            <span>Device: {data.device_type}</span>
          </div>
        )}
        {data.overtime_hours && (
          <div className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            <span>Overtime: {data.overtime_hours} hours</span>
          </div>
        )}
      </div>
    );
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={`relative ${className}`}
          onClick={() => setIsOpen(!isOpen)}
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-96 p-0" align="end">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold">Notifications</CardTitle>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={markAllAsRead}
                    className="text-xs"
                  >
                    Mark all read
                  </Button>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={refreshNotifications}
                  className="text-xs"
                >
                  Refresh
                </Button>
              </div>
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-gray-600">
                You have {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </p>
            )}
          </CardHeader>
          
          <CardContent className="p-0">
            <ScrollArea className="h-96">
              {isLoading ? (
                <div className="p-4 text-center text-gray-500">
                  Loading notifications...
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No notifications yet</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-3 border-l-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                        getNotificationColor(notification.type)
                      } ${!notification.read ? 'bg-opacity-100' : 'bg-opacity-50'}`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          {getNotificationIcon(notification.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className={`text-sm font-medium ${
                              !notification.read ? 'text-gray-900' : 'text-gray-700'
                            }`}>
                              {notification.title}
                            </h4>
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                            )}
                          </div>
                          
                          <p className={`text-sm mt-1 ${
                            !notification.read ? 'text-gray-800' : 'text-gray-600'
                          }`}>
                            {notification.message}
                          </p>
                          
                          {renderNotificationData(notification.data)}
                          
                          <p className="text-xs text-gray-500 mt-2">
                            {formatNotificationTime(notification.created_at)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
};

// Compact notification bell for smaller spaces
export const NotificationBell: React.FC<{ className?: string }> = ({ className }) => {
  const { unreadCount } = useNotifications();

  return (
    <div className={`relative ${className}`}>
      <Bell className="h-5 w-5 text-gray-600" />
      {unreadCount > 0 && (
        <Badge 
          variant="destructive" 
          className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0 flex items-center justify-center text-xs"
        >
          {unreadCount > 9 ? '9+' : unreadCount}
        </Badge>
      )}
    </div>
  );
};

// Toast notification component for real-time updates
export const NotificationToast: React.FC<{
  notification: TimeNotification;
  onClose: () => void;
}> = ({ notification, onClose }) => {
  return (
    <Card className="w-80 shadow-lg border-l-4 border-blue-500">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-0.5">
              {getNotificationIcon(notification.type)}
            </div>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-gray-900">
                {notification.title}
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                {notification.message}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-6 w-6 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper function for external use
const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'clock_in':
    case 'clock_out':
      return <Clock className="h-4 w-4 text-blue-500" />;
    case 'overtime_alert':
      return <AlertTriangle className="h-4 w-4 text-orange-500" />;
    case 'late_arrival':
      return <AlertTriangle className="h-4 w-4 text-red-500" />;
    case 'break_start':
    case 'break_end':
      return <Info className="h-4 w-4 text-green-500" />;
    default:
      return <Bell className="h-4 w-4 text-gray-500" />;
  }
};
