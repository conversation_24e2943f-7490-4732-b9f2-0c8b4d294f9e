<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Voice Command System Test - CTN Nigeria</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2d3748;
            margin: 0;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .voice-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .voice-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }
        .voice-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .voice-card.recognition {
            border-left: 4px solid #10b981;
        }
        .voice-card.agent {
            border-left: 4px solid #8b5cf6;
        }
        .voice-card.response {
            border-left: 4px solid #f59e0b;
        }
        .voice-card.navigation {
            border-left: 4px solid #3b82f6;
        }
        .voice-card h3 {
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #1f2937;
        }
        .button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .button.listening {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            animation: pulse 2s infinite;
        }
        .button.speaking {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            animation: pulse 2s infinite;
        }
        .button.success {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .button.secondary {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fcd34d;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .transcript-display {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            min-height: 100px;
            white-space: pre-wrap;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            overflow-y: auto;
            max-height: 300px;
        }
        .conversation-history {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
        .message.user {
            background: #dbeafe;
            border-left: 4px solid #3b82f6;
        }
        .message.agent {
            background: #f3e8ff;
            border-left: 4px solid #8b5cf6;
        }
        .waveform {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
            height: 40px;
            margin: 10px 0;
        }
        .waveform-bar {
            width: 4px;
            background: #667eea;
            border-radius: 2px;
            transition: height 0.1s ease;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature-item h4 {
            margin: 0 0 8px 0;
            color: #1f2937;
        }
        .feature-item p {
            margin: 0;
            color: #6b7280;
            font-size: 14px;
        }
        .command-examples {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .command-examples h4 {
            margin: 0 0 15px 0;
            color: #0c4a6e;
        }
        .command-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .command-item {
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #bae6fd;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .command-item:hover {
            background: #0ea5e9;
            color: white;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-top: 5px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 AI Voice Command System</h1>
            <p>Advanced voice recognition, AI agent, and text-to-speech testing interface</p>
        </div>

        <div id="status" class="status info">
            <strong>Status:</strong> Initializing voice command system...
        </div>

        <!-- Voice Controls Grid -->
        <div class="voice-controls">
            <!-- Voice Recognition -->
            <div class="voice-card recognition">
                <h3>🎤 Voice Recognition</h3>
                <p>Speech-to-text with real-time processing</p>
                <div class="waveform" id="waveform" style="display: none;">
                    <!-- Waveform bars will be generated here -->
                </div>
                <button id="startListening" class="button">
                    🎤 Start Listening
                </button>
                <button id="stopListening" class="button secondary" disabled>
                    🛑 Stop Listening
                </button>
                <button id="testMicrophone" class="button secondary">
                    🔧 Test Microphone
                </button>
            </div>

            <!-- AI Voice Agent -->
            <div class="voice-card agent">
                <h3>🤖 AI Voice Agent</h3>
                <p>Intelligent conversation and assistance</p>
                <button id="testAgent" class="button">
                    💬 Test Agent
                </button>
                <button id="showCapabilities" class="button secondary">
                    🎯 Show Capabilities
                </button>
                <button id="askQuestion" class="button secondary">
                    ❓ Ask Question
                </button>
            </div>

            <!-- Voice Response -->
            <div class="voice-card response">
                <h3>🔊 Voice Response</h3>
                <p>Text-to-speech with natural voices</p>
                <button id="testSpeech" class="button">
                    🔊 Test Speech
                </button>
                <button id="stopSpeaking" class="button secondary" disabled>
                    ⏹️ Stop Speaking
                </button>
                <button id="voiceSettings" class="button secondary">
                    ⚙️ Voice Settings
                </button>
            </div>

            <!-- Navigation -->
            <div class="voice-card navigation">
                <h3>🧭 Voice Navigation</h3>
                <p>Navigate through voice commands</p>
                <button id="testNavigation" class="button">
                    🧭 Test Navigation
                </button>
                <button id="showRoutes" class="button secondary">
                    📍 Show Routes
                </button>
                <button id="guidedTour" class="button secondary">
                    🎯 Guided Tour
                </button>
            </div>
        </div>

        <!-- Current Transcript -->
        <div>
            <h3>📝 Live Transcript</h3>
            <div id="transcript" class="transcript-display">
                Transcript will appear here when you start speaking...
            </div>
        </div>

        <!-- Conversation History -->
        <div>
            <h3>💬 Conversation History</h3>
            <div id="conversation" class="conversation-history">
                <p style="text-align: center; color: #6b7280;">
                    No conversation yet. Try saying "Hello" or "Help me navigate"
                </p>
            </div>
        </div>

        <!-- Voice Command Examples -->
        <div class="command-examples">
            <h4>🎯 Try These Voice Commands</h4>
            <div class="command-list">
                <div class="command-item" onclick="simulateCommand('Go to dashboard')">
                    "Go to dashboard"
                </div>
                <div class="command-item" onclick="simulateCommand('Show my tasks')">
                    "Show my tasks"
                </div>
                <div class="command-item" onclick="simulateCommand('Create a new project')">
                    "Create a new project"
                </div>
                <div class="command-item" onclick="simulateCommand('Help me navigate')">
                    "Help me navigate"
                </div>
                <div class="command-item" onclick="simulateCommand('What can you do')">
                    "What can you do?"
                </div>
                <div class="command-item" onclick="simulateCommand('Show team members')">
                    "Show team members"
                </div>
                <div class="command-item" onclick="simulateCommand('Open reports')">
                    "Open reports"
                </div>
                <div class="command-item" onclick="simulateCommand('Start time tracking')">
                    "Start time tracking"
                </div>
            </div>
        </div>

        <!-- System Features -->
        <div>
            <h3>🚀 Voice System Features</h3>
            <div class="feature-grid">
                <div class="feature-item">
                    <h4>🎤 Advanced Speech Recognition</h4>
                    <p>Real-time speech-to-text with high accuracy and multiple language support</p>
                </div>
                <div class="feature-item">
                    <h4>🤖 Intelligent AI Agent</h4>
                    <p>Context-aware AI assistant with full system knowledge and navigation capabilities</p>
                </div>
                <div class="feature-item">
                    <h4>🔊 Natural Voice Responses</h4>
                    <p>Text-to-speech with multiple voices, adjustable speed, and natural intonation</p>
                </div>
                <div class="feature-item">
                    <h4>🧭 Voice Navigation</h4>
                    <p>Complete system navigation through voice commands with guided assistance</p>
                </div>
                <div class="feature-item">
                    <h4>💬 Conversation Memory</h4>
                    <p>Persistent conversation history with context awareness across sessions</p>
                </div>
                <div class="feature-item">
                    <h4>⚙️ Customizable Settings</h4>
                    <p>Personalized voice preferences, wake words, and accessibility features</p>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div>
            <h3>📊 Voice System Statistics</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value" id="totalCommands">0</div>
                    <div class="stat-label">Total Commands</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="avgConfidence">0%</div>
                    <div class="stat-label">Avg Confidence</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="totalSessions">0</div>
                    <div class="stat-label">Voice Sessions</div>
                </div>
            </div>
        </div>

        <!-- Test All Button -->
        <div style="text-align: center; margin: 30px 0;">
            <button id="testAllFeatures" class="button" style="font-size: 18px; padding: 15px 30px;">
                🧪 Test All Voice Features
            </button>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        // Voice system state
        let isListening = false;
        let isSpeaking = false;
        let recognition = null;
        let synthesis = null;
        let currentSession = null;
        let conversationHistory = [];
        let stats = {
            totalCommands: 0,
            successfulCommands: 0,
            totalSessions: 0,
            avgConfidence: 0
        };

        // Initialize voice system
        async function initializeVoiceSystem() {
            try {
                showStatus('Initializing voice command system...', 'info');
                
                // Check browser support
                if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                    throw new Error('Speech recognition not supported in this browser');
                }
                
                if (!('speechSynthesis' in window)) {
                    throw new Error('Speech synthesis not supported in this browser');
                }

                // Initialize speech recognition
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.continuous = true;
                recognition.interimResults = true;
                recognition.lang = 'en-US';

                // Initialize speech synthesis
                synthesis = window.speechSynthesis;

                // Set up event listeners
                setupEventListeners();

                // Load voice statistics
                await loadVoiceStats();

                showStatus('Voice command system ready! Try saying "Hello" or click Start Listening', 'success');
                
            } catch (error) {
                console.error('Voice initialization error:', error);
                showStatus(`Initialization failed: ${error.message}`, 'error');
            }
        }

        function setupEventListeners() {
            // Speech recognition events
            recognition.onstart = () => {
                isListening = true;
                updateListeningUI(true);
                showWaveform(true);
                log('🎤 Voice recognition started');
            };

            recognition.onend = () => {
                isListening = false;
                updateListeningUI(false);
                showWaveform(false);
                log('🎤 Voice recognition ended');
            };

            recognition.onresult = (event) => {
                handleSpeechResult(event);
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                showStatus(`Speech recognition error: ${event.error}`, 'error');
                isListening = false;
                updateListeningUI(false);
            };

            // Button event listeners
            document.getElementById('startListening').onclick = startListening;
            document.getElementById('stopListening').onclick = stopListening;
            document.getElementById('testMicrophone').onclick = testMicrophone;
            document.getElementById('testAgent').onclick = testAgent;
            document.getElementById('showCapabilities').onclick = showCapabilities;
            document.getElementById('askQuestion').onclick = askQuestion;
            document.getElementById('testSpeech').onclick = testSpeech;
            document.getElementById('stopSpeaking').onclick = stopSpeaking;
            document.getElementById('voiceSettings').onclick = showVoiceSettings;
            document.getElementById('testNavigation').onclick = testNavigation;
            document.getElementById('showRoutes').onclick = showRoutes;
            document.getElementById('guidedTour').onclick = guidedTour;
            document.getElementById('testAllFeatures').onclick = testAllFeatures;
        }

        async function startListening() {
            try {
                if (!recognition) {
                    throw new Error('Speech recognition not initialized');
                }

                // Request microphone permission
                await navigator.mediaDevices.getUserMedia({ audio: true });
                
                recognition.start();
                showStatus('Listening... Speak now!', 'info');
                
            } catch (error) {
                console.error('Error starting speech recognition:', error);
                showStatus(`Failed to start listening: ${error.message}`, 'error');
            }
        }

        function stopListening() {
            if (recognition && isListening) {
                recognition.stop();
                showStatus('Stopped listening', 'info');
            }
        }

        function handleSpeechResult(event) {
            let transcript = '';
            let isFinal = false;

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const result = event.results[i];
                transcript += result[0].transcript;
                if (result.isFinal) {
                    isFinal = true;
                }
            }

            // Update transcript display
            updateTranscript(transcript, isFinal);

            if (isFinal) {
                processVoiceCommand(transcript, event.results[event.results.length - 1][0].confidence);
            }
        }

        async function processVoiceCommand(command, confidence) {
            try {
                log(`🎯 Processing command: "${command}" (confidence: ${(confidence * 100).toFixed(1)}%)`);
                
                // Add to conversation history
                addToConversation('user', command, confidence);
                
                // Simulate AI processing
                const response = await simulateAIResponse(command);
                
                // Add AI response to conversation
                addToConversation('agent', response.text, response.confidence);
                
                // Speak the response
                if (response.text) {
                    await speakText(response.text);
                }
                
                // Update statistics
                updateStats(command, confidence, true);
                
                // Execute any actions
                if (response.actions) {
                    executeActions(response.actions);
                }
                
            } catch (error) {
                console.error('Error processing voice command:', error);
                const errorMsg = "I'm sorry, I couldn't process that command. Please try again.";
                addToConversation('agent', errorMsg, 0.5);
                await speakText(errorMsg);
                updateStats(command, confidence, false);
            }
        }

        async function simulateAIResponse(command) {
            const lowerCommand = command.toLowerCase();
            
            // Navigation commands
            if (lowerCommand.includes('go to') || lowerCommand.includes('navigate') || lowerCommand.includes('open')) {
                if (lowerCommand.includes('dashboard')) {
                    return {
                        text: "Navigating to the dashboard. You'll see your project overview, recent activities, and key metrics there.",
                        confidence: 0.95,
                        actions: [{ type: 'navigate', target: '/' }]
                    };
                }
                if (lowerCommand.includes('project')) {
                    return {
                        text: "Opening the projects page where you can view, create, and manage all your projects.",
                        confidence: 0.95,
                        actions: [{ type: 'navigate', target: '/projects' }]
                    };
                }
                if (lowerCommand.includes('task')) {
                    return {
                        text: "Taking you to the tasks section where you can manage your assignments and track progress.",
                        confidence: 0.95,
                        actions: [{ type: 'navigate', target: '/tasks' }]
                    };
                }
            }
            
            // Help commands
            if (lowerCommand.includes('help') || lowerCommand.includes('what can you do')) {
                return {
                    text: "I'm your AI assistant for CTN Nigeria. I can help you navigate the system, create projects and tasks, manage your team, track time, generate reports, and much more. Just tell me what you need!",
                    confidence: 0.9,
                    actions: []
                };
            }
            
            // Creation commands
            if (lowerCommand.includes('create') || lowerCommand.includes('new')) {
                if (lowerCommand.includes('project')) {
                    return {
                        text: "I'll help you create a new project. Let me guide you through the process with all the necessary details.",
                        confidence: 0.9,
                        actions: [{ type: 'navigate', target: '/projects/new' }]
                    };
                }
                if (lowerCommand.includes('task')) {
                    return {
                        text: "Let's create a new task. I'll help you set the title, description, priority, and assignment.",
                        confidence: 0.9,
                        actions: [{ type: 'navigate', target: '/tasks/new' }]
                    };
                }
            }
            
            // Greeting
            if (lowerCommand.includes('hello') || lowerCommand.includes('hi')) {
                return {
                    text: "Hello! I'm your AI voice assistant for CTN Nigeria. I'm here to help you navigate the system and get things done efficiently. What can I help you with today?",
                    confidence: 0.95,
                    actions: []
                };
            }
            
            // Default response
            return {
                text: "I understand you said '" + command + "'. I can help you with navigation, creating projects and tasks, managing your team, and much more. What specific task would you like assistance with?",
                confidence: 0.7,
                actions: []
            };
        }

        async function speakText(text) {
            return new Promise((resolve) => {
                if (!synthesis) {
                    resolve();
                    return;
                }

                // Stop any current speech
                synthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 1.0;

                utterance.onstart = () => {
                    isSpeaking = true;
                    updateSpeakingUI(true);
                    log(`🔊 Speaking: "${text.substring(0, 50)}..."`);
                };

                utterance.onend = () => {
                    isSpeaking = false;
                    updateSpeakingUI(false);
                    log('🔊 Speech completed');
                    resolve();
                };

                utterance.onerror = (event) => {
                    console.error('Speech synthesis error:', event.error);
                    isSpeaking = false;
                    updateSpeakingUI(false);
                    resolve();
                };

                synthesis.speak(utterance);
            });
        }

        function executeActions(actions) {
            actions.forEach(action => {
                switch (action.type) {
                    case 'navigate':
                        log(`🧭 Navigation: ${action.target}`);
                        showStatus(`Would navigate to: ${action.target}`, 'info');
                        break;
                    case 'execute':
                        log(`⚡ Execute: ${action.description || action.target}`);
                        break;
                    default:
                        log(`🎯 Action: ${action.type} - ${action.target}`);
                }
            });
        }

        // UI Update Functions
        function updateListeningUI(listening) {
            const startBtn = document.getElementById('startListening');
            const stopBtn = document.getElementById('stopListening');
            
            if (listening) {
                startBtn.textContent = '🎤 Listening...';
                startBtn.classList.add('listening');
                startBtn.disabled = true;
                stopBtn.disabled = false;
            } else {
                startBtn.textContent = '🎤 Start Listening';
                startBtn.classList.remove('listening');
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }

        function updateSpeakingUI(speaking) {
            const stopBtn = document.getElementById('stopSpeaking');
            const testBtn = document.getElementById('testSpeech');
            
            if (speaking) {
                testBtn.classList.add('speaking');
                stopBtn.disabled = false;
            } else {
                testBtn.classList.remove('speaking');
                stopBtn.disabled = true;
            }
        }

        function updateTranscript(text, isFinal) {
            const transcriptDiv = document.getElementById('transcript');
            if (isFinal) {
                transcriptDiv.textContent += `[FINAL] ${text}\n`;
            } else {
                transcriptDiv.textContent = `[INTERIM] ${text}`;
            }
            transcriptDiv.scrollTop = transcriptDiv.scrollHeight;
        }

        function addToConversation(type, content, confidence) {
            const conversation = document.getElementById('conversation');
            const message = document.createElement('div');
            message.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            const confidenceText = confidence ? ` (${(confidence * 100).toFixed(1)}%)` : '';
            
            message.innerHTML = `
                <strong>${type === 'user' ? 'You' : 'AI Assistant'}:</strong> ${content}
                <div style="font-size: 12px; color: #6b7280; margin-top: 5px;">
                    ${timestamp}${confidenceText}
                </div>
            `;
            
            // Clear placeholder if this is the first message
            if (conversationHistory.length === 0) {
                conversation.innerHTML = '';
            }
            
            conversation.appendChild(message);
            conversation.scrollTop = conversation.scrollHeight;
            
            conversationHistory.push({ type, content, confidence, timestamp });
        }

        function showWaveform(show) {
            const waveform = document.getElementById('waveform');
            if (show) {
                waveform.style.display = 'flex';
                // Generate animated waveform bars
                waveform.innerHTML = '';
                for (let i = 0; i < 20; i++) {
                    const bar = document.createElement('div');
                    bar.className = 'waveform-bar';
                    bar.style.height = Math.random() * 30 + 5 + 'px';
                    waveform.appendChild(bar);
                }
                
                // Animate bars
                const animateBars = () => {
                    if (isListening) {
                        Array.from(waveform.children).forEach(bar => {
                            bar.style.height = Math.random() * 30 + 5 + 'px';
                        });
                        setTimeout(animateBars, 100);
                    }
                };
                animateBars();
            } else {
                waveform.style.display = 'none';
            }
        }

        function updateStats(command, confidence, success) {
            stats.totalCommands++;
            if (success) stats.successfulCommands++;
            
            // Update average confidence
            const totalConfidence = stats.avgConfidence * (stats.totalCommands - 1) + confidence;
            stats.avgConfidence = totalConfidence / stats.totalCommands;
            
            // Update UI
            document.getElementById('totalCommands').textContent = stats.totalCommands;
            document.getElementById('successRate').textContent = 
                ((stats.successfulCommands / stats.totalCommands) * 100).toFixed(1) + '%';
            document.getElementById('avgConfidence').textContent = 
                (stats.avgConfidence * 100).toFixed(1) + '%';
        }

        async function loadVoiceStats() {
            try {
                // Simulate loading stats from database
                stats.totalSessions = Math.floor(Math.random() * 50) + 10;
                document.getElementById('totalSessions').textContent = stats.totalSessions;
            } catch (error) {
                console.error('Error loading voice stats:', error);
            }
        }

        // Test Functions
        async function testMicrophone() {
            try {
                showStatus('Testing microphone access...', 'info');
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                showStatus('Microphone test successful!', 'success');
            } catch (error) {
                showStatus(`Microphone test failed: ${error.message}`, 'error');
            }
        }

        async function testAgent() {
            const testMessage = "Hello, this is a test of the AI voice agent";
            addToConversation('user', testMessage, 1.0);
            const response = await simulateAIResponse(testMessage);
            addToConversation('agent', response.text, response.confidence);
            await speakText(response.text);
        }

        function showCapabilities() {
            const capabilities = [
                "Navigate to any page in the system",
                "Create projects, tasks, and memos",
                "Search and filter information",
                "Provide help and tutorials",
                "Manage team and assignments",
                "Track time and generate reports",
                "Answer questions about the system",
                "Provide guided assistance"
            ];
            
            const capText = "I can help you with: " + capabilities.join(", ");
            addToConversation('agent', capText, 0.95);
            speakText(capText);
        }

        async function askQuestion() {
            const question = prompt("What would you like to ask the AI agent?");
            if (question) {
                addToConversation('user', question, 1.0);
                const response = await simulateAIResponse(question);
                addToConversation('agent', response.text, response.confidence);
                await speakText(response.text);
            }
        }

        async function testSpeech() {
            const testText = "This is a test of the voice response system. The text-to-speech functionality is working correctly with natural voice output.";
            await speakText(testText);
        }

        function stopSpeaking() {
            if (synthesis) {
                synthesis.cancel();
                isSpeaking = false;
                updateSpeakingUI(false);
                log('🔊 Speech stopped');
            }
        }

        function showVoiceSettings() {
            const voices = synthesis ? synthesis.getVoices() : [];
            const voiceList = voices.map(v => `${v.name} (${v.lang})`).join('\n');
            alert(`Available voices:\n\n${voiceList || 'No voices available'}`);
        }

        async function testNavigation() {
            const navCommands = [
                "Go to dashboard",
                "Open projects",
                "Show my tasks",
                "Navigate to team page"
            ];
            
            for (const command of navCommands) {
                log(`🧭 Testing: ${command}`);
                const response = await simulateAIResponse(command);
                log(`✅ Response: ${response.text}`);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            showStatus('Navigation test completed!', 'success');
        }

        function showRoutes() {
            const routes = [
                "/ - Dashboard",
                "/projects - Projects",
                "/tasks - Tasks",
                "/team - Team",
                "/reports - Reports",
                "/memos - Memos",
                "/profile - Profile",
                "/settings - Settings"
            ];
            
            const routeText = "Available navigation routes: " + routes.join(", ");
            addToConversation('agent', routeText, 0.95);
            speakText(routeText);
        }

        async function guidedTour() {
            const tourSteps = [
                "Welcome to the CTN Nigeria voice command system!",
                "You can navigate anywhere by saying 'Go to' followed by the page name.",
                "Try saying 'Create a new project' to start a new project.",
                "Ask 'What can you do?' to learn about my capabilities.",
                "Say 'Help me with tasks' for task management assistance.",
                "The tour is complete. Feel free to explore with voice commands!"
            ];
            
            for (const step of tourSteps) {
                addToConversation('agent', step, 0.95);
                await speakText(step);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        async function testAllFeatures() {
            showStatus('Running comprehensive voice system test...', 'info');
            
            try {
                // Test microphone
                await testMicrophone();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Test speech synthesis
                await speakText("Testing speech synthesis functionality");
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Test AI agent
                await testAgent();
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Test navigation
                const navResponse = await simulateAIResponse("Go to dashboard");
                addToConversation('agent', navResponse.text, navResponse.confidence);
                await speakText(navResponse.text);
                
                showStatus('All voice features tested successfully!', 'success');
                
            } catch (error) {
                showStatus(`Test failed: ${error.message}`, 'error');
            }
        }

        // Utility Functions
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
        }

        // Simulate voice command (for testing)
        window.simulateCommand = async function(command) {
            log(`🎯 Simulating command: "${command}"`);
            addToConversation('user', command, 1.0);
            const response = await simulateAIResponse(command);
            addToConversation('agent', response.text, response.confidence);
            await speakText(response.text);
            updateStats(command, 1.0, true);
        };

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeVoiceSystem);
    </script>
</body>
</html>
