
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

export interface StaffReport {
  id: string;
  report_type: 'weekly' | 'project' | 'site' | 'general';
  title: string;
  description: string;
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';
  submitted_by: string;
  reviewed_by?: string;
  submitted_at: string;
  reviewed_at?: string;
  review_comments?: string;
  metadata: any;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name: string;
    email: string;
  };
  reviewer?: {
    full_name: string;
    email: string;
  };
}

export interface CreateReportData {
  report_type: 'weekly' | 'project' | 'site' | 'general';
  title: string;
  description: string;
  metadata?: any;
}

export interface UpdateReportData {
  title?: string;
  description?: string;
  metadata?: any;
}

export interface ReviewReportData {
  status: 'approved' | 'rejected';
  review_comments?: string;
}

export const useStaffReports = () => {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const queryClient = useQueryClient();

  // Fetch reports based on user role
  const { data: reports = [], isLoading, error, refetch } = useQuery({
    queryKey: ['staff-reports', userProfile?.role, userProfile?.id],
    queryFn: async () => {
      console.log('Fetching staff reports...');
      // Try new schema first (with profiles relationships)
      let query = supabase
        .from('reports')
        .select(`
          *,
          profiles:submitted_by!inner (
            full_name,
            email
          ),
          reviewer:reviewed_by (
            full_name,
            email
          )
        `);

      // If staff, only show their own reports
      if (userProfile?.role === 'staff') {
        query = query.eq('submitted_by', userProfile.id);
      }
      
      // If manager, show reports from their department
      if (userProfile?.role === 'manager' && userProfile?.department_id) {
        const { data: departmentStaff } = await supabase
          .from('profiles')
          .select('id')
          .eq('department_id', userProfile.department_id);
        
        if (departmentStaff && departmentStaff.length > 0) {
          const staffIds = departmentStaff.map(staff => staff.id);
          query = query.in('submitted_by', staffIds);
        }
      }

      let { data, error } = await query.order('created_at', { ascending: false });

      // If the new schema fails (400, 406, or PGRST116 errors), fall back to old schema
      if (error && (error.code === 'PGRST116' || error.status === 400 || error.status === 406)) {
        console.log('Falling back to old schema for reports, error:', error);
        let fallbackQuery = supabase
          .from('reports')
          .select('*');

        // Apply the same filters for fallback
        if (userProfile?.role === 'staff') {
          fallbackQuery = fallbackQuery.eq('submitted_by', userProfile.id);
        }

        if (userProfile?.role === 'manager' && userProfile?.department_id) {
          const { data: departmentStaff } = await supabase
            .from('profiles')
            .select('id')
            .eq('department_id', userProfile.department_id);

          if (departmentStaff && departmentStaff.length > 0) {
            const staffIds = departmentStaff.map(staff => staff.id);
            fallbackQuery = fallbackQuery.in('submitted_by', staffIds);
          }
        }

        const result = await fallbackQuery.order('created_at', { ascending: false });
        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('Error fetching reports:', error);
        throw error;
      }

      console.log('Reports fetched:', data);
      
      // Transform the data to match our StaffReport interface
      const transformedData: StaffReport[] = (data || []).map(report => ({
        id: report.id,
        report_type: report.report_type as 'weekly' | 'project' | 'site' | 'general',
        title: report.title,
        description: report.description,
        status: report.status as 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected',
        submitted_by: report.submitted_by,
        reviewed_by: report.reviewed_by,
        submitted_at: report.submitted_at,
        reviewed_at: report.reviewed_at,
        review_comments: report.review_comments,
        metadata: report.metadata,
        created_at: report.created_at,
        updated_at: report.updated_at,
        // Handle both new schema (with profiles relationship) and old schema (without)
        profiles: report.profiles ? (Array.isArray(report.profiles) ? report.profiles[0] : report.profiles) : null,
        reviewer: report.reviewer ? (Array.isArray(report.reviewer) ? report.reviewer[0] : report.reviewer) : null,
      }));

      return transformedData;
    },
    enabled: !!userProfile?.id,
  });

  // Create new report
  const createReport = useMutation({
    mutationFn: async (data: CreateReportData) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('reports')
        .insert([{
          ...data,
          submitted_by: userProfile.id,
          status: 'submitted'
        }]);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Report submitted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-reports'] });
    },
    onError: (error, variables, context) => {
      console.error('[useStaffReports] Error submitting report:', error, {
        user: userProfile?.id,
        reportData: variables
      });
      toast({
        title: "Error",
        description: "Failed to submit report",
        variant: "destructive",
      });
    }
  });

  // Update report (only for drafts or by author)
  const updateReport = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateReportData }) => {
      const { error } = await supabase
        .from('reports')
        .update(data)
        .eq('id', id)
        .eq('submitted_by', userProfile?.id); // Only author can update

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Report updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-reports'] });
    },
    onError: (error) => {
      console.error('Error updating report:', error);
      toast({
        title: "Error",
        description: "Failed to update report",
        variant: "destructive",
      });
    }
  });

  // Review report (for managers/admins)
  const reviewReport = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: ReviewReportData }) => {
      if (!userProfile?.id) throw new Error('User not authenticated');
      
      const { error } = await supabase
        .from('reports')
        .update({
          status: data.status,
          review_comments: data.review_comments,
          reviewed_by: userProfile.id,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Report reviewed successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-reports'] });
    },
    onError: (error) => {
      console.error('Error reviewing report:', error);
      toast({
        title: "Error",
        description: "Failed to review report",
        variant: "destructive",
      });
    }
  });

  // Delete report (for managers/admins or report author)
  const deleteReport = useMutation({
    mutationFn: async (id: string) => {
      let query = supabase.from('reports').delete().eq('id', id);
      
      // Staff can only delete their own reports
      if (userProfile?.role === 'staff') {
        query = query.eq('submitted_by', userProfile.id);
      }

      const { error } = await query;
      if (error) throw error;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Report deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-reports'] });
    },
    onError: (error) => {
      console.error('Error deleting report:', error);
      toast({
        title: "Error",
        description: "Failed to delete report",
        variant: "destructive",
      });
    }
  });

  return {
    reports,
    isLoading,
    error,
    loading,
    createReport,
    updateReport,
    reviewReport,
    deleteReport,
    refetch
  };
};
