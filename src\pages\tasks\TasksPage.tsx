import { TaskAssignmentManagement } from '@/components/tasks/TaskAssignmentManagement'
import { StaffTaskDashboard } from '@/components/staff/StaffTaskDashboard'
import { useAuth } from '@/components/auth/AuthProvider'

const TasksPage = () => {
  const { userProfile } = useAuth()
  const isStaff = userProfile?.role === 'staff'

  return (
    <div className='p-4 md:p-6'>
      {isStaff ? <StaffTaskDashboard /> : <TaskAssignmentManagement />}
    </div>
  )
}

export default TasksPage
