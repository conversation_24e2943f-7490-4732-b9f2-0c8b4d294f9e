import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/components/auth/AuthProvider';
import { supabase } from '@/integrations/supabase/client';
import { JWTUtils, TokenValidationResult, DecodedJWT } from '@/utils/jwtUtils';
import { 
  Shield, 
  Clock, 
  User, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';

export const JWTDebugPanel: React.FC = () => {
  const { session, user } = useAuth();
  const [customToken, setCustomToken] = useState('');
  const [showTokens, setShowTokens] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [validation, setValidation] = useState<TokenValidationResult | null>(null);
  const [decoded, setDecoded] = useState<DecodedJWT | null>(null);

  // Get current session token
  const currentToken = session?.access_token;

  // Update debug info when token changes
  useEffect(() => {
    if (currentToken) {
      const info = JWTUtils.debug(currentToken);
      setDebugInfo(info);
      setValidation(info.validation);
      setDecoded(info.decoded);
    }
  }, [currentToken]);

  const analyzeCustomToken = () => {
    if (!customToken.trim()) return;
    
    const info = JWTUtils.debug(customToken.trim());
    setDebugInfo(info);
    setValidation(info.validation);
    setDecoded(info.decoded);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatJSON = (obj: any) => {
    return JSON.stringify(obj, null, 2);
  };

  const getStatusBadge = (isValid: boolean, isExpired: boolean) => {
    if (isExpired) {
      return <Badge variant="destructive" className="flex items-center gap-1">
        <XCircle className="h-3 w-3" />
        Expired
      </Badge>;
    }
    if (isValid) {
      return <Badge variant="default" className="flex items-center gap-1 bg-green-500">
        <CheckCircle className="h-3 w-3" />
        Valid
      </Badge>;
    }
    return <Badge variant="destructive" className="flex items-center gap-1">
      <XCircle className="h-3 w-3" />
      Invalid
    </Badge>;
  };

  const TokenDisplay: React.FC<{ token: string; label: string }> = ({ token, label }) => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <label className="text-sm font-medium">{label}</label>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowTokens(!showTokens)}
          >
            {showTokens ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => copyToClipboard(token)}
          >
            <Copy className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-md font-mono text-xs break-all">
        {showTokens ? token : '•'.repeat(Math.min(token.length, 100))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            JWT Debug Panel
          </CardTitle>
          <CardDescription>
            Inspect and validate JWT tokens for debugging authentication issues
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="current" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="current">Current Session</TabsTrigger>
              <TabsTrigger value="custom">Custom Token</TabsTrigger>
              <TabsTrigger value="analysis">Analysis</TabsTrigger>
            </TabsList>

            <TabsContent value="current" className="space-y-4">
              {currentToken ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Current Session Token</h3>
                    {validation && (
                      <div className="flex items-center gap-2">
                        {getStatusBadge(validation.isValid, validation.isExpired)}
                        <Badge variant="outline" className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {JWTUtils.getFormattedTimeToExpiry(currentToken)}
                        </Badge>
                      </div>
                    )}
                  </div>

                  <TokenDisplay token={currentToken} label="Access Token" />

                  {session?.refresh_token && (
                    <TokenDisplay token={session.refresh_token} label="Refresh Token" />
                  )}

                  {/* User Info */}
                  {debugInfo?.userInfo && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm flex items-center gap-2">
                          <User className="h-4 w-4" />
                          User Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">ID:</span>
                            <p className="font-mono text-xs break-all">{debugInfo.userInfo.id}</p>
                          </div>
                          <div>
                            <span className="font-medium">Email:</span>
                            <p>{debugInfo.userInfo.email || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium">Role:</span>
                            <p>{debugInfo.userInfo.role || 'N/A'}</p>
                          </div>
                          <div>
                            <span className="font-medium">Full Name:</span>
                            <p>{debugInfo.userInfo.fullName || 'N/A'}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Timing Information */}
                  {debugInfo?.timing && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-sm flex items-center gap-2">
                          <Clock className="h-4 w-4" />
                          Token Timing
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Age:</span>
                            <p>{debugInfo.timing.age}</p>
                          </div>
                          <div>
                            <span className="font-medium">Time to Expiry:</span>
                            <p>{debugInfo.timing.timeToExpiry}</p>
                          </div>
                          <div>
                            <span className="font-medium">Should Refresh:</span>
                            <p>{debugInfo.timing.shouldRefresh ? 'Yes' : 'No'}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    No active session found. Please log in to view token information.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>

            <TabsContent value="custom" className="space-y-4">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Paste JWT Token</label>
                  <Textarea
                    placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                    value={customToken}
                    onChange={(e) => setCustomToken(e.target.value)}
                    className="font-mono text-xs"
                    rows={4}
                  />
                </div>
                <Button onClick={analyzeCustomToken} disabled={!customToken.trim()}>
                  <Shield className="h-4 w-4 mr-2" />
                  Analyze Token
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="analysis" className="space-y-4">
              {validation && decoded ? (
                <div className="space-y-4">
                  {/* Validation Results */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Validation Results</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span>Status:</span>
                          {getStatusBadge(validation.isValid, validation.isExpired)}
                        </div>

                        {validation.errors.length > 0 && (
                          <div>
                            <span className="font-medium text-red-600">Errors:</span>
                            <ul className="list-disc list-inside text-sm text-red-600 mt-1">
                              {validation.errors.map((error, index) => (
                                <li key={index}>{error}</li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {validation.warnings.length > 0 && (
                          <div>
                            <span className="font-medium text-yellow-600">Warnings:</span>
                            <ul className="list-disc list-inside text-sm text-yellow-600 mt-1">
                              {validation.warnings.map((warning, index) => (
                                <li key={index}>{warning}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Token Structure */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Token Structure</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Tabs defaultValue="header" className="w-full">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="header">Header</TabsTrigger>
                          <TabsTrigger value="payload">Payload</TabsTrigger>
                          <TabsTrigger value="signature">Signature</TabsTrigger>
                        </TabsList>

                        <TabsContent value="header">
                          <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md text-xs overflow-auto">
                            {formatJSON(decoded.header)}
                          </pre>
                        </TabsContent>

                        <TabsContent value="payload">
                          <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md text-xs overflow-auto">
                            {formatJSON(decoded.payload)}
                          </pre>
                        </TabsContent>

                        <TabsContent value="signature">
                          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md font-mono text-xs break-all">
                            {decoded.signature}
                          </div>
                        </TabsContent>
                      </Tabs>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    No token analyzed yet. Use the "Current Session" or "Custom Token" tabs to analyze a JWT.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
