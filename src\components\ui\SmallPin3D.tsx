import React from 'react';
import { MapPin } from 'lucide-react';

interface SmallPin3DProps {
  location?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
}

export const SmallPin3D: React.FC<SmallPin3DProps> = ({
  location = "Current Location",
  className = "",
  size = 'sm',
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  const variantClasses = {
    default: 'from-gray-800 to-black shadow-black/30',
    success: 'from-green-500 to-green-600 shadow-green-500/30',
    warning: 'from-yellow-500 to-yellow-600 shadow-yellow-500/30',
    error: 'from-red-500 to-red-600 shadow-red-500/30'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <div className={`relative inline-flex items-center justify-center ${className}`}>
      {/* 3D Pin Container */}
      <div 
        className={`
          ${sizeClasses[size]}
          bg-gradient-to-br ${variantClasses[variant]}
          rounded-full
          shadow-lg ${variantClasses[variant]}
          transform transition-all duration-300
          hover:scale-110 hover:shadow-xl
          cursor-pointer
          relative
          border-2 border-white/20
          backdrop-blur-sm
        `}
        style={{
          transform: 'perspective(100px) rotateX(10deg)',
          filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))'
        }}
      >
        {/* Inner glow effect */}
        <div 
          className={`
            absolute inset-1 
            bg-gradient-to-br from-white/30 to-transparent 
            rounded-full
          `}
        />
        
        {/* Pin Icon */}
        <MapPin 
          className={`
            ${iconSizes[size]} 
            text-white 
            relative z-10
            drop-shadow-sm
          `}
        />
        
        {/* Pulse animation */}
        <div 
          className={`
            absolute inset-0 
            bg-gradient-to-br ${variantClasses[variant]}
            rounded-full 
            animate-ping 
            opacity-20
          `}
        />
      </div>
      
      {/* Location tooltip */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="bg-black/80 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          {location}
        </div>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1 border-l-4 border-r-4 border-b-4 border-transparent border-b-black/80"></div>
      </div>
    </div>
  );
};
