import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Smartphone, 
  AlertTriangle, 
  RefreshCw, 
  ExternalLink,
  Download,
  Settings,
  Wifi,
  WifiOff
} from "lucide-react";

interface FallbackProps {
  error?: Error;
  resetError?: () => void;
  context?: string;
}

const MobileFallback: React.FC<FallbackProps> = ({ 
  error, 
  resetError, 
  context = 'application' 
}) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = async () => {
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    // Wait a bit before retrying
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (resetError) {
      resetError();
    } else {
      // Fallback: reload the page
      window.location.reload();
    }
    
    setIsRetrying(false);
  };

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const clearCache = async () => {
    try {
      // Clear localStorage
      localStorage.clear();
      
      // Clear sessionStorage
      sessionStorage.clear();
      
      // Clear service worker cache if available
      if ('serviceWorker' in navigator && 'caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }
      
      // Reload after clearing cache
      window.location.reload();
    } catch (error) {
      console.error('Failed to clear cache:', error);
      // Fallback to simple reload
      window.location.reload();
    }
  };

  const getErrorType = () => {
    if (!isOnline) return 'network';
    if (error?.message?.includes('ChunkLoadError')) return 'chunk';
    if (error?.message?.includes('Loading chunk')) return 'chunk';
    if (error?.message?.includes('Loading CSS chunk')) return 'chunk';
    if (error?.message?.includes('Failed to fetch')) return 'network';
    return 'unknown';
  };

  const getErrorMessage = () => {
    const errorType = getErrorType();
    
    switch (errorType) {
      case 'network':
        return isOnline 
          ? 'Network connection is unstable. Please check your internet connection.'
          : 'You are currently offline. Please check your internet connection.';
      case 'chunk':
        return 'Failed to load application resources. This usually happens after an app update.';
      default:
        return error?.message || `An unexpected error occurred in the ${context}.`;
    }
  };

  const getRecommendedActions = () => {
    const errorType = getErrorType();
    
    switch (errorType) {
      case 'network':
        return [
          'Check your internet connection',
          'Try switching between WiFi and mobile data',
          'Disable VPN if you\'re using one',
          'Try again in a few moments'
        ];
      case 'chunk':
        return [
          'Clear your browser cache',
          'Reload the application',
          'Update your browser if possible',
          'Try using an incognito/private window'
        ];
      default:
        return [
          'Try refreshing the page',
          'Clear browser cache and data',
          'Check if the issue persists',
          'Contact support if the problem continues'
        ];
    }
  };

  return (
    <div className="min-h-screen bg-black flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-4">
        {/* Main Error Card */}
        <Card className="bg-black border-red-500/30 text-white">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 rounded-full bg-red-500/20 border border-red-500/30">
                <Smartphone className="h-8 w-8 text-red-500" />
              </div>
            </div>
            <CardTitle className="text-red-400">
              {getErrorType() === 'network' ? 'Connection Issue' : 'Application Error'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Connection Status */}
            <div className="flex items-center justify-center gap-2 p-2 rounded border border-red-500/20">
              {isOnline ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm">
                {isOnline ? 'Connected' : 'Offline'}
              </span>
            </div>

            {/* Error Message */}
            <Alert className="border-red-500/30 bg-red-500/10">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-sm">
                {getErrorMessage()}
              </AlertDescription>
            </Alert>

            {/* Retry Information */}
            {retryCount > 0 && (
              <div className="text-center text-sm text-gray-400">
                Retry attempts: {retryCount}
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-2">
              <Button 
                onClick={handleRetry}
                disabled={isRetrying}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                {isRetrying ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Retrying...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </>
                )}
              </Button>

              <div className="grid grid-cols-2 gap-2">
                <Button 
                  variant="outline" 
                  onClick={handleReload}
                  className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                >
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Reload
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleGoHome}
                  className="border-red-500/30 text-red-400 hover:bg-red-500/10"
                >
                  <ExternalLink className="h-4 w-4 mr-1" />
                  Home
                </Button>
              </div>

              {getErrorType() === 'chunk' && (
                <Button 
                  variant="outline" 
                  onClick={clearCache}
                  className="w-full border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Clear Cache & Reload
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Troubleshooting Tips */}
        <Card className="bg-black border-red-500/20 text-white">
          <CardHeader>
            <CardTitle className="text-sm text-red-400">Troubleshooting Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-1 text-xs text-gray-300">
              {getRecommendedActions().map((action, index) => (
                <li key={index} className="flex items-start gap-2">
                  <span className="text-red-500 mt-1">•</span>
                  <span>{action}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Debug Information */}
        {error && (
          <Card className="bg-black border-gray-500/20 text-white">
            <CardHeader>
              <CardTitle className="text-xs text-gray-400">Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-gray-500 font-mono break-all">
                <div><strong>Error:</strong> {error.name}</div>
                <div><strong>Message:</strong> {error.message}</div>
                <div><strong>Context:</strong> {context}</div>
                <div><strong>User Agent:</strong> {navigator.userAgent.substring(0, 50)}...</div>
                <div><strong>Timestamp:</strong> {new Date().toISOString()}</div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Footer */}
        <div className="text-center text-xs text-gray-500">
          CTNL AI Work-Board • Mobile Fallback Mode
        </div>
      </div>
    </div>
  );
};

export default MobileFallback;
