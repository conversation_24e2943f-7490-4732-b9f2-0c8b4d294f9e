import React from 'react';
import { 
  Folder, 
  MoreVertical, 
  Users, 
  Lock, 
  Globe, 
  Star,
  Archive,
  Briefcase,
  Shield,
  FileText,
  User,
  Share2,
  Edit,
  Trash2
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface CustomFolderCardProps {
  folder: {
    id: string;
    name: string;
    description?: string;
    access_level: string;
    document_count?: number;
    created_at?: string;
    color?: string;
    icon?: string;
    is_system?: boolean;
    folder_type?: string;
    creator_name?: string;
  };
  onOpen?: (folderId: string) => void;
  onEdit?: (folderId: string) => void;
  onDelete?: (folderId: string) => void;
  onShare?: (folderId: string) => void;
}

const getIconComponent = (iconName: string) => {
  const iconMap: { [key: string]: React.ComponentType<any> } = {
    folder: Folder,
    user: User,
    users: Users,
    briefcase: Briefcase,
    archive: Archive,
    star: Star,
    shield: Shield,
    template: FileText,
  };
  
  const IconComponent = iconMap[iconName] || Folder;
  return IconComponent;
};

export const CustomFolderCard = ({ 
  folder, 
  onOpen, 
  onEdit, 
  onDelete, 
  onShare 
}: CustomFolderCardProps) => {
  const getAccessIcon = () => {
    switch (folder.access_level) {
      case 'private':
        return <Lock className="h-3 w-3" />;
      case 'department':
        return <Users className="h-3 w-3" />;
      case 'public':
        return <Globe className="h-3 w-3" />;
      default:
        return <Lock className="h-3 w-3" />;
    }
  };

  const getAccessColor = () => {
    switch (folder.access_level) {
      case 'private':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'department':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'public':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const IconComponent = getIconComponent(folder.icon || 'folder');
  const folderColor = folder.color || '#3B82F6';

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-0 shadow-sm hover:scale-[1.02]">
      <CardContent className="p-0">
        {/* Header with gradient background */}
        <div 
          className="h-20 rounded-t-lg relative overflow-hidden"
          style={{
            background: `linear-gradient(135deg, ${folderColor}15, ${folderColor}25)`
          }}
        >
          <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent" />
          
          {/* Folder icon */}
          <div className="absolute top-3 left-3">
            <div 
              className="p-2 rounded-lg shadow-sm"
              style={{ 
                backgroundColor: folderColor + '20',
                border: `1px solid ${folderColor}30`
              }}
            >
              <IconComponent 
                className="h-5 w-5" 
                style={{ color: folderColor }} 
              />
            </div>
          </div>

          {/* Actions dropdown */}
          <div className="absolute top-3 right-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm"
                  className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity bg-white/80 hover:bg-white/90"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={() => onOpen?.(folder.id)}>
                  <Folder className="h-4 w-4 mr-2" />
                  Open Folder
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit?.(folder.id)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Details
                </DropdownMenuItem>
                {onShare && (
                  <DropdownMenuItem onClick={() => onShare(folder.id)}>
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Folder
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={() => onDelete?.(folder.id)}
                  disabled={folder.is_system}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {folder.is_system ? 'Cannot Delete' : 'Delete Folder'}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* System badge */}
          {folder.is_system && (
            <div className="absolute bottom-2 left-3">
              <Badge variant="outline" className="text-xs bg-white/90">
                <Shield className="h-3 w-3 mr-1" />
                System
              </Badge>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="p-4 space-y-3">
          {/* Folder name and description */}
          <div 
            className="space-y-1 cursor-pointer"
            onClick={() => onOpen?.(folder.id)}
          >
            <h3 className="font-semibold text-sm leading-tight line-clamp-1">
              {folder.name}
            </h3>
            {folder.description && (
              <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed">
                {folder.description}
              </p>
            )}
          </div>

          {/* Stats and access level */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge className={`text-xs px-2 py-1 ${getAccessColor()}`}>
                <span className="flex items-center gap-1">
                  {getAccessIcon()}
                  <span className="capitalize">{folder.access_level}</span>
                </span>
              </Badge>
              
              {folder.folder_type && folder.folder_type !== 'custom' && (
                <Badge variant="outline" className="text-xs">
                  {folder.folder_type}
                </Badge>
              )}
            </div>

            <div className="text-right">
              <p className="text-xs font-medium text-muted-foreground">
                {folder.document_count || 0} items
              </p>
              {folder.creator_name && (
                <p className="text-xs text-muted-foreground">
                  by {folder.creator_name}
                </p>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
