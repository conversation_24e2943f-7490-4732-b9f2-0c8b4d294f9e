import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, Database } from 'lucide-react';

export const DatabaseConnectionTest = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);

  const testDatabaseConnection = async () => {
    setLoading(true);
    setResults(null);

    const testResults: any = {
      timestamp: new Date().toISOString(),
      tests: []
    };

    try {
      // Test 1: Basic connection
      console.log('🔍 Testing basic Supabase connection...');
      testResults.tests.push({
        name: 'Basic Connection',
        status: 'running'
      });

      try {
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        testResults.tests[0].status = error ? 'failed' : 'passed';
        testResults.tests[0].error = error?.message;
        testResults.tests[0].data = data;
      } catch (err: any) {
        testResults.tests[0].status = 'failed';
        testResults.tests[0].error = err.message;
      }

      // Test 2: Profiles table structure
      console.log('🔍 Testing profiles table structure...');
      testResults.tests.push({
        name: 'Profiles Table Structure',
        status: 'running'
      });

      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('id, email, full_name, role, created_at')
          .limit(1);
        
        testResults.tests[1].status = error ? 'failed' : 'passed';
        testResults.tests[1].error = error?.message;
        testResults.tests[1].data = data;
        testResults.tests[1].columns = data?.[0] ? Object.keys(data[0]) : [];
      } catch (err: any) {
        testResults.tests[1].status = 'failed';
        testResults.tests[1].error = err.message;
      }

      // Test 3: Departments table
      console.log('🔍 Testing departments table...');
      testResults.tests.push({
        name: 'Departments Table',
        status: 'running'
      });

      try {
        const { data, error } = await supabase
          .from('departments')
          .select('id, name, description')
          .limit(1);
        
        testResults.tests[2].status = error ? 'failed' : 'passed';
        testResults.tests[2].error = error?.message;
        testResults.tests[2].data = data;
      } catch (err: any) {
        testResults.tests[2].status = 'failed';
        testResults.tests[2].error = err.message;
      }

      // Test 4: Auth user access
      console.log('🔍 Testing auth user access...');
      testResults.tests.push({
        name: 'Auth User Access',
        status: 'running'
      });

      try {
        const { data: { user }, error } = await supabase.auth.getUser();
        testResults.tests[3].status = error ? 'failed' : 'passed';
        testResults.tests[3].error = error?.message;
        testResults.tests[3].data = user ? { id: user.id, email: user.email } : null;
      } catch (err: any) {
        testResults.tests[3].status = 'failed';
        testResults.tests[3].error = err.message;
      }

      // Test 5: RLS policies (if user is authenticated)
      if (testResults.tests[3].status === 'passed' && testResults.tests[3].data?.id) {
        console.log('🔍 Testing RLS policies...');
        testResults.tests.push({
          name: 'RLS Policies (User Profile Access)',
          status: 'running'
        });

        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', testResults.tests[3].data.id)
            .single();
          
          testResults.tests[4].status = error ? 'failed' : 'passed';
          testResults.tests[4].error = error?.message;
          testResults.tests[4].data = data;
        } catch (err: any) {
          testResults.tests[4].status = 'failed';
          testResults.tests[4].error = err.message;
        }
      }

    } catch (globalError: any) {
      testResults.globalError = globalError.message;
    }

    setResults(testResults);
    setLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      default:
        return <Database className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Database Connection Test
          <Button onClick={testDatabaseConnection} disabled={loading} size="sm">
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Run Tests'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {results && (
          <>
            <div className="text-sm text-muted-foreground">
              Test run at: {new Date(results.timestamp).toLocaleString()}
            </div>

            {results.globalError && (
              <Alert variant="destructive">
                <AlertDescription>
                  Global Error: {results.globalError}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-3">
              {results.tests.map((test: any, index: number) => (
                <div key={index} className="border rounded-lg p-3">
                  <div className="flex items-center gap-2 mb-2">
                    {getStatusIcon(test.status)}
                    <span className="font-medium">{test.name}</span>
                    <span className={`text-sm px-2 py-1 rounded ${
                      test.status === 'passed' ? 'bg-green-100 text-green-800' :
                      test.status === 'failed' ? 'bg-red-100 text-red-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {test.status}
                    </span>
                  </div>
                  
                  {test.error && (
                    <div className="text-sm text-red-600 mb-2">
                      Error: {test.error}
                    </div>
                  )}
                  
                  {test.columns && (
                    <div className="text-sm text-muted-foreground mb-2">
                      Columns: {test.columns.join(', ')}
                    </div>
                  )}
                  
                  {test.data && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-muted-foreground">View Data</summary>
                      <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                        {JSON.stringify(test.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </>
        )}

        {!results && !loading && (
          <div className="text-center text-muted-foreground py-8">
            Click "Run Tests" to check database connectivity and table access
          </div>
        )}
      </CardContent>
    </Card>
  );
};
