<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Debug Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .debug-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .debug-section h3 {
            margin: 0 0 15px 0;
            color: #007bff;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .result-box {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
        }
        .error-box {
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #dc3545;
        }
        .warning-box {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        .code-block {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .diagnostic-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .diagnostic-card h4 {
            margin: 0 0 10px 0;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Dashboard Debug Tool</h1>
            <p>Diagnose and fix empty dashboard issues</p>
        </div>
        
        <div class="warning-box">
            <h3>🚨 Issue Detected</h3>
            <p>Your dashboard area appears empty. This tool will help identify and fix the problem.</p>
        </div>
        
        <div class="debug-section">
            <h3>🔍 Quick Diagnosis</h3>
            <p>Click the button below to run a comprehensive diagnosis of your dashboard system:</p>
            <button id="diagnoseBtn" class="button" onclick="runDiagnosis()">
                🔍 Run Dashboard Diagnosis
            </button>
            <button id="fixBtn" class="button" onclick="applyFixes()" disabled>
                🔧 Apply Fixes
            </button>
            <button id="dashboardBtn" class="button" onclick="openDashboard()">
                📊 Open Dashboard
            </button>
        </div>
        
        <div id="diagnosticResults"></div>
        
        <div class="debug-section">
            <h3>💡 Common Solutions</h3>
            <div class="diagnostic-grid">
                <div class="diagnostic-card">
                    <h4>1. Authentication Issue</h4>
                    <p><strong>Problem:</strong> User not properly authenticated</p>
                    <p><strong>Solution:</strong> Clear cache and re-login</p>
                    <button class="button" onclick="clearAuthAndReload()">Clear Auth & Reload</button>
                </div>
                
                <div class="diagnostic-card">
                    <h4>2. Component Loading Error</h4>
                    <p><strong>Problem:</strong> Dashboard components failing to load</p>
                    <p><strong>Solution:</strong> Force refresh and clear cache</p>
                    <button class="button" onclick="forceRefresh()">Force Refresh</button>
                </div>
                
                <div class="diagnostic-card">
                    <h4>3. Data Loading Issue</h4>
                    <p><strong>Problem:</strong> Dashboard data not loading</p>
                    <p><strong>Solution:</strong> Reset data cache</p>
                    <button class="button" onclick="resetDataCache()">Reset Data Cache</button>
                </div>
                
                <div class="diagnostic-card">
                    <h4>4. Role-Based Routing</h4>
                    <p><strong>Problem:</strong> Manager dashboard not loading correctly</p>
                    <p><strong>Solution:</strong> Force manager dashboard route</p>
                    <button class="button" onclick="forceManagerDashboard()">Force Manager Dashboard</button>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        let diagnosticResults = {};
        
        async function runDiagnosis() {
            const diagnoseBtn = document.getElementById('diagnoseBtn');
            const fixBtn = document.getElementById('fixBtn');
            
            diagnoseBtn.disabled = true;
            diagnoseBtn.textContent = '🔄 Diagnosing...';
            
            try {
                let resultsHtml = '<div class="debug-section"><h3>🔍 Diagnostic Results</h3>';
                
                // Check 1: Authentication Status
                try {
                    const { data: { user }, error } = await supabase.auth.getUser();
                    if (user) {
                        resultsHtml += `
                            <div class="result-box">
                                <h4>✅ Authentication Status</h4>
                                <p><strong>User:</strong> ${user.email}</p>
                                <p><strong>ID:</strong> ${user.id}</p>
                                <p><strong>Status:</strong> Authenticated</p>
                            </div>
                        `;
                        diagnosticResults.auth = { status: 'success', user };
                    } else {
                        resultsHtml += `
                            <div class="error-box">
                                <h4>❌ Authentication Status</h4>
                                <p>User not authenticated. This is likely the cause of the empty dashboard.</p>
                            </div>
                        `;
                        diagnosticResults.auth = { status: 'error', message: 'Not authenticated' };
                    }
                } catch (authError) {
                    resultsHtml += `
                        <div class="error-box">
                            <h4>❌ Authentication Error</h4>
                            <p>Error checking authentication: ${authError.message}</p>
                        </div>
                    `;
                    diagnosticResults.auth = { status: 'error', error: authError.message };
                }
                
                // Check 2: User Profile
                try {
                    const { data: profile, error: profileError } = await supabase
                        .from('profiles')
                        .select('*')
                        .single();
                    
                    if (profile) {
                        resultsHtml += `
                            <div class="result-box">
                                <h4>✅ User Profile</h4>
                                <p><strong>Name:</strong> ${profile.full_name || 'Not set'}</p>
                                <p><strong>Role:</strong> ${profile.role || 'Not set'}</p>
                                <p><strong>Account Type:</strong> ${profile.account_type || 'Not set'}</p>
                            </div>
                        `;
                        diagnosticResults.profile = { status: 'success', profile };
                    } else {
                        resultsHtml += `
                            <div class="error-box">
                                <h4>❌ User Profile</h4>
                                <p>No user profile found. This could cause dashboard routing issues.</p>
                            </div>
                        `;
                        diagnosticResults.profile = { status: 'error', message: 'No profile found' };
                    }
                } catch (profileError) {
                    resultsHtml += `
                        <div class="warning-box">
                            <h4>⚠️ User Profile</h4>
                            <p>Error loading profile: ${profileError.message}</p>
                        </div>
                    `;
                    diagnosticResults.profile = { status: 'warning', error: profileError.message };
                }
                
                // Check 3: Dashboard Data
                const tables = ['time_logs', 'expense_reports', 'projects', 'tasks', 'notifications'];
                let dataStatus = [];
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('*')
                            .limit(1);
                        
                        if (error) {
                            dataStatus.push(`❌ ${table}: ${error.message}`);
                        } else {
                            dataStatus.push(`✅ ${table}: ${data?.length || 0} records accessible`);
                        }
                    } catch (e) {
                        dataStatus.push(`❌ ${table}: ${e.message}`);
                    }
                }
                
                resultsHtml += `
                    <div class="result-box">
                        <h4>📊 Data Access Status</h4>
                        ${dataStatus.map(status => `<p>${status}</p>`).join('')}
                    </div>
                `;
                
                // Check 4: Local Storage
                const hasThemeDialog = localStorage.getItem('hasSeenThemeDialog');
                const authData = localStorage.getItem('supabase.auth.token');
                
                resultsHtml += `
                    <div class="result-box">
                        <h4>💾 Local Storage Status</h4>
                        <p><strong>Theme Dialog:</strong> ${hasThemeDialog ? 'Seen' : 'Not seen'}</p>
                        <p><strong>Auth Token:</strong> ${authData ? 'Present' : 'Missing'}</p>
                    </div>
                `;
                
                // Check 5: Current URL and Route
                resultsHtml += `
                    <div class="result-box">
                        <h4>🌐 Current Route Status</h4>
                        <p><strong>URL:</strong> ${window.location.href}</p>
                        <p><strong>Path:</strong> ${window.location.pathname}</p>
                        <p><strong>Expected:</strong> Should be /dashboard or /dashboard/manager for manager role</p>
                    </div>
                `;
                
                resultsHtml += '</div>';
                
                // Recommendations
                resultsHtml += `
                    <div class="debug-section">
                        <h3>💡 Recommendations</h3>
                        ${generateRecommendations()}
                    </div>
                `;
                
                document.getElementById('diagnosticResults').innerHTML = resultsHtml;
                
                // Enable fix button if issues found
                if (diagnosticResults.auth?.status === 'error' || diagnosticResults.profile?.status === 'error') {
                    fixBtn.disabled = false;
                }
                
            } catch (error) {
                document.getElementById('diagnosticResults').innerHTML = `
                    <div class="error-box">
                        <h4>❌ Diagnosis Failed</h4>
                        <p>Error running diagnosis: ${error.message}</p>
                    </div>
                `;
            } finally {
                diagnoseBtn.disabled = false;
                diagnoseBtn.textContent = '🔍 Run Dashboard Diagnosis';
            }
        }
        
        function generateRecommendations() {
            let recommendations = '';
            
            if (diagnosticResults.auth?.status === 'error') {
                recommendations += `
                    <div class="warning-box">
                        <h4>🔑 Authentication Issue</h4>
                        <p>You need to log in to access the dashboard. Click "Clear Auth & Reload" to start fresh.</p>
                    </div>
                `;
            }
            
            if (diagnosticResults.profile?.status === 'error') {
                recommendations += `
                    <div class="warning-box">
                        <h4>👤 Profile Issue</h4>
                        <p>Your user profile is missing. This can cause dashboard routing problems.</p>
                    </div>
                `;
            }
            
            if (window.location.pathname === '/dashboard-debug.html') {
                recommendations += `
                    <div class="result-box">
                        <h4>🎯 Next Steps</h4>
                        <p>1. Try "Force Manager Dashboard" to navigate directly to your dashboard</p>
                        <p>2. If that doesn't work, try "Clear Auth & Reload" to start fresh</p>
                        <p>3. Check the browser console for any JavaScript errors</p>
                    </div>
                `;
            }
            
            return recommendations || `
                <div class="result-box">
                    <h4>✅ System Looks Good</h4>
                    <p>No obvious issues detected. Try refreshing the dashboard or clearing cache.</p>
                </div>
            `;
        }
        
        async function applyFixes() {
            const fixBtn = document.getElementById('fixBtn');
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Applying Fixes...';
            
            try {
                // Clear any problematic cache
                localStorage.removeItem('hasSeenThemeDialog');
                
                // Force refresh
                window.location.reload();
                
            } catch (error) {
                alert('Error applying fixes: ' + error.message);
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🔧 Apply Fixes';
            }
        }
        
        function clearAuthAndReload() {
            localStorage.clear();
            sessionStorage.clear();
            window.location.href = '/auth';
        }
        
        function forceRefresh() {
            window.location.reload(true);
        }
        
        function resetDataCache() {
            // Clear any cached data
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            localStorage.removeItem('dashboard-data');
            window.location.reload();
        }
        
        function forceManagerDashboard() {
            window.location.href = '/dashboard';
        }
        
        function openDashboard() {
            window.open('/dashboard', '_blank');
        }
        
        // Make functions global
        window.runDiagnosis = runDiagnosis;
        window.applyFixes = applyFixes;
        window.clearAuthAndReload = clearAuthAndReload;
        window.forceRefresh = forceRefresh;
        window.resetDataCache = resetDataCache;
        window.forceManagerDashboard = forceManagerDashboard;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
