import { supabase } from "@/integrations/supabase/client";

export type ActivitySeverity = 'info' | 'warning' | 'error' | 'critical' | 'success';
export type ActivityCategory = 'general' | 'auth' | 'project' | 'user' | 'system' | 'database' | 'api' | 'security';

export interface ActivityLogEntry {
  user_id?: string;
  activity_type: string;
  description: string;
  metadata?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
  severity?: ActivitySeverity;
  category?: ActivityCategory;
}

export interface ActivityFilter {
  user_id?: string;
  activity_type?: string;
  severity?: ActivitySeverity;
  category?: ActivityCategory;
  start_date?: string;
  end_date?: string;
  limit?: number;
}

export class ActivityLogger {
  private static instance: ActivityLogger;
  private isEnabled: boolean = true;
  private batchSize: number = 10;
  private batchTimeout: number = 5000; // 5 seconds
  private pendingLogs: ActivityLogEntry[] = [];
  private batchTimer: NodeJS.Timeout | null = null;

  private constructor() {
    // Initialize batch processing
    this.startBatchProcessing();
  }

  public static getInstance(): ActivityLogger {
    if (!ActivityLogger.instance) {
      ActivityLogger.instance = new ActivityLogger();
    }
    return ActivityLogger.instance;
  }

  /**
   * Log a single activity
   */
  public async log(entry: ActivityLogEntry): Promise<string | null> {
    if (!this.isEnabled) return null;

    try {
      // Add browser context if available
      const enrichedEntry = this.enrichEntry(entry);

      // For critical activities, log immediately
      if (entry.severity === 'critical' || entry.severity === 'error') {
        return await this.logImmediate(enrichedEntry);
      }

      // Otherwise, add to batch
      this.addToBatch(enrichedEntry);
      return null;
    } catch (error) {
      console.error('Failed to log activity:', error);
      return null;
    }
  }

  /**
   * Log multiple activities at once
   */
  public async logBatch(entries: ActivityLogEntry[]): Promise<boolean> {
    if (!this.isEnabled || entries.length === 0) return false;

    try {
      const enrichedEntries = entries.map(entry => this.enrichEntry(entry));
      
      const { error } = await supabase
        .from('system_activities')
        .insert(enrichedEntries);

      if (error) {
        console.error('Failed to log activity batch:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to log activity batch:', error);
      return false;
    }
  }

  /**
   * Get activities with filtering
   */
  public async getActivities(filter: ActivityFilter = {}): Promise<any[]> {
    try {
      let query = supabase
        .from('system_activities')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email,
            department:department_id (
              name
            )
          )
        `);

      // Apply filters
      if (filter.user_id) {
        query = query.eq('user_id', filter.user_id);
      }

      if (filter.activity_type) {
        query = query.eq('activity_type', filter.activity_type);
      }

      if (filter.severity) {
        query = query.eq('severity', filter.severity);
      }

      if (filter.category) {
        query = query.eq('category', filter.category);
      }

      if (filter.start_date) {
        query = query.gte('created_at', filter.start_date);
      }

      if (filter.end_date) {
        query = query.lte('created_at', filter.end_date);
      }

      query = query
        .order('created_at', { ascending: false })
        .limit(filter.limit || 100);

      const { data, error } = await query;

      if (error) {
        console.error('Failed to get activities:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Failed to get activities:', error);
      return [];
    }
  }

  /**
   * Get activity statistics
   */
  public async getStatistics(days: number = 7): Promise<any> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .rpc('get_activity_statistics', { p_days: days });

      if (error) {
        console.error('Failed to get activity statistics:', error);
        return null;
      }

      return data?.[0] || null;
    } catch (error) {
      console.error('Failed to get activity statistics:', error);
      return null;
    }
  }

  /**
   * Enable or disable activity logging
   */
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    
    if (!enabled && this.batchTimer) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    } else if (enabled && !this.batchTimer) {
      this.startBatchProcessing();
    }
  }

  /**
   * Flush pending logs immediately
   */
  public async flush(): Promise<boolean> {
    if (this.pendingLogs.length === 0) return true;

    const logsToFlush = [...this.pendingLogs];
    this.pendingLogs = [];

    return await this.logBatch(logsToFlush);
  }

  // Private methods

  private enrichEntry(entry: ActivityLogEntry): ActivityLogEntry {
    const enriched = { ...entry };

    // Add default values
    enriched.severity = enriched.severity || 'info';
    enriched.category = enriched.category || 'general';

    // Add browser context if available
    if (typeof window !== 'undefined') {
      enriched.user_agent = enriched.user_agent || navigator.userAgent;
      enriched.session_id = enriched.session_id || this.getSessionId();
    }

    // Add timestamp to metadata
    enriched.metadata = {
      ...enriched.metadata,
      timestamp: new Date().toISOString(),
      client_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };

    return enriched;
  }

  private async logImmediate(entry: ActivityLogEntry): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('system_activities')
        .insert(entry)
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log immediate activity:', error);
        return null;
      }

      return data?.id || null;
    } catch (error) {
      console.error('Failed to log immediate activity:', error);
      return null;
    }
  }

  private addToBatch(entry: ActivityLogEntry): void {
    this.pendingLogs.push(entry);

    // If batch is full, flush immediately
    if (this.pendingLogs.length >= this.batchSize) {
      this.flush();
    }
  }

  private startBatchProcessing(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = setTimeout(() => {
      if (this.pendingLogs.length > 0) {
        this.flush();
      }
      this.startBatchProcessing();
    }, this.batchTimeout);
  }

  private getSessionId(): string {
    // Try to get session ID from localStorage or generate one
    if (typeof window !== 'undefined') {
      let sessionId = localStorage.getItem('activity_session_id');
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        localStorage.setItem('activity_session_id', sessionId);
      }
      return sessionId;
    }
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Convenience functions for common activity types
export const activityLogger = ActivityLogger.getInstance();

export const logUserActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'user',
    severity: 'info'
  });
};

export const logAuthActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>,
  severity: ActivitySeverity = 'info'
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'auth',
    severity
  });
};

export const logProjectActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'project',
    severity: 'info'
  });
};

export const logSystemActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>,
  severity: ActivitySeverity = 'info'
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'system',
    severity
  });
};

export const logSecurityActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>,
  severity: ActivitySeverity = 'warning'
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'security',
    severity
  });
};

export const logDatabaseActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'database',
    severity: 'info'
  });
};

export const logAPIActivity = (
  activity_type: string,
  description: string,
  metadata?: Record<string, any>,
  severity: ActivitySeverity = 'info'
) => {
  return activityLogger.log({
    activity_type,
    description,
    metadata,
    category: 'api',
    severity
  });
};

// Auto-flush on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    activityLogger.flush();
  });
}
