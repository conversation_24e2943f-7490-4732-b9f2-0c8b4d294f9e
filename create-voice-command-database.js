// Create database schema for AI Voice Command system
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function createVoiceCommandDatabase() {
  console.log('🚀 Starting AI Voice Command database creation...');
  
  try {
    // Create voice command database tables
    logStep('Creating AI Voice Command database tables...');
    
    const { error: tablesError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Voice Sessions Table
        CREATE TABLE IF NOT EXISTS public.voice_sessions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE CASCADE,
          session_token TEXT UNIQUE NOT NULL,
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'ended', 'error')),
          language TEXT DEFAULT 'en-US',
          voice_settings JSONB DEFAULT '{}',
          context JSONB DEFAULT '{}',
          conversation_history JSONB DEFAULT '[]',
          total_interactions INTEGER DEFAULT 0,
          total_duration_seconds INTEGER DEFAULT 0,
          last_interaction_at TIMESTAMP WITH TIME ZONE,
          started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          ended_at TIMESTAMP WITH TIME ZONE,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice Commands Table
        CREATE TABLE IF NOT EXISTS public.voice_commands (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          session_id UUID REFERENCES public.voice_sessions(id) ON DELETE CASCADE,
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          command_text TEXT NOT NULL,
          processed_text TEXT,
          intent TEXT,
          confidence_score DECIMAL(3,2) DEFAULT 0.0,
          entities JSONB DEFAULT '{}',
          command_type TEXT DEFAULT 'general' CHECK (command_type IN ('navigation', 'action', 'query', 'general', 'system')),
          status TEXT DEFAULT 'processing' CHECK (status IN ('processing', 'completed', 'failed', 'cancelled')),
          processing_time_ms INTEGER DEFAULT 0,
          audio_duration_seconds DECIMAL(5,2),
          audio_quality_score DECIMAL(3,2),
          language_detected TEXT,
          response_text TEXT,
          response_audio_url TEXT,
          response_duration_seconds DECIMAL(5,2),
          actions_performed JSONB DEFAULT '[]',
          navigation_path TEXT,
          error_message TEXT,
          context JSONB DEFAULT '{}',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice Agent Interactions Table
        CREATE TABLE IF NOT EXISTS public.voice_agent_interactions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          session_id UUID REFERENCES public.voice_sessions(id) ON DELETE CASCADE,
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          interaction_type TEXT DEFAULT 'conversation' CHECK (interaction_type IN ('conversation', 'navigation', 'assistance', 'tutorial', 'troubleshooting')),
          user_input TEXT NOT NULL,
          user_input_type TEXT DEFAULT 'voice' CHECK (user_input_type IN ('voice', 'text', 'gesture')),
          agent_response TEXT NOT NULL,
          agent_response_type TEXT DEFAULT 'voice' CHECK (agent_response_type IN ('voice', 'text', 'visual', 'action')),
          intent_recognized TEXT,
          confidence_score DECIMAL(3,2) DEFAULT 0.0,
          context_used JSONB DEFAULT '{}',
          actions_suggested JSONB DEFAULT '[]',
          actions_executed JSONB DEFAULT '[]',
          navigation_performed TEXT,
          help_provided TEXT,
          user_satisfaction_score INTEGER CHECK (user_satisfaction_score >= 1 AND user_satisfaction_score <= 5),
          follow_up_needed BOOLEAN DEFAULT FALSE,
          escalation_required BOOLEAN DEFAULT FALSE,
          processing_time_ms INTEGER DEFAULT 0,
          audio_input_url TEXT,
          audio_output_url TEXT,
          visual_feedback JSONB DEFAULT '{}',
          error_occurred BOOLEAN DEFAULT FALSE,
          error_details TEXT,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice Command Templates Table
        CREATE TABLE IF NOT EXISTS public.voice_command_templates (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          category TEXT NOT NULL,
          subcategory TEXT,
          command_patterns TEXT[] NOT NULL,
          intent TEXT NOT NULL,
          description TEXT,
          example_phrases TEXT[],
          required_parameters TEXT[],
          optional_parameters TEXT[],
          response_templates TEXT[],
          action_mappings JSONB DEFAULT '{}',
          navigation_targets TEXT[],
          permission_required TEXT,
          context_requirements JSONB DEFAULT '{}',
          success_criteria TEXT,
          fallback_actions JSONB DEFAULT '[]',
          is_active BOOLEAN DEFAULT TRUE,
          usage_count INTEGER DEFAULT 0,
          success_rate DECIMAL(5,2) DEFAULT 0.0,
          created_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          updated_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice Navigation Flows Table
        CREATE TABLE IF NOT EXISTS public.voice_navigation_flows (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          description TEXT,
          start_point TEXT NOT NULL,
          end_point TEXT NOT NULL,
          flow_steps JSONB NOT NULL,
          voice_prompts JSONB DEFAULT '{}',
          confirmation_required BOOLEAN DEFAULT FALSE,
          estimated_duration_seconds INTEGER,
          difficulty_level TEXT DEFAULT 'easy' CHECK (difficulty_level IN ('easy', 'medium', 'hard')),
          prerequisites TEXT[],
          success_criteria TEXT,
          fallback_flows TEXT[],
          is_active BOOLEAN DEFAULT TRUE,
          usage_count INTEGER DEFAULT 0,
          completion_rate DECIMAL(5,2) DEFAULT 0.0,
          average_duration_seconds INTEGER DEFAULT 0,
          user_feedback_score DECIMAL(3,2) DEFAULT 0.0,
          created_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          updated_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice Agent Knowledge Base Table
        CREATE TABLE IF NOT EXISTS public.voice_agent_knowledge (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          category TEXT NOT NULL,
          subcategory TEXT,
          topic TEXT NOT NULL,
          question_patterns TEXT[] NOT NULL,
          answer_text TEXT NOT NULL,
          answer_audio_url TEXT,
          related_actions JSONB DEFAULT '[]',
          navigation_hints TEXT[],
          context_requirements JSONB DEFAULT '{}',
          confidence_threshold DECIMAL(3,2) DEFAULT 0.7,
          response_variations TEXT[],
          follow_up_questions TEXT[],
          escalation_triggers TEXT[],
          is_active BOOLEAN DEFAULT TRUE,
          usage_count INTEGER DEFAULT 0,
          effectiveness_score DECIMAL(3,2) DEFAULT 0.0,
          last_updated_by UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          review_required BOOLEAN DEFAULT FALSE,
          review_date DATE,
          tags TEXT[],
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice User Preferences Table
        CREATE TABLE IF NOT EXISTS public.voice_user_preferences (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID UNIQUE REFERENCES public.profiles(user_id) ON DELETE CASCADE,
          preferred_language TEXT DEFAULT 'en-US',
          voice_speed DECIMAL(3,2) DEFAULT 1.0,
          voice_pitch DECIMAL(3,2) DEFAULT 1.0,
          voice_volume DECIMAL(3,2) DEFAULT 1.0,
          preferred_voice_gender TEXT DEFAULT 'neutral' CHECK (preferred_voice_gender IN ('male', 'female', 'neutral')),
          wake_word TEXT DEFAULT 'Hey Assistant',
          auto_listen BOOLEAN DEFAULT FALSE,
          voice_feedback_enabled BOOLEAN DEFAULT TRUE,
          visual_feedback_enabled BOOLEAN DEFAULT TRUE,
          confirmation_required BOOLEAN DEFAULT FALSE,
          privacy_mode BOOLEAN DEFAULT FALSE,
          accessibility_features JSONB DEFAULT '{}',
          custom_commands JSONB DEFAULT '{}',
          blocked_features TEXT[],
          notification_preferences JSONB DEFAULT '{}',
          session_timeout_minutes INTEGER DEFAULT 30,
          auto_save_conversations BOOLEAN DEFAULT TRUE,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Voice Analytics Table
        CREATE TABLE IF NOT EXISTS public.voice_analytics (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(user_id) ON DELETE SET NULL,
          session_id UUID REFERENCES public.voice_sessions(id) ON DELETE CASCADE,
          metric_type TEXT NOT NULL,
          metric_name TEXT NOT NULL,
          metric_value DECIMAL(10,2) NOT NULL,
          metric_unit TEXT,
          dimension_1 TEXT,
          dimension_2 TEXT,
          dimension_3 TEXT,
          context JSONB DEFAULT '{}',
          recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          metadata JSONB DEFAULT '{}'
        );

        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_voice_sessions_user_id ON public.voice_sessions(user_id);
        CREATE INDEX IF NOT EXISTS idx_voice_sessions_status ON public.voice_sessions(status);
        CREATE INDEX IF NOT EXISTS idx_voice_sessions_started_at ON public.voice_sessions(started_at);
        
        CREATE INDEX IF NOT EXISTS idx_voice_commands_session_id ON public.voice_commands(session_id);
        CREATE INDEX IF NOT EXISTS idx_voice_commands_user_id ON public.voice_commands(user_id);
        CREATE INDEX IF NOT EXISTS idx_voice_commands_intent ON public.voice_commands(intent);
        CREATE INDEX IF NOT EXISTS idx_voice_commands_command_type ON public.voice_commands(command_type);
        CREATE INDEX IF NOT EXISTS idx_voice_commands_status ON public.voice_commands(status);
        CREATE INDEX IF NOT EXISTS idx_voice_commands_created_at ON public.voice_commands(created_at);
        
        CREATE INDEX IF NOT EXISTS idx_voice_agent_interactions_session_id ON public.voice_agent_interactions(session_id);
        CREATE INDEX IF NOT EXISTS idx_voice_agent_interactions_user_id ON public.voice_agent_interactions(user_id);
        CREATE INDEX IF NOT EXISTS idx_voice_agent_interactions_type ON public.voice_agent_interactions(interaction_type);
        CREATE INDEX IF NOT EXISTS idx_voice_agent_interactions_created_at ON public.voice_agent_interactions(created_at);
        
        CREATE INDEX IF NOT EXISTS idx_voice_command_templates_category ON public.voice_command_templates(category);
        CREATE INDEX IF NOT EXISTS idx_voice_command_templates_intent ON public.voice_command_templates(intent);
        CREATE INDEX IF NOT EXISTS idx_voice_command_templates_active ON public.voice_command_templates(is_active);
        
        CREATE INDEX IF NOT EXISTS idx_voice_navigation_flows_active ON public.voice_navigation_flows(is_active);
        CREATE INDEX IF NOT EXISTS idx_voice_navigation_flows_start_point ON public.voice_navigation_flows(start_point);
        
        CREATE INDEX IF NOT EXISTS idx_voice_agent_knowledge_category ON public.voice_agent_knowledge(category);
        CREATE INDEX IF NOT EXISTS idx_voice_agent_knowledge_active ON public.voice_agent_knowledge(is_active);
        CREATE INDEX IF NOT EXISTS idx_voice_agent_knowledge_tags ON public.voice_agent_knowledge USING GIN(tags);
        
        CREATE INDEX IF NOT EXISTS idx_voice_user_preferences_user_id ON public.voice_user_preferences(user_id);
        
        CREATE INDEX IF NOT EXISTS idx_voice_analytics_user_id ON public.voice_analytics(user_id);
        CREATE INDEX IF NOT EXISTS idx_voice_analytics_session_id ON public.voice_analytics(session_id);
        CREATE INDEX IF NOT EXISTS idx_voice_analytics_metric_type ON public.voice_analytics(metric_type);
        CREATE INDEX IF NOT EXISTS idx_voice_analytics_recorded_at ON public.voice_analytics(recorded_at);
      `
    });

    if (tablesError) {
      throw new Error('Voice command tables creation failed: ' + tablesError.message);
    }

    logSuccess('Voice command database tables created successfully');

    // Enable RLS on all voice tables
    logStep('Enabling RLS on voice command tables...');
    
    const { error: rlsError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Enable RLS on all voice command tables
        ALTER TABLE public.voice_sessions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_commands ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_agent_interactions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_command_templates ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_navigation_flows ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_agent_knowledge ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_user_preferences ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.voice_analytics ENABLE ROW LEVEL SECURITY;

        -- Create RLS policies for voice sessions
        CREATE POLICY "voice_sessions_select_own" ON public.voice_sessions FOR SELECT USING (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_sessions_insert_own" ON public.voice_sessions FOR INSERT WITH CHECK (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_sessions_update_own" ON public.voice_sessions FOR UPDATE USING (auth.uid() = user_id OR auth.uid() IS NULL);

        -- Create RLS policies for voice commands
        CREATE POLICY "voice_commands_select_own" ON public.voice_commands FOR SELECT USING (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_commands_insert_own" ON public.voice_commands FOR INSERT WITH CHECK (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_commands_update_own" ON public.voice_commands FOR UPDATE USING (auth.uid() = user_id OR auth.uid() IS NULL);

        -- Create RLS policies for voice agent interactions
        CREATE POLICY "voice_agent_interactions_select_own" ON public.voice_agent_interactions FOR SELECT USING (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_agent_interactions_insert_own" ON public.voice_agent_interactions FOR INSERT WITH CHECK (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_agent_interactions_update_own" ON public.voice_agent_interactions FOR UPDATE USING (auth.uid() = user_id OR auth.uid() IS NULL);

        -- Create RLS policies for voice command templates (public read, admin write)
        CREATE POLICY "voice_command_templates_select_all" ON public.voice_command_templates FOR SELECT USING (true);
        CREATE POLICY "voice_command_templates_insert_admin" ON public.voice_command_templates FOR INSERT WITH CHECK (true);
        CREATE POLICY "voice_command_templates_update_admin" ON public.voice_command_templates FOR UPDATE USING (true);

        -- Create RLS policies for voice navigation flows (public read, admin write)
        CREATE POLICY "voice_navigation_flows_select_all" ON public.voice_navigation_flows FOR SELECT USING (true);
        CREATE POLICY "voice_navigation_flows_insert_admin" ON public.voice_navigation_flows FOR INSERT WITH CHECK (true);
        CREATE POLICY "voice_navigation_flows_update_admin" ON public.voice_navigation_flows FOR UPDATE USING (true);

        -- Create RLS policies for voice agent knowledge (public read, admin write)
        CREATE POLICY "voice_agent_knowledge_select_all" ON public.voice_agent_knowledge FOR SELECT USING (true);
        CREATE POLICY "voice_agent_knowledge_insert_admin" ON public.voice_agent_knowledge FOR INSERT WITH CHECK (true);
        CREATE POLICY "voice_agent_knowledge_update_admin" ON public.voice_agent_knowledge FOR UPDATE USING (true);

        -- Create RLS policies for voice user preferences
        CREATE POLICY "voice_user_preferences_select_own" ON public.voice_user_preferences FOR SELECT USING (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_user_preferences_insert_own" ON public.voice_user_preferences FOR INSERT WITH CHECK (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_user_preferences_update_own" ON public.voice_user_preferences FOR UPDATE USING (auth.uid() = user_id OR auth.uid() IS NULL);

        -- Create RLS policies for voice analytics
        CREATE POLICY "voice_analytics_select_own" ON public.voice_analytics FOR SELECT USING (auth.uid() = user_id OR auth.uid() IS NULL);
        CREATE POLICY "voice_analytics_insert_all" ON public.voice_analytics FOR INSERT WITH CHECK (true);

        -- Grant permissions
        GRANT ALL ON public.voice_sessions TO authenticated;
        GRANT ALL ON public.voice_sessions TO anon;
        GRANT ALL ON public.voice_commands TO authenticated;
        GRANT ALL ON public.voice_commands TO anon;
        GRANT ALL ON public.voice_agent_interactions TO authenticated;
        GRANT ALL ON public.voice_agent_interactions TO anon;
        GRANT ALL ON public.voice_command_templates TO authenticated;
        GRANT ALL ON public.voice_command_templates TO anon;
        GRANT ALL ON public.voice_navigation_flows TO authenticated;
        GRANT ALL ON public.voice_navigation_flows TO anon;
        GRANT ALL ON public.voice_agent_knowledge TO authenticated;
        GRANT ALL ON public.voice_agent_knowledge TO anon;
        GRANT ALL ON public.voice_user_preferences TO authenticated;
        GRANT ALL ON public.voice_user_preferences TO anon;
        GRANT ALL ON public.voice_analytics TO authenticated;
        GRANT ALL ON public.voice_analytics TO anon;
      `
    });

    if (rlsError) {
      console.warn('RLS setup warning:', rlsError.message);
    } else {
      logSuccess('RLS policies created successfully');
    }

    logSuccess('🎉 VOICE COMMAND DATABASE CREATION COMPLETED!');
    console.log('\n🚀 CREATED VOICE COMMAND TABLES:');
    console.log('✅ voice_sessions - Voice interaction sessions');
    console.log('✅ voice_commands - Individual voice commands and processing');
    console.log('✅ voice_agent_interactions - AI agent conversations');
    console.log('✅ voice_command_templates - Command patterns and responses');
    console.log('✅ voice_navigation_flows - Navigation workflows');
    console.log('✅ voice_agent_knowledge - Knowledge base for voice agent');
    console.log('✅ voice_user_preferences - User voice settings');
    console.log('✅ voice_analytics - Voice interaction analytics');
    console.log('\n🎯 Voice command database is ready for AI voice system!');
    
    return true;

  } catch (error) {
    logError(`Voice command database creation failed: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
}

// Run the creation
createVoiceCommandDatabase()
  .then((success) => {
    if (success) {
      console.log('\n🎉 SUCCESS: Voice command database created successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Voice command database creation encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
