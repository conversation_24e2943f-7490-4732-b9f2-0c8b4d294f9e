import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Split, Download } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const TextSplitter: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [delimiter, setDelimiter] = useState(',');
  const [splitMode, setSplitMode] = useState('delimiter');
  const [chunkSize, setChunkSize] = useState(100);
  const [removeEmpty, setRemoveEmpty] = useState(true);
  const [trimWhitespace, setTrimWhitespace] = useState(true);
  const [results, setResults] = useState<string[]>([]);

  const presetDelimiters = [
    { value: ',', label: 'Comma (,)' },
    { value: ';', label: 'Semicolon (;)' },
    { value: '|', label: 'Pipe (|)' },
    { value: '\t', label: 'Tab' },
    { value: '\n', label: 'New Line' },
    { value: ' ', label: 'Space' },
    { value: ':', label: 'Colon (:)' },
    { value: '-', label: 'Dash (-)' },
    { value: 'custom', label: 'Custom' }
  ];

  const performSplit = () => {
    if (!inputText.trim()) {
      setResults([]);
      return;
    }

    let splitResults: string[] = [];

    try {
      switch (splitMode) {
        case 'delimiter':
          if (delimiter === '\\n') {
            splitResults = inputText.split('\n');
          } else if (delimiter === '\\t') {
            splitResults = inputText.split('\t');
          } else {
            splitResults = inputText.split(delimiter);
          }
          break;

        case 'length':
          const size = Math.max(1, chunkSize);
          for (let i = 0; i < inputText.length; i += size) {
            splitResults.push(inputText.substring(i, i + size));
          }
          break;

        case 'words':
          const words = inputText.split(/\s+/);
          const wordsPerChunk = Math.max(1, chunkSize);
          for (let i = 0; i < words.length; i += wordsPerChunk) {
            splitResults.push(words.slice(i, i + wordsPerChunk).join(' '));
          }
          break;

        case 'lines':
          const lines = inputText.split('\n');
          const linesPerChunk = Math.max(1, chunkSize);
          for (let i = 0; i < lines.length; i += linesPerChunk) {
            splitResults.push(lines.slice(i, i + linesPerChunk).join('\n'));
          }
          break;

        case 'sentences':
          splitResults = inputText.split(/[.!?]+/).filter(s => s.trim());
          break;

        case 'paragraphs':
          splitResults = inputText.split(/\n\s*\n/).filter(p => p.trim());
          break;

        default:
          splitResults = [inputText];
      }

      // Apply post-processing
      if (trimWhitespace) {
        splitResults = splitResults.map(item => item.trim());
      }

      if (removeEmpty) {
        splitResults = splitResults.filter(item => item.length > 0);
      }

      setResults(splitResults);
    } catch (error) {
      toast.error('Error splitting text');
      setResults([]);
    }
  };

  useEffect(() => {
    performSplit();
  }, [inputText, delimiter, splitMode, chunkSize, removeEmpty, trimWhitespace]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const copyAllResults = async () => {
    try {
      const allText = results.join('\n---\n');
      await navigator.clipboard.writeText(allText);
      toast.success('All results copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy results');
    }
  };

  const downloadResults = () => {
    const content = results.map((item, index) => `Part ${index + 1}:\n${item}`).join('\n\n---\n\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'split-text-results.txt';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Results downloaded!');
  };

  const clearAll = () => {
    setInputText('');
    setResults([]);
  };

  const getStats = () => {
    const totalChars = results.reduce((sum, item) => sum + item.length, 0);
    const avgLength = results.length > 0 ? Math.round(totalChars / results.length) : 0;
    const minLength = results.length > 0 ? Math.min(...results.map(item => item.length)) : 0;
    const maxLength = results.length > 0 ? Math.max(...results.map(item => item.length)) : 0;

    return {
      count: results.length,
      totalChars,
      avgLength,
      minLength,
      maxLength
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-50 border-purple-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-purple-800">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Split className="h-6 w-6 text-purple-600" />
            </div>
            {title || 'Text Splitter'}
          </CardTitle>
          <p className="text-purple-600 text-sm">
            Split text by delimiter, length, words, lines, sentences, or paragraphs
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Split Mode Selection */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Split Mode:</label>
              <Select value={splitMode} onValueChange={setSplitMode}>
                <SelectTrigger className="border-2 border-purple-200 focus:border-purple-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="delimiter">By Delimiter</SelectItem>
                  <SelectItem value="length">By Character Length</SelectItem>
                  <SelectItem value="words">By Word Count</SelectItem>
                  <SelectItem value="lines">By Line Count</SelectItem>
                  <SelectItem value="sentences">By Sentences</SelectItem>
                  <SelectItem value="paragraphs">By Paragraphs</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {splitMode === 'delimiter' && (
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-700">Delimiter:</label>
                <Select value={delimiter} onValueChange={setDelimiter}>
                  <SelectTrigger className="border-2 border-purple-200 focus:border-purple-400">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {presetDelimiters.map(preset => (
                      <SelectItem key={preset.value} value={preset.value}>
                        {preset.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {delimiter === 'custom' && (
                  <Input
                    placeholder="Enter custom delimiter..."
                    onChange={(e) => setDelimiter(e.target.value)}
                    className="border-2 border-purple-200 focus:border-purple-400"
                  />
                )}
              </div>
            )}

            {(splitMode === 'length' || splitMode === 'words' || splitMode === 'lines') && (
              <div className="space-y-2">
                <label className="text-sm font-semibold text-gray-700">
                  {splitMode === 'length' ? 'Characters per chunk:' : 
                   splitMode === 'words' ? 'Words per chunk:' : 'Lines per chunk:'}
                </label>
                <Input
                  type="number"
                  min="1"
                  value={chunkSize}
                  onChange={(e) => setChunkSize(parseInt(e.target.value) || 1)}
                  className="border-2 border-purple-200 focus:border-purple-400"
                />
              </div>
            )}
          </div>

          {/* Options */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="remove-empty"
                checked={removeEmpty}
                onCheckedChange={setRemoveEmpty}
              />
              <label htmlFor="remove-empty" className="text-sm font-medium">
                Remove Empty Parts
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="trim-whitespace"
                checked={trimWhitespace}
                onCheckedChange={setTrimWhitespace}
              />
              <label htmlFor="trim-whitespace" className="text-sm font-medium">
                Trim Whitespace
              </label>
            </div>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <span>📝</span>
              Input Text:
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter your text to split..."
              className="min-h-[140px] resize-none border-2 border-purple-200 focus:border-purple-400"
            />
          </div>

          {/* Stats */}
          {results.length > 0 && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.count} parts
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                📏 Avg: {stats.avgLength} chars
              </Badge>
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                📐 Range: {stats.minLength}-{stats.maxLength}
              </Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                💾 Total: {stats.totalChars} chars
              </Badge>
            </div>
          )}

          {/* Results */}
          {results.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  Results ({results.length} parts):
                </label>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyAllResults}
                    className="flex items-center gap-2"
                  >
                    <Copy className="h-4 w-4" />
                    Copy All
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadResults}
                    className="flex items-center gap-2"
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                </div>
              </div>
              
              <div className="max-h-96 overflow-y-auto space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="p-3 bg-white rounded-lg border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-xs font-medium text-gray-500">
                        Part {index + 1} ({result.length} chars)
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(result)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                    <div className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded max-h-20 overflow-y-auto">
                      {result}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Clear Button */}
          <div className="flex justify-center">
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Split Modes Guide
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">By Delimiter:</strong> Split at specific characters</p>
              <p><strong className="text-green-600">By Length:</strong> Fixed character chunks</p>
              <p><strong className="text-purple-600">By Words:</strong> Fixed word count chunks</p>
            </div>
            <div className="space-y-2">
              <p><strong className="text-orange-600">By Lines:</strong> Group multiple lines</p>
              <p><strong className="text-red-600">By Sentences:</strong> Split at sentence endings</p>
              <p><strong className="text-indigo-600">By Paragraphs:</strong> Split at double line breaks</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextSplitter;
