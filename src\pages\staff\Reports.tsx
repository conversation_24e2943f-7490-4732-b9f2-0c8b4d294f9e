import { WeeklyReport } from '@/components/staff/reports/WeeklyReport'
import { ProjectReport } from '@/components/staff/reports/ProjectReport'
import { TelecomSiteReport } from '@/components/staff/reports/TelecomSiteReport'
import { ReportManagement } from '@/components/manager/ReportManagement'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/components/auth/AuthProvider'

const Reports = () => {
  const { userProfile } = useAuth()
  const isManagerOrAdmin = userProfile?.role === 'manager' || userProfile?.role === 'admin'

  return (
    <div className='space-y-6 p-4 md:p-6 max-w-7xl mx-auto animate-fade-in'>
      <Tabs defaultValue={isManagerOrAdmin ? 'manage' : 'submit'} className='space-y-6'>
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='submit'>Submit Reports</TabsTrigger>
          <TabsTrigger value='manage'>
            {isManagerOrAdmin ? 'Manage Reports' : 'My Reports'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value='submit' className='space-y-6'>
          <div className='grid gap-6'>
            <WeeklyReport />
            <ProjectReport />
            <TelecomSiteReport />
          </div>
        </TabsContent>

        <TabsContent value='manage' className='space-y-6'>
          <ReportManagement />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default Reports
