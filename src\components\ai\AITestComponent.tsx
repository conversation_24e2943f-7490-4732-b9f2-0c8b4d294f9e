import React, { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'pending';
  message: string;
}

export const AITestComponent: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  const runTests = async () => {
    setIsRunning(true);
    const testResults: TestResult[] = [];

    // Test 1: Check if ai_results table exists
    try {
      const { data, error } = await supabase.from('ai_results').select('count').limit(1);
      testResults.push({
        name: 'AI Results Table',
        status: error ? 'error' : 'success',
        message: error ? error.message : 'Table exists and accessible'
      });
    } catch (error) {
      testResults.push({
        name: 'AI Results Table',
        status: 'error',
        message: 'Table does not exist or is not accessible'
      });
    }

    // Test 2: Check if ai_interactions table exists
    try {
      const { data, error } = await supabase.from('ai_interactions').select('count').limit(1);
      testResults.push({
        name: 'AI Interactions Table',
        status: error ? 'error' : 'success',
        message: error ? error.message : 'Table exists and accessible'
      });
    } catch (error) {
      testResults.push({
        name: 'AI Interactions Table',
        status: 'error',
        message: 'Table does not exist or is not accessible'
      });
    }

    // Test 3: Check if ai_documents table exists
    try {
      const { data, error } = await supabase.from('ai_documents').select('count').limit(1);
      testResults.push({
        name: 'AI Documents Table',
        status: error ? 'error' : 'success',
        message: error ? error.message : 'Table exists and accessible'
      });
    } catch (error) {
      testResults.push({
        name: 'AI Documents Table',
        status: 'error',
        message: 'Table does not exist or is not accessible'
      });
    }

    // Test 4: Check if document_analysis table exists
    try {
      const { data, error } = await supabase.from('document_analysis').select('count').limit(1);
      testResults.push({
        name: 'Document Analysis Table',
        status: error ? 'error' : 'success',
        message: error ? error.message : 'Table exists and accessible'
      });
    } catch (error) {
      testResults.push({
        name: 'Document Analysis Table',
        status: 'error',
        message: 'Table does not exist or is not accessible'
      });
    }

    // Test 5: Check if document_ai_analysis table exists
    try {
      const { data, error } = await supabase.from('document_ai_analysis').select('count').limit(1);
      testResults.push({
        name: 'Document AI Analysis Table',
        status: error ? 'error' : 'success',
        message: error ? error.message : 'Table exists and accessible'
      });
    } catch (error) {
      testResults.push({
        name: 'Document AI Analysis Table',
        status: 'error',
        message: 'Table does not exist or is not accessible'
      });
    }

    // Test 6: Test inserting a sample AI result
    try {
      const { data, error } = await supabase
        .from('ai_results')
        .insert({
          query_text: 'Test query',
          result_data: { test: true },
          model_used: 'test-model'
        })
        .select()
        .single();

      if (error) throw error;

      // Clean up test data
      await supabase.from('ai_results').delete().eq('id', data.id);

      testResults.push({
        name: 'AI Results Insert/Delete',
        status: 'success',
        message: 'Successfully inserted and deleted test record'
      });
    } catch (error) {
      testResults.push({
        name: 'AI Results Insert/Delete',
        status: 'error',
        message: error instanceof Error ? error.message : 'Failed to insert test record'
      });
    }

    setTests(testResults);
    setIsRunning(false);

    const successCount = testResults.filter(t => t.status === 'success').length;
    const totalCount = testResults.length;

    toast({
      title: 'AI Module Tests Complete',
      description: `${successCount}/${totalCount} tests passed`,
      variant: successCount === totalCount ? 'default' : 'destructive'
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5" />
          AI Module Database Tests
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={runTests} 
          disabled={isRunning}
          className="w-full"
        >
          {isRunning ? 'Running Tests...' : 'Run Database Tests'}
        </Button>

        {tests.length > 0 && (
          <div className="space-y-3">
            {tests.map((test, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <div className="font-medium">{test.name}</div>
                    <div className="text-sm text-muted-foreground">{test.message}</div>
                  </div>
                </div>
                {getStatusBadge(test.status)}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
