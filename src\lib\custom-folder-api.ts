import { supabase } from '@/integrations/supabase/client';

export interface CustomFolderData {
  id?: string;
  name: string;
  description?: string;
  parent_folder_id?: string;
  folder_type?: 'custom' | 'system' | 'department' | 'project';
  access_level?: 'private' | 'department' | 'public';
  created_by?: string;
  department_id?: string;
  shared_with?: string[];
  color?: string;
  icon?: string;
  is_archived?: boolean;
  is_system?: boolean;
}

export interface FolderPermission {
  id?: string;
  folder_id: string;
  user_id: string;
  permission_type: 'read' | 'write' | 'admin';
  granted_by: string;
}

export class CustomFolderAPI {
  /**
   * Get all folders accessible to the current user
   */
  static async getFolders(): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('custom_folders')
        .select(`
          *,
          creator:profiles!created_by(id, full_name, email),
          department:departments!department_id(id, name),
          document_count:document_archive(count)
        `)
        .eq('is_archived', false)
        .order('name');

      if (error) throw error;

      // Transform data to include computed fields
      const folders = data?.map(folder => ({
        ...folder,
        creator_name: folder.creator?.full_name,
        department_name: folder.department?.name,
        document_count: folder.document_count?.[0]?.count || 0
      })) || [];

      return { data: folders, error: null };
    } catch (error: any) {
      console.error('Error fetching folders:', error);
      return { data: [], error };
    }
  }

  /**
   * Create a new folder
   */
  static async createFolder(folderData: CustomFolderData): Promise<{ data: any; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('custom_folders')
        .insert({
          ...folderData,
          created_by: user.id,
          department_id: folderData.department_id || null
        })
        .select(`
          *,
          creator:profiles!created_by(id, full_name, email)
        `)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error creating folder:', error);
      return { data: null, error };
    }
  }

  /**
   * Update an existing folder
   */
  static async updateFolder(folderId: string, updates: Partial<CustomFolderData>): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('custom_folders')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', folderId)
        .select(`
          *,
          creator:profiles!created_by(id, full_name, email)
        `)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error updating folder:', error);
      return { data: null, error };
    }
  }

  /**
   * Delete a folder
   */
  static async deleteFolder(folderId: string): Promise<{ success: boolean; error: any }> {
    try {
      // Check if folder has documents
      const { data: documents, error: docError } = await supabase
        .from('document_archive')
        .select('id')
        .eq('folder_id', folderId)
        .limit(1);

      if (docError) throw docError;

      if (documents && documents.length > 0) {
        throw new Error('Cannot delete folder that contains documents. Please move or delete documents first.');
      }

      // Check if folder has subfolders
      const { data: subfolders, error: subError } = await supabase
        .from('custom_folders')
        .select('id')
        .eq('parent_folder_id', folderId)
        .limit(1);

      if (subError) throw subError;

      if (subfolders && subfolders.length > 0) {
        throw new Error('Cannot delete folder that contains subfolders. Please delete subfolders first.');
      }

      const { error } = await supabase
        .from('custom_folders')
        .delete()
        .eq('id', folderId);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error deleting folder:', error);
      return { success: false, error };
    }
  }

  /**
   * Share a folder with users
   */
  static async shareFolder(folderId: string, userIds: string[]): Promise<{ success: boolean; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Update the shared_with array
      const { data, error } = await supabase
        .from('custom_folders')
        .update({
          shared_with: userIds,
          updated_at: new Date().toISOString()
        })
        .eq('id', folderId)
        .select()
        .single();

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error sharing folder:', error);
      return { success: false, error };
    }
  }

  /**
   * Grant specific permissions to a user for a folder
   */
  static async grantPermission(permission: FolderPermission): Promise<{ data: any; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('folder_permissions')
        .upsert({
          ...permission,
          granted_by: user.id
        })
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      console.error('Error granting permission:', error);
      return { data: null, error };
    }
  }

  /**
   * Get folder permissions
   */
  static async getFolderPermissions(folderId: string): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('folder_permissions')
        .select(`
          *,
          user:profiles!user_id(id, full_name, email),
          granted_by_user:profiles!granted_by(id, full_name, email)
        `)
        .eq('folder_id', folderId);

      if (error) throw error;

      return { data: data || [], error: null };
    } catch (error: any) {
      console.error('Error fetching folder permissions:', error);
      return { data: [], error };
    }
  }

  /**
   * Remove permission
   */
  static async removePermission(permissionId: string): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await supabase
        .from('folder_permissions')
        .delete()
        .eq('id', permissionId);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error removing permission:', error);
      return { success: false, error };
    }
  }

  /**
   * Get documents in a folder
   */
  static async getFolderDocuments(folderId: string): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('document_archive')
        .select(`
          *,
          uploader:profiles!uploaded_by(id, full_name, email)
        `)
        .eq('folder_id', folderId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { data: data || [], error: null };
    } catch (error: any) {
      console.error('Error fetching folder documents:', error);
      return { data: [], error };
    }
  }

  /**
   * Move document to folder
   */
  static async moveDocumentToFolder(documentId: string, folderId: string | null): Promise<{ success: boolean; error: any }> {
    try {
      const { error } = await supabase
        .from('document_archive')
        .update({
          folder_id: folderId,
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId);

      if (error) throw error;

      return { success: true, error: null };
    } catch (error: any) {
      console.error('Error moving document:', error);
      return { success: false, error };
    }
  }

  /**
   * Get folder hierarchy (breadcrumb)
   */
  static async getFolderHierarchy(folderId: string): Promise<{ data: any[]; error: any }> {
    try {
      const hierarchy = [];
      let currentId = folderId;

      while (currentId) {
        const { data, error } = await supabase
          .from('custom_folders')
          .select('id, name, parent_folder_id')
          .eq('id', currentId)
          .single();

        if (error) throw error;

        hierarchy.unshift(data);
        currentId = data.parent_folder_id;
      }

      return { data: hierarchy, error: null };
    } catch (error: any) {
      console.error('Error fetching folder hierarchy:', error);
      return { data: [], error };
    }
  }

  /**
   * Search folders
   */
  static async searchFolders(query: string): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('custom_folders')
        .select(`
          *,
          creator:profiles!created_by(id, full_name, email)
        `)
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .eq('is_archived', false)
        .order('name');

      if (error) throw error;

      return { data: data || [], error: null };
    } catch (error: any) {
      console.error('Error searching folders:', error);
      return { data: [], error };
    }
  }
}
