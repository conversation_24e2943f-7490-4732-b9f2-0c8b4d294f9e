import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Folder, 
  FolderPlus, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Share2, 
  Lock, 
  Globe, 
  Users,
  ChevronRight,
  ChevronDown,
  FileText,
  Settings
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';

interface CustomFolder {
  id: string;
  name: string;
  description?: string;
  parent_folder_id?: string;
  folder_type: 'custom' | 'system' | 'department' | 'project';
  access_level: 'private' | 'department' | 'public';
  created_by: string;
  department_id?: string;
  shared_with: string[];
  color: string;
  icon: string;
  is_archived: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  document_count?: number;
  creator_name?: string;
}

interface FolderFormData {
  name: string;
  description: string;
  access_level: 'private' | 'department' | 'public';
  color: string;
  icon: string;
  parent_folder_id?: string;
}

const FOLDER_COLORS = [
  { value: '#3B82F6', label: 'Blue' },
  { value: '#10B981', label: 'Green' },
  { value: '#F59E0B', label: 'Yellow' },
  { value: '#EF4444', label: 'Red' },
  { value: '#8B5CF6', label: 'Purple' },
  { value: '#06B6D4', label: 'Cyan' },
  { value: '#84CC16', label: 'Lime' },
  { value: '#F97316', label: 'Orange' },
];

const FOLDER_ICONS = [
  { value: 'folder', label: 'Folder' },
  { value: 'user', label: 'Personal' },
  { value: 'users', label: 'Team' },
  { value: 'briefcase', label: 'Work' },
  { value: 'archive', label: 'Archive' },
  { value: 'star', label: 'Important' },
  { value: 'shield', label: 'Secure' },
  { value: 'template', label: 'Template' },
];

export const CustomFolderManagement = () => {
  const { userProfile } = useSupabaseAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingFolder, setEditingFolder] = useState<CustomFolder | null>(null);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [formData, setFormData] = useState<FolderFormData>({
    name: '',
    description: '',
    access_level: 'private',
    color: '#3B82F6',
    icon: 'folder',
    parent_folder_id: undefined
  });

  // Fetch folders
  const { data: folders = [], isLoading, error } = useQuery({
    queryKey: ['custom-folders', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];

      const { data, error } = await supabase
        .from('custom_folders')
        .select(`
          *,
          creator:profiles!created_by(id, full_name),
          document_count:document_archive(count)
        `)
        .order('name');

      if (error) {
        console.error('Error fetching folders:', error);
        throw error;
      }

      return data?.map(folder => ({
        ...folder,
        creator_name: folder.creator?.full_name,
        document_count: folder.document_count?.[0]?.count || 0
      })) || [];
    },
    enabled: !!userProfile?.id
  });

  // Create folder mutation
  const createFolderMutation = useMutation({
    mutationFn: async (folderData: FolderFormData) => {
      const { data, error } = await supabase
        .from('custom_folders')
        .insert({
          ...folderData,
          created_by: userProfile?.id,
          department_id: userProfile?.department_id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['custom-folders'] });
      setIsCreateDialogOpen(false);
      resetForm();
      toast({
        title: 'Success',
        description: 'Folder created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create folder',
        variant: 'destructive',
      });
    }
  });

  // Update folder mutation
  const updateFolderMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<FolderFormData> }) => {
      const { data, error } = await supabase
        .from('custom_folders')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['custom-folders'] });
      setEditingFolder(null);
      resetForm();
      toast({
        title: 'Success',
        description: 'Folder updated successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update folder',
        variant: 'destructive',
      });
    }
  });

  // Delete folder mutation
  const deleteFolderMutation = useMutation({
    mutationFn: async (folderId: string) => {
      const { error } = await supabase
        .from('custom_folders')
        .delete()
        .eq('id', folderId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['custom-folders'] });
      toast({
        title: 'Success',
        description: 'Folder deleted successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete folder',
        variant: 'destructive',
      });
    }
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      access_level: 'private',
      color: '#3B82F6',
      icon: 'folder',
      parent_folder_id: undefined
    });
  };

  const handleCreateFolder = () => {
    if (!formData.name.trim()) {
      toast({
        title: 'Error',
        description: 'Folder name is required',
        variant: 'destructive',
      });
      return;
    }
    createFolderMutation.mutate(formData);
  };

  const handleUpdateFolder = () => {
    if (!editingFolder || !formData.name.trim()) return;
    updateFolderMutation.mutate({
      id: editingFolder.id,
      updates: formData
    });
  };

  const handleDeleteFolder = (folder: CustomFolder) => {
    if (folder.is_system) {
      toast({
        title: 'Error',
        description: 'System folders cannot be deleted',
        variant: 'destructive',
      });
      return;
    }

    if (confirm(`Are you sure you want to delete "${folder.name}"? This action cannot be undone.`)) {
      deleteFolderMutation.mutate(folder.id);
    }
  };

  const startEdit = (folder: CustomFolder) => {
    setEditingFolder(folder);
    setFormData({
      name: folder.name,
      description: folder.description || '',
      access_level: folder.access_level,
      color: folder.color,
      icon: folder.icon,
      parent_folder_id: folder.parent_folder_id
    });
  };

  const toggleExpanded = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const getAccessIcon = (accessLevel: string) => {
    switch (accessLevel) {
      case 'private': return <Lock className="h-4 w-4" />;
      case 'department': return <Users className="h-4 w-4" />;
      case 'public': return <Globe className="h-4 w-4" />;
      default: return <Lock className="h-4 w-4" />;
    }
  };

  const getAccessBadgeColor = (accessLevel: string) => {
    switch (accessLevel) {
      case 'private': return 'bg-red-100 text-red-800';
      case 'department': return 'bg-blue-100 text-blue-800';
      case 'public': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Organize folders into hierarchy
  const rootFolders = folders.filter(f => !f.parent_folder_id);
  const getSubfolders = (parentId: string) => folders.filter(f => f.parent_folder_id === parentId);

  const renderFolder = (folder: CustomFolder, level = 0) => {
    const subfolders = getSubfolders(folder.id);
    const hasSubfolders = subfolders.length > 0;
    const isExpanded = expandedFolders.has(folder.id);

    return (
      <div key={folder.id} className="space-y-2">
        <div 
          className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
          style={{ marginLeft: `${level * 20}px` }}
        >
          <div className="flex items-center gap-3 flex-1">
            {hasSubfolders && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpanded(folder.id)}
                className="p-1 h-6 w-6"
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            )}
            
            <div 
              className="p-2 rounded"
              style={{ backgroundColor: folder.color + '20' }}
            >
              <Folder className="h-5 w-5" style={{ color: folder.color }} />
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">{folder.name}</h4>
                {folder.is_system && (
                  <Badge variant="outline" className="text-xs">System</Badge>
                )}
                <Badge className={`text-xs ${getAccessBadgeColor(folder.access_level)}`}>
                  <span className="flex items-center gap-1">
                    {getAccessIcon(folder.access_level)}
                    {folder.access_level}
                  </span>
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {folder.document_count || 0} documents
                {folder.description && ` • ${folder.description}`}
              </p>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => startEdit(folder)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => handleDeleteFolder(folder)}
                disabled={folder.is_system}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {hasSubfolders && isExpanded && (
          <div className="space-y-2">
            {subfolders.map(subfolder => renderFolder(subfolder, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            My Folders
          </CardTitle>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => { resetForm(); setEditingFolder(null); }}>
                <FolderPlus className="h-4 w-4 mr-2" />
                Create Folder
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingFolder ? 'Edit Folder' : 'Create New Folder'}
                </DialogTitle>
                <DialogDescription>
                  {editingFolder 
                    ? 'Update folder details and settings'
                    : 'Create a new folder to organize your documents'
                  }
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Folder Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter folder name"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Enter folder description"
                    rows={2}
                  />
                </div>

                <div>
                  <Label htmlFor="access_level">Access Level</Label>
                  <Select
                    value={formData.access_level}
                    onValueChange={(value: any) => setFormData(prev => ({ ...prev, access_level: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="private">
                        <span className="flex items-center gap-2">
                          <Lock className="h-4 w-4" />
                          Private - Only you
                        </span>
                      </SelectItem>
                      <SelectItem value="department">
                        <span className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Department - Your department
                        </span>
                      </SelectItem>
                      <SelectItem value="public">
                        <span className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Public - Everyone
                        </span>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="color">Color</Label>
                    <Select
                      value={formData.color}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, color: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {FOLDER_COLORS.map(color => (
                          <SelectItem key={color.value} value={color.value}>
                            <span className="flex items-center gap-2">
                              <div 
                                className="w-4 h-4 rounded"
                                style={{ backgroundColor: color.value }}
                              />
                              {color.label}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="icon">Icon</Label>
                    <Select
                      value={formData.icon}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, icon: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {FOLDER_ICONS.map(icon => (
                          <SelectItem key={icon.value} value={icon.value}>
                            {icon.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex justify-end gap-2 pt-4">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setIsCreateDialogOpen(false);
                      setEditingFolder(null);
                      resetForm();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={editingFolder ? handleUpdateFolder : handleCreateFolder}
                    disabled={createFolderMutation.isPending || updateFolderMutation.isPending}
                  >
                    {editingFolder ? 'Update' : 'Create'} Folder
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </CardHeader>

        <CardContent>
          {error && (
            <div className="text-center py-8">
              <p className="text-destructive">Error loading folders: {error.message}</p>
            </div>
          )}

          {!error && rootFolders.length === 0 && (
            <div className="text-center py-12">
              <Folder className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-medium mb-2">No Folders Yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first folder to organize your documents
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <FolderPlus className="h-4 w-4 mr-2" />
                Create Your First Folder
              </Button>
            </div>
          )}

          {!error && rootFolders.length > 0 && (
            <div className="space-y-2">
              {rootFolders.map(folder => renderFolder(folder))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={!!editingFolder} onOpenChange={(open) => !open && setEditingFolder(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Folder</DialogTitle>
            <DialogDescription>
              Update folder details and settings
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Folder Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter folder name"
              />
            </div>

            <div>
              <Label htmlFor="edit-description">Description (Optional)</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter folder description"
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="edit-access">Access Level</Label>
              <Select
                value={formData.access_level}
                onValueChange={(value: any) => setFormData(prev => ({ ...prev, access_level: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="private">
                    <span className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      Private - Only you
                    </span>
                  </SelectItem>
                  <SelectItem value="department">
                    <span className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Department - Your department
                    </span>
                  </SelectItem>
                  <SelectItem value="public">
                    <span className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Public - Everyone
                    </span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-color">Color</Label>
                <Select
                  value={formData.color}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, color: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FOLDER_COLORS.map(color => (
                      <SelectItem key={color.value} value={color.value}>
                        <span className="flex items-center gap-2">
                          <div 
                            className="w-4 h-4 rounded"
                            style={{ backgroundColor: color.value }}
                          />
                          {color.label}
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="edit-icon">Icon</Label>
                <Select
                  value={formData.icon}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, icon: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {FOLDER_ICONS.map(icon => (
                      <SelectItem key={icon.value} value={icon.value}>
                        {icon.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button 
                variant="outline" 
                onClick={() => setEditingFolder(null)}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleUpdateFolder}
                disabled={updateFolderMutation.isPending}
              >
                Update Folder
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
