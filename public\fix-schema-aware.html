<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schema-Aware Dashboard Fix</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .log {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.5s ease;
            border-radius: 4px;
        }
        .center {
            text-align: center;
        }
        .schema-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Schema-Aware Dashboard Fix</h1>
            <p>Adapts to your actual table structure and inserts compatible data</p>
        </div>
        
        <div class="warning">
            <strong>🔍 Analysis Complete:</strong> Tables exist but have different column structures. This tool will adapt to your schema.
        </div>
        
        <div id="schemaInfo" class="schema-info" style="display: none;">
            <h4>📋 Detected Schema Issues:</h4>
            <ul id="schemaIssues"></ul>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
        </div>
        
        <div id="status"></div>
        
        <div class="center">
            <button id="analyzeBtn" class="button" onclick="analyzeSchema()">
                🔍 Analyze Table Schema
            </button>
            
            <button id="fixBtn" class="button" onclick="runSchemaAwareFix()" disabled>
                🚀 Fix with Correct Schema
            </button>
            
            <button id="dashboardBtn" class="button" onclick="openDashboard()" disabled>
                📊 Open Dashboard
            </button>
        </div>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        let tableSchemas = {};
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        function updateProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percent + '%';
        }
        
        async function analyzeSchema() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const fixBtn = document.getElementById('fixBtn');
            
            analyzeBtn.disabled = true;
            analyzeBtn.textContent = '🔄 Analyzing...';
            
            try {
                log('🔍 Analyzing table schemas...');
                updateProgress(10);
                
                const tables = ['invoices', 'expense_reports', 'reports', 'time_logs', 'notifications'];
                const schemaIssues = [];
                
                for (const table of tables) {
                    try {
                        log(`Analyzing ${table} schema...`);
                        
                        // Try to get one record to understand the schema
                        const { data, error } = await supabase
                            .from(table)
                            .select('*')
                            .limit(1);
                        
                        if (error) {
                            log(`❌ ${table}: ${error.message}`);
                            schemaIssues.push(`${table}: ${error.message}`);
                        } else {
                            // Store the schema for this table
                            if (data && data.length > 0) {
                                tableSchemas[table] = Object.keys(data[0]);
                                log(`✅ ${table}: Found columns: ${Object.keys(data[0]).join(', ')}`);
                            } else {
                                // Try to insert a minimal record to discover required fields
                                const testData = getMinimalTestData(table);
                                const { error: insertError } = await supabase
                                    .from(table)
                                    .insert(testData)
                                    .select();
                                
                                if (insertError) {
                                    log(`ℹ️ ${table}: ${insertError.message}`);
                                    schemaIssues.push(`${table}: ${insertError.message}`);
                                } else {
                                    log(`✅ ${table}: Test insert successful`);
                                }
                            }
                        }
                    } catch (e) {
                        log(`❌ ${table}: ${e.message}`);
                        schemaIssues.push(`${table}: ${e.message}`);
                    }
                }
                
                updateProgress(100);
                
                // Display schema issues
                if (schemaIssues.length > 0) {
                    const schemaInfo = document.getElementById('schemaInfo');
                    const issuesList = document.getElementById('schemaIssues');
                    
                    issuesList.innerHTML = '';
                    schemaIssues.forEach(issue => {
                        const li = document.createElement('li');
                        li.textContent = issue;
                        issuesList.appendChild(li);
                    });
                    
                    schemaInfo.style.display = 'block';
                }
                
                log('✅ Schema analysis completed');
                showStatus('✅ Schema analysis completed. Ready to insert compatible data.', 'success');
                
                fixBtn.disabled = false;
                
            } catch (error) {
                log('❌ Schema analysis failed: ' + error.message);
                showStatus('❌ Schema analysis failed: ' + error.message, 'error');
            } finally {
                analyzeBtn.disabled = false;
                analyzeBtn.textContent = '🔍 Analyze Table Schema';
            }
        }
        
        function getMinimalTestData(table) {
            const { data: { user } } = supabase.auth.getUser();
            const userId = user?.id || '029b8df4-89fa-4c39-af15-f3b1d842f337';
            
            switch (table) {
                case 'invoices':
                    return {
                        invoice_number: 'TEST-' + Date.now(),
                        client_name: 'Test Client',
                        created_by: userId
                    };
                case 'expense_reports':
                    return {
                        title: 'Test Expense',
                        category: 'test',
                        submitted_by: userId,
                        expense_date: new Date().toISOString().split('T')[0]
                    };
                case 'reports':
                    return {
                        title: 'Test Report',
                        submitted_by: userId
                    };
                case 'time_logs':
                    return {
                        user_id: userId,
                        hours_worked: 1.0
                    };
                case 'notifications':
                    return {
                        user_id: userId,
                        title: 'Test Notification',
                        message: 'Test message'
                    };
                default:
                    return {};
            }
        }
        
        async function runSchemaAwareFix() {
            const fixBtn = document.getElementById('fixBtn');
            const dashboardBtn = document.getElementById('dashboardBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Fixing...';
            
            try {
                log('🚀 Starting schema-aware fix...');
                updateProgress(10);
                
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('User not authenticated');
                
                const userId = user.id;
                log('✅ User authenticated: ' + user.email);
                updateProgress(20);
                
                // Insert data based on actual schema
                let totalInserted = 0;
                
                // Try invoices with minimal data
                try {
                    const invoiceData = [
                        {
                            invoice_number: 'INV-' + Date.now() + '-1',
                            client_name: 'Acme Corporation',
                            created_by: userId
                        },
                        {
                            invoice_number: 'INV-' + Date.now() + '-2',
                            client_name: 'Tech Solutions Ltd',
                            created_by: userId
                        }
                    ];
                    
                    const { data: invoiceResult, error: invoiceError } = await supabase
                        .from('invoices')
                        .insert(invoiceData)
                        .select();
                    
                    if (invoiceError) {
                        log(`⚠️ Invoices: ${invoiceError.message}`);
                    } else {
                        log(`✅ Inserted ${invoiceResult?.length || 0} invoices`);
                        totalInserted += invoiceResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Invoices: ${e.message}`);
                }
                
                updateProgress(40);
                
                // Try expense reports with required fields
                try {
                    const expenseData = [
                        {
                            title: 'Office Supplies',
                            category: 'office',
                            submitted_by: userId,
                            expense_date: new Date().toISOString().split('T')[0]
                        },
                        {
                            title: 'Travel Expenses',
                            category: 'travel',
                            submitted_by: userId,
                            expense_date: new Date().toISOString().split('T')[0]
                        }
                    ];
                    
                    const { data: expenseResult, error: expenseError } = await supabase
                        .from('expense_reports')
                        .insert(expenseData)
                        .select();
                    
                    if (expenseError) {
                        log(`⚠️ Expense reports: ${expenseError.message}`);
                    } else {
                        log(`✅ Inserted ${expenseResult?.length || 0} expense reports`);
                        totalInserted += expenseResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Expense reports: ${e.message}`);
                }
                
                updateProgress(60);
                
                // Try reports with minimal data
                try {
                    const reportData = [
                        {
                            title: 'Monthly Performance Report',
                            submitted_by: userId
                        },
                        {
                            title: 'Project Status Update',
                            submitted_by: userId
                        }
                    ];
                    
                    const { data: reportResult, error: reportError } = await supabase
                        .from('reports')
                        .insert(reportData)
                        .select();
                    
                    if (reportError) {
                        log(`⚠️ Reports: ${reportError.message}`);
                    } else {
                        log(`✅ Inserted ${reportResult?.length || 0} reports`);
                        totalInserted += reportResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Reports: ${e.message}`);
                }
                
                updateProgress(80);
                
                // Try time logs with minimal data
                try {
                    const timeLogData = [
                        {
                            user_id: userId,
                            hours_worked: 8.0
                        },
                        {
                            user_id: userId,
                            hours_worked: 6.5
                        }
                    ];
                    
                    const { data: timeLogResult, error: timeLogError } = await supabase
                        .from('time_logs')
                        .insert(timeLogData)
                        .select();
                    
                    if (timeLogError) {
                        log(`⚠️ Time logs: ${timeLogError.message}`);
                    } else {
                        log(`✅ Inserted ${timeLogResult?.length || 0} time logs`);
                        totalInserted += timeLogResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Time logs: ${e.message}`);
                }
                
                // Try notifications with minimal data
                try {
                    const notificationData = [
                        {
                            user_id: userId,
                            title: 'Welcome to Dashboard',
                            message: 'Your dashboard is ready!'
                        },
                        {
                            user_id: userId,
                            title: 'Data Updated',
                            message: 'Dashboard data has been updated'
                        }
                    ];
                    
                    const { data: notificationResult, error: notificationError } = await supabase
                        .from('notifications')
                        .insert(notificationData)
                        .select();
                    
                    if (notificationError) {
                        log(`⚠️ Notifications: ${notificationError.message}`);
                    } else {
                        log(`✅ Inserted ${notificationResult?.length || 0} notifications`);
                        totalInserted += notificationResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Notifications: ${e.message}`);
                }
                
                updateProgress(100);
                
                log(`🎉 Schema-aware fix completed! Total records inserted: ${totalInserted}`);
                
                if (totalInserted > 0) {
                    showStatus(`🎉 Successfully inserted ${totalInserted} records! Dashboard should now show data.`, 'success');
                } else {
                    showStatus('⚠️ No new records inserted, but existing data should be accessible.', 'warning');
                }
                
                dashboardBtn.disabled = false;
                
            } catch (error) {
                log('❌ Schema-aware fix failed: ' + error.message);
                showStatus('❌ Fix failed: ' + error.message, 'error');
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🚀 Fix with Correct Schema';
            }
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Make functions global
        window.analyzeSchema = analyzeSchema;
        window.runSchemaAwareFix = runSchemaAwareFix;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
