import { useState } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Users,
  Building,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { useDepartmentSync } from '@/hooks/useDepartmentSync'
import { supabase } from '@/integrations/supabase/client'
import { useToast } from '@/hooks/use-toast'

export const DepartmentSyncPanel = () => {
  const { toast } = useToast()
  const {
    departments,
    profiles,
    loading,
    syncing,
    syncAllUserDepartments,
    refreshDepartmentCounts
  } = useDepartmentSync()

  const unassignedUsers = profiles.filter(profile => !profile.department_id)
  const assignedUsers = profiles.filter(profile => profile.department_id)

  const getDepartmentStats = () => {
    return departments.map(dept => ({
      ...dept,
      actualCount: profiles.filter(p => p.department_id === dept.id).length
    }))
  }

  const departmentStats = getDepartmentStats()
  const totalUsers = profiles.length
  const assignmentPercentage = totalUsers > 0 ? Math.round((assignedUsers.length / totalUsers) * 100) : 0

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Department Synchronization</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex items-center justify-center h-32'>
            <Loader2 className='h-8 w-8 animate-spin' />
            <span className='ml-2'>Loading department data...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className='space-y-6'>
      {/* Overview Cards */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-4'>
        <Card>
          <CardContent className='pt-6'>
            <div className='flex items-center justify-between'>
              <p className='text-sm font-medium'>Total Users</p>
              <Users className='h-4 w-4 text-primary' />
            </div>
            <p className='text-2xl font-bold'>{totalUsers}</p>
            <p className='text-xs text-muted-foreground'>Registered users</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='pt-6'>
            <div className='flex items-center justify-between'>
              <p className='text-sm font-medium'>Assigned Users</p>
              <CheckCircle className='h-4 w-4 text-green-600' />
            </div>
            <p className='text-2xl font-bold'>{assignedUsers.length}</p>
            <p className='text-xs text-muted-foreground'>{assignmentPercentage}% assigned</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='pt-6'>
            <div className='flex items-center justify-between'>
              <p className='text-sm font-medium'>Unassigned Users</p>
              <AlertCircle className='h-4 w-4 text-orange-600' />
            </div>
            <p className='text-2xl font-bold'>{unassignedUsers.length}</p>
            <p className='text-xs text-muted-foreground'>Need assignment</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className='pt-6'>
            <div className='flex items-center justify-between'>
              <p className='text-sm font-medium'>Departments</p>
              <Building className='h-4 w-4 text-blue-600' />
            </div>
            <p className='text-2xl font-bold'>{departments.length}</p>
            <p className='text-xs text-muted-foreground'>Active departments</p>
          </CardContent>
        </Card>
      </div>

      {/* Assignment Progress */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <RefreshCw className='h-5 w-5' />
            Department Assignment Progress
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='space-y-2'>
            <div className='flex justify-between text-sm'>
              <span>Users Assigned</span>
              <span>{assignedUsers.length} of {totalUsers}</span>
            </div>
            <Progress value={assignmentPercentage} className='h-2' />
          </div>

          <div className='flex gap-2'>
            <Button
              onClick={syncAllUserDepartments}
              disabled={syncing || unassignedUsers.length === 0}
              className='flex items-center gap-2'
            >
              {syncing
                ? (
                  <Loader2 className='h-4 w-4 animate-spin' />
                  )
                : (
                  <RefreshCw className='h-4 w-4' />
                  )}
              Sync All Users
            </Button>

            <Button
              variant='outline'
              onClick={refreshDepartmentCounts}
              disabled={syncing}
              className='flex items-center gap-2'
            >
              <RefreshCw className='h-4 w-4' />
              Refresh Counts
            </Button>

            <Button
              variant='outline'
              onClick={async () => {
                try {
                  // Fix all department counts
                  for (const dept of departmentStats) {
                    if (dept.employee_count !== dept.actualCount) {
                      await supabase
                        .from('departments')
                        .update({ employee_count: dept.actualCount })
                        .eq('id', dept.id)
                    }
                  }

                  toast({
                    title: 'Success',
                    description: 'Fixed all department counts'
                  })

                  // Refresh the data
                  await refreshDepartmentCounts()
                } catch (error) {
                  toast({
                    title: 'Error',
                    description: 'Failed to fix counts',
                    variant: 'destructive'
                  })
                }
              }}
              disabled={syncing}
              className='flex items-center gap-2'
            >
              <CheckCircle className='h-4 w-4' />
              Fix All Counts
            </Button>
          </div>

          {unassignedUsers.length > 0 && (
            <div className='p-4 bg-orange-50 dark:bg-orange-950 rounded-lg'>
              <p className='text-sm font-medium text-orange-800 dark:text-orange-200'>
                {unassignedUsers.length} users need department assignment
              </p>
              <p className='text-xs text-orange-600 dark:text-orange-300 mt-1'>
                Click "Sync All Users" to automatically assign departments based on user roles
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Department Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Department Statistics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
            {departmentStats.map((dept) => (
              <div key={dept.id} className='p-4 border rounded-lg'>
                <div className='flex justify-between items-start mb-2'>
                  <h3 className='font-medium'>{dept.name}</h3>
                  <Badge variant='outline'>
                    {dept.actualCount} users
                  </Badge>
                </div>
                <p className='text-sm text-muted-foreground mb-2'>
                  {dept.description}
                </p>
                {dept.employee_count !== dept.actualCount && (
                  <div className='space-y-2'>
                    <p className='text-xs text-orange-600'>
                      Count mismatch: DB shows {dept.employee_count}, actual is {dept.actualCount}
                    </p>
                    <Button
                      size='sm'
                      variant='outline'
                      onClick={async () => {
                        try {
                          await supabase
                            .from('departments')
                            .update({ employee_count: dept.actualCount })
                            .eq('id', dept.id)

                          toast({
                            title: 'Success',
                            description: `Fixed count for ${dept.name}`
                          })

                          // Refresh the data
                          window.location.reload()
                        } catch (error) {
                          toast({
                            title: 'Error',
                            description: 'Failed to fix count',
                            variant: 'destructive'
                          })
                        }
                      }}
                      className='text-xs h-6'
                    >
                      Fix Count
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Unassigned Users List */}
      {unassignedUsers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className='text-orange-600'>
              Unassigned Users ({unassignedUsers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className='space-y-2'>
              {unassignedUsers.map((user) => (
                <div key={user.id} className='flex justify-between items-center p-2 bg-muted rounded'>
                  <div>
                    <span className='font-medium'>{user.full_name || 'No Name'}</span>
                    <span className='text-sm text-muted-foreground ml-2'>
                      ({user.email})
                    </span>
                  </div>
                  <Badge variant='outline'>{user.role || 'No Role'}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
