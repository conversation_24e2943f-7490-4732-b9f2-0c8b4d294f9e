import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { api, APIKey, APIKeyStatistics, APIKeyUsageLog } from '@/lib/api';
import { 
  Key, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff, 
  Activity, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Copy,
  RefreshCw,
  BarChart3,
  Shield,
  Search,
  Filter
} from 'lucide-react';

const SERVICE_PROVIDERS = [
  { value: 'openai', label: 'OpenAI' },
  { value: 'google', label: 'Google' },
  { value: 'zoom', label: 'Zoom' },
  { value: 'stripe', label: 'Stripe' },
  { value: 'twilio', label: 'Twilio' },
  { value: 'sendgrid', label: 'SendGrid' },
  { value: 'aws', label: 'AWS' },
  { value: 'azure', label: 'Azure' },
  { value: 'github', label: 'GitHub' },
  { value: 'slack', label: 'Slack' },
  { value: 'microsoft', label: 'Microsoft' },
  { value: 'other', label: 'Other' }
];

const APIKeyManagement: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [statistics, setStatistics] = useState<APIKeyStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [isDemoMode, setIsDemoMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterProvider, setFilterProvider] = useState<string>('all');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingKey, setEditingKey] = useState<APIKey | null>(null);
  const [deletingKey, setDeletingKey] = useState<APIKey | null>(null);
  const [viewingUsage, setViewingUsage] = useState<string | null>(null);
  const [usageLogs, setUsageLogs] = useState<APIKeyUsageLog[]>([]);
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const [formData, setFormData] = useState({
    name: '',
    service_provider: '',
    api_key: '',
    description: '',
    expires_at: ''
  });

  // Removed mock data - using real database data only


  useEffect(() => {
    loadAPIKeys();
    loadStatistics();
  }, []);

  const loadAPIKeys = async () => {
    try {
      setLoading(true);
      console.log('Attempting to load API keys from database...');
      
      // Simplified approach: try direct database access, catch specific errors
      const response = await api.handleRoute('/api/admin/api-keys', 'GET').catch(err => {
        console.log('Direct API call failed:', err);
        return { success: false, error: err, data: null, message: 'Database unavailable' };
      });
      
      console.log('API response:', response);
      
      if (response && response.success && response.data && Array.isArray(response.data)) {
        console.log('Successfully loaded API keys from database');
        setApiKeys(response.data);
        setIsDemoMode(false);
      } else {
        console.log('Database response failed or invalid, no API keys available:', response?.error);
        setIsDemoMode(false);
        setApiKeys([]);
      }
    } catch (error) {
      console.log('Database error caught, no API keys available:', error);
      setIsDemoMode(false);
      setApiKeys([]);
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      console.log('Attempting to load API key statistics...');
      
      const response = await api.handleRoute('/api/admin/api-keys/statistics', 'GET').catch(err => {
        console.log('Statistics API call failed:', err);
        return { success: false, error: err, data: null, message: 'Statistics unavailable' };
      });
      
      console.log('Statistics response:', response);
      
      if (response && response.success && response.data) {
        console.log('Successfully loaded statistics from database');
        setStatistics(response.data);
      } else {
        console.log('Statistics response failed, using demo data:', response?.error);
        setStatistics(getMockStatistics());
      }
    } catch (error) {
      console.log('Statistics error caught, using demo data:', error);
      setStatistics(getMockStatistics());
    }
  };

  const loadUsageLogs = async (apiKeyId: string) => {
    try {
      const response = await api.handleRoute(`/api/admin/api-keys/${apiKeyId}/usage`, 'GET');
      if (response.success) {
        setUsageLogs(response.data);
      } else {
        // Mock usage logs for demo
        const mockLogs: APIKeyUsageLog[] = [
          {
            id: '1',
            api_key_id: apiKeyId,
            endpoint: '/api/openai/completions',
            method: 'POST',
            status_code: 200,
            response_time_ms: 1250,
            error_message: null,
            used_at: new Date().toISOString()
          },
          {
            id: '2',
            api_key_id: apiKeyId,
            endpoint: '/api/openai/chat/completions',
            method: 'POST',
            status_code: 200,
            response_time_ms: 890,
            error_message: null,
            used_at: new Date(Date.now() - 3600000).toISOString()
          }
        ];
        setUsageLogs(mockLogs);
      }
    } catch (error) {
      console.error('Database not available, using demo usage logs:', error);
      const mockLogs: APIKeyUsageLog[] = [
        {
          id: '1',
          api_key_id: apiKeyId,
          endpoint: '/api/openai/completions',
          method: 'POST',
          status_code: 200,
          response_time_ms: 1250,
          error_message: null,
          used_at: new Date().toISOString()
        },
        {
          id: '2',
          api_key_id: apiKeyId,
          endpoint: '/api/openai/chat/completions',
          method: 'POST',
          status_code: 200,
          response_time_ms: 890,
          error_message: null,
          used_at: new Date(Date.now() - 3600000).toISOString()
        }
      ];
      setUsageLogs(mockLogs);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.service_provider || !formData.api_key) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    if (isDemoMode) {
      toast({
        title: "Demo Mode",
        description: "This is a demonstration. In production, the API key would be saved to the database.",
        variant: "default"
      });
      
      // Simulate adding to demo data
      const newKey: APIKey = {
        id: Date.now().toString(),
        name: formData.name,
        service_provider: formData.service_provider,
        api_key_encrypted: formData.api_key.substring(0, 8) + '*'.repeat(Math.max(0, formData.api_key.length - 12)) + formData.api_key.slice(-4),
        description: formData.description || '',
        status: 'active' as const,
        usage_count: 0,
        last_used_at: null,
        expires_at: formData.expires_at || null,
        created_by: 'admin',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      if (editingKey) {
        setApiKeys(prev => prev.map(key => key.id === editingKey.id ? { ...newKey, id: editingKey.id } : key));
      } else {
        setApiKeys(prev => [...prev, newKey]);
      }
      
      setShowAddDialog(false);
      setEditingKey(null);
      setFormData({ name: '', service_provider: '', api_key: '', description: '', expires_at: '' });
      return;
    }

    try {
      const payload = {
        ...formData,
        expires_at: formData.expires_at || null
      };

      let response;
      if (editingKey) {
        response = await api.handleRoute(`/api/admin/api-keys/${editingKey.id}`, 'PATCH', payload);
      } else {
        response = await api.handleRoute('/api/admin/api-keys', 'POST', payload);
      }

      if (response.success) {
        toast({
          title: "Success",
          description: editingKey ? "API key updated successfully" : "API key created successfully"
        });
        setShowAddDialog(false);
        setEditingKey(null);
        setFormData({ name: '', service_provider: '', api_key: '', description: '', expires_at: '' });
        loadAPIKeys();
        loadStatistics();
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to save API key",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error saving API key:', error);
      toast({
        title: "Error",
        description: "Failed to save API key",
        variant: "destructive"
      });
    }
  };

  const handleDelete = async () => {
    if (!deletingKey) return;

    if (isDemoMode) {
      toast({
        title: "Demo Mode",
        description: "API key removed from demo data",
        variant: "default"
      });
      setApiKeys(prev => prev.filter(key => key.id !== deletingKey.id));
      setDeletingKey(null);
      return;
    }

    try {
      const response = await api.handleRoute(`/api/admin/api-keys/${deletingKey.id}`, 'DELETE');
      if (response.success) {
        toast({
          title: "Success",
          description: "API key deleted successfully"
        });
        setDeletingKey(null);
        loadAPIKeys();
        loadStatistics();
      } else {
        toast({
          title: "Error",
          description: "Failed to delete API key",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error deleting API key:', error);
      toast({
        title: "Error",
        description: "Failed to delete API key",
        variant: "destructive"
      });
    }
  };

  const handleToggleStatus = async (apiKey: APIKey) => {
    if (isDemoMode) {
      const newStatus = apiKey.status === 'active' ? 'inactive' : 'active';
      setApiKeys(prev => prev.map(key => 
        key.id === apiKey.id ? { ...key, status: newStatus } : key
      ));
      toast({
        title: "Demo Mode",
        description: `API key ${newStatus === 'active' ? 'activated' : 'deactivated'} in demo`,
        variant: "default"
      });
      return;
    }

    try {
      const newStatus = apiKey.status === 'active' ? 'inactive' : 'active';
      const response = await api.handleRoute(`/api/admin/api-keys/${apiKey.id}`, 'PATCH', {
        status: newStatus
      });

      if (response.success) {
        toast({
          title: "Success",
          description: `API key ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`
        });
        loadAPIKeys();
        loadStatistics();
      } else {
        toast({
          title: "Error",
          description: "Failed to update API key status",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error updating API key status:', error);
      toast({
        title: "Error",
        description: "Failed to update API key status",
        variant: "destructive"
      });
    }
  };

  const handleCopyKey = (key: string) => {
    navigator.clipboard.writeText(key);
    toast({
      title: "Success",
      description: "API key copied to clipboard"
    });
  };

  const toggleKeyVisibility = (keyId: string) => {
    const newVisibleKeys = new Set(visibleKeys);
    if (newVisibleKeys.has(keyId)) {
      newVisibleKeys.delete(keyId);
    } else {
      newVisibleKeys.add(keyId);
    }
    setVisibleKeys(newVisibleKeys);
  };

  const filteredKeys = apiKeys.filter(key => {
    const matchesSearch = key.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         key.service_provider.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         key.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || key.status === filterStatus;
    const matchesProvider = filterProvider === 'all' || key.service_provider === filterProvider;
    
    return matchesSearch && matchesStatus && matchesProvider;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'inactive': return <XCircle className="w-4 h-4 text-gray-500" />;
      case 'expired': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <XCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      expired: 'bg-red-100 text-red-800'
    };
    return variants[status as keyof typeof variants] || variants.inactive;
  };

  const maskAPIKey = (key: string) => {
    if (key.length <= 8) return '*'.repeat(key.length);
    return key.substring(0, 4) + '*'.repeat(key.length - 8) + key.substring(key.length - 4);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Demo Mode Banner */}
      {isDemoMode && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <div>
              <h4 className="text-sm font-medium text-yellow-800">Demo Mode</h4>
              <p className="text-sm text-yellow-700">
                Database is not available. Using demonstration data. To use real data, ensure your Supabase instance is running.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">API Key Management</h1>
          <p className="text-gray-600">
            Manage and monitor your API keys securely
            {isDemoMode && <span className="text-yellow-600 font-medium"> (Demo Mode)</span>}
          </p>
        </div>
        <Button onClick={() => setShowAddDialog(true)} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Add API Key
        </Button>
      </div>

      {/* Statistics Cards */}
      {statistics && (
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Keys</p>
                  <p className="text-2xl font-bold">{statistics.total_keys}</p>
                </div>
                <Key className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Keys</p>
                  <p className="text-2xl font-bold text-green-600">{statistics.active_keys}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Expired Keys</p>
                  <p className="text-2xl font-bold text-red-600">{statistics.expired_keys}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Usage</p>
                  <p className="text-2xl font-bold">{statistics.total_usage}</p>
                </div>
                <Activity className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Top Service</p>
                  <p className="text-lg font-bold capitalize">{statistics.most_used_service || 'None'}</p>
                </div>
                <BarChart3 className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <Input
                  placeholder="Search API keys..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filterProvider} onValueChange={setFilterProvider}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Provider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Providers</SelectItem>
                {SERVICE_PROVIDERS.map(provider => (
                  <SelectItem key={provider.value} value={provider.value}>
                    {provider.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* API Keys Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            API Keys ({filteredKeys.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Service Provider</TableHead>
                  <TableHead>API Key</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Usage</TableHead>
                  <TableHead>Last Used</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredKeys.map((apiKey) => (
                  <TableRow key={apiKey.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{apiKey.name}</div>
                        {apiKey.description && (
                          <div className="text-sm text-gray-500">{apiKey.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="capitalize">
                        {apiKey.service_provider}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {visibleKeys.has(apiKey.id) ? apiKey.api_key_encrypted : maskAPIKey(apiKey.api_key_encrypted)}
                        </code>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleKeyVisibility(apiKey.id)}
                        >
                          {visibleKeys.has(apiKey.id) ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyKey(apiKey.api_key_encrypted)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(apiKey.status)}
                        <Badge className={getStatusBadge(apiKey.status)}>
                          {apiKey.status}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Activity className="w-4 h-4 text-gray-400" />
                        <span>{apiKey.usage_count}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {apiKey.last_used_at ? (
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-sm">
                            {new Date(apiKey.last_used_at).toLocaleDateString()}
                          </span>
                        </div>
                      ) : (
                        <span className="text-gray-400">Never</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {apiKey.expires_at ? (
                        <span className="text-sm">
                          {new Date(apiKey.expires_at).toLocaleDateString()}
                        </span>
                      ) : (
                        <span className="text-gray-400">Never</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingKey(apiKey);
                            setFormData({
                              name: apiKey.name,
                              service_provider: apiKey.service_provider,
                              api_key: apiKey.api_key_encrypted,
                              description: apiKey.description || '',
                              expires_at: apiKey.expires_at ? new Date(apiKey.expires_at).toISOString().split('T')[0] : ''
                            });
                            setShowAddDialog(true);
                          }}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(apiKey)}
                        >
                          {apiKey.status === 'active' ? <XCircle className="w-4 h-4" /> : <CheckCircle className="w-4 h-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setViewingUsage(apiKey.id);
                            loadUsageLogs(apiKey.id);
                          }}
                        >
                          <BarChart3 className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingKey(apiKey)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingKey ? 'Edit API Key' : 'Add New API Key'}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., OpenAI GPT-4"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="service_provider">Service Provider *</Label>
              <Select
                value={formData.service_provider}
                onValueChange={(value) => setFormData({ ...formData, service_provider: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select service provider" />
                </SelectTrigger>
                <SelectContent>
                  {SERVICE_PROVIDERS.map(provider => (
                    <SelectItem key={provider.value} value={provider.value}>
                      {provider.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="api_key">API Key *</Label>
              <Input
                id="api_key"
                type="password"
                value={formData.api_key}
                onChange={(e) => setFormData({ ...formData, api_key: e.target.value })}
                placeholder="Enter API key"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Optional description"
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="expires_at">Expiration Date</Label>
              <Input
                id="expires_at"
                type="date"
                value={formData.expires_at}
                onChange={(e) => setFormData({ ...formData, expires_at: e.target.value })}
              />
            </div>
            
            <div className="flex gap-2 pt-4">
              <Button type="submit" className="flex-1">
                {editingKey ? 'Update' : 'Create'} API Key
              </Button>
              <Button type="button" variant="outline" onClick={() => {
                setShowAddDialog(false);
                setEditingKey(null);
                setFormData({ name: '', service_provider: '', api_key: '', description: '', expires_at: '' });
              }}>
                Cancel
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingKey} onOpenChange={() => setDeletingKey(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the API key "{deletingKey?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Usage Logs Dialog */}
      <Dialog open={!!viewingUsage} onOpenChange={() => setViewingUsage(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>API Key Usage Logs</DialogTitle>
          </DialogHeader>
          <div className="max-h-96 overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Endpoint</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Response Time</TableHead>
                  <TableHead>Error</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {usageLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>{new Date(log.used_at).toLocaleString()}</TableCell>
                    <TableCell>{log.endpoint}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{log.method}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={log.status_code >= 400 ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}>
                        {log.status_code}
                      </Badge>
                    </TableCell>
                    <TableCell>{log.response_time_ms}ms</TableCell>
                    <TableCell>{log.error_message || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default APIKeyManagement; 