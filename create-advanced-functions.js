// Create database functions for advanced modules
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

const logStep = (message) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message) => {
  console.log(`✅ ${message}`);
};

const logError = (message) => {
  console.error(`❌ ${message}`);
};

async function createAdvancedFunctions() {
  console.log('🚀 Starting advanced functions creation...');
  
  try {
    // Create advanced database functions
    logStep('Creating advanced database functions...');
    
    const { error: functionsError } = await supabase.rpc('exec_sql', {
      sql_text: `
        -- Function to increment task comments count
        CREATE OR REPLACE FUNCTION increment_task_comments(task_id UUID)
        RETURNS VOID AS $$
        BEGIN
          UPDATE public.tasks 
          SET comments_count = COALESCE(comments_count, 0) + 1,
              updated_at = NOW()
          WHERE id = task_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to create task with assignment
        CREATE OR REPLACE FUNCTION create_task_with_assignment(
          p_title TEXT,
          p_description TEXT DEFAULT NULL,
          p_project_id UUID DEFAULT NULL,
          p_assigned_to UUID DEFAULT NULL,
          p_status TEXT DEFAULT 'todo',
          p_priority TEXT DEFAULT 'medium',
          p_type TEXT DEFAULT 'task',
          p_category TEXT DEFAULT NULL,
          p_estimated_hours DECIMAL(5,2) DEFAULT NULL,
          p_due_date DATE DEFAULT NULL,
          p_created_by UUID DEFAULT NULL
        )
        RETURNS UUID AS $$
        DECLARE
          task_id UUID;
        BEGIN
          -- Create the task
          INSERT INTO public.tasks (
            title, description, project_id, assigned_to, status, priority, type,
            category, estimated_hours, due_date, created_by, progress_percentage,
            comments_count, watchers, created_at, updated_at
          ) VALUES (
            p_title, p_description, p_project_id, p_assigned_to, p_status, p_priority, p_type,
            p_category, p_estimated_hours, p_due_date, p_created_by, 0,
            0, ARRAY[p_created_by], NOW(), NOW()
          ) RETURNING id INTO task_id;
          
          -- Create assignment if assigned_to is provided
          IF p_assigned_to IS NOT NULL THEN
            INSERT INTO public.task_assignments (
              task_id, assigned_to, assigned_by, role, status, assigned_at
            ) VALUES (
              task_id, p_assigned_to, p_created_by, 'assignee', 'active', NOW()
            );
          END IF;
          
          -- Log the activity
          INSERT INTO public.user_activities (
            user_id, activity_type, action, description, entity_type, entity_id,
            entity_name, impact_level, category, success, created_at
          ) VALUES (
            p_created_by, 'create', 'Created Task', 
            'Created new task: ' || p_title, 'task', task_id,
            p_title, 'medium', 'task_management', TRUE, NOW()
          );
          
          RETURN task_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to start time tracking
        CREATE OR REPLACE FUNCTION start_time_tracking(
          p_user_id UUID,
          p_activity_type TEXT DEFAULT 'work',
          p_description TEXT DEFAULT NULL,
          p_project_id UUID DEFAULT NULL,
          p_task_id UUID DEFAULT NULL
        )
        RETURNS UUID AS $$
        DECLARE
          time_log_id UUID;
        BEGIN
          -- Stop any existing active time logs for the user
          UPDATE public.time_logs 
          SET status = 'completed',
              end_time = NOW(),
              duration_minutes = EXTRACT(EPOCH FROM (NOW() - start_time)) / 60,
              updated_at = NOW()
          WHERE user_id = p_user_id AND status = 'active';
          
          -- Create new time log
          INSERT INTO public.time_logs (
            user_id, project_id, task_id, activity_type, description,
            start_time, status, is_billable, hourly_rate, created_at, updated_at
          ) VALUES (
            p_user_id, p_project_id, p_task_id, p_activity_type, p_description,
            NOW(), 'active', 
            CASE WHEN p_activity_type IN ('work', 'meeting') THEN TRUE ELSE FALSE END,
            50.00, NOW(), NOW()
          ) RETURNING id INTO time_log_id;
          
          -- Log the activity
          INSERT INTO public.user_activities (
            user_id, activity_type, action, description, entity_type, entity_id,
            impact_level, category, success, created_at
          ) VALUES (
            p_user_id, 'create', 'Started Time Tracking',
            'Started tracking time for ' || p_activity_type, 'time_log', time_log_id,
            'low', 'time_management', TRUE, NOW()
          );
          
          RETURN time_log_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to stop time tracking
        CREATE OR REPLACE FUNCTION stop_time_tracking(
          p_time_log_id UUID,
          p_notes TEXT DEFAULT NULL
        )
        RETURNS BOOLEAN AS $$
        DECLARE
          log_record RECORD;
          duration_mins INTEGER;
          total_amt DECIMAL(10,2);
        BEGIN
          -- Get the time log record
          SELECT * INTO log_record FROM public.time_logs WHERE id = p_time_log_id;
          
          IF NOT FOUND THEN
            RETURN FALSE;
          END IF;
          
          -- Calculate duration and amount
          duration_mins := EXTRACT(EPOCH FROM (NOW() - log_record.start_time)) / 60;
          total_amt := CASE 
            WHEN log_record.hourly_rate IS NOT NULL THEN (duration_mins / 60.0) * log_record.hourly_rate
            ELSE 0
          END;
          
          -- Update the time log
          UPDATE public.time_logs 
          SET end_time = NOW(),
              duration_minutes = duration_mins,
              total_amount = total_amt,
              status = 'completed',
              notes = p_notes,
              updated_at = NOW()
          WHERE id = p_time_log_id;
          
          -- Log the activity
          INSERT INTO public.user_activities (
            user_id, activity_type, action, description, entity_type, entity_id,
            impact_level, category, success, metadata, created_at
          ) VALUES (
            log_record.user_id, 'complete', 'Stopped Time Tracking',
            'Stopped tracking time (' || duration_mins || ' minutes)', 'time_log', p_time_log_id,
            'low', 'time_management', TRUE, 
            jsonb_build_object('duration_minutes', duration_mins, 'total_amount', total_amt),
            NOW()
          );
          
          RETURN TRUE;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to log system event
        CREATE OR REPLACE FUNCTION log_system_event(
          p_level TEXT,
          p_category TEXT,
          p_message TEXT,
          p_details TEXT DEFAULT NULL,
          p_user_id UUID DEFAULT NULL,
          p_metadata JSONB DEFAULT '{}'
        )
        RETURNS UUID AS $$
        DECLARE
          log_id UUID;
        BEGIN
          INSERT INTO public.system_logs (
            user_id, level, category, message, details, context, metadata, created_at
          ) VALUES (
            p_user_id, p_level, p_category, p_message, p_details,
            jsonb_build_object('timestamp', NOW(), 'source', 'database_function'),
            p_metadata, NOW()
          ) RETURNING id INTO log_id;
          
          RETURN log_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to get user dashboard stats
        CREATE OR REPLACE FUNCTION get_user_dashboard_stats(p_user_id UUID)
        RETURNS JSONB AS $$
        DECLARE
          result JSONB;
          task_stats JSONB;
          time_stats JSONB;
          project_stats JSONB;
        BEGIN
          -- Get task statistics
          SELECT jsonb_build_object(
            'total', COUNT(*),
            'todo', COUNT(*) FILTER (WHERE status = 'todo'),
            'in_progress', COUNT(*) FILTER (WHERE status = 'in_progress'),
            'done', COUNT(*) FILTER (WHERE status = 'done'),
            'overdue', COUNT(*) FILTER (WHERE due_date < CURRENT_DATE AND status NOT IN ('done', 'cancelled'))
          ) INTO task_stats
          FROM public.tasks
          WHERE assigned_to = p_user_id;
          
          -- Get time tracking statistics (last 7 days)
          SELECT jsonb_build_object(
            'total_hours', COALESCE(SUM(duration_minutes) / 60.0, 0),
            'billable_hours', COALESCE(SUM(duration_minutes) FILTER (WHERE is_billable = TRUE) / 60.0, 0),
            'sessions', COUNT(*),
            'total_amount', COALESCE(SUM(total_amount), 0)
          ) INTO time_stats
          FROM public.time_logs
          WHERE user_id = p_user_id 
            AND start_time >= CURRENT_DATE - INTERVAL '7 days'
            AND status = 'completed';
          
          -- Get project statistics
          SELECT jsonb_build_object(
            'assigned_projects', COUNT(DISTINCT project_id),
            'active_projects', COUNT(DISTINCT project_id) FILTER (WHERE status = 'active')
          ) INTO project_stats
          FROM public.tasks
          WHERE assigned_to = p_user_id AND project_id IS NOT NULL;
          
          -- Combine all statistics
          result := jsonb_build_object(
            'tasks', task_stats,
            'time_tracking', time_stats,
            'projects', project_stats,
            'generated_at', NOW()
          );
          
          RETURN result;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to get organization overview
        CREATE OR REPLACE FUNCTION get_organization_overview()
        RETURNS JSONB AS $$
        DECLARE
          result JSONB;
        BEGIN
          SELECT jsonb_build_object(
            'total_users', (SELECT COUNT(*) FROM public.profiles WHERE status = 'active'),
            'total_projects', (SELECT COUNT(*) FROM public.projects),
            'active_projects', (SELECT COUNT(*) FROM public.projects WHERE status = 'active'),
            'total_tasks', (SELECT COUNT(*) FROM public.tasks),
            'completed_tasks', (SELECT COUNT(*) FROM public.tasks WHERE status = 'done'),
            'total_departments', (SELECT COUNT(*) FROM public.departments WHERE status = 'active'),
            'recent_activities', (
              SELECT jsonb_agg(
                jsonb_build_object(
                  'action', action,
                  'description', description,
                  'created_at', created_at,
                  'user_id', user_id
                )
              )
              FROM (
                SELECT action, description, created_at, user_id
                FROM public.user_activities
                ORDER BY created_at DESC
                LIMIT 10
              ) recent
            ),
            'generated_at', NOW()
          ) INTO result;
          
          RETURN result;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to assign task to user
        CREATE OR REPLACE FUNCTION assign_task_to_user(
          p_task_id UUID,
          p_user_id UUID,
          p_assigned_by UUID,
          p_role TEXT DEFAULT 'assignee',
          p_notes TEXT DEFAULT NULL
        )
        RETURNS BOOLEAN AS $$
        BEGIN
          -- Update task assignment
          UPDATE public.tasks 
          SET assigned_to = p_user_id,
              updated_at = NOW()
          WHERE id = p_task_id;
          
          -- Create assignment record
          INSERT INTO public.task_assignments (
            task_id, assigned_to, assigned_by, role, status, assigned_at, notes
          ) VALUES (
            p_task_id, p_user_id, p_assigned_by, p_role, 'active', NOW(), p_notes
          );
          
          -- Add user to watchers if not already there
          UPDATE public.tasks 
          SET watchers = CASE 
            WHEN p_user_id = ANY(watchers) THEN watchers
            ELSE array_append(watchers, p_user_id)
          END
          WHERE id = p_task_id;
          
          -- Log the activity
          INSERT INTO public.user_activities (
            user_id, activity_type, action, description, entity_type, entity_id,
            impact_level, category, success, created_at
          ) VALUES (
            p_assigned_by, 'assign', 'Assigned Task',
            'Assigned task to user', 'task', p_task_id,
            'medium', 'task_management', TRUE, NOW()
          );
          
          RETURN TRUE;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Grant execute permissions
        GRANT EXECUTE ON FUNCTION increment_task_comments TO authenticated;
        GRANT EXECUTE ON FUNCTION increment_task_comments TO anon;
        GRANT EXECUTE ON FUNCTION create_task_with_assignment TO authenticated;
        GRANT EXECUTE ON FUNCTION create_task_with_assignment TO anon;
        GRANT EXECUTE ON FUNCTION start_time_tracking TO authenticated;
        GRANT EXECUTE ON FUNCTION start_time_tracking TO anon;
        GRANT EXECUTE ON FUNCTION stop_time_tracking TO authenticated;
        GRANT EXECUTE ON FUNCTION stop_time_tracking TO anon;
        GRANT EXECUTE ON FUNCTION log_system_event TO authenticated;
        GRANT EXECUTE ON FUNCTION log_system_event TO anon;
        GRANT EXECUTE ON FUNCTION get_user_dashboard_stats TO authenticated;
        GRANT EXECUTE ON FUNCTION get_user_dashboard_stats TO anon;
        GRANT EXECUTE ON FUNCTION get_organization_overview TO authenticated;
        GRANT EXECUTE ON FUNCTION get_organization_overview TO anon;
        GRANT EXECUTE ON FUNCTION assign_task_to_user TO authenticated;
        GRANT EXECUTE ON FUNCTION assign_task_to_user TO anon;
      `
    });

    if (functionsError) {
      throw new Error('Advanced functions creation failed: ' + functionsError.message);
    }

    logSuccess('Advanced database functions created successfully');

    // Test the functions
    logStep('Testing advanced functions...');
    
    // Test organization overview function
    try {
      const { data: orgOverview, error: orgError } = await supabase.rpc('get_organization_overview');
      
      if (orgError) {
        console.warn('Organization overview test warning:', orgError.message);
      } else {
        logSuccess('Organization overview function working');
        console.log('Organization Overview:', orgOverview);
      }
    } catch (testErr) {
      console.warn('Organization overview test error:', testErr.message);
    }

    // Test user dashboard stats function
    try {
      const testUserId = '00000000-0000-0000-0000-000000000001';
      const { data: userStats, error: userError } = await supabase.rpc('get_user_dashboard_stats', {
        p_user_id: testUserId
      });
      
      if (userError) {
        console.warn('User dashboard stats test warning:', userError.message);
      } else {
        logSuccess('User dashboard stats function working');
        console.log('User Stats:', userStats);
      }
    } catch (testErr) {
      console.warn('User dashboard stats test error:', testErr.message);
    }

    // Test system logging function
    try {
      const { data: logId, error: logError } = await supabase.rpc('log_system_event', {
        p_level: 'info',
        p_category: 'general',
        p_message: 'Advanced functions test completed',
        p_details: 'All advanced database functions have been created and tested successfully',
        p_metadata: { test: true, timestamp: new Date().toISOString() }
      });
      
      if (logError) {
        console.warn('System logging test warning:', logError.message);
      } else {
        logSuccess('System logging function working');
        console.log('Log ID:', logId);
      }
    } catch (testErr) {
      console.warn('System logging test error:', testErr.message);
    }

    logSuccess('🎉 ADVANCED FUNCTIONS CREATION COMPLETED!');
    console.log('\n🚀 CREATED FUNCTIONS:');
    console.log('✅ increment_task_comments - Increment task comment count');
    console.log('✅ create_task_with_assignment - Create task with automatic assignment');
    console.log('✅ start_time_tracking - Start time tracking session');
    console.log('✅ stop_time_tracking - Stop time tracking and calculate totals');
    console.log('✅ log_system_event - Log system events and activities');
    console.log('✅ get_user_dashboard_stats - Get user dashboard statistics');
    console.log('✅ get_organization_overview - Get organization overview');
    console.log('✅ assign_task_to_user - Assign task to user with logging');
    console.log('\n🎯 All advanced functions are ready for use!');
    
    return true;

  } catch (error) {
    logError(`Advanced functions creation failed: ${error.message}`);
    console.error('Full error:', error);
    return false;
  }
}

// Run the creation
createAdvancedFunctions()
  .then((success) => {
    if (success) {
      console.log('\n🎉 SUCCESS: Advanced functions created successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ FAILED: Advanced functions creation encountered errors!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 CRITICAL ERROR:', error);
    process.exit(1);
  });
