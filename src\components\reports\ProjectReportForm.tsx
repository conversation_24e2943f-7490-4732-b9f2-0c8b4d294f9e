import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FileText, Upload, Loader2, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
}

export const ProjectReportForm = () => {
  const [selectedProject, setSelectedProject] = useState("");
  const { toast } = useToast();

  // Load real projects data with proper error handling
  const {
    data: projects = [],
    isLoading: projectsLoading,
    error: projectsError
  } = useQuery({
    queryKey: ['projects-for-reports'],
    queryFn: async () => {
      const response = await api.projects.getAll();
      if (response.success && response.data) {
        return response.data as Project[];
      } else {
        throw new Error(response.error?.message || 'Failed to load projects');
      }
    },
    retry: 2,
    retryDelay: 1000,
    onError: (error: any) => {
      console.error('Error loading projects for reports:', error);
      toast({
        title: "Failed to Load Projects",
        description: "Unable to load project data for reports. Please try again.",
        variant: "destructive",
      });
    }
  });

  const handleSubmitReport = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    // Check if projects are available
    if (projects.length === 0) {
      toast({
        title: "No Projects Available",
        description: "Cannot submit report - no projects are available. Please create a project first.",
        variant: "destructive",
      });
      return;
    }

    if (!selectedProject) {
      toast({
        title: "Validation Error",
        description: "Please select a project before submitting the report",
        variant: "destructive",
      });
      return;
    }

    const selectedProjectData = projects.find(p => p.id === selectedProject);

    toast({
      title: "Project Report Submitted",
      description: `Report for project "${selectedProjectData?.name || selectedProject}" has been submitted successfully`,
    });

    // Reset form after successful submission
    setSelectedProject("");
    e.currentTarget.reset();
  };

  return (
    <Card className="bg-black/10 dark:bg-white/5 backdrop-blur-lg border-none">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg font-medium">
          <FileText className="h-5 w-5 text-primary" />
          Project Report
        </CardTitle>
      </CardHeader>
      <CardContent className="p-4 sm:p-6">
        <form onSubmit={handleSubmitReport} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Project</label>
            <Select
              value={selectedProject}
              onValueChange={setSelectedProject}
              disabled={projectsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={
                  projectsLoading
                    ? "Loading projects..."
                    : projectsError
                      ? "Error loading projects"
                      : projects.length === 0
                        ? "No projects available"
                        : "Select a project"
                } />
              </SelectTrigger>
              <SelectContent>
                {projectsLoading ? (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm">Loading...</span>
                  </div>
                ) : projects.length > 0 ? (
                  projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name} ({project.status})
                    </SelectItem>
                  ))
                ) : (
                  <div className="flex items-center justify-center p-2 text-muted-foreground">
                    <span className="text-sm">
                      {projectsError ? "Failed to load projects" : "No projects found"}
                    </span>
                  </div>
                )}
              </SelectContent>
            </Select>

            {/* Enhanced error state */}
            {projectsError && (
              <div className="flex items-center gap-2 text-sm text-destructive">
                <AlertCircle className="h-4 w-4" />
                <span>Unable to load projects. Please refresh the page or try again later.</span>
              </div>
            )}

            {/* Empty state when no projects */}
            {!projectsLoading && !projectsError && projects.length === 0 && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <AlertCircle className="h-4 w-4" />
                <span>No projects available. Create a project first to generate reports.</span>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Project Status</label>
            <Select name="status" defaultValue="in-progress">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="delayed">Delayed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Progress Report</label>
            <textarea
              name="report"
              className="w-full rounded-md border border-white/10 bg-white/5 p-2 min-h-[100px]"
              placeholder="Enter project progress details..."
              required
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Upload Supporting Documents</label>
            <div className="border-2 border-dashed border-white/10 rounded-lg p-4 text-center">
              <Upload className="h-8 w-8 mx-auto text-primary" />
              <p className="text-sm text-muted-foreground mt-2">
                Drop files here or click to upload
              </p>
              <input
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                multiple
              />
            </div>
          </div>

          <Button
            type="submit"
            className="w-full md:w-auto"
            disabled={projectsLoading || projects.length === 0 || !selectedProject}
          >
            {projectsLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </>
            ) : projects.length === 0 ? (
              "No Projects Available"
            ) : !selectedProject ? (
              "Select Project First"
            ) : (
              "Submit Project Report"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};