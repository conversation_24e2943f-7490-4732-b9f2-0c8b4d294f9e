<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple RLS Fix - CTNL AI Work-Board</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
            background: #000000;
            color: #ffffff;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .header h1 {
            color: #ff0000;
            margin-bottom: 0.5rem;
        }

        .card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 0, 0, 0.2);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .button {
            background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 1rem;
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
        }

        .button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .status {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .status.info {
            background: rgba(0, 123, 255, 0.1);
            border: 1px solid rgba(0, 123, 255, 0.3);
        }

        .status.success {
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status.error {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .sql-box {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
            overflow-x: auto;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Simple RLS Fix</h1>
            <p>Quick fix for profiles table RLS issues</p>
        </div>

        <div class="card">
            <h3>🚨 Issue</h3>
            <p>The profiles table has RLS policy conflicts causing 403/406 errors during profile creation and access.</p>
        </div>

        <div class="card">
            <h3>📋 Manual Fix Instructions</h3>
            <p>Copy and run this SQL in your Supabase SQL Editor:</p>
            
            <div class="sql-box" id="sqlCode">-- Fix Profiles RLS Policies
-- Run this in Supabase SQL Editor

-- 1. Disable RLS temporarily
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- 2. Drop all existing policies
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;

-- 3. Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 4. Create clean policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE USING (auth.uid() = id) WITH CHECK (auth.uid() = id);

-- 5. Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;</div>

            <button class="button" onclick="copySQL()">📋 Copy SQL</button>
        </div>

        <div class="card">
            <h3>🧪 Test Profile Access</h3>
            <button class="button" id="testButton" onclick="testProfileAccess()">
                Test Current Profile Access
            </button>
        </div>

        <div class="card">
            <h3>🔄 Alternative: Disable RLS Temporarily</h3>
            <p>If the above doesn't work, you can temporarily disable RLS while we fix the underlying issues:</p>
            <button class="button" onclick="disableRLS()" style="background: rgba(255, 193, 7, 0.8);">
                ⚠️ Temporarily Disable RLS
            </button>
        </div>

        <div id="status" class="status info" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            statusDiv.style.display = 'block';
        }

        window.copySQL = function() {
            const sqlCode = document.getElementById('sqlCode').textContent;
            navigator.clipboard.writeText(sqlCode).then(() => {
                showStatus('✅ SQL copied to clipboard!\n\nNow:\n1. Go to your Supabase dashboard\n2. Open SQL Editor\n3. Paste and run the SQL\n4. Come back and test profile access', 'success');
            }).catch(() => {
                showStatus('❌ Failed to copy SQL. Please select and copy manually.', 'error');
            });
        };

        window.testProfileAccess = async function() {
            const testButton = document.getElementById('testButton');
            testButton.disabled = true;
            
            try {
                showStatus('🧪 Testing profile access...', 'info');
                
                // Get current user
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                
                if (userError || !user) {
                    showStatus('❌ No authenticated user found. Please sign in first.', 'error');
                    return;
                }
                
                // Test profile access
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('id', user.id)
                    .single();
                
                if (profileError) {
                    showStatus(`❌ Profile access failed: ${profileError.message}\n\nThis confirms the RLS issue. Please run the SQL fix above.`, 'error');
                } else if (profile) {
                    showStatus(`✅ Profile access successful!\n\nProfile found: ${profile.full_name || 'No name'} (${profile.role || 'No role'})`, 'success');
                } else {
                    showStatus('⚠️ No profile found for current user. This might be expected for new users.', 'warning');
                }
                
                // Test profile creation
                const { error: insertError } = await supabase
                    .from('profiles')
                    .upsert({
                        id: user.id,
                        full_name: user.user_metadata?.full_name || 'Test User',
                        email: user.email,
                        role: 'staff',
                        status: 'active'
                    });
                
                if (insertError) {
                    showStatus(`❌ Profile creation test failed: ${insertError.message}\n\nRLS policies are blocking profile creation.`, 'error');
                } else {
                    showStatus('✅ Profile creation test passed! RLS is working correctly.', 'success');
                }
                
            } catch (error) {
                showStatus(`❌ Test failed: ${error.message}`, 'error');
            } finally {
                testButton.disabled = false;
            }
        };

        window.disableRLS = async function() {
            if (!confirm('This will temporarily disable RLS on the profiles table. Are you sure?')) {
                return;
            }
            
            try {
                showStatus('⚠️ This action requires manual SQL execution.\n\nRun this in Supabase SQL Editor:\nALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;', 'warning');
            } catch (error) {
                showStatus(`❌ Error: ${error.message}`, 'error');
            }
        };

        // Auto-test on load if user is authenticated
        supabase.auth.onAuthStateChange((event, session) => {
            if (session) {
                showStatus(`👋 Welcome ${session.user.email}!\n\nYou can now test profile access.`, 'info');
            }
        });
    </script>
</body>
</html>
