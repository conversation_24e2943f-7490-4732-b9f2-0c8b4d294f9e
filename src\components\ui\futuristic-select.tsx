import * as React from "react"
import * as SelectPrimitive from "@radix-ui/react-select"
import { Check, ChevronDown, Zap, Users, Shield, Calculator, UserCog } from "lucide-react"
import { cn } from "@/lib/utils"

const FuturisticSelect = SelectPrimitive.Root

const FuturisticSelectGroup = SelectPrimitive.Group

const FuturisticSelectValue = SelectPrimitive.Value

const FuturisticSelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger> & {
    variant?: "role" | "department"
  }
>(({ className, children, variant = "role", ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      "group relative flex h-12 w-full items-center justify-between rounded-[30px] border-2 border-white/20 bg-gradient-to-r from-white/5 to-white/10 backdrop-blur-[20px] px-4 py-3 text-sm font-medium text-white transition-all duration-500 ease-out",
      "hover:border-blue-400/50 hover:from-blue-400/10 hover:to-blue-400/5 hover:shadow-lg",
      "focus:outline-none focus:border-blue-400 focus:from-blue-400/20 focus:to-blue-400/10 focus:shadow-xl",
      "disabled:cursor-not-allowed disabled:opacity-50",
      variant === "role" && "hover:border-red-400/50 hover:from-red-400/10 hover:to-red-400/5 focus:border-red-400 focus:from-red-400/20 focus:to-red-400/10",
      className
    )}
    {...props}
  >
    <div className="flex items-center gap-3">
      {variant === "role" && (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-[#ff1c04]/20 to-[#e01703]/20 backdrop-blur-sm">
          <Shield className="h-4 w-4 text-[#ff1c04]" />
        </div>
      )}
      {variant === "department" && (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-[#0FA0CE]/20 to-[#0FA0CE]/20 backdrop-blur-sm">
          <Users className="h-4 w-4 text-[#0FA0CE]" />
        </div>
      )}
      {children}
    </div>
    <SelectPrimitive.Icon asChild>
      <ChevronDown className="h-5 w-5 opacity-70 transition-transform duration-300 group-data-[state=open]:rotate-180" />
    </SelectPrimitive.Icon>

  </SelectPrimitive.Trigger>
))
FuturisticSelectTrigger.displayName = SelectPrimitive.Trigger.displayName

const FuturisticSelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = "popper", ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        "relative z-50 max-h-96 min-w-[200px] overflow-hidden rounded-[20px] border border-white/20 bg-black/90 backdrop-blur-[30px] text-white shadow-2xl data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
        position === "popper" &&
          "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      )}
      position={position}
      {...props}
    >
      <SelectPrimitive.Viewport
        className="p-2"
      >
        {children}
      </SelectPrimitive.Viewport>
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
))
FuturisticSelectContent.displayName = SelectPrimitive.Content.displayName

const FuturisticSelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item> & {
    icon?: React.ReactNode
    description?: string
  }
>(({ className, children, icon, description, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "group relative flex cursor-pointer select-none items-center rounded-[15px] px-3 py-3 text-sm outline-none transition-all duration-300",
      "hover:bg-white/10 hover:shadow-lg",
      "focus:bg-white/10 focus:shadow-lg",
      "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <div className="flex items-center gap-3 flex-1">
      {icon && (
        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-white/10 backdrop-blur-sm transition-all duration-300 group-hover:bg-white/20">
          {icon}
        </div>
      )}
      <div className="flex flex-col">
        <span className="font-medium">{children}</span>
        {description && (
          <span className="text-xs text-white/60 mt-0.5">{description}</span>
        )}
      </div>
    </div>
    
    <SelectPrimitive.ItemIndicator className="flex h-5 w-5 items-center justify-center">
      <Check className="h-4 w-4 text-[#0FA0CE]" />
    </SelectPrimitive.ItemIndicator>

  </SelectPrimitive.Item>
))
FuturisticSelectItem.displayName = SelectPrimitive.Item.displayName

// Role icons mapping
export const roleIcons = {
  staff: <Users className="h-4 w-4 text-blue-400" />,
  manager: <UserCog className="h-4 w-4 text-green-400" />,
  admin: <Shield className="h-4 w-4 text-red-400" />,
  accountant: <Calculator className="h-4 w-4 text-yellow-400" />,
  "staff-admin": <Zap className="h-4 w-4 text-purple-400" />
}

// Role descriptions
export const roleDescriptions = {
  staff: "Standard user access with basic permissions",
  manager: "Team management and project oversight",
  admin: "Full system administration privileges",
  accountant: "Financial management and reporting",
  "staff-admin": "Enhanced staff privileges with admin features"
}

export {
  FuturisticSelect,
  FuturisticSelectGroup,
  FuturisticSelectValue,
  FuturisticSelectTrigger,
  FuturisticSelectContent,
  FuturisticSelectItem,
}
