import { LocationData, LocationService } from '@/types/timeTracking';

class LocationServiceImpl implements LocationService {
  private watchId: number | null = null;

  async getCurrentLocation(): Promise<LocationData> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          
          try {
            const address = await this.reverseGeocode(latitude, longitude);
            resolve({
              latitude,
              longitude,
              address,
              accuracy,
              method: 'gps',
            });
          } catch (error) {
            // If reverse geocoding fails, still return coordinates
            resolve({
              latitude,
              longitude,
              address: 'Address unavailable',
              accuracy,
              method: 'gps',
            });
          }
        },
        (error) => {
          let errorMessage = 'Location access denied';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
          }
          
          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000, // 1 minute
        }
      );
    });
  }

  watchPosition(callback: (location: LocationData) => void): number {
    if (!navigator.geolocation) {
      throw new Error('Geolocation is not supported by this browser');
    }

    this.watchId = navigator.geolocation.watchPosition(
      async (position) => {
        const { latitude, longitude, accuracy } = position.coords;
        
        try {
          const address = await this.reverseGeocode(latitude, longitude);
          callback({
            latitude,
            longitude,
            address,
            accuracy,
            method: 'gps',
          });
        } catch (error) {
          callback({
            latitude,
            longitude,
            address: 'Address unavailable',
            accuracy,
            method: 'gps',
          });
        }
      },
      (error) => {
        console.error('Location watch error:', error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 30000, // 30 seconds
      }
    );

    return this.watchId;
  }

  clearWatch(watchId: number): void {
    if (navigator.geolocation) {
      navigator.geolocation.clearWatch(watchId);
    }
  }

  async reverseGeocode(lat: number, lng: number): Promise<string> {
    try {
      // Try multiple geocoding services for better reliability
      // Note: Nominatim is commented out due to CORS issues in browser
      const services = [
        // {
        //   name: 'nominatim',
        //   url: `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`,
        //   headers: { 'User-Agent': 'TimeTrackingApp/1.0' }
        // },
        {
          name: 'bigdatacloud',
          url: `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${lat}&longitude=${lng}&localityLanguage=en`,
          headers: {}
        }
      ];

      for (const service of services) {
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

          const response = await fetch(service.url, {
            headers: service.headers,
            signal: controller.signal
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            console.warn(`${service.name} geocoding service returned ${response.status}`);
            continue;
          }

          const data = await response.json();

          if (service.name === 'nominatim') {
            if (data.display_name) {
              return this.formatNominatimAddress(data);
            }
          } else if (service.name === 'bigdatacloud') {
            if (data.locality || data.city) {
              return this.formatBigDataCloudAddress(data);
            }
          }
        } catch (error) {
          console.warn(`${service.name} geocoding failed:`, error);
          continue;
        }
      }

      // If all services fail, return coordinates
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    } catch (error) {
      console.error('All reverse geocoding services failed:', error);
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
    }
  }

  private formatNominatimAddress(data: any): string {
    if (data.display_name) {
      // Clean up the display name to be more concise
      const parts = data.display_name.split(', ');
      // Take first 3-4 meaningful parts
      return parts.slice(0, Math.min(4, parts.length)).join(', ');
    }

    // Fallback: construct address from components
    const address = data.address;
    if (address) {
      const parts = [
        address.house_number,
        address.road,
        address.neighbourhood || address.suburb,
        address.city || address.town || address.village,
        address.state,
      ].filter(Boolean);

      return parts.slice(0, 3).join(', ');
    }

    return 'Address unavailable';
  }

  private formatBigDataCloudAddress(data: any): string {
    const parts = [
      data.locality,
      data.city,
      data.principalSubdivision,
      data.countryName,
    ].filter(Boolean);

    return parts.slice(0, 3).join(', ');
  }

  // Get approximate location using IP (fallback method)
  async getLocationByIP(): Promise<LocationData> {
    try {
      const response = await fetch('https://ipapi.co/json/');
      const data = await response.json();
      
      if (data.latitude && data.longitude) {
        return {
          latitude: data.latitude,
          longitude: data.longitude,
          address: `${data.city}, ${data.region}, ${data.country_name}`,
          accuracy: 10000, // IP-based location is very inaccurate
          method: 'network',
        };
      }
      
      throw new Error('IP location unavailable');
    } catch (error) {
      throw new Error('Unable to determine location');
    }
  }

  // Check if location services are available
  isLocationAvailable(): boolean {
    return 'geolocation' in navigator;
  }

  // Request location permission
  async requestLocationPermission(): Promise<boolean> {
    if (!this.isLocationAvailable()) {
      return false;
    }

    try {
      const permission = await navigator.permissions.query({ name: 'geolocation' });
      return permission.state === 'granted';
    } catch (error) {
      // Fallback: try to get location to test permission
      try {
        await this.getCurrentLocation();
        return true;
      } catch {
        return false;
      }
    }
  }

  // Calculate distance between two points (in meters)
  calculateDistance(
    lat1: number,
    lng1: number,
    lat2: number,
    lng2: number
  ): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = (lat1 * Math.PI) / 180;
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lng2 - lng1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  // Check if user is within a geofence
  isWithinGeofence(
    userLat: number,
    userLng: number,
    centerLat: number,
    centerLng: number,
    radiusMeters: number
  ): boolean {
    const distance = this.calculateDistance(userLat, userLng, centerLat, centerLng);
    return distance <= radiusMeters;
  }

  // Enhanced location capture with multiple attempts and fallbacks
  async getEnhancedLocation(): Promise<LocationData> {
    const attempts = 3;
    let lastError: Error | null = null;

    for (let i = 0; i < attempts; i++) {
      try {
        const location = await this.getCurrentLocation();

        // Validate location accuracy
        if (location.accuracy && location.accuracy > 100) {
          console.warn(`Location accuracy is low: ${location.accuracy}m`);

          // If accuracy is poor and we have more attempts, try again
          if (i < attempts - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            continue;
          }
        }

        return location;
      } catch (error) {
        lastError = error as Error;
        console.warn(`Location attempt ${i + 1} failed:`, error);

        // Wait before retry
        if (i < attempts - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    // If GPS fails, try IP-based location as fallback
    try {
      console.log('Falling back to IP-based location');
      return await this.getLocationByIP();
    } catch (error) {
      throw lastError || new Error('Unable to determine location');
    }
  }

  // Get location with specific accuracy requirements
  async getHighAccuracyLocation(maxAccuracy: number = 50): Promise<LocationData> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      let attempts = 0;
      const maxAttempts = 5;
      const timeout = 30000; // 30 seconds total timeout

      const tryGetLocation = () => {
        attempts++;

        navigator.geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude, accuracy } = position.coords;

            if (accuracy && accuracy <= maxAccuracy) {
              try {
                const address = await this.reverseGeocode(latitude, longitude);
                resolve({
                  latitude,
                  longitude,
                  address,
                  accuracy,
                  method: 'gps',
                });
              } catch (error) {
                resolve({
                  latitude,
                  longitude,
                  address: 'Address unavailable',
                  accuracy,
                  method: 'gps',
                });
              }
            } else if (attempts < maxAttempts) {
              console.log(`Accuracy ${accuracy}m not good enough, retrying...`);
              setTimeout(tryGetLocation, 2000);
            } else {
              // Accept the best we got
              try {
                const address = await this.reverseGeocode(latitude, longitude);
                resolve({
                  latitude,
                  longitude,
                  address,
                  accuracy,
                  method: 'gps',
                });
              } catch (error) {
                resolve({
                  latitude,
                  longitude,
                  address: 'Address unavailable',
                  accuracy,
                  method: 'gps',
                });
              }
            }
          },
          (error) => {
            if (attempts < maxAttempts) {
              setTimeout(tryGetLocation, 2000);
            } else {
              reject(error);
            }
          },
          {
            enableHighAccuracy: true,
            timeout: timeout / maxAttempts,
            maximumAge: 0, // Don't use cached location
          }
        );
      };

      tryGetLocation();
    });
  }

  // Continuous location tracking for work sessions
  startLocationTracking(
    callback: (location: LocationData) => void,
    options: {
      interval?: number; // milliseconds
      accuracy?: number; // meters
      significantChange?: number; // meters
    } = {}
  ): number {
    const {
      interval = 300000, // 5 minutes default
      accuracy = 100,
      significantChange = 50
    } = options;

    let lastLocation: LocationData | null = null;
    let watchId: number | null = null;

    const trackLocation = async () => {
      try {
        const location = await this.getCurrentLocation();

        // Check if location has changed significantly
        if (lastLocation && significantChange > 0) {
          const distance = this.calculateDistance(
            lastLocation.latitude,
            lastLocation.longitude,
            location.latitude,
            location.longitude
          );

          if (distance < significantChange) {
            return; // No significant change, don't update
          }
        }

        lastLocation = location;
        callback(location);
      } catch (error) {
        console.error('Location tracking error:', error);
      }
    };

    // Initial location
    trackLocation();

    // Set up interval tracking
    const intervalId = setInterval(trackLocation, interval);

    // Also set up continuous watching for more responsive updates
    if (navigator.geolocation) {
      watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const { latitude, longitude, accuracy: posAccuracy } = position.coords;

          if (posAccuracy && posAccuracy <= accuracy) {
            try {
              const address = await this.reverseGeocode(latitude, longitude);
              const location: LocationData = {
                latitude,
                longitude,
                address,
                accuracy: posAccuracy,
                method: 'gps',
              };

              // Check significant change
              if (lastLocation && significantChange > 0) {
                const distance = this.calculateDistance(
                  lastLocation.latitude,
                  lastLocation.longitude,
                  location.latitude,
                  location.longitude
                );

                if (distance >= significantChange) {
                  lastLocation = location;
                  callback(location);
                }
              } else {
                lastLocation = location;
                callback(location);
              }
            } catch (error) {
              console.error('Watch position geocoding error:', error);
            }
          }
        },
        (error) => {
          console.error('Watch position error:', error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000,
        }
      );
    }

    // Return a combined ID for cleanup
    return parseInt(`${intervalId}${watchId || 0}`);
  }

  // Stop location tracking
  stopLocationTracking(trackingId: number): void {
    const intervalId = Math.floor(trackingId / 1000);
    const watchId = trackingId % 1000;

    clearInterval(intervalId);

    if (watchId > 0 && navigator.geolocation) {
      navigator.geolocation.clearWatch(watchId);
    }
  }
}

export const locationService = new LocationServiceImpl();
