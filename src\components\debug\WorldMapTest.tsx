import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';

export const WorldMapTest: React.FC = () => {
  return (
    <div className="p-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>World Map Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Direct PNG Test */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Direct PNG Image</h3>
            <div className="w-64 h-32 border border-gray-300 rounded-lg overflow-hidden">
              <img
                src="/images/world-map.png"
                alt="World Map PNG Direct"
                className="w-full h-full object-cover"
                onLoad={() => console.log('✅ Direct PNG loaded')}
                onError={() => console.error('❌ Direct PNG failed')}
              />
            </div>
          </div>

          {/* Direct SVG Test (Legacy) */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Direct SVG Image (Legacy)</h3>
            <div className="w-64 h-32 border border-gray-300 rounded-lg overflow-hidden">
              <img
                src="/images/world-map-red.svg"
                alt="World Map SVG Direct"
                className="w-full h-full object-contain"
                onLoad={() => console.log('✅ Direct SVG loaded')}
                onError={() => console.error('❌ Direct SVG failed')}
              />
            </div>
          </div>

          {/* In Red Background Test */}
          <div>
            <h3 className="text-lg font-semibold mb-2">SVG on Red Background</h3>
            <div className="w-64 h-32 bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-lg overflow-hidden relative">
              <img 
                src="/images/world-map-red.svg" 
                alt="World Map on Red" 
                className="w-full h-full object-contain opacity-60"
                style={{ 
                  filter: 'brightness(2) contrast(1.2) saturate(0.8)',
                  mixBlendMode: 'overlay'
                }}
                onLoad={() => console.log('✅ Red background SVG loaded')}
                onError={() => console.error('❌ Red background SVG failed')}
              />
            </div>
          </div>

          {/* 3D PIN Style Test with PNG */}
          <div>
            <h3 className="text-lg font-semibold mb-2">3D PIN Style (New PNG)</h3>
            <div className="w-64 h-32 rounded-lg overflow-hidden relative">
              <div className="absolute inset-0 flex items-center justify-center">
                <img
                  src="/images/world-map.png"
                  alt="World Map 3D PIN PNG Style"
                  className="w-full h-full object-cover rounded-lg"
                  style={{
                    filter: 'brightness(0.7) contrast(1.1) saturate(1.2)',
                  }}
                  onLoad={() => console.log('✅ 3D PIN style PNG loaded')}
                  onError={() => console.error('❌ 3D PIN style PNG failed')}
                />
              </div>

              <div className="absolute inset-0 bg-gradient-to-br from-black/40 via-transparent to-black/50 rounded-lg"></div>

              <div className="absolute bottom-2 left-2 text-white text-xs drop-shadow-lg z-10">
                CTNL AI Work-Board
              </div>

              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div className="relative">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse shadow-lg"></div>
                  <div className="absolute inset-0 w-2 h-2 bg-red-400 rounded-full animate-ping opacity-75"></div>
                </div>
              </div>
            </div>
          </div>

          {/* 3D PIN Style Test with SVG (Legacy) */}
          <div>
            <h3 className="text-lg font-semibold mb-2">3D PIN Style (Legacy SVG)</h3>
            <div className="w-64 h-32 bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-lg overflow-hidden relative">
              <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-transparent"></div>

              <div className="absolute inset-0 flex items-center justify-center opacity-60">
                <img
                  src="/images/world-map-red.svg"
                  alt="World Map 3D PIN SVG Style"
                  className="w-full h-full object-contain scale-110"
                  style={{
                    filter: 'brightness(2) contrast(1.2) saturate(0.8)',
                    mixBlendMode: 'overlay'
                  }}
                  onLoad={() => console.log('✅ 3D PIN style SVG loaded')}
                  onError={() => console.error('❌ 3D PIN style SVG failed')}
                />
              </div>

              <div className="absolute bottom-2 left-2 text-white text-xs">
                CTNL AI Work-Board (SVG)
              </div>

              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div className="relative">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 w-2 h-2 bg-white rounded-full animate-ping opacity-75"></div>
                </div>
              </div>
            </div>
          </div>

          {/* CSS Fallback Test */}
          <div>
            <h3 className="text-lg font-semibold mb-2">CSS Fallback Map</h3>
            <div className="w-64 h-32 bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-lg overflow-hidden relative">
              <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-transparent"></div>
              
              <div className="w-full h-full relative opacity-60">
                {/* Continents as simple shapes */}
                <div className="absolute top-4 left-2 w-8 h-6 bg-white/40 rounded-lg transform rotate-12"></div> {/* North America */}
                <div className="absolute top-8 left-4 w-4 h-8 bg-white/40 rounded-lg transform rotate-6"></div> {/* South America */}
                <div className="absolute top-2 left-12 w-6 h-4 bg-white/40 rounded-lg"></div> {/* Europe */}
                <div className="absolute top-6 left-14 w-5 h-10 bg-white/40 rounded-lg transform -rotate-3"></div> {/* Africa */}
                <div className="absolute top-1 left-18 w-12 h-6 bg-white/40 rounded-lg transform rotate-3"></div> {/* Asia */}
                <div className="absolute bottom-4 right-6 w-6 h-4 bg-white/40 rounded-lg"></div> {/* Australia */}
                
                {/* Grid lines */}
                <div className="absolute inset-0 opacity-30">
                  <div className="absolute top-1/3 left-0 right-0 h-px bg-white/50"></div>
                  <div className="absolute top-2/3 left-0 right-0 h-px bg-white/50"></div>
                  <div className="absolute top-0 bottom-0 left-1/3 w-px bg-white/50"></div>
                  <div className="absolute top-0 bottom-0 right-1/3 w-px bg-white/50"></div>
                </div>
              </div>
              
              <div className="absolute bottom-2 left-2 text-white text-xs">
                CSS Fallback Map
              </div>
            </div>
          </div>

          {/* Image Path Test */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Image Path Test</h3>
            <div className="text-sm space-y-1">
              <p><strong>Expected path:</strong> /images/world-map-red.svg</p>
              <p><strong>Full URL:</strong> {window.location.origin}/images/world-map-red.svg</p>
              <button 
                className="px-3 py-1 bg-blue-500 text-white rounded text-xs"
                onClick={() => {
                  fetch('/images/world-map-red.svg')
                    .then(response => {
                      console.log('🔍 SVG fetch response:', response.status, response.statusText);
                      if (response.ok) {
                        console.log('✅ SVG file exists and is accessible');
                      } else {
                        console.error('❌ SVG file not found or not accessible');
                      }
                    })
                    .catch(error => {
                      console.error('❌ SVG fetch error:', error);
                    });
                }}
              >
                Test SVG Accessibility
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
