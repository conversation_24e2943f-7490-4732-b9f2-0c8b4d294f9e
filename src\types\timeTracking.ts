// Enhanced Time Tracking Types

export interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  accuracy?: number;
  method?: 'gps' | 'network' | 'manual';
}

export interface DeviceInfo {
  type: 'mobile' | 'desktop' | 'tablet' | 'unknown';
  browser?: string;
  userAgent?: string;
  ipAddress?: string;
  platform?: string;
  brand?: string;
  model?: string;
  os?: string;
}

export interface TimeLogEntry {
  id: string;
  user_id: string;
  clock_in: string;
  clock_out?: string | null;
  clock_in_timestamp: string;
  clock_out_timestamp?: string | null;
  total_hours?: number | null;
  latitude?: number | null;
  longitude?: number | null;
  location_address?: string | null;
  location_latitude?: number | null;
  location_longitude?: number | null;
  location_accuracy?: number | null;
  location_method?: string;
  date: string;
  project_id?: string | null;
  task_id?: string | null;
  notes?: string | null;
  device_info?: Record<string, any>;
  device_type?: string;
  browser_info?: string;
  ip_address?: string;
  timezone?: string;
  clock_in_method?: string;
  clock_out_method?: string;
  status?: 'active' | 'break' | 'lunch' | 'overtime';
  break_duration?: number;
  overtime_hours?: number;
  is_remote?: boolean;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name: string;
    department?: {
      name: string;
    };
  };
}

export interface RealtimeClockStatus {
  user_id: string;
  user_name: string;
  department_name: string;
  is_clocked_in: boolean;
  clock_in_time?: string | null;
  duration_hours: number;
  location_address: string;
  device_type: string;
  status: string;
  is_remote: boolean;
  latitude?: number | null;
  longitude?: number | null;
}

export interface TeamTimeLog {
  log_id: string;
  user_id: string;
  user_name: string;
  department_name: string;
  clock_in: string;
  clock_out?: string | null;
  total_hours: number;
  location_address: string;
  device_type: string;
  status: string;
  is_remote: boolean;
}

export interface AttendanceStats {
  total_employees: number;
  clocked_in_count: number;
  on_break_count: number;
  remote_count: number;
  average_hours: number;
  late_arrivals: number;
  early_departures: number;
}

export interface ClockInRequest {
  location: LocationData;
  device: DeviceInfo;
  timezone?: string;
  method?: 'manual' | 'auto' | 'geofence';
  project_id?: string;
  task_id?: string;
  notes?: string;
}

export interface ClockOutRequest {
  location?: LocationData;
  device?: DeviceInfo;
  method?: 'manual' | 'auto' | 'geofence';
  notes?: string;
  break_duration?: number;
  overtime_hours?: number;
}

export interface TimeCardProps {
  userRole: 'staff' | 'manager' | 'admin' | 'staff-admin' | 'accountant';
  showTeamData?: boolean;
  departmentFilter?: string;
  compact?: boolean;
  showControls?: boolean;
}

export interface TimeTrackingHookOptions {
  userId?: string;
  departmentId?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  realtime?: boolean;
  refetchInterval?: number;
}

export interface TimeTrackingState {
  currentSession?: TimeLogEntry | null;
  isLoading: boolean;
  error?: string | null;
  realtimeStatus?: RealtimeClockStatus[];
  teamLogs?: TeamTimeLog[];
  stats?: AttendanceStats;
}

export interface TimeTrackingActions {
  clockIn: (request: ClockInRequest) => Promise<TimeLogEntry>;
  clockOut: (sessionId: string, request?: ClockOutRequest) => Promise<TimeLogEntry>;
  updateStatus: (sessionId: string, status: TimeLogEntry['status']) => Promise<void>;
  addBreak: (sessionId: string, duration: number) => Promise<void>;
  addNote: (sessionId: string, note: string) => Promise<void>;
  refreshData: () => Promise<void>;
}

export interface LocationService {
  getCurrentLocation: () => Promise<LocationData>;
  watchPosition: (callback: (location: LocationData) => void) => number;
  clearWatch: (watchId: number) => void;
  reverseGeocode: (lat: number, lng: number) => Promise<string>;
}

export interface DeviceService {
  getDeviceInfo: () => DeviceInfo;
  getBrowserInfo: () => string;
  getIPAddress: () => Promise<string>;
  detectPlatform: () => string;
}

// Utility types for API responses
export type TimeLogResponse = {
  data: TimeLogEntry[];
  count?: number;
  error?: string;
};

export type RealtimeStatusResponse = {
  data: RealtimeClockStatus[];
  error?: string;
};

export type AttendanceStatsResponse = {
  data: AttendanceStats;
  error?: string;
};

// Event types for real-time updates
export interface TimeTrackingEvent {
  type: 'clock_in' | 'clock_out' | 'status_change' | 'break_start' | 'break_end';
  userId: string;
  timestamp: string;
  data: Partial<TimeLogEntry>;
}

// Filter and sort options
export interface TimeLogFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  department?: string;
  status?: TimeLogEntry['status'];
  isRemote?: boolean;
  deviceType?: DeviceInfo['type'];
}

export interface TimeLogSortOptions {
  field: keyof TimeLogEntry;
  direction: 'asc' | 'desc';
}

// Export configuration
export interface TimeTrackingConfig {
  workStartTime: string; // HH:MM format
  workEndTime: string;
  breakDuration: number; // in minutes
  overtimeThreshold: number; // in hours
  locationRequired: boolean;
  deviceTrackingEnabled: boolean;
  geofenceEnabled: boolean;
  autoClockOut: boolean;
  autoClockOutTime: string; // HH:MM format
}
