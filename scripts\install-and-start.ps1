# PowerShell script to install dependencies and start the development server
# Run this script from the project root directory

Write-Host "🚀 AI CTNL Dashboard - Install Dependencies and Start Server" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Green

# Check if Node.js is installed
Write-Host "📋 Checking Node.js installation..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js 20+ first." -ForegroundColor Red
    Write-Host "Download from: https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check if npm is installed
Write-Host "📋 Checking npm installation..." -ForegroundColor Yellow
try {
    $npmVersion = npm --version
    Write-Host "✅ npm version: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm is not installed." -ForegroundColor Red
    exit 1
}

# Clear cache and clean install
Write-Host "🧹 Clearing cache and cleaning up..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
    Write-Host "✅ Removed node_modules" -ForegroundColor Green
}

if (Test-Path "package-lock.json") {
    Remove-Item -Force "package-lock.json"
    Write-Host "✅ Removed package-lock.json" -ForegroundColor Green
}

if (Test-Path ".vite") {
    Remove-Item -Recurse -Force ".vite"
    Write-Host "✅ Removed .vite cache" -ForegroundColor Green
}

if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "✅ Removed dist folder" -ForegroundColor Green
}

# Install dependencies
Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
Write-Host "This may take a few minutes..." -ForegroundColor Gray

try {
    npm install
    Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    Write-Host "Trying with --legacy-peer-deps..." -ForegroundColor Yellow
    try {
        npm install --legacy-peer-deps
        Write-Host "✅ Dependencies installed with legacy peer deps" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to install dependencies even with legacy peer deps" -ForegroundColor Red
        Write-Host "Please check your internet connection and try again." -ForegroundColor Yellow
        exit 1
    }
}

# Check if .env file exists
Write-Host "🔧 Checking environment configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    Write-Host "⚠️ .env file not found. Creating template..." -ForegroundColor Yellow
    
    $envTemplate = @"
# Supabase Configuration
VITE_SUPABASE_URL=https://dvflgnqwbsjityrowatf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28

# OpenAI Configuration (Optional)
VITE_OPENAI_API_KEY=your_openai_api_key_here

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=AI CTNL Dashboard

# Development
NODE_ENV=development
"@
    
    $envTemplate | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "✅ Created .env template file" -ForegroundColor Green
    Write-Host "📝 Please update the .env file with your actual API keys if needed" -ForegroundColor Yellow
} else {
    Write-Host "✅ .env file found" -ForegroundColor Green
}

# Display project information
Write-Host ""
Write-Host "📊 Project Information:" -ForegroundColor Cyan
Write-Host "- Project: AI CTNL Dashboard" -ForegroundColor White
Write-Host "- Framework: React + TypeScript + Vite" -ForegroundColor White
Write-Host "- UI: Tailwind CSS + shadcn/ui" -ForegroundColor White
Write-Host "- Backend: Supabase" -ForegroundColor White
Write-Host "- Port: 5173 (default)" -ForegroundColor White

Write-Host ""
Write-Host "🔧 Available Fix Tools:" -ForegroundColor Cyan
Write-Host "- Dashboard Fix: http://localhost:5173/fix-dashboard-complete.html" -ForegroundColor White
Write-Host "- Profile Roles Fix: http://localhost:5173/fix-profile-roles.html" -ForegroundColor White
Write-Host "- Activities Fix: http://localhost:5173/fix-activities.html" -ForegroundColor White

Write-Host ""
Write-Host "🚀 Starting development server..." -ForegroundColor Green
Write-Host "The dashboard will open in your browser automatically." -ForegroundColor Gray
Write-Host "Press Ctrl+C to stop the server." -ForegroundColor Gray
Write-Host ""

# Start the development server
try {
    npm run dev
} catch {
    Write-Host "❌ Failed to start development server" -ForegroundColor Red
    Write-Host "Please check the error messages above and try again." -ForegroundColor Yellow
    exit 1
}
