-- ============================================================================
-- COMPLETE CLEANUP WITH FOREIGN KEY HANDLING
-- This handles all foreign key constraints before deleting users
-- ============================================================================

-- STEP 1: Disable RLS on all tables
ALTER TABLE IF EXISTS public.profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.time_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.projects DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.departments DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.document_analysis DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.ai_conversations DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_members DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.project_assignments DISABLE ROW LEVEL SECURITY;

-- STEP 2: Show current state before cleanup
SELECT 'BEFORE CLEANUP:' as info;
SELECT 'Auth users:' as table_name, COUNT(*) as count FROM auth.users
UNION ALL
SELECT 'Profiles:' as table_name, COUNT(*) as count FROM public.profiles
UNION ALL
SELECT 'Document analysis:' as table_name, COUNT(*) as count FROM public.document_analysis
UNION ALL
SELECT 'Time logs:' as table_name, COUNT(*) as count FROM public.time_logs
UNION ALL
SELECT 'Projects:' as table_name, COUNT(*) as count FROM public.projects
UNION ALL
SELECT 'Notifications:' as table_name, COUNT(*) as count FROM public.notifications
UNION ALL
SELECT 'AI conversations:' as table_name, COUNT(*) as count FROM public.ai_conversations;

-- STEP 3: Delete all data that references users (in correct order)

-- Delete document analysis records first
DELETE FROM public.document_analysis;

-- Delete AI conversations
DELETE FROM public.ai_conversations;

-- Delete notifications
DELETE FROM public.notifications;

-- Delete time logs
DELETE FROM public.time_logs;

-- Delete project members and assignments
DELETE FROM public.project_members;
DELETE FROM public.project_assignments;

-- Delete any other tables that might reference users
DELETE FROM public.memos WHERE TRUE;
DELETE FROM public.expenses WHERE TRUE;
DELETE FROM public.invoices WHERE TRUE;
DELETE FROM public.assets WHERE TRUE;
DELETE FROM public.fleet_vehicles WHERE TRUE;
DELETE FROM public.procurement_requests WHERE TRUE;

-- Update projects to remove user references
UPDATE public.projects SET created_by = NULL WHERE created_by IS NOT NULL;
UPDATE public.projects SET updated_by = NULL WHERE updated_by IS NOT NULL;
UPDATE public.projects SET assigned_to = NULL WHERE assigned_to IS NOT NULL;

-- Update departments to remove user references
UPDATE public.departments SET manager_id = NULL WHERE manager_id IS NOT NULL;
UPDATE public.departments SET created_by = NULL WHERE created_by IS NOT NULL;
UPDATE public.departments SET updated_by = NULL WHERE updated_by IS NOT NULL;

-- STEP 4: Now delete profiles
DELETE FROM public.profiles;

-- STEP 5: Delete authentication data
DELETE FROM auth.sessions;
DELETE FROM auth.refresh_tokens;
DELETE FROM auth.audit_log_entries;
DELETE FROM auth.identities;

-- STEP 6: Finally delete users
DELETE FROM auth.users;

-- STEP 7: Reset sequences if they exist
DO $$
DECLARE
    seq_name TEXT;
BEGIN
    FOR seq_name IN 
        SELECT sequence_name 
        FROM information_schema.sequences 
        WHERE sequence_schema = 'public'
    LOOP
        EXECUTE 'SELECT setval(''' || seq_name || ''', 1, false)';
    END LOOP;
END $$;

-- STEP 8: Drop ALL existing RLS policies on profiles
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_manager_department" ON public.profiles;
DROP POLICY IF EXISTS "profiles_hr_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_own" ON public.profiles;
DROP POLICY IF EXISTS "Staff-admin can view profiles" ON public.profiles;
DROP POLICY IF EXISTS "profiles_read_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_update" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;

-- STEP 9: Re-enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- STEP 10: Create fresh, clean RLS policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- STEP 11: Grant clean permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;
GRANT ALL ON public.profiles TO service_role;

-- STEP 12: Create/replace profile creation function
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
    'active',
    NOW(),
    NOW()
  );
  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 13: Recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- STEP 14: Verify complete cleanup
SELECT 'AFTER CLEANUP:' as info;
SELECT 'Auth users:' as table_name, COUNT(*) as count FROM auth.users
UNION ALL
SELECT 'Profiles:' as table_name, COUNT(*) as count FROM public.profiles
UNION ALL
SELECT 'Document analysis:' as table_name, COUNT(*) as count FROM public.document_analysis
UNION ALL
SELECT 'Time logs:' as table_name, COUNT(*) as count FROM public.time_logs
UNION ALL
SELECT 'Projects:' as table_name, COUNT(*) as count FROM public.projects
UNION ALL
SELECT 'Notifications:' as table_name, COUNT(*) as count FROM public.notifications
UNION ALL
SELECT 'AI conversations:' as table_name, COUNT(*) as count FROM public.ai_conversations;

-- STEP 15: Show RLS status
SELECT 'RLS STATUS:' as info;
SELECT schemaname, tablename, rowsecurity,
       (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public') as policy_count
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- STEP 16: List active policies
SELECT 'ACTIVE POLICIES:' as info;
SELECT policyname, cmd as operation
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;

SELECT 'COMPLETE CLEANUP FINISHED - ALL FOREIGN KEY CONSTRAINTS HANDLED!' as final_status;
SELECT 'Database is now completely clean and ready for fresh users.' as instruction;
