import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { withCors, createCorsResponse } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

const handler = async (req: Request): Promise<Response> => {
  try {
    const body = await req.json();
    const { sql } = body;

    if (!sql) {
      return createCorsResponse(
        { error: 'Missing required field: sql' },
        400
      );
    }

    // Execute the SQL using the service role
    const { data, error } = await supabase.rpc('exec_sql', { sql });

    if (error) {
      console.error('SQL execution error:', error);
      return createCorsResponse(
        { error: error.message },
        500
      );
    }

    return createCorsResponse({
      success: true,
      data,
      message: 'SQL executed successfully'
    });

  } catch (error) {
    console.error('Handler error:', error);
    return createCorsResponse(
      { error: error.message || 'Internal server error' },
      500
    );
  }
};

Deno.serve(withCors(handler));
