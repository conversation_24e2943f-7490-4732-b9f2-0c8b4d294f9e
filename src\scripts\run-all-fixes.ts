import { supabase } from '@/integrations/supabase/client';

/**
 * Complete System Fix - Runs all dashboard and database fixes
 * This script will fix all issues in the correct order
 */

const logStep = (message: string) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message: string) => {
  console.log(`✅ ${message}`);
};

const logError = (message: string) => {
  console.error(`❌ ${message}`);
};

// Step 1: Fix Profile Roles and RLS
const fixProfileRoles = async () => {
  logStep('Step 1: Fixing profile roles and RLS policies...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop existing role constraint if it exists
        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
        
        -- Add comprehensive role constraint
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_role_check 
          CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
        
        -- Drop existing account_type constraint if it exists
        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_account_type_check;
        
        -- Add comprehensive account_type constraint
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_account_type_check 
          CHECK (account_type IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
        
        -- Ensure all required columns exist
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff';
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC';
        
        -- Drop all existing profile policies
        DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
        
        -- Enable RLS
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
        
        -- Create new profile policies
        CREATE POLICY "profiles_select_own" ON public.profiles
          FOR SELECT USING (auth.uid() = id);
        
        CREATE POLICY "profiles_select_admin" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'hr', 'staff-admin')
            )
          );
        
        CREATE POLICY "profiles_insert_own" ON public.profiles
          FOR INSERT WITH CHECK (auth.uid() = id);
        
        CREATE POLICY "profiles_update_own" ON public.profiles
          FOR UPDATE USING (auth.uid() = id);
        
        CREATE POLICY "profiles_update_admin" ON public.profiles
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
          );
      `
    });

    if (error) throw error;
    logSuccess('Profile roles and RLS policies fixed');
    return true;
  } catch (error) {
    logError(`Profile roles fix failed: ${error.message}`);
    return false;
  }
};

// Step 2: Create Dashboard Tables
const createDashboardTables = async () => {
  logStep('Step 2: Creating dashboard tables...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create invoices table
        CREATE TABLE IF NOT EXISTS public.invoices (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          invoice_number VARCHAR(50) UNIQUE NOT NULL,
          client_name TEXT NOT NULL,
          amount DECIMAL(10,2) NOT NULL,
          total_amount DECIMAL(10,2) NOT NULL,
          payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'overdue', 'cancelled')),
          due_date DATE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL
        );

        -- Create expense_reports table
        CREATE TABLE IF NOT EXISTS public.expense_reports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          amount DECIMAL(10,2) NOT NULL,
          category TEXT NOT NULL,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
          approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
          submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          receipt_url TEXT,
          expense_date DATE DEFAULT CURRENT_DATE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create reports table
        CREATE TABLE IF NOT EXISTS public.reports (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          title TEXT NOT NULL,
          description TEXT,
          report_type TEXT DEFAULT 'general' CHECK (report_type IN ('general', 'financial', 'project', 'maintenance', 'incident', 'performance')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'completed')),
          submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          reviewed_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
          project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
          due_date DATE,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create time_logs table
        CREATE TABLE IF NOT EXISTS public.time_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
          task_id UUID REFERENCES public.tasks(id) ON DELETE SET NULL,
          description TEXT,
          hours_worked DECIMAL(5,2) NOT NULL,
          log_date DATE DEFAULT CURRENT_DATE,
          billable BOOLEAN DEFAULT true,
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'submitted', 'approved')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create notifications table
        CREATE TABLE IF NOT EXISTS public.notifications (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          title TEXT NOT NULL,
          message TEXT NOT NULL,
          type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
          read BOOLEAN DEFAULT false,
          action_url TEXT,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create system_activities table
        CREATE TABLE IF NOT EXISTS public.system_activities (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
          action VARCHAR(100) NOT NULL,
          description TEXT,
          entity_type VARCHAR(100),
          entity_id UUID,
          metadata JSONB DEFAULT '{}',
          severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
          category VARCHAR(50) DEFAULT 'general',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON public.invoices(payment_status);
        CREATE INDEX IF NOT EXISTS idx_expense_reports_status ON public.expense_reports(status);
        CREATE INDEX IF NOT EXISTS idx_reports_status ON public.reports(status);
        CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
        CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
        CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON public.system_activities(user_id);
      `
    });

    if (error) throw error;
    logSuccess('Dashboard tables created');
    return true;
  } catch (error) {
    logError(`Dashboard tables creation failed: ${error.message}`);
    return false;
  }
};

// Step 3: Fix RLS Policies for Dashboard Tables
const fixDashboardRLS = async () => {
  logStep('Step 3: Fixing RLS policies for dashboard tables...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on all tables
        ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.expense_reports ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.system_activities ENABLE ROW LEVEL SECURITY;

        -- Drop existing policies
        DROP POLICY IF EXISTS "invoices_select_admin" ON public.invoices;
        DROP POLICY IF EXISTS "expense_reports_select_own" ON public.expense_reports;
        DROP POLICY IF EXISTS "reports_select_own" ON public.reports;
        DROP POLICY IF EXISTS "time_logs_select_own" ON public.time_logs;
        DROP POLICY IF EXISTS "notifications_select_own" ON public.notifications;
        DROP POLICY IF EXISTS "system_activities_select_admin" ON public.system_activities;

        -- INVOICES POLICIES
        CREATE POLICY "invoices_select_admin_accountant" ON public.invoices
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
            )
          );

        CREATE POLICY "invoices_insert_admin_accountant" ON public.invoices
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'accountant')
            )
          );

        -- EXPENSE REPORTS POLICIES
        CREATE POLICY "expense_reports_select_own_or_admin" ON public.expense_reports
          FOR SELECT USING (
            submitted_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant')
            )
          );

        CREATE POLICY "expense_reports_insert_authenticated" ON public.expense_reports
          FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND submitted_by = auth.uid());

        -- REPORTS POLICIES
        CREATE POLICY "reports_select_own_or_admin" ON public.reports
          FOR SELECT USING (
            submitted_by = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'staff-admin')
            )
          );

        CREATE POLICY "reports_insert_authenticated" ON public.reports
          FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND submitted_by = auth.uid());

        -- TIME LOGS POLICIES
        CREATE POLICY "time_logs_select_own_or_admin" ON public.time_logs
          FOR SELECT USING (
            user_id = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager')
            )
          );

        CREATE POLICY "time_logs_insert_own" ON public.time_logs
          FOR INSERT WITH CHECK (auth.uid() IS NOT NULL AND user_id = auth.uid());

        -- NOTIFICATIONS POLICIES
        CREATE POLICY "notifications_select_own" ON public.notifications
          FOR SELECT USING (user_id = auth.uid());

        CREATE POLICY "notifications_insert_system" ON public.notifications
          FOR INSERT WITH CHECK (
            auth.uid() IS NOT NULL OR current_setting('role') = 'service_role'
          );

        -- SYSTEM ACTIVITIES POLICIES
        CREATE POLICY "system_activities_select_admin" ON public.system_activities
          FOR SELECT USING (
            user_id = auth.uid() OR
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role IN ('admin', 'manager', 'staff-admin')
            )
          );

        CREATE POLICY "system_activities_insert_authenticated" ON public.system_activities
          FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

        -- Grant permissions
        GRANT SELECT, INSERT, UPDATE ON public.invoices TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.expense_reports TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.reports TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.time_logs TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
        GRANT SELECT, INSERT, UPDATE ON public.system_activities TO authenticated;
      `
    });

    if (error) throw error;
    logSuccess('Dashboard RLS policies fixed');
    return true;
  } catch (error) {
    logError(`Dashboard RLS fix failed: ${error.message}`);
    return false;
  }
};

// Step 4: Insert Sample Data
const insertSampleData = async () => {
  logStep('Step 4: Inserting sample data...');
  
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) throw new Error('User not authenticated');

    const userId = user.id;

    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Insert sample invoices
        INSERT INTO public.invoices (invoice_number, client_name, amount, total_amount, payment_status, due_date, created_by) VALUES
        ('INV-2024-001', 'Acme Corporation', 5000.00, 5000.00, 'paid', CURRENT_DATE + INTERVAL '30 days', '${userId}'),
        ('INV-2024-002', 'Tech Solutions Ltd', 7500.00, 7500.00, 'pending', CURRENT_DATE + INTERVAL '15 days', '${userId}'),
        ('INV-2024-003', 'Global Industries', 3200.00, 3200.00, 'overdue', CURRENT_DATE - INTERVAL '5 days', '${userId}'),
        ('INV-2024-004', 'Digital Dynamics', 4800.00, 4800.00, 'paid', CURRENT_DATE + INTERVAL '20 days', '${userId}'),
        ('INV-2024-005', 'Future Systems', 6200.00, 6200.00, 'pending', CURRENT_DATE + INTERVAL '10 days', '${userId}')
        ON CONFLICT (invoice_number) DO NOTHING;

        -- Insert sample expense reports
        INSERT INTO public.expense_reports (title, description, amount, category, status, submitted_by) VALUES
        ('Office Supplies', 'Monthly office supplies purchase', 450.00, 'office', 'approved', '${userId}'),
        ('Travel Expenses', 'Business trip to Lagos', 1200.00, 'travel', 'pending', '${userId}'),
        ('Equipment Purchase', 'New laptop for development', 2500.00, 'equipment', 'approved', '${userId}'),
        ('Training Course', 'Professional development course', 800.00, 'training', 'pending', '${userId}'),
        ('Client Meeting', 'Lunch meeting with client', 150.00, 'meals', 'approved', '${userId}')
        ON CONFLICT DO NOTHING;

        -- Insert sample reports
        INSERT INTO public.reports (title, description, report_type, priority, status, submitted_by) VALUES
        ('Monthly Performance Report', 'Summary of team performance for this month', 'performance', 'medium', 'submitted', '${userId}'),
        ('Project Status Update', 'Current status of ongoing projects', 'project', 'high', 'under_review', '${userId}'),
        ('Financial Summary', 'Quarterly financial overview', 'financial', 'high', 'approved', '${userId}'),
        ('Maintenance Report', 'Equipment maintenance summary', 'maintenance', 'medium', 'completed', '${userId}'),
        ('Incident Report', 'Security incident documentation', 'incident', 'urgent', 'under_review', '${userId}')
        ON CONFLICT DO NOTHING;

        -- Insert sample time logs
        INSERT INTO public.time_logs (user_id, description, hours_worked, log_date, billable) VALUES
        ('${userId}', 'Dashboard development', 8.0, CURRENT_DATE, true),
        ('${userId}', 'Bug fixes and testing', 6.5, CURRENT_DATE - INTERVAL '1 day', true),
        ('${userId}', 'Client meeting', 2.0, CURRENT_DATE - INTERVAL '2 days', true),
        ('${userId}', 'Code review', 3.5, CURRENT_DATE - INTERVAL '3 days', true),
        ('${userId}', 'Documentation update', 4.0, CURRENT_DATE - INTERVAL '4 days', true)
        ON CONFLICT DO NOTHING;

        -- Insert sample notifications
        INSERT INTO public.notifications (user_id, title, message, type, read) VALUES
        ('${userId}', 'Welcome to Dashboard', 'Your dashboard is now ready to use!', 'success', false),
        ('${userId}', 'New Report Submitted', 'A new report has been submitted for review', 'info', false),
        ('${userId}', 'Invoice Overdue', 'Invoice INV-2024-003 is now overdue', 'warning', true),
        ('${userId}', 'Expense Approved', 'Your office supplies expense has been approved', 'success', true),
        ('${userId}', 'System Maintenance', 'Scheduled maintenance tonight at 2 AM', 'info', false)
        ON CONFLICT DO NOTHING;

        -- Insert sample system activities
        INSERT INTO public.system_activities (user_id, action, description, severity, category) VALUES
        ('${userId}', 'login', 'User logged into the system', 'info', 'authentication'),
        ('${userId}', 'report_submit', 'Monthly performance report submitted', 'info', 'reports'),
        ('${userId}', 'expense_create', 'New expense report created', 'info', 'finance'),
        ('${userId}', 'dashboard_view', 'Dashboard accessed', 'info', 'navigation'),
        ('${userId}', 'profile_update', 'Profile information updated', 'info', 'user_management')
        ON CONFLICT DO NOTHING;
      `
    });

    if (error) throw error;
    logSuccess('Sample data inserted');
    return true;
  } catch (error) {
    logError(`Sample data insertion failed: ${error.message}`);
    return false;
  }
};

// Step 5: Test All Systems
const testSystems = async () => {
  logStep('Step 5: Testing all systems...');
  
  try {
    // Test invoices
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('*')
      .limit(3);

    if (invoicesError) throw new Error(`Invoices test failed: ${invoicesError.message}`);
    logSuccess(`Invoices: ${invoices?.length || 0} records accessible`);

    // Test expense reports
    const { data: expenses, error: expensesError } = await supabase
      .from('expense_reports')
      .select('*')
      .limit(3);

    if (expensesError) throw new Error(`Expense reports test failed: ${expensesError.message}`);
    logSuccess(`Expense reports: ${expenses?.length || 0} records accessible`);

    // Test reports
    const { data: reports, error: reportsError } = await supabase
      .from('reports')
      .select('*')
      .limit(3);

    if (reportsError) throw new Error(`Reports test failed: ${reportsError.message}`);
    logSuccess(`Reports: ${reports?.length || 0} records accessible`);

    // Test time logs
    const { data: timeLogs, error: timeLogsError } = await supabase
      .from('time_logs')
      .select('*')
      .limit(3);

    if (timeLogsError) throw new Error(`Time logs test failed: ${timeLogsError.message}`);
    logSuccess(`Time logs: ${timeLogs?.length || 0} records accessible`);

    // Test notifications
    const { data: notifications, error: notificationsError } = await supabase
      .from('notifications')
      .select('*')
      .limit(3);

    if (notificationsError) throw new Error(`Notifications test failed: ${notificationsError.message}`);
    logSuccess(`Notifications: ${notifications?.length || 0} records accessible`);

    return true;
  } catch (error) {
    logError(`System tests failed: ${error.message}`);
    return false;
  }
};

// Main function to run all fixes
export const runAllFixes = async () => {
  logStep('🚀 Starting complete system fix...');
  
  try {
    // Run all fixes in sequence
    if (!(await fixProfileRoles())) throw new Error('Profile roles fix failed');
    if (!(await createDashboardTables())) throw new Error('Dashboard tables creation failed');
    if (!(await fixDashboardRLS())) throw new Error('Dashboard RLS fix failed');
    if (!(await insertSampleData())) throw new Error('Sample data insertion failed');
    if (!(await testSystems())) throw new Error('System tests failed');

    logSuccess('🎉 All fixes completed successfully!');
    logSuccess('Dashboard should now display data properly');
    logSuccess('All role-based access controls are working');
    logSuccess('Report submissions should work without errors');
    
    return true;
  } catch (error) {
    logError(`Complete system fix failed: ${error.message}`);
    return false;
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  runAllFixes();
}

export default runAllFixes;
