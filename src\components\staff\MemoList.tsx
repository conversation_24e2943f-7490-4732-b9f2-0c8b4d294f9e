import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { format } from "date-fns";
import { Eye, EyeOff, FileText, Trash2, Download, Paperclip } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface MemoAttachment {
  id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
}

interface Memo {
  id: string;
  title: string;
  subject: string;
  content: string;
  purpose: string;
  created_at: string;
  created_by: string;
  status: string;
  to_recipient: string;
  account_details: string;
  total_amount: number;
  memo_date: string;
}

export function MemoList() {
  const [expandedMemos, setExpandedMemos] = useState<Set<string>>(new Set());
  const { userProfile } = useAuth();
  const { toast } = useToast();

  const { data: memos, isLoading, refetch } = useQuery({
    queryKey: ['memos-staff', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];
      
      const { data, error } = await supabase
        .from('memos')
        .select(`
          id,
          title,
          subject,
          content,
          purpose,
          created_at,
          created_by,
          status,
          to_recipient,
          account_details,
          total_amount,
          memo_date
        `)
        .eq('created_by', userProfile.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Memo[];
    },
    enabled: !!userProfile?.id,
  });

  const { data: profiles } = useQuery({
    queryKey: ['profiles-memo'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email')
        .order('full_name');

      if (error) throw error;
      return data;
    },
  });

  const { data: attachments } = useQuery({
    queryKey: ['memo-attachments', expandedMemos],
    queryFn: async () => {
      if (expandedMemos.size === 0) return {};
      
      const { data, error } = await supabase
        .from('memo_attachments')
        .select('*')
        .in('memo_id', Array.from(expandedMemos));

      if (error) throw error;
      
      // Group attachments by memo_id
      const grouped = data.reduce((acc, attachment) => {
        if (!acc[attachment.memo_id]) {
          acc[attachment.memo_id] = [];
        }
        acc[attachment.memo_id].push(attachment);
        return acc;
      }, {} as Record<string, MemoAttachment[]>);
      
      return grouped;
    },
    enabled: expandedMemos.size > 0,
  });

  const getUserName = (userId: string) => {
    const profile = profiles?.find(p => p.id === userId);
    return profile?.full_name || profile?.email || 'Unknown User';
  };

  const toggleExpanded = (memoId: string) => {
    const newExpanded = new Set(expandedMemos);
    if (newExpanded.has(memoId)) {
      newExpanded.delete(memoId);
    } else {
      newExpanded.add(memoId);
    }
    setExpandedMemos(newExpanded);
  };

  const handleDelete = async (memoId: string) => {
    if (!window.confirm("Are you sure you want to delete this memo?")) {
      return;
    }

    try {
      const { error } = await supabase
        .from('memos')
        .delete()
        .eq('id', memoId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Memo deleted successfully",
      });
      
      refetch();
    } catch (error) {
      console.error("Error deleting memo:", error);
      toast({
        title: "Error",
        description: "Failed to delete memo",
        variant: "destructive",
      });
    }
  };

  const downloadAttachment = async (filePath: string, fileName: string) => {
    try {
      const { data, error } = await supabase.storage
        .from('memo-attachments')
        .download(filePath);

      if (error) throw error;

      const url = URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Error",
        description: "Failed to download file",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <Card className="glass-card border border-primary/20">
        <CardContent className="p-6">
          <div className="text-center">Loading memos...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="glass-card border border-primary/20">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          My Memos ({memos?.length || 0})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {memos && memos.length > 0 ? (
            memos.map((memo) => {
              const isExpanded = expandedMemos.has(memo.id);
              const canDelete = userProfile?.id === memo.created_by || userProfile?.role === 'admin';
              const memoAttachments = attachments?.[memo.id] || [];
              
              return (
                <Card key={memo.id} className="border border-muted">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{memo.subject || memo.title}</CardTitle>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-sm text-muted-foreground">
                            From: {getUserName(memo.created_by)}
                          </span>
                          {memo.to_recipient && (
                            <>
                              <span className="text-sm text-muted-foreground">•</span>
                              <span className="text-sm text-muted-foreground">
                                To: {memo.to_recipient}
                              </span>
                            </>
                          )}
                          <span className="text-sm text-muted-foreground">•</span>
                          <span className="text-sm text-muted-foreground">
                            {format(new Date(memo.memo_date || memo.created_at), 'MMM dd, yyyy')}
                          </span>
                          <Badge variant="secondary" className="ml-2">
                            {memo.status || 'pending'}
                          </Badge>
                          {memo.total_amount > 0 && (
                            <Badge variant="outline" className="ml-2">
                              ₦{memo.total_amount.toLocaleString()}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(memo.id)}
                        >
                          {isExpanded ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                        
                        {canDelete && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(memo.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  
                  {isExpanded && (
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        <div className="bg-muted p-4 rounded-md">
                          <h4 className="font-medium mb-2">Content:</h4>
                          <p className="whitespace-pre-wrap">{memo.content}</p>
                        </div>

                        {memo.purpose && (
                          <div className="bg-muted p-4 rounded-md">
                            <h4 className="font-medium mb-2">Purpose:</h4>
                            <p className="whitespace-pre-wrap">{memo.purpose}</p>
                          </div>
                        )}

                        {memo.account_details && (
                          <div className="bg-muted p-4 rounded-md">
                            <h4 className="font-medium mb-2">Account Details:</h4>
                            <p className="whitespace-pre-wrap">{memo.account_details}</p>
                          </div>
                        )}

                        {memoAttachments.length > 0 && (
                          <div className="bg-muted p-4 rounded-md">
                            <h4 className="font-medium mb-2 flex items-center gap-2">
                              <Paperclip className="h-4 w-4" />
                              Attachments:
                            </h4>
                            <div className="space-y-2">
                              {memoAttachments.map((attachment) => (
                                <div key={attachment.id} className="flex items-center justify-between bg-background p-2 rounded">
                                  <span className="text-sm">{attachment.file_name}</span>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => downloadAttachment(attachment.file_path, attachment.file_name)}
                                  >
                                    <Download className="h-4 w-4" />
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  )}
                </Card>
              );
            })
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No memos found</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
