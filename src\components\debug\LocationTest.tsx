import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MapPin, Navigation, Loader2, RefreshCw } from 'lucide-react';
import { LocationData } from '@/types/timeTracking';
import { locationService } from '@/services/locationService';

export const LocationTest: React.FC = () => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testLocation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      console.log('🧪 Testing location service...');
      const result = await locationService.getCurrentLocation();
      
      console.log('🧪 Location test result:', result);
      setLocation(result);
    } catch (err) {
      console.error('🧪 Location test failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Location Service Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={testLocation} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Getting Location...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Navigation className="h-4 w-4" />
              Test Location
            </div>
          )}
        </Button>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        {location && (
          <div className="space-y-2">
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <h4 className="font-semibold text-green-800">Location Found:</h4>
              <p className="text-green-700 text-sm">{location.address}</p>
              <p className="text-green-600 text-xs">
                Coordinates: {location.latitude.toFixed(6)}, {location.longitude.toFixed(6)}
              </p>
              <p className="text-green-600 text-xs">
                Accuracy: {Math.round(location.accuracy)}m | Method: {location.method}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
