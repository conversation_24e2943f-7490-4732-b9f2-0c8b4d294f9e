-- ============================================================================
-- FIX 406/409 PROFILE ERRORS
-- Addresses specific errors: 406 (Not Acceptable) and 409 (Conflict)
-- User: 44349058-db4b-4a0b-8c99-8a913d07df74 (<EMAIL>)
-- ============================================================================

-- Step 1: Disable RLS to clear any stuck states
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- Step 2: Drop ALL existing policies to start fresh
DO $$ 
DECLARE
    policy_record RECORD;
BEGIN
    FOR policy_record IN 
        SELECT policyname 
        FROM pg_policies 
        WHERE tablename = 'profiles' AND schemaname = 'public'
    LOOP
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON public.profiles';
        RAISE NOTICE 'Dropped policy: %', policy_record.policyname;
    END LOOP;
END $$;

-- Step 3: Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Step 4: Create minimal, non-conflicting policies
CREATE POLICY "allow_own_profile_select" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "allow_own_profile_insert" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "allow_own_profile_update" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Step 5: Grant permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- Step 6: Create emergency profile for the specific user
-- This handles the 409 conflict by using ON CONFLICT
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
    updated_at = NOW();

-- Step 7: Verify the fix
SELECT 'Profile created/updated successfully' as status,
       id, email, role, status, created_at
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Step 8: Test RLS policies
SELECT 'RLS Status' as check_type, 
       schemaname, 
       tablename, 
       rowsecurity,
       (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public') as policy_count
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- Step 9: List active policies
SELECT 'Active Policies' as check_type,
       policyname, 
       cmd as operation,
       CASE WHEN qual IS NOT NULL THEN 'Has USING clause' ELSE 'No USING clause' END as using_clause,
       CASE WHEN with_check IS NOT NULL THEN 'Has WITH CHECK clause' ELSE 'No WITH CHECK clause' END as check_clause
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;

COMMENT ON TABLE public.profiles IS 'Profiles table - Fixed 406/409 errors on 2025-07-18';
