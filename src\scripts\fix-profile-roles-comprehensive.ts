import { supabase } from '@/integrations/supabase/client';

/**
 * Comprehensive fix for Supabase profile roles and RLS policies
 * This script will ensure all roles (manager, staff, accountant, staff-admin) work correctly
 */

const logStep = (message: string) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message: string) => {
  console.log(`✅ ${message}`);
};

const logError = (message: string) => {
  console.error(`❌ ${message}`);
};

// Step 1: Update profiles table schema with all roles
const updateProfilesTableSchema = async () => {
  logStep('Updating profiles table schema with all roles...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop existing role constraint if it exists
        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
        
        -- Add comprehensive role constraint
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_role_check 
          CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
        
        -- Drop existing account_type constraint if it exists
        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_account_type_check;
        
        -- Add comprehensive account_type constraint
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_account_type_check 
          CHECK (account_type IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
        
        -- Drop existing status constraint if it exists
        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_status_check;
        
        -- Add comprehensive status constraint
        ALTER TABLE public.profiles ADD CONSTRAINT profiles_status_check 
          CHECK (status IN ('active', 'inactive', 'suspended', 'pending'));
        
        -- Ensure all required columns exist
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff';
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC';
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS bio TEXT;
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS location TEXT;
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS skills TEXT[];
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';
        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}';
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
        CREATE INDEX IF NOT EXISTS idx_profiles_department_id ON public.profiles(department_id);
        CREATE INDEX IF NOT EXISTS idx_profiles_status ON public.profiles(status);
        CREATE INDEX IF NOT EXISTS idx_profiles_account_type ON public.profiles(account_type);
        CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Profiles table schema updated successfully');
    return true;
  } catch (error) {
    logError(`Failed to update profiles schema: ${error.message}`);
    return false;
  }
};

// Step 2: Fix RLS policies for all roles
const fixProfilesRLSPolicies = async () => {
  logStep('Fixing profiles RLS policies for all roles...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop all existing policies to start fresh
        DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
        DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
        DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
        DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
        DROP POLICY IF EXISTS "Admins can update all profiles" ON public.profiles;
        DROP POLICY IF EXISTS "Admins can delete profiles" ON public.profiles;
        DROP POLICY IF EXISTS "Managers can view department profiles" ON public.profiles;
        DROP POLICY IF EXISTS "HR can view all profiles" ON public.profiles;
        DROP POLICY IF EXISTS "Staff-admin can view profiles" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_read_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_manager_department" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_hr_all" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_staff_admin" ON public.profiles;
        
        -- Enable RLS
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
        
        -- 1. SELECT policies
        
        -- Users can view their own profile
        CREATE POLICY "profiles_select_own" ON public.profiles
          FOR SELECT USING (auth.uid() = id);
        
        -- Admin can view all profiles
        CREATE POLICY "profiles_select_admin" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'admin'
            )
          );
        
        -- Manager can view profiles in their department
        CREATE POLICY "profiles_select_manager" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles p1
              WHERE p1.id = auth.uid() 
              AND p1.role = 'manager'
              AND (
                p1.department_id = public.profiles.department_id OR
                public.profiles.id = auth.uid()
              )
            )
          );
        
        -- HR can view all profiles
        CREATE POLICY "profiles_select_hr" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'hr'
            )
          );
        
        -- Staff-admin can view all profiles
        CREATE POLICY "profiles_select_staff_admin" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'staff-admin'
            )
          );
        
        -- 2. INSERT policies
        
        -- Users can insert their own profile
        CREATE POLICY "profiles_insert_own" ON public.profiles
          FOR INSERT WITH CHECK (auth.uid() = id);
        
        -- Admin can insert any profile
        CREATE POLICY "profiles_insert_admin" ON public.profiles
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'admin'
            )
          );
        
        -- 3. UPDATE policies
        
        -- Users can update their own profile (except role)
        CREATE POLICY "profiles_update_own" ON public.profiles
          FOR UPDATE USING (auth.uid() = id)
          WITH CHECK (
            auth.uid() = id AND
            (OLD.role = NEW.role OR NEW.role IS NULL)
          );
        
        -- Admin can update any profile
        CREATE POLICY "profiles_update_admin" ON public.profiles
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'admin'
            )
          );
        
        -- Manager can update profiles in their department (except role)
        CREATE POLICY "profiles_update_manager" ON public.profiles
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles p1
              WHERE p1.id = auth.uid() 
              AND p1.role = 'manager'
              AND p1.department_id = public.profiles.department_id
            )
          )
          WITH CHECK (OLD.role = NEW.role OR NEW.role IS NULL);
        
        -- HR can update profiles (except role)
        CREATE POLICY "profiles_update_hr" ON public.profiles
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'hr'
            )
          )
          WITH CHECK (OLD.role = NEW.role OR NEW.role IS NULL);
        
        -- Staff-admin can update profiles (except role)
        CREATE POLICY "profiles_update_staff_admin" ON public.profiles
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'staff-admin'
            )
          )
          WITH CHECK (OLD.role = NEW.role OR NEW.role IS NULL);
        
        -- 4. DELETE policies (only admin)
        
        CREATE POLICY "profiles_delete_admin" ON public.profiles
          FOR DELETE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE id = auth.uid() AND role = 'admin'
            )
          );
        
        -- Grant permissions
        GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
        GRANT ALL ON public.profiles TO service_role;
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Profiles RLS policies fixed successfully');
    return true;
  } catch (error) {
    logError(`Failed to fix profiles RLS policies: ${error.message}`);
    return false;
  }
};

// Step 3: Create role management functions
const createRoleManagementFunctions = async () => {
  logStep('Creating role management functions...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Function to check if user has specific role
        CREATE OR REPLACE FUNCTION public.user_has_role(user_id UUID, required_role TEXT)
        RETURNS BOOLEAN AS $$
        BEGIN
          RETURN EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = user_id AND role = required_role
          );
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to check if user has any of the specified roles
        CREATE OR REPLACE FUNCTION public.user_has_any_role(user_id UUID, roles TEXT[])
        RETURNS BOOLEAN AS $$
        BEGIN
          RETURN EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = user_id AND role = ANY(roles)
          );
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to get user role
        CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
        RETURNS TEXT AS $$
        DECLARE
          user_role TEXT;
        BEGIN
          SELECT role INTO user_role 
          FROM public.profiles 
          WHERE id = user_id;
          
          RETURN COALESCE(user_role, 'staff');
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Function to update user role (admin only)
        CREATE OR REPLACE FUNCTION public.update_user_role(
          target_user_id UUID,
          new_role TEXT
        )
        RETURNS BOOLEAN AS $$
        BEGIN
          -- Check if current user is admin
          IF NOT public.user_has_role(auth.uid(), 'admin') THEN
            RAISE EXCEPTION 'Only admins can update user roles';
          END IF;
          
          -- Validate role
          IF new_role NOT IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin') THEN
            RAISE EXCEPTION 'Invalid role: %', new_role;
          END IF;
          
          -- Update role
          UPDATE public.profiles 
          SET role = new_role, updated_at = NOW()
          WHERE id = target_user_id;
          
          RETURN FOUND;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
        
        -- Grant execute permissions
        GRANT EXECUTE ON FUNCTION public.user_has_role TO authenticated;
        GRANT EXECUTE ON FUNCTION public.user_has_any_role TO authenticated;
        GRANT EXECUTE ON FUNCTION public.get_user_role TO authenticated;
        GRANT EXECUTE ON FUNCTION public.update_user_role TO authenticated;
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Role management functions created successfully');
    return true;
  } catch (error) {
    logError(`Failed to create role management functions: ${error.message}`);
    return false;
  }
};

// Step 4: Test role access
const testRoleAccess = async () => {
  logStep('Testing role access...');
  
  try {
    // Test basic profile access
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id, role, full_name')
      .limit(5);

    if (profileError) {
      throw new Error(`Profile query failed: ${profileError.message}`);
    }

    logSuccess(`Profile query successful, found ${profileData?.length || 0} profiles`);

    // Test role functions
    const { data: roleTest, error: roleError } = await supabase
      .rpc('get_user_role', { user_id: (await supabase.auth.getUser()).data.user?.id });

    if (roleError) {
      console.warn('Role function test failed (this might be expected):', roleError);
    } else {
      logSuccess(`Role function test successful, current role: ${roleTest}`);
    }

    return true;
  } catch (error) {
    logError(`Role access test failed: ${error.message}`);
    return false;
  }
};

// Main function to run all fixes
export const fixProfileRolesComprehensive = async () => {
  logStep('Starting comprehensive profile roles fix...');

  try {
    // Step 1: Update table schema
    if (!(await updateProfilesTableSchema())) {
      throw new Error('Failed to update profiles table schema');
    }

    // Step 2: Fix RLS policies
    if (!(await fixProfilesRLSPolicies())) {
      throw new Error('Failed to fix RLS policies');
    }

    // Step 3: Create role management functions
    if (!(await createRoleManagementFunctions())) {
      throw new Error('Failed to create role management functions');
    }

    // Step 4: Test the setup
    if (!(await testRoleAccess())) {
      throw new Error('Role access tests failed');
    }

    logSuccess('🎉 Comprehensive profile roles fix completed successfully!');
    
    // Store success in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('profile_roles_fix_completed', new Date().toISOString());
    }
    
    return true;

  } catch (error) {
    logError(`Comprehensive profile roles fix failed: ${error.message}`);
    return false;
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  // Check if fix has been run recently
  const lastRun = localStorage.getItem('profile_roles_fix_completed');
  const shouldRun = !lastRun || (new Date().getTime() - new Date(lastRun).getTime()) > 24 * 60 * 60 * 1000;
  
  if (shouldRun) {
    fixProfileRolesComprehensive();
  }
}
