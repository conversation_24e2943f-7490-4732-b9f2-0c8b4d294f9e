import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Smartphone, Wifi, Battery, Signal, Info, RefreshCw } from "lucide-react";

interface DeviceInfo {
  userAgent: string;
  platform: string;
  screenWidth: number;
  screenHeight: number;
  devicePixelRatio: number;
  orientation: string;
  touchSupport: boolean;
  onlineStatus: boolean;
  batteryLevel?: number;
  connectionType?: string;
}

const MobileDebug: React.FC = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  const collectDeviceInfo = async (): Promise<DeviceInfo> => {
    const info: DeviceInfo = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      devicePixelRatio: window.devicePixelRatio,
      orientation: window.screen.orientation?.type || 'unknown',
      touchSupport: 'ontouchstart' in window,
      onlineStatus: navigator.onLine,
    };

    // Try to get battery info (if supported)
    try {
      if ('getBattery' in navigator) {
        const battery = await (navigator as any).getBattery();
        info.batteryLevel = Math.round(battery.level * 100);
      }
    } catch (error) {
      console.log('Battery API not supported');
    }

    // Try to get connection info (if supported)
    try {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        info.connectionType = connection.effectiveType || connection.type;
      }
    } catch (error) {
      console.log('Network Information API not supported');
    }

    return info;
  };

  const refreshInfo = async () => {
    const info = await collectDeviceInfo();
    setDeviceInfo(info);
  };

  useEffect(() => {
    refreshInfo();
    
    // Listen for orientation changes
    const handleOrientationChange = () => {
      setTimeout(refreshInfo, 100); // Small delay to get updated values
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);
    window.addEventListener('online', refreshInfo);
    window.addEventListener('offline', refreshInfo);

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', handleOrientationChange);
      window.removeEventListener('online', refreshInfo);
      window.removeEventListener('offline', refreshInfo);
    };
  }, []);

  // Only show on mobile devices or when explicitly enabled
  useEffect(() => {
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isDebugMode = localStorage.getItem('mobile-debug') === 'true';
    setIsVisible(isMobile || isDebugMode);
  }, []);

  if (!isVisible || !deviceInfo) {
    return null;
  }

  const getDeviceType = () => {
    const ua = deviceInfo.userAgent.toLowerCase();
    if (ua.includes('iphone')) return 'iPhone';
    if (ua.includes('ipad')) return 'iPad';
    if (ua.includes('android')) return 'Android';
    if (ua.includes('windows phone')) return 'Windows Phone';
    return 'Unknown';
  };

  const getConnectionIcon = () => {
    if (!deviceInfo.onlineStatus) return <Wifi className="h-4 w-4 text-red-500" />;
    return <Signal className="h-4 w-4 text-green-500" />;
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className="bg-black/90 border-red-500/30 text-white">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2 text-sm">
            <Smartphone className="h-4 w-4 text-red-500" />
            Mobile Debug Info
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshInfo}
              className="ml-auto p-1 h-6 w-6"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-xs">
          <div className="flex items-center justify-between">
            <span>Device:</span>
            <Badge variant="outline" className="text-red-400 border-red-500/30">
              {getDeviceType()}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Screen:</span>
            <span>{deviceInfo.screenWidth} × {deviceInfo.screenHeight}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>DPR:</span>
            <span>{deviceInfo.devicePixelRatio}x</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Orientation:</span>
            <span>{deviceInfo.orientation}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Touch:</span>
            <Badge variant={deviceInfo.touchSupport ? "default" : "secondary"}>
              {deviceInfo.touchSupport ? "Yes" : "No"}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span>Connection:</span>
            <div className="flex items-center gap-1">
              {getConnectionIcon()}
              <span>{deviceInfo.onlineStatus ? "Online" : "Offline"}</span>
            </div>
          </div>
          
          {deviceInfo.connectionType && (
            <div className="flex items-center justify-between">
              <span>Type:</span>
              <span>{deviceInfo.connectionType}</span>
            </div>
          )}
          
          {deviceInfo.batteryLevel !== undefined && (
            <div className="flex items-center justify-between">
              <span>Battery:</span>
              <div className="flex items-center gap-1">
                <Battery className="h-3 w-3" />
                <span>{deviceInfo.batteryLevel}%</span>
              </div>
            </div>
          )}
          
          <div className="pt-2 border-t border-red-500/20">
            <div className="flex items-center gap-1 text-red-400">
              <Info className="h-3 w-3" />
              <span>Debug Mode Active</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MobileDebug;
