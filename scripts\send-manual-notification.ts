import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || 'http://127.0.0.1:54321';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const supabase = createClient(supabaseUrl, supabaseKey);

// Email recipients - Currently limited to verified email in testing mode
const recipients = [
  '<EMAIL>', // Verified email for testing mode
  // Note: After domain verification, add more recipients:
  // '<EMAIL>', // Primary admin/owner
  // '<EMAIL>',
  // '<EMAIL>',
  // '<EMAIL>'
];

function createEmailTemplate() {
  const manualUrl = 'https://ai.ctnigeria.com/user-manual.html';
  const dashboardUrl = 'https://ai.ctnigeria.com/dashboard';
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>CTNL AI WORK-BOARD - System Ready</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8f9fa;">
        <div style="max-width: 600px; margin: 0 auto; background: #ffffff;">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); padding: 40px 20px; text-align: center;">
                <h1 style="margin: 0; font-size: 32px; font-weight: 700; background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                    🚀 CTNL AI WORK-BOARD
                </h1>
                <p style="margin: 10px 0 0 0; color: #cccccc; font-size: 18px; font-weight: 500;">
                    Your AI-Powered Workforce Management System is Ready!
                </p>
            </div>

            <!-- Main Content -->
            <div style="padding: 40px 30px;">
                <h2 style="color: #1a1a1a; font-size: 24px; margin-bottom: 20px;">🎉 Welcome to the Future of Workforce Management!</h2>
                
                <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
                    We're excited to announce that <strong>CTNL AI WORK-BOARD</strong> is now fully operational and ready to transform your workforce management experience. Your comprehensive AI-powered system is live at <strong>ai.ctnigeria.com</strong>.
                </p>

                <!-- Key Features -->
                <div style="background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 25px; margin: 25px 0;">
                    <h3 style="color: #2d3748; font-size: 18px; margin-bottom: 15px;">🚀 What's Available Now:</h3>
                    <ul style="color: #4a5568; font-size: 14px; line-height: 1.8; margin: 0; padding-left: 20px;">
                        <li><strong>AI-Powered Document Analysis</strong> - Upload and analyze documents with advanced AI</li>
                        <li><strong>Intelligent Chat Assistant</strong> - Get instant help and insights</li>
                        <li><strong>Smart Time Tracking</strong> - Location-aware clock in/out with device detection</li>
                        <li><strong>Role-Based Dashboards</strong> - Customized interfaces for Admin, Manager, Staff, and Accountant roles</li>
                        <li><strong>Project Management</strong> - Kanban boards with progress tracking</li>
                        <li><strong>Real-Time Notifications</strong> - Email and in-app alerts</li>
                        <li><strong>Advanced Reporting</strong> - Comprehensive analytics and insights</li>
                        <li><strong>Mobile-Responsive Design</strong> - Works perfectly on all devices</li>
                    </ul>
                </div>

                <!-- Action Buttons -->
                <div style="text-align: center; margin: 30px 0;">
                    <a href="${manualUrl}" style="display: inline-block; background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 10px;">
                        📖 View Complete User Manual
                    </a>
                    <br>
                    <a href="${dashboardUrl}" style="display: inline-block; background: linear-gradient(135deg, #059669 0%, #10b981 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin: 10px;">
                        🚀 Access Your Dashboard
                    </a>
                </div>

                <!-- Quick Start Guide -->
                <div style="background: #fef5e7; border: 1px solid #f6ad55; border-radius: 8px; padding: 20px; margin: 25px 0;">
                    <h3 style="color: #c05621; font-size: 16px; margin-bottom: 10px;">⚡ Quick Start:</h3>
                    <ol style="color: #744210; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                        <li>Visit <strong>ai.ctnigeria.com</strong> and sign in with your credentials</li>
                        <li>Explore your role-specific dashboard</li>
                        <li>Try the AI document analysis feature</li>
                        <li>Set up your notification preferences</li>
                        <li>Start tracking time and managing tasks</li>
                    </ol>
                </div>

                <!-- Support Information -->
                <div style="background: #edf2f7; border: 1px solid #cbd5e0; border-radius: 8px; padding: 20px; margin: 25px 0;">
                    <h3 style="color: #2d3748; font-size: 16px; margin-bottom: 10px;">🛟 Need Help?</h3>
                    <p style="color: #4a5568; font-size: 14px; line-height: 1.6; margin: 0;">
                        Our support team is ready to assist you with any questions or technical issues. 
                        Contact us at <a href="mailto:<EMAIL>" style="color: #dc2626; text-decoration: none;"><EMAIL></a> 
                        or access the comprehensive user manual for detailed guidance.
                    </p>
                </div>

                <p style="color: #4a5568; font-size: 14px; line-height: 1.6; margin-top: 30px;">
                    Thank you for choosing <strong>CTNL AI WORK-BOARD</strong>. We're committed to providing you with the most advanced workforce management solution available.
                </p>
            </div>

            <!-- Footer -->
            <div style="background: #2d3748; color: #a0aec0; padding: 20px; text-align: center; font-size: 12px;">
                <p style="margin: 0;">
                    © 2024 CTNL AI WORK-BOARD. All rights reserved.<br>
                    Powered by advanced AI technology for workforce management excellence.
                </p>
            </div>
        </div>
    </body>
    </html>
  `;
}

async function sendUserManualNotification() {
  try {
    console.log('🚀 Sending CTNL AI WORK-BOARD launch notifications...');

    const emailTemplate = createEmailTemplate();

    // Use the send-notification Edge Function
    const { data, error } = await supabase.functions.invoke('send-notification', {
      body: {
        type: 'system_launch',
        recipients: recipients,
        data: {
          systemName: 'CTNL AI WORK-BOARD',
          manualUrl: 'https://ai.ctnigeria.com/user-manual.html',
          dashboardUrl: 'https://ai.ctnigeria.com/dashboard',
          supportEmail: '<EMAIL>'
        }
      }
    });

    if (error) {
      throw new Error(`Email sending failed: ${error.message}`);
    }

    console.log('✅ Launch notification sent successfully!');
    console.log('📧 Recipients:', recipients.join(', '));
    console.log('🔗 Manual URL: https://ai.ctnigeria.com/user-manual.html');
    console.log('🔗 Dashboard URL: https://ai.ctnigeria.com/dashboard');
    
    return {
      success: true,
      message: 'Launch notification sent successfully',
      recipients: recipients.length,
      data
    };

  } catch (error) {
    console.error('❌ Failed to send launch notification:', error);
    throw error;
  }
}

// Execute the function directly
console.log('🚀 Starting manual notification process...');
sendUserManualNotification()
  .then((result) => {
    console.log('🎉 Notification process completed:', result);
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Notification process failed:', error);
    process.exit(1);
  });

export { sendUserManualNotification };
