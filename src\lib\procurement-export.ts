import { ProcurementAPI } from './procurement-api';

// Export utilities for procurement data
export class ProcurementExportService {
  
  // ============= CSV EXPORT =============
  static async exportToCSV(filters?: any): Promise<string> {
    try {
      const { data: requests, error } = await ProcurementAPI.getProcurementRequests(filters);
      if (error) throw new Error(error.message);

      if (!requests || requests.length === 0) {
        throw new Error('No data to export');
      }

      // CSV Headers
      const headers = [
        'Request Number',
        'Title',
        'Status',
        'Priority',
        'Requested By',
        'Department',
        'Project',
        'Category',
        'Estimated Cost',
        'Actual Cost',
        'Required By Date',
        'Created Date',
        'Items Count',
        'Justification',
        'Notes'
      ];

      // Convert data to CSV rows
      const rows = requests.map(request => [
        request.request_number,
        request.title,
        request.status,
        request.priority,
        request.requested_by_profile?.full_name || '',
        request.department?.name || '',
        request.project?.name || '',
        request.category,
        request.estimated_total_cost,
        request.actual_total_cost || '',
        request.required_by_date || '',
        new Date(request.created_at).toLocaleDateString(),
        request.procurement_items?.length || 0,
        request.justification,
        request.notes || ''
      ]);

      // Combine headers and rows
      const csvContent = [headers, ...rows]
        .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
        .join('\n');

      return csvContent;
    } catch (error: any) {
      throw new Error(`CSV export failed: ${error.message}`);
    }
  }

  // ============= EXCEL EXPORT =============
  static async exportToExcel(filters?: any): Promise<Blob> {
    try {
      // For a real implementation, you would use a library like xlsx or exceljs
      // This is a simplified version that creates a basic Excel-compatible format
      
      const csvContent = await this.exportToCSV(filters);
      
      // Convert CSV to Excel-like format (simplified)
      const excelContent = '\ufeff' + csvContent; // Add BOM for UTF-8
      
      return new Blob([excelContent], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
    } catch (error: any) {
      throw new Error(`Excel export failed: ${error.message}`);
    }
  }

  // ============= PDF EXPORT =============
  static async exportToPDF(filters?: any): Promise<Blob> {
    try {
      const { data: requests, error } = await ProcurementAPI.getProcurementRequests(filters);
      if (error) throw new Error(error.message);

      if (!requests || requests.length === 0) {
        throw new Error('No data to export');
      }

      // For a real implementation, you would use a library like jsPDF or Puppeteer
      // This is a simplified HTML-to-PDF approach
      
      const htmlContent = this.generatePDFHTML(requests);
      
      // In a real implementation, you would convert HTML to PDF
      // For now, we'll return the HTML as a blob
      return new Blob([htmlContent], { type: 'application/pdf' });
    } catch (error: any) {
      throw new Error(`PDF export failed: ${error.message}`);
    }
  }

  // ============= DOCX EXPORT =============
  static async exportToDocx(filters?: any): Promise<Blob> {
    try {
      const { data: requests, error } = await ProcurementAPI.getProcurementRequests(filters);
      if (error) throw new Error(error.message);

      if (!requests || requests.length === 0) {
        throw new Error('No data to export');
      }

      // For a real implementation, you would use a library like docx or officegen
      // This is a simplified version
      
      const docContent = this.generateDocxContent(requests);
      
      return new Blob([docContent], { 
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
      });
    } catch (error: any) {
      throw new Error(`DOCX export failed: ${error.message}`);
    }
  }

  // ============= HELPER METHODS =============
  
  private static generatePDFHTML(requests: any[]): string {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString();
    };

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Procurement Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
          .request { border: 1px solid #ddd; margin-bottom: 20px; padding: 15px; }
          .request-header { background: #f8f9fa; padding: 10px; margin: -15px -15px 15px -15px; }
          .items { margin-top: 15px; }
          .item { background: #f9f9f9; padding: 10px; margin: 5px 0; }
          table { width: 100%; border-collapse: collapse; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Procurement Report</h1>
          <p>Generated on ${new Date().toLocaleDateString()}</p>
        </div>
        
        <div class="summary">
          <h2>Summary</h2>
          <p><strong>Total Requests:</strong> ${requests.length}</p>
          <p><strong>Total Value:</strong> ${formatCurrency(requests.reduce((sum, r) => sum + r.estimated_total_cost, 0))}</p>
        </div>
        
        ${requests.map(request => `
          <div class="request">
            <div class="request-header">
              <h3>${request.title}</h3>
              <p><strong>Request #:</strong> ${request.request_number} | 
                 <strong>Status:</strong> ${request.status} | 
                 <strong>Priority:</strong> ${request.priority}</p>
            </div>
            
            <table>
              <tr><td><strong>Requested By:</strong></td><td>${request.requested_by_profile?.full_name || 'N/A'}</td></tr>
              <tr><td><strong>Department:</strong></td><td>${request.department?.name || 'N/A'}</td></tr>
              <tr><td><strong>Category:</strong></td><td>${request.category}</td></tr>
              <tr><td><strong>Estimated Cost:</strong></td><td>${formatCurrency(request.estimated_total_cost)}</td></tr>
              <tr><td><strong>Created Date:</strong></td><td>${formatDate(request.created_at)}</td></tr>
              ${request.required_by_date ? `<tr><td><strong>Required By:</strong></td><td>${formatDate(request.required_by_date)}</td></tr>` : ''}
            </table>
            
            <p><strong>Justification:</strong> ${request.justification}</p>
            
            ${request.procurement_items && request.procurement_items.length > 0 ? `
              <div class="items">
                <h4>Items (${request.procurement_items.length})</h4>
                ${request.procurement_items.map((item: any) => `
                  <div class="item">
                    <strong>${item.item_name}</strong> - 
                    Qty: ${item.quantity} ${item.unit_of_measurement} - 
                    Cost: ${formatCurrency(item.estimated_total_cost)}
                    ${item.description ? `<br><em>${item.description}</em>` : ''}
                  </div>
                `).join('')}
              </div>
            ` : ''}
          </div>
        `).join('')}
      </body>
      </html>
    `;
  }

  private static generateDocxContent(requests: any[]): string {
    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(amount);
    };

    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleDateString();
    };

    // Simplified DOCX content (in a real implementation, you'd use proper DOCX libraries)
    return `
PROCUREMENT REPORT
Generated on ${new Date().toLocaleDateString()}

SUMMARY
Total Requests: ${requests.length}
Total Value: ${formatCurrency(requests.reduce((sum, r) => sum + r.estimated_total_cost, 0))}

REQUESTS
${requests.map(request => `
${request.title}
Request #: ${request.request_number}
Status: ${request.status}
Priority: ${request.priority}
Requested By: ${request.requested_by_profile?.full_name || 'N/A'}
Department: ${request.department?.name || 'N/A'}
Category: ${request.category}
Estimated Cost: ${formatCurrency(request.estimated_total_cost)}
Created Date: ${formatDate(request.created_at)}
${request.required_by_date ? `Required By: ${formatDate(request.required_by_date)}` : ''}

Justification: ${request.justification}

${request.procurement_items && request.procurement_items.length > 0 ? `
Items (${request.procurement_items.length}):
${request.procurement_items.map((item: any) => `
- ${item.item_name} - Qty: ${item.quantity} ${item.unit_of_measurement} - Cost: ${formatCurrency(item.estimated_total_cost)}
  ${item.description ? `  Description: ${item.description}` : ''}
`).join('')}
` : ''}

---
`).join('')}
    `;
  }

  // ============= DOWNLOAD HELPER =============
  static downloadFile(content: string | Blob, filename: string, mimeType: string) {
    const blob = content instanceof Blob ? content : new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }

  // ============= MAIN EXPORT METHOD =============
  static async exportData(format: 'pdf' | 'xlsx' | 'docx' | 'csv', filters?: any) {
    const timestamp = new Date().toISOString().split('T')[0];
    const baseFilename = `procurement-report-${timestamp}`;

    try {
      switch (format) {
        case 'csv':
          const csvContent = await this.exportToCSV(filters);
          this.downloadFile(csvContent, `${baseFilename}.csv`, 'text/csv');
          break;

        case 'xlsx':
          const excelBlob = await this.exportToExcel(filters);
          this.downloadFile(excelBlob, `${baseFilename}.xlsx`, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
          break;

        case 'pdf':
          const pdfBlob = await this.exportToPDF(filters);
          this.downloadFile(pdfBlob, `${baseFilename}.pdf`, 'application/pdf');
          break;

        case 'docx':
          const docxBlob = await this.exportToDocx(filters);
          this.downloadFile(docxBlob, `${baseFilename}.docx`, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
          break;

        default:
          throw new Error(`Unsupported export format: ${format}`);
      }
    } catch (error: any) {
      throw new Error(`Export failed: ${error.message}`);
    }
  }
}
