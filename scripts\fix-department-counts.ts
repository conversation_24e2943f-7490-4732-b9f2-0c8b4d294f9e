import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.VITE_SUPABASE_URL!;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY!;

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDepartmentCounts() {
  console.log('🔧 Fixing department count mismatches...');
  
  try {
    // First, let's see the current state
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select('id, name, employee_count');
    
    if (deptError) throw deptError;
    
    console.log('\n📊 Current department counts in DB:');
    for (const dept of departments || []) {
      // Get actual count
      const { count: actualCount } = await supabase
        .from('profiles')
        .select('id', { count: 'exact' })
        .eq('department_id', dept.id);
      
      console.log(`${dept.name}: DB shows ${dept.employee_count}, actual is ${actualCount}`);
      
      // Update if mismatch
      if (dept.employee_count !== actualCount) {
        const { error: updateError } = await supabase
          .from('departments')
          .update({ employee_count: actualCount || 0 })
          .eq('id', dept.id);
        
        if (updateError) {
          console.error(`❌ Error updating ${dept.name}:`, updateError);
        } else {
          console.log(`✅ Fixed ${dept.name}: ${dept.employee_count} → ${actualCount}`);
        }
      }
    }
    
    console.log('\n🎉 Department count fix completed!');
    
  } catch (error) {
    console.error('❌ Error fixing department counts:', error);
  }
}

// Run the fix
fixDepartmentCounts();
