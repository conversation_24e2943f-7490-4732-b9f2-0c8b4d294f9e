-- System Activities Table Schema
-- This table tracks all system activities and user actions for audit and monitoring purposes

-- Drop existing table if it exists (for development only)
-- DROP TABLE IF EXISTS system_activities CASCADE;

-- Create system_activities table
CREATE TABLE IF NOT EXISTS system_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
    activity_type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical', 'success')),
    category VARCHAR(50) DEFAULT 'general' CHECK (category IN ('general', 'auth', 'project', 'user', 'system', 'database', 'api', 'security')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON system_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_system_activities_activity_type ON system_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_system_activities_created_at ON system_activities(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_activities_severity ON system_activities(severity);
CREATE INDEX IF NOT EXISTS idx_system_activities_category ON system_activities(category);
CREATE INDEX IF NOT EXISTS idx_system_activities_session_id ON system_activities(session_id);

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_system_activities_user_date ON system_activities(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_activities_type_date ON system_activities(activity_type, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_system_activities_category_date ON system_activities(category, created_at DESC);

-- Enable Row Level Security
ALTER TABLE system_activities ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Admin can see all activities
CREATE POLICY "Admin can view all activities" ON system_activities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Users can only see their own activities
CREATE POLICY "Users can view own activities" ON system_activities
    FOR SELECT USING (user_id = auth.uid());

-- Only authenticated users can insert activities
CREATE POLICY "Authenticated users can insert activities" ON system_activities
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- Admin can update activities
CREATE POLICY "Admin can update activities" ON system_activities
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Admin can delete activities
CREATE POLICY "Admin can delete activities" ON system_activities
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_system_activities_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS trigger_update_system_activities_updated_at ON system_activities;
CREATE TRIGGER trigger_update_system_activities_updated_at
    BEFORE UPDATE ON system_activities
    FOR EACH ROW
    EXECUTE FUNCTION update_system_activities_updated_at();

-- Create function to log system activities
CREATE OR REPLACE FUNCTION log_system_activity(
    p_user_id UUID DEFAULT NULL,
    p_activity_type VARCHAR(100) DEFAULT 'system_action',
    p_description TEXT DEFAULT 'System activity',
    p_metadata JSONB DEFAULT '{}',
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_session_id VARCHAR(255) DEFAULT NULL,
    p_severity VARCHAR(20) DEFAULT 'info',
    p_category VARCHAR(50) DEFAULT 'general'
)
RETURNS UUID AS $$
DECLARE
    activity_id UUID;
BEGIN
    INSERT INTO system_activities (
        user_id,
        activity_type,
        description,
        metadata,
        ip_address,
        user_agent,
        session_id,
        severity,
        category
    ) VALUES (
        p_user_id,
        p_activity_type,
        p_description,
        p_metadata,
        p_ip_address,
        p_user_agent,
        p_session_id,
        p_severity,
        p_category
    ) RETURNING id INTO activity_id;
    
    RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get activity statistics
CREATE OR REPLACE FUNCTION get_activity_statistics(
    p_days INTEGER DEFAULT 7
)
RETURNS TABLE (
    total_activities BIGINT,
    unique_users BIGINT,
    top_activity_type TEXT,
    activities_by_day JSONB
) AS $$
DECLARE
    start_date TIMESTAMP WITH TIME ZONE;
BEGIN
    start_date := NOW() - (p_days || ' days')::INTERVAL;
    
    SELECT 
        COUNT(*) as total_count,
        COUNT(DISTINCT user_id) as unique_user_count,
        (
            SELECT activity_type 
            FROM system_activities 
            WHERE created_at >= start_date 
            GROUP BY activity_type 
            ORDER BY COUNT(*) DESC 
            LIMIT 1
        ) as top_type,
        (
            SELECT jsonb_object_agg(
                date_trunc('day', created_at)::date,
                count
            )
            FROM (
                SELECT 
                    date_trunc('day', created_at) as day,
                    COUNT(*) as count
                FROM system_activities 
                WHERE created_at >= start_date
                GROUP BY date_trunc('day', created_at)
                ORDER BY day
            ) daily_counts
        ) as daily_activities
    INTO total_activities, unique_users, top_activity_type, activities_by_day
    FROM system_activities
    WHERE created_at >= start_date;
    
    RETURN NEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to clean old activities (retention policy)
CREATE OR REPLACE FUNCTION cleanup_old_activities(
    p_retention_days INTEGER DEFAULT 90
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    cutoff_date TIMESTAMP WITH TIME ZONE;
BEGIN
    cutoff_date := NOW() - (p_retention_days || ' days')::INTERVAL;
    
    DELETE FROM system_activities 
    WHERE created_at < cutoff_date 
    AND severity NOT IN ('error', 'critical');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup activity
    PERFORM log_system_activity(
        NULL,
        'system_cleanup',
        'Cleaned up ' || deleted_count || ' old activity records',
        jsonb_build_object(
            'deleted_count', deleted_count,
            'retention_days', p_retention_days,
            'cutoff_date', cutoff_date
        ),
        NULL,
        NULL,
        NULL,
        'info',
        'system'
    );
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert some sample activities for testing
INSERT INTO system_activities (user_id, activity_type, description, metadata, severity, category) VALUES
    (NULL, 'system_startup', 'System started successfully', '{"version": "1.0.0"}', 'info', 'system'),
    (NULL, 'database_migration', 'Database schema updated', '{"migration": "add_activities_table"}', 'info', 'database'),
    (NULL, 'system_health_check', 'System health check completed', '{"status": "healthy"}', 'success', 'system')
ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT SELECT, INSERT ON system_activities TO authenticated;
GRANT EXECUTE ON FUNCTION log_system_activity TO authenticated;
GRANT EXECUTE ON FUNCTION get_activity_statistics TO authenticated;

-- Grant admin permissions
GRANT ALL ON system_activities TO service_role;
GRANT EXECUTE ON FUNCTION cleanup_old_activities TO service_role;

-- Create view for activity summary
CREATE OR REPLACE VIEW activity_summary AS
SELECT 
    DATE(created_at) as activity_date,
    activity_type,
    category,
    severity,
    COUNT(*) as count,
    COUNT(DISTINCT user_id) as unique_users
FROM system_activities
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at), activity_type, category, severity
ORDER BY activity_date DESC, count DESC;

-- Grant access to the view
GRANT SELECT ON activity_summary TO authenticated;

-- Create materialized view for performance (refresh daily)
CREATE MATERIALIZED VIEW IF NOT EXISTS activity_analytics AS
SELECT 
    DATE(created_at) as date,
    activity_type,
    category,
    severity,
    COUNT(*) as total_count,
    COUNT(DISTINCT user_id) as unique_users,
    AVG(EXTRACT(EPOCH FROM (created_at - LAG(created_at) OVER (PARTITION BY user_id ORDER BY created_at)))) as avg_time_between_activities
FROM system_activities
WHERE created_at >= NOW() - INTERVAL '90 days'
GROUP BY DATE(created_at), activity_type, category, severity
ORDER BY date DESC;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_activity_analytics_unique 
ON activity_analytics (date, activity_type, category, severity);

-- Grant access to materialized view
GRANT SELECT ON activity_analytics TO authenticated;

-- Create function to refresh analytics
CREATE OR REPLACE FUNCTION refresh_activity_analytics()
RETURNS VOID AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY activity_analytics;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION refresh_activity_analytics TO service_role;
