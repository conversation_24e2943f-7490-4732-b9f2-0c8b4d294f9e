import { supabase } from '@/integrations/supabase/client';
import { activityLogger } from '@/services/activityLogger';

/**
 * Test script to verify that the system activities fix is working correctly
 */

const testSystemActivities = async () => {
  console.log('🧪 Testing system activities fix...');

  try {
    // Test 1: Direct database insert
    console.log('📝 Test 1: Direct database insert...');
    const { data: directInsert, error: directError } = await supabase
      .from('system_activities')
      .insert({
        action: 'test_direct_insert',
        description: 'Testing direct database insert',
        metadata: { test: true, method: 'direct' },
        severity: 'info',
        category: 'system'
      })
      .select()
      .single();

    if (directError) {
      console.error('❌ Direct insert failed:', directError);
      return false;
    }

    console.log('✅ Direct insert successful:', directInsert.id);

    // Test 2: Activity logger service
    console.log('📝 Test 2: Activity logger service...');
    const activityId = await activityLogger.logActivity({
      action: 'test_service_insert',
      description: 'Testing activity logger service',
      metadata: { test: true, method: 'service' },
      severity: 'success',
      category: 'system'
    });

    if (!activityId) {
      console.error('❌ Service insert failed');
      return false;
    }

    console.log('✅ Service insert successful:', activityId);

    // Test 3: Database function
    console.log('📝 Test 3: Database function...');
    const { data: functionResult, error: functionError } = await supabase
      .rpc('log_system_activity', {
        p_action: 'test_function_insert',
        p_description: 'Testing database function',
        p_metadata: { test: true, method: 'function' },
        p_severity: 'info',
        p_category: 'system'
      });

    if (functionError) {
      console.error('❌ Function insert failed:', functionError);
      return false;
    }

    console.log('✅ Function insert successful:', functionResult);

    // Test 4: Query with joins
    console.log('📝 Test 4: Query with joins...');
    const { data: joinQuery, error: joinError } = await supabase
      .from('system_activities')
      .select(`
        *,
        profiles:user_id (
          full_name,
          email
        )
      `)
      .limit(5);

    if (joinError) {
      console.error('❌ Join query failed:', joinError);
      return false;
    }

    console.log('✅ Join query successful, found', joinQuery?.length || 0, 'activities');

    // Test 5: Department query
    console.log('📝 Test 5: Department query...');
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select('*')
      .limit(5);

    if (deptError) {
      console.error('❌ Department query failed:', deptError);
      return false;
    }

    console.log('✅ Department query successful, found', departments?.length || 0, 'departments');

    // Test 6: Predefined activity methods
    console.log('📝 Test 6: Predefined activity methods...');
    
    const loginId = await activityLogger.logUserLogin();
    if (!loginId) {
      console.error('❌ Login activity failed');
      return false;
    }
    console.log('✅ Login activity successful:', loginId);

    const errorId = await activityLogger.logSystemError('Test error', { test: true });
    if (!errorId) {
      console.error('❌ Error activity failed');
      return false;
    }
    console.log('✅ Error activity successful:', errorId);

    const projectId = await activityLogger.logProjectAction('created', 'test-project-id');
    if (!projectId) {
      console.error('❌ Project activity failed');
      return false;
    }
    console.log('✅ Project activity successful:', projectId);

    // Test 7: Complex query with department join
    console.log('📝 Test 7: Complex query with department join...');
    const { data: complexQuery, error: complexError } = await supabase
      .from('system_activities')
      .select(`
        *,
        profiles:user_id (
          full_name,
          email,
          department:department_id (
            name,
            description
          )
        )
      `)
      .eq('action', 'test_direct_insert')
      .limit(1);

    if (complexError) {
      console.warn('⚠️ Complex query failed (this might be expected if department_id doesn\'t exist):', complexError);
    } else {
      console.log('✅ Complex query successful');
    }

    console.log('🎉 All tests passed! System activities are working correctly.');
    return true;

  } catch (error) {
    console.error('❌ Test suite failed:', error);
    return false;
  }
};

// Export for use in other files
export { testSystemActivities };

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('test-activities') === 'true') {
    testSystemActivities();
  }
}

export default testSystemActivities;
