import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export const LoginDebugger = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const { user, session, userProfile, loading: authLoading, initialized } = useAuth();

  const runDiagnostics = async () => {
    setLoading(true);
    const info: any = {
      timestamp: new Date().toISOString(),
      environment: {
        hostname: window.location.hostname,
        protocol: window.location.protocol,
        userAgent: navigator.userAgent,
        localStorage: typeof localStorage !== 'undefined',
        online: navigator.onLine
      },
      auth: {
        user: !!user,
        session: !!session,
        userProfile: !!userProfile,
        authLoading,
        initialized,
        userId: user?.id || 'none',
        userEmail: user?.email || 'none'
      },
      supabase: {
        url: 'https://dvflgnqwbsjityrowatf.supabase.co',
        connected: false,
        profilesTableExists: false,
        canQueryProfiles: false
      }
    };

    try {
      // Test Supabase connection
      const { data: healthCheck, error: healthError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      info.supabase.connected = !healthError;
      info.supabase.profilesTableExists = !healthError;
      info.supabase.healthError = healthError?.message;

      // Test profile query if user exists
      if (user?.id) {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        info.supabase.canQueryProfiles = !profileError;
        info.supabase.profileError = profileError?.message;
        info.supabase.profileData = profile;
      }

      // Test auth session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      info.auth.sessionCheck = {
        hasSession: !!sessionData.session,
        error: sessionError?.message,
        userId: sessionData.session?.user?.id
      };

    } catch (error: any) {
      info.supabase.connectionError = error.message;
    }

    setDebugInfo(info);
    setLoading(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, [user, session, userProfile]);

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === undefined) return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    return status ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusBadge = (status: boolean | undefined, label: string) => {
    const variant = status === undefined ? 'secondary' : status ? 'default' : 'destructive';
    return <Badge variant={variant}>{label}</Badge>;
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Login Diagnostics
          <Button onClick={runDiagnostics} disabled={loading} size="sm">
            {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Refresh'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Environment Status */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            {getStatusIcon(debugInfo.environment?.localStorage)}
            <span className="text-sm">LocalStorage</span>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(debugInfo.environment?.online)}
            <span className="text-sm">Online</span>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(debugInfo.supabase?.connected)}
            <span className="text-sm">Supabase</span>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(debugInfo.auth?.initialized)}
            <span className="text-sm">Auth Init</span>
          </div>
        </div>

        {/* Auth Status */}
        <div className="space-y-2">
          <h3 className="font-semibold">Authentication Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {getStatusBadge(debugInfo.auth?.user, 'User')}
            {getStatusBadge(debugInfo.auth?.session, 'Session')}
            {getStatusBadge(debugInfo.auth?.userProfile, 'Profile')}
            {getStatusBadge(!debugInfo.auth?.authLoading, 'Loaded')}
          </div>
          {debugInfo.auth?.userId && (
            <p className="text-sm text-muted-foreground">User ID: {debugInfo.auth.userId}</p>
          )}
          {debugInfo.auth?.userEmail && (
            <p className="text-sm text-muted-foreground">Email: {debugInfo.auth.userEmail}</p>
          )}
        </div>

        {/* Database Status */}
        <div className="space-y-2">
          <h3 className="font-semibold">Database Status</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {getStatusBadge(debugInfo.supabase?.connected, 'Connected')}
            {getStatusBadge(debugInfo.supabase?.profilesTableExists, 'Profiles Table')}
            {getStatusBadge(debugInfo.supabase?.canQueryProfiles, 'Can Query')}
          </div>
          {debugInfo.supabase?.healthError && (
            <p className="text-sm text-red-600">Health Error: {debugInfo.supabase.healthError}</p>
          )}
          {debugInfo.supabase?.profileError && (
            <p className="text-sm text-red-600">Profile Error: {debugInfo.supabase.profileError}</p>
          )}
        </div>

        {/* Raw Debug Data */}
        <details className="mt-4">
          <summary className="cursor-pointer font-semibold">Raw Debug Data</summary>
          <pre className="mt-2 p-4 bg-muted rounded text-xs overflow-auto max-h-96">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </details>
      </CardContent>
    </Card>
  );
};
