import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';

/**
 * Enhanced AI Service
 * This AI acts as an intelligent organization member that knows everything about the system
 */

interface AIContext {
  user: any;
  organization: {
    name: string;
    departments: any[];
    projects: any[];
    members: any[];
    recentActivities: any[];
  };
  currentSession: {
    timestamp: string;
    userRole: string;
    department: string;
  };
}

interface AIResponse {
  message: string;
  actions?: Array<{
    type: string;
    label: string;
    data: any;
  }>;
  suggestions?: string[];
  context?: any;
}

export class EnhancedAIService {
  private static context: AIContext | null = null;
  private static knowledgeBase: Map<string, any> = new Map();

  // Initialize AI with comprehensive organization knowledge
  static async initialize(): Promise<void> {
    try {
      console.log('🤖 Initializing Enhanced AI Service...');
      
      // Load current user
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      // Load organization data
      const [departments, projects, members] = await Promise.all([
        ComprehensiveAPI.getAllDepartments(),
        ComprehensiveAPI.getAllProjects(),
        ComprehensiveAPI.getAllProfiles()
      ]);

      // Load recent activities
      const { data: recentActivities } = await supabase
        .from('system_activities')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      this.context = {
        user: currentUser,
        organization: {
          name: 'CTN Nigeria',
          departments: departments || [],
          projects: projects || [],
          members: members || [],
          recentActivities: recentActivities || []
        },
        currentSession: {
          timestamp: new Date().toISOString(),
          userRole: currentUser?.role || 'staff',
          department: currentUser?.department_id || 'unknown'
        }
      };

      // Build knowledge base
      await this.buildKnowledgeBase();
      
      console.log('✅ Enhanced AI Service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced AI Service:', error);
    }
  }

  // Build comprehensive knowledge base
  private static async buildKnowledgeBase(): Promise<void> {
    if (!this.context) return;

    const kb = this.knowledgeBase;
    
    // Organization structure
    kb.set('organization_structure', {
      departments: this.context.organization.departments.map(d => ({
        name: d.name,
        manager: d.manager?.full_name,
        memberCount: this.context.organization.members.filter(m => m.department_id === d.id).length
      })),
      totalMembers: this.context.organization.members.length,
      activeProjects: this.context.organization.projects.filter(p => p.status === 'active').length
    });

    // Project insights
    kb.set('project_insights', {
      totalProjects: this.context.organization.projects.length,
      byStatus: this.groupBy(this.context.organization.projects, 'status'),
      byPriority: this.groupBy(this.context.organization.projects, 'priority'),
      recentProjects: this.context.organization.projects
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5)
    });

    // Member insights
    kb.set('member_insights', {
      totalMembers: this.context.organization.members.length,
      byRole: this.groupBy(this.context.organization.members, 'role'),
      byDepartment: this.groupBy(this.context.organization.members, 'department_id'),
      activeMembers: this.context.organization.members.filter(m => m.status === 'active').length
    });

    // System capabilities
    kb.set('system_capabilities', {
      features: [
        'Project Management',
        'Member Assignment',
        'Memo Submission',
        'Report Generation',
        'Task Management',
        'Department Organization',
        'AI Assistance',
        'Activity Logging'
      ],
      workflows: [
        'Create Project → Assign Members → Track Progress',
        'Submit Memo → Review → Publish',
        'Generate Report → Submit → Review → Approve',
        'Create Task → Assign → Complete'
      ]
    });

    // Common queries and responses
    kb.set('common_queries', {
      'how to create project': 'I can help you create a new project. You need to provide: project name, description, client name (optional), budget, location, start/end dates, and assign a manager.',
      'how to add members': 'To add members to a project, go to the project details and use the "Add Member" button. You can assign roles and allocate hours.',
      'how to submit memo': 'To submit a memo, use the memo form with title, content, type (general/policy/announcement/urgent/meeting), priority, and target audience.',
      'how to submit report': 'For reports, provide title, description, type (general/financial/project/hr/technical), priority, and attach any relevant files.',
      'project status': 'Projects can have these statuses: planning, active, on_hold, completed, cancelled. You can update status in project settings.',
      'user roles': 'We have these roles: admin (full access), manager (department management), staff-admin (administrative support), hr (human resources), accountant (financial), staff (general employees).'
    });
  }

  // Process AI chat message
  static async processMessage(message: string): Promise<AIResponse> {
    if (!this.context) {
      await this.initialize();
    }

    const lowerMessage = message.toLowerCase();
    
    // Log the interaction
    await this.logInteraction(message, 'user');

    // Analyze intent and generate response
    const response = await this.generateResponse(lowerMessage);
    
    // Log AI response
    await this.logInteraction(response.message, 'assistant');
    
    return response;
  }

  // Generate intelligent response based on context
  private static async generateResponse(message: string): Promise<AIResponse> {
    const kb = this.knowledgeBase;
    const context = this.context!;
    
    // Project-related queries
    if (message.includes('project') || message.includes('create project')) {
      if (message.includes('create') || message.includes('new')) {
        return {
          message: `I can help you create a new project! As a ${context.user?.role}, you have the authority to create projects. Here's what I need:

📋 **Required Information:**
• Project name
• Description
• Client name (if applicable)
• Budget estimate
• Location
• Start and end dates
• Priority level (low/medium/high/urgent)

🎯 **Current Organization Status:**
• Active projects: ${kb.get('project_insights')?.byStatus?.active || 0}
• Your department: ${context.organization.departments.find(d => d.id === context.user?.department_id)?.name || 'Unknown'}
• Available team members: ${kb.get('member_insights')?.activeMembers || 0}

Would you like me to guide you through the project creation process?`,
          actions: [
            {
              type: 'navigate',
              label: 'Create New Project',
              data: { route: '/projects/new' }
            },
            {
              type: 'help',
              label: 'Project Creation Guide',
              data: { topic: 'project_creation' }
            }
          ],
          suggestions: [
            'Show me existing projects',
            'What are the project requirements?',
            'How do I assign team members?'
          ]
        };
      } else if (message.includes('list') || message.includes('show')) {
        const projectInsights = kb.get('project_insights');
        return {
          message: `📊 **Current Projects Overview:**

🎯 **Total Projects:** ${projectInsights?.totalProjects || 0}

📈 **By Status:**
${Object.entries(projectInsights?.byStatus || {}).map(([status, count]) => 
  `• ${status.charAt(0).toUpperCase() + status.slice(1)}: ${count}`
).join('\n')}

⚡ **By Priority:**
${Object.entries(projectInsights?.byPriority || {}).map(([priority, count]) => 
  `• ${priority.charAt(0).toUpperCase() + priority.slice(1)}: ${count}`
).join('\n')}

🆕 **Recent Projects:**
${projectInsights?.recentProjects?.slice(0, 3).map((p: any) => 
  `• ${p.name} (${p.status})`
).join('\n') || 'No recent projects'}`,
          actions: [
            {
              type: 'navigate',
              label: 'View All Projects',
              data: { route: '/projects' }
            },
            {
              type: 'filter',
              label: 'Filter by Status',
              data: { type: 'project_status' }
            }
          ],
          suggestions: [
            'Create a new project',
            'Show project details',
            'Assign members to project'
          ]
        };
      }
    }

    // Member-related queries
    if (message.includes('member') || message.includes('team') || message.includes('assign')) {
      const memberInsights = kb.get('member_insights');
      return {
        message: `👥 **Team Management:**

🏢 **Organization Overview:**
• Total members: ${memberInsights?.totalMembers || 0}
• Active members: ${memberInsights?.activeMembers || 0}
• Departments: ${context.organization.departments.length}

👔 **By Role:**
${Object.entries(memberInsights?.byRole || {}).map(([role, count]) => 
  `• ${role.charAt(0).toUpperCase() + role.slice(1)}: ${count}`
).join('\n')}

🏢 **By Department:**
${context.organization.departments.slice(0, 5).map(d => {
  const memberCount = context.organization.members.filter(m => m.department_id === d.id).length;
  return `• ${d.name}: ${memberCount} members`;
}).join('\n')}

As a ${context.user?.role}, you can ${context.user?.role === 'admin' || context.user?.role === 'manager' ? 'assign members to projects and manage team assignments' : 'view team information and your assignments'}.`,
        actions: [
          {
            type: 'navigate',
            label: 'View Team Members',
            data: { route: '/team' }
          },
          {
            type: 'action',
            label: 'Assign to Project',
            data: { action: 'assign_member' }
          }
        ],
        suggestions: [
          'Show my assignments',
          'How to assign members?',
          'View department members'
        ]
      };
    }

    // Memo-related queries
    if (message.includes('memo') || message.includes('announcement')) {
      return {
        message: `📝 **Memo Management:**

I can help you create and manage memos! Here are the types available:

📋 **Memo Types:**
• **General** - Regular communications
• **Policy** - Policy updates and changes
• **Announcement** - Important announcements
• **Urgent** - Time-sensitive information
• **Meeting** - Meeting notices and agendas

🎯 **Visibility Options:**
• **Public** - All organization members
• **Department** - Your department only
• **Private** - Specific individuals
• **Managers Only** - Management team

📊 **Priority Levels:**
• Low, Medium, High, Urgent

As a ${context.user?.role}, you can create memos and they will be automatically routed to the appropriate audience.`,
        actions: [
          {
            type: 'navigate',
            label: 'Create New Memo',
            data: { route: '/memos/new' }
          },
          {
            type: 'navigate',
            label: 'View All Memos',
            data: { route: '/memos' }
          }
        ],
        suggestions: [
          'Create urgent memo',
          'View recent memos',
          'Memo submission guidelines'
        ]
      };
    }

    // Report-related queries
    if (message.includes('report') || message.includes('submit report')) {
      return {
        message: `📊 **Report Management:**

I can assist you with report creation and submission!

📋 **Report Types:**
• **General** - Standard reports
• **Financial** - Budget and financial reports
• **Project** - Project progress and status
• **HR** - Human resources reports
• **Technical** - Technical documentation
• **Battery** - Battery system reports
• **Telecom** - Telecommunications reports

⚡ **Report Process:**
1. **Draft** - Create and edit
2. **Submit** - Send for review
3. **Under Review** - Being evaluated
4. **Approved** - Accepted and published
5. **Rejected** - Needs revision

🎯 **Priority Levels:**
• Low, Medium, High, Urgent

Your role (${context.user?.role}) allows you to submit reports, and they will be routed to the appropriate reviewers.`,
        actions: [
          {
            type: 'navigate',
            label: 'Submit New Report',
            data: { route: '/reports/new' }
          },
          {
            type: 'navigate',
            label: 'View My Reports',
            data: { route: '/reports/my' }
          }
        ],
        suggestions: [
          'Report submission guidelines',
          'View report templates',
          'Check report status'
        ]
      };
    }

    // System help and capabilities
    if (message.includes('help') || message.includes('what can you do') || message.includes('capabilities')) {
      const capabilities = kb.get('system_capabilities');
      return {
        message: `🤖 **AI Assistant Capabilities:**

I'm your intelligent organization assistant! Here's what I can help you with:

🎯 **Core Features:**
${capabilities?.features?.map((f: string) => `• ${f}`).join('\n') || ''}

🔄 **Common Workflows:**
${capabilities?.workflows?.map((w: string) => `• ${w}`).join('\n') || ''}

💡 **I can help you:**
• Navigate the system efficiently
• Understand processes and procedures
• Get real-time organization insights
• Create projects, memos, and reports
• Manage team assignments
• Track progress and activities

🎭 **Your Current Context:**
• Role: ${context.user?.role}
• Department: ${context.organization.departments.find(d => d.id === context.user?.department_id)?.name || 'Unknown'}
• Access Level: ${this.getAccessLevel(context.user?.role)}

Just ask me anything about the system, and I'll provide contextual help!`,
        suggestions: [
          'Create a new project',
          'Submit a memo',
          'View team members',
          'Generate a report',
          'Show my tasks'
        ]
      };
    }

    // Default response with context
    return {
      message: `Hello! I'm your AI assistant for CTN Nigeria. I understand you're asking about "${message}".

🎯 **Quick Context:**
• You're logged in as: ${context.user?.full_name} (${context.user?.role})
• Organization: ${context.organization.name}
• Active projects: ${kb.get('project_insights')?.byStatus?.active || 0}
• Team members: ${kb.get('member_insights')?.totalMembers || 0}

I can help you with:
• Project management and creation
• Team member assignments
• Memo and report submission
• System navigation and guidance
• Organization insights and analytics

What would you like to do today?`,
      suggestions: [
        'Create a new project',
        'Submit a memo',
        'View team members',
        'Generate a report',
        'Show system capabilities'
      ]
    };
  }

  // Helper methods
  private static groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((result, item) => {
      const group = item[key] || 'unknown';
      result[group] = (result[group] || 0) + 1;
      return result;
    }, {});
  }

  private static getAccessLevel(role: string): string {
    const levels = {
      admin: 'Full System Access',
      manager: 'Department Management',
      'staff-admin': 'Administrative Support',
      hr: 'Human Resources',
      accountant: 'Financial Management',
      staff: 'Standard User'
    };
    return levels[role as keyof typeof levels] || 'Standard User';
  }

  private static async logInteraction(message: string, role: 'user' | 'assistant'): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      await supabase.from('ai_interactions').insert({
        user_id: currentUser?.id,
        role,
        message,
        type: 'chat',
        metadata: {
          timestamp: new Date().toISOString(),
          session_id: this.context?.currentSession.timestamp,
          user_role: currentUser?.role
        }
      });
    } catch (error) {
      console.error('Error logging AI interaction:', error);
    }
  }

  // Get organization insights
  static getOrganizationInsights(): any {
    return {
      context: this.context,
      knowledgeBase: Object.fromEntries(this.knowledgeBase)
    };
  }

  // Refresh context
  static async refreshContext(): Promise<void> {
    await this.initialize();
  }
}

export default EnhancedAIService;
