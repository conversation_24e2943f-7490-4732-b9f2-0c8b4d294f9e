import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface AgentResponseRequest {
  query: string
  userContext: any
  actions: any[]
  toolResults: any[]
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { query, userContext, actions, toolResults }: AgentResponseRequest = await req.json()

    if (!query) {
      return new Response(
        JSON.stringify({ error: 'Query is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get OpenAI API key
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const { data: apiKeys, error: keyError } = await supabase
      .from('api_keys')
      .select('api_key')
      .eq('provider', 'openai')
      .eq('is_active', true)
      .single()

    if (keyError || !apiKeys?.api_key) {
      return new Response(
        JSON.stringify(generateFallbackResponse(query, toolResults)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Build prompt for response generation
    const prompt = buildResponsePrompt(query, userContext, actions, toolResults)

    // Call OpenAI to generate response
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKeys.api_key}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: buildSystemPrompt(userContext)
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1500,
      }),
    })

    if (!openaiResponse.ok) {
      console.error('OpenAI API error:', await openaiResponse.text())
      return new Response(
        JSON.stringify(generateFallbackResponse(query, toolResults)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const aiResponse = await openaiResponse.json()
    const content = aiResponse.choices[0]?.message?.content

    if (!content) {
      return new Response(
        JSON.stringify(generateFallbackResponse(query, toolResults)),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Calculate confidence based on tool results and response quality
    const confidence = calculateConfidence(toolResults, content)

    return new Response(
      JSON.stringify({
        message: content,
        confidence,
        reasoning: `Generated response based on ${actions.length} action(s) and ${toolResults.filter(r => r.success).length} successful tool execution(s)`,
        tokensUsed: aiResponse.usage?.total_tokens || 0
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in generate-agent-response function:', error)
    return new Response(
      JSON.stringify({ error: 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function buildSystemPrompt(userContext: any): string {
  const userName = userContext?.userProfile?.full_name || 'User'
  const userRole = userContext?.userProfile?.role || 'user'
  const department = userContext?.userProfile?.department?.name || 'Unknown'

  return `You are an AI assistant for CTNL AI Workboard, a comprehensive business management platform.

Current User Context:
- Name: ${userName}
- Role: ${userRole}
- Department: ${department}

Your capabilities:
1. Answer questions about the platform and business processes
2. Help with task management, project tracking, and team collaboration
3. Provide insights based on available data and documents
4. Execute actions using available tools when needed
5. Offer guidance on best practices and workflows

Guidelines:
- Be helpful, professional, and concise
- Provide actionable advice when possible
- Reference specific data when available from tool results
- Acknowledge limitations when you don't have enough information
- Use the user's name and context to personalize responses
- Focus on business productivity and efficiency`
}

function buildResponsePrompt(query: string, userContext: any, actions: any[], toolResults: any[]): string {
  let prompt = `User Query: "${query}"\n\n`

  if (actions.length > 0) {
    prompt += `Actions Taken:\n`
    actions.forEach((action, index) => {
      prompt += `${index + 1}. ${action.tool}: ${action.reasoning}\n`
    })
    prompt += '\n'
  }

  if (toolResults.length > 0) {
    prompt += `Tool Results:\n`
    toolResults.forEach((result, index) => {
      prompt += `${index + 1}. ${actions[index]?.tool || 'Unknown'}: `
      if (result.success) {
        prompt += `Success - ${JSON.stringify(result.data || result).substring(0, 200)}\n`
      } else {
        prompt += `Failed - ${result.error}\n`
      }
    })
    prompt += '\n'
  }

  prompt += `Please provide a helpful response to the user's query based on the above information. Be specific about what was found or accomplished, and provide actionable next steps if appropriate.`

  return prompt
}

function generateFallbackResponse(query: string, toolResults: any[]): any {
  const successfulResults = toolResults.filter(r => r.success)
  
  let message = "I understand your request"
  
  if (successfulResults.length > 0) {
    message += ` and was able to gather some information for you. I executed ${successfulResults.length} action(s) successfully.`
  } else if (toolResults.length > 0) {
    message += ", but encountered some issues while trying to help. Please try rephrasing your request or contact support if the problem persists."
  } else {
    message += ". How can I help you with CTNL AI Workboard today?"
  }

  return {
    message,
    confidence: successfulResults.length > 0 ? 0.6 : 0.3,
    reasoning: "Generated fallback response due to API limitations",
    tokensUsed: 0
  }
}

function calculateConfidence(toolResults: any[], responseContent: string): number {
  let confidence = 0.5 // Base confidence

  // Increase confidence based on successful tool results
  const successfulResults = toolResults.filter(r => r.success)
  const successRate = toolResults.length > 0 ? successfulResults.length / toolResults.length : 1
  confidence += successRate * 0.3

  // Increase confidence based on response length and quality
  if (responseContent.length > 100) {
    confidence += 0.1
  }
  if (responseContent.length > 300) {
    confidence += 0.1
  }

  // Cap at 1.0
  return Math.min(confidence, 1.0)
}
