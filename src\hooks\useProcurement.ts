import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProcurementAPI, ProcurementRequest, ProcurementItem, ProcurementApproval, ProcurementInvoice } from '@/lib/procurement-api';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';

// ============= PROCUREMENT REQUESTS HOOK =============

export const useProcurementRequests = (filters?: {
  status?: string;
  requested_by?: string;
  department_id?: string;
  project_id?: string;
}) => {
  const { toast } = useToast();

  const {
    data: requests,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['procurement-requests', filters],
    queryFn: async () => {
      const { data, error } = await ProcurementAPI.getProcurementRequests(filters);
      if (error) throw new Error(error.message);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    requests: requests || [],
    loading: isLoading,
    error,
    refetch
  };
};

// ============= SINGLE PROCUREMENT REQUEST HOOK =============

export const useProcurementRequest = (id: string) => {
  const { toast } = useToast();

  const {
    data: request,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['procurement-request', id],
    queryFn: async () => {
      const { data, error } = await ProcurementAPI.getProcurementRequestById(id);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  return {
    request,
    loading: isLoading,
    error,
    refetch
  };
};

// ============= PROCUREMENT MUTATIONS HOOK =============

export const useProcurementMutations = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { userProfile } = useAuth();

  // Create procurement request
  const createRequestMutation = useMutation({
    mutationFn: async (requestData: Partial<ProcurementRequest>) => {
      const { data, error } = await ProcurementAPI.createProcurementRequest(requestData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      toast({
        title: "Success",
        description: "Procurement request created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create procurement request",
        variant: "destructive",
      });
    },
  });

  // Update procurement request
  const updateRequestMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<ProcurementRequest> }) => {
      const { data, error } = await ProcurementAPI.updateProcurementRequest(id, updates);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      queryClient.invalidateQueries({ queryKey: ['procurement-request', data?.id] });
      toast({
        title: "Success",
        description: "Procurement request updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update procurement request",
        variant: "destructive",
      });
    },
  });

  // Add procurement item
  const addItemMutation = useMutation({
    mutationFn: async (itemData: Partial<ProcurementItem>) => {
      const { data, error } = await ProcurementAPI.addProcurementItem(itemData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      queryClient.invalidateQueries({ queryKey: ['procurement-request', data?.procurement_request_id] });
      toast({
        title: "Success",
        description: "Item added to procurement request",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add item",
        variant: "destructive",
      });
    },
  });

  // Update procurement item
  const updateItemMutation = useMutation({
    mutationFn: async ({ id, updates }: { id: string; updates: Partial<ProcurementItem> }) => {
      const { data, error } = await ProcurementAPI.updateProcurementItem(id, updates);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      queryClient.invalidateQueries({ queryKey: ['procurement-request', data?.procurement_request_id] });
      toast({
        title: "Success",
        description: "Item updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update item",
        variant: "destructive",
      });
    },
  });

  // Delete procurement item
  const deleteItemMutation = useMutation({
    mutationFn: async (id: string) => {
      const { data, error } = await ProcurementAPI.deleteProcurementItem(id);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      toast({
        title: "Success",
        description: "Item deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete item",
        variant: "destructive",
      });
    },
  });

  // Submit for approval
  const submitForApprovalMutation = useMutation({
    mutationFn: async (requestId: string) => {
      const { data, error } = await ProcurementAPI.submitForApproval(requestId);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      toast({
        title: "Success",
        description: "Request submitted for manager approval",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to submit for approval",
        variant: "destructive",
      });
    },
  });

  // Approve request
  const approveRequestMutation = useMutation({
    mutationFn: async ({ requestId, approvalData }: { 
      requestId: string; 
      approvalData: { comments?: string; approved_amount?: number; conditions?: string; }
    }) => {
      const { data, error } = await ProcurementAPI.approveRequest(requestId, approvalData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      toast({
        title: "Success",
        description: "Request approved successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to approve request",
        variant: "destructive",
      });
    },
  });

  // Reject request
  const rejectRequestMutation = useMutation({
    mutationFn: async ({ requestId, comments }: { requestId: string; comments: string }) => {
      const { data, error } = await ProcurementAPI.rejectRequest(requestId, comments);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      toast({
        title: "Success",
        description: "Request rejected",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to reject request",
        variant: "destructive",
      });
    },
  });

  // Create invoice
  const createInvoiceMutation = useMutation({
    mutationFn: async (invoiceData: Partial<ProcurementInvoice>) => {
      const { data, error } = await ProcurementAPI.createInvoice(invoiceData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      toast({
        title: "Success",
        description: "Invoice created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create invoice",
        variant: "destructive",
      });
    },
  });

  // Mark as procured
  const markAsProcuredMutation = useMutation({
    mutationFn: async (requestId: string) => {
      const { data, error } = await ProcurementAPI.markAsProcured(requestId);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['procurement-requests'] });
      queryClient.invalidateQueries({ queryKey: ['staff-admin-expenses'] }); // Refresh expenses
      toast({
        title: "Success",
        description: "Items marked as procured and added to expenses",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to mark as procured",
        variant: "destructive",
      });
    },
  });

  return {
    createRequest: createRequestMutation.mutate,
    updateRequest: updateRequestMutation.mutate,
    addItem: addItemMutation.mutate,
    updateItem: updateItemMutation.mutate,
    deleteItem: deleteItemMutation.mutate,
    submitForApproval: submitForApprovalMutation.mutate,
    approveRequest: approveRequestMutation.mutate,
    rejectRequest: rejectRequestMutation.mutate,
    createInvoice: createInvoiceMutation.mutate,
    markAsProcured: markAsProcuredMutation.mutate,
    
    // Loading states
    isCreatingRequest: createRequestMutation.isPending,
    isUpdatingRequest: updateRequestMutation.isPending,
    isAddingItem: addItemMutation.isPending,
    isUpdatingItem: updateItemMutation.isPending,
    isDeletingItem: deleteItemMutation.isPending,
    isSubmittingForApproval: submitForApprovalMutation.isPending,
    isApprovingRequest: approveRequestMutation.isPending,
    isRejectingRequest: rejectRequestMutation.isPending,
    isCreatingInvoice: createInvoiceMutation.isPending,
    isMarkingAsProcured: markAsProcuredMutation.isPending,
  };
};

// ============= PROCUREMENT DASHBOARD HOOK =============

export const useProcurementDashboard = () => {
  const { userProfile } = useAuth();
  const [filters, setFilters] = useState<any>({});

  // Get requests based on user role
  const getFiltersForRole = () => {
    if (!userProfile) return {};
    
    switch (userProfile.role) {
      case 'staff':
        return { requested_by: userProfile.id };
      case 'manager':
        return { status: 'manager_review' };
      case 'admin':
      case 'staff-admin':
        return { status: 'admin_processing' };
      case 'accountant':
        return { status: 'accountant_review' };
      default:
        return {};
    }
  };

  const { requests, loading, refetch } = useProcurementRequests({
    ...filters,
    ...getFiltersForRole()
  });

  // Get summary statistics
  const summary = {
    total: requests.length,
    pending: requests.filter(r => r.status === 'pending').length,
    approved: requests.filter(r => r.approval_status === 'approved').length,
    rejected: requests.filter(r => r.approval_status === 'rejected').length,
    procured: requests.filter(r => r.status === 'procured').length,
    totalValue: requests.reduce((sum, r) => sum + (r.actual_total_cost || r.estimated_total_cost), 0)
  };

  return {
    requests,
    loading,
    refetch,
    summary,
    filters,
    setFilters,
    userRole: userProfile?.role
  };
};

// ============= EXPORT HOOK =============

export const useProcurementExport = () => {
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);

  const exportData = async (format: 'pdf' | 'xlsx' | 'docx' | 'csv', filters?: any) => {
    setIsExporting(true);
    try {
      const { data, error } = await ProcurementAPI.exportProcurementData(format, filters);
      if (error) throw new Error(error.message);
      
      // Here you would implement the actual export logic
      // For now, we'll just show a success message
      toast({
        title: "Export Started",
        description: `Exporting procurement data as ${format.toUpperCase()}...`,
      });
      
      return data;
    } catch (error: any) {
      toast({
        title: "Export Failed",
        description: error.message || "Failed to export data",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsExporting(false);
    }
  };

  return {
    exportData,
    isExporting
  };
};
