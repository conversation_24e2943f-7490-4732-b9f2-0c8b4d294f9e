import { supabase } from "@/integrations/supabase/client";

export interface FolderData {
  name: string;
  description?: string;
  type?: string;
  parent_folder_id?: string;
  access_level?: 'public' | 'department' | 'private';
  manager_id?: string;
}

export interface SystemFolder {
  id: string;
  name: string;
  description?: string;
  type: string;
  parent_folder_id?: string;
  access_level: string;
  manager_id?: string;
  document_count?: number;
  created_at: string;
  updated_at: string;
}

// System folder types that should exist at root level
export const SYSTEM_FOLDER_TYPES = [
  {
    type: 'projects',
    name: 'Projects',
    description: 'Project-related documents and files',
    icon: '📁'
  },
  {
    type: 'departments',
    name: 'Departments',
    description: 'Department-specific documents',
    icon: '🏢'
  },
  {
    type: 'financial',
    name: 'Financial',
    description: 'Financial documents, invoices, and reports',
    icon: '💰'
  },
  {
    type: 'hr',
    name: 'Human Resources',
    description: 'HR policies, employee documents',
    icon: '👥'
  },
  {
    type: 'operations',
    name: 'Operations',
    description: 'Operational procedures and documentation',
    icon: '⚙️'
  },
  {
    type: 'legal',
    name: 'Legal',
    description: 'Legal documents and contracts',
    icon: '⚖️'
  },
  {
    type: 'archive',
    name: 'Archive',
    description: 'Archived and historical documents',
    icon: '📦'
  }
] as const;

export class FolderAPI {
  /**
   * Get all folders with hierarchy
   */
  static async getAllFolders(): Promise<{ data: SystemFolder[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select(`
          id,
          name,
          description,
          manager_id,
          created_at,
          updated_at,
          manager:profiles!manager_id(id, full_name, email)
        `)
        .order('name');

      if (error) throw error;

      // Transform departments to system folders
      const folders: SystemFolder[] = (data || []).map(dept => ({
        id: dept.id,
        name: dept.name,
        description: dept.description || undefined,
        type: 'department',
        access_level: 'department',
        manager_id: dept.manager_id || undefined,
        created_at: dept.created_at,
        updated_at: dept.updated_at
      }));

      return { data: folders, error: null };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Create a root level folder with system type
   */
  static async createRootFolder(folderData: FolderData): Promise<{ data: any; error: any }> {
    try {
      // Validate system type
      const validTypes = SYSTEM_FOLDER_TYPES.map(t => t.type);
      if (folderData.type && !validTypes.includes(folderData.type)) {
        throw new Error(`Invalid system type. Must be one of: ${validTypes.join(', ')}`);
      }

      // Check if folder with this name already exists at root level
      const { data: existing } = await supabase
        .from('departments')
        .select('id, name')
        .eq('name', folderData.name)
        .maybeSingle();

      if (existing) {
        throw new Error(`A folder with the name "${folderData.name}" already exists`);
      }

      const { data, error } = await supabase
        .from('departments')
        .insert([{
          name: folderData.name,
          description: folderData.description || `${folderData.type || 'System'} folder`,
          manager_id: folderData.manager_id || null
        }])
        .select(`
          id,
          name,
          description,
          manager_id,
          created_at,
          updated_at
        `)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Create a subfolder within an existing folder
   */
  static async createSubfolder(folderData: FolderData & { parent_folder_id: string }): Promise<{ data: any; error: any }> {
    try {
      // Verify parent folder exists
      const { data: parent } = await supabase
        .from('departments')
        .select('id, name')
        .eq('id', folderData.parent_folder_id)
        .single();

      if (!parent) {
        throw new Error('Parent folder not found');
      }

      const { data, error } = await supabase
        .from('departments')
        .insert([{
          name: folderData.name,
          description: folderData.description || `Subfolder of ${parent.name}`,
          manager_id: folderData.manager_id || null
        }])
        .select(`
          id,
          name,
          description,
          manager_id,
          created_at,
          updated_at
        `)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Update folder information
   */
  static async updateFolder(folderId: string, folderData: Partial<FolderData>): Promise<{ data: any; error: any }> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .update({
          name: folderData.name,
          description: folderData.description,
          manager_id: folderData.manager_id,
          updated_at: new Date().toISOString()
        })
        .eq('id', folderId)
        .select(`
          id,
          name,
          description,
          manager_id,
          created_at,
          updated_at
        `)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Delete folder (only if empty)
   */
  static async deleteFolder(folderId: string): Promise<{ data: any; error: any }> {
    try {
      // Check if folder has documents
      const { data: documents } = await supabase
        .from('document_archive')
        .select('id')
        .eq('department_id', folderId)
        .limit(1);

      if (documents && documents.length > 0) {
        throw new Error('Cannot delete folder that contains documents. Please move or delete documents first.');
      }

      // Check if folder has projects
      const { data: projects } = await supabase
        .from('projects')
        .select('id')
        .eq('department_id', folderId)
        .limit(1);

      if (projects && projects.length > 0) {
        throw new Error('Cannot delete folder that contains projects. Please move or delete projects first.');
      }

      const { data, error } = await supabase
        .from('departments')
        .delete()
        .eq('id', folderId)
        .select()
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Initialize system folders if they don't exist
   */
  static async initializeSystemFolders(): Promise<{ created: number; errors: string[] }> {
    let created = 0;
    const errors: string[] = [];

    for (const systemFolder of SYSTEM_FOLDER_TYPES) {
      try {
        // Check if folder already exists
        const { data: existing } = await supabase
          .from('departments')
          .select('id')
          .eq('name', systemFolder.name)
          .maybeSingle();

        if (!existing) {
          const result = await this.createRootFolder({
            name: systemFolder.name,
            description: systemFolder.description,
            type: systemFolder.type,
            access_level: 'department'
          });

          if (result.error) {
            errors.push(`Failed to create ${systemFolder.name}: ${result.error.message}`);
          } else {
            created++;
          }
        }
      } catch (error: any) {
        errors.push(`Error creating ${systemFolder.name}: ${error.message}`);
      }
    }

    return { created, errors };
  }

  /**
   * Get folder statistics
   */
  static async getFolderStats(folderId: string): Promise<{
    documentCount: number;
    projectCount: number;
    subfolderCount: number;
  }> {
    try {
      const [documents, projects] = await Promise.all([
        supabase
          .from('document_archive')
          .select('id', { count: 'exact' })
          .eq('department_id', folderId),
        supabase
          .from('projects')
          .select('id', { count: 'exact' })
          .eq('department_id', folderId)
      ]);

      return {
        documentCount: documents.count || 0,
        projectCount: projects.count || 0,
        subfolderCount: 0 // Departments don't have subfolders in current schema
      };
    } catch (error) {
      console.error('Error getting folder stats:', error);
      return {
        documentCount: 0,
        projectCount: 0,
        subfolderCount: 0
      };
    }
  }

  /**
   * Get available managers for folder assignment
   */
  static async getAvailableManagers(): Promise<{ data: any[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .in('role', ['manager', 'admin', 'staff-admin'])
        .order('full_name');

      return { data: data || [], error };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }
}
