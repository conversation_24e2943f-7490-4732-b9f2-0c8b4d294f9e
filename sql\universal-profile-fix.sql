-- ============================================================================
-- UNIVERSAL PROFILE FIX - WORKS FOR ANY USER
-- This fixes RLS and duplicate email issues for all users
-- Current issue: User 94827b40-c98a-4fd3-bfff-8cefc21fbbd6 (<EMAIL>)
-- ============================================================================

-- STEP 1: Disable RLS to see all profiles
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- STEP 2: Show current problematic state
SELECT 'CURRENT PROFILES WITH ISSUES:' as info;
SELECT id, email, full_name, role, status, created_at 
FROM public.profiles 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY email, created_at;

-- STEP 3: Clean up duplicate emails systematically
-- For <EMAIL> - keep the newest profile
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != (
    SELECT id FROM public.profiles 
    WHERE email = '<EMAIL>' 
    ORDER BY created_at DESC 
    LIMIT 1
  );

-- For <EMAIL> - keep the specific user we fixed earlier
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != '44349058-db4b-4a0b-8c99-8a913d07df74';

-- STEP 4: Ensure the current user profile exists
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '94827b40-c98a-4fd3-bfff-8cefc21fbbd6'::UUID,
    'Obibi Ifeanyi',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

-- STEP 5: Also ensure the previous user profile exists
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

-- STEP 6: Drop ALL existing RLS policies (comprehensive cleanup)
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_manager_department" ON public.profiles;
DROP POLICY IF EXISTS "profiles_hr_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_own" ON public.profiles;
DROP POLICY IF EXISTS "Staff-admin can view profiles" ON public.profiles;
DROP POLICY IF EXISTS "profiles_read_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_manager" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_hr" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_staff_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_admin" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_update" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;

-- STEP 7: Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- STEP 8: Create simple, universal policies that work for all users
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- STEP 9: Grant all necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;
GRANT ALL ON public.profiles TO service_role;

-- STEP 10: Update the profile creation function to handle conflicts better
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Use INSERT with ON CONFLICT to handle duplicates gracefully
  INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'role', 'staff'),
    'active',
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, profiles.full_name),
    updated_at = NOW();

  RETURN NEW;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but don't fail the user creation
    RAISE WARNING 'Failed to create profile for user %: %', NEW.id, SQLERRM;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 11: Recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- STEP 12: Final verification
SELECT 'FINAL STATE - ALL USERS:' as info;
SELECT id, email, full_name, role, status, created_at, updated_at
FROM public.profiles 
ORDER BY email, created_at;

-- Check for remaining duplicates
SELECT 'DUPLICATE CHECK:' as info;
SELECT email, COUNT(*) as count
FROM public.profiles 
GROUP BY email 
HAVING COUNT(*) > 1;

-- Verify RLS status
SELECT 'RLS STATUS:' as info;
SELECT schemaname, tablename, rowsecurity,
       (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'profiles' AND schemaname = 'public') as policy_count
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- List active policies
SELECT 'ACTIVE POLICIES:' as info;
SELECT policyname, cmd as operation
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;

SELECT 'UNIVERSAL FIX COMPLETED - ALL USERS SHOULD WORK NOW!' as final_status;
