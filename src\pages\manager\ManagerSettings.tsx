import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { UserProfileModule } from '@/components/profile/UserProfileModule'
import { Settings } from 'lucide-react'

const ManagerSettings = () => {
  return (
    <div className='space-y-6'>
      <div className='flex items-center gap-2 mb-6'>
        <Settings className='h-6 w-6' />
        <h1 className='text-2xl font-bold'>Manager Settings</h1>
      </div>

      <UserProfileModule />

      <Card>
        <CardHeader>
          <CardTitle>Manager Preferences</CardTitle>
        </CardHeader>
        <CardContent>
          <p className='text-muted-foreground'>
            Additional manager-specific settings will be available here.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}

export default ManagerSettings
