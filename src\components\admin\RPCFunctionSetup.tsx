import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import {
    CheckCircle,
    Code,
    Database,
    RefreshCw,
    XCircle,
    Zap
} from 'lucide-react';
import React, { useState } from 'react';
import { toast } from 'sonner';

export const RPCFunctionSetup: React.FC = () => {
  const [isCreating, setIsCreating] = useState(false);
  const [setupStatus, setSetupStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  const createRPCFunctions = async () => {
    setIsCreating(true);
    setSetupStatus('idle');
    setLogs([]);
    
    try {
      addLog('🚀 Creating RPC functions for time tracking...');
      
      // Create get_attendance_stats function
      addLog('1️⃣ Creating get_attendance_stats function...');
      const attendanceStatsSQL = `
        CREATE OR REPLACE FUNCTION get_attendance_stats()
        RETURNS JSON AS $$
        DECLARE
            result JSON;
        BEGIN
            SELECT json_build_object(
                'total_hours', COALESCE(SUM(total_hours), 0),
                'days_present', COUNT(DISTINCT DATE(clock_in)),
                'current_month_hours', COALESCE(SUM(
                    CASE WHEN DATE_TRUNC('month', clock_in) = DATE_TRUNC('month', CURRENT_DATE) 
                    THEN total_hours ELSE 0 END
                ), 0),
                'average_hours', COALESCE(AVG(total_hours), 0),
                'last_clock_in', MAX(clock_in),
                'user_count', COUNT(DISTINCT user_id)
            ) INTO result
            FROM public.time_logs 
            WHERE clock_in >= CURRENT_DATE - INTERVAL '30 days';
            
            RETURN COALESCE(result, '{}'::json);
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
      `;
      
      const { error: attendanceError } = await supabase.rpc('exec_sql', { sql: attendanceStatsSQL });
      if (attendanceError) {
        addLog(`❌ exec_sql not available: ${attendanceError.message}`);
        addLog('📋 Please run the SQL script manually in Supabase Dashboard');
        throw new Error('exec_sql function not available. Please use manual SQL script.');
      }
      addLog('✅ get_attendance_stats function created');
      
      // Create get_team_time_logs function
      addLog('2️⃣ Creating get_team_time_logs function...');
      const teamTimeLogsSQL = `
        CREATE OR REPLACE FUNCTION get_team_time_logs()
        RETURNS JSON AS $$
        DECLARE
            result JSON;
        BEGIN
            SELECT json_agg(
                json_build_object(
                    'id', tl.id,
                    'user_id', tl.user_id,
                    'clock_in', tl.clock_in,
                    'clock_out', tl.clock_out,
                    'total_hours', tl.total_hours,
                    'status', tl.status,
                    'notes', tl.notes,
                    'location', tl.location,
                    'user_name', COALESCE(p.full_name, 'Unknown User')
                )
            ) INTO result
            FROM public.time_logs tl
            LEFT JOIN public.profiles p ON tl.user_id = p.id
            WHERE tl.clock_in >= CURRENT_DATE - INTERVAL '7 days'
            ORDER BY tl.clock_in DESC
            LIMIT 100;
            
            RETURN COALESCE(result, '[]'::json);
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
      `;
      
      const { error: teamError } = await supabase.rpc('exec_sql', { sql: teamTimeLogsSQL });
      if (teamError) {
        addLog(`❌ Failed to create get_team_time_logs: ${teamError.message}`);
        throw teamError;
      }
      addLog('✅ get_team_time_logs function created');
      
      // Grant permissions
      addLog('3️⃣ Granting permissions...');
      const permissionsSQL = `
        GRANT EXECUTE ON FUNCTION get_attendance_stats() TO authenticated;
        GRANT EXECUTE ON FUNCTION get_team_time_logs() TO authenticated;
      `;
      
      const { error: permError } = await supabase.rpc('exec_sql', { sql: permissionsSQL });
      if (permError) {
        addLog(`⚠️ Permission warning: ${permError.message}`);
      } else {
        addLog('✅ Permissions granted');
      }
      
      // Test the functions
      addLog('4️⃣ Testing functions...');
      
      // Test attendance stats
      const { data: testAttendance, error: testAttendanceError } = await supabase.rpc('get_attendance_stats');
      if (testAttendanceError) {
        addLog(`⚠️ get_attendance_stats test warning: ${testAttendanceError.message}`);
      } else {
        addLog(`✅ get_attendance_stats test successful: ${JSON.stringify(testAttendance)}`);
      }
      
      // Test team time logs
      const { data: testTeam, error: testTeamError } = await supabase.rpc('get_team_time_logs');
      if (testTeamError) {
        addLog(`⚠️ get_team_time_logs test warning: ${testTeamError.message}`);
      } else {
        addLog(`✅ get_team_time_logs test successful: ${Array.isArray(testTeam) ? testTeam.length : 'data'} records`);
      }
      
      addLog('🎉 RPC functions setup completed successfully!');
      setSetupStatus('success');
      toast.success('RPC functions created successfully!');
      
    } catch (error) {
      console.error('RPC setup error:', error);
      addLog(`❌ RPC setup failed: ${error}`);
      setSetupStatus('error');
      toast.error('RPC setup failed. Check logs for details.');
    } finally {
      setIsCreating(false);
    }
  };

  const testRPCFunctions = async () => {
    try {
      addLog('🧪 Testing RPC functions...');
      
      // Test get_attendance_stats
      const { data: statsData, error: statsError } = await supabase.rpc('get_attendance_stats');
      if (statsError) {
        addLog(`❌ get_attendance_stats test failed: ${statsError.message}`);
        toast.error('get_attendance_stats function not working');
      } else {
        addLog(`✅ get_attendance_stats working: ${JSON.stringify(statsData)}`);
        toast.success('get_attendance_stats function working');
      }
      
      // Test get_team_time_logs
      const { data: teamData, error: teamError } = await supabase.rpc('get_team_time_logs');
      if (teamError) {
        addLog(`❌ get_team_time_logs test failed: ${teamError.message}`);
        toast.error('get_team_time_logs function not working');
      } else {
        addLog(`✅ get_team_time_logs working: ${Array.isArray(teamData) ? teamData.length : 'data'} records`);
        toast.success('get_team_time_logs function working');
      }
      
    } catch (error) {
      addLog(`❌ Test failed: ${error}`);
      toast.error('RPC function test failed');
    }
  };

  const getStatusIcon = () => {
    switch (setupStatus) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-500" />;
      default: return <Database className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (setupStatus) {
      case 'success': return <Badge className="bg-green-100 text-green-800">Functions Created</Badge>;
      case 'error': return <Badge variant="destructive">Error</Badge>;
      default: return <Badge variant="outline">Ready</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          {getStatusIcon()}
          <div>
            <h2 className="text-2xl font-bold">RPC Functions Setup</h2>
            <p className="text-muted-foreground">Create missing database functions for time tracking</p>
          </div>
        </div>
        {getStatusBadge()}
      </div>

      {/* Issue Description */}
      <Alert>
        <Code className="h-4 w-4" />
        <AlertDescription>
          <strong>Missing RPC Functions:</strong> The time tracking system needs `get_attendance_stats` and
          `get_team_time_logs` functions to work properly. If the automated creation fails, use the manual SQL script below.
        </AlertDescription>
      </Alert>

      {/* Manual SQL Script */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Manual SQL Script (If Automated Creation Fails)</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-3">
            Copy this script to Supabase Dashboard → SQL Editor if the automated creation doesn't work:
          </p>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-xs overflow-auto max-h-32">
            <pre>{`-- RPC FUNCTIONS CREATION SCRIPT
CREATE OR REPLACE FUNCTION get_attendance_stats()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object(
        'total_hours', COALESCE((SELECT SUM(total_hours) FROM public.time_logs WHERE user_id = auth.uid()), 0),
        'days_present', COALESCE((SELECT COUNT(DISTINCT DATE(clock_in)) FROM public.time_logs WHERE user_id = auth.uid()), 0)
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_team_time_logs()
RETURNS JSON AS $$
BEGIN
    RETURN COALESCE((SELECT json_agg(json_build_object('id', id, 'user_id', user_id, 'clock_in', clock_in, 'clock_out', clock_out, 'total_hours', total_hours, 'status', status)) FROM public.time_logs WHERE clock_in >= CURRENT_DATE - INTERVAL '7 days' ORDER BY clock_in DESC LIMIT 50), '[]'::json);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION get_attendance_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION get_team_time_logs() TO authenticated;`}</pre>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-500" />
              Create Functions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Create the missing RPC functions for time tracking
            </p>
            <Button 
              onClick={createRPCFunctions} 
              disabled={isCreating}
              className="w-full"
            >
              {isCreating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Create RPC Functions
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Database className="h-4 w-4 text-green-500" />
              Test Functions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Test if the RPC functions are working correctly
            </p>
            <Button 
              onClick={testRPCFunctions} 
              variant="outline"
              className="w-full"
            >
              <Database className="h-4 w-4 mr-2" />
              Test Functions
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Logs */}
      {logs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Setup Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-xs max-h-64 overflow-y-auto">
              {logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">What This Creates</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <span className="text-blue-500">1.</span>
            <span><strong>get_attendance_stats()</strong> - Returns attendance statistics as JSON</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-500">2.</span>
            <span><strong>get_team_time_logs()</strong> - Returns team time logs with user names</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-purple-500">3.</span>
            <span><strong>Permissions</strong> - Grants execute permissions to authenticated users</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-orange-500">4.</span>
            <span><strong>Testing</strong> - Verifies functions work correctly</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
