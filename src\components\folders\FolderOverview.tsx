import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Folder, 
  Users, 
  Lock, 
  Globe, 
  Search,
  Filter,
  Grid,
  List,
  Plus,
  TrendingUp,
  FileText
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CustomFolderCard } from './CustomFolderCard';
import { CustomFolderAPI } from '@/lib/custom-folder-api';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';

interface FolderOverviewProps {
  showCreateButton?: boolean;
  onCreateFolder?: () => void;
  onFolderClick?: (folderId: string) => void;
}

export const FolderOverview = ({ 
  showCreateButton = true, 
  onCreateFolder,
  onFolderClick 
}: FolderOverviewProps) => {
  const { userProfile } = useSupabaseAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Fetch folders
  const { data: folders = [], isLoading, error } = useQuery({
    queryKey: ['folder-overview', userProfile?.id],
    queryFn: async () => {
      const { data, error } = await CustomFolderAPI.getFolders();
      if (error) throw error;
      return data;
    },
    enabled: !!userProfile?.id
  });

  // Filter folders based on search and type
  const filteredFolders = folders.filter(folder => {
    const matchesSearch = !searchQuery || 
      folder.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      folder.description?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = filterType === 'all' || 
      (filterType === 'my' && folder.created_by === userProfile?.id) ||
      (filterType === 'shared' && folder.access_level !== 'private') ||
      (filterType === 'system' && folder.is_system) ||
      (filterType === folder.access_level);

    return matchesSearch && matchesType;
  });

  // Calculate statistics
  const stats = {
    total: folders.length,
    myFolders: folders.filter(f => f.created_by === userProfile?.id).length,
    sharedFolders: folders.filter(f => f.access_level !== 'private').length,
    systemFolders: folders.filter(f => f.is_system).length,
    totalDocuments: folders.reduce((sum, f) => sum + (f.document_count || 0), 0)
  };

  const getAccessIcon = (accessLevel: string) => {
    switch (accessLevel) {
      case 'private': return <Lock className="h-4 w-4" />;
      case 'department': return <Users className="h-4 w-4" />;
      case 'public': return <Globe className="h-4 w-4" />;
      default: return <Lock className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Folders</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Folder className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">My Folders</p>
                <p className="text-2xl font-bold">{stats.myFolders}</p>
              </div>
              <Lock className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Shared Folders</p>
                <p className="text-2xl font-bold">{stats.sharedFolders}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Documents</p>
                <p className="text-2xl font-bold">{stats.totalDocuments}</p>
              </div>
              <FileText className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Folder className="h-5 w-5" />
              Folder Management
            </CardTitle>
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 border rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
              {showCreateButton && onCreateFolder && (
                <Button onClick={onCreateFolder}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Folder
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Search and Filter */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search folders..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter folders" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Folders</SelectItem>
                <SelectItem value="my">My Folders</SelectItem>
                <SelectItem value="shared">Shared Folders</SelectItem>
                <SelectItem value="system">System Folders</SelectItem>
                <SelectItem value="private">Private</SelectItem>
                <SelectItem value="department">Department</SelectItem>
                <SelectItem value="public">Public</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Folders Display */}
          {error && (
            <div className="text-center py-8">
              <p className="text-destructive">Error loading folders: {error.message}</p>
            </div>
          )}

          {!error && filteredFolders.length === 0 && (
            <div className="text-center py-12">
              <Folder className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-medium mb-2">
                {searchQuery || filterType !== 'all' ? 'No folders match your criteria' : 'No folders found'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || filterType !== 'all' 
                  ? 'Try adjusting your search or filter settings'
                  : 'Create your first folder to get started'
                }
              </p>
              {showCreateButton && onCreateFolder && !searchQuery && filterType === 'all' && (
                <Button onClick={onCreateFolder}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Folder
                </Button>
              )}
            </div>
          )}

          {!error && filteredFolders.length > 0 && (
            <>
              {/* Results count */}
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>
                  Showing {filteredFolders.length} of {folders.length} folders
                </span>
                {(searchQuery || filterType !== 'all') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSearchQuery('');
                      setFilterType('all');
                    }}
                  >
                    Clear filters
                  </Button>
                )}
              </div>

              {/* Folders Grid/List */}
              <div className={
                viewMode === 'grid' 
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'
                  : 'space-y-2'
              }>
                {filteredFolders.map(folder => (
                  viewMode === 'grid' ? (
                    <CustomFolderCard
                      key={folder.id}
                      folder={folder}
                      onOpen={onFolderClick}
                    />
                  ) : (
                    <Card key={folder.id} className="hover:shadow-sm transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div 
                            className="flex items-center gap-3 flex-1 cursor-pointer"
                            onClick={() => onFolderClick?.(folder.id)}
                          >
                            <div 
                              className="p-2 rounded"
                              style={{ backgroundColor: folder.color + '20' }}
                            >
                              <Folder className="h-5 w-5" style={{ color: folder.color }} />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium">{folder.name}</h4>
                                {folder.is_system && (
                                  <Badge variant="outline" className="text-xs">System</Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground">
                                {folder.document_count || 0} documents
                                {folder.description && ` • ${folder.description}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              <span className="flex items-center gap-1">
                                {getAccessIcon(folder.access_level)}
                                {folder.access_level}
                              </span>
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
