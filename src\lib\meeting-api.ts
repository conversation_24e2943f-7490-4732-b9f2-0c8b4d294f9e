import { supabase } from '@/integrations/supabase/client';

export interface Meeting {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time?: string;
  meeting_url?: string;
  status: string;
  organizer_id: string;
  meeting_type?: string;
  location?: string;
  organizer?: {
    id: string;
    full_name: string;
    email: string;
  };
}

export interface ZoomMeeting {
  id: string;
  topic: string;
  agenda?: string;
  start_time: string;
  duration: number;
  join_url?: string;
  status: string;
  host_id: string;
  meeting_id?: string;
  passcode?: string;
  participants?: number;
  zoom_meeting_id?: string;
  host?: {
    id: string;
    full_name: string;
    email: string;
  };
}

export class MeetingAPI {
  /**
   * Fetch all meetings for a user (tries zoom_meetings first, then regular meetings)
   */
  static async getUserMeetings(userId: string): Promise<Meeting[]> {
    try {
      // First try zoom_meetings table
      const { data: zoomData, error: zoomError } = await supabase
        .from('zoom_meetings')
        .select(`
          id,
          topic as title,
          agenda as description,
          start_time,
          duration,
          join_url as meeting_url,
          status,
          host_id as organizer_id,
          host:profiles!host_id(id, full_name, email)
        `)
        .eq('host_id', userId)
        .order('start_time', { ascending: true });

      if (!zoomError && zoomData) {
        // Transform zoom meetings to match expected format
        const transformedMeetings: Meeting[] = zoomData.map(meeting => ({
          id: meeting.id,
          title: meeting.title,
          description: meeting.description,
          start_time: meeting.start_time,
          end_time: new Date(new Date(meeting.start_time).getTime() + (meeting.duration * 60000)).toISOString(),
          meeting_url: meeting.meeting_url,
          status: meeting.status,
          organizer_id: meeting.organizer_id,
          meeting_type: 'zoom',
          location: 'Online',
          organizer: meeting.host
        }));
        return transformedMeetings;
      }

      // Fallback to regular meetings table
      const { data: meetingsData, error: meetingsError } = await supabase
        .from('meetings')
        .select(`
          id,
          title,
          description,
          start_time,
          end_time,
          meeting_url,
          status,
          organizer_id,
          meeting_type,
          location,
          organizer:profiles!organizer_id(id, full_name, email)
        `)
        .eq('organizer_id', userId)
        .order('start_time', { ascending: true });

      if (meetingsError) {
        console.error('Error fetching meetings:', meetingsError);
        return [];
      }

      return meetingsData || [];
    } catch (error) {
      console.error('Meeting API error:', error);
      return [];
    }
  }

  /**
   * Fetch all meetings (for managers/admins)
   */
  static async getAllMeetings(): Promise<Meeting[]> {
    try {
      // First try zoom_meetings table
      const { data: zoomData, error: zoomError } = await supabase
        .from('zoom_meetings')
        .select(`
          id,
          topic as title,
          agenda as description,
          start_time,
          duration,
          join_url as meeting_url,
          status,
          host_id as organizer_id,
          host:profiles!host_id(id, full_name, email)
        `)
        .order('start_time', { ascending: true });

      if (!zoomError && zoomData) {
        // Transform zoom meetings to match expected format
        const transformedMeetings: Meeting[] = zoomData.map(meeting => ({
          id: meeting.id,
          title: meeting.title,
          description: meeting.description,
          start_time: meeting.start_time,
          end_time: new Date(new Date(meeting.start_time).getTime() + (meeting.duration * 60000)).toISOString(),
          meeting_url: meeting.meeting_url,
          status: meeting.status,
          organizer_id: meeting.organizer_id,
          meeting_type: 'zoom',
          location: 'Online',
          organizer: meeting.host
        }));
        return transformedMeetings;
      }

      // Fallback to regular meetings table
      const { data: meetingsData, error: meetingsError } = await supabase
        .from('meetings')
        .select(`
          id,
          title,
          description,
          start_time,
          end_time,
          meeting_url,
          status,
          organizer_id,
          meeting_type,
          location,
          organizer:profiles!organizer_id(id, full_name, email)
        `)
        .order('start_time', { ascending: true });

      if (meetingsError) {
        console.error('Error fetching all meetings:', meetingsError);
        return [];
      }

      return meetingsData || [];
    } catch (error) {
      console.error('Meeting API error:', error);
      return [];
    }
  }

  /**
   * Create a new meeting
   */
  static async createMeeting(meetingData: Partial<Meeting>): Promise<Meeting | null> {
    try {
      const { data, error } = await supabase
        .from('meetings')
        .insert([{
          title: meetingData.title,
          description: meetingData.description,
          start_time: meetingData.start_time,
          end_time: meetingData.end_time,
          meeting_url: meetingData.meeting_url,
          status: meetingData.status || 'scheduled',
          organizer_id: meetingData.organizer_id,
          meeting_type: meetingData.meeting_type || 'regular',
          location: meetingData.location
        }])
        .select(`
          id,
          title,
          description,
          start_time,
          end_time,
          meeting_url,
          status,
          organizer_id,
          meeting_type,
          location,
          organizer:profiles!organizer_id(id, full_name, email)
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating meeting:', error);
      return null;
    }
  }

  /**
   * Create a new zoom meeting
   */
  static async createZoomMeeting(meetingData: Partial<ZoomMeeting>): Promise<ZoomMeeting | null> {
    try {
      const { data, error } = await supabase
        .from('zoom_meetings')
        .insert([{
          topic: meetingData.topic,
          agenda: meetingData.agenda,
          start_time: meetingData.start_time,
          duration: meetingData.duration || 60,
          join_url: meetingData.join_url,
          status: meetingData.status || 'scheduled',
          host_id: meetingData.host_id,
          meeting_id: meetingData.meeting_id,
          passcode: meetingData.passcode,
          participants: meetingData.participants || 0,
          zoom_meeting_id: meetingData.zoom_meeting_id
        }])
        .select(`
          *,
          host:profiles!host_id(id, full_name, email)
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating zoom meeting:', error);
      return null;
    }
  }

  /**
   * Update a meeting
   */
  static async updateMeeting(meetingId: string, updates: Partial<Meeting>): Promise<Meeting | null> {
    try {
      const { data, error } = await supabase
        .from('meetings')
        .update(updates)
        .eq('id', meetingId)
        .select(`
          id,
          title,
          description,
          start_time,
          end_time,
          meeting_url,
          status,
          organizer_id,
          meeting_type,
          location,
          organizer:profiles!organizer_id(id, full_name, email)
        `)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating meeting:', error);
      return null;
    }
  }

  /**
   * Delete a meeting
   */
  static async deleteMeeting(meetingId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('meetings')
        .delete()
        .eq('id', meetingId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting meeting:', error);
      return false;
    }
  }

  /**
   * Delete a zoom meeting
   */
  static async deleteZoomMeeting(meetingId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('zoom_meetings')
        .delete()
        .eq('id', meetingId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting zoom meeting:', error);
      return false;
    }
  }
}
