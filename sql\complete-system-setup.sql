-- ============================================================================
-- COMPLETE SYSTEM SETUP SQL
-- This script creates all necessary tables, relationships, and sample data
-- for the AI-CTNIGERIA Workboard System with Battery Management
-- ============================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- STEP 1: CORE SYSTEM TABLES
-- ============================================================================

-- Create departments table
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID,
    budget DECIMAL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    email TEXT UNIQUE,
    role TEXT DEFAULT 'staff' CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin')),
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    avatar_url TEXT,
    phone TEXT,
    position TEXT,
    account_type TEXT DEFAULT 'staff',
    last_login TIMESTAMP WITH TIME ZONE,
    preferences JSONB DEFAULT '{}',
    timezone TEXT DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 2: PROJECT MANAGEMENT TABLES
-- ============================================================================

-- Create projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    client_name TEXT,
    budget DECIMAL DEFAULT 0,
    budget_spent DECIMAL DEFAULT 0,
    location TEXT,
    start_date DATE,
    end_date DATE,
    actual_end_date DATE,
    status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    manager_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create tasks table
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    assigned_to_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    created_by_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    due_date TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    estimated_hours DECIMAL,
    actual_hours DECIMAL,
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 3: COMMUNICATION TABLES
-- ============================================================================

-- Create memos table
CREATE TABLE IF NOT EXISTS public.memos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    subject TEXT,
    purpose TEXT,
    memo_type TEXT DEFAULT 'general' CHECK (memo_type IN ('general', 'urgent', 'policy', 'announcement', 'meeting_notes', 'directive', 'reminder')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived', 'pending', 'approved', 'rejected')),
    visibility TEXT DEFAULT 'department' CHECK (visibility IN ('public', 'department', 'role_specific', 'private')),
    target_audience JSONB DEFAULT '{}',
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    from_user UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    to_recipient TEXT,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    department TEXT,
    effective_date DATE,
    expiry_date DATE,
    memo_date DATE,
    is_pinned BOOLEAN DEFAULT FALSE,
    read_count INTEGER DEFAULT 0,
    tags TEXT[],
    account_details TEXT,
    total_amount DECIMAL DEFAULT 0,
    payment_items JSONB,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 4: FINANCIAL MANAGEMENT TABLES
-- ============================================================================

-- Create expense_reports table
CREATE TABLE IF NOT EXISTS public.expense_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    amount DECIMAL NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT,
    expense_date DATE NOT NULL,
    receipt_url TEXT,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'reimbursed')),
    approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create budgets table
CREATE TABLE IF NOT EXISTS public.budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    allocated_amount DECIMAL NOT NULL,
    spent_amount DECIMAL DEFAULT 0,
    remaining_amount DECIMAL GENERATED ALWAYS AS (allocated_amount - spent_amount) STORED,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed', 'cancelled')),
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    approval_date DATE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 5: BATTERY MANAGEMENT SYSTEM TABLES
-- ============================================================================

-- Battery Types Table
CREATE TABLE IF NOT EXISTS public.battery_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    voltage DECIMAL(5,2) NOT NULL,
    capacity_ah DECIMAL(8,2) NOT NULL,
    chemistry VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    specifications JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id)
);

-- Battery Locations Table
CREATE TABLE IF NOT EXISTS public.battery_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    location_type VARCHAR(50) NOT NULL,
    address TEXT,
    coordinates POINT,
    parent_location_id UUID REFERENCES public.battery_locations(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id)
);

-- Batteries Table (Main inventory)
CREATE TABLE IF NOT EXISTS public.batteries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    serial_number VARCHAR(100) NOT NULL UNIQUE,
    battery_type_id UUID NOT NULL REFERENCES public.battery_types(id),
    current_location_id UUID REFERENCES public.battery_locations(id),
    status VARCHAR(50) NOT NULL DEFAULT 'new',
    condition VARCHAR(50) NOT NULL DEFAULT 'excellent',
    purchase_date DATE,
    installation_date DATE,
    warranty_expiry_date DATE,
    last_maintenance_date DATE,
    next_maintenance_date DATE,
    purchase_cost DECIMAL(10,2),
    supplier VARCHAR(100),
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id),
    profile_id UUID REFERENCES public.profiles(id)
);

-- Battery Readings Table (Performance tracking)
CREATE TABLE IF NOT EXISTS public.battery_readings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    battery_id UUID NOT NULL REFERENCES public.batteries(id) ON DELETE CASCADE,
    reading_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    voltage DECIMAL(5,2),
    current_amperage DECIMAL(8,2),
    temperature DECIMAL(5,2),
    state_of_charge DECIMAL(5,2),
    capacity_remaining DECIMAL(8,2),
    internal_resistance DECIMAL(8,4),
    reading_type VARCHAR(50) NOT NULL DEFAULT 'manual',
    technician_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    generated_by VARCHAR(100),
    profile_id UUID REFERENCES public.profiles(id)
);

-- Battery Maintenance Records
CREATE TABLE IF NOT EXISTS public.battery_maintenance (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    battery_id UUID NOT NULL REFERENCES public.batteries(id) ON DELETE CASCADE,
    maintenance_type VARCHAR(100) NOT NULL,
    maintenance_date DATE NOT NULL,
    technician_id UUID REFERENCES public.profiles(id),
    description TEXT NOT NULL,
    parts_used TEXT,
    cost DECIMAL(10,2),
    next_maintenance_date DATE,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id),
    profile_id UUID REFERENCES public.profiles(id)
);

-- Battery Transfers/Movements
CREATE TABLE IF NOT EXISTS public.battery_transfers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    battery_id UUID NOT NULL REFERENCES public.batteries(id) ON DELETE CASCADE,
    from_location_id UUID REFERENCES public.battery_locations(id),
    to_location_id UUID NOT NULL REFERENCES public.battery_locations(id),
    transfer_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    transferred_by UUID REFERENCES public.profiles(id),
    received_by UUID REFERENCES public.profiles(id),
    reason VARCHAR(200),
    notes TEXT,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    profile_id UUID REFERENCES public.profiles(id)
);

-- ============================================================================
-- STEP 6: SYSTEM ACTIVITY AND NOTIFICATIONS
-- ============================================================================

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    is_read BOOLEAN DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create system_activities table
CREATE TABLE IF NOT EXISTS public.system_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    description TEXT NOT NULL,
    entity_type TEXT,
    entity_id UUID,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 7: ADD FOREIGN KEY CONSTRAINTS
-- ============================================================================

-- Add foreign key to departments.manager_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'departments_manager_id_fkey'
    ) THEN
        ALTER TABLE public.departments
        ADD CONSTRAINT departments_manager_id_fkey
        FOREIGN KEY (manager_id) REFERENCES public.profiles(id) ON DELETE SET NULL;
    END IF;
END $$;

-- ============================================================================
-- STEP 8: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Core table indexes
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_department_id ON public.profiles(department_id);
CREATE INDEX IF NOT EXISTS idx_profiles_status ON public.profiles(status);

-- Project management indexes
CREATE INDEX IF NOT EXISTS idx_projects_manager_id ON public.projects(manager_id);
CREATE INDEX IF NOT EXISTS idx_projects_department_id ON public.projects(department_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to_id ON public.tasks(assigned_to_id);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON public.tasks(status);

-- Memos indexes
CREATE INDEX IF NOT EXISTS idx_memos_created_by ON public.memos(created_by);
CREATE INDEX IF NOT EXISTS idx_memos_department_id ON public.memos(department_id);
CREATE INDEX IF NOT EXISTS idx_memos_status ON public.memos(status);
CREATE INDEX IF NOT EXISTS idx_memos_created_at ON public.memos(created_at);

-- Battery management indexes
CREATE INDEX IF NOT EXISTS idx_batteries_serial_number ON public.batteries(serial_number);
CREATE INDEX IF NOT EXISTS idx_batteries_type ON public.batteries(battery_type_id);
CREATE INDEX IF NOT EXISTS idx_batteries_location ON public.batteries(current_location_id);
CREATE INDEX IF NOT EXISTS idx_batteries_status ON public.batteries(status);
CREATE INDEX IF NOT EXISTS idx_batteries_created_by ON public.batteries(created_by);
CREATE INDEX IF NOT EXISTS idx_batteries_profile_id ON public.batteries(profile_id);
CREATE INDEX IF NOT EXISTS idx_battery_readings_battery_id ON public.battery_readings(battery_id);
CREATE INDEX IF NOT EXISTS idx_battery_readings_date ON public.battery_readings(reading_date);

-- System activity indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_is_read ON public.notifications(is_read);
CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON public.system_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_system_activities_created_at ON public.system_activities(created_at);

-- ============================================================================
-- STEP 9: INSERT SAMPLE DATA
-- ============================================================================

-- Insert sample departments
INSERT INTO public.departments (id, name, description) VALUES
    ('********-1111-1111-1111-********1111', 'IT Department', 'Information Technology and Systems'),
    ('********-2222-2222-2222-********2222', 'HR Department', 'Human Resources and Administration'),
    ('********-3333-3333-3333-************', 'Finance Department', 'Finance and Accounting'),
    ('********-4444-4444-4444-************', 'Operations', 'Field Operations and Maintenance'),
    ('********-5555-5555-5555-************', 'Engineering', 'Technical Engineering and Design'),
    ('********-6666-6666-6666-************', 'Construction', 'Construction and Infrastructure')
ON CONFLICT (id) DO NOTHING;

-- Insert sample battery types
INSERT INTO public.battery_types (name, description, voltage, capacity_ah, chemistry, manufacturer, model) VALUES
    ('Deep Cycle 12V 100Ah', 'Standard deep cycle battery for telecom sites', 12.0, 100.0, 'Lead-acid', 'Trojan', 'T-105'),
    ('Lithium 48V 200Ah', 'High capacity lithium battery bank', 48.0, 200.0, 'Lithium-ion', 'Tesla', 'Powerwall'),
    ('AGM 12V 75Ah', 'Absorbed Glass Mat battery for UPS systems', 12.0, 75.0, 'AGM', 'Optima', 'D75'),
    ('Gel 6V 225Ah', 'Gel cell battery for solar applications', 6.0, 225.0, 'Gel', 'Rolls', 'S6-225AGM'),
    ('LiFePO4 24V 100Ah', 'Lithium Iron Phosphate for marine use', 24.0, 100.0, 'LiFePO4', 'Battle Born', 'BB10024')
ON CONFLICT (name) DO NOTHING;

-- Insert sample battery locations
INSERT INTO public.battery_locations (name, description, location_type, address) VALUES
    ('Main Warehouse', 'Central battery storage facility', 'Warehouse', '123 Industrial Ave, Lagos'),
    ('Site Alpha', 'Telecom tower site Alpha', 'Site', 'Alpha Tower Location, Abuja'),
    ('Site Beta', 'Telecom tower site Beta', 'Site', 'Beta Tower Location, Port Harcourt'),
    ('Mobile Unit 1', 'Field service vehicle 1', 'Vehicle', 'Mobile deployment unit'),
    ('Workshop', 'Battery maintenance and repair facility', 'Workshop', '456 Service Road, Lagos'),
    ('Emergency Storage', 'Emergency backup battery storage', 'Storage', 'Emergency facility, Abuja')
ON CONFLICT (name) DO NOTHING;

-- ============================================================================
-- STEP 10: CREATE TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- ============================================================================

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for all tables with updated_at columns
CREATE TRIGGER update_departments_updated_at BEFORE UPDATE ON public.departments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON public.tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_memos_updated_at BEFORE UPDATE ON public.memos FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_batteries_updated_at BEFORE UPDATE ON public.batteries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_battery_types_updated_at BEFORE UPDATE ON public.battery_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_battery_locations_updated_at BEFORE UPDATE ON public.battery_locations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_battery_maintenance_updated_at BEFORE UPDATE ON public.battery_maintenance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- STEP 11: ENABLE ROW LEVEL SECURITY (RLS)
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.batteries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battery_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battery_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battery_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battery_maintenance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battery_transfers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_activities ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 12: CREATE RLS POLICIES
-- ============================================================================

-- Profiles policies
CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON public.profiles FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

-- Departments policies
CREATE POLICY "Users can view departments" ON public.departments FOR SELECT USING (true);
CREATE POLICY "Admins can manage departments" ON public.departments FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

-- Projects policies
CREATE POLICY "Users can view department projects" ON public.projects FOR SELECT USING (
    department_id IN (SELECT department_id FROM public.profiles WHERE id = auth.uid())
    OR manager_id = auth.uid()
    OR EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

-- Tasks policies
CREATE POLICY "Users can view assigned tasks" ON public.tasks FOR SELECT USING (
    assigned_to_id = auth.uid()
    OR created_by_id = auth.uid()
    OR EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

-- Memos policies
CREATE POLICY "Users can view published memos" ON public.memos FOR SELECT USING (
    status = 'published' AND (
        visibility = 'public' OR
        created_by = auth.uid() OR
        (visibility = 'department' AND department_id IN (
            SELECT department_id FROM public.profiles WHERE id = auth.uid()
        )) OR
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
    )
);

-- Battery management policies
CREATE POLICY "Users can view batteries" ON public.batteries FOR SELECT USING (
    profile_id = auth.uid()
    OR created_by = auth.uid()
    OR EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Users can create batteries" ON public.batteries FOR INSERT WITH CHECK (
    created_by = auth.uid()
);

CREATE POLICY "Users can update own batteries" ON public.batteries FOR UPDATE USING (
    created_by = auth.uid()
    OR EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

-- Notifications policies
CREATE POLICY "Users can view own notifications" ON public.notifications FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can update own notifications" ON public.notifications FOR UPDATE USING (user_id = auth.uid());

-- ============================================================================
-- SETUP COMPLETE
-- ============================================================================

-- Create a function to verify the setup
CREATE OR REPLACE FUNCTION verify_system_setup()
RETURNS TABLE(
    table_name TEXT,
    row_count BIGINT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        'departments'::TEXT,
        (SELECT COUNT(*) FROM public.departments),
        CASE WHEN (SELECT COUNT(*) FROM public.departments) > 0 THEN '✅ Ready' ELSE '⚠️ Empty' END
    UNION ALL
    SELECT
        'battery_types'::TEXT,
        (SELECT COUNT(*) FROM public.battery_types),
        CASE WHEN (SELECT COUNT(*) FROM public.battery_types) > 0 THEN '✅ Ready' ELSE '⚠️ Empty' END
    UNION ALL
    SELECT
        'battery_locations'::TEXT,
        (SELECT COUNT(*) FROM public.battery_locations),
        CASE WHEN (SELECT COUNT(*) FROM public.battery_locations) > 0 THEN '✅ Ready' ELSE '⚠️ Empty' END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Display setup completion message
DO $$
BEGIN
    RAISE NOTICE '🎉 SYSTEM SETUP COMPLETE! 🎉';
    RAISE NOTICE '';
    RAISE NOTICE '✅ All tables created with audit columns';
    RAISE NOTICE '✅ Foreign key relationships established';
    RAISE NOTICE '✅ Indexes created for performance';
    RAISE NOTICE '✅ Sample data inserted';
    RAISE NOTICE '✅ RLS policies configured';
    RAISE NOTICE '✅ Triggers set up for automatic timestamps';
    RAISE NOTICE '';
    RAISE NOTICE '🔋 Battery Management System Ready';
    RAISE NOTICE '📝 Memo System Ready';
    RAISE NOTICE '📊 Project Management Ready';
    RAISE NOTICE '💰 Financial Management Ready';
    RAISE NOTICE '';
    RAISE NOTICE 'Run: SELECT * FROM verify_system_setup(); to check status';
END $$;
