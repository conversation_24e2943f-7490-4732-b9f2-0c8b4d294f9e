import React, { useState, useEffect } from 'react';
import { Globe, Volume2, Check, X } from 'lucide-react';
import { LanguageSelectionService, LanguageOption } from '@/services/language-selection-service';
import { VoiceResponseService } from '@/services/voice-response-service';

interface LanguageSelectorProps {
  onLanguageChange?: (language: string) => void;
  showModal?: boolean;
  onClose?: () => void;
  compact?: boolean;
  showNigerianOnly?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  onLanguageChange,
  showModal = false,
  onClose,
  compact = false,
  showNigerianOnly = false
}) => {
  const [isOpen, setIsOpen] = useState(showModal);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en-GB');
  const [availableLanguages, setAvailableLanguages] = useState<LanguageOption[]>([]);
  const [isTestingVoice, setIsTestingVoice] = useState<string | null>(null);
  const [currentLanguageOption, setCurrentLanguageOption] = useState<LanguageOption | null>(null);

  useEffect(() => {
    initializeLanguages();
  }, []);

  useEffect(() => {
    if (showModal !== undefined) {
      setIsOpen(showModal);
    }
  }, [showModal]);

  const initializeLanguages = async () => {
    try {
      await LanguageSelectionService.initialize();
      
      const languages = showNigerianOnly 
        ? LanguageSelectionService.getNigerianLanguages()
        : LanguageSelectionService.getAvailableLanguages();
      
      setAvailableLanguages(languages);
      
      const current = LanguageSelectionService.getCurrentLanguage();
      setSelectedLanguage(current);
      
      const currentOption = LanguageSelectionService.getCurrentLanguageOption();
      setCurrentLanguageOption(currentOption);
    } catch (error) {
      console.error('Error initializing languages:', error);
    }
  };

  const handleLanguageSelect = async (languageCode: string) => {
    try {
      setSelectedLanguage(languageCode);
      
      const success = await LanguageSelectionService.setLanguage(languageCode);
      if (success) {
        const newOption = availableLanguages.find(lang => lang.code === languageCode);
        setCurrentLanguageOption(newOption || null);
        
        onLanguageChange?.(languageCode);
        
        // Speak greeting in new language
        if (newOption) {
          await VoiceResponseService.speak({
            text: newOption.greeting,
            voice: languageCode,
            interrupt: true
          });
        }
      }
    } catch (error) {
      console.error('Error selecting language:', error);
    }
  };

  const testLanguageVoice = async (languageCode: string) => {
    try {
      setIsTestingVoice(languageCode);
      await LanguageSelectionService.testLanguageVoice(languageCode);
    } catch (error) {
      console.error('Error testing voice:', error);
    } finally {
      setIsTestingVoice(null);
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    onClose?.();
  };

  if (compact) {
    return (
      <div className="language-selector-compact">
        <button
          onClick={() => setIsOpen(true)}
          className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          title="Select Language"
        >
          <Globe size={16} />
          <span className="text-sm">
            {currentLanguageOption?.flag} {currentLanguageOption?.name || 'English (UK)'}
          </span>
        </button>

        {isOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Globe size={20} />
                  Select Language
                </h3>
                <button
                  onClick={handleClose}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  <X size={20} />
                </button>
              </div>

              <div className="space-y-2">
                {availableLanguages.map((language) => (
                  <button
                    key={language.code}
                    onClick={() => handleLanguageSelect(language.code)}
                    className={`w-full flex items-center justify-between p-3 rounded-lg border transition-all ${
                      selectedLanguage === language.code
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{language.flag}</span>
                      <div className="text-left">
                        <div className="font-medium">{language.name}</div>
                        <div className="text-sm text-gray-600">{language.nativeName}</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {language.voiceSupported && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            testLanguageVoice(language.code);
                          }}
                          disabled={isTestingVoice === language.code}
                          className="p-1 hover:bg-gray-200 rounded transition-colors"
                          title="Test voice"
                        >
                          <Volume2 
                            size={16} 
                            className={isTestingVoice === language.code ? 'animate-pulse text-blue-500' : 'text-gray-500'}
                          />
                        </button>
                      )}
                      
                      {selectedLanguage === language.code && (
                        <Check size={16} className="text-blue-500" />
                      )}
                    </div>
                  </button>
                ))}
              </div>

              <div className="mt-6 flex justify-end gap-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleClose}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Done
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="language-selector">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
        <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white p-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-white/20 rounded-lg">
              <Globe size={32} />
            </div>
            <div>
              <h2 className="text-2xl font-bold">
                {LanguageSelectionService.getLocalizedText('select_language')}
              </h2>
              <p className="text-white/90 mt-1">
                Choose your preferred language for voice interaction
              </p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Nigerian Languages Section */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              🇳🇬 Nigerian Languages
            </h3>
            <div className="grid gap-3">
              {availableLanguages
                .filter(lang => lang.isNigerian || lang.code === 'en-GB')
                .map((language) => (
                <div
                  key={language.code}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedLanguage === language.code
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-green-300 hover:bg-green-25'
                  }`}
                  onClick={() => handleLanguageSelect(language.code)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <span className="text-3xl">{language.flag}</span>
                      <div>
                        <div className="font-semibold text-lg">{language.name}</div>
                        <div className="text-gray-600">{language.nativeName}</div>
                        <div className="text-sm text-gray-500 mt-1">
                          {language.sampleText}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      {language.voiceSupported && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            testLanguageVoice(language.code);
                          }}
                          disabled={isTestingVoice === language.code}
                          className="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
                          title="Test voice sample"
                        >
                          <Volume2 
                            size={16} 
                            className={isTestingVoice === language.code ? 'animate-pulse' : ''}
                          />
                          <span className="text-sm">
                            {isTestingVoice === language.code ? 'Playing...' : 'Test Voice'}
                          </span>
                        </button>
                      )}
                      
                      {selectedLanguage === language.code && (
                        <div className="flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg">
                          <Check size={16} />
                          <span className="text-sm">Selected</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Other Languages Section */}
          {!showNigerianOnly && (
            <div>
              <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                🌍 Other Languages
              </h3>
              <div className="grid gap-2">
                {availableLanguages
                  .filter(lang => !lang.isNigerian && lang.code !== 'en-GB')
                  .map((language) => (
                  <div
                    key={language.code}
                    className={`p-3 rounded-lg border cursor-pointer transition-all ${
                      selectedLanguage === language.code
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-blue-300 hover:bg-blue-25'
                    }`}
                    onClick={() => handleLanguageSelect(language.code)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-xl">{language.flag}</span>
                        <div>
                          <div className="font-medium">{language.name}</div>
                          <div className="text-sm text-gray-600">{language.nativeName}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {language.voiceSupported && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              testLanguageVoice(language.code);
                            }}
                            disabled={isTestingVoice === language.code}
                            className="p-2 hover:bg-gray-200 rounded transition-colors"
                            title="Test voice"
                          >
                            <Volume2 
                              size={16} 
                              className={isTestingVoice === language.code ? 'animate-pulse text-blue-500' : 'text-gray-500'}
                            />
                          </button>
                        )}
                        
                        {selectedLanguage === language.code && (
                          <Check size={16} className="text-blue-500" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Language Features */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-semibold mb-2">Language Features:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>Voice Recognition</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span>Text-to-Speech</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span>AI Conversation</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>System Navigation</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelector;
