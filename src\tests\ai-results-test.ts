/**
 * Test script for AI Results functionality
 * This script tests the CRUD operations for AI Results
 */

import { supabase } from '@/integrations/supabase/client';

export interface TestResult {
  test: string;
  passed: boolean;
  error?: string;
  data?: any;
}

export class AIResultsTest {
  private results: TestResult[] = [];

  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting AI Results Tests...');
    
    await this.testDatabaseConnection();
    await this.testCreateAIResult();
    await this.testReadAIResults();
    await this.testDeleteAIResult();
    
    console.log('✅ AI Results Tests Complete');
    return this.results;
  }

  private async testDatabaseConnection(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('ai_results')
        .select('count(*)')
        .limit(1);
      
      if (error) throw error;
      
      this.results.push({
        test: 'Database Connection',
        passed: true,
        data: `Connected successfully, table exists`
      });
    } catch (error: any) {
      this.results.push({
        test: 'Database Connection',
        passed: false,
        error: error.message
      });
    }
  }

  private async testCreateAIResult(): Promise<void> {
    try {
      const testData = {
        query_text: 'Test query for AI Results functionality',
        result_data: { 
          summary: 'This is a test result',
          confidence: 0.95,
          test_timestamp: new Date().toISOString()
        },
        model_used: 'test-model',
        created_by: null
      };

      const { data, error } = await supabase
        .from('ai_results')
        .insert([testData])
        .select()
        .single();
      
      if (error) throw error;
      
      this.results.push({
        test: 'Create AI Result',
        passed: true,
        data: `Created result with ID: ${data.id}`
      });
      
      // Store the ID for cleanup
      (this as any).testResultId = data.id;
    } catch (error: any) {
      this.results.push({
        test: 'Create AI Result',
        passed: false,
        error: error.message
      });
    }
  }

  private async testReadAIResults(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('ai_results')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (error) throw error;
      
      this.results.push({
        test: 'Read AI Results',
        passed: true,
        data: `Retrieved ${data.length} results`
      });
    } catch (error: any) {
      this.results.push({
        test: 'Read AI Results',
        passed: false,
        error: error.message
      });
    }
  }

  private async testDeleteAIResult(): Promise<void> {
    try {
      const testResultId = (this as any).testResultId;
      if (!testResultId) {
        throw new Error('No test result ID available for deletion');
      }

      const { error } = await supabase
        .from('ai_results')
        .delete()
        .eq('id', testResultId);
      
      if (error) throw error;
      
      this.results.push({
        test: 'Delete AI Result',
        passed: true,
        data: `Deleted result with ID: ${testResultId}`
      });
    } catch (error: any) {
      this.results.push({
        test: 'Delete AI Result',
        passed: false,
        error: error.message
      });
    }
  }

  printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    this.results.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.test}`);
      
      if (result.passed && result.data) {
        console.log(`   📝 ${result.data}`);
      }
      
      if (!result.passed && result.error) {
        console.log(`   ❗ Error: ${result.error}`);
      }
    });
    
    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;
    
    console.log(`\n🎯 Results: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! AI Results functionality is working correctly.');
    } else {
      console.log('⚠️  Some tests failed. Please check the errors above.');
    }
  }
}

// Export a function to run tests from console
export const runAIResultsTests = async (): Promise<void> => {
  const tester = new AIResultsTest();
  await tester.runAllTests();
  tester.printResults();
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).runAIResultsTests = runAIResultsTests;
}
