/**
 * Enhanced AI Page
 * Showcases the new LangChain and real-time collaboration features
 */

import { EnhancedAIAssistant } from '@/components/ai/EnhancedAIAssistant';
import { EnhancedFeaturesStatus } from '@/components/integration/LangChainRealtimeProvider';
import { CollaborativeEditor } from '@/components/realtime/CollaborativeEditor';
import { PresenceIndicator, TypingIndicator } from '@/components/realtime/PresenceIndicator';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/hooks/use-toast';
import { useCollaborativeSession, usePresence } from '@/hooks/useRealtime';
import { supabase } from '@/integrations/supabase/client';
import { Brain, FileText, MessageSquare, Users, Zap } from 'lucide-react';
import React, { useState } from 'react';

export default function EnhancedAIPage() {
  const { user } = useSupabaseAuth();
  const { updateCurrentPage } = usePresence();
  const [activeTab, setActiveTab] = useState('ai');
  const [documentId, setDocumentId] = useState<string | null>(null);
  const { participants } = useCollaborativeSession(
    'enhanced-ai-demo',
    'document',
    'demo-document'
  );

  // Update current page for presence
  React.useEffect(() => {
    updateCurrentPage('/ai/enhanced');
  }, [updateCurrentPage]);

  // Create or get demo document
  React.useEffect(() => {
    const createOrGetDocument = async () => {
      try {
        // Check if demo document exists
        const { data: existingDocs, error: queryError } = await supabase
          .from('documents')
          .select('id')
          .eq('title', 'Collaborative Demo Document')
          .limit(1);

        if (queryError) throw queryError;

        if (existingDocs && existingDocs.length > 0) {
          setDocumentId(existingDocs[0].id);
        } else {
          // Create demo document
          const { data: newDoc, error: createError } = await supabase
            .from('documents')
            .insert({
              title: 'Collaborative Demo Document',
              content: `# Collaborative Demo Document

This is a real-time collaborative document that demonstrates the new features of the CTNL AI Workboard.

## Features

- Real-time collaboration with multiple users
- Cursor tracking and presence indicators
- Comments and annotations
- Document versioning

Try editing this document and see how changes are synchronized in real-time!

## LangChain Integration

The system now includes:
- Advanced RAG (Retrieval Augmented Generation)
- Agent-based AI with tool usage
- Memory and context awareness
- Document processing and embedding

## How to Use

1. Edit this document to see real-time collaboration
2. Add comments by selecting text
3. Try the AI assistant with complex queries
4. Explore the other tabs to see more features`,
              user_id: user?.id,
              is_public: true,
              version: 1,
            })
            .select()
            .single();

          if (createError) throw createError;
          setDocumentId(newDoc.id);
        }
      } catch (error) {
        console.error('Error creating/getting document:', error);
        toast({
          title: 'Error',
          description: 'Failed to load collaborative document',
          variant: 'destructive',
        });
      }
    };

    if (user) {
      createOrGetDocument();
    }
  }, [user]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Enhanced AI & Collaboration</h1>
          <p className="text-muted-foreground">
            Explore the new LangChain and real-time collaboration features
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <EnhancedFeaturesStatus />
          <PresenceIndicator />
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Feature Showcase</CardTitle>
            <Badge variant="outline" className="px-3">
              {participants.length} active users
            </Badge>
          </div>
          <CardDescription>
            Explore the new features powered by LangChain and real-time collaboration
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-0">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="w-full justify-start px-6 pt-2">
              <TabsTrigger value="ai" className="flex items-center">
                <Brain className="h-4 w-4 mr-2" />
                AI Assistant
              </TabsTrigger>
              <TabsTrigger value="collaboration" className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Collaboration
              </TabsTrigger>
              <TabsTrigger value="document" className="flex items-center">
                <FileText className="h-4 w-4 mr-2" />
                Document
              </TabsTrigger>
              <TabsTrigger value="features" className="flex items-center">
                <Zap className="h-4 w-4 mr-2" />
                Features
              </TabsTrigger>
            </TabsList>
            
            <Separator />
            
            <TabsContent value="ai" className="p-6 pt-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <EnhancedAIAssistant 
                    showSources={true}
                    showActions={true}
                    enableRAG={true}
                    enableAgents={true}
                  />
                </div>
                
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Brain className="h-5 w-5 mr-2" />
                        Enhanced AI Features
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-medium">LangChain Integration</h3>
                        <p className="text-sm text-muted-foreground">
                          Powered by LangChain with memory, RAG, and agents
                        </p>
                      </div>
                      
                      <div>
                        <h3 className="font-medium">Try Asking:</h3>
                        <ul className="text-sm space-y-2 mt-2">
                          <li className="flex items-center">
                            <MessageSquare className="h-3 w-3 mr-2 text-primary" />
                            "What documents are in the knowledge base?"
                          </li>
                          <li className="flex items-center">
                            <MessageSquare className="h-3 w-3 mr-2 text-primary" />
                            "Create a task for the marketing team"
                          </li>
                          <li className="flex items-center">
                            <MessageSquare className="h-3 w-3 mr-2 text-primary" />
                            "Who's online right now?"
                          </li>
                          <li className="flex items-center">
                            <MessageSquare className="h-3 w-3 mr-2 text-primary" />
                            "Summarize the collaborative document"
                          </li>
                        </ul>
                      </div>
                      
                      <Button variant="outline" className="w-full" onClick={() => setActiveTab('features')}>
                        View All Features
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="collaboration" className="p-6 pt-4">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  {documentId && (
                    <CollaborativeEditor 
                      documentId={documentId}
                      showComments={true}
                      showCursors={true}
                    />
                  )}
                </div>
                
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Users className="h-5 w-5 mr-2" />
                        Real-time Collaboration
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <h3 className="font-medium">Active Users</h3>
                        <PresenceIndicator maxVisible={5} showStatus={true} showCurrentPage={true} className="mt-2" />
                      </div>
                      
                      <div>
                        <h3 className="font-medium">Typing Indicators</h3>
                        <TypingIndicator sessionId="enhanced-ai-demo" className="mt-2" />
                      </div>
                      
                      <div>
                        <h3 className="font-medium">Collaboration Features:</h3>
                        <ul className="text-sm space-y-2 mt-2">
                          <li className="flex items-center">
                            <Users className="h-3 w-3 mr-2 text-primary" />
                            Real-time presence and status
                          </li>
                          <li className="flex items-center">
                            <Users className="h-3 w-3 mr-2 text-primary" />
                            Cursor tracking and selection
                          </li>
                          <li className="flex items-center">
                            <Users className="h-3 w-3 mr-2 text-primary" />
                            Comments and annotations
                          </li>
                          <li className="flex items-center">
                            <Users className="h-3 w-3 mr-2 text-primary" />
                            Document locking and versioning
                          </li>
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="document" className="p-6 pt-4">
              {documentId && (
                <CollaborativeEditor 
                  documentId={documentId}
                  showComments={true}
                  showCursors={true}
                  className="min-h-[600px]"
                />
              )}
            </TabsContent>
            
            <TabsContent value="features" className="p-6 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Brain className="h-5 w-5 mr-2" />
                      LangChain Integration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium">Enhanced RAG System</h3>
                      <p className="text-sm text-muted-foreground">
                        Retrieval-Augmented Generation with document processing, embedding, and vector search
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium">Agent System</h3>
                      <p className="text-sm text-muted-foreground">
                        Intelligent agents with tool usage, reasoning, and action execution
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium">Memory Management</h3>
                      <p className="text-sm text-muted-foreground">
                        Conversation history, context awareness, and user-specific memory
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium">Document Processing</h3>
                      <p className="text-sm text-muted-foreground">
                        Automatic chunking, embedding, and indexing of documents for knowledge retrieval
                      </p>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="h-5 w-5 mr-2" />
                      Real-time Collaboration
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h3 className="font-medium">Presence System</h3>
                      <p className="text-sm text-muted-foreground">
                        Real-time user presence, status updates, and activity tracking
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium">Collaborative Editing</h3>
                      <p className="text-sm text-muted-foreground">
                        Real-time document editing with cursor tracking and conflict resolution
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium">Comments & Annotations</h3>
                      <p className="text-sm text-muted-foreground">
                        Add comments to documents, resolve discussions, and track changes
                      </p>
                    </div>
                    
                    <div>
                      <h3 className="font-medium">Session Management</h3>
                      <p className="text-sm text-muted-foreground">
                        Collaborative sessions for documents, projects, and meetings
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
