import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { 
  Trash2, 
  RefreshCw, 
  Database, 
  Users, 
  BarChart3, 
  Clock,
  CheckCircle,
  AlertTriangle
} from "lucide-react";
import {
  forceClearCache,
  clearDashboardCache,
  clearUserCache,
  clearCacheByPattern,
  clearAllCacheAndReload,
  cacheManager
} from "@/utils/cacheManager";

export const CacheClearingPanel = () => {
  const { toast } = useToast();
  const [isClearing, setIsClearing] = useState(false);
  const [lastClearTime, setLastClearTime] = useState<string | null>(
    localStorage.getItem('lastCacheClear')
  );

  const handleClearAll = async () => {
    setIsClearing(true);
    try {
      const success = await clearAllCacheAndReload();
      if (success) {
        setLastClearTime(Date.now().toString());
        localStorage.setItem('lastCacheClear', Date.now().toString());

        toast({
          title: "Complete Cache Clear",
          description: "All cache, storage, and cookies cleared. Redirecting to prevent reload loops...",
          variant: "default",
        });

        // Force redirect to auth page to prevent infinite reload
        setTimeout(() => {
          window.location.href = '/auth';
        }, 2000);
      } else {
        throw new Error('Cache clearing failed');
      }
    } catch (error) {
      console.error('Cache clear error:', error);
      toast({
        title: "Cache Clear Failed",
        description: "There was an error clearing the cache. Please try manual browser refresh.",
        variant: "destructive",
      });
    } finally {
      setIsClearing(false);
    }
  };

  const handleClearDashboard = () => {
    clearDashboardCache();
    toast({
      title: "Dashboard Cache Cleared",
      description: "Dashboard data cache has been cleared.",
    });
  };

  const handleClearUsers = () => {
    clearUserCache();
    toast({
      title: "User Cache Cleared",
      description: "User-related cache has been cleared.",
    });
  };

  const handleClearPattern = (pattern: string, description: string) => {
    clearCacheByPattern(pattern);
    toast({
      title: "Cache Cleared",
      description: `${description} cache has been cleared.`,
    });
  };

  const formatLastClearTime = () => {
    if (!lastClearTime) return "Never";
    const date = new Date(parseInt(lastClearTime));
    return date.toLocaleString();
  };

  const shouldShowWarning = () => {
    if (!lastClearTime) return true;
    const timeDiff = Date.now() - parseInt(lastClearTime);
    const thirtyMinutes = 30 * 60 * 1000;
    return timeDiff > thirtyMinutes;
  };

  return (
    <Card className="glassmorphism">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5 text-primary" />
          Cache Management
          {shouldShowWarning() && (
            <Badge variant="destructive" className="ml-2">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Cache Stale
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Cache Status */}
        <div className="p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Last Cache Clear:</span>
            </div>
            <Badge variant={shouldShowWarning() ? "destructive" : "secondary"}>
              {formatLastClearTime()}
            </Badge>
          </div>
        </div>

        {/* Infinite Reload Fix Notice */}
        <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <RefreshCw className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <p className="font-medium">Experiencing infinite reload issues?</p>
            <p>Use "Clear All Cache" below to fix reload loops and improve system performance.</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Button
            onClick={handleClearAll}
            disabled={isClearing}
            className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
          >
            {isClearing ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
            Clear All Cache
          </Button>

          <Button
            onClick={handleClearDashboard}
            variant="outline"
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            Clear Dashboard
          </Button>

          <Button
            onClick={handleClearUsers}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Users className="h-4 w-4" />
            Clear User Data
          </Button>

          <Button
            onClick={() => handleClearPattern('invoice', 'Invoice')}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Clear Invoices
          </Button>
        </div>

        {/* Advanced Cache Controls */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground">Advanced Controls</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={() => handleClearPattern('project', 'Project')}
              variant="ghost"
              size="sm"
              className="justify-start"
            >
              Clear Projects
            </Button>
            <Button
              onClick={() => handleClearPattern('task', 'Task')}
              variant="ghost"
              size="sm"
              className="justify-start"
            >
              Clear Tasks
            </Button>
            <Button
              onClick={() => handleClearPattern('memo', 'Memo')}
              variant="ghost"
              size="sm"
              className="justify-start"
            >
              Clear Memos
            </Button>
            <Button
              onClick={() => handleClearPattern('report', 'Report')}
              variant="ghost"
              size="sm"
              className="justify-start"
            >
              Clear Reports
            </Button>
          </div>
        </div>

        {/* Cache Info */}
        <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex items-start gap-2">
            <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
            <div className="text-sm text-blue-800 dark:text-blue-200">
              <p className="font-medium">Cache automatically clears:</p>
              <ul className="mt-1 text-xs space-y-1">
                <li>• Every 30 minutes when stale</li>
                <li>• On server restart detection</li>
                <li>• When authentication state changes</li>
                <li>• During periodic cleanup (hourly)</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
