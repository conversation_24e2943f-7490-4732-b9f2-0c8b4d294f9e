import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Co<PERSON>, RotateCcw, RotateCw, Type } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const TextReverseTool: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [activeTab, setActiveTab] = useState('reverse');
  const { toast } = useToast();

  const reverseText = (text: string): string => {
    return text.split('').reverse().join('');
  };

  const reverseWords = (text: string): string => {
    return text.split(' ').reverse().join(' ');
  };

  const reverseLines = (text: string): string => {
    return text.split('\n').reverse().join('\n');
  };

  const reverseSentences = (text: string): string => {
    return text.split(/([.!?]+)/).filter(Boolean).reverse().join('');
  };

  const handleProcess = () => {
    let result = '';
    
    switch (activeTab) {
      case 'reverse':
        result = reverseText(inputText);
        break;
      case 'words':
        result = reverseWords(inputText);
        break;
      case 'lines':
        result = reverseLines(inputText);
        break;
      case 'sentences':
        result = reverseSentences(inputText);
        break;
      default:
        result = reverseText(inputText);
    }
    
    setOutputText(result);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "Text copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy text",
        variant: "destructive",
      });
    }
  };

  const clearAll = () => {
    setInputText('');
    setOutputText('');
  };

  const swapInputOutput = () => {
    const temp = inputText;
    setInputText(outputText);
    setOutputText(temp);
  };

  const loadSample = () => {
    setInputText("Hello World!\nThis is a sample text.\nWith multiple lines and sentences!");
  };

  const getTabDescription = () => {
    switch (activeTab) {
      case 'reverse':
        return 'Reverse all characters in the text';
      case 'words':
        return 'Reverse the order of words';
      case 'lines':
        return 'Reverse the order of lines';
      case 'sentences':
        return 'Reverse the order of sentences';
      default:
        return 'Reverse text';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-purple-500 rounded-lg">
          <RotateCw className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">Text Reverser</h2>
          <p className="text-muted-foreground">Reverse text in various ways</p>
        </div>
      </div>

      {/* Mode Selection */}
      <Card>
        <CardContent className="p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="reverse">Characters</TabsTrigger>
              <TabsTrigger value="words">Words</TabsTrigger>
              <TabsTrigger value="lines">Lines</TabsTrigger>
              <TabsTrigger value="sentences">Sentences</TabsTrigger>
            </TabsList>
          </Tabs>
          <p className="text-sm text-muted-foreground mt-2">{getTabDescription()}</p>
        </CardContent>
      </Card>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Input Text
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={loadSample}>
                Load Sample
              </Button>
              <Button variant="outline" size="sm" onClick={swapInputOutput} disabled={!outputText}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Swap
              </Button>
              <Button variant="outline" size="sm" onClick={clearAll}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Enter text to reverse..."
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="min-h-[150px] font-mono"
          />
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {inputText.length} characters
              </Badge>
              <Badge variant="outline">
                {inputText.split(' ').filter(word => word.length > 0).length} words
              </Badge>
              <Badge variant="outline">
                {inputText.split('\n').length} lines
              </Badge>
            </div>
            <Button onClick={handleProcess} disabled={!inputText.trim()}>
              <RotateCw className="h-4 w-4 mr-2" />
              Reverse {activeTab === 'reverse' ? 'Text' : activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Output Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Reversed Text
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(outputText)}
              disabled={!outputText}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 rounded-md p-3 min-h-[150px] font-mono text-sm">
            {outputText || (
              <span className="text-muted-foreground italic">
                Reversed text will appear here...
              </span>
            )}
          </div>
          {outputText && (
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center gap-2">
                <Badge variant="outline">
                  {outputText.length} characters
                </Badge>
                <Badge variant="outline">
                  {outputText.split(' ').filter(word => word.length > 0).length} words
                </Badge>
                <Badge variant="outline">
                  {outputText.split('\n').length} lines
                </Badge>
              </div>
              <Badge variant="secondary">
                {activeTab === 'reverse' ? 'Characters' : activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Reversed
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Reverse Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Character Reverse:</h4>
              <p className="text-muted-foreground mb-3">
                Reverses every character in the text. "Hello" becomes "olleH".
              </p>
              <h4 className="font-semibold mb-2">Word Reverse:</h4>
              <p className="text-muted-foreground">
                Reverses the order of words. "Hello World" becomes "World Hello".
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Line Reverse:</h4>
              <p className="text-muted-foreground mb-3">
                Reverses the order of lines in multi-line text.
              </p>
              <h4 className="font-semibold mb-2">Sentence Reverse:</h4>
              <p className="text-muted-foreground">
                Reverses the order of sentences based on punctuation.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextReverseTool;
