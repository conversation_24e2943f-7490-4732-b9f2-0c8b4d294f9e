import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Calendar, Clock, User, FileText, Send, CheckCircle, XCircle, AlertCircle, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { api } from "@/lib/api";

interface LeaveRequest {
  id: string;
  user_id: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  reason: string;
  status: string;
  approved_by: string | null;
  approved_at: string | null;
  rejection_reason: string | null;
  created_at: string;
}

interface LeaveBalance {
  id: string;
  leave_type: string;
  total_days: number;
  used_days: number;
  remaining_days: number;
  year: number;
}

interface LeaveFormData {
  leave_type: string;
  start_date: string;
  end_date: string;
  reason: string;
}

export const EnhancedLeaveApplication = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<LeaveFormData>({
    leave_type: "annual",
    start_date: "",
    end_date: "",
    reason: "",
  });

  // Fetch user's leave requests
  const { data: leaveRequests, isLoading: requestsLoading } = useQuery({
    queryKey: ['leave-requests', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];
      
      const { data, error } = await supabase
        .from('leave_requests')
        .select('*')
        .eq('user_id', userProfile.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as LeaveRequest[];
    },
    enabled: !!userProfile?.id,
  });

  // Fetch user's leave balances with enhanced error handling
  const { data: leaveBalances, isLoading: balancesLoading } = useQuery({
    queryKey: ['leave-balances', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];

      try {
        const currentYear = new Date().getFullYear();
        const { data, error } = await supabase
          .from('leave_balances')
          .select('*')
          .eq('user_id', userProfile.id)
          .eq('year', currentYear);

        if (error) {
          console.warn('Leave balances query error:', error);

          // If table doesn't exist or access denied, return default balances
          if (error.code === 'PGRST116' || error.code === '42P01' || error.status === 406) {
            console.log('Leave balances table not accessible, using default values');
            return [
              {
                leave_type: 'annual',
                remaining_days: 25,
                total_days: 25,
                used_days: 0,
                year: currentYear,
                user_id: userProfile.id
              },
              {
                leave_type: 'sick',
                remaining_days: 10,
                total_days: 10,
                used_days: 0,
                year: currentYear,
                user_id: userProfile.id
              }
            ];
          }

          // For other errors, still return default but log the issue
          return [
            {
              leave_type: 'annual',
              remaining_days: 25,
              total_days: 25,
              used_days: 0,
              year: currentYear,
              user_id: userProfile.id
            }
          ];
        }

        return data as LeaveBalance[];
      } catch (error) {
        console.warn('Could not fetch leave balances:', error);
        const currentYear = new Date().getFullYear();
        return [
          {
            leave_type: 'annual',
            remaining_days: 25,
            total_days: 25,
            used_days: 0,
            year: currentYear,
            user_id: userProfile?.id || ''
          }
        ];
      }
    },
    enabled: !!userProfile?.id,
  });

  // Submit leave request mutation
  const submitLeaveMutation = useMutation({
    mutationFn: async (requestData: LeaveFormData) => {
      // First, submit the leave request
      const { data: leaveRequest, error: leaveError } = await supabase
        .from('leave_requests')
        .insert([{
          user_id: userProfile?.id,
          leave_type: requestData.leave_type,
          start_date: requestData.start_date,
          end_date: requestData.end_date,
          reason: requestData.reason,
          status: 'pending',
        }])
        .select()
        .single();

      if (leaveError) throw leaveError;

      // Send notifications to managers
      try {
        await api.notifications.sendLeaveRequestNotification({
          leaveRequestId: leaveRequest.id,
          employeeName: userProfile?.full_name || 'Unknown Employee',
          leaveType: requestData.leave_type,
          startDate: requestData.start_date,
          endDate: requestData.end_date,
          reason: requestData.reason,
        });
      } catch (notificationError) {
        console.warn('Failed to send notifications:', notificationError);
        // Don't fail the whole request if notifications fail
      }

      return leaveRequest;
    },
    onSuccess: () => {
      toast({
        title: "Leave Request Submitted Successfully!",
        description: "Your manager has been notified and will review your request.",
        duration: 5000,
      });
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      queryClient.invalidateQueries({ queryKey: ['leave-balances'] });
      setFormData({
        leave_type: "annual",
        start_date: "",
        end_date: "",
        reason: "",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Failed to Submit Leave Request",
        description: error.message || "Please try again later.",
        variant: "destructive",
      });
      console.error('Leave request error:', error);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.start_date || !formData.end_date || !formData.reason.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    const startDate = new Date(formData.start_date);
    const endDate = new Date(formData.end_date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (startDate < today) {
      toast({
        title: "Invalid Start Date",
        description: "Start date cannot be in the past.",
        variant: "destructive",
      });
      return;
    }

    if (startDate > endDate) {
      toast({
        title: "Invalid Date Range",
        description: "End date must be after start date.",
        variant: "destructive",
      });
      return;
    }

    // Check leave balance
    const selectedBalance = leaveBalances?.find(b => b.leave_type === formData.leave_type);
    const requestedDays = calculateLeaveDays(formData.start_date, formData.end_date);
    
    if (selectedBalance && requestedDays > selectedBalance.remaining_days) {
      toast({
        title: "Insufficient Leave Balance",
        description: `You only have ${selectedBalance.remaining_days} days remaining for ${formData.leave_type} leave.`,
        variant: "destructive",
      });
      return;
    }

    submitLeaveMutation.mutate(formData);
  };

  const getStatusBadge = (status: string) => {
    const configs = {
      pending: { variant: "secondary" as const, icon: Clock, color: "text-yellow-600" },
      approved: { variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
      rejected: { variant: "destructive" as const, icon: XCircle, color: "text-red-600" }
    };
    
    const config = configs[status as keyof typeof configs] || configs.pending;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.toUpperCase()}
      </Badge>
    );
  };

  const calculateLeaveDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let count = 0;
    const current = new Date(start);

    while (current <= end) {
      const dayOfWeek = current.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Exclude weekends
        count++;
      }
      current.setDate(current.getDate() + 1);
    }
    
    return count;
  };

  const getLeaveBalance = (leaveType: string) => {
    return leaveBalances?.find(b => b.leave_type === leaveType);
  };

  const getPendingRequests = () => {
    return leaveRequests?.filter(req => req.status === 'pending').length || 0;
  };

  return (
    <div className="space-y-6">
      {/* Leave Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Annual Leave</p>
                <p className="text-2xl font-bold">
                  {getLeaveBalance('annual')?.remaining_days || 0} days
                </p>
                <p className="text-xs text-muted-foreground">
                  of {getLeaveBalance('annual')?.total_days || 0} remaining
                </p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Requests</p>
                <p className="text-2xl font-bold">{getPendingRequests()}</p>
                <p className="text-xs text-muted-foreground">awaiting approval</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Used This Year</p>
                <p className="text-2xl font-bold">
                  {getLeaveBalance('annual')?.used_days || 0} days
                </p>
                <p className="text-xs text-muted-foreground">annual leave taken</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Leave Request Form */}
      <Card className="shadow-lg">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
          <CardTitle className="flex items-center gap-2 text-blue-700">
            <Send className="h-5 w-5" />
            Submit New Leave Request
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="leave_type" className="text-sm font-medium">Leave Type</Label>
                <Select 
                  value={formData.leave_type} 
                  onValueChange={(value) => setFormData({ ...formData, leave_type: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="annual">Annual Leave</SelectItem>
                    <SelectItem value="sick">Sick Leave</SelectItem>
                    <SelectItem value="emergency">Emergency Leave</SelectItem>
                    <SelectItem value="maternity">Maternity Leave</SelectItem>
                    <SelectItem value="paternity">Paternity Leave</SelectItem>
                    <SelectItem value="study">Study Leave</SelectItem>
                    <SelectItem value="personal">Personal Leave</SelectItem>
                  </SelectContent>
                </Select>
                {getLeaveBalance(formData.leave_type) && (
                  <p className="text-xs text-muted-foreground mt-1">
                    Available: {getLeaveBalance(formData.leave_type)?.remaining_days} days
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="duration" className="text-sm font-medium">Duration</Label>
                <Input
                  className="mt-1"
                  value={
                    formData.start_date && formData.end_date
                      ? `${calculateLeaveDays(formData.start_date, formData.end_date)} working days`
                      : "Select dates to calculate"
                  }
                  disabled
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="start_date" className="text-sm font-medium">Start Date</Label>
                <Input
                  type="date"
                  className="mt-1"
                  value={formData.start_date}
                  onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                  min={new Date().toISOString().split('T')[0]}
                  required
                />
              </div>
              <div>
                <Label htmlFor="end_date" className="text-sm font-medium">End Date</Label>
                <Input
                  type="date"
                  className="mt-1"
                  value={formData.end_date}
                  onChange={(e) => setFormData({ ...formData, end_date: e.target.value })}
                  min={formData.start_date || new Date().toISOString().split('T')[0]}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="reason" className="text-sm font-medium">Reason for Leave</Label>
              <Textarea
                className="mt-1"
                value={formData.reason}
                onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
                placeholder="Please provide a detailed reason for your leave request..."
                rows={4}
                required
              />
            </div>

            <Button 
              type="submit" 
              disabled={submitLeaveMutation.isPending || !userProfile}
              className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              {submitLeaveMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting Request...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Submit Leave Request
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Leave History */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Leave Request History ({leaveRequests?.length || 0})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {requestsLoading ? (
            <div className="text-center py-8">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p>Loading leave history...</p>
            </div>
          ) : leaveRequests && leaveRequests.length > 0 ? (
            <div className="space-y-4">
              {leaveRequests.map((request) => (
                <div key={request.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <h4 className="font-medium capitalize text-lg">
                        {request.leave_type.replace('_', ' ')} Leave
                      </h4>
                      {getStatusBadge(request.status)}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Applied on {format(new Date(request.created_at), 'MMM dd, yyyy')}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="space-y-1">
                      <span className="font-medium text-muted-foreground">Duration:</span>
                      <div className="font-medium">
                        {format(new Date(request.start_date), 'MMM dd')} - {format(new Date(request.end_date), 'MMM dd, yyyy')}
                      </div>
                      <div className="text-muted-foreground">
                        ({calculateLeaveDays(request.start_date, request.end_date)} working days)
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <span className="font-medium text-muted-foreground">Reason:</span>
                      <div className="text-gray-700">{request.reason}</div>
                    </div>
                    
                    <div className="space-y-1">
                      {request.status === 'approved' && request.approved_at && (
                        <>
                          <span className="font-medium text-green-600">Approved:</span>
                          <div className="text-muted-foreground">
                            {format(new Date(request.approved_at), 'MMM dd, yyyy')}
                          </div>
                        </>
                      )}
                      {request.status === 'rejected' && request.rejection_reason && (
                        <>
                          <span className="font-medium text-red-600">Rejection Reason:</span>
                          <div className="text-muted-foreground">{request.rejection_reason}</div>
                        </>
                      )}
                      {request.status === 'pending' && (
                        <>
                          <span className="font-medium text-yellow-600">Status:</span>
                          <div className="text-muted-foreground">Awaiting manager approval</div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 text-muted-foreground">
              <Calendar className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="font-medium text-lg mb-2">No Leave Requests Yet</h3>
              <p className="text-sm">Your submitted leave requests will appear here</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}; 