// Direct email sending using Resend API
const RESEND_API_KEY = 're_iEWVBukX_AM4BhzUNeyxxVTLdHNrLLGqs';

const recipients = [
  '<EMAIL>',
  '<EMAIL>', 
  'obin<PERSON><PERSON>@ctnigeria.com',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

const emailTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTNL AI WorkBoard - Now Live!</title>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #000000; color: #ffffff;">
    <div style="max-width: 600px; margin: 0 auto; padding: 2rem;">
        <!-- Header -->
        <div style="text-align: center; margin-bottom: 2rem;">
            <h1 style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-size: 2.5rem; font-weight: bold; margin: 0;">
                🚀 CTNL AI WorkBoard is Now Live!
            </h1>
            <p style="color: #cccccc; font-size: 1.1rem; margin-top: 1rem;">
                Your comprehensive AI-powered task management system is ready for use
            </p>
        </div>

        <!-- Main Content -->
        <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 16px; padding: 2rem; margin-bottom: 2rem;">
            <h2 style="color: #ef4444; margin-bottom: 1rem;">🎉 Welcome to the Future of Work Management</h2>
            <p style="color: #cccccc; line-height: 1.6; margin-bottom: 1.5rem;">
                We're excited to announce that CTNL AI WorkBoard is now fully operational and ready to transform your workflow experience. 
                This powerful platform combines cutting-edge AI technology with intuitive design to deliver unparalleled productivity.
            </p>

            <!-- Action Buttons -->
            <div style="text-align: center; margin: 2rem 0;">
                <a href="https://ai.ctnigeria.com/auth" style="background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 1rem 2rem; border-radius: 8px; text-decoration: none; font-weight: 600; display: inline-block; margin: 0.5rem;">
                    🚀 Access Platform
                </a>
                <a href="https://ai.ctnigeria.com/user-manual.html" style="background: rgba(255,255,255,0.1); color: white; border: 2px solid rgba(255,255,255,0.3); padding: 1rem 2rem; border-radius: 8px; text-decoration: none; font-weight: 600; display: inline-block; margin: 0.5rem;">
                    📖 User Manual
                </a>
            </div>
        </div>

        <!-- Features -->
        <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 16px; padding: 2rem; margin-bottom: 2rem;">
            <h3 style="color: #ef4444; margin-bottom: 1rem;">✨ Key Features</h3>
            <ul style="color: #cccccc; line-height: 1.8; padding-left: 1rem;">
                <li>🤖 AI-Powered Task Assistant & Automation</li>
                <li>🕒 Real-Time Time Attendance with GPS Location</li>
                <li>📋 Advanced Project Management with Kanban Boards</li>
                <li>💰 Comprehensive Financial & Accounting Suite</li>
                <li>📱 Fully Mobile-Responsive Design</li>
                <li>👥 Multi-Role Access Control (Admin, Manager, Staff, Accountant)</li>
                <li>🔔 Real-time Notifications & Email Alerts</li>
                <li>📊 Advanced Reporting & Analytics Dashboard</li>
                <li>📖 Complete User Manual & Documentation</li>
            </ul>
        </div>

        <!-- Access Information -->
        <div style="background: rgba(239, 68, 68, 0.1); border: 1px solid rgba(239, 68, 68, 0.3); border-radius: 16px; padding: 2rem; margin-bottom: 2rem;">
            <h3 style="color: #ef4444; margin-bottom: 1rem;">🔐 Your Access Information</h3>
            <div style="color: #cccccc;">
                <p><strong>🌐 Website:</strong> <a href="https://ai.ctnigeria.com" style="color: #ef4444;">https://ai.ctnigeria.com</a></p>
                <p><strong>🔑 Login:</strong> <a href="https://ai.ctnigeria.com/auth" style="color: #ef4444;">https://ai.ctnigeria.com/auth</a></p>
                <p><strong>📖 User Manual:</strong> <a href="https://ai.ctnigeria.com/user-manual.html" style="color: #ef4444;">Complete User Guide</a></p>
            </div>
        </div>

        <!-- System Stats -->
        <div style="background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.1); border-radius: 16px; padding: 2rem; margin-bottom: 2rem;">
            <h3 style="color: #ef4444; margin-bottom: 1rem;">📊 System Capabilities</h3>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; color: #cccccc;">
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; color: #ef4444;">5</div>
                    <div>User Roles</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; color: #ef4444;">27</div>
                    <div>Database Tables</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; color: #ef4444;">15+</div>
                    <div>AI Functions</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 2rem; font-weight: bold; color: #ef4444;">100%</div>
                    <div>Mobile Ready</div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="text-align: center; color: #888888; font-size: 0.9rem; margin-top: 2rem;">
            <p>This email was sent from CTNL AI WorkBoard System</p>
            <p>© 2025 CTNL. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
`;

async function sendEmails() {
    console.log('🚀 Sending CTNL AI WorkBoard launch emails...');
    
    for (const email of recipients) {
        try {
            const response = await fetch('https://api.resend.com/emails', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${RESEND_API_KEY}`,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    from: 'CTNL AI WorkBoard <<EMAIL>>',
                    to: [email],
                    subject: '🚀 CTNL AI WorkBoard is Now Live - Access Your Dashboard',
                    html: emailTemplate
                })
            });

            const result = await response.json();
            
            if (response.ok) {
                console.log(`✅ Email sent successfully to ${email}`);
            } else {
                console.error(`❌ Failed to send email to ${email}:`, result);
            }
        } catch (error) {
            console.error(`❌ Error sending email to ${email}:`, error);
        }
        
        // Small delay between emails
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🎉 Launch notification campaign completed!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📧 Total Recipients:', recipients.length);
    console.log('🔗 Platform URL: https://ai.ctnigeria.com');
    console.log('📖 User Manual: https://ai.ctnigeria.com/user-manual.html');
    console.log('✨ CTNL AI WorkBoard is ready for your team!');
}

sendEmails();
