import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';

/**
 * System Logs Service
 * Comprehensive logging system with categorization, severity levels, and search functionality
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'critical';
export type LogCategory = 'general' | 'auth' | 'database' | 'api' | 'ui' | 'security' | 'performance' | 'business';

export interface LogEntry {
  id?: string;
  user_id?: string;
  level: LogLevel;
  category: LogCategory;
  subcategory?: string;
  message: string;
  details?: string;
  source?: string;
  function_name?: string;
  file_path?: string;
  line_number?: number;
  stack_trace?: string;
  request_id?: string;
  session_id?: string;
  ip_address?: string;
  user_agent?: string;
  endpoint?: string;
  method?: string;
  status_code?: number;
  response_time_ms?: number;
  error_code?: string;
  error_type?: string;
  context?: Record<string, any>;
  metadata?: Record<string, any>;
  resolved?: boolean;
  resolved_by?: string;
  resolved_at?: string;
  resolution_notes?: string;
  created_at?: string;
}

export interface LogFilter {
  level?: LogLevel[];
  category?: LogCategory[];
  subcategory?: string[];
  user_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
  resolved?: boolean;
  limit?: number;
  offset?: number;
}

export interface LogStats {
  total: number;
  by_level: Record<LogLevel, number>;
  by_category: Record<LogCategory, number>;
  recent_errors: LogEntry[];
  unresolved_count: number;
  top_error_sources: Array<{ source: string; count: number }>;
}

export class SystemLogsService {
  private static sessionId: string = '';
  private static requestId: string = '';

  // Initialize logging service
  static initialize(): void {
    this.sessionId = crypto.randomUUID();
    this.requestId = crypto.randomUUID();
    
    // Set up global error handlers
    this.setupGlobalErrorHandlers();
    
    console.log('📝 System Logs Service initialized');
  }

  // Log a message with specified level and category
  static async log(
    level: LogLevel,
    category: LogCategory,
    message: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      const logEntry: LogEntry = {
        user_id: currentUser?.user_id,
        level,
        category,
        message,
        details,
        source: this.getCallerInfo(),
        session_id: this.sessionId,
        request_id: this.requestId,
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent,
        context: {
          url: window.location.href,
          timestamp: new Date().toISOString(),
          user_role: currentUser?.role
        },
        metadata: {
          ...metadata,
          browser: this.getBrowserInfo(),
          screen: this.getScreenInfo()
        }
      };

      await this.insertLogEntry(logEntry);
      
      // Also log to console for development
      this.logToConsole(level, category, message, details);
      
    } catch (error) {
      console.error('Failed to log message:', error);
    }
  }

  // Convenience methods for different log levels
  static async debug(category: LogCategory, message: string, details?: string, metadata?: Record<string, any>): Promise<void> {
    await this.log('debug', category, message, details, metadata);
  }

  static async info(category: LogCategory, message: string, details?: string, metadata?: Record<string, any>): Promise<void> {
    await this.log('info', category, message, details, metadata);
  }

  static async warn(category: LogCategory, message: string, details?: string, metadata?: Record<string, any>): Promise<void> {
    await this.log('warn', category, message, details, metadata);
  }

  static async error(category: LogCategory, message: string, details?: string, metadata?: Record<string, any>): Promise<void> {
    await this.log('error', category, message, details, metadata);
  }

  static async critical(category: LogCategory, message: string, details?: string, metadata?: Record<string, any>): Promise<void> {
    await this.log('critical', category, message, details, metadata);
  }

  // Log API requests and responses
  static async logAPIRequest(
    method: string,
    endpoint: string,
    status_code: number,
    response_time_ms: number,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const level: LogLevel = status_code >= 500 ? 'error' : status_code >= 400 ? 'warn' : 'info';
    
    await this.log(level, 'api', `${method} ${endpoint} - ${status_code}`, details, {
      ...metadata,
      method,
      endpoint,
      status_code,
      response_time_ms
    });
  }

  // Log authentication events
  static async logAuthEvent(
    event: string,
    user_id?: string,
    success: boolean = true,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const level: LogLevel = success ? 'info' : 'warn';
    
    await this.log(level, 'auth', `Authentication: ${event}`, details, {
      ...metadata,
      event,
      user_id,
      success
    });
  }

  // Log database operations
  static async logDatabaseOperation(
    operation: string,
    table: string,
    success: boolean = true,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const level: LogLevel = success ? 'debug' : 'error';
    
    await this.log(level, 'database', `${operation} on ${table}`, details, {
      ...metadata,
      operation,
      table,
      success
    });
  }

  // Log business events
  static async logBusinessEvent(
    event: string,
    entity_type: string,
    entity_id?: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log('info', 'business', `Business Event: ${event}`, details, {
      ...metadata,
      event,
      entity_type,
      entity_id
    });
  }

  // Log performance metrics
  static async logPerformance(
    metric: string,
    value: number,
    unit: string,
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const level: LogLevel = value > 5000 ? 'warn' : 'info'; // Warn if over 5 seconds
    
    await this.log(level, 'performance', `Performance: ${metric} = ${value}${unit}`, details, {
      ...metadata,
      metric,
      value,
      unit
    });
  }

  // Log security events
  static async logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const level: LogLevel = severity === 'critical' ? 'critical' : severity === 'high' ? 'error' : 'warn';
    
    await this.log(level, 'security', `Security Event: ${event}`, details, {
      ...metadata,
      event,
      severity
    });
  }

  // Get logs with filtering and pagination
  static async getLogs(filter: LogFilter = {}): Promise<{ data: LogEntry[]; count: number }> {
    try {
      let query = supabase
        .from('system_logs')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filter.level && filter.level.length > 0) {
        query = query.in('level', filter.level);
      }

      if (filter.category && filter.category.length > 0) {
        query = query.in('category', filter.category);
      }

      if (filter.subcategory && filter.subcategory.length > 0) {
        query = query.in('subcategory', filter.subcategory);
      }

      if (filter.user_id) {
        query = query.eq('user_id', filter.user_id);
      }

      if (filter.date_from) {
        query = query.gte('created_at', filter.date_from);
      }

      if (filter.date_to) {
        query = query.lte('created_at', filter.date_to);
      }

      if (filter.search) {
        query = query.or(`message.ilike.%${filter.search}%,details.ilike.%${filter.search}%`);
      }

      if (filter.resolved !== undefined) {
        query = query.eq('resolved', filter.resolved);
      }

      // Apply pagination
      const limit = filter.limit || 50;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      // Order by most recent first
      query = query.order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error fetching logs:', error);
      return { data: [], count: 0 };
    }
  }

  // Get log statistics
  static async getLogStats(days: number = 7): Promise<LogStats> {
    try {
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      const { data: logs } = await supabase
        .from('system_logs')
        .select('*')
        .gte('created_at', dateFrom.toISOString());

      if (!logs) {
        return this.getEmptyStats();
      }

      const stats: LogStats = {
        total: logs.length,
        by_level: {
          debug: logs.filter(l => l.level === 'debug').length,
          info: logs.filter(l => l.level === 'info').length,
          warn: logs.filter(l => l.level === 'warn').length,
          error: logs.filter(l => l.level === 'error').length,
          critical: logs.filter(l => l.level === 'critical').length
        },
        by_category: {
          general: logs.filter(l => l.category === 'general').length,
          auth: logs.filter(l => l.category === 'auth').length,
          database: logs.filter(l => l.category === 'database').length,
          api: logs.filter(l => l.category === 'api').length,
          ui: logs.filter(l => l.category === 'ui').length,
          security: logs.filter(l => l.category === 'security').length,
          performance: logs.filter(l => l.category === 'performance').length,
          business: logs.filter(l => l.category === 'business').length
        },
        recent_errors: logs
          .filter(l => l.level === 'error' || l.level === 'critical')
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 10),
        unresolved_count: logs.filter(l => !l.resolved && (l.level === 'error' || l.level === 'critical')).length,
        top_error_sources: this.getTopErrorSources(logs)
      };

      return stats;
    } catch (error) {
      console.error('Error fetching log stats:', error);
      return this.getEmptyStats();
    }
  }

  // Resolve a log entry
  static async resolveLog(logId: string, resolution_notes?: string): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();

      await supabase
        .from('system_logs')
        .update({
          resolved: true,
          resolved_by: currentUser?.user_id,
          resolved_at: new Date().toISOString(),
          resolution_notes
        })
        .eq('id', logId);

      await this.log('info', 'general', `Log entry resolved: ${logId}`, resolution_notes);
    } catch (error) {
      console.error('Error resolving log:', error);
    }
  }

  // Clear old logs
  static async clearOldLogs(days: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const { data, error } = await supabase
        .from('system_logs')
        .delete()
        .lt('created_at', cutoffDate.toISOString());

      if (error) {
        throw error;
      }

      const deletedCount = data?.length || 0;
      await this.log('info', 'general', `Cleared ${deletedCount} old log entries`, `Older than ${days} days`);
      
      return deletedCount;
    } catch (error) {
      console.error('Error clearing old logs:', error);
      return 0;
    }
  }

  // Export logs
  static async exportLogs(filter: LogFilter = {}): Promise<string> {
    try {
      const { data: logs } = await this.getLogs({ ...filter, limit: 10000 });
      
      const csvHeader = 'Timestamp,Level,Category,Message,Details,User,Source\n';
      const csvRows = logs.map(log => {
        const timestamp = new Date(log.created_at!).toISOString();
        const level = log.level;
        const category = log.category;
        const message = `"${log.message.replace(/"/g, '""')}"`;
        const details = `"${(log.details || '').replace(/"/g, '""')}"`;
        const user = log.user_id || 'System';
        const source = log.source || 'Unknown';
        
        return `${timestamp},${level},${category},${message},${details},${user},${source}`;
      }).join('\n');

      return csvHeader + csvRows;
    } catch (error) {
      console.error('Error exporting logs:', error);
      return '';
    }
  }

  // Private helper methods
  private static async insertLogEntry(logEntry: LogEntry): Promise<void> {
    try {
      await supabase.from('system_logs').insert(logEntry);
    } catch (error) {
      console.error('Failed to insert log entry:', error);
    }
  }

  private static logToConsole(level: LogLevel, category: LogCategory, message: string, details?: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] [${category}] ${message}`;
    
    switch (level) {
      case 'debug':
        console.debug(logMessage, details);
        break;
      case 'info':
        console.info(logMessage, details);
        break;
      case 'warn':
        console.warn(logMessage, details);
        break;
      case 'error':
      case 'critical':
        console.error(logMessage, details);
        break;
    }
  }

  private static getCallerInfo(): string {
    try {
      const stack = new Error().stack;
      if (stack) {
        const lines = stack.split('\n');
        // Skip the first few lines to get to the actual caller
        for (let i = 3; i < lines.length; i++) {
          const line = lines[i];
          if (line && !line.includes('SystemLogsService')) {
            return line.trim();
          }
        }
      }
    } catch (error) {
      // Ignore errors in getting caller info
    }
    return 'Unknown';
  }

  private static async getClientIP(): Promise<string> {
    try {
      // In a real application, you might get this from a header or API
      return 'Unknown';
    } catch (error) {
      return 'Unknown';
    }
  }

  private static getBrowserInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };
  }

  private static getScreenInfo(): Record<string, any> {
    return {
      width: screen.width,
      height: screen.height,
      availWidth: screen.availWidth,
      availHeight: screen.availHeight,
      colorDepth: screen.colorDepth,
      pixelDepth: screen.pixelDepth
    };
  }

  private static setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.error('general', 'Unhandled Promise Rejection', event.reason?.toString(), {
        promise: event.promise,
        reason: event.reason
      });
    });

    // Handle global errors
    window.addEventListener('error', (event) => {
      this.error('general', 'Global Error', event.message, {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error?.toString()
      });
    });
  }

  private static getTopErrorSources(logs: LogEntry[]): Array<{ source: string; count: number }> {
    const errorLogs = logs.filter(l => l.level === 'error' || l.level === 'critical');
    const sourceCounts: Record<string, number> = {};

    errorLogs.forEach(log => {
      const source = log.source || 'Unknown';
      sourceCounts[source] = (sourceCounts[source] || 0) + 1;
    });

    return Object.entries(sourceCounts)
      .map(([source, count]) => ({ source, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);
  }

  private static getEmptyStats(): LogStats {
    return {
      total: 0,
      by_level: {
        debug: 0,
        info: 0,
        warn: 0,
        error: 0,
        critical: 0
      },
      by_category: {
        general: 0,
        auth: 0,
        database: 0,
        api: 0,
        ui: 0,
        security: 0,
        performance: 0,
        business: 0
      },
      recent_errors: [],
      unresolved_count: 0,
      top_error_sources: []
    };
  }

  // Generate new request ID for tracking related operations
  static generateRequestId(): string {
    this.requestId = crypto.randomUUID();
    return this.requestId;
  }

  // Get current session ID
  static getSessionId(): string {
    return this.sessionId;
  }

  // Get current request ID
  static getRequestId(): string {
    return this.requestId;
  }
}

export default SystemLogsService;
