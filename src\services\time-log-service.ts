import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { UserActivitiesService } from './user-activities-service';
import { SystemLogsService } from './system-logs-service';

/**
 * Time Log Service
 * Comprehensive time tracking system with clock in/out, project time allocation, and reporting
 */

export type ActivityType = 'work' | 'break' | 'meeting' | 'training' | 'admin' | 'other';
export type TimeLogStatus = 'active' | 'paused' | 'completed' | 'cancelled';

export interface TimeLog {
  id?: string;
  user_id: string;
  project_id?: string;
  task_id?: string;
  activity_type: ActivityType;
  description?: string;
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  status: TimeLogStatus;
  location?: string;
  device_info?: Record<string, any>;
  is_billable?: boolean;
  hourly_rate?: number;
  total_amount?: number;
  approved?: boolean;
  approved_by?: string;
  approved_at?: string;
  notes?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface TimeLogFilter {
  user_id?: string;
  project_id?: string;
  task_id?: string;
  activity_type?: ActivityType[];
  status?: TimeLogStatus[];
  date_from?: string;
  date_to?: string;
  is_billable?: boolean;
  approved?: boolean;
  limit?: number;
  offset?: number;
}

export interface TimeLogStats {
  total_hours: number;
  billable_hours: number;
  non_billable_hours: number;
  total_amount: number;
  by_activity: Record<ActivityType, number>;
  by_project: Array<{ project_id: string; project_name: string; hours: number }>;
  by_day: Array<{ date: string; hours: number }>;
  efficiency_score: number;
  average_session_duration: number;
}

export interface ActiveTimeLog {
  id: string;
  start_time: string;
  activity_type: ActivityType;
  description?: string;
  project_id?: string;
  task_id?: string;
  elapsed_minutes: number;
}

export class TimeLogService {
  private static activeTimer: NodeJS.Timeout | null = null;
  private static currentTimeLog: ActiveTimeLog | null = null;

  // Initialize time log service
  static initialize(): void {
    // Check for any active time logs on startup
    this.checkForActiveTimeLogs();
    
    console.log('⏱️ Time Log Service initialized');
  }

  // Start time tracking
  static async startTimeTracking(
    activity_type: ActivityType,
    description?: string,
    project_id?: string,
    task_id?: string,
    metadata?: Record<string, any>
  ): Promise<string | null> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Stop any existing active time log
      await this.stopActiveTimeTracking();

      const timeLog: TimeLog = {
        user_id: currentUser.user_id!,
        project_id,
        task_id,
        activity_type,
        description,
        start_time: new Date().toISOString(),
        status: 'active',
        location: await this.getCurrentLocation(),
        device_info: this.getDeviceInfo(),
        is_billable: this.determineBillableStatus(activity_type),
        hourly_rate: await this.getUserHourlyRate(currentUser.user_id!),
        metadata: {
          ...metadata,
          started_by: 'user',
          start_method: 'manual'
        }
      };

      const { data, error } = await supabase
        .from('time_logs')
        .insert(timeLog)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Set up active tracking
      this.currentTimeLog = {
        id: data.id,
        start_time: data.start_time,
        activity_type: data.activity_type,
        description: data.description,
        project_id: data.project_id,
        task_id: data.task_id,
        elapsed_minutes: 0
      };

      // Start the timer
      this.startTimer();

      // Log the activity
      await UserActivitiesService.logActivity(
        'create',
        'Started Time Tracking',
        `Started tracking time for ${activity_type}${description ? `: ${description}` : ''}`,
        'time_log',
        data.id,
        description || activity_type,
        { activity_type, project_id, task_id }
      );

      await SystemLogsService.info('business', 'Time tracking started', `User started tracking ${activity_type}`, {
        user_id: currentUser.user_id,
        time_log_id: data.id,
        activity_type
      });

      return data.id;
    } catch (error) {
      console.error('Error starting time tracking:', error);
      await SystemLogsService.error('business', 'Failed to start time tracking', error.toString());
      return null;
    }
  }

  // Stop time tracking
  static async stopTimeTracking(timeLogId?: string, notes?: string): Promise<boolean> {
    try {
      const logId = timeLogId || this.currentTimeLog?.id;
      
      if (!logId) {
        throw new Error('No active time log to stop');
      }

      const endTime = new Date().toISOString();
      
      // Get the time log to calculate duration
      const { data: timeLog, error: fetchError } = await supabase
        .from('time_logs')
        .select('*')
        .eq('id', logId)
        .single();

      if (fetchError || !timeLog) {
        throw new Error('Time log not found');
      }

      const startTime = new Date(timeLog.start_time);
      const endTimeDate = new Date(endTime);
      const durationMinutes = Math.round((endTimeDate.getTime() - startTime.getTime()) / (1000 * 60));
      const totalAmount = timeLog.hourly_rate ? (durationMinutes / 60) * timeLog.hourly_rate : 0;

      // Update the time log
      const { error: updateError } = await supabase
        .from('time_logs')
        .update({
          end_time: endTime,
          duration_minutes: durationMinutes,
          total_amount: totalAmount,
          status: 'completed',
          notes,
          updated_at: new Date().toISOString()
        })
        .eq('id', logId);

      if (updateError) {
        throw updateError;
      }

      // Clear active tracking
      this.stopTimer();
      this.currentTimeLog = null;

      // Log the activity
      await UserActivitiesService.logActivity(
        'complete',
        'Stopped Time Tracking',
        `Stopped tracking time for ${timeLog.activity_type} (${durationMinutes} minutes)`,
        'time_log',
        logId,
        timeLog.description || timeLog.activity_type,
        { 
          duration_minutes: durationMinutes,
          total_amount: totalAmount,
          activity_type: timeLog.activity_type
        }
      );

      await SystemLogsService.info('business', 'Time tracking stopped', `User stopped tracking ${timeLog.activity_type}`, {
        time_log_id: logId,
        duration_minutes: durationMinutes,
        total_amount: totalAmount
      });

      return true;
    } catch (error) {
      console.error('Error stopping time tracking:', error);
      await SystemLogsService.error('business', 'Failed to stop time tracking', error.toString());
      return false;
    }
  }

  // Pause time tracking
  static async pauseTimeTracking(timeLogId?: string): Promise<boolean> {
    try {
      const logId = timeLogId || this.currentTimeLog?.id;
      
      if (!logId) {
        throw new Error('No active time log to pause');
      }

      const { error } = await supabase
        .from('time_logs')
        .update({
          status: 'paused',
          updated_at: new Date().toISOString()
        })
        .eq('id', logId);

      if (error) {
        throw error;
      }

      // Pause the timer
      this.stopTimer();

      await UserActivitiesService.logActivity(
        'update',
        'Paused Time Tracking',
        'Paused active time tracking session',
        'time_log',
        logId
      );

      return true;
    } catch (error) {
      console.error('Error pausing time tracking:', error);
      return false;
    }
  }

  // Resume time tracking
  static async resumeTimeTracking(timeLogId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('time_logs')
        .update({
          status: 'active',
          updated_at: new Date().toISOString()
        })
        .eq('id', timeLogId);

      if (error) {
        throw error;
      }

      // Get the time log details for active tracking
      const { data: timeLog } = await supabase
        .from('time_logs')
        .select('*')
        .eq('id', timeLogId)
        .single();

      if (timeLog) {
        this.currentTimeLog = {
          id: timeLog.id,
          start_time: timeLog.start_time,
          activity_type: timeLog.activity_type,
          description: timeLog.description,
          project_id: timeLog.project_id,
          task_id: timeLog.task_id,
          elapsed_minutes: timeLog.duration_minutes || 0
        };

        this.startTimer();
      }

      await UserActivitiesService.logActivity(
        'update',
        'Resumed Time Tracking',
        'Resumed paused time tracking session',
        'time_log',
        timeLogId
      );

      return true;
    } catch (error) {
      console.error('Error resuming time tracking:', error);
      return false;
    }
  }

  // Get current active time log
  static getCurrentTimeLog(): ActiveTimeLog | null {
    return this.currentTimeLog;
  }

  // Get time logs with filtering
  static async getTimeLogs(filter: TimeLogFilter = {}): Promise<{ data: TimeLog[]; count: number }> {
    try {
      let query = supabase
        .from('time_logs')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filter.user_id) {
        query = query.eq('user_id', filter.user_id);
      }

      if (filter.project_id) {
        query = query.eq('project_id', filter.project_id);
      }

      if (filter.task_id) {
        query = query.eq('task_id', filter.task_id);
      }

      if (filter.activity_type && filter.activity_type.length > 0) {
        query = query.in('activity_type', filter.activity_type);
      }

      if (filter.status && filter.status.length > 0) {
        query = query.in('status', filter.status);
      }

      if (filter.date_from) {
        query = query.gte('start_time', filter.date_from);
      }

      if (filter.date_to) {
        query = query.lte('start_time', filter.date_to);
      }

      if (filter.is_billable !== undefined) {
        query = query.eq('is_billable', filter.is_billable);
      }

      if (filter.approved !== undefined) {
        query = query.eq('approved', filter.approved);
      }

      // Apply pagination
      const limit = filter.limit || 50;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      // Order by most recent first
      query = query.order('start_time', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error fetching time logs:', error);
      return { data: [], count: 0 };
    }
  }

  // Get time log statistics
  static async getTimeLogStats(user_id?: string, days: number = 7): Promise<TimeLogStats> {
    try {
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      let query = supabase
        .from('time_logs')
        .select('*')
        .gte('start_time', dateFrom.toISOString())
        .eq('status', 'completed');

      if (user_id) {
        query = query.eq('user_id', user_id);
      }

      const { data: timeLogs } = await query;

      if (!timeLogs) {
        return this.getEmptyStats();
      }

      const totalMinutes = timeLogs.reduce((sum, log) => sum + (log.duration_minutes || 0), 0);
      const billableMinutes = timeLogs.filter(log => log.is_billable).reduce((sum, log) => sum + (log.duration_minutes || 0), 0);
      const nonBillableMinutes = totalMinutes - billableMinutes;
      const totalAmount = timeLogs.reduce((sum, log) => sum + (log.total_amount || 0), 0);

      const stats: TimeLogStats = {
        total_hours: Math.round((totalMinutes / 60) * 100) / 100,
        billable_hours: Math.round((billableMinutes / 60) * 100) / 100,
        non_billable_hours: Math.round((nonBillableMinutes / 60) * 100) / 100,
        total_amount: Math.round(totalAmount * 100) / 100,
        by_activity: this.groupByActivity(timeLogs),
        by_project: await this.groupByProject(timeLogs),
        by_day: this.groupByDay(timeLogs),
        efficiency_score: this.calculateEfficiencyScore(timeLogs),
        average_session_duration: totalMinutes / timeLogs.length || 0
      };

      return stats;
    } catch (error) {
      console.error('Error fetching time log stats:', error);
      return this.getEmptyStats();
    }
  }

  // Approve time logs
  static async approveTimeLogs(timeLogIds: string[], notes?: string): Promise<boolean> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('time_logs')
        .update({
          approved: true,
          approved_by: currentUser.user_id,
          approved_at: new Date().toISOString(),
          notes: notes
        })
        .in('id', timeLogIds);

      if (error) {
        throw error;
      }

      // Log the approval activity
      await UserActivitiesService.logActivity(
        'approve',
        'Approved Time Logs',
        `Approved ${timeLogIds.length} time log entries`,
        'time_log',
        undefined,
        undefined,
        { approved_count: timeLogIds.length, notes }
      );

      return true;
    } catch (error) {
      console.error('Error approving time logs:', error);
      return false;
    }
  }

  // Export time logs
  static async exportTimeLogs(filter: TimeLogFilter = {}): Promise<string> {
    try {
      const { data: timeLogs } = await this.getTimeLogs({ ...filter, limit: 10000 });
      
      const csvHeader = 'Date,User,Activity Type,Description,Project,Start Time,End Time,Duration (hours),Billable,Rate,Amount,Status,Approved\n';
      const csvRows = timeLogs.map(log => {
        const date = new Date(log.start_time).toLocaleDateString();
        const user = log.user_id;
        const activityType = log.activity_type;
        const description = `"${(log.description || '').replace(/"/g, '""')}"`;
        const project = log.project_id || '';
        const startTime = new Date(log.start_time).toLocaleTimeString();
        const endTime = log.end_time ? new Date(log.end_time).toLocaleTimeString() : '';
        const duration = log.duration_minutes ? (log.duration_minutes / 60).toFixed(2) : '0';
        const billable = log.is_billable ? 'Yes' : 'No';
        const rate = log.hourly_rate || '0';
        const amount = log.total_amount || '0';
        const status = log.status;
        const approved = log.approved ? 'Yes' : 'No';
        
        return `${date},${user},${activityType},${description},${project},${startTime},${endTime},${duration},${billable},${rate},${amount},${status},${approved}`;
      }).join('\n');

      return csvHeader + csvRows;
    } catch (error) {
      console.error('Error exporting time logs:', error);
      return '';
    }
  }

  // Private helper methods
  private static async checkForActiveTimeLogs(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) return;

      const { data: activeLog } = await supabase
        .from('time_logs')
        .select('*')
        .eq('user_id', currentUser.user_id)
        .eq('status', 'active')
        .order('start_time', { ascending: false })
        .limit(1)
        .single();

      if (activeLog) {
        this.currentTimeLog = {
          id: activeLog.id,
          start_time: activeLog.start_time,
          activity_type: activeLog.activity_type,
          description: activeLog.description,
          project_id: activeLog.project_id,
          task_id: activeLog.task_id,
          elapsed_minutes: this.calculateElapsedMinutes(activeLog.start_time)
        };

        this.startTimer();
      }
    } catch (error) {
      console.error('Error checking for active time logs:', error);
    }
  }

  private static async stopActiveTimeTracking(): Promise<void> {
    if (this.currentTimeLog) {
      await this.stopTimeTracking(this.currentTimeLog.id);
    }
  }

  private static startTimer(): void {
    this.stopTimer(); // Clear any existing timer
    
    this.activeTimer = setInterval(() => {
      if (this.currentTimeLog) {
        this.currentTimeLog.elapsed_minutes = this.calculateElapsedMinutes(this.currentTimeLog.start_time);
      }
    }, 60000); // Update every minute
  }

  private static stopTimer(): void {
    if (this.activeTimer) {
      clearInterval(this.activeTimer);
      this.activeTimer = null;
    }
  }

  private static calculateElapsedMinutes(startTime: string): number {
    const start = new Date(startTime);
    const now = new Date();
    return Math.round((now.getTime() - start.getTime()) / (1000 * 60));
  }

  private static determineBillableStatus(activity_type: ActivityType): boolean {
    // Work and meetings are typically billable, breaks and admin are not
    return ['work', 'meeting'].includes(activity_type);
  }

  private static async getUserHourlyRate(user_id: string): Promise<number> {
    try {
      // This would typically come from user profile or HR system
      // For now, return a default rate
      return 50.00; // Default hourly rate
    } catch (error) {
      return 0;
    }
  }

  private static async getCurrentLocation(): Promise<string> {
    try {
      // In a real application, you might get this from geolocation API
      return 'Office';
    } catch (error) {
      return 'Unknown';
    }
  }

  private static getDeviceInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language
    };
  }

  private static groupByActivity(timeLogs: TimeLog[]): Record<ActivityType, number> {
    const grouped: Record<ActivityType, number> = {
      work: 0,
      break: 0,
      meeting: 0,
      training: 0,
      admin: 0,
      other: 0
    };

    timeLogs.forEach(log => {
      const hours = (log.duration_minutes || 0) / 60;
      grouped[log.activity_type] += hours;
    });

    // Round to 2 decimal places
    Object.keys(grouped).forEach(key => {
      grouped[key as ActivityType] = Math.round(grouped[key as ActivityType] * 100) / 100;
    });

    return grouped;
  }

  private static async groupByProject(timeLogs: TimeLog[]): Promise<Array<{ project_id: string; project_name: string; hours: number }>> {
    try {
      const projectHours: Record<string, number> = {};
      
      timeLogs.forEach(log => {
        if (log.project_id) {
          const hours = (log.duration_minutes || 0) / 60;
          projectHours[log.project_id] = (projectHours[log.project_id] || 0) + hours;
        }
      });

      // Get project names
      const projectIds = Object.keys(projectHours);
      const projects = await ComprehensiveAPI.getAllProjects();
      const projectMap = new Map(projects.map(p => [p.id, p.name]));

      return Object.entries(projectHours)
        .map(([project_id, hours]) => ({
          project_id,
          project_name: projectMap.get(project_id) || 'Unknown Project',
          hours: Math.round(hours * 100) / 100
        }))
        .sort((a, b) => b.hours - a.hours);
    } catch (error) {
      return [];
    }
  }

  private static groupByDay(timeLogs: TimeLog[]): Array<{ date: string; hours: number }> {
    const dailyHours: Record<string, number> = {};
    
    timeLogs.forEach(log => {
      const date = new Date(log.start_time).toISOString().split('T')[0];
      const hours = (log.duration_minutes || 0) / 60;
      dailyHours[date] = (dailyHours[date] || 0) + hours;
    });

    return Object.entries(dailyHours)
      .map(([date, hours]) => ({
        date,
        hours: Math.round(hours * 100) / 100
      }))
      .sort((a, b) => a.date.localeCompare(b.date));
  }

  private static calculateEfficiencyScore(timeLogs: TimeLog[]): number {
    if (timeLogs.length === 0) return 0;
    
    const workLogs = timeLogs.filter(log => log.activity_type === 'work');
    const totalMinutes = timeLogs.reduce((sum, log) => sum + (log.duration_minutes || 0), 0);
    const workMinutes = workLogs.reduce((sum, log) => sum + (log.duration_minutes || 0), 0);
    
    return totalMinutes > 0 ? Math.round((workMinutes / totalMinutes) * 100) : 0;
  }

  private static getEmptyStats(): TimeLogStats {
    return {
      total_hours: 0,
      billable_hours: 0,
      non_billable_hours: 0,
      total_amount: 0,
      by_activity: {
        work: 0,
        break: 0,
        meeting: 0,
        training: 0,
        admin: 0,
        other: 0
      },
      by_project: [],
      by_day: [],
      efficiency_score: 0,
      average_session_duration: 0
    };
  }
}

export default TimeLogService;
