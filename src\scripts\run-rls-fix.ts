import { fixRLSComprehensive } from './fix-rls-comprehensive';

/**
 * Simple script to run the comprehensive RLS fix
 * Can be imported and executed from anywhere in the application
 */

export const runRLSFix = async () => {
  console.log('🚀 Starting RLS fix...');
  
  try {
    const success = await fixRLSComprehensive();
    
    if (success) {
      console.log('✅ RLS fix completed successfully!');
      return { success: true, message: 'RLS fix completed successfully!' };
    } else {
      console.error('❌ RLS fix failed');
      return { success: false, message: 'RLS fix failed' };
    }
  } catch (error) {
    console.error('❌ Error running RLS fix:', error);
    return { success: false, message: `Error: ${error.message}` };
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  // Check if we should auto-run the fix
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.get('fix-rls') === 'true') {
    runRLSFix();
  }
}

export default runRLSFix;
