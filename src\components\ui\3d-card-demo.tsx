'use client'

import React from 'react'
import { CardBody, CardContainer, CardItem } from '@/components/ui/3d-card'

export default function ThreeDCardDemo () {
  return (
    <CardContainer className='inter-var'>
      <CardBody className='bg-gradient-to-br from-gray-50 via-white to-gray-100 relative group/card dark:hover:shadow-2xl dark:hover:shadow-red-500/[0.1] dark:bg-gradient-to-br dark:from-gray-900 dark:via-black dark:to-gray-800 dark:border-red-500/[0.2] border-red-500/[0.1] w-auto sm:w-[30rem] h-auto rounded-xl p-6 border'>
        <CardItem
          translateZ='50'
          className='text-xl font-bold text-neutral-600 dark:text-white'
        >
          CTNL AI Work-Board
        </CardItem>
        <CardItem
          as='p'
          translateZ='60'
          className='text-neutral-500 text-sm max-w-sm mt-2 dark:text-neutral-300'
        >
          Experience the power of AI-driven workforce management with stunning 3D interactions
        </CardItem>
        <CardItem translateZ='100' className='w-full mt-4'>
          <div className='h-60 w-full bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-xl group-hover/card:shadow-xl flex items-center justify-center'>
            <div className='text-center text-white'>
              <div className='text-4xl font-bold mb-2'>CTNL AI</div>
              <div className='text-sm opacity-80'>Workforce Management</div>
            </div>
          </div>
        </CardItem>
        <div className='flex justify-between items-center mt-20'>
          <CardItem
            translateZ={20}
            as='button'
            className='px-4 py-2 rounded-xl text-xs font-normal dark:text-white text-neutral-600 hover:text-red-500 transition-colors'
          >
            Learn More →
          </CardItem>
          <CardItem
            translateZ={20}
            as='button'
            className='px-4 py-2 rounded-xl bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white text-xs font-bold transition-all duration-300 shadow-lg hover:shadow-red-500/25'
          >
            Get Started
          </CardItem>
        </div>
      </CardBody>
    </CardContainer>
  )
}
