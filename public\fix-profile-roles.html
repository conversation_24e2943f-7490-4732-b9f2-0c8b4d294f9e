<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Profile Roles - Supabase</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .role-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .role-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .role-card h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .role-card p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Profile Roles & RLS Fix</h1>
        <p>This tool will fix Supabase profile roles and RLS policies for all user types: manager, staff, accountant, staff-admin, hr, and admin.</p>
        
        <div class="warning">
            <strong>⚠️ Important:</strong> This will update database schema and RLS policies. Make sure you have admin access.
        </div>
        
        <h3>📋 Supported Roles</h3>
        <div class="role-list">
            <div class="role-card">
                <h4>Admin</h4>
                <p>Full system access, can manage all users and settings</p>
            </div>
            <div class="role-card">
                <h4>Manager</h4>
                <p>Department management, team oversight, project coordination</p>
            </div>
            <div class="role-card">
                <h4>Staff-Admin</h4>
                <p>Administrative support, user management assistance</p>
            </div>
            <div class="role-card">
                <h4>HR</h4>
                <p>Human resources, employee management, recruitment</p>
            </div>
            <div class="role-card">
                <h4>Accountant</h4>
                <p>Financial management, budgeting, expense tracking</p>
            </div>
            <div class="role-card">
                <h4>Staff</h4>
                <p>General employees, task execution, basic access</p>
            </div>
        </div>
        
        <div id="status"></div>
        
        <button id="fixBtn" class="button" onclick="runFix()">
            🚀 Fix Profile Roles & RLS
        </button>
        
        <button id="testBtn" class="button" onclick="testRoles()" disabled>
            🧪 Test Role Access
        </button>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        async function runFix() {
            const fixBtn = document.getElementById('fixBtn');
            const testBtn = document.getElementById('testBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Fixing...';
            
            try {
                log('🚀 Starting comprehensive profile roles fix...');
                
                // Step 1: Update profiles table schema
                log('📋 Updating profiles table schema...');
                const { error: schemaError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Drop existing role constraint if it exists
                        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
                        
                        -- Add comprehensive role constraint
                        ALTER TABLE public.profiles ADD CONSTRAINT profiles_role_check 
                          CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
                        
                        -- Drop existing account_type constraint if it exists
                        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_account_type_check;
                        
                        -- Add comprehensive account_type constraint
                        ALTER TABLE public.profiles ADD CONSTRAINT profiles_account_type_check 
                          CHECK (account_type IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));
                        
                        -- Drop existing status constraint if it exists
                        ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_status_check;
                        
                        -- Add comprehensive status constraint
                        ALTER TABLE public.profiles ADD CONSTRAINT profiles_status_check 
                          CHECK (status IN ('active', 'inactive', 'suspended', 'pending'));
                        
                        -- Ensure all required columns exist
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff';
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC';
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS bio TEXT;
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS location TEXT;
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS skills TEXT[];
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}';
                        ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{}';
                        
                        -- Create indexes for performance
                        CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
                        CREATE INDEX IF NOT EXISTS idx_profiles_department_id ON public.profiles(department_id);
                        CREATE INDEX IF NOT EXISTS idx_profiles_status ON public.profiles(status);
                        CREATE INDEX IF NOT EXISTS idx_profiles_account_type ON public.profiles(account_type);
                        CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
                    `
                });
                
                if (schemaError) {
                    throw new Error('Schema update failed: ' + schemaError.message);
                }
                
                log('✅ Schema updated successfully');
                
                // Step 2: Fix RLS policies
                log('🔒 Fixing RLS policies...');
                const { error: rlsError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Drop all existing policies to start fresh
                        DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
                        DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
                        DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
                        DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
                        DROP POLICY IF EXISTS "Admins can update all profiles" ON public.profiles;
                        DROP POLICY IF EXISTS "Admins can delete profiles" ON public.profiles;
                        DROP POLICY IF EXISTS "Managers can view department profiles" ON public.profiles;
                        DROP POLICY IF EXISTS "HR can view all profiles" ON public.profiles;
                        DROP POLICY IF EXISTS "Staff-admin can view profiles" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_read_own" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_admin_all" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_manager_department" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_hr_all" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_staff_admin" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_select_manager" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_select_hr" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_select_staff_admin" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_insert_admin" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_update_admin" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_update_manager" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_update_hr" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_update_staff_admin" ON public.profiles;
                        DROP POLICY IF EXISTS "profiles_delete_admin" ON public.profiles;
                        
                        -- Enable RLS
                        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
                        
                        -- 1. SELECT policies
                        
                        -- Users can view their own profile
                        CREATE POLICY "profiles_select_own" ON public.profiles
                          FOR SELECT USING (auth.uid() = id);
                        
                        -- Admin can view all profiles
                        CREATE POLICY "profiles_select_admin" ON public.profiles
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role = 'admin'
                            )
                          );
                        
                        -- Manager can view profiles in their department
                        CREATE POLICY "profiles_select_manager" ON public.profiles
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles p1
                              WHERE p1.id = auth.uid() 
                              AND p1.role = 'manager'
                              AND (
                                p1.department_id = public.profiles.department_id OR
                                public.profiles.id = auth.uid()
                              )
                            )
                          );
                        
                        -- HR can view all profiles
                        CREATE POLICY "profiles_select_hr" ON public.profiles
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role = 'hr'
                            )
                          );
                        
                        -- Staff-admin can view all profiles
                        CREATE POLICY "profiles_select_staff_admin" ON public.profiles
                          FOR SELECT USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role = 'staff-admin'
                            )
                          );
                        
                        -- 2. INSERT policies
                        
                        -- Users can insert their own profile
                        CREATE POLICY "profiles_insert_own" ON public.profiles
                          FOR INSERT WITH CHECK (auth.uid() = id);
                        
                        -- Admin can insert any profile
                        CREATE POLICY "profiles_insert_admin" ON public.profiles
                          FOR INSERT WITH CHECK (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role = 'admin'
                            )
                          );
                        
                        -- 3. UPDATE policies
                        
                        -- Users can update their own profile (except role)
                        CREATE POLICY "profiles_update_own" ON public.profiles
                          FOR UPDATE USING (auth.uid() = id)
                          WITH CHECK (
                            auth.uid() = id AND
                            (OLD.role = NEW.role OR NEW.role IS NULL)
                          );
                        
                        -- Admin can update any profile
                        CREATE POLICY "profiles_update_admin" ON public.profiles
                          FOR UPDATE USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role = 'admin'
                            )
                          );
                        
                        -- 4. DELETE policies (only admin)
                        
                        CREATE POLICY "profiles_delete_admin" ON public.profiles
                          FOR DELETE USING (
                            EXISTS (
                              SELECT 1 FROM public.profiles 
                              WHERE id = auth.uid() AND role = 'admin'
                            )
                          );
                        
                        -- Grant permissions
                        GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
                        GRANT ALL ON public.profiles TO service_role;
                    `
                });
                
                if (rlsError) {
                    throw new Error('RLS policies failed: ' + rlsError.message);
                }
                
                log('✅ RLS policies fixed successfully');
                
                // Step 3: Create role management functions
                log('⚙️ Creating role management functions...');
                const { error: functionsError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Function to check if user has specific role
                        CREATE OR REPLACE FUNCTION public.user_has_role(user_id UUID, required_role TEXT)
                        RETURNS BOOLEAN AS $$
                        BEGIN
                          RETURN EXISTS (
                            SELECT 1 FROM public.profiles 
                            WHERE id = user_id AND role = required_role
                          );
                        END;
                        $$ LANGUAGE plpgsql SECURITY DEFINER;
                        
                        -- Function to get user role
                        CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
                        RETURNS TEXT AS $$
                        DECLARE
                          user_role TEXT;
                        BEGIN
                          SELECT role INTO user_role 
                          FROM public.profiles 
                          WHERE id = user_id;
                          
                          RETURN COALESCE(user_role, 'staff');
                        END;
                        $$ LANGUAGE plpgsql SECURITY DEFINER;
                        
                        -- Grant execute permissions
                        GRANT EXECUTE ON FUNCTION public.user_has_role TO authenticated;
                        GRANT EXECUTE ON FUNCTION public.get_user_role TO authenticated;
                    `
                });
                
                if (functionsError) {
                    throw new Error('Functions creation failed: ' + functionsError.message);
                }
                
                log('✅ Role management functions created successfully');
                log('🎉 Profile roles fix completed successfully!');
                showStatus('✅ Profile roles and RLS policies fixed successfully!', 'success');
                
                testBtn.disabled = false;
                
            } catch (error) {
                log('❌ Error: ' + error.message);
                showStatus('❌ Fix failed: ' + error.message, 'error');
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🚀 Fix Profile Roles & RLS';
            }
        }
        
        async function testRoles() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            
            try {
                log('🧪 Testing profile roles and access...');
                
                // Test 1: Basic profile query
                const { data: profiles, error: profileError } = await supabase
                    .from('profiles')
                    .select('id, role, full_name, email')
                    .limit(5);
                
                if (profileError) {
                    throw new Error('Profile query failed: ' + profileError.message);
                }
                
                log('✅ Profile query successful, found ' + (profiles?.length || 0) + ' profiles');
                
                // Test 2: Current user role
                const { data: user } = await supabase.auth.getUser();
                if (user.user) {
                    const { data: currentRole, error: roleError } = await supabase
                        .rpc('get_user_role', { user_id: user.user.id });
                    
                    if (roleError) {
                        log('⚠️ Role function test failed: ' + roleError.message);
                    } else {
                        log('✅ Current user role: ' + currentRole);
                    }
                }
                
                // Test 3: Role constraints
                log('🔍 Testing role constraints...');
                const validRoles = ['admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'];
                log('✅ Valid roles: ' + validRoles.join(', '));
                
                log('🎉 All tests passed! Profile roles are working correctly.');
                showStatus('🎉 All tests passed! Profile roles are working correctly.', 'success');
                
            } catch (error) {
                log('❌ Test failed: ' + error.message);
                showStatus('❌ Test failed: ' + error.message, 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 Test Role Access';
            }
        }
        
        // Make functions global
        window.runFix = runFix;
        window.testRoles = testRoles;
    </script>
</body>
</html>
