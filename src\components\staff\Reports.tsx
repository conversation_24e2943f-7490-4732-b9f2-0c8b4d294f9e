import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, Calendar, TrendingUp } from 'lucide-react'

export const Reports = () => {
  return (
    <div className='space-y-6'>
      <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <FileText className='h-5 w-5' />
              Weekly Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-muted-foreground mb-4'>
              Submit your weekly activity reports
            </p>
            <div className='text-2xl font-bold'>3</div>
            <p className='text-xs text-muted-foreground'>Reports this month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <Calendar className='h-5 w-5' />
              Project Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-muted-foreground mb-4'>
              Track project progress and milestones
            </p>
            <div className='text-2xl font-bold'>5</div>
            <p className='text-xs text-muted-foreground'>Active projects</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className='flex items-center gap-2'>
              <TrendingUp className='h-5 w-5' />
              Site Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className='text-sm text-muted-foreground mb-4'>
              Telecom site status and maintenance
            </p>
            <div className='text-2xl font-bold'>12</div>
            <p className='text-xs text-muted-foreground'>Sites monitored</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
