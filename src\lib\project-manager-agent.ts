/**
 * Project Manager Assistant Agent for CTNL AI Workboard
 * Based on LangGraph workflow with structured output and self-reflection
 */

import { supabase } from '@/integrations/supabase/client';

// Data Models
export interface Task {
  id: string;
  task_name: string;
  task_description: string;
  estimated_days: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'not_started' | 'in_progress' | 'completed' | 'blocked';
}

export interface TaskDependency {
  task_id: string;
  dependent_task_ids: string[];
  dependency_type: 'finish_to_start' | 'start_to_start' | 'finish_to_finish';
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  skills: string[];
  availability: number; // 0-100 percentage
  current_workload: number; // hours per week
}

export interface TaskAllocation {
  task_id: string;
  team_member_id: string;
  allocated_hours: number;
  start_date: Date;
  end_date: Date;
}

export interface TaskSchedule {
  task_id: string;
  start_date: Date;
  end_date: Date;
  duration_days: number;
}

export interface Risk {
  task_id: string;
  risk_score: number; // 0-10
  risk_factors: string[];
  mitigation_strategies: string[];
}

export interface ProjectPlan {
  tasks: Task[];
  dependencies: TaskDependency[];
  schedule: TaskSchedule[];
  allocations: TaskAllocation[];
  risks: Risk[];
  total_duration_days: number;
  overall_risk_score: number;
}

// Agent State
export interface AgentState {
  project_description: string;
  team_members: TeamMember[];
  tasks: Task[];
  dependencies: TaskDependency[];
  schedule: TaskSchedule[];
  allocations: TaskAllocation[];
  risks: Risk[];
  iteration_number: number;
  max_iterations: number;
  insights: string[];
  project_plans: ProjectPlan[];
  overall_risk_scores: number[];
}

export class ProjectManagerAgent {
  private openaiApiKey: string;
  private useLangChain: boolean;
  private isInitialized: boolean = false;

  constructor() {
    this.openaiApiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
    this.useLangChain = import.meta.env.USE_LANGCHAIN === 'true';
    this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      // Load API key from database if not in environment
      if (!this.openaiApiKey) {
        await this.loadAPIKeyFromDatabase();
      }
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize Project Manager Agent:', error);
    }
  }

  private async loadAPIKeyFromDatabase(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('api_keys')
        .select('api_key')
        .eq('service', 'openai')
        .eq('is_active', true)
        .single();

      if (!error && data) {
        this.openaiApiKey = data.api_key;
      }
    } catch (error) {
      console.warn('Could not load API key from database:', error);
    }
  }

  /**
   * Generate tasks from project description
   */
  async generateTasks(projectDescription: string): Promise<Task[]> {
    const prompt = `
      You are an expert project manager. Analyze this project description and create actionable tasks:
      
      Project: ${projectDescription}
      
      Requirements:
      1. Extract all actionable and realistic tasks
      2. Provide estimated days for each task (max 5 days, break down larger tasks)
      3. Assign priority levels (low, medium, high, critical)
      4. Ensure tasks are clearly defined and achievable
      
      Return a JSON array of tasks with: id, task_name, task_description, estimated_days, priority, status
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseTasksResponse(response);
    } catch (error) {
      console.error('Error generating tasks:', error);
      return this.generateFallbackTasks(projectDescription);
    }
  }

  /**
   * Analyze task dependencies
   */
  async analyzeDependencies(tasks: Task[]): Promise<TaskDependency[]> {
    const prompt = `
      Analyze these tasks and identify dependencies between them:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      
      For each task, determine:
      1. Which tasks must be completed before it can start
      2. The type of dependency (finish_to_start, start_to_start, finish_to_finish)
      
      Return a JSON array of dependencies with: task_id, dependent_task_ids, dependency_type
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseDependenciesResponse(response);
    } catch (error) {
      console.error('Error analyzing dependencies:', error);
      return [];
    }
  }

  /**
   * Create project schedule
   */
  async createSchedule(
    tasks: Task[], 
    dependencies: TaskDependency[], 
    insights: string[] = []
  ): Promise<TaskSchedule[]> {
    const prompt = `
      Create an optimized project schedule for these tasks:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Dependencies: ${JSON.stringify(dependencies, null, 2)}
      Previous Insights: ${insights.join('; ')}
      
      Requirements:
      1. Respect all task dependencies
      2. Minimize overall project duration
      3. Parallelize tasks where possible
      4. Consider task priorities
      
      Return a JSON array of schedules with: task_id, start_date, end_date, duration_days
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseScheduleResponse(response);
    } catch (error) {
      console.error('Error creating schedule:', error);
      return this.generateFallbackSchedule(tasks);
    }
  }

  /**
   * Allocate tasks to team members
   */
  async allocateTasks(
    tasks: Task[],
    schedule: TaskSchedule[],
    teamMembers: TeamMember[],
    insights: string[] = []
  ): Promise<TaskAllocation[]> {
    const prompt = `
      Allocate tasks to team members based on skills and availability:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Schedule: ${JSON.stringify(schedule, null, 2)}
      Team Members: ${JSON.stringify(teamMembers, null, 2)}
      Previous Insights: ${insights.join('; ')}
      
      Requirements:
      1. Match tasks to team member skills
      2. Respect availability and current workload
      3. No overlapping task assignments
      4. Balance workload across team
      
      Return a JSON array of allocations with: task_id, team_member_id, allocated_hours, start_date, end_date
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseAllocationResponse(response);
    } catch (error) {
      console.error('Error allocating tasks:', error);
      return [];
    }
  }

  /**
   * Assess project risks
   */
  async assessRisks(
    tasks: Task[],
    schedule: TaskSchedule[],
    allocations: TaskAllocation[]
  ): Promise<Risk[]> {
    const prompt = `
      Assess risks for this project plan:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Schedule: ${JSON.stringify(schedule, null, 2)}
      Allocations: ${JSON.stringify(allocations, null, 2)}
      
      For each task, analyze:
      1. Complexity and technical risks
      2. Resource availability risks
      3. Dependency and scheduling risks
      4. Assign risk score (0-10)
      5. Suggest mitigation strategies
      
      Return a JSON array of risks with: task_id, risk_score, risk_factors, mitigation_strategies
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return this.parseRiskResponse(response);
    } catch (error) {
      console.error('Error assessing risks:', error);
      return [];
    }
  }

  /**
   * Generate insights for improvement
   */
  async generateInsights(
    tasks: Task[],
    schedule: TaskSchedule[],
    allocations: TaskAllocation[],
    risks: Risk[]
  ): Promise<string> {
    const prompt = `
      Analyze this project plan and generate actionable insights:
      
      Tasks: ${JSON.stringify(tasks, null, 2)}
      Schedule: ${JSON.stringify(schedule, null, 2)}
      Allocations: ${JSON.stringify(allocations, null, 2)}
      Risks: ${JSON.stringify(risks, null, 2)}
      
      Provide insights on:
      1. Potential bottlenecks and resource conflicts
      2. High-risk tasks that need attention
      3. Optimization opportunities
      4. Recommendations to reduce overall project risk
      
      Return clear, actionable recommendations.
    `;

    try {
      const response = await this.callOpenAI(prompt);
      return response;
    } catch (error) {
      console.error('Error generating insights:', error);
      return 'Unable to generate insights at this time.';
    }
  }

  /**
   * Main workflow to create complete project plan
   */
  async createProjectPlan(
    projectDescription: string,
    teamMembers: TeamMember[],
    maxIterations: number = 3
  ): Promise<ProjectPlan> {
    const state: AgentState = {
      project_description: projectDescription,
      team_members: teamMembers,
      tasks: [],
      dependencies: [],
      schedule: [],
      allocations: [],
      risks: [],
      iteration_number: 0,
      max_iterations: maxIterations,
      insights: [],
      project_plans: [],
      overall_risk_scores: []
    };

    // Step 1: Generate tasks
    console.log('🎯 Generating tasks...');
    state.tasks = await this.generateTasks(projectDescription);

    // Step 2: Analyze dependencies
    console.log('🔗 Analyzing dependencies...');
    state.dependencies = await this.analyzeDependencies(state.tasks);

    // Iterative improvement loop
    for (let i = 0; i < maxIterations; i++) {
      state.iteration_number = i + 1;
      console.log(`🔄 Iteration ${state.iteration_number}/${maxIterations}`);

      // Step 3: Create schedule
      console.log('📅 Creating schedule...');
      state.schedule = await this.createSchedule(state.tasks, state.dependencies, state.insights);

      // Step 4: Allocate tasks
      console.log('👥 Allocating tasks...');
      state.allocations = await this.allocateTasks(
        state.tasks, 
        state.schedule, 
        state.team_members, 
        state.insights
      );

      // Step 5: Assess risks
      console.log('⚠️ Assessing risks...');
      state.risks = await this.assessRisks(state.tasks, state.schedule, state.allocations);

      // Calculate overall risk score
      const overallRiskScore = state.risks.reduce((sum, risk) => sum + risk.risk_score, 0);
      state.overall_risk_scores.push(overallRiskScore);

      // Create project plan for this iteration
      const projectPlan: ProjectPlan = {
        tasks: state.tasks,
        dependencies: state.dependencies,
        schedule: state.schedule,
        allocations: state.allocations,
        risks: state.risks,
        total_duration_days: this.calculateProjectDuration(state.schedule),
        overall_risk_score: overallRiskScore
      };
      state.project_plans.push(projectPlan);

      // Check if we should continue iterating
      if (i > 0 && state.overall_risk_scores[i] < state.overall_risk_scores[0]) {
        console.log('✅ Risk reduced, stopping iterations');
        break;
      }

      // Generate insights for next iteration
      if (i < maxIterations - 1) {
        console.log('💡 Generating insights...');
        const insight = await this.generateInsights(
          state.tasks, 
          state.schedule, 
          state.allocations, 
          state.risks
        );
        state.insights.push(insight);
      }
    }

    // Return the best project plan (lowest risk score)
    const bestPlan = state.project_plans.reduce((best, current) => 
      current.overall_risk_score < best.overall_risk_score ? current : best
    );

    console.log('🎉 Project plan created successfully!');
    return bestPlan;
  }

  /**
   * Save project plan to database
   */
  async saveProjectPlan(projectPlan: ProjectPlan, projectId: string): Promise<boolean> {
    try {
      // Save to projects table
      const { error: projectError } = await supabase
        .from('projects')
        .update({
          tasks: projectPlan.tasks,
          schedule: projectPlan.schedule,
          allocations: projectPlan.allocations,
          risks: projectPlan.risks,
          total_duration_days: projectPlan.total_duration_days,
          overall_risk_score: projectPlan.overall_risk_score,
          updated_at: new Date().toISOString()
        })
        .eq('id', projectId);

      if (projectError) throw projectError;

      // Log the AI interaction
      await supabase.from('ai_interactions').insert({
        user_id: (await supabase.auth.getUser()).data.user?.id,
        interaction_type: 'project_planning',
        input_data: { project_id: projectId },
        output_data: projectPlan,
        metadata: {
          agent_type: 'project_manager',
          iterations: projectPlan.risks.length,
          risk_score: projectPlan.overall_risk_score
        }
      });

      return true;
    } catch (error) {
      console.error('Error saving project plan:', error);
      return false;
    }
  }

  // Helper methods
  private async callOpenAI(prompt: string): Promise<string> {
    if (!this.useLangChain || !this.openaiApiKey) {
      throw new Error('OpenAI API not configured');
    }

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.7,
        max_tokens: 2000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || '';
  }

  private parseTasksResponse(response: string): Task[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return this.generateFallbackTasks('');
    }
  }

  private parseDependenciesResponse(response: string): TaskDependency[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }

  private parseScheduleResponse(response: string): TaskSchedule[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed.map(item => ({
        ...item,
        start_date: new Date(item.start_date),
        end_date: new Date(item.end_date)
      })) : [];
    } catch {
      return [];
    }
  }

  private parseAllocationResponse(response: string): TaskAllocation[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed.map(item => ({
        ...item,
        start_date: new Date(item.start_date),
        end_date: new Date(item.end_date)
      })) : [];
    } catch {
      return [];
    }
  }

  private parseRiskResponse(response: string): Risk[] {
    try {
      const parsed = JSON.parse(response);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  }

  private generateFallbackTasks(description: string): Task[] {
    return [
      {
        id: '1',
        task_name: 'Project Planning',
        task_description: 'Initial project planning and requirements gathering',
        estimated_days: 3,
        priority: 'high',
        status: 'not_started'
      },
      {
        id: '2',
        task_name: 'Implementation',
        task_description: 'Core implementation work',
        estimated_days: 5,
        priority: 'high',
        status: 'not_started'
      },
      {
        id: '3',
        task_name: 'Testing',
        task_description: 'Testing and quality assurance',
        estimated_days: 2,
        priority: 'medium',
        status: 'not_started'
      }
    ];
  }

  private generateFallbackSchedule(tasks: Task[]): TaskSchedule[] {
    const startDate = new Date();
    return tasks.map((task, index) => ({
      task_id: task.id,
      start_date: new Date(startDate.getTime() + (index * task.estimated_days * 24 * 60 * 60 * 1000)),
      end_date: new Date(startDate.getTime() + ((index + 1) * task.estimated_days * 24 * 60 * 60 * 1000)),
      duration_days: task.estimated_days
    }));
  }

  private calculateProjectDuration(schedule: TaskSchedule[]): number {
    if (schedule.length === 0) return 0;

    const startDate = Math.min(...schedule.map(s => s.start_date.getTime()));
    const endDate = Math.max(...schedule.map(s => s.end_date.getTime()));

    return Math.ceil((endDate - startDate) / (24 * 60 * 60 * 1000));
  }

  /**
   * Generate a comprehensive project plan with database integration
   */
  async generateProjectPlan(description: string, teamMembers: TeamMember[]): Promise<ProjectPlan | null> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const initialState: AgentState = {
        project_description: description,
        team_members: teamMembers,
        tasks: [],
        dependencies: [],
        schedule: [],
        allocations: [],
        risks: [],
        iteration_number: 0,
        max_iterations: 3,
        insights: [],
        project_plans: [],
        overall_risk_scores: []
      };

      const finalState = await this.runWorkflow(initialState);

      if (finalState.project_plans.length > 0) {
        const projectPlan = finalState.project_plans[finalState.project_plans.length - 1];

        // Log the project plan generation
        await this.logProjectPlanGeneration(description, projectPlan);

        return projectPlan;
      }

      return null;
    } catch (error) {
      console.error('Error generating project plan:', error);
      await this.logError('generateProjectPlan', error);
      return null;
    }
  }

  /**
   * Save project plan to database and create actual project
   */
  async saveProjectToDatabase(projectPlan: ProjectPlan, projectName: string, userId: string): Promise<string | null> {
    try {
      // Create the main project
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .insert({
          name: projectName,
          description: projectPlan.project_description,
          status: 'planning',
          priority: this.calculateProjectPriority(projectPlan),
          start_date: projectPlan.schedule[0]?.start_date || new Date().toISOString().split('T')[0],
          end_date: this.calculateEndDate(projectPlan.schedule),
          created_by: userId,
          manager_id: userId
        })
        .select()
        .single();

      if (projectError) throw projectError;

      const projectId = project.id;

      // Create tasks
      for (const task of projectPlan.tasks) {
        const { error: taskError } = await supabase
          .from('tasks')
          .insert({
            project_id: projectId,
            title: task.task_name,
            description: task.task_description,
            status: 'pending',
            priority: task.priority,
            estimated_hours: task.estimated_days * 8, // Convert days to hours
            created_by_id: userId
          });

        if (taskError) {
          console.error('Error creating task:', taskError);
        }
      }

      // Create project assignments
      for (const allocation of projectPlan.allocations) {
        const teamMember = projectPlan.team_members.find(tm => tm.id === allocation.team_member_id);
        if (teamMember) {
          const { error: assignmentError } = await supabase
            .from('project_assignments')
            .insert({
              project_id: projectId,
              assigned_to: allocation.team_member_id,
              role: teamMember.role,
              hours_allocated: allocation.hours_allocated,
              status: 'active'
            });

          if (assignmentError) {
            console.error('Error creating assignment:', assignmentError);
          }
        }
      }

      return projectId;
    } catch (error) {
      console.error('Error saving project to database:', error);
      await this.logError('saveProjectToDatabase', error);
      return null;
    }
  }

  /**
   * Automated task assignment based on team member skills and availability
   */
  async autoAssignTasks(projectId: string): Promise<boolean> {
    try {
      // Get project tasks
      const { data: tasks, error: tasksError } = await supabase
        .from('tasks')
        .select('*')
        .eq('project_id', projectId)
        .is('assigned_to_id', null);

      if (tasksError) throw tasksError;

      // Get team members
      const { data: assignments, error: assignmentsError } = await supabase
        .from('project_assignments')
        .select('*, profiles(*)')
        .eq('project_id', projectId);

      if (assignmentsError) throw assignmentsError;

      // Auto-assign tasks based on skills and workload
      for (const task of tasks || []) {
        const bestAssignee = this.findBestAssignee(task, assignments || []);

        if (bestAssignee) {
          const { error: updateError } = await supabase
            .from('tasks')
            .update({ assigned_to_id: bestAssignee.assigned_to })
            .eq('id', task.id);

          if (updateError) {
            console.error('Error assigning task:', updateError);
          }
        }
      }

      return true;
    } catch (error) {
      console.error('Error auto-assigning tasks:', error);
      await this.logError('autoAssignTasks', error);
      return false;
    }
  }

  /**
   * Generate progress reports
   */
  async generateProgressReport(projectId: string): Promise<any> {
    try {
      // Get project data
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*, tasks(*), project_assignments(*)')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      // Calculate progress metrics
      const totalTasks = project.tasks?.length || 0;
      const completedTasks = project.tasks?.filter((t: any) => t.status === 'completed').length || 0;
      const inProgressTasks = project.tasks?.filter((t: any) => t.status === 'in_progress').length || 0;
      const blockedTasks = project.tasks?.filter((t: any) => t.status === 'blocked').length || 0;

      const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      const report = {
        projectId,
        projectName: project.name,
        status: project.status,
        progressPercentage: Math.round(progressPercentage),
        totalTasks,
        completedTasks,
        inProgressTasks,
        blockedTasks,
        teamMembers: project.project_assignments?.length || 0,
        startDate: project.start_date,
        endDate: project.end_date,
        generatedAt: new Date().toISOString()
      };

      // Save report to database
      await supabase.from('reports').insert({
        title: `Progress Report - ${project.name}`,
        report_type: 'project',
        project_id: projectId,
        data: report,
        generated_by: project.manager_id
      });

      return report;
    } catch (error) {
      console.error('Error generating progress report:', error);
      await this.logError('generateProgressReport', error);
      return null;
    }
  }

  // Helper methods
  private calculateProjectPriority(projectPlan: ProjectPlan): string {
    const highPriorityTasks = projectPlan.tasks.filter(t => t.priority === 'high' || t.priority === 'critical').length;
    const totalTasks = projectPlan.tasks.length;

    if (highPriorityTasks / totalTasks > 0.5) return 'high';
    if (highPriorityTasks / totalTasks > 0.3) return 'medium';
    return 'low';
  }

  private calculateEndDate(schedule: TaskSchedule[]): string {
    if (schedule.length === 0) {
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      return futureDate.toISOString().split('T')[0];
    }

    const latestEndDate = Math.max(...schedule.map(s => s.end_date.getTime()));
    return new Date(latestEndDate).toISOString().split('T')[0];
  }

  private findBestAssignee(task: any, assignments: any[]): any {
    // Simple assignment logic - can be enhanced with ML
    const availableAssignees = assignments.filter(a => a.status === 'active');

    if (availableAssignees.length === 0) return null;

    // For now, assign to the person with the least current workload
    return availableAssignees.reduce((best, current) => {
      return (current.hours_worked || 0) < (best.hours_worked || 0) ? current : best;
    });
  }

  private async logProjectPlanGeneration(description: string, projectPlan: ProjectPlan): Promise<void> {
    try {
      await supabase.from('ai_interactions').insert({
        interaction_type: 'project_planning',
        input_text: description,
        output_text: JSON.stringify(projectPlan),
        model_used: 'project_manager_agent',
        status: 'completed',
        metadata: {
          tasks_count: projectPlan.tasks.length,
          team_members_count: projectPlan.team_members.length,
          estimated_duration: this.calculateProjectDuration(projectPlan.schedule)
        }
      });
    } catch (error) {
      console.error('Error logging project plan generation:', error);
    }
  }

  private async logError(operation: string, error: any): Promise<void> {
    try {
      await supabase.from('ai_interactions').insert({
        interaction_type: 'project_manager_error',
        input_text: operation,
        output_text: error.message || 'Unknown error',
        model_used: 'project_manager_agent',
        status: 'failed',
        metadata: { error: error.toString() }
      });
    } catch (logError) {
      console.error('Error logging error:', logError);
    }
  }
}
