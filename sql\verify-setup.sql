-- ============================================================================
-- SYSTEM VERIFICATION SCRIPT
-- Run this to check if your database setup is complete and working
-- ============================================================================

-- Check if all required tables exist
CREATE OR REPLACE FUNCTION check_required_tables()
RETURNS TABLE(
    table_name TEXT,
    exists BOOLEAN,
    status TEXT
) AS $$
DECLARE
    required_tables TEXT[] := ARRAY[
        'departments',
        'profiles', 
        'memos',
        'battery_types',
        'battery_locations',
        'batteries',
        'battery_readings',
        'battery_maintenance',
        'battery_transfers',
        'projects',
        'tasks',
        'notifications',
        'system_activities'
    ];
    tbl TEXT;
    table_exists BOOLEAN;
BEGIN
    FOREACH tbl IN ARRAY required_tables
    LOOP
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = tbl
        ) INTO table_exists;
        
        RETURN QUERY SELECT 
            tbl,
            table_exists,
            CASE 
                WHEN table_exists THEN '✅ Exists'
                ELSE '❌ Missing'
            END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Check foreign key relationships
CREATE OR REPLACE FUNCTION check_foreign_keys()
RETURNS TABLE(
    constraint_name TEXT,
    table_name TEXT,
    column_name TEXT,
    foreign_table TEXT,
    foreign_column TEXT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tc.constraint_name::TEXT,
        tc.table_name::TEXT,
        kcu.column_name::TEXT,
        ccu.table_name::TEXT AS foreign_table,
        ccu.column_name::TEXT AS foreign_column,
        '✅ Active'::TEXT AS status
    FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public'
    AND tc.table_name IN (
        'profiles', 'memos', 'batteries', 'battery_readings', 
        'battery_maintenance', 'battery_transfers', 'projects', 'tasks'
    )
    ORDER BY tc.table_name, tc.constraint_name;
END;
$$ LANGUAGE plpgsql;

-- Check data availability
CREATE OR REPLACE FUNCTION check_sample_data()
RETURNS TABLE(
    table_name TEXT,
    row_count BIGINT,
    status TEXT,
    recommendation TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'departments'::TEXT,
        (SELECT COUNT(*) FROM public.departments),
        CASE 
            WHEN (SELECT COUNT(*) FROM public.departments) >= 3 THEN '✅ Good'
            WHEN (SELECT COUNT(*) FROM public.departments) > 0 THEN '⚠️ Limited'
            ELSE '❌ Empty'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM public.departments) = 0 THEN 'Run sample data insert'
            ELSE 'Ready for use'
        END
    UNION ALL
    SELECT 
        'battery_types'::TEXT,
        (SELECT COUNT(*) FROM public.battery_types),
        CASE 
            WHEN (SELECT COUNT(*) FROM public.battery_types) >= 3 THEN '✅ Good'
            WHEN (SELECT COUNT(*) FROM public.battery_types) > 0 THEN '⚠️ Limited'
            ELSE '❌ Empty'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM public.battery_types) = 0 THEN 'Add battery types via UI or SQL'
            ELSE 'Ready for battery creation'
        END
    UNION ALL
    SELECT 
        'battery_locations'::TEXT,
        (SELECT COUNT(*) FROM public.battery_locations),
        CASE 
            WHEN (SELECT COUNT(*) FROM public.battery_locations) >= 3 THEN '✅ Good'
            WHEN (SELECT COUNT(*) FROM public.battery_locations) > 0 THEN '⚠️ Limited'
            ELSE '❌ Empty'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM public.battery_locations) = 0 THEN 'Add locations via UI or SQL'
            ELSE 'Ready for battery placement'
        END
    UNION ALL
    SELECT 
        'profiles'::TEXT,
        (SELECT COUNT(*) FROM public.profiles),
        CASE 
            WHEN (SELECT COUNT(*) FROM public.profiles) > 0 THEN '✅ Has Users'
            ELSE '⚠️ No Users'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM public.profiles) = 0 THEN 'Users will be created on first login'
            ELSE 'User system active'
        END
    UNION ALL
    SELECT 
        'batteries'::TEXT,
        (SELECT COUNT(*) FROM public.batteries),
        CASE 
            WHEN (SELECT COUNT(*) FROM public.batteries) > 0 THEN '✅ Has Inventory'
            ELSE '⚠️ No Inventory'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM public.batteries) = 0 THEN 'Create batteries via Battery Management UI'
            ELSE 'Inventory system active'
        END;
END;
$$ LANGUAGE plpgsql;

-- Check RLS policies
CREATE OR REPLACE FUNCTION check_rls_policies()
RETURNS TABLE(
    table_name TEXT,
    rls_enabled BOOLEAN,
    policy_count BIGINT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        t.rowsecurity,
        (SELECT COUNT(*) FROM pg_policies p WHERE p.tablename = t.tablename),
        CASE 
            WHEN t.rowsecurity AND (SELECT COUNT(*) FROM pg_policies p WHERE p.tablename = t.tablename) > 0 
            THEN '✅ Secured'
            WHEN t.rowsecurity 
            THEN '⚠️ RLS On, No Policies'
            ELSE '❌ No RLS'
        END
    FROM pg_tables t
    WHERE t.schemaname = 'public'
    AND t.tablename IN ('profiles', 'memos', 'batteries', 'battery_readings', 'departments')
    ORDER BY t.tablename;
END;
$$ LANGUAGE plpgsql;

-- Check indexes for performance
CREATE OR REPLACE FUNCTION check_indexes()
RETURNS TABLE(
    table_name TEXT,
    index_name TEXT,
    column_names TEXT,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.tablename::TEXT,
        i.indexname::TEXT,
        array_to_string(array_agg(a.attname ORDER BY a.attnum), ', ')::TEXT,
        '✅ Active'::TEXT
    FROM pg_indexes i
    JOIN pg_class c ON c.relname = i.indexname
    JOIN pg_attribute a ON a.attrelid = c.oid
    JOIN pg_tables t ON t.tablename = i.tablename
    WHERE t.schemaname = 'public'
    AND i.indexname NOT LIKE '%_pkey'
    AND t.tablename IN ('profiles', 'memos', 'batteries', 'battery_readings')
    GROUP BY t.tablename, i.indexname
    ORDER BY t.tablename, i.indexname;
END;
$$ LANGUAGE plpgsql;

-- Main verification function
CREATE OR REPLACE FUNCTION verify_complete_setup()
RETURNS TEXT AS $$
DECLARE
    missing_tables INTEGER;
    empty_core_tables INTEGER;
    rls_issues INTEGER;
    result TEXT := '';
BEGIN
    -- Check missing tables
    SELECT COUNT(*) INTO missing_tables 
    FROM check_required_tables() 
    WHERE NOT exists;
    
    -- Check empty core tables
    SELECT COUNT(*) INTO empty_core_tables
    FROM check_sample_data()
    WHERE table_name IN ('departments', 'battery_types', 'battery_locations')
    AND status = '❌ Empty';
    
    -- Check RLS issues
    SELECT COUNT(*) INTO rls_issues
    FROM check_rls_policies()
    WHERE status != '✅ Secured';
    
    -- Build result
    result := E'🔍 SYSTEM VERIFICATION REPORT\n';
    result := result || E'================================\n\n';
    
    IF missing_tables = 0 THEN
        result := result || E'✅ All required tables exist\n';
    ELSE
        result := result || E'❌ Missing ' || missing_tables || E' required tables\n';
    END IF;
    
    IF empty_core_tables = 0 THEN
        result := result || E'✅ Core tables have sample data\n';
    ELSE
        result := result || E'⚠️  ' || empty_core_tables || E' core tables need sample data\n';
    END IF;
    
    IF rls_issues = 0 THEN
        result := result || E'✅ Row Level Security properly configured\n';
    ELSE
        result := result || E'⚠️  ' || rls_issues || E' tables have RLS issues\n';
    END IF;
    
    result := result || E'\n📊 DETAILED REPORTS:\n';
    result := result || E'- Tables: SELECT * FROM check_required_tables();\n';
    result := result || E'- Data: SELECT * FROM check_sample_data();\n';
    result := result || E'- Security: SELECT * FROM check_rls_policies();\n';
    result := result || E'- Foreign Keys: SELECT * FROM check_foreign_keys();\n';
    result := result || E'- Indexes: SELECT * FROM check_indexes();\n\n';
    
    IF missing_tables = 0 AND empty_core_tables = 0 THEN
        result := result || E'🎉 SYSTEM READY FOR USE!\n';
        result := result || E'Access your application at: http://localhost:8083/\n';
        result := result || E'Battery Management: http://localhost:8083/dashboard/battery\n';
    ELSE
        result := result || E'🔧 SETUP INCOMPLETE - Run setup scripts first\n';
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Quick status check
CREATE OR REPLACE FUNCTION quick_status()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    action_needed TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'Database Tables'::TEXT,
        CASE 
            WHEN (SELECT COUNT(*) FROM check_required_tables() WHERE NOT exists) = 0 
            THEN '✅ Complete'
            ELSE '❌ Incomplete'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM check_required_tables() WHERE NOT exists) = 0 
            THEN 'None'
            ELSE 'Run setup SQL scripts'
        END
    UNION ALL
    SELECT 
        'Sample Data'::TEXT,
        CASE 
            WHEN (SELECT COUNT(*) FROM check_sample_data() WHERE status = '❌ Empty' AND table_name IN ('departments', 'battery_types', 'battery_locations')) = 0
            THEN '✅ Ready'
            ELSE '⚠️ Limited'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM check_sample_data() WHERE status = '❌ Empty' AND table_name IN ('departments', 'battery_types', 'battery_locations')) = 0
            THEN 'None'
            ELSE 'Add sample data'
        END
    UNION ALL
    SELECT 
        'Security (RLS)'::TEXT,
        CASE 
            WHEN (SELECT COUNT(*) FROM check_rls_policies() WHERE status != '✅ Secured') = 0
            THEN '✅ Secured'
            ELSE '⚠️ Needs Review'
        END,
        CASE 
            WHEN (SELECT COUNT(*) FROM check_rls_policies() WHERE status != '✅ Secured') = 0
            THEN 'None'
            ELSE 'Review RLS policies'
        END;
END;
$$ LANGUAGE plpgsql;

-- Display instructions
DO $$
BEGIN
    RAISE NOTICE '🔍 DATABASE VERIFICATION TOOLS READY';
    RAISE NOTICE '';
    RAISE NOTICE 'Quick Check:';
    RAISE NOTICE '  SELECT * FROM quick_status();';
    RAISE NOTICE '';
    RAISE NOTICE 'Full Report:';
    RAISE NOTICE '  SELECT verify_complete_setup();';
    RAISE NOTICE '';
    RAISE NOTICE 'Detailed Checks:';
    RAISE NOTICE '  SELECT * FROM check_required_tables();';
    RAISE NOTICE '  SELECT * FROM check_sample_data();';
    RAISE NOTICE '  SELECT * FROM check_rls_policies();';
    RAISE NOTICE '  SELECT * FROM check_foreign_keys();';
END $$;
