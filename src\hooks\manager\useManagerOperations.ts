
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface Project {
  id: string;
  name: string;
  description: string;
  status: string;
  department_id?: string;
  budget?: number;
  client_name?: string;
  created_at?: string;
  created_by?: string;
  end_date?: string;
  start_date?: string;
  location?: string;
  updated_at?: string;
}

interface UserAssignment {
  id: string;
  assigned_to_id: string;
  assignment_type: string;
  user_id: string;
  created_at: string | null;
  updated_at: string | null;
  profiles?: {
    full_name: string;
  };
}

export const useManagerOperations = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const { data: projects, isLoading: projectsLoading } = useQuery({
    queryKey: ["manager-projects"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("projects")
        .select("*");
      
      if (error) {
        console.error("Error fetching projects:", error);
        toast({
          title: "Error",
          description: "Failed to load projects",
          variant: "destructive",
        });
        return [] as Project[];
      }
      return data as Project[];
    },
  });

  const { data: assignments, isLoading: assignmentsLoading } = useQuery({
    queryKey: ["user-assignments"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("user_assignments")
        .select(`
          id,
          assigned_to_id,
          assignment_type,
          user_id,
          created_at,
          updated_at,
          profiles!user_assignments_assigned_to_id_fkey(full_name)
        `);
      
      if (error) {
        console.error("Error fetching assignments:", error);
        toast({
          title: "Error",
          description: "Failed to load assignments",
          variant: "destructive",
        });
        return [] as UserAssignment[];
      }
      return data as UserAssignment[];
    },
  });

  const updateProject = useMutation({
    mutationFn: async (updatedProject: Project) => {
      const { data, error } = await supabase
        .from("projects")
        .update(updatedProject)
        .eq("id", updatedProject.id)
        .select();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["manager-projects"] });
      toast({
        title: "Success",
        description: "Project updated successfully",
      });
    },
    onError: (error) => {
      console.error("Error updating project:", error);
      toast({
        title: "Error",
        description: "Failed to update project",
        variant: "destructive",
      });
    },
  });

  return {
    projects,
    projectsLoading,
    assignments,
    assignmentsLoading,
    updateProject,
  };
};
