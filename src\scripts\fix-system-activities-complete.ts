import { supabase } from '@/integrations/supabase/client';

/**
 * Complete fix for system_activities table and RLS policies
 * This script will:
 * 1. Drop and recreate the table with correct schema
 * 2. Set up proper RLS policies for all roles
 * 3. Create necessary functions and triggers
 * 4. Test the setup
 */

const dropExistingTable = async () => {
  console.log('🗑️ Dropping existing system_activities table...');
  
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      -- Drop existing policies first
      DROP POLICY IF EXISTS "Admin can view all activities" ON system_activities;
      DROP POLICY IF EXISTS "Users can view own activities" ON system_activities;
      DROP POLICY IF EXISTS "Authenticated users can insert activities" ON system_activities;
      DROP POLICY IF EXISTS "Admin can update activities" ON system_activities;
      DROP POLICY IF EXISTS "Admin can delete activities" ON system_activities;
      DROP POLICY IF EXISTS "System can insert activities" ON system_activities;
      DROP POLICY IF EXISTS "Service role can manage activities" ON system_activities;
      
      -- Drop table
      DROP TABLE IF EXISTS system_activities CASCADE;
      
      -- Drop functions
      DROP FUNCTION IF EXISTS log_system_activity CASCADE;
      DROP FUNCTION IF EXISTS get_activity_statistics CASCADE;
      DROP FUNCTION IF EXISTS cleanup_old_activities CASCADE;
    `
  });

  if (error) {
    console.error('Error dropping table:', error);
    return false;
  }

  console.log('✅ Existing table dropped successfully');
  return true;
};

const createSystemActivitiesTable = async () => {
  console.log('📋 Creating system_activities table with correct schema...');

  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      -- Create system_activities table with standardized schema
      CREATE TABLE system_activities (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
        type VARCHAR(100) NOT NULL, -- Using 'type' for consistency with error message
        description TEXT NOT NULL,
        metadata JSONB DEFAULT '{}',
        ip_address INET,
        user_agent TEXT,
        session_id VARCHAR(255),
        severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical', 'success')),
        category VARCHAR(50) DEFAULT 'general' CHECK (category IN ('general', 'auth', 'project', 'user', 'system', 'database', 'api', 'security')),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );

      -- Create indexes for performance
      CREATE INDEX idx_system_activities_user_id ON system_activities(user_id);
      CREATE INDEX idx_system_activities_type ON system_activities(type);
      CREATE INDEX idx_system_activities_created_at ON system_activities(created_at DESC);
      CREATE INDEX idx_system_activities_user_date ON system_activities(user_id, created_at DESC);
      CREATE INDEX idx_system_activities_type_date ON system_activities(type, created_at DESC);
      CREATE INDEX idx_system_activities_category_date ON system_activities(category, created_at DESC);

      -- Create trigger for updated_at
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';

      CREATE TRIGGER update_system_activities_updated_at 
        BEFORE UPDATE ON system_activities 
        FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    `
  });

  if (error) {
    console.error('Error creating table:', error);
    return false;
  }

  console.log('✅ Table created successfully');
  return true;
};

const setupRLSPolicies = async () => {
  console.log('🔒 Setting up RLS policies...');

  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      -- Enable RLS
      ALTER TABLE system_activities ENABLE ROW LEVEL SECURITY;

      -- Policy 1: Admin can view all activities
      CREATE POLICY "Admin can view all activities" ON system_activities
        FOR SELECT USING (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
          )
        );

      -- Policy 2: Users can view their own activities
      CREATE POLICY "Users can view own activities" ON system_activities
        FOR SELECT USING (user_id = auth.uid());

      -- Policy 3: Authenticated users can insert activities (with user_id check)
      CREATE POLICY "Authenticated users can insert activities" ON system_activities
        FOR INSERT WITH CHECK (
          auth.uid() IS NOT NULL AND 
          (user_id = auth.uid() OR user_id IS NULL)
        );

      -- Policy 4: Service role can insert any activities (for system operations)
      CREATE POLICY "Service role can insert activities" ON system_activities
        FOR INSERT WITH CHECK (
          auth.jwt() ->> 'role' = 'service_role' OR
          auth.uid() IS NOT NULL
        );

      -- Policy 5: Admin can update activities
      CREATE POLICY "Admin can update activities" ON system_activities
        FOR UPDATE USING (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
          )
        );

      -- Policy 6: Admin can delete activities
      CREATE POLICY "Admin can delete activities" ON system_activities
        FOR DELETE USING (
          EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
          )
        );
    `
  });

  if (error) {
    console.error('Error setting up RLS policies:', error);
    return false;
  }

  console.log('✅ RLS policies created successfully');
  return true;
};

const createHelperFunctions = async () => {
  console.log('⚙️ Creating helper functions...');

  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      -- Function to log system activities
      CREATE OR REPLACE FUNCTION log_system_activity(
        p_user_id UUID DEFAULT NULL,
        p_type VARCHAR(100) DEFAULT 'system_action',
        p_description TEXT DEFAULT 'System activity',
        p_metadata JSONB DEFAULT '{}',
        p_severity VARCHAR(20) DEFAULT 'info',
        p_category VARCHAR(50) DEFAULT 'general'
      )
      RETURNS UUID AS $$
      DECLARE
        activity_id UUID;
      BEGIN
        INSERT INTO system_activities (
          user_id, type, description, metadata, severity, category
        ) VALUES (
          p_user_id, p_type, p_description, p_metadata, p_severity, p_category
        ) RETURNING id INTO activity_id;
        
        RETURN activity_id;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Function to get activity statistics
      CREATE OR REPLACE FUNCTION get_activity_statistics(
        p_days INTEGER DEFAULT 30
      )
      RETURNS TABLE (
        activity_date DATE,
        activity_type VARCHAR(100),
        category VARCHAR(50),
        severity VARCHAR(20),
        count BIGINT,
        unique_users BIGINT
      ) AS $$
      BEGIN
        RETURN QUERY
        SELECT 
          DATE(sa.created_at) as activity_date,
          sa.type as activity_type,
          sa.category,
          sa.severity,
          COUNT(*) as count,
          COUNT(DISTINCT sa.user_id) as unique_users
        FROM system_activities sa
        WHERE sa.created_at >= NOW() - INTERVAL '1 day' * p_days
        GROUP BY DATE(sa.created_at), sa.type, sa.category, sa.severity
        ORDER BY activity_date DESC, count DESC;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;

      -- Function to cleanup old activities
      CREATE OR REPLACE FUNCTION cleanup_old_activities(
        p_days INTEGER DEFAULT 90
      )
      RETURNS INTEGER AS $$
      DECLARE
        deleted_count INTEGER;
      BEGIN
        DELETE FROM system_activities 
        WHERE created_at < NOW() - INTERVAL '1 day' * p_days;
        
        GET DIAGNOSTICS deleted_count = ROW_COUNT;
        RETURN deleted_count;
      END;
      $$ LANGUAGE plpgsql SECURITY DEFINER;
    `
  });

  if (error) {
    console.error('Error creating helper functions:', error);
    return false;
  }

  console.log('✅ Helper functions created successfully');
  return true;
};

const grantPermissions = async () => {
  console.log('🔑 Granting permissions...');

  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      -- Grant permissions to authenticated users
      GRANT SELECT, INSERT ON system_activities TO authenticated;
      GRANT EXECUTE ON FUNCTION log_system_activity TO authenticated;
      GRANT EXECUTE ON FUNCTION get_activity_statistics TO authenticated;

      -- Grant admin permissions to service role
      GRANT ALL ON system_activities TO service_role;
      GRANT EXECUTE ON FUNCTION cleanup_old_activities TO service_role;
    `
  });

  if (error) {
    console.error('Error granting permissions:', error);
    return false;
  }

  console.log('✅ Permissions granted successfully');
  return true;
};

const testSystemActivities = async () => {
  console.log('🧪 Testing system activities...');

  try {
    // Test 1: Insert a test activity
    const { data: insertData, error: insertError } = await supabase
      .from('system_activities')
      .insert({
        type: 'test_activity',
        description: 'Test activity for system validation',
        metadata: { test: true, timestamp: new Date().toISOString() },
        severity: 'info',
        category: 'system'
      })
      .select()
      .single();

    if (insertError) {
      console.error('❌ Insert test failed:', insertError);
      return false;
    }

    console.log('✅ Insert test passed:', insertData.id);

    // Test 2: Query activities
    const { data: queryData, error: queryError } = await supabase
      .from('system_activities')
      .select(`
        *,
        profiles:user_id(full_name, email)
      `)
      .limit(5);

    if (queryError) {
      console.error('❌ Query test failed:', queryError);
      return false;
    }

    console.log('✅ Query test passed, found', queryData?.length, 'activities');

    // Test 3: Use helper function
    const { data: functionData, error: functionError } = await supabase
      .rpc('log_system_activity', {
        p_type: 'function_test',
        p_description: 'Testing log_system_activity function',
        p_metadata: { function_test: true },
        p_severity: 'success',
        p_category: 'system'
      });

    if (functionError) {
      console.error('❌ Function test failed:', functionError);
      return false;
    }

    console.log('✅ Function test passed, activity ID:', functionData);

    return true;
  } catch (error) {
    console.error('❌ Test failed with exception:', error);
    return false;
  }
};

export const fixSystemActivitiesComplete = async () => {
  console.log('🚀 Starting complete system activities fix...');

  try {
    // Step 1: Drop existing table
    if (!(await dropExistingTable())) {
      throw new Error('Failed to drop existing table');
    }

    // Step 2: Create new table
    if (!(await createSystemActivitiesTable())) {
      throw new Error('Failed to create table');
    }

    // Step 3: Setup RLS policies
    if (!(await setupRLSPolicies())) {
      throw new Error('Failed to setup RLS policies');
    }

    // Step 4: Create helper functions
    if (!(await createHelperFunctions())) {
      throw new Error('Failed to create helper functions');
    }

    // Step 5: Grant permissions
    if (!(await grantPermissions())) {
      throw new Error('Failed to grant permissions');
    }

    // Step 6: Test the setup
    if (!(await testSystemActivities())) {
      throw new Error('Failed system activities tests');
    }

    console.log('🎉 System activities setup completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ System activities fix failed:', error);
    return false;
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  fixSystemActivitiesComplete();
}
