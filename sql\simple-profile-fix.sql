-- ============================================================================
-- SIMPLE PROFILE FIX - CORRECTED SYNTAX
-- Fixes the email constraint and RLS issues
-- User: 44349058-db4b-4a0b-8c99-8a913d07df74 (<EMAIL>)
-- ============================================================================

-- Step 1: Disable RLS to access existing data
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- Step 2: Check what profiles exist with this email
SELECT 'Existing profiles with this email:' as info, id, email, full_name, role, status 
FROM public.profiles 
WHERE email = '<EMAIL>';

-- Step 3: Delete any profiles with this email that don't match the current user ID
DELETE FROM public.profiles 
WHERE email = '<EMAIL>' 
  AND id != '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Step 4: Create or update the correct profile (only one ON CONFLICT allowed)
INSERT INTO public.profiles (id, full_name, email, role, status, created_at, updated_at)
VALUES (
    '44349058-db4b-4a0b-8c99-8a913d07df74'::UUID,
    'CTNL User',
    '<EMAIL>',
    'staff',
    'active',
    NOW(),
    NOW()
)
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = EXCLUDED.full_name,
    role = EXCLUDED.role,
    status = EXCLUDED.status,
    updated_at = NOW();

-- Step 5: Drop existing policies one by one (safer than using loops)
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
DROP POLICY IF EXISTS "profiles_service_role" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_authenticated" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_all" ON public.profiles;
DROP POLICY IF EXISTS "profiles_delete_own" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_profile_update" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_select" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_insert" ON public.profiles;
DROP POLICY IF EXISTS "allow_own_update" ON public.profiles;
DROP POLICY IF EXISTS "profiles_select_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_insert_policy" ON public.profiles;
DROP POLICY IF EXISTS "profiles_update_policy" ON public.profiles;

-- Step 6: Re-enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Step 7: Create simple, working policies
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Step 8: Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.profiles TO authenticated;
GRANT ALL ON public.profiles TO service_role;

-- Step 9: Verify the fix
SELECT 'Profile verification:' as info, id, email, full_name, role, status, created_at
FROM public.profiles 
WHERE id = '44349058-db4b-4a0b-8c99-8a913d07df74';

-- Step 10: Check RLS status
SELECT 'RLS Status:' as info, 
       schemaname, 
       tablename, 
       rowsecurity
FROM pg_tables 
WHERE tablename = 'profiles' AND schemaname = 'public';

-- Step 11: List active policies
SELECT 'Active policies:' as info,
       policyname, 
       cmd as operation
FROM pg_policies 
WHERE tablename = 'profiles' AND schemaname = 'public'
ORDER BY policyname;
