import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTimeTracking } from '@/hooks/useTimeTracking';
import { cn } from '@/lib/utils';
import { deviceService } from '@/services/deviceService';
import { locationService } from '@/services/locationService';
import { DeviceInfo, LocationData, TimeCardProps } from '@/types/timeTracking';
import { format } from 'date-fns';
import {
    Clock,
    MapPin,
    Monitor,
    MoreHorizontal,
    Smartphone,
    Tablet,
    Timer
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

export const CompactTimeCard: React.FC<TimeCardProps> = ({
  userRole,
  showControls = true,
}) => {
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isAnimating, setIsAnimating] = useState(false);
  const [locationAddress, setLocationAddress] = useState<string>('Getting location...');

  const {
    currentSession,
    isLoading,
    actions,
    isClockingIn,
    isClockingOut,
    isUpdatingStatus
  } = useTimeTracking({
    realtime: true,
    refetchInterval: 30000
  });

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Get device info and location on mount
  useEffect(() => {
    const getDeviceInfo = async () => {
      const info = deviceService.getDeviceInfo();
      const ipAddress = await deviceService.getIPAddress();
      setDeviceInfo({ ...info, ipAddress });
    };

    getDeviceInfo();

    // Get current location for display
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            const address = await locationService.reverseGeocode(
              position.coords.latitude,
              position.coords.longitude
            );
            setLocationAddress(address);
          } catch (error) {
            setLocationAddress('Location unavailable');
          }
        },
        () => {
          setLocationAddress('Location access denied');
        }
      );
    }
  }, []);

  const isCurrentlyClockedIn = !!currentSession && !currentSession.clock_out;
  
  const getWorkDuration = () => {
    if (!currentSession?.clock_in) return '00:00';
    
    const clockInTime = new Date(currentSession.clock_in);
    const now = currentTime;
    const diff = now.getTime() - clockInTime.getTime();
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  const getDeviceIcon = () => {
    if (!deviceInfo) return <Monitor className="h-3 w-3" />;
    
    switch (deviceInfo.type) {
      case 'mobile':
        return <Smartphone className="h-3 w-3" />;
      case 'tablet':
        return <Tablet className="h-3 w-3" />;
      default:
        return <Monitor className="h-3 w-3" />;
    }
  };

  const getStatusColor = () => {
    if (!isCurrentlyClockedIn) return 'bg-gray-500/20 text-gray-500';
    
    switch (currentSession?.status) {
      case 'break':
        return 'bg-yellow-500/20 text-yellow-500';
      case 'lunch':
        return 'bg-orange-500/20 text-orange-500';
      case 'overtime':
        return 'bg-purple-500/20 text-purple-500';
      default:
        return 'bg-green-500/20 text-green-500';
    }
  };

  const handleClockIn = async () => {
    setIsGettingLocation(true);
    setIsAnimating(true);

    try {
      const location: LocationData = await locationService.getCurrentLocation();
      const device: DeviceInfo = deviceInfo || deviceService.getDeviceInfo();

      await actions.clockIn({
        location,
        device,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        method: 'manual',
      });

      // Update location display
      setLocationAddress(location.address || 'Location captured');
    } catch (error) {
      console.error('Clock in failed:', error);
    } finally {
      setIsGettingLocation(false);
      setTimeout(() => setIsAnimating(false), 2000);
    }
  };

  const handleClockOut = async () => {
    if (!currentSession?.id) return;

    setIsAnimating(true);

    try {
      const location: LocationData = await locationService.getCurrentLocation();
      const device: DeviceInfo = deviceInfo || deviceService.getDeviceInfo();

      await actions.clockOut(currentSession.id, {
        location,
        device,
        method: 'manual',
      });
    } catch (error) {
      console.error('Clock out failed:', error);
    } finally {
      setTimeout(() => setIsAnimating(false), 2000);
    }
  };

  const handleStatusChange = async (status: 'active' | 'break' | 'lunch') => {
    if (!currentSession?.id) return;
    
    try {
      await actions.updateStatus(currentSession.id, status);
    } catch (error) {
      console.error('Status update failed:', error);
    }
  };

  if (isLoading) {
    return (
      <Card className="relative overflow-hidden bg-gradient-to-br from-red-500/10 to-red-600/20 border-red-500/20">
        <CardContent className="p-4">
          <div className="animate-pulse space-y-2">
            <div className="h-3 bg-red-500/20 rounded w-3/4"></div>
            <div className="h-6 bg-red-500/20 rounded w-1/2"></div>
            <div className="h-3 bg-red-500/20 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "relative overflow-hidden transition-all duration-500",
      "bg-gradient-to-br from-[#000000] to-[#0a0a0a] border-[#ff1c04]/20",
      "shadow-lg hover:shadow-xl",
      "backdrop-blur-sm"
    )}>
      {/* Enhanced Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Orbs */}
        <div className="absolute -top-10 -right-10 w-24 h-24 bg-gradient-to-br from-[#ff1c04]/20 to-[#000000]/10 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-tr from-[#000000]/20 to-[#ff1c04]/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>

        {/* Floating Particles */}
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-[#ff1c04]/40 rounded-full animate-pulse"></div>
        <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-[#0FA0CE]/40 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
        <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-[#ff1c04]/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
      </div>

      <CardContent className="relative z-10 p-4 sm:p-6">
        {/* Grid Layout: Left side for info, Right side for controls */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-center min-h-[300px]">
          {/* Left Side: Information Panel */}
          <div className="space-y-4">
            <div className="text-left">
              <h3 className="text-xl font-bold text-white mb-2">Time Tracking</h3>
              <p className="text-sm text-gray-300">Monitor your work hours and attendance</p>
            </div>

            {/* Current Time Display */}
            <div className="bg-gradient-to-r from-[#ff1c04]/10 to-[#000000]/20 rounded-lg p-4 border border-[#ff1c04]/20">
              <div className="text-sm text-[#ff1c04] font-medium mb-1">Current Time</div>
              <div className="text-2xl font-bold text-white">
                {format(currentTime, 'HH:mm:ss')}
              </div>
              <div className="text-xs text-gray-400">
                {format(currentTime, 'EEEE, MMMM d')}
              </div>
            </div>

            {/* Status Information */}
            <div className="bg-gradient-to-r from-[#000000]/20 to-[#ff1c04]/10 rounded-lg p-4 border border-[#ff1c04]/20">
              <div className="text-sm text-[#ff1c04] font-medium mb-2">Status</div>
              <div className="flex items-center gap-2 mb-2">
                <Badge className={cn("text-xs font-medium px-3 py-1", getStatusColor())}>
                  {isCurrentlyClockedIn ? (
                    currentSession?.status?.toUpperCase() || 'ACTIVE'
                  ) : (
                    'CLOCKED OUT'
                  )}
                </Badge>
              </div>

              {/* Work Duration */}
              {isCurrentlyClockedIn && (
                <div>
                  <div className="text-xs text-gray-400 mb-1">Work Duration</div>
                  <div className="text-lg font-bold text-[#ff1c04]">
                    {getWorkDuration()}
                  </div>
                </div>
              )}
            </div>

            {/* Additional Info Grid */}
            <div className="grid grid-cols-1 gap-3">
              {/* Location */}
              <div className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-[#ff1c04]/5 to-[#000000]/10 border border-[#ff1c04]/10">
                <MapPin className="h-4 w-4 text-[#ff1c04] flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-xs text-[#ff1c04] font-medium">Location</div>
                  <div className="text-sm text-gray-300 truncate">{locationAddress}</div>
                </div>
              </div>

              {/* Clock In Time */}
              {isCurrentlyClockedIn && currentSession?.clock_in && (
                <div className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-green-500/5 to-[#000000]/10 border border-green-500/10">
                  <Clock className="h-4 w-4 text-green-500 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="text-xs text-green-500 font-medium">Clocked In</div>
                    <div className="text-sm text-gray-300">
                      {format(new Date(currentSession.clock_in), 'HH:mm')}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Side: Control Panel */}
          <div className="flex flex-col items-center justify-center space-y-6">
            {/* Main Clock Button */}
            <div className="relative flex items-center justify-center">
              <div className={`relative w-32 h-32 ${isAnimating ? 'animate-spin' : ''}`}>
                {/* Outer Ring */}
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#ff1c04]/30 via-[#000000]/20 to-[#ff1c04]/30 animate-pulse blur-sm"></div>

                {/* Middle Ring */}
                <div className="absolute inset-2 rounded-full bg-gradient-to-r from-[#000000]/40 via-[#ff1c04]/30 to-[#000000]/40 animate-pulse" style={{ animationDelay: '0.5s' }}></div>

                {/* Inner Circle - Main Button */}
                <div
                  className={cn(
                    "absolute inset-4 rounded-full shadow-xl hover:shadow-2xl cursor-pointer flex items-center justify-center transform hover:scale-105 transition-all duration-500 pulse-glow",
                    isCurrentlyClockedIn
                      ? "bg-gradient-to-br from-[#ff1c04] via-[#e01703] to-[#cc1502]"
                      : "bg-gradient-to-br from-[#000000] via-[#1a1a1a] to-[#2a2a2a]"
                  )}
                  onClick={showControls ? (isCurrentlyClockedIn ? handleClockOut : handleClockIn) : undefined}
                >
                  <div className="text-center text-white relative z-10">
                    {isGettingLocation ? (
                      <MapPin className="h-6 w-6 mx-auto mb-1 animate-pulse drop-shadow-lg" />
                    ) : isClockingIn || isClockingOut ? (
                      <Timer className="h-6 w-6 mx-auto mb-1 animate-spin drop-shadow-lg" />
                    ) : (
                      <Clock className="h-6 w-6 mx-auto mb-1 drop-shadow-lg" />
                    )}
                    <span className="text-sm font-bold tracking-wide">
                      {isGettingLocation ? 'LOCATING' :
                       isClockingIn ? 'CLOCKING IN' :
                       isClockingOut ? 'CLOCKING OUT' :
                       isCurrentlyClockedIn ? 'CLOCK OUT' : 'CLOCK IN'}
                    </span>
                  </div>

                  {/* Inner Glow Effect */}
                  <div className="absolute inset-1 rounded-full bg-gradient-to-br from-white/20 to-transparent"></div>
                </div>

                {/* Floating particles around button */}
                <div className="absolute -top-2 -left-2 w-2 h-2 bg-[#ff1c04]/60 rounded-full animate-pulse"></div>
                <div className="absolute -bottom-2 -right-2 w-2 h-2 bg-[#0FA0CE]/60 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
              </div>
            </div>

            {/* Device Info */}
            {deviceInfo && (
              <div className="w-full bg-gradient-to-r from-[#0FA0CE]/5 to-[#000000]/10 rounded-lg p-4 border border-[#0FA0CE]/10">
                <div className="flex items-center gap-3">
                  {getDeviceIcon()}
                  <div className="flex-1 min-w-0">
                    <div className="text-sm text-[#0FA0CE] font-medium">Device</div>
                    <div className="text-xs text-gray-300 truncate">
                      {deviceInfo.brand} {deviceInfo.model} • {deviceInfo.os}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status Change Controls */}
        {showControls && isCurrentlyClockedIn && (
          <div className="mt-6 pt-4 border-t border-[#ff1c04]/20">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="w-full bg-gradient-to-r from-[#ff1c04]/10 to-[#000000]/20 border-[#ff1c04]/30 text-white hover:bg-[#ff1c04]/20">
                  <MoreHorizontal className="h-4 w-4 mr-2" />
                  Change Status
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="bg-[#000000] border-[#ff1c04]/30">
                <DropdownMenuItem onClick={() => handleStatusChange('active')} className="text-white hover:bg-[#ff1c04]/20">
                  Set Active
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('break')} className="text-white hover:bg-[#ff1c04]/20">
                  Take Break
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('lunch')} className="text-white hover:bg-[#ff1c04]/20">
                  Lunch Break
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </CardContent>

      {/* Animated Border Effect */}
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-red-500/20 via-transparent to-red-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
    </Card>
  );
};
