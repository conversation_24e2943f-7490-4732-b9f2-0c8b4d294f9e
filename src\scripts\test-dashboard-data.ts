import { supabase } from '../integrations/supabase/client';
import { api } from '../lib/api';

export class DashboardDataTester {
  
  /**
   * Test all dashboard data sources
   */
  static async testDashboardData() {
    console.log('🧪 Testing dashboard data sources...');
    
    try {
      // Test 1: Check if basic tables exist and have data
      console.log('\n1️⃣ Testing basic table access...');
      
      const tableTests = [
        { name: 'profiles', query: () => supabase.from('profiles').select('id, full_name, role').limit(5) },
        { name: 'departments', query: () => supabase.from('departments').select('id, name').limit(5) },
        { name: 'projects', query: () => supabase.from('projects').select('id, name, status').limit(5) },
        { name: 'tasks', query: () => supabase.from('tasks').select('id, title, status').limit(5) },
        { name: 'project_assignments', query: () => supabase.from('project_assignments').select('id, assigned_to, status').limit(5) },
        { name: 'assets_inventory', query: () => supabase.from('assets_inventory').select('id, name, status').limit(5) },
        { name: 'expense_reports', query: () => supabase.from('expense_reports').select('id, amount, status').limit(5) },
        { name: 'invoices', query: () => supabase.from('invoices').select('id, total_amount, payment_status').limit(5) }
      ];
      
      const tableResults: any = {};
      
      for (const test of tableTests) {
        try {
          const { data, error } = await test.query();
          if (error) {
            console.log(`❌ ${test.name}: ${error.message}`);
            tableResults[test.name] = { exists: false, error: error.message, count: 0 };
          } else {
            console.log(`✅ ${test.name}: ${data?.length || 0} records`);
            tableResults[test.name] = { exists: true, count: data?.length || 0, sample: data?.[0] };
          }
        } catch (err: any) {
          console.log(`💥 ${test.name}: ${err.message}`);
          tableResults[test.name] = { exists: false, error: err.message, count: 0 };
        }
      }
      
      // Test 2: Test API endpoints
      console.log('\n2️⃣ Testing API endpoints...');
      
      try {
        const dashboardStatsResult = await api.system.getDashboardStats();
        console.log('📊 Dashboard stats API:', dashboardStatsResult.success ? '✅' : '❌', dashboardStatsResult.error?.message || '');
        if (dashboardStatsResult.success && dashboardStatsResult.data) {
          console.log('  - Users:', dashboardStatsResult.data.users?.length || 0);
          console.log('  - Projects:', dashboardStatsResult.data.projects?.length || 0);
          console.log('  - Tasks:', dashboardStatsResult.data.tasks?.length || 0);
        }
      } catch (err: any) {
        console.log('❌ Dashboard stats API failed:', err.message);
      }
      
      try {
        const projectsResult = await api.projects.getAll();
        console.log('📋 Projects API:', projectsResult.success ? '✅' : '❌', projectsResult.error?.message || '');
        if (projectsResult.success) {
          console.log('  - Projects count:', Array.isArray(projectsResult.data) ? projectsResult.data.length : 0);
        }
      } catch (err: any) {
        console.log('❌ Projects API failed:', err.message);
      }
      
      try {
        const tasksResult = await api.tasks.getAll({});
        console.log('✅ Tasks API:', tasksResult.success ? '✅' : '❌', tasksResult.error?.message || '');
        if (tasksResult.success) {
          console.log('  - Tasks count:', Array.isArray(tasksResult.data) ? tasksResult.data.length : 0);
        }
      } catch (err: any) {
        console.log('❌ Tasks API failed:', err.message);
      }
      
      // Test 3: Test authentication
      console.log('\n3️⃣ Testing authentication...');
      
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      if (authError) {
        console.log('❌ Auth error:', authError.message);
      } else if (user) {
        console.log('✅ User authenticated:', user.email);
        
        // Get user profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (profileError) {
          console.log('❌ Profile error:', profileError.message);
        } else {
          console.log('✅ User profile:', profile?.full_name, '|', profile?.role);
        }
      } else {
        console.log('❌ No user authenticated');
      }
      
      // Test 4: Create sample data if tables are empty
      console.log('\n4️⃣ Checking for sample data...');
      
      if (tableResults.departments?.count === 0 && tableResults.departments?.exists) {
        console.log('🔧 Creating sample departments...');
        try {
          const { error } = await supabase.from('departments').insert([
            { name: 'Information Technology', description: 'IT Department' },
            { name: 'Human Resources', description: 'HR Department' },
            { name: 'Finance', description: 'Finance Department' }
          ]);
          if (error) {
            console.log('❌ Failed to create departments:', error.message);
          } else {
            console.log('✅ Sample departments created');
          }
        } catch (err: any) {
          console.log('❌ Error creating departments:', err.message);
        }
      }
      
      if (tableResults.projects?.count === 0 && tableResults.projects?.exists) {
        console.log('🔧 Creating sample projects...');
        try {
          const { error } = await supabase.from('projects').insert([
            {
              name: 'Website Redesign',
              description: 'Redesign company website',
              status: 'active',
              progress_percentage: 75
            },
            {
              name: 'Mobile App Development',
              description: 'Develop mobile application',
              status: 'active',
              progress_percentage: 45
            },
            {
              name: 'Database Migration',
              description: 'Migrate to new database system',
              status: 'completed',
              progress_percentage: 100
            }
          ]);
          if (error) {
            console.log('❌ Failed to create projects:', error.message);
          } else {
            console.log('✅ Sample projects created');
          }
        } catch (err: any) {
          console.log('❌ Error creating projects:', err.message);
        }
      }
      
      if (tableResults.tasks?.count === 0 && tableResults.tasks?.exists) {
        console.log('🔧 Creating sample tasks...');
        try {
          const { error } = await supabase.from('tasks').insert([
            {
              title: 'Design Homepage',
              description: 'Create new homepage design',
              status: 'completed',
              priority: 'high'
            },
            {
              title: 'Setup Database',
              description: 'Configure database connections',
              status: 'in_progress',
              priority: 'medium'
            },
            {
              title: 'Write Documentation',
              description: 'Document API endpoints',
              status: 'pending',
              priority: 'low'
            }
          ]);
          if (error) {
            console.log('❌ Failed to create tasks:', error.message);
          } else {
            console.log('✅ Sample tasks created');
          }
        } catch (err: any) {
          console.log('❌ Error creating tasks:', err.message);
        }
      }
      
      // Test 5: Summary
      console.log('\n📋 DASHBOARD DATA TEST SUMMARY');
      console.log('=====================================');
      
      const existingTables = Object.entries(tableResults)
        .filter(([_, result]: [string, any]) => result.exists)
        .map(([name, _]) => name);
      
      const missingTables = Object.entries(tableResults)
        .filter(([_, result]: [string, any]) => !result.exists)
        .map(([name, _]) => name);
      
      console.log(`✅ Existing tables (${existingTables.length}):`, existingTables.join(', '));
      if (missingTables.length > 0) {
        console.log(`❌ Missing tables (${missingTables.length}):`, missingTables.join(', '));
      }
      
      const totalRecords = Object.values(tableResults)
        .reduce((sum: number, result: any) => sum + (result.count || 0), 0);
      
      console.log(`📊 Total records across all tables: ${totalRecords}`);
      
      if (totalRecords === 0) {
        console.log('⚠️ No data found in any tables. Dashboard will show empty cards.');
        console.log('💡 Run the comprehensive migration script to create sample data.');
      }
      
      return {
        tableResults,
        existingTables,
        missingTables,
        totalRecords
      };
      
    } catch (error) {
      console.error('💥 Dashboard data test failed:', error);
      return null;
    }
  }
  
  /**
   * Quick fix for empty dashboard
   */
  static async quickFixEmptyDashboard() {
    console.log('🔧 Quick fix for empty dashboard...');
    
    try {
      // Check if we have any data
      const testResult = await this.testDashboardData();
      
      if (!testResult || testResult.totalRecords === 0) {
        console.log('🚀 Creating minimal sample data for dashboard...');
        
        // Create basic sample data that will make the dashboard work
        const sampleData = {
          departments: [
            { name: 'IT Department', description: 'Information Technology' },
            { name: 'HR Department', description: 'Human Resources' }
          ],
          projects: [
            { name: 'Dashboard Development', status: 'active', progress_percentage: 80 },
            { name: 'User Management', status: 'completed', progress_percentage: 100 },
            { name: 'Reporting System', status: 'active', progress_percentage: 60 }
          ],
          tasks: [
            { title: 'Fix Dashboard Cards', status: 'in_progress', priority: 'high' },
            { title: 'Update Documentation', status: 'completed', priority: 'medium' },
            { title: 'Test Features', status: 'pending', priority: 'low' }
          ]
        };
        
        // Insert sample data
        for (const [table, data] of Object.entries(sampleData)) {
          try {
            const { error } = await supabase.from(table).insert(data);
            if (error) {
              console.log(`❌ Failed to insert ${table}:`, error.message);
            } else {
              console.log(`✅ Inserted ${data.length} ${table} records`);
            }
          } catch (err: any) {
            console.log(`❌ Error inserting ${table}:`, err.message);
          }
        }
        
        console.log('✅ Sample data created. Refresh the dashboard to see changes.');
      } else {
        console.log('✅ Dashboard has data. The issue might be elsewhere.');
      }
      
    } catch (error) {
      console.error('💥 Quick fix failed:', error);
    }
  }
}

// Make it available globally for console debugging
if (typeof window !== 'undefined') {
  (window as any).testDashboardData = () => DashboardDataTester.testDashboardData();
  (window as any).quickFixDashboard = () => DashboardDataTester.quickFixEmptyDashboard();
  console.log('🛠️ Dashboard data tester loaded. Available commands:');
  console.log('  - testDashboardData() - Test all dashboard data sources');
  console.log('  - quickFixDashboard() - Create sample data if dashboard is empty');
}

// DashboardDataTester already exported as class declaration above
