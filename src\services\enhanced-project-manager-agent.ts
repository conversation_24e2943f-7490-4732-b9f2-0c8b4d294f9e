import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { AdvancedAIService } from './advanced-ai-service';
import { SystemLogsService } from './system-logs-service';
import { UserActivitiesService } from './user-activities-service';

/**
 * Enhanced AI Project Manager Agent
 * Advanced project management AI with intelligent automation, insights, and real-time assistance
 * Provides comprehensive project oversight, risk management, and optimization recommendations
 */

export interface ProjectInsight {
  id: string;
  type: 'risk' | 'opportunity' | 'bottleneck' | 'optimization' | 'resource' | 'timeline';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  actionItems: string[];
  estimatedImpact: number; // 1-10 scale
  confidence: number; // 0-1 scale
  relatedEntities: Array<{
    type: 'project' | 'task' | 'user' | 'milestone';
    id: string;
    name: string;
  }>;
  createdAt: string;
  resolvedAt?: string;
}

export interface ProjectMetrics {
  projectId: string;
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  blockedTasks: number;
  averageTaskCompletion: number; // days
  teamUtilization: number; // percentage
  budgetUtilization: number; // percentage
  riskScore: number; // 0-10
  healthScore: number; // 0-100
  velocityTrend: 'increasing' | 'stable' | 'decreasing';
  predictedCompletion: string;
  confidenceInterval: number;
}

export interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    type: 'task_status' | 'deadline_approaching' | 'resource_conflict' | 'risk_threshold' | 'milestone_reached';
    conditions: Record<string, any>;
  };
  actions: Array<{
    type: 'notify' | 'reassign' | 'escalate' | 'adjust_timeline' | 'create_task' | 'update_status';
    parameters: Record<string, any>;
  }>;
  isActive: boolean;
  executionCount: number;
  lastExecuted?: string;
}

export interface ProjectRecommendation {
  id: string;
  type: 'task_optimization' | 'resource_reallocation' | 'timeline_adjustment' | 'risk_mitigation' | 'process_improvement';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  benefits: string[];
  implementation: {
    steps: string[];
    estimatedEffort: string;
    requiredResources: string[];
  };
  impact: {
    timeReduction?: number; // days
    costSaving?: number; // percentage
    riskReduction?: number; // 0-10 scale
    qualityImprovement?: number; // percentage
  };
  confidence: number;
  createdAt: string;
}

export class EnhancedProjectManagerAgent {
  private static isInitialized: boolean = false;
  private static activeProjects: Map<string, ProjectMetrics> = new Map();
  private static insights: Map<string, ProjectInsight[]> = new Map();
  private static automationRules: AutomationRule[] = [];
  private static recommendations: Map<string, ProjectRecommendation[]> = new Map();

  // Initialize the enhanced project manager agent
  static async initialize(): Promise<boolean> {
    try {
      console.log('🤖 Initializing Enhanced Project Manager Agent...');

      // Initialize dependencies
      await AdvancedAIService.initialize();

      // Load automation rules
      await this.loadAutomationRules();

      // Start monitoring active projects
      await this.startProjectMonitoring();

      // Initialize real-time insights
      await this.initializeInsightsEngine();

      this.isInitialized = true;
      console.log('✅ Enhanced Project Manager Agent initialized successfully');

      await SystemLogsService.info('project_manager', 'Enhanced PM Agent initialized', 
        'Advanced project management AI ready');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced Project Manager Agent:', error);
      await SystemLogsService.error('project_manager', 'PM Agent initialization failed', error.toString());
      return false;
    }
  }

  // Analyze project health and generate insights
  static async analyzeProjectHealth(projectId: string): Promise<ProjectMetrics> {
    try {
      console.log(`🔍 Analyzing project health for: ${projectId}`);

      // Get project data
      const { data: project } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (!project) {
        throw new Error('Project not found');
      }

      // Get project tasks
      const { data: tasks } = await supabase
        .from('tasks')
        .select('*')
        .eq('project_id', projectId);

      // Get team assignments
      const { data: assignments } = await supabase
        .from('project_assignments')
        .select('*, profiles(*)')
        .eq('project_id', projectId);

      // Calculate metrics
      const totalTasks = tasks?.length || 0;
      const completedTasks = tasks?.filter(t => t.status === 'completed').length || 0;
      const overdueTasks = tasks?.filter(t => 
        t.due_date && new Date(t.due_date) < new Date() && t.status !== 'completed'
      ).length || 0;
      const blockedTasks = tasks?.filter(t => t.status === 'blocked').length || 0;

      // Calculate completion rate
      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

      // Calculate average task completion time
      const completedTasksWithDates = tasks?.filter(t => 
        t.status === 'completed' && t.created_at && t.completed_at
      ) || [];
      
      const averageCompletion = completedTasksWithDates.length > 0 
        ? completedTasksWithDates.reduce((sum, task) => {
            const start = new Date(task.created_at);
            const end = new Date(task.completed_at);
            return sum + (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
          }, 0) / completedTasksWithDates.length
        : 0;

      // Calculate risk score
      const riskScore = this.calculateRiskScore(tasks || [], overdueTasks, blockedTasks);

      // Calculate health score
      const healthScore = this.calculateHealthScore(completionRate, riskScore, overdueTasks, totalTasks);

      // Predict completion date
      const predictedCompletion = this.predictProjectCompletion(tasks || [], averageCompletion);

      const metrics: ProjectMetrics = {
        projectId,
        totalTasks,
        completedTasks,
        overdueTasks,
        blockedTasks,
        averageTaskCompletion: averageCompletion,
        teamUtilization: this.calculateTeamUtilization(assignments || []),
        budgetUtilization: this.calculateBudgetUtilization(project),
        riskScore,
        healthScore,
        velocityTrend: this.calculateVelocityTrend(projectId),
        predictedCompletion,
        confidenceInterval: this.calculateConfidenceInterval(tasks || [])
      };

      // Store metrics
      this.activeProjects.set(projectId, metrics);

      // Generate insights based on metrics
      await this.generateProjectInsights(projectId, metrics);

      // Log analysis
      await UserActivitiesService.logActivity(
        'analyze',
        'Project Health Analysis',
        `Analyzed project health: ${healthScore}/100 health score`,
        'project',
        projectId,
        project.name
      );

      return metrics;
    } catch (error) {
      console.error('Error analyzing project health:', error);
      throw error;
    }
  }

  // Generate intelligent project insights
  static async generateProjectInsights(projectId: string, metrics: ProjectMetrics): Promise<ProjectInsight[]> {
    try {
      const insights: ProjectInsight[] = [];

      // Risk-based insights
      if (metrics.riskScore > 7) {
        insights.push({
          id: crypto.randomUUID(),
          type: 'risk',
          severity: 'critical',
          title: 'High Project Risk Detected',
          description: `Project risk score is ${metrics.riskScore}/10, indicating significant challenges.`,
          impact: 'Project may face delays, budget overruns, or quality issues.',
          recommendation: 'Immediate risk mitigation required. Review blocked tasks and resource allocation.',
          actionItems: [
            'Review and resolve blocked tasks',
            'Reassess project timeline and milestones',
            'Consider additional resources or scope reduction',
            'Implement daily standups for better coordination'
          ],
          estimatedImpact: 8,
          confidence: 0.9,
          relatedEntities: [{ type: 'project', id: projectId, name: 'Current Project' }],
          createdAt: new Date().toISOString()
        });
      }

      // Overdue tasks insight
      if (metrics.overdueTasks > 0) {
        const severity = metrics.overdueTasks > 5 ? 'high' : metrics.overdueTasks > 2 ? 'medium' : 'low';
        insights.push({
          id: crypto.randomUUID(),
          type: 'bottleneck',
          severity,
          title: `${metrics.overdueTasks} Overdue Tasks Detected`,
          description: `${metrics.overdueTasks} tasks are past their due dates, creating project bottlenecks.`,
          impact: 'Delays in dependent tasks and overall project timeline.',
          recommendation: 'Prioritize overdue tasks and reassign if necessary.',
          actionItems: [
            'Review overdue task dependencies',
            'Reassign tasks to available team members',
            'Extend deadlines where appropriate',
            'Identify root causes of delays'
          ],
          estimatedImpact: 6,
          confidence: 0.95,
          relatedEntities: [{ type: 'project', id: projectId, name: 'Current Project' }],
          createdAt: new Date().toISOString()
        });
      }

      // Team utilization insight
      if (metrics.teamUtilization < 60) {
        insights.push({
          id: crypto.randomUUID(),
          type: 'resource',
          severity: 'medium',
          title: 'Low Team Utilization',
          description: `Team utilization is at ${metrics.teamUtilization}%, indicating underutilized resources.`,
          impact: 'Potential for faster project completion with better resource allocation.',
          recommendation: 'Redistribute tasks to optimize team capacity.',
          actionItems: [
            'Review individual workloads',
            'Assign additional tasks to available team members',
            'Consider parallel task execution',
            'Identify skill gaps and training needs'
          ],
          estimatedImpact: 5,
          confidence: 0.8,
          relatedEntities: [{ type: 'project', id: projectId, name: 'Current Project' }],
          createdAt: new Date().toISOString()
        });
      }

      // Velocity trend insight
      if (metrics.velocityTrend === 'decreasing') {
        insights.push({
          id: crypto.randomUUID(),
          type: 'optimization',
          severity: 'medium',
          title: 'Decreasing Project Velocity',
          description: 'Project velocity is trending downward, indicating potential productivity issues.',
          impact: 'Project may not meet planned deadlines without intervention.',
          recommendation: 'Investigate causes of velocity decrease and implement improvements.',
          actionItems: [
            'Analyze recent task completion patterns',
            'Identify and remove blockers',
            'Review team motivation and engagement',
            'Consider process improvements'
          ],
          estimatedImpact: 7,
          confidence: 0.75,
          relatedEntities: [{ type: 'project', id: projectId, name: 'Current Project' }],
          createdAt: new Date().toISOString()
        });
      }

      // Opportunity insights
      if (metrics.healthScore > 80 && metrics.completedTasks / metrics.totalTasks > 0.7) {
        insights.push({
          id: crypto.randomUUID(),
          type: 'opportunity',
          severity: 'low',
          title: 'Project Performing Well',
          description: `Project health score is ${metrics.healthScore}/100 with strong completion rate.`,
          impact: 'Opportunity to accelerate delivery or take on additional scope.',
          recommendation: 'Consider early delivery or scope expansion opportunities.',
          actionItems: [
            'Review potential for early completion',
            'Identify additional value-add features',
            'Document best practices for future projects',
            'Consider team recognition for excellent performance'
          ],
          estimatedImpact: 4,
          confidence: 0.85,
          relatedEntities: [{ type: 'project', id: projectId, name: 'Current Project' }],
          createdAt: new Date().toISOString()
        });
      }

      // Store insights
      this.insights.set(projectId, insights);

      // Log insights generation
      await SystemLogsService.info('project_manager', 'Project insights generated', 
        `Generated ${insights.length} insights for project ${projectId}`);

      return insights;
    } catch (error) {
      console.error('Error generating project insights:', error);
      return [];
    }
  }

  // Generate project recommendations
  static async generateProjectRecommendations(projectId: string): Promise<ProjectRecommendation[]> {
    try {
      const metrics = this.activeProjects.get(projectId);
      if (!metrics) {
        throw new Error('Project metrics not available');
      }

      const recommendations: ProjectRecommendation[] = [];

      // Task optimization recommendations
      if (metrics.averageTaskCompletion > 5) {
        recommendations.push({
          id: crypto.randomUUID(),
          type: 'task_optimization',
          priority: 'medium',
          title: 'Optimize Task Breakdown',
          description: 'Tasks are taking longer than expected to complete. Consider breaking down large tasks.',
          benefits: [
            'Improved task visibility and tracking',
            'Better progress estimation',
            'Reduced risk of task abandonment',
            'Enhanced team motivation through frequent completions'
          ],
          implementation: {
            steps: [
              'Identify tasks taking longer than 5 days',
              'Break down large tasks into smaller subtasks',
              'Update task estimates and dependencies',
              'Reassign subtasks to appropriate team members'
            ],
            estimatedEffort: '2-4 hours',
            requiredResources: ['Project Manager', 'Team Leads']
          },
          impact: {
            timeReduction: 3,
            qualityImprovement: 15
          },
          confidence: 0.8,
          createdAt: new Date().toISOString()
        });
      }

      // Resource reallocation recommendations
      if (metrics.teamUtilization < 70) {
        recommendations.push({
          id: crypto.randomUUID(),
          type: 'resource_reallocation',
          priority: 'high',
          title: 'Optimize Team Resource Allocation',
          description: 'Team utilization is below optimal levels. Redistribute workload for better efficiency.',
          benefits: [
            'Faster project completion',
            'Better team engagement',
            'Improved skill utilization',
            'Reduced project costs'
          ],
          implementation: {
            steps: [
              'Analyze individual team member workloads',
              'Identify skills and availability gaps',
              'Reassign tasks based on capacity and skills',
              'Monitor utilization improvements'
            ],
            estimatedEffort: '1-2 days',
            requiredResources: ['Project Manager', 'HR/Resource Manager']
          },
          impact: {
            timeReduction: 7,
            costSaving: 15
          },
          confidence: 0.85,
          createdAt: new Date().toISOString()
        });
      }

      // Risk mitigation recommendations
      if (metrics.riskScore > 6) {
        recommendations.push({
          id: crypto.randomUUID(),
          type: 'risk_mitigation',
          priority: 'urgent',
          title: 'Implement Risk Mitigation Strategies',
          description: 'High risk score requires immediate attention to prevent project failure.',
          benefits: [
            'Reduced project failure risk',
            'Better stakeholder confidence',
            'Improved project predictability',
            'Enhanced team morale'
          ],
          implementation: {
            steps: [
              'Conduct detailed risk assessment',
              'Develop mitigation plans for top risks',
              'Implement risk monitoring dashboard',
              'Establish escalation procedures'
            ],
            estimatedEffort: '3-5 days',
            requiredResources: ['Project Manager', 'Senior Leadership', 'Risk Specialist']
          },
          impact: {
            riskReduction: 4,
            qualityImprovement: 25
          },
          confidence: 0.9,
          createdAt: new Date().toISOString()
        });
      }

      // Store recommendations
      this.recommendations.set(projectId, recommendations);

      return recommendations;
    } catch (error) {
      console.error('Error generating project recommendations:', error);
      return [];
    }
  }

  // Helper methods for calculations
  private static calculateRiskScore(tasks: any[], overdueTasks: number, blockedTasks: number): number {
    const totalTasks = tasks.length;
    if (totalTasks === 0) return 0;

    const overdueRatio = overdueTasks / totalTasks;
    const blockedRatio = blockedTasks / totalTasks;
    const highPriorityTasks = tasks.filter(t => t.priority === 'high' || t.priority === 'critical').length;
    const highPriorityRatio = highPriorityTasks / totalTasks;

    // Risk score calculation (0-10 scale)
    const riskScore = (overdueRatio * 4) + (blockedRatio * 3) + (highPriorityRatio * 2) + 
                     (tasks.filter(t => !t.assigned_to_id).length / totalTasks * 1);

    return Math.min(Math.round(riskScore * 10) / 10, 10);
  }

  private static calculateHealthScore(completionRate: number, riskScore: number, overdueTasks: number, totalTasks: number): number {
    const baseScore = completionRate;
    const riskPenalty = (riskScore / 10) * 30;
    const overduePenalty = totalTasks > 0 ? (overdueTasks / totalTasks) * 20 : 0;
    
    const healthScore = Math.max(baseScore - riskPenalty - overduePenalty, 0);
    return Math.round(healthScore);
  }

  private static calculateTeamUtilization(assignments: any[]): number {
    if (assignments.length === 0) return 0;
    
    // Simplified calculation - in real implementation, this would consider
    // actual hours worked vs available hours
    const activeAssignments = assignments.filter(a => a.status === 'active').length;
    return Math.round((activeAssignments / assignments.length) * 100);
  }

  private static calculateBudgetUtilization(project: any): number {
    // Simplified calculation - would integrate with actual budget tracking
    return project.budget_used && project.budget_total 
      ? Math.round((project.budget_used / project.budget_total) * 100)
      : 0;
  }

  private static calculateVelocityTrend(projectId: string): 'increasing' | 'stable' | 'decreasing' {
    // Simplified - would analyze historical completion data
    return 'stable';
  }

  private static predictProjectCompletion(tasks: any[], averageCompletion: number): string {
    const remainingTasks = tasks.filter(t => t.status !== 'completed').length;
    const estimatedDays = remainingTasks * averageCompletion;
    const completionDate = new Date();
    completionDate.setDate(completionDate.getDate() + estimatedDays);
    return completionDate.toISOString().split('T')[0];
  }

  private static calculateConfidenceInterval(tasks: any[]): number {
    // Simplified confidence calculation based on task completion variance
    const completedTasks = tasks.filter(t => t.status === 'completed');
    if (completedTasks.length < 3) return 0.5;
    
    // Higher confidence with more completed tasks and consistent patterns
    return Math.min(0.95, 0.5 + (completedTasks.length * 0.05));
  }

  // Load automation rules
  private static async loadAutomationRules(): Promise<void> {
    // Default automation rules
    this.automationRules = [
      {
        id: crypto.randomUUID(),
        name: 'Overdue Task Alert',
        description: 'Notify when tasks become overdue',
        trigger: {
          type: 'deadline_approaching',
          conditions: { days_overdue: 1 }
        },
        actions: [
          {
            type: 'notify',
            parameters: { recipients: ['assignee', 'project_manager'], message: 'Task is overdue' }
          }
        ],
        isActive: true,
        executionCount: 0
      },
      {
        id: crypto.randomUUID(),
        name: 'High Risk Project Escalation',
        description: 'Escalate when project risk exceeds threshold',
        trigger: {
          type: 'risk_threshold',
          conditions: { risk_score: 8 }
        },
        actions: [
          {
            type: 'escalate',
            parameters: { level: 'senior_management', urgency: 'high' }
          }
        ],
        isActive: true,
        executionCount: 0
      }
    ];
  }

  // Start project monitoring
  private static async startProjectMonitoring(): Promise<void> {
    // In a real implementation, this would set up real-time monitoring
    console.log('🔍 Project monitoring started');
  }

  // Initialize insights engine
  private static async initializeInsightsEngine(): Promise<void> {
    console.log('💡 Insights engine initialized');
  }

  // Get project insights
  static getProjectInsights(projectId: string): ProjectInsight[] {
    return this.insights.get(projectId) || [];
  }

  // Get project recommendations
  static getProjectRecommendations(projectId: string): ProjectRecommendation[] {
    return this.recommendations.get(projectId) || [];
  }

  // Get project metrics
  static getProjectMetrics(projectId: string): ProjectMetrics | null {
    return this.activeProjects.get(projectId) || null;
  }

  // Check if initialized
  static isReady(): boolean {
    return this.isInitialized;
  }
}

export default EnhancedProjectManagerAgent;
