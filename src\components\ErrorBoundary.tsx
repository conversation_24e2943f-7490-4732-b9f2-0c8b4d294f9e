
import React, { Component, ErrorInfo, ReactNode } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { AlertTriangle, RefreshCw, Home, Bug, Copy } from "lucide-react";
import { errorHandler, type ErrorContext } from '@/lib/error-handler';
import { logger } from '@/lib/logger';
import { toast } from 'sonner';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  context?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  maxRetries?: number;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
    errorId: null,
    retryCount: 0,
  };

  public static getDerivedStateFromError(error: Error): Partial<State> {
    return { hasError: true, error, errorInfo: null };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const context: ErrorContext = {
      component: this.props.context || 'ErrorBoundary',
      function: 'componentDidCatch',
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      metadata: {
        componentStack: errorInfo.componentStack,
        retryCount: this.state.retryCount,
        props: this.props.context ? { context: this.props.context } : undefined,
      },
    };

    // Handle error through centralized error handler
    const appError = errorHandler.handleError(error, context, false);

    // Log error through centralized logger
    logger.error('ui', `Error boundary caught error in ${this.props.context || 'unknown component'}`, {
      component: this.props.context,
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      retryCount: this.state.retryCount,
      errorId: appError.id,
    });

    this.setState({
      error,
      errorInfo,
      errorId: appError.id,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error to external service
    this.logErrorToService(error, errorInfo, appError.id);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo, errorId: string) => {
    const errorData = {
      id: errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      context: this.props.context,
      retryCount: this.state.retryCount,
    };

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught An Error');
      console.error('Error ID:', errorId);
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Error Data:', errorData);
      console.groupEnd();
    }

    // In production, send to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { contexts: { errorInfo } });
      // Example: LogRocket.captureException(error);
      // Example: Custom error reporting API
    }
  };

  private handleRetry = () => {
    const maxRetries = this.props.maxRetries || 3;

    if (this.state.retryCount >= maxRetries) {
      toast.error('Maximum retry attempts reached', {
        description: 'Please refresh the page or contact support if the problem persists.',
      });
      return;
    }

    logger.info('ui', 'User retrying after error boundary catch', {
      component: this.props.context,
      errorId: this.state.errorId,
      retryCount: this.state.retryCount + 1,
    });

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: this.state.retryCount + 1,
    });
  };

  private handleGoHome = () => {
    logger.info('ui', 'User navigating home after error boundary catch', {
      component: this.props.context,
      errorId: this.state.errorId,
    });

    window.location.href = "/";
  };

  private handleCopyError = () => {
    const errorDetails = {
      id: this.state.errorId,
      message: this.state.error?.message,
      timestamp: new Date().toISOString(),
      component: this.props.context,
      url: window.location.href,
    };

    const errorText = JSON.stringify(errorDetails, null, 2);

    if (navigator.clipboard) {
      navigator.clipboard.writeText(errorText).then(() => {
        toast.success('Error details copied to clipboard');
      }).catch(() => {
        toast.error('Failed to copy error details');
      });
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = errorText;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      toast.success('Error details copied to clipboard');
    }

    logger.info('ui', 'User copied error details', {
      errorId: this.state.errorId,
      component: this.props.context,
    });
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const maxRetries = this.props.maxRetries || 3;
      const canRetry = this.state.retryCount < maxRetries;

      return (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-destructive">
                <AlertTriangle className="h-6 w-6" />
                Something went wrong
              </CardTitle>
              {this.state.errorId && (
                <p className="text-xs text-muted-foreground">
                  Error ID: {this.state.errorId}
                </p>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <div className="flex items-start justify-between mb-2">
                  <p className="text-sm font-medium text-foreground">
                    Error: {this.state.error?.message}
                  </p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={this.handleCopyError}
                    className="h-6 w-6 p-0"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>

                {this.props.context && (
                  <p className="text-xs text-muted-foreground mb-2">
                    Component: {this.props.context}
                  </p>
                )}

                {this.state.retryCount > 0 && (
                  <p className="text-xs text-muted-foreground mb-2">
                    Retry attempts: {this.state.retryCount}/{maxRetries}
                  </p>
                )}

                {process.env.NODE_ENV === 'development' && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                      Technical Details (Development Only)
                    </summary>
                    <div className="mt-2 space-y-2">
                      <div>
                        <p className="text-xs font-medium text-muted-foreground">Stack Trace:</p>
                        <pre className="text-xs text-muted-foreground whitespace-pre-wrap overflow-auto max-h-32 bg-background p-2 rounded border">
                          {this.state.error?.stack}
                        </pre>
                      </div>
                      {this.state.errorInfo && (
                        <div>
                          <p className="text-xs font-medium text-muted-foreground">Component Stack:</p>
                          <pre className="text-xs text-muted-foreground whitespace-pre-wrap overflow-auto max-h-32 bg-background p-2 rounded border">
                            {this.state.errorInfo.componentStack}
                          </pre>
                        </div>
                      )}
                    </div>
                  </details>
                )}
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  This error has been automatically logged. You can try the following:
                </p>
                <ul className="list-disc list-inside text-sm text-muted-foreground space-y-1 ml-2">
                  {canRetry && <li>Click "Try Again" to retry the operation</li>}
                  <li>Go back to the homepage</li>
                  <li>Refresh the page manually</li>
                  <li>Clear your browser cache and cookies</li>
                  <li>Contact support with the error ID if the problem persists</li>
                </ul>
              </div>

              <div className="flex gap-2 pt-4">
                {canRetry ? (
                  <Button onClick={this.handleRetry} className="flex-1">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again ({maxRetries - this.state.retryCount} left)
                  </Button>
                ) : (
                  <Button
                    onClick={() => window.location.reload()}
                    className="flex-1"
                    variant="secondary"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Refresh Page
                  </Button>
                )}
                <Button onClick={this.handleGoHome} variant="outline" className="flex-1">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};
