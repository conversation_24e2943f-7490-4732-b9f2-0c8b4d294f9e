-- Fix Foreign Key Constraint Error for document_analysis table
-- This script provides solutions for the document_analysis_created_by_fkey constraint error

-- SOLUTION 1: Delete related document_analysis records before deleting users
-- Replace 'USER_ID_TO_DELETE' with the actual user ID you want to delete

-- Step 1: Delete related document_analysis records
DELETE FROM public.document_analysis 
WHERE created_by = 'USER_ID_TO_DELETE';

-- Step 2: Now you can safely delete the user
-- DELETE FROM auth.users WHERE id = 'USER_ID_TO_DELETE';

-- SOLUTION 2: Update created_by to NULL instead of deleting
-- This preserves the document analysis records but removes the user reference

UPDATE public.document_analysis 
SET created_by = NULL 
WHERE created_by = 'USER_ID_TO_DELETE';

-- SOLUTION 3: Transfer ownership to another user
-- Replace 'NEW_USER_ID' with the ID of the user who should take ownership

UPDATE public.document_analysis 
SET created_by = 'NEW_USER_ID' 
WHERE created_by = 'USER_ID_TO_DELETE';

-- SOLUTION 4: Modify the foreign key constraint to CASCADE DELETE
-- This will automatically delete related document_analysis records when a user is deleted

-- First, drop the existing constraint
ALTER TABLE public.document_analysis 
DROP CONSTRAINT IF EXISTS document_analysis_created_by_fkey;

-- Recreate the constraint with CASCADE DELETE
ALTER TABLE public.document_analysis 
ADD CONSTRAINT document_analysis_created_by_fkey 
FOREIGN KEY (created_by) 
REFERENCES auth.users(id) 
ON DELETE CASCADE;

-- SOLUTION 5: Check which users have related document_analysis records
-- Use this query to see which users would be affected

SELECT 
    u.id as user_id,
    u.email,
    COUNT(da.id) as document_analysis_count
FROM auth.users u
LEFT JOIN public.document_analysis da ON u.id = da.created_by
WHERE da.created_by IS NOT NULL
GROUP BY u.id, u.email
ORDER BY document_analysis_count DESC;

-- SOLUTION 6: Bulk cleanup - Remove all document_analysis records with non-existent users
-- This cleans up orphaned records

DELETE FROM public.document_analysis 
WHERE created_by NOT IN (
    SELECT id FROM auth.users
);

-- SOLUTION 7: Set created_by to NULL for all records (if you want to remove all user references)
UPDATE public.document_analysis SET created_by = NULL;

-- VERIFICATION QUERIES

-- Check current foreign key constraints on document_analysis table
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'document_analysis'
    AND tc.table_schema = 'public';

-- Check for any remaining constraint violations
SELECT 
    da.id,
    da.created_by,
    da.file_name,
    da.created_at
FROM public.document_analysis da
LEFT JOIN auth.users u ON da.created_by = u.id
WHERE da.created_by IS NOT NULL 
    AND u.id IS NULL;
