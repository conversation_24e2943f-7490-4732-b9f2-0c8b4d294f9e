-- ============================================================================
-- QUICK SYSTEM SETUP - Essential Tables Only
-- Run this first to get the system working immediately
-- ============================================================================

-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- ESSENTIAL TABLES FOR IMMEDIATE FUNCTIONALITY
-- ============================================================================

-- 1. Departments (required for profiles)
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Profiles (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    email TEXT UNIQUE,
    role TEXT DEFAULT 'staff' CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin')),
    department_id UUID REFERENCES public.departments(id),
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    avatar_url TEXT,
    phone TEXT,
    position TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Memos (for communication)
CREATE TABLE IF NOT EXISTS public.memos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    subject TEXT,
    purpose TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('draft', 'published', 'archived', 'pending', 'approved', 'rejected')),
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    from_user UUID REFERENCES public.profiles(id),
    to_recipient TEXT,
    department_id UUID REFERENCES public.departments(id),
    department TEXT,
    memo_date DATE,
    account_details TEXT,
    total_amount DECIMAL DEFAULT 0,
    payment_items JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Battery Types (for battery management)
CREATE TABLE IF NOT EXISTS public.battery_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    voltage DECIMAL(5,2) NOT NULL,
    capacity_ah DECIMAL(8,2) NOT NULL,
    chemistry VARCHAR(50) NOT NULL,
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id)
);

-- 5. Battery Locations
CREATE TABLE IF NOT EXISTS public.battery_locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    location_type VARCHAR(50) NOT NULL,
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id)
);

-- 6. Batteries (main inventory)
CREATE TABLE IF NOT EXISTS public.batteries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    serial_number VARCHAR(100) NOT NULL UNIQUE,
    battery_type_id UUID NOT NULL REFERENCES public.battery_types(id),
    current_location_id UUID REFERENCES public.battery_locations(id),
    status VARCHAR(50) NOT NULL DEFAULT 'new',
    condition VARCHAR(50) NOT NULL DEFAULT 'excellent',
    purchase_date DATE,
    installation_date DATE,
    warranty_expiry_date DATE,
    purchase_cost DECIMAL(10,2),
    supplier VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES public.profiles(id),
    updated_by UUID REFERENCES public.profiles(id),
    profile_id UUID REFERENCES public.profiles(id)
);

-- ============================================================================
-- ESSENTIAL SAMPLE DATA
-- ============================================================================

-- Insert departments
INSERT INTO public.departments (id, name, description) VALUES
    ('********-1111-1111-1111-********1111', 'IT Department', 'Information Technology'),
    ('********-2222-2222-2222-********2222', 'HR Department', 'Human Resources'),
    ('********-3333-3333-3333-********3333', 'Finance Department', 'Finance and Accounting'),
    ('********-4444-4444-4444-********4444', 'Operations', 'Field Operations'),
    ('********-5555-5555-5555-********5555', 'Engineering', 'Technical Engineering')
ON CONFLICT (id) DO NOTHING;

-- Insert battery types
INSERT INTO public.battery_types (name, description, voltage, capacity_ah, chemistry, manufacturer, model) VALUES
    ('Deep Cycle 12V 100Ah', 'Standard deep cycle battery', 12.0, 100.0, 'Lead-acid', 'Trojan', 'T-105'),
    ('Lithium 48V 200Ah', 'High capacity lithium battery', 48.0, 200.0, 'Lithium-ion', 'Tesla', 'Powerwall'),
    ('AGM 12V 75Ah', 'AGM battery for UPS systems', 12.0, 75.0, 'AGM', 'Optima', 'D75')
ON CONFLICT (name) DO NOTHING;

-- Insert battery locations
INSERT INTO public.battery_locations (name, description, location_type, address) VALUES
    ('Main Warehouse', 'Central storage facility', 'Warehouse', '123 Industrial Ave, Lagos'),
    ('Site Alpha', 'Telecom tower site Alpha', 'Site', 'Alpha Tower, Abuja'),
    ('Site Beta', 'Telecom tower site Beta', 'Site', 'Beta Tower, Port Harcourt'),
    ('Mobile Unit 1', 'Field service vehicle', 'Vehicle', 'Mobile unit'),
    ('Workshop', 'Maintenance facility', 'Workshop', '456 Service Road, Lagos')
ON CONFLICT (name) DO NOTHING;

-- ============================================================================
-- ESSENTIAL INDEXES
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_memos_created_by ON public.memos(created_by);
CREATE INDEX IF NOT EXISTS idx_memos_status ON public.memos(status);
CREATE INDEX IF NOT EXISTS idx_batteries_serial_number ON public.batteries(serial_number);
CREATE INDEX IF NOT EXISTS idx_batteries_status ON public.batteries(status);

-- ============================================================================
-- ENABLE RLS (Basic)
-- ============================================================================

ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.batteries ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Users can view own profile" ON public.profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Admins can view all profiles" ON public.profiles FOR SELECT USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role = 'admin')
);

CREATE POLICY "Users can view memos" ON public.memos FOR SELECT USING (
    created_by = auth.uid() OR 
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Users can view batteries" ON public.batteries FOR SELECT USING (
    created_by = auth.uid() OR 
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Users can create batteries" ON public.batteries FOR INSERT WITH CHECK (
    created_by = auth.uid()
);

-- ============================================================================
-- VERIFICATION FUNCTION
-- ============================================================================

CREATE OR REPLACE FUNCTION quick_setup_status()
RETURNS TABLE(
    component TEXT,
    status TEXT,
    count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'Departments'::TEXT,
        CASE WHEN (SELECT COUNT(*) FROM public.departments) > 0 THEN '✅ Ready' ELSE '❌ Empty' END,
        (SELECT COUNT(*) FROM public.departments)
    UNION ALL
    SELECT 
        'Battery Types'::TEXT,
        CASE WHEN (SELECT COUNT(*) FROM public.battery_types) > 0 THEN '✅ Ready' ELSE '❌ Empty' END,
        (SELECT COUNT(*) FROM public.battery_types)
    UNION ALL
    SELECT 
        'Battery Locations'::TEXT,
        CASE WHEN (SELECT COUNT(*) FROM public.battery_locations) > 0 THEN '✅ Ready' ELSE '❌ Empty' END,
        (SELECT COUNT(*) FROM public.battery_locations)
    UNION ALL
    SELECT 
        'Profiles'::TEXT,
        CASE WHEN (SELECT COUNT(*) FROM public.profiles) > 0 THEN '✅ Has Users' ELSE '⚠️ No Users Yet' END,
        (SELECT COUNT(*) FROM public.profiles)
    UNION ALL
    SELECT 
        'Batteries'::TEXT,
        CASE WHEN (SELECT COUNT(*) FROM public.batteries) > 0 THEN '✅ Has Inventory' ELSE '⚠️ No Batteries Yet' END,
        (SELECT COUNT(*) FROM public.batteries);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '🚀 QUICK SETUP COMPLETE!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Essential tables created';
    RAISE NOTICE '✅ Sample data inserted';
    RAISE NOTICE '✅ Basic security enabled';
    RAISE NOTICE '';
    RAISE NOTICE '🔋 Battery Management: Ready';
    RAISE NOTICE '📝 Memo System: Ready';
    RAISE NOTICE '👥 User Profiles: Ready';
    RAISE NOTICE '';
    RAISE NOTICE 'Check status: SELECT * FROM quick_setup_status();';
    RAISE NOTICE '';
    RAISE NOTICE '🎯 Next: Run complete-system-setup.sql for full features';
END $$;
