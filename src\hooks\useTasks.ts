
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface Task {
  id: string;
  title: string;
  description: string | null;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to_id: string | null;
  created_by_id: string;
  due_date: string | null;
  estimated_hours: number | null;
  actual_hours: number | null;
  project_id: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
  assignee?: {
    full_name: string;
    email: string;
    role: string;
  };
  creator?: {
    full_name: string;
    email: string;
    role: string;
  };
  project?: {
    name: string;
    client_name: string;
  };
  department?: {
    name: string;
  };
}

interface CreateTaskData {
  title: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  assigned_to_id?: string;
  due_date?: string;
  estimated_hours?: number;
  project_id?: string;
  department_id?: string;
}

export const useTasks = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const fetchTasks = async () => {
    setLoading(true);
    try {
      // Try new schema first (assigned_to_id, created_by)
      let { data, error } = await supabase
        .from('tasks')
        .select(`
          *,
          assignee:profiles!tasks_assigned_to_id_fkey (
            id,
            full_name,
            email,
            role
          ),
          creator:profiles!tasks_created_by_fkey (
            id,
            full_name,
            email,
            role
          ),
          project:projects (
            id,
            name,
            client_name
          ),
          department:departments (
            id,
            name
          )
        `)
        .order('created_at', { ascending: false });

      // If foreign key relationships fail, try basic query
      if (error && (error.code === 'PGRST116' || error.message?.includes('foreign key'))) {
        console.log('Foreign key relationships failed, trying basic query...');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('tasks')
          .select('*')
          .order('created_at', { ascending: false });

        if (fallbackError) throw fallbackError;

        // Transform basic data
        const basicTasks: Task[] = (fallbackData || []).map(task => ({
          id: task.id,
          title: task.title,
          description: task.description,
          status: task.status as 'pending' | 'in_progress' | 'completed' | 'cancelled',
          priority: task.priority as 'low' | 'medium' | 'high' | 'urgent',
          assigned_to_id: task.assigned_to_id || task.assigned_to, // Handle both column names
          created_by_id: task.created_by || task.created_by_id, // Handle both column names
          due_date: task.due_date,
          estimated_hours: task.estimated_hours,
          actual_hours: task.actual_hours,
          project_id: task.project_id,
          department_id: task.department_id,
          created_at: task.created_at,
          updated_at: task.updated_at,
        }));

        setTasks(basicTasks);
        return;
      }

      if (error) {
        console.error('Tasks fetch error:', error);
        throw error;
      }
      
      const transformedTasks: Task[] = (data || []).map(task => ({
        id: task.id,
        title: task.title,
        description: task.description,
        status: task.status as 'pending' | 'in_progress' | 'completed' | 'cancelled',
        priority: task.priority as 'low' | 'medium' | 'high' | 'urgent',
        assigned_to_id: task.assigned_to_id,
        created_by_id: task.created_by_id,
        due_date: task.due_date,
        estimated_hours: task.estimated_hours,
        actual_hours: task.actual_hours,
        project_id: task.project_id,
        department_id: task.department_id,
        created_at: task.created_at,
        updated_at: task.updated_at,
        assignee: Array.isArray(task.assignee) ? task.assignee[0] : task.assignee,
        creator: Array.isArray(task.creator) ? task.creator[0] : task.creator,
        project: Array.isArray(task.project) ? task.project[0] : task.project,
        department: Array.isArray(task.department) ? task.department[0] : task.department
      }));
      
      setTasks(transformedTasks);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast({
        title: "Error",
        description: "Failed to fetch tasks",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  const createTask = async (data: CreateTaskData) => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Prepare task data with proper validation
      const taskData = {
        title: data.title?.trim(),
        description: data.description?.trim() || null,
        priority: data.priority || 'medium',
        status: 'pending',
        assigned_to_id: data.assigned_to_id || null,
        project_id: data.project_id || null,
        department_id: data.department_id || null,
        due_date: data.due_date || null,
        estimated_hours: data.estimated_hours || null,
        created_by_id: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Validate required fields
      if (!taskData.title) {
        throw new Error('Task title is required');
      }

      const { data: newTask, error } = await supabase
        .from('tasks')
        .insert([taskData])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success",
        description: "Task created successfully",
      });

      await fetchTasks();
      return { success: true, data: newTask };
    } catch (error: any) {
      console.error('Error creating task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create task",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const updateTask = async (taskId: string, updates: Partial<CreateTaskData & { status: Task['status']; actual_hours: number }>) => {
    setLoading(true);
    try {
      // Prepare update data with validation
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Clean up undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const { data, error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success",
        description: "Task updated successfully",
      });

      await fetchTasks();
      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating task:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update task",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const deleteTask = async (taskId: string) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Task deleted successfully",
      });

      await fetchTasks();
      return { success: true };
    } catch (error) {
      console.error('Error deleting task:', error);
      toast({
        title: "Error",
        description: "Failed to delete task",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  return {
    tasks,
    loading,
    createTask,
    updateTask,
    deleteTask,
    refetch: fetchTasks
  };
};
