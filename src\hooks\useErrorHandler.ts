/**
 * React Hooks for Error Handling
 * Provides convenient hooks for error handling in React components
 */

import { useCallback, useState, useEffect } from 'react';
import { errorHandler, handleError, type ErrorContext, type AppError } from '@/lib/error-handler';
import { logger } from '@/lib/logger';
import { toast } from 'sonner';

// Hook for handling errors in components
export const useErrorHandler = (context?: string) => {
  const [lastError, setLastError] = useState<AppError | null>(null);

  const handleComponentError = useCallback((
    error: Error | string,
    additionalContext?: Record<string, any>,
    showToast = true
  ) => {
    const errorContext: ErrorContext = {
      component: context,
      metadata: additionalContext,
    };

    const appError = handleError(error, errorContext, showToast);
    setLastError(appError);
    
    return appError;
  }, [context]);

  const clearError = useCallback(() => {
    setLastError(null);
  }, []);

  return {
    handleError: handleComponentError,
    lastError,
    clearError,
    hasError: !!lastError,
  };
};

// Hook for handling async operations with error handling
export const useAsyncError = (context?: string) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const { handleError } = useErrorHandler(context);

  const executeAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName?: string,
    showToast = true
  ): Promise<T | null> => {
    setIsLoading(true);
    setError(null);

    try {
      logger.debug('system', `Starting async operation: ${operationName || 'unknown'}`, {
        component: context,
      });

      const result = await operation();
      
      logger.info('system', `Async operation completed: ${operationName || 'unknown'}`, {
        component: context,
      });

      return result;
    } catch (err) {
      const appError = handleError(
        err as Error,
        { 
          function: operationName,
          metadata: { isAsync: true }
        },
        showToast
      );
      setError(appError);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [context, handleError]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    executeAsync,
    isLoading,
    error,
    clearError,
    hasError: !!error,
  };
};

// Hook for handling form validation errors
export const useFormErrorHandler = (formName?: string) => {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [formError, setFormError] = useState<string | null>(null);
  const { handleError } = useErrorHandler(`Form:${formName}`);

  const setFieldError = useCallback((field: string, message: string) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: message,
    }));

    logger.debug('ui', `Form validation error for field: ${field}`, {
      component: `Form:${formName}`,
      field,
      message,
    });
  }, [formName]);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
    setFormError(null);
  }, []);

  const handleFormError = useCallback((error: Error | string, showToast = true) => {
    const message = typeof error === 'string' ? error : error.message;
    setFormError(message);
    
    handleError(error, { function: 'form-submission' }, showToast);
  }, [handleError]);

  const validateField = useCallback((
    field: string,
    value: any,
    validators: Array<(value: any) => string | null>
  ): boolean => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) {
        setFieldError(field, error);
        return false;
      }
    }
    clearFieldError(field);
    return true;
  }, [setFieldError, clearFieldError]);

  return {
    fieldErrors,
    formError,
    setFieldError,
    clearFieldError,
    clearAllErrors,
    handleFormError,
    validateField,
    hasFieldErrors: Object.keys(fieldErrors).length > 0,
    hasFormError: !!formError,
  };
};

// Hook for handling API errors
export const useApiErrorHandler = (apiName?: string) => {
  const [apiErrors, setApiErrors] = useState<Record<string, AppError>>({});
  const { handleError } = useErrorHandler(`API:${apiName}`);

  const handleApiError = useCallback((
    endpoint: string,
    error: Error | string,
    method = 'GET',
    showToast = true
  ) => {
    const appError = handleError(
      error,
      {
        function: `${method} ${endpoint}`,
        metadata: { endpoint, method, apiName }
      },
      showToast
    );

    setApiErrors(prev => ({
      ...prev,
      [endpoint]: appError,
    }));

    return appError;
  }, [apiName, handleError]);

  const clearApiError = useCallback((endpoint: string) => {
    setApiErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[endpoint];
      return newErrors;
    });
  }, []);

  const clearAllApiErrors = useCallback(() => {
    setApiErrors({});
  }, []);

  const getApiError = useCallback((endpoint: string) => {
    return apiErrors[endpoint] || null;
  }, [apiErrors]);

  return {
    apiErrors,
    handleApiError,
    clearApiError,
    clearAllApiErrors,
    getApiError,
    hasApiErrors: Object.keys(apiErrors).length > 0,
  };
};

// Hook for retry logic with exponential backoff
export const useRetryHandler = (maxRetries = 3, baseDelay = 1000) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const { handleError } = useErrorHandler('RetryHandler');

  const executeWithRetry = useCallback(async <T>(
    operation: () => Promise<T>,
    operationName?: string
  ): Promise<T | null> => {
    let currentRetry = 0;
    
    while (currentRetry <= maxRetries) {
      try {
        if (currentRetry > 0) {
          setIsRetrying(true);
          const delay = baseDelay * Math.pow(2, currentRetry - 1);
          
          logger.info('system', `Retrying operation: ${operationName}`, {
            attempt: currentRetry,
            delay,
            maxRetries,
          });

          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const result = await operation();
        
        if (currentRetry > 0) {
          logger.info('system', `Operation succeeded after retry: ${operationName}`, {
            successfulAttempt: currentRetry,
          });
          toast.success(`Operation succeeded after ${currentRetry} ${currentRetry === 1 ? 'retry' : 'retries'}`);
        }

        setRetryCount(currentRetry);
        setIsRetrying(false);
        return result;
      } catch (error) {
        currentRetry++;
        
        if (currentRetry > maxRetries) {
          handleError(
            error as Error,
            {
              function: operationName,
              metadata: { 
                totalAttempts: currentRetry,
                maxRetries,
                finalFailure: true 
              }
            }
          );
          setRetryCount(currentRetry - 1);
          setIsRetrying(false);
          return null;
        }

        logger.warn('system', `Operation failed, will retry: ${operationName}`, {
          attempt: currentRetry,
          error: (error as Error).message,
          nextRetryIn: baseDelay * Math.pow(2, currentRetry - 1),
        });
      }
    }

    setIsRetrying(false);
    return null;
  }, [maxRetries, baseDelay, handleError]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
  }, []);

  return {
    executeWithRetry,
    retryCount,
    isRetrying,
    canRetry: retryCount < maxRetries,
    reset,
  };
};

// Hook for error recovery strategies
export const useErrorRecovery = (context?: string) => {
  const [recoveryStrategies, setRecoveryStrategies] = useState<Record<string, () => void>>({});
  const { handleError } = useErrorHandler(context);

  const registerRecoveryStrategy = useCallback((
    errorCode: string,
    strategy: () => void,
    description?: string
  ) => {
    setRecoveryStrategies(prev => ({
      ...prev,
      [errorCode]: strategy,
    }));

    logger.debug('system', `Registered recovery strategy for error: ${errorCode}`, {
      component: context,
      description,
    });
  }, [context]);

  const executeRecovery = useCallback((errorCode: string) => {
    const strategy = recoveryStrategies[errorCode];
    
    if (strategy) {
      try {
        logger.info('system', `Executing recovery strategy for error: ${errorCode}`, {
          component: context,
        });
        
        strategy();
        
        toast.success('Recovery action executed successfully');
      } catch (error) {
        handleError(
          error as Error,
          { function: 'executeRecovery', metadata: { errorCode } }
        );
      }
    } else {
      logger.warn('system', `No recovery strategy found for error: ${errorCode}`, {
        component: context,
      });
    }
  }, [recoveryStrategies, context, handleError]);

  const hasRecoveryStrategy = useCallback((errorCode: string) => {
    return !!recoveryStrategies[errorCode];
  }, [recoveryStrategies]);

  return {
    registerRecoveryStrategy,
    executeRecovery,
    hasRecoveryStrategy,
    availableStrategies: Object.keys(recoveryStrategies),
  };
};

// Hook for error monitoring and analytics
export const useErrorMonitoring = () => {
  const [errorStats, setErrorStats] = useState({
    totalErrors: 0,
    errorsByCategory: {} as Record<string, number>,
    errorsBySeverity: {} as Record<string, number>,
    recentErrors: [] as AppError[],
  });

  useEffect(() => {
    const updateStats = () => {
      const logs = errorHandler.getErrorLog();
      const stats = {
        totalErrors: logs.length,
        errorsByCategory: {} as Record<string, number>,
        errorsBySeverity: {} as Record<string, number>,
        recentErrors: logs.slice(0, 10),
      };

      logs.forEach(error => {
        stats.errorsByCategory[error.category] = (stats.errorsByCategory[error.category] || 0) + 1;
        stats.errorsBySeverity[error.severity] = (stats.errorsBySeverity[error.severity] || 0) + 1;
      });

      setErrorStats(stats);
    };

    // Update stats initially
    updateStats();

    // Update stats every 30 seconds
    const interval = setInterval(updateStats, 30000);

    return () => clearInterval(interval);
  }, []);

  const clearErrorStats = useCallback(() => {
    errorHandler.clearErrorLog();
    setErrorStats({
      totalErrors: 0,
      errorsByCategory: {},
      errorsBySeverity: {},
      recentErrors: [],
    });
  }, []);

  return {
    errorStats,
    clearErrorStats,
  };
};
