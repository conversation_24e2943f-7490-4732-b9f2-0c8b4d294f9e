import { createClient } from '@supabase/supabase-js';
import { Resend } from 'resend';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseKey);

// Initialize Resend with API key from environment
const resendApiKey = process.env.Resend_API_Key || 're_iEWVBukX_AM4BhzUNeyxxVTLdHNrLLGqs';
const resend = new Resend(resendApiKey);

async function testEmailNotifications() {
  console.log('📧 Testing Email Notifications...\n');

  try {
    // Test 1: Send a test email using Resend
    console.log('⏳ Sending test email via Resend API...');

    const emailResult = await resend.emails.send({
      from: 'AI Workboard <<EMAIL>>', // Use verified domain
      to: ['<EMAIL>'], // User's email from memories
      subject: '🧪 Test Email - AI Workboard Notification System',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #dc2626;">🚀 AI Workboard Test Email</h2>
          <p>This is a test email to verify that the notification system is working correctly.</p>
          
          <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>✅ System Status</h3>
            <ul>
              <li>✅ Database: Fully synchronized</li>
              <li>✅ Email API: Connected</li>
              <li>✅ Notifications: Active</li>
            </ul>
          </div>
          
          <p style="color: #6b7280;">
            Sent at: ${new Date().toLocaleString()}<br>
            From: AI Workboard Notification System
          </p>
        </div>
      `,
    });

    if (emailResult.error) {
      console.log(`❌ Email sending failed: ${emailResult.error.message}`);
    } else {
      console.log(`✅ Email sent successfully! ID: ${emailResult.data?.id}`);
    }

    // Test 2: Log email notification in database
    console.log('\n⏳ Logging email notification in database...');
    
    const { data: emailLog, error: emailLogError } = await supabase
      .from('email_notifications')
      .insert({
        type: 'test_notification',
        recipients: ['<EMAIL>'],
        subject: '🧪 Test Email - AI Workboard Notification System',
        body: 'Test email notification sent via Resend API',
        status: emailResult.error ? 'failed' : 'sent',
        error_message: emailResult.error?.message || null,
        sent_at: emailResult.error ? null : new Date().toISOString()
      })
      .select();

    if (emailLogError) {
      console.log(`❌ Failed to log email: ${emailLogError.message}`);
    } else {
      console.log(`✅ Email notification logged in database`);
    }

  } catch (error: any) {
    console.log(`💥 Email test failed: ${error.message}`);
  }
}

async function testInAppNotifications() {
  console.log('\n🔔 Testing In-App Notifications...\n');

  try {
    // Test 1: Get or create a test user
    console.log('⏳ Setting up test user...');
    
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profileError) {
      console.log(`❌ Failed to get profiles: ${profileError.message}`);
      return;
    }

    if (!profiles || profiles.length === 0) {
      console.log('❌ No profiles found in database');
      return;
    }

    const testUser = profiles[0];
    console.log(`✅ Using test user: ${testUser.full_name || testUser.email || testUser.id}`);

    // Test 2: Create in-app notifications
    console.log('\n⏳ Creating test in-app notifications...');

    const testNotifications = [
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '🎉 Welcome to AI Workboard',
        message: 'Your notification system is working perfectly!',
        type: 'info',
        read: false
      },
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '📊 Project Update',
        message: 'A new project has been assigned to you.',
        type: 'success',
        read: false
      },
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '⏰ Time Tracking Reminder',
        message: 'Don\'t forget to clock in for today.',
        type: 'warning',
        read: false
      },
      {
        user_id: testUser.id,
        recipient_id: testUser.id,
        title: '🔋 Battery Alert',
        message: 'Battery BAT-001 requires maintenance.',
        type: 'error',
        read: false
      }
    ];

    const { data: notifications, error: notificationError } = await supabase
      .from('notifications')
      .insert(testNotifications)
      .select();

    if (notificationError) {
      console.log(`❌ Failed to create notifications: ${notificationError.message}`);
    } else {
      console.log(`✅ Created ${notifications?.length || 0} test notifications`);
    }

    // Test 3: Retrieve notifications
    console.log('\n⏳ Retrieving notifications for user...');
    
    const { data: userNotifications, error: retrieveError } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', testUser.id)
      .order('created_at', { ascending: false })
      .limit(10);

    if (retrieveError) {
      console.log(`❌ Failed to retrieve notifications: ${retrieveError.message}`);
    } else {
      console.log(`✅ Retrieved ${userNotifications?.length || 0} notifications`);
      
      if (userNotifications && userNotifications.length > 0) {
        console.log('\n📋 Recent Notifications:');
        userNotifications.forEach((notif, index) => {
          const readStatus = notif.read ? '✅' : '🔔';
          console.log(`   ${readStatus} ${notif.title}`);
          console.log(`      ${notif.message}`);
          console.log(`      Type: ${notif.type} | Created: ${new Date(notif.created_at).toLocaleString()}`);
        });
      }
    }

    // Test 4: Test notification preferences
    console.log('\n⏳ Testing notification preferences...');
    
    const { data: preferences, error: prefError } = await supabase
      .from('notification_preferences')
      .upsert({
        user_id: testUser.id,
        email_enabled: true,
        push_enabled: true,
        in_app_enabled: true,
        notification_types: ['clock_in', 'clock_out', 'overtime_alert', 'late_arrival']
      })
      .select();

    if (prefError) {
      console.log(`❌ Failed to set preferences: ${prefError.message}`);
    } else {
      console.log(`✅ Notification preferences configured`);
    }

    // Test 5: Log activity
    console.log('\n⏳ Testing activity logging...');
    
    const { data: activity, error: activityError } = await supabase
      .from('recent_activity_logs')
      .insert({
        user_id: testUser.id,
        action: 'notification_test',
        description: 'Tested notification system functionality',
        entity_type: 'system',
        metadata: {
          test_type: 'notification_system',
          timestamp: new Date().toISOString(),
          notifications_created: notifications?.length || 0
        }
      })
      .select();

    if (activityError) {
      console.log(`❌ Failed to log activity: ${activityError.message}`);
    } else {
      console.log(`✅ Activity logged successfully`);
    }

  } catch (error: any) {
    console.log(`💥 In-app notification test failed: ${error.message}`);
  }
}

async function testNotificationStats() {
  console.log('\n📊 Testing Notification Statistics...\n');

  try {
    // Get notification counts
    const { data: notificationStats, error: statsError } = await supabase
      .from('notifications')
      .select('type, read')
      .then(result => {
        if (result.error) throw result.error;
        
        const stats = {
          total: result.data?.length || 0,
          unread: result.data?.filter(n => !n.read).length || 0,
          byType: result.data?.reduce((acc: any, n) => {
            acc[n.type] = (acc[n.type] || 0) + 1;
            return acc;
          }, {}) || {}
        };
        
        return { data: stats, error: null };
      });

    if (statsError) {
      console.log(`❌ Failed to get notification stats: ${statsError.message}`);
    } else {
      console.log('📈 Notification Statistics:');
      console.log(`   📧 Total notifications: ${notificationStats.data.total}`);
      console.log(`   🔔 Unread notifications: ${notificationStats.data.unread}`);
      console.log(`   📊 By type:`, notificationStats.data.byType);
    }

    // Get email notification stats
    const { data: emailStats, error: emailStatsError } = await supabase
      .from('email_notifications')
      .select('status')
      .then(result => {
        if (result.error) throw result.error;
        
        const stats = result.data?.reduce((acc: any, n) => {
          acc[n.status] = (acc[n.status] || 0) + 1;
          return acc;
        }, {}) || {};
        
        return { data: stats, error: null };
      });

    if (emailStatsError) {
      console.log(`❌ Failed to get email stats: ${emailStatsError.message}`);
    } else {
      console.log('\n📧 Email Notification Statistics:');
      console.log(`   📊 By status:`, emailStats.data);
    }

  } catch (error: any) {
    console.log(`💥 Stats test failed: ${error.message}`);
  }
}

async function main() {
  console.log('🧪 AI Workboard Notification System Test\n');
  console.log('=' .repeat(50));
  
  await testEmailNotifications();
  await testInAppNotifications();
  await testNotificationStats();
  
  console.log('\n' + '=' .repeat(50));
  console.log('🏁 Notification testing completed!');
  console.log('💡 Check your email and the application for test notifications.');
}

main().catch((error) => {
  console.error('💥 Test script failed:', error);
  process.exit(1);
});
