import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Plus, User, Mail, Lock, UserCheck } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";

interface CreateUserFormProps {
  departments: any[];
  onUserCreated: () => void;
}

export const CreateUserForm = ({ departments, onUserCreated }: CreateUserFormProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);
  const [formData, setFormData] = useState({
    email: "",
    fullName: "",
    role: "staff",
    departmentId: "",
    temporaryPassword: "",
    generatePassword: true
  });
  const { toast } = useToast();

  const generateRandomPassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!formData.email || !formData.fullName) {
        throw new Error("Please fill in all required fields");
      }

      // Generate password if needed
      const password = formData.generatePassword ? generateRandomPassword() : formData.temporaryPassword;
      
      if (!password) {
        throw new Error("Password is required");
      }

      // Create user account
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: formData.email,
        password: password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`,
          data: {
            full_name: formData.fullName,
            role: formData.role,
            account_type: formData.role,
            department_id: formData.departmentId || null
          }
        }
      });

      if (authError) throw authError;

      // Update profile with additional information
      if (authData.user) {
        // Wait a moment for the profile to be created by the trigger
        await new Promise(resolve => setTimeout(resolve, 1000));

        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            full_name: formData.fullName,
            role: formData.role,
            account_type: formData.role,
            department_id: formData.departmentId || null,
            status: 'active',
            updated_at: new Date().toISOString()
          })
          .eq('id', authData.user.id);

        if (profileError) {
          console.error('Error updating profile:', profileError);
          // Try to insert if update fails (profile might not exist yet)
          const { error: insertError } = await supabase
            .from('profiles')
            .insert({
              id: authData.user.id,
              full_name: formData.fullName,
              email: formData.email,
              role: formData.role,
              account_type: formData.role,
              department_id: formData.departmentId || null,
              status: 'active'
            });

          if (insertError) {
            console.error('Error inserting profile:', insertError);
          }
        }
      }

      toast({
        title: "User Created Successfully",
        description: `User ${formData.fullName} has been created. ${sendWelcomeEmail ? 'A welcome email has been sent.' : 'Please share the login credentials manually.'}`,
      });

      // Show password if generated
      if (formData.generatePassword) {
        toast({
          title: "Temporary Password Generated",
          description: `Password: ${password} - Please share this securely with the user.`,
          duration: 10000,
        });
      }

      // Reset form and close dialog
      setFormData({
        email: "",
        fullName: "",
        role: "staff",
        departmentId: "",
        temporaryPassword: "",
        generatePassword: true
      });
      setIsOpen(false);
      onUserCreated();

    } catch (error: any) {
      console.error('Error creating user:', error);
      toast({
        title: "Error Creating User",
        description: error.message || "Failed to create user account",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Create New User
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserCheck className="h-5 w-5" />
            Create New User Account
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fullName" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Full Name *
            </Label>
            <Input
              id="fullName"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value)}
              placeholder="Enter full name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email Address *
            </Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter email address"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="role">Role</Label>
            <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="staff">Staff</SelectItem>
                <SelectItem value="manager">Manager</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="accountant">Accountant</SelectItem>
                <SelectItem value="staff-admin">Staff Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="department">Department</Label>
            <Select value={formData.departmentId} onValueChange={(value) => handleInputChange('departmentId', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((dept) => (
                  <SelectItem key={dept.id} value={String(dept.id)}>
                    {dept.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="generatePassword"
                checked={formData.generatePassword}
                onCheckedChange={(checked) => handleInputChange('generatePassword', checked)}
              />
              <Label htmlFor="generatePassword" className="text-sm">
                Generate temporary password automatically
              </Label>
            </div>

            {!formData.generatePassword && (
              <div className="space-y-2">
                <Label htmlFor="temporaryPassword" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  Temporary Password *
                </Label>
                <Input
                  id="temporaryPassword"
                  type="password"
                  value={formData.temporaryPassword}
                  onChange={(e) => handleInputChange('temporaryPassword', e.target.value)}
                  placeholder="Enter temporary password"
                  required={!formData.generatePassword}
                />
              </div>
            )}

            <div className="flex items-center space-x-2">
              <Checkbox
                id="sendWelcomeEmail"
                checked={sendWelcomeEmail}
                onCheckedChange={setSendWelcomeEmail}
              />
              <Label htmlFor="sendWelcomeEmail" className="text-sm">
                Send welcome email with login instructions
              </Label>
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="submit" disabled={loading} className="flex-1">
              {loading ? "Creating..." : "Create User"}
            </Button>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
