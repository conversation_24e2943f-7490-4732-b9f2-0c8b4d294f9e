import { Database } from './base';

export interface Project {
  id: string;
  name: string;
  description: string | null;
  client_name: string | null;
  budget: number | null;
  budget_spent: number | null;
  location: string | null;
  start_date: string | null;
  end_date: string | null;
  actual_end_date: string | null;
  status: string;
  priority: string | null;
  manager_id: string | null;
  department_id: string | null;
  progress_percentage: number | null;
  completion_percentage: number | null;
  actual_hours: number | null;
  estimated_hours: number | null;
  health_score: number | null;
  risk_level: string | null;
  category: string | null;
  tags: string[] | null;
  template_id: string | null;
  metadata: any | null;
  last_activity_at: string | null;
  archived_at: string | null;
  archived_by: string | null;
  created_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface ProjectAssignment {
  id: string;
  project_id: string | null;
  staff_id: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export type ProjectWithAssignments = Project & {
  project_assignments: ProjectAssignment[];
};

export type ProjectAssignmentInsert = Database['public']['Tables']['project_assignments']['Insert'];