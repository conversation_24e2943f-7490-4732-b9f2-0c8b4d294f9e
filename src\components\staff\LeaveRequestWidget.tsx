import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Plus, FileText } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { safeSupabaseQuery } from "@/utils/supabaseErrorHandler";
import { format } from "date-fns";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { EnhancedLeaveApplication } from "./EnhancedLeaveApplication";

interface LeaveRequest {
  id: string;
  leave_type: string;
  start_date: string;
  end_date: string;
  status: string;
  created_at: string;
}

interface LeaveBalance {
  leave_type: string;
  remaining_days: number;
}

export const LeaveRequestWidget = () => {
  const { userProfile } = useAuth();
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch recent leave requests
  const { data: recentRequests, isLoading: requestsLoading } = useQuery({
    queryKey: ['recent-leave-requests', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];
      
      const { data, error } = await supabase
        .from('leave_requests')
        .select('*')
        .eq('user_id', userProfile.id)
        .order('created_at', { ascending: false })
        .limit(3);

      if (error) throw error;
      return data as LeaveRequest[];
    },
    enabled: !!userProfile?.id,
  });

  // Fetch leave balance with enhanced error handling using safeSupabaseQuery
  const { data: leaveBalance } = useQuery({
    queryKey: ['leave-balance', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return null;

      const currentYear = new Date().getFullYear();

      // Use the safe query wrapper to handle RLS and CORS errors
      const result = await safeSupabaseQuery(
        () => supabase
          .from('leave_balances')
          .select('*')
          .eq('user_id', userProfile.id)
          .eq('leave_type', 'annual')
          .eq('year', currentYear)
          .maybeSingle(),
        'leave_balances',
        userProfile.id,
        { enableFallback: true, logErrors: true }
      );

      // If we got fallback data or an error, use the first item from fallback array
      if (result.usedFallback && Array.isArray(result.data)) {
        const annualBalance = result.data.find(balance => balance.leave_type === 'annual');
        return annualBalance || result.data[0];
      }

      // If we have real data, return it
      if (result.data) {
        return result.data as LeaveBalance;
      }

      // Final fallback
      return {
        leave_type: 'annual',
        remaining_days: 25,
        total_days: 25,
        used_days: 0,
        year: currentYear
      };
    },
    enabled: !!userProfile?.id,
  });

  const getStatusBadge = (status: string) => {
    const configs = {
      pending: { variant: "secondary" as const, color: "text-yellow-600" },
      approved: { variant: "default" as const, color: "text-green-600" },
      rejected: { variant: "destructive" as const, color: "text-red-600" }
    };
    
    const config = configs[status as keyof typeof configs] || configs.pending;
    
    return (
      <Badge variant={config.variant} className="text-xs">
        {status.toUpperCase()}
      </Badge>
    );
  };

  const calculateLeaveDays = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let count = 0;
    const current = new Date(start);

    while (current <= end) {
      const dayOfWeek = current.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Exclude weekends
        count++;
      }
      current.setDate(current.getDate() + 1);
    }
    
    return count;
  };

  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Calendar className="h-5 w-5 text-blue-600" />
            Leave Management
          </CardTitle>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-1" />
                Request Leave
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Leave Request Application</DialogTitle>
              </DialogHeader>
              <EnhancedLeaveApplication />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Leave Balance */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-700">Annual Leave Balance</p>
              <p className="text-2xl font-bold text-blue-800">
                {leaveBalance?.remaining_days || 0} days
              </p>
              <p className="text-xs text-blue-600">remaining this year</p>
            </div>
            <Calendar className="h-8 w-8 text-blue-500" />
          </div>
        </div>

        {/* Recent Requests */}
        <div>
          <div className="flex items-center gap-2 mb-3">
            <FileText className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Recent Requests</span>
          </div>
          
          {requestsLoading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-xs text-muted-foreground mt-2">Loading...</p>
            </div>
          ) : recentRequests && recentRequests.length > 0 ? (
            <div className="space-y-2">
              {recentRequests.map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium capitalize">
                        {request.leave_type.replace('_', ' ')} Leave
                      </span>
                      {getStatusBadge(request.status)}
                    </div>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {format(new Date(request.start_date), 'MMM dd')} - {format(new Date(request.end_date), 'MMM dd')}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {calculateLeaveDays(request.start_date, request.end_date)} days
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No leave requests yet</p>
              <p className="text-xs">Submit your first leave request above</p>
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="pt-2 border-t">
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full"
            onClick={() => setIsDialogOpen(true)}
          >
            <FileText className="h-4 w-4 mr-2" />
            View All Leave Requests
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}; 