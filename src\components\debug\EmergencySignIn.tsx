/**
 * Emergency Sign-In Component
 * Simple, direct sign-in without complex state management
 */

import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertTriangle, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const EmergencySignIn: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const navigate = useNavigate();

  const handleDirectSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🚨 Emergency sign-in attempt...');
      
      // Direct Supabase sign-in without auth provider
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password.trim(),
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        console.log('✅ Emergency sign-in successful:', data.user.id);
        setSuccess('Sign-in successful! Redirecting...');
        
        // Get user profile to determine role
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', data.user.id)
          .single();

        let redirectPath = '/dashboard/staff'; // Default
        
        if (!profileError && profile) {
          const roleRoutes = {
            admin: '/dashboard/admin',
            manager: '/dashboard/manager',
            staff: '/dashboard/staff',
            accountant: '/dashboard/accountant',
            'staff-admin': '/dashboard/staff-admin'
          };
          redirectPath = roleRoutes[profile.role as keyof typeof roleRoutes] || '/dashboard/staff';
        }

        // Force redirect
        setTimeout(() => {
          window.location.href = redirectPath;
        }, 1500);
      }
    } catch (error: any) {
      console.error('🚨 Emergency sign-in failed:', error);
      setError(error.message || 'Sign-in failed');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('🔍 Testing Supabase connection...');
      
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        throw error;
      }

      setSuccess('Supabase connection is working!');
      console.log('✅ Supabase connection test successful');
    } catch (error: any) {
      console.error('❌ Supabase connection test failed:', error);
      setError(`Connection failed: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleClearAuth = async () => {
    try {
      console.log('🧹 Clearing authentication state...');
      
      // Sign out from Supabase
      await supabase.auth.signOut();
      
      // Clear local storage
      localStorage.clear();
      sessionStorage.clear();
      
      setSuccess('Authentication cleared! You can now try signing in again.');
      setEmail('');
      setPassword('');
    } catch (error: any) {
      console.error('❌ Failed to clear auth:', error);
      setError(`Failed to clear auth: ${error.message}`);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6">
      <Card className="border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertTriangle className="h-5 w-5" />
            Emergency Sign-In
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              This is a direct sign-in bypass for troubleshooting authentication issues.
            </AlertDescription>
          </Alert>

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-700 dark:text-green-300">
                {success}
              </AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleDirectSignIn} className="space-y-4">
            <div>
              <label htmlFor="emergency-email" className="block text-sm font-medium mb-1">
                Email
              </label>
              <Input
                id="emergency-email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                required
                disabled={loading}
              />
            </div>

            <div>
              <label htmlFor="emergency-password" className="block text-sm font-medium mb-1">
                Password
              </label>
              <Input
                id="emergency-password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                required
                disabled={loading}
              />
            </div>

            <Button 
              type="submit" 
              className="w-full bg-red-600 hover:bg-red-700"
              disabled={loading || !email || !password}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Signing In...
                </>
              ) : (
                'Emergency Sign In'
              )}
            </Button>
          </form>

          <div className="space-y-2">
            <Button 
              variant="outline" 
              onClick={handleTestConnection}
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Connection'
              )}
            </Button>

            <Button 
              variant="outline" 
              onClick={handleClearAuth}
              className="w-full"
              disabled={loading}
            >
              Clear Auth State
            </Button>

            <Button 
              variant="ghost" 
              onClick={() => navigate('/auth')}
              className="w-full"
            >
              Back to Normal Sign In
            </Button>
          </div>

          <div className="text-xs text-muted-foreground">
            <p><strong>Debug Info:</strong></p>
            <p>Supabase URL: {supabase.supabaseUrl}</p>
            <p>Current URL: {window.location.href}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
