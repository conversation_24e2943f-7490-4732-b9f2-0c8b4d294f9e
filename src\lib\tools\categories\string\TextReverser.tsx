import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, ArrowLeftRight, Shuffle } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const TextReverser: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [reverseMode, setReverseMode] = useState('characters');
  const [preserveSpaces, setPreserveSpaces] = useState(false);
  const [preserveCase, setPreserveCase] = useState(false);
  const [reverseLines, setReverseLines] = useState(false);
  const [result, setResult] = useState('');

  const reverseModes = [
    { value: 'characters', label: 'Characters', description: 'Reverse character by character' },
    { value: 'words', label: 'Words', description: 'Reverse word order' },
    { value: 'sentences', label: 'Sentences', description: 'Reverse sentence order' },
    { value: 'lines', label: 'Lines', description: 'Reverse line order' },
    { value: 'paragraphs', label: 'Paragraphs', description: 'Reverse paragraph order' }
  ];

  const performReverse = () => {
    if (!inputText.trim()) {
      setResult('');
      return;
    }

    let reversed = '';

    try {
      switch (reverseMode) {
        case 'characters':
          if (preserveSpaces) {
            // Reverse only non-space characters while keeping spaces in place
            const chars = inputText.split('');
            const nonSpaceChars = chars.filter(char => char !== ' ').reverse();
            let nonSpaceIndex = 0;
            
            reversed = chars.map(char => {
              if (char === ' ') {
                return ' ';
              } else {
                return nonSpaceChars[nonSpaceIndex++];
              }
            }).join('');
          } else {
            reversed = inputText.split('').reverse().join('');
          }
          break;

        case 'words':
          const words = inputText.split(/(\s+)/);
          const wordTokens = words.filter((_, index) => index % 2 === 0).reverse();
          const spaceTokens = words.filter((_, index) => index % 2 === 1);
          
          reversed = wordTokens.reduce((acc, word, index) => {
            acc += word;
            if (index < spaceTokens.length) {
              acc += spaceTokens[index];
            }
            return acc;
          }, '');
          break;

        case 'sentences':
          const sentences = inputText.split(/([.!?]+\s*)/).filter(s => s.trim());
          const sentenceContent = sentences.filter((_, index) => index % 2 === 0).reverse();
          const sentenceDelimiters = sentences.filter((_, index) => index % 2 === 1);
          
          reversed = sentenceContent.reduce((acc, sentence, index) => {
            acc += sentence;
            if (index < sentenceDelimiters.length) {
              acc += sentenceDelimiters[index];
            }
            return acc;
          }, '');
          break;

        case 'lines':
          const lines = inputText.split('\n');
          reversed = lines.reverse().join('\n');
          break;

        case 'paragraphs':
          const paragraphs = inputText.split(/\n\s*\n/);
          reversed = paragraphs.reverse().join('\n\n');
          break;

        default:
          reversed = inputText.split('').reverse().join('');
      }

      // Apply additional transformations
      if (reverseLines && reverseMode !== 'lines') {
        const lines = reversed.split('\n');
        reversed = lines.reverse().join('\n');
      }

      if (preserveCase && reverseMode === 'characters') {
        // Preserve the case pattern of the original text
        const originalChars = inputText.split('');
        const reversedChars = reversed.split('');
        
        reversed = reversedChars.map((char, index) => {
          if (index < originalChars.length) {
            const originalChar = originalChars[index];
            if (originalChar === originalChar.toUpperCase() && originalChar !== originalChar.toLowerCase()) {
              return char.toUpperCase();
            } else if (originalChar === originalChar.toLowerCase() && originalChar !== originalChar.toUpperCase()) {
              return char.toLowerCase();
            }
          }
          return char;
        }).join('');
      }

      setResult(reversed);
    } catch (error) {
      toast.error('Error reversing text');
      setResult('');
    }
  };

  useEffect(() => {
    performReverse();
  }, [inputText, reverseMode, preserveSpaces, preserveCase, reverseLines]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
  };

  const swapInputOutput = () => {
    if (result) {
      setInputText(result);
    }
  };

  const loadSampleText = () => {
    const samples = [
      "Hello World! How are you today?",
      "The quick brown fox jumps over the lazy dog.",
      "Line 1\nLine 2\nLine 3\nLine 4",
      "First paragraph here.\n\nSecond paragraph here.\n\nThird paragraph here."
    ];
    const randomSample = samples[Math.floor(Math.random() * samples.length)];
    setInputText(randomSample);
  };

  const getStats = () => {
    const originalLength = inputText.length;
    const resultLength = result.length;
    const originalWords = inputText.split(/\s+/).filter(word => word.length > 0).length;
    const resultWords = result.split(/\s+/).filter(word => word.length > 0).length;
    
    return {
      originalLength,
      resultLength,
      originalWords,
      resultWords,
      mode: reverseModes.find(mode => mode.value === reverseMode)?.label || 'Unknown'
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-pink-50 via-rose-50 to-red-50 border-pink-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-pink-800">
            <div className="p-2 bg-pink-100 rounded-lg">
              <ArrowLeftRight className="h-6 w-6 text-pink-600" />
            </div>
            {title || 'Text Reverser'}
          </CardTitle>
          <p className="text-pink-600 text-sm">
            Reverse text by characters, words, sentences, lines, or paragraphs
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Reverse Mode Selection */}
          <div className="space-y-2">
            <label className="text-sm font-semibold text-gray-700">Reverse Mode:</label>
            <Select value={reverseMode} onValueChange={setReverseMode}>
              <SelectTrigger className="border-2 border-pink-200 focus:border-pink-400">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reverseModes.map(mode => (
                  <SelectItem key={mode.value} value={mode.value}>
                    <div>
                      <div className="font-medium">{mode.label}</div>
                      <div className="text-xs text-gray-500">{mode.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Options */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="preserve-spaces"
                checked={preserveSpaces}
                onCheckedChange={setPreserveSpaces}
                disabled={reverseMode !== 'characters'}
              />
              <label htmlFor="preserve-spaces" className="text-sm font-medium">
                Preserve Spaces
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="preserve-case"
                checked={preserveCase}
                onCheckedChange={setPreserveCase}
                disabled={reverseMode !== 'characters'}
              />
              <label htmlFor="preserve-case" className="text-sm font-medium">
                Preserve Case
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="reverse-lines"
                checked={reverseLines}
                onCheckedChange={setReverseLines}
                disabled={reverseMode === 'lines'}
              />
              <label htmlFor="reverse-lines" className="text-sm font-medium">
                Also Reverse Lines
              </label>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSampleText}
              className="flex items-center gap-2"
            >
              <Shuffle className="h-4 w-4" />
              Sample
            </Button>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <span>📝</span>
              Input Text:
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter your text to reverse..."
              className="min-h-[140px] resize-none border-2 border-pink-200 focus:border-pink-400"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.originalLength} chars
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                📝 {stats.originalWords} words
              </Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                🔄 {stats.mode} mode
              </Badge>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              onClick={swapInputOutput}
              disabled={!result}
              className="flex items-center gap-2"
            >
              <ArrowLeftRight className="h-4 w-4" />
              Swap Input/Output
            </Button>
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>

          {/* Result */}
          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  Reversed Text:
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[140px] bg-gray-50 border-2 border-gray-200"
              />
              
              <div className="flex gap-2 flex-wrap">
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  📊 {stats.resultLength} chars
                </Badge>
                <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                  📝 {stats.resultWords} words
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Reverse Mode Examples
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Characters:</strong> "Hello" → "olleH"</p>
              <p><strong className="text-green-600">Words:</strong> "Hello World" → "World Hello"</p>
              <p><strong className="text-purple-600">Sentences:</strong> "Hi! How are you?" → "How are you? Hi!"</p>
            </div>
            <div className="space-y-2">
              <p><strong className="text-orange-600">Lines:</strong> Reverses line order</p>
              <p><strong className="text-red-600">Paragraphs:</strong> Reverses paragraph order</p>
              <p><strong className="text-indigo-600">Options:</strong> Preserve spaces, case, or reverse lines too</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextReverser;
