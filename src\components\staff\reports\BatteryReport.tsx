import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Battery, Upload, AlertCircle, CheckCircle, Loader2, RefreshCw, TrendingUp, Zap, ThermometerSun } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { BatteryInventory } from "@/components/battery/BatteryInventory";
import { BatterySales } from "@/components/battery/BatterySales";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface BatteryInventoryItem {
  id: string;
  battery_id: string;
  battery_model: string;
  manufacturer: string;
  location: string;
  status: string;
  capacity_kwh: number;
}

interface FormErrors {
  battery_id?: string;
  charge_level?: string;
  voltage_reading?: string;
  temperature?: string;
  maintenance_notes?: string;
}

export const BatteryReport = () => {
  const [selectedBatteryId, setSelectedBatteryId] = useState("");
  const [chargeLevel, setChargeLevel] = useState(100);
  const [batteryStatus, setBatteryStatus] = useState("operational");
  const [maintenanceNotes, setMaintenanceNotes] = useState("");
  const [voltageReading, setVoltageReading] = useState("");
  const [temperature, setTemperature] = useState("");
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch battery inventory with enhanced error handling
  const { data: batteries, isLoading: batteriesLoading, error: batteriesError, refetch: refetchBatteries } = useQuery({
    queryKey: ['battery-inventory'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase
          .from('battery_inventory')
          .select('*')
          .order('battery_id');

        if (error && error.code !== '42P01') {
          throw error;
        }
        
        return data as BatteryInventoryItem[] || [];
      } catch (error) {
        console.warn('Battery inventory not available:', error);
        return [];
      }
    },
    retry: 2,
    retryDelay: 1000
  });

  // Fetch recent battery reports for this user with enhanced error handling
  const { data: recentReports, isLoading: reportsLoading, refetch: refetchReports } = useQuery({
    queryKey: ['recent-battery-reports', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];

      try {
        const { data, error } = await supabase
          .from('battery_reports')
          .select(`
            *,
            battery_inventory!inner(battery_id, battery_model, location)
          `)
          .eq('reported_by', userProfile.id)
          .order('created_at', { ascending: false })
          .limit(5);

        if (error && error.code !== '42P01') {
          throw error;
        }

        return data || [];
      } catch (error) {
        console.warn('Battery reports not available:', error);
        return [];
      }
    },
    enabled: !!userProfile?.id,
    retry: 2,
    retryDelay: 1000
  });

  // Enhanced form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!selectedBatteryId) {
      newErrors.battery_id = 'Please select a battery';
    }

    if (chargeLevel < 0 || chargeLevel > 100) {
      newErrors.charge_level = 'Charge level must be between 0 and 100';
    }

    if (voltageReading && (isNaN(Number(voltageReading)) || Number(voltageReading) < 0)) {
      newErrors.voltage_reading = 'Please enter a valid voltage reading';
    }

    if (temperature && (isNaN(Number(temperature)) || Number(temperature) < -50 || Number(temperature) > 100)) {
      newErrors.temperature = 'Temperature must be between -50°C and 100°C';
    }

    if (!maintenanceNotes.trim()) {
      newErrors.maintenance_notes = 'Maintenance notes are required';
    } else if (maintenanceNotes.length < 10) {
      newErrors.maintenance_notes = 'Maintenance notes must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Submit battery report with enhanced error handling
  const submitReportMutation = useMutation({
    mutationFn: async () => {
      if (!userProfile?.id || !selectedBatteryId) {
        throw new Error("Missing required data");
      }

      const reportData = {
        battery_id: selectedBatteryId,
        charge_level: chargeLevel,
        voltage_reading: voltageReading ? parseFloat(voltageReading) : null,
        temperature: temperature ? parseFloat(temperature) : null,
        status: batteryStatus,
        maintenance_notes: maintenanceNotes,
        reported_by: userProfile.id,
        report_date: new Date().toISOString().split('T')[0],
      };

      const { data, error } = await supabase
        .from('battery_reports')
        .insert([reportData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      toast({
        title: "Battery Report Submitted",
        description: "Your battery report has been submitted successfully.",
      });
      
      // Reset form
      resetForm();
      
      // Refresh reports
      queryClient.invalidateQueries({ queryKey: ['recent-battery-reports'] });
    },
    onError: (error: any) => {
      console.error("Error submitting report:", error);
      
      let errorMessage = "Failed to submit battery report. Please try again.";
      
      if (error.message?.includes('foreign key')) {
        errorMessage = "Invalid battery selection. Please choose a valid battery.";
      } else if (error.message?.includes('permission')) {
        errorMessage = "You don't have permission to submit reports.";
      }
      
      toast({
        title: "Submission Failed",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setSelectedBatteryId("");
    setChargeLevel(100);
    setBatteryStatus("operational");
    setMaintenanceNotes("");
    setVoltageReading("");
    setTemperature("");
    setErrors({});
  };

  const handleSubmitReport = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before submitting the report.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    submitReportMutation.mutate();
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      operational: { color: "bg-green-500/20 text-green-700", icon: CheckCircle, label: "Operational" },
      maintenance: { color: "bg-yellow-500/20 text-yellow-700", icon: AlertCircle, label: "Maintenance" },
      critical: { color: "bg-red-500/20 text-red-700", icon: AlertCircle, label: "Critical" },
      pending: { color: "bg-blue-500/20 text-blue-700", icon: Loader2, label: "Pending" },
      approved: { color: "bg-green-500/20 text-green-700", icon: CheckCircle, label: "Approved" },
      rejected: { color: "bg-red-500/20 text-red-700", icon: AlertCircle, label: "Rejected" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const IconComponent = config.icon;

    return (
      <Badge className={`${config.color} text-xs flex items-center gap-1 border-0`}>
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const getChargeColor = (level: number): string => {
    if (level >= 80) return "text-green-600";
    if (level >= 50) return "text-yellow-600";
    if (level >= 20) return "text-orange-600";
    return "text-red-600";
  };

  return (
    <Tabs defaultValue="report" className="space-y-6">
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="report">Battery Report</TabsTrigger>
        <TabsTrigger value="inventory">Inventory</TabsTrigger>
        <TabsTrigger value="sales">Sales</TabsTrigger>
      </TabsList>

      <TabsContent value="report">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Enhanced Report Form */}
          <Card className="bg-gradient-to-br from-background to-muted/30 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium">
                <Battery className="h-5 w-5 text-primary" />
                Submit Battery Report
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Submit detailed battery performance and maintenance reports
              </p>
            </CardHeader>
            
            <CardContent>
              {batteriesError && (
                <Alert className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Unable to load battery inventory. Some features may be limited.
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => refetchBatteries()}
                      className="ml-2"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Retry
                    </Button>
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmitReport} className="space-y-4">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Battery className="h-4 w-4" />
                    Select Battery *
                  </Label>
                  <Select
                    value={selectedBatteryId}
                    onValueChange={(value) => {
                      setSelectedBatteryId(value);
                      if (errors.battery_id) {
                        setErrors(prev => ({ ...prev, battery_id: undefined }));
                      }
                    }}
                    disabled={batteriesLoading}
                  >
                    <SelectTrigger className={errors.battery_id ? 'border-destructive' : ''}>
                      <SelectValue placeholder={batteriesLoading ? "Loading batteries..." : "Select a battery"} />
                    </SelectTrigger>
                    <SelectContent>
                      {batteries?.map((battery) => (
                        <SelectItem key={battery.id} value={battery.id}>
                          <div className="flex items-center justify-between w-full">
                            <span className="font-medium">{battery.battery_id}</span>
                            <span className="text-sm text-muted-foreground ml-2">
                              {battery.battery_model} • {battery.location}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.battery_id && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.battery_id}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Battery Status *
                  </Label>
                  <Select value={batteryStatus} onValueChange={setBatteryStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="operational">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          Operational
                        </div>
                      </SelectItem>
                      <SelectItem value="maintenance">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-yellow-600" />
                          Needs Maintenance
                        </div>
                      </SelectItem>
                      <SelectItem value="critical">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-600" />
                          Critical Issue
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Charge Level (%) *
                  </Label>
                  <div className="space-y-2">
                    <Input 
                      type="number" 
                      min="0" 
                      max="100" 
                      value={chargeLevel}
                      onChange={(e) => {
                        const value = parseInt(e.target.value) || 0;
                        setChargeLevel(value);
                        if (errors.charge_level) {
                          setErrors(prev => ({ ...prev, charge_level: undefined }));
                        }
                      }}
                      className={`${errors.charge_level ? 'border-destructive' : ''} ${getChargeColor(chargeLevel)}`}
                      required
                    />
                    <Progress value={chargeLevel} className="h-2" />
                    <p className={`text-sm ${getChargeColor(chargeLevel)}`}>
                      {chargeLevel >= 80 ? "Excellent" :
                       chargeLevel >= 50 ? "Good" :
                       chargeLevel >= 20 ? "Low" : "Critical"}
                    </p>
                  </div>
                  {errors.charge_level && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.charge_level}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      Voltage (V)
                    </Label>
                    <Input 
                      type="number" 
                      step="0.1"
                      value={voltageReading}
                      onChange={(e) => {
                        setVoltageReading(e.target.value);
                        if (errors.voltage_reading) {
                          setErrors(prev => ({ ...prev, voltage_reading: undefined }));
                        }
                      }}
                      placeholder="e.g., 12.6"
                      className={errors.voltage_reading ? 'border-destructive' : ''}
                    />
                    {errors.voltage_reading && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.voltage_reading}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <ThermometerSun className="h-4 w-4" />
                      Temperature (°C)
                    </Label>
                    <Input 
                      type="number" 
                      step="0.1"
                      value={temperature}
                      onChange={(e) => {
                        setTemperature(e.target.value);
                        if (errors.temperature) {
                          setErrors(prev => ({ ...prev, temperature: undefined }));
                        }
                      }}
                      placeholder="e.g., 25.0"
                      className={errors.temperature ? 'border-destructive' : ''}
                    />
                    {errors.temperature && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.temperature}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maintenance_notes">Maintenance Notes *</Label>
                  <Textarea
                    id="maintenance_notes"
                    value={maintenanceNotes}
                    onChange={(e) => {
                      setMaintenanceNotes(e.target.value);
                      if (errors.maintenance_notes) {
                        setErrors(prev => ({ ...prev, maintenance_notes: undefined }));
                      }
                    }}
                    className={`min-h-[100px] ${errors.maintenance_notes ? 'border-destructive' : ''}`}
                    placeholder="Enter detailed maintenance observations, issues found, or recommendations..."
                    required
                  />
                  <div className="flex justify-between items-center">
                    {errors.maintenance_notes ? (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.maintenance_notes}
                      </p>
                    ) : (
                      <div />
                    )}
                    <p className="text-sm text-muted-foreground">
                      {maintenanceNotes.length}/500 characters
                    </p>
                  </div>
                </div>

                <Button 
                  type="submit" 
                  className="w-full"
                  disabled={submitReportMutation.isPending || !selectedBatteryId || isSubmitting}
                >
                  {submitReportMutation.isPending || isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Submitting Report...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Submit Battery Report
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Enhanced Recent Reports */}
          <Card className="bg-gradient-to-br from-background to-muted/30 border-secondary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg font-medium">
                <Battery className="h-5 w-5 text-secondary" />
                My Recent Reports
                <Badge variant="outline" className="ml-auto">
                  {recentReports?.length || 0}
                </Badge>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Track your submitted battery reports
              </p>
            </CardHeader>
            <CardContent>
              {reportsLoading ? (
                <div className="space-y-3">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-20 bg-muted/50 rounded-lg animate-pulse" />
                  ))}
                </div>
              ) : recentReports && recentReports.length > 0 ? (
                <div className="space-y-4">
                  {recentReports.map((report: any) => (
                    <Card key={report.id} className="p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <Battery className="h-4 w-4 text-primary" />
                          <span className="font-medium text-sm">
                            {report.battery_inventory?.battery_id || 'Unknown Battery'}
                          </span>
                        </div>
                        {getStatusBadge(report.approval_status)}
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-xs">
                        <div>
                          <p className="text-muted-foreground">Location</p>
                          <p className="font-medium">{report.battery_inventory?.location}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Status</p>
                          <p className="font-medium capitalize">{report.status}</p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Charge Level</p>
                          <p className={`font-medium ${getChargeColor(report.charge_level)}`}>
                            {report.charge_level}%
                          </p>
                        </div>
                        <div>
                          <p className="text-muted-foreground">Date</p>
                          <p className="font-medium">
                            {new Date(report.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      {report.voltage_reading && (
                        <div className="mt-3 pt-3 border-t">
                          <p className="text-xs text-muted-foreground mb-1">Technical Details</p>
                          <div className="flex gap-4 text-xs">
                            <span>Voltage: {report.voltage_reading}V</span>
                            {report.temperature && <span>Temp: {report.temperature}°C</span>}
                          </div>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Battery className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  <p className="font-medium mb-1">No reports submitted yet</p>
                  <p className="text-sm">Submit your first battery report to get started</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </TabsContent>

      <TabsContent value="inventory">
        <BatteryInventory />
      </TabsContent>

      <TabsContent value="sales">
        <BatterySales />
      </TabsContent>
    </Tabs>
  );
};
