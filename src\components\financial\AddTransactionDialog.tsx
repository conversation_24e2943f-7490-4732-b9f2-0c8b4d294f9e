import { useState } from "react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, DollarSign, CreditCard, ArrowUpDown, Receipt } from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface AddTransactionDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onTransactionAdded?: () => void;
}

interface TransactionFormData {
  transaction_type: string;
  amount: string;
  payment_method: string;
  recipient_name: string;
  recipient_account: string;
  description: string;
  notes: string;
  invoice_id?: string;
  category?: string;
  expense_date?: string;
}

export const AddTransactionDialog = ({ isOpen, onOpenChange, onTransactionAdded }: AddTransactionDialogProps) => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState<TransactionFormData>({
    transaction_type: "payment",
    amount: "",
    payment_method: "bank_transfer",
    recipient_name: "",
    recipient_account: "",
    description: "",
    notes: "",
    invoice_id: "",
    category: "",
    expense_date: new Date().toISOString().split('T')[0]
  });

  // Fetch invoices for payment transactions
  const { data: invoices } = useQuery({
    queryKey: ['invoices-for-transactions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('invoices')
        .select('id, invoice_number, client_name, total_amount, payment_status')
        .eq('payment_status', 'pending')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    },
    enabled: isOpen,
  });

  // Create payment transaction
  const createPaymentTransactionMutation = useMutation({
    mutationFn: async (data: TransactionFormData) => {
      const transactionReference = `TXN-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
      
      const transactionData = {
        invoice_id: data.invoice_id || null,
        amount: parseFloat(data.amount),
        transaction_type: data.transaction_type,
        payment_method: data.payment_method,
        transaction_reference: transactionReference,
        reference_number: transactionReference,
        recipient_name: data.recipient_name,
        recipient_account: data.recipient_account || null,
        transaction_date: new Date().toISOString().split('T')[0],
        status: 'pending',
        notes: data.notes || null,
        created_by: userProfile?.id,
        processed_by: userProfile?.id
      };

      const { error } = await supabase
        .from('payment_transactions')
        .insert([transactionData]);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payment-transactions'] });
      queryClient.invalidateQueries({ queryKey: ['financial-data'] });
      toast({
        title: "Success",
        description: "Payment transaction created successfully",
      });
      resetForm();
      onTransactionAdded?.();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create payment transaction",
        variant: "destructive",
      });
    },
  });

  // Create expense transaction
  const createExpenseMutation = useMutation({
    mutationFn: async (data: TransactionFormData) => {
      const expenseData = {
        title: data.description,
        description: data.notes || data.description,
        amount: parseFloat(data.amount),
        category: data.category || 'general',
        expense_date: data.expense_date || new Date().toISOString().split('T')[0],
        vendor_name: data.recipient_name,
        payment_method: data.payment_method,
        created_by: userProfile?.id,
        status: 'approved'
      };

      const { error } = await supabase
        .from('expenses')
        .insert([expenseData]);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['expenses'] });
      queryClient.invalidateQueries({ queryKey: ['financial-data'] });
      toast({
        title: "Success",
        description: "Expense recorded successfully",
      });
      resetForm();
      onTransactionAdded?.();
      onOpenChange(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to record expense",
        variant: "destructive",
      });
    },
  });

  const resetForm = () => {
    setFormData({
      transaction_type: "payment",
      amount: "",
      payment_method: "bank_transfer",
      recipient_name: "",
      recipient_account: "",
      description: "",
      notes: "",
      invoice_id: "",
      category: "",
      expense_date: new Date().toISOString().split('T')[0]
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.amount || !formData.recipient_name || !formData.description) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    if (formData.transaction_type === 'expense') {
      createExpenseMutation.mutate(formData);
    } else {
      createPaymentTransactionMutation.mutate(formData);
    }
  };

  const updateFormData = (field: keyof TransactionFormData, value: string) => {
    // Handle "none" values as empty string for optional fields
    const processedValue = value === 'none' ? '' : value;
    setFormData(prev => ({ ...prev, [field]: processedValue }));
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'payment': return <CreditCard className="h-4 w-4" />;
      case 'expense': return <Receipt className="h-4 w-4" />;
      case 'income': return <DollarSign className="h-4 w-4" />;
      case 'transfer': return <ArrowUpDown className="h-4 w-4" />;
      default: return <DollarSign className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Transaction
          </DialogTitle>
        </DialogHeader>

        <Tabs value={formData.transaction_type} onValueChange={(value) => updateFormData('transaction_type', value)}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="payment" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payment
            </TabsTrigger>
            <TabsTrigger value="expense" className="flex items-center gap-2">
              <Receipt className="h-4 w-4" />
              Expense
            </TabsTrigger>
            <TabsTrigger value="income" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Income
            </TabsTrigger>
            <TabsTrigger value="transfer" className="flex items-center gap-2">
              <ArrowUpDown className="h-4 w-4" />
              Transfer
            </TabsTrigger>
          </TabsList>

          <form onSubmit={handleSubmit} className="space-y-4 mt-4">
            <TabsContent value="payment" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="invoice_id">Invoice (Optional)</Label>
                  <Select value={formData.invoice_id} onValueChange={(value) => updateFormData('invoice_id', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select invoice" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No invoice</SelectItem>
                      {invoices?.map((invoice) => (
                        <SelectItem key={invoice.id} value={invoice.id}>
                          {invoice.invoice_number} - {invoice.client_name} (₦{invoice.total_amount.toLocaleString()})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="amount">Amount *</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    value={formData.amount}
                    onChange={(e) => updateFormData('amount', e.target.value)}
                    required
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="expense" className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select value={formData.category} onValueChange={(value) => updateFormData('category', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="office_supplies">Office Supplies</SelectItem>
                      <SelectItem value="travel">Travel</SelectItem>
                      <SelectItem value="utilities">Utilities</SelectItem>
                      <SelectItem value="equipment">Equipment</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="fuel">Fuel</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="expense_date">Expense Date</Label>
                  <Input
                    id="expense_date"
                    type="date"
                    value={formData.expense_date}
                    onChange={(e) => updateFormData('expense_date', e.target.value)}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="amount">Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={(e) => updateFormData('amount', e.target.value)}
                  required
                />
              </div>
            </TabsContent>

            <TabsContent value="income" className="space-y-4">
              <div>
                <Label htmlFor="amount">Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={(e) => updateFormData('amount', e.target.value)}
                  required
                />
              </div>
            </TabsContent>

            <TabsContent value="transfer" className="space-y-4">
              <div>
                <Label htmlFor="amount">Amount *</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.amount}
                  onChange={(e) => updateFormData('amount', e.target.value)}
                  required
                />
              </div>
            </TabsContent>

            {/* Common fields for all transaction types */}
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="recipient_name">
                    {formData.transaction_type === 'expense' ? 'Vendor/Supplier *' : 'Recipient Name *'}
                  </Label>
                  <Input
                    id="recipient_name"
                    placeholder={formData.transaction_type === 'expense' ? 'Enter vendor name' : 'Enter recipient name'}
                    value={formData.recipient_name}
                    onChange={(e) => updateFormData('recipient_name', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="payment_method">Payment Method</Label>
                  <Select value={formData.payment_method} onValueChange={(value) => updateFormData('payment_method', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="check">Check</SelectItem>
                      <SelectItem value="credit_card">Credit Card</SelectItem>
                      <SelectItem value="mobile_money">Mobile Money</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {formData.payment_method !== 'cash' && (
                <div>
                  <Label htmlFor="recipient_account">Account Details</Label>
                  <Input
                    id="recipient_account"
                    placeholder="Account number or details"
                    value={formData.recipient_account}
                    onChange={(e) => updateFormData('recipient_account', e.target.value)}
                  />
                </div>
              )}

              <div>
                <Label htmlFor="description">Description *</Label>
                <Input
                  id="description"
                  placeholder="Brief description of the transaction"
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  required
                />
              </div>

              <div>
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes or details"
                  value={formData.notes}
                  onChange={(e) => updateFormData('notes', e.target.value)}
                  rows={3}
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={createPaymentTransactionMutation.isPending || createExpenseMutation.isPending}
                className="flex items-center gap-2"
              >
                {getTransactionIcon(formData.transaction_type)}
                {createPaymentTransactionMutation.isPending || createExpenseMutation.isPending 
                  ? 'Creating...' 
                  : `Create ${formData.transaction_type.charAt(0).toUpperCase() + formData.transaction_type.slice(1)}`
                }
              </Button>
            </div>
          </form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
