# Ensure proper MIME types for JavaScript modules
AddType application/javascript .js
AddType application/javascript .mjs
AddType text/javascript .js
AddType text/javascript .mjs

# Enable CORS for development
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization"

# Cache control for development
<FilesMatch "\.(js|mjs|css|html)$">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</FilesMatch>

# Ensure proper module script handling
<FilesMatch "\.m?js$">
    Header set Content-Type "application/javascript"
</FilesMatch>