import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';
import { 
  Bot, 
  Play, 
  Square, 
  FileText, 
  Upload, 
  Navigation, 
  MousePointer, 
  Keyboard,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react';
import { aiTaskAutomation, AITaskExecution } from '@/lib/ai-task-automation';

export const AITaskAutomationDemo: React.FC = () => {
  const [command, setCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [currentExecution, setCurrentExecution] = useState<AITaskExecution | null>(null);
  const [executionHistory, setExecutionHistory] = useState<AITaskExecution[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const { toast } = useToast();
  const { userProfile } = useAuth();

  // Predefined example commands
  const exampleCommands = [
    {
      command: 'Write memo titled "Weekly Update" saying "This week we completed the AI integration project successfully"',
      description: 'Create a memo with title and content',
      icon: <FileText className="h-4 w-4" />
    },
    {
      command: 'Navigate to admin dashboard and upload the selected files',
      description: 'Navigate and upload files',
      icon: <Upload className="h-4 w-4" />
    },
    {
      command: 'Go to manager dashboard, click on projects tab, and create new project',
      description: 'Multi-step navigation and interaction',
      icon: <Navigation className="h-4 w-4" />
    },
    {
      command: 'Fill out the contact form with name "John Doe" and email "<EMAIL>"',
      description: 'Form filling automation',
      icon: <Keyboard className="h-4 w-4" />
    }
  ];

  useEffect(() => {
    // Update execution status periodically
    const interval = setInterval(() => {
      const current = aiTaskAutomation.getCurrentExecution();
      setCurrentExecution(current);
      
      if (!current && isExecuting) {
        setIsExecuting(false);
        setExecutionHistory(aiTaskAutomation.getExecutionHistory());
      }
    }, 500);

    return () => clearInterval(interval);
  }, [isExecuting]);

  const executeCommand = async () => {
    if (!command.trim() || isExecuting) return;

    if (!userProfile) {
      toast({
        title: "Authentication Required",
        description: "Please log in to use AI task automation",
        variant: "destructive"
      });
      return;
    }

    setIsExecuting(true);
    
    try {
      const execution = await aiTaskAutomation.executeUserCommand({
        command: command.trim(),
        userId: userProfile.id,
        userRole: userProfile.role,
        context: {
          currentPage: window.location.pathname,
          files: selectedFiles
        }
      });

      toast({
        title: "AI Task Automation",
        description: `Started executing: ${execution.name}`,
      });

      setCommand('');
      setSelectedFiles([]);
    } catch (error) {
      toast({
        title: "Execution Failed",
        description: `Error: ${error}`,
        variant: "destructive"
      });
      setIsExecuting(false);
    }
  };

  const stopExecution = () => {
    // Note: In a real implementation, you'd need to add cancellation logic
    setIsExecuting(false);
    toast({
      title: "Execution Stopped",
      description: "AI task automation has been stopped",
      variant: "destructive"
    });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(files);
  };

  const getStepIcon = (type: string) => {
    switch (type) {
      case 'navigate': return <Navigation className="h-4 w-4" />;
      case 'click': return <MousePointer className="h-4 w-4" />;
      case 'input': return <Keyboard className="h-4 w-4" />;
      case 'upload': return <Upload className="h-4 w-4" />;
      case 'submit': return <CheckCircle className="h-4 w-4" />;
      default: return <Zap className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'outline',
      running: 'secondary',
      completed: 'default',
      failed: 'destructive'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'outline'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-blue-500" />
            AI Task Automation System
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Command the AI to perform complex tasks including page navigation, button clicks, 
              form filling, file uploads, and memo creation. The AI will execute tasks step-by-step 
              with visual feedback.
            </p>

            {/* Command Input */}
            <div className="flex gap-2">
              <Input
                placeholder="Enter your command (e.g., 'Write memo titled Hello saying Welcome to our system')"
                value={command}
                onChange={(e) => setCommand(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && executeCommand()}
                disabled={isExecuting}
                className="flex-1"
              />
              {isExecuting ? (
                <Button onClick={stopExecution} variant="destructive">
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
              ) : (
                <Button onClick={executeCommand} disabled={!command.trim()}>
                  <Play className="h-4 w-4 mr-2" />
                  Execute
                </Button>
              )}
            </div>

            {/* File Upload for Commands */}
            <div className="flex items-center gap-2">
              <Input
                type="file"
                multiple
                onChange={handleFileSelect}
                className="flex-1"
                accept=".pdf,.doc,.docx,.txt,.jpg,.png"
              />
              {selectedFiles.length > 0 && (
                <Badge variant="outline">
                  {selectedFiles.length} file(s) selected
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Example Commands */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Example Commands</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            {exampleCommands.map((example, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 cursor-pointer"
                onClick={() => setCommand(example.command)}
              >
                <div className="flex items-center gap-3">
                  {example.icon}
                  <div>
                    <p className="font-medium text-sm">{example.description}</p>
                    <p className="text-xs text-muted-foreground">{example.command}</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm">
                  Use
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Execution */}
      {currentExecution && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-blue-500" />
                Current Execution: {currentExecution.name}
              </span>
              {getStatusBadge(currentExecution.status)}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {currentExecution.description}
              </p>

              {/* Progress */}
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Progress</span>
                  <span>{Math.round(currentExecution.progress)}%</span>
                </div>
                <Progress value={currentExecution.progress} className="w-full" />
              </div>

              {/* Steps */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Execution Steps:</h4>
                {currentExecution.steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center gap-3 p-2 rounded ${
                      index === currentExecution.currentStep
                        ? 'bg-blue-50 border border-blue-200'
                        : index < (currentExecution.currentStep || 0)
                        ? 'bg-green-50 border border-green-200'
                        : 'bg-gray-50'
                    }`}
                  >
                    {getStepIcon(step.type)}
                    <div className="flex-1">
                      <p className="text-sm font-medium">{step.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {step.type.toUpperCase()}: {step.target}
                      </p>
                    </div>
                    {index < (currentExecution.currentStep || 0) && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {index === currentExecution.currentStep && (
                      <div className="h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                    )}
                  </div>
                ))}
              </div>

              {currentExecution.error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{currentExecution.error}</AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Execution History */}
      {executionHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Execution History</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {executionHistory.slice(0, 5).map((execution) => (
                <div
                  key={execution.id}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <Bot className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="font-medium text-sm">{execution.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {execution.steps.length} steps • {Math.round(execution.progress)}% complete
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(execution.status)}
                    {execution.status === 'completed' && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {execution.status === 'failed' && (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Capabilities Overview */}
      <Card>
        <CardHeader>
          <CardTitle>AI Automation Capabilities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-3 border rounded-lg">
              <Navigation className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <p className="font-medium text-sm">Page Navigation</p>
              <p className="text-xs text-muted-foreground">Navigate between pages</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <MousePointer className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <p className="font-medium text-sm">Element Interaction</p>
              <p className="text-xs text-muted-foreground">Click buttons and links</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <Keyboard className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <p className="font-medium text-sm">Form Filling</p>
              <p className="text-xs text-muted-foreground">Input text and data</p>
            </div>
            <div className="text-center p-3 border rounded-lg">
              <Upload className="h-8 w-8 mx-auto mb-2 text-orange-500" />
              <p className="font-medium text-sm">File Operations</p>
              <p className="text-xs text-muted-foreground">Upload and submit files</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
