import { supabase } from '@/integrations/supabase/client'

export const setupBatteryTables = async () => {
  console.log('🔋 Setting up battery management tables...')

  try {
    // Read the SQL file content
    const sqlCommands = `
      -- Battery Management System Tables
      -- This script creates comprehensive battery tracking tables with audit columns

      -- 1. Battery Types Table
      CREATE TABLE IF NOT EXISTS battery_types (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        description TEXT,
        voltage DECIMAL(5,2) NOT NULL,
        capacity_ah DECIMAL(8,2) NOT NULL,
        chemistry VARCHAR(50) NOT NULL,
        manufacturer VARCHAR(100),
        model VARCHAR(100),
        specifications JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id),
        updated_by UUID REFERENCES profiles(id)
      );

      -- 2. Battery Locations Table
      CREATE TABLE IF NOT EXISTS battery_locations (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        location_type VARCHAR(50) NOT NULL,
        address TEXT,
        coordinates POINT,
        parent_location_id UUID REFERENCES battery_locations(id),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id),
        updated_by UUID REFERENCES profiles(id)
      );

      -- 3. Batteries Table (Main inventory)
      CREATE TABLE IF NOT EXISTS batteries (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        serial_number VARCHAR(100) NOT NULL UNIQUE,
        battery_type_id UUID NOT NULL REFERENCES battery_types(id),
        current_location_id UUID REFERENCES battery_locations(id),
        status VARCHAR(50) NOT NULL DEFAULT 'new',
        condition VARCHAR(50) NOT NULL DEFAULT 'excellent',
        purchase_date DATE,
        installation_date DATE,
        warranty_expiry_date DATE,
        last_maintenance_date DATE,
        next_maintenance_date DATE,
        purchase_cost DECIMAL(10,2),
        supplier VARCHAR(100),
        notes TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id),
        updated_by UUID REFERENCES profiles(id),
        profile_id UUID REFERENCES profiles(id)
      );

      -- 4. Battery Readings Table (Performance tracking)
      CREATE TABLE IF NOT EXISTS battery_readings (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        battery_id UUID NOT NULL REFERENCES batteries(id) ON DELETE CASCADE,
        reading_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        voltage DECIMAL(5,2),
        current_amperage DECIMAL(8,2),
        temperature DECIMAL(5,2),
        state_of_charge DECIMAL(5,2),
        capacity_remaining DECIMAL(8,2),
        internal_resistance DECIMAL(8,4),
        reading_type VARCHAR(50) NOT NULL DEFAULT 'manual',
        technician_notes TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id),
        generated_by VARCHAR(100),
        profile_id UUID REFERENCES profiles(id)
      );

      -- 5. Battery Maintenance Records
      CREATE TABLE IF NOT EXISTS battery_maintenance (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        battery_id UUID NOT NULL REFERENCES batteries(id) ON DELETE CASCADE,
        maintenance_type VARCHAR(100) NOT NULL,
        maintenance_date DATE NOT NULL,
        technician_id UUID REFERENCES profiles(id),
        description TEXT NOT NULL,
        parts_used TEXT,
        cost DECIMAL(10,2),
        next_maintenance_date DATE,
        status VARCHAR(50) DEFAULT 'completed',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id),
        updated_by UUID REFERENCES profiles(id),
        profile_id UUID REFERENCES profiles(id)
      );

      -- 6. Battery Transfers/Movements
      CREATE TABLE IF NOT EXISTS battery_transfers (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        battery_id UUID NOT NULL REFERENCES batteries(id) ON DELETE CASCADE,
        from_location_id UUID REFERENCES battery_locations(id),
        to_location_id UUID NOT NULL REFERENCES battery_locations(id),
        transfer_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        transferred_by UUID REFERENCES profiles(id),
        received_by UUID REFERENCES profiles(id),
        reason VARCHAR(200),
        notes TEXT,
        status VARCHAR(50) DEFAULT 'completed',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_by UUID REFERENCES profiles(id),
        profile_id UUID REFERENCES profiles(id)
      );
    `

    // Execute the SQL commands
    const { error: createError } = await supabase.rpc('exec_sql', { sql: sqlCommands })

    if (createError) {
      console.error('Error creating tables:', createError)
      // Try alternative approach - execute each table creation separately
      await createTablesIndividually()
    } else {
      console.log('✅ Battery tables created successfully')
    }

    // Create indexes
    await createIndexes()

    // Create triggers
    await createTriggers()

    // Insert sample data
    await insertSampleData()

    console.log('🎉 Battery management system setup completed!')
    return { success: true }
  } catch (error) {
    console.error('❌ Error setting up battery tables:', error)
    return { success: false, error }
  }
}

const createTablesIndividually = async () => {
  console.log('📝 Creating tables individually...')

  const tables = [
    {
      name: 'battery_types',
      sql: `
        CREATE TABLE IF NOT EXISTS battery_types (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name VARCHAR(100) NOT NULL UNIQUE,
          description TEXT,
          voltage DECIMAL(5,2) NOT NULL,
          capacity_ah DECIMAL(8,2) NOT NULL,
          chemistry VARCHAR(50) NOT NULL,
          manufacturer VARCHAR(100),
          model VARCHAR(100),
          specifications JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID,
          updated_by UUID
        );
      `
    },
    {
      name: 'battery_locations',
      sql: `
        CREATE TABLE IF NOT EXISTS battery_locations (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          name VARCHAR(100) NOT NULL,
          description TEXT,
          location_type VARCHAR(50) NOT NULL,
          address TEXT,
          coordinates POINT,
          parent_location_id UUID,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID,
          updated_by UUID
        );
      `
    },
    {
      name: 'batteries',
      sql: `
        CREATE TABLE IF NOT EXISTS batteries (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          serial_number VARCHAR(100) NOT NULL UNIQUE,
          battery_type_id UUID NOT NULL,
          current_location_id UUID,
          status VARCHAR(50) NOT NULL DEFAULT 'new',
          condition VARCHAR(50) NOT NULL DEFAULT 'excellent',
          purchase_date DATE,
          installation_date DATE,
          warranty_expiry_date DATE,
          last_maintenance_date DATE,
          next_maintenance_date DATE,
          purchase_cost DECIMAL(10,2),
          supplier VARCHAR(100),
          notes TEXT,
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          created_by UUID,
          updated_by UUID,
          profile_id UUID
        );
      `
    }
  ]

  for (const table of tables) {
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: table.sql })
      if (error) {
        console.error(`Error creating ${table.name}:`, error)
      } else {
        console.log(`✅ Created table: ${table.name}`)
      }
    } catch (err) {
      console.error(`Error creating ${table.name}:`, err)
    }
  }
}

const createIndexes = async () => {
  console.log('📊 Creating indexes...')

  const indexes = [
    'CREATE INDEX IF NOT EXISTS idx_batteries_serial_number ON batteries(serial_number);',
    'CREATE INDEX IF NOT EXISTS idx_batteries_type ON batteries(battery_type_id);',
    'CREATE INDEX IF NOT EXISTS idx_batteries_location ON batteries(current_location_id);',
    'CREATE INDEX IF NOT EXISTS idx_batteries_status ON batteries(status);',
    'CREATE INDEX IF NOT EXISTS idx_batteries_created_by ON batteries(created_by);',
    'CREATE INDEX IF NOT EXISTS idx_batteries_profile_id ON batteries(profile_id);'
  ]

  for (const indexSql of indexes) {
    try {
      const { error } = await supabase.rpc('exec_sql', { sql: indexSql })
      if (error) {
        console.error('Error creating index:', error)
      }
    } catch (err) {
      console.error('Error creating index:', err)
    }
  }
}

const createTriggers = async () => {
  console.log('⚡ Creating triggers...')

  const triggerFunction = `
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ language 'plpgsql';
  `

  try {
    await supabase.rpc('exec_sql', { sql: triggerFunction })
    console.log('✅ Created trigger function')
  } catch (err) {
    console.error('Error creating trigger function:', err)
  }
}

const insertSampleData = async () => {
  console.log('📝 Inserting sample data...')

  try {
    // Insert battery types
    const { error: typesError } = await supabase
      .from('battery_types')
      .upsert([
        {
          name: 'Deep Cycle 12V 100Ah',
          description: 'Standard deep cycle battery for telecom sites',
          voltage: 12.0,
          capacity_ah: 100.0,
          chemistry: 'Lead-acid',
          manufacturer: 'Trojan',
          model: 'T-105'
        },
        {
          name: 'Lithium 48V 200Ah',
          description: 'High capacity lithium battery bank',
          voltage: 48.0,
          capacity_ah: 200.0,
          chemistry: 'Lithium-ion',
          manufacturer: 'Tesla',
          model: 'Powerwall'
        }
      ], { onConflict: 'name' })

    if (typesError) {
      console.error('Error inserting battery types:', typesError)
    } else {
      console.log('✅ Inserted sample battery types')
    }

    // Insert locations
    const { error: locationsError } = await supabase
      .from('battery_locations')
      .upsert([
        {
          name: 'Main Warehouse',
          description: 'Central battery storage facility',
          location_type: 'Warehouse',
          address: '123 Industrial Ave, Lagos'
        },
        {
          name: 'Site Alpha',
          description: 'Telecom tower site Alpha',
          location_type: 'Site',
          address: 'Alpha Tower Location, Abuja'
        }
      ], { onConflict: 'name' })

    if (locationsError) {
      console.error('Error inserting locations:', locationsError)
    } else {
      console.log('✅ Inserted sample locations')
    }
  } catch (error) {
    console.error('Error inserting sample data:', error)
  }
}
