import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Download } from 'lucide-react'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useFinancialData } from '@/hooks/useFinancialData'
import { FinancialOverviewCards } from '@/components/financial/FinancialOverviewCards'
import { FinancialRevenueTab } from '@/components/financial/FinancialRevenueTab'
import { FinancialExpensesTab } from '@/components/financial/FinancialExpensesTab'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'

export const FinancialReports = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('30')
  const { toast } = useToast()

  const {
    invoices,
    expenses,
    accountsInvoices,
    financialSummary,
    isLoading
  } = useFinancialData(selectedPeriod)

  const generateReport = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('User not authenticated')

      const reportData = {
        period: `Last ${selectedPeriod} days`,
        generatedAt: new Date().toISOString(),
        summary: financialSummary,
        invoices: invoices.length,
        expenses: expenses.length,
        accountsPayable: accountsInvoices.length
      }

      // Save to database
      const { error } = await supabase
        .from('financial_reports')
        .insert([{
          report_name: `Financial Report - ${new Date().toLocaleDateString()}`,
          report_type: 'comprehensive',
          period_start: new Date(Date.now() - parseInt(selectedPeriod) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          period_end: new Date().toISOString().split('T')[0],
          report_data: reportData,
          generated_by: user.id,
          status: 'completed'
        }])

      if (error) throw error

      // Download JSON report
      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `financial-report-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)

      toast({
        title: 'Report Generated',
        description: 'Financial report has been generated and saved successfully'
      })
    } catch (error) {
      console.error('Error generating report:', error)
      toast({
        title: 'Error',
        description: 'Failed to generate financial report',
        variant: 'destructive'
      })
    }
  }

  return (
    <div className='space-y-6'>
      <div className='flex justify-between items-center'>
        <div>
          <h2 className='text-2xl font-bold'>Financial Reports</h2>
          <p className='text-muted-foreground'>Comprehensive financial analysis and reporting</p>
        </div>
        <div className='flex gap-2'>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className='w-40'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='7'>Last 7 days</SelectItem>
              <SelectItem value='30'>Last 30 days</SelectItem>
              <SelectItem value='90'>Last 90 days</SelectItem>
              <SelectItem value='365'>Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={generateReport} variant='outline' disabled={isLoading}>
            <Download className='h-4 w-4 mr-2' />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <FinancialOverviewCards summary={financialSummary} isLoading={isLoading} />

      <Tabs defaultValue='overview' className='space-y-4'>
        <TabsList className='grid w-full grid-cols-4'>
          <TabsTrigger value='overview'>Overview</TabsTrigger>
          <TabsTrigger value='revenue'>Revenue</TabsTrigger>
          <TabsTrigger value='expenses'>Expenses</TabsTrigger>
          <TabsTrigger value='accounts'>Accounts</TabsTrigger>
        </TabsList>

        <TabsContent value='overview' className='space-y-4'>
          <div className='grid gap-6 lg:grid-cols-2'>
            {/* Financial Health Summary */}
            <div className='bg-white p-6 rounded-lg border'>
              <h3 className='text-lg font-semibold mb-4'>Financial Health</h3>
              <div className='space-y-4'>
                <div className='flex justify-between items-center'>
                  <span>Total Invoices</span>
                  <span className='font-bold'>{invoices.length}</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Total Expenses</span>
                  <span className='font-bold'>{expenses.length}</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Accounts Payable</span>
                  <span className='font-bold'>{accountsInvoices.length}</span>
                </div>
                <div className='flex justify-between items-center'>
                  <span>Profit Margin</span>
                  <span className={`font-bold ${(financialSummary?.net_profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {financialSummary?.total_revenue && financialSummary.total_revenue > 0
                      ? Math.round(((financialSummary.net_profit || 0) / financialSummary.total_revenue) * 100)
                      : 0}%
                  </span>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className='bg-white p-6 rounded-lg border'>
              <h3 className='text-lg font-semibold mb-4'>Recent Activity</h3>
              <div className='space-y-3'>
                {invoices.slice(0, 3).map((invoice) => (
                  <div key={invoice.id} className='text-sm'>
                    <span className='font-medium'>Invoice</span> #{invoice.invoice_number} -
                    <span className='text-green-600 font-bold'> ₦{invoice.total_amount.toLocaleString()}</span>
                  </div>
                ))}
                {expenses.slice(0, 2).map((expense) => (
                  <div key={expense.id} className='text-sm'>
                    <span className='font-medium'>Expense:</span> {expense.title} -
                    <span className='text-red-600 font-bold'> ₦{expense.amount.toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value='revenue'>
          <FinancialRevenueTab invoices={invoices} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value='expenses'>
          <FinancialExpensesTab expenses={expenses} isLoading={isLoading} />
        </TabsContent>

        <TabsContent value='accounts' className='space-y-4'>
          <div className='bg-white p-6 rounded-lg border'>
            <h3 className='text-lg font-semibold mb-4'>Accounts Payable</h3>
            <div className='space-y-4'>
              {accountsInvoices.length === 0
                ? (
                  <p className='text-center text-muted-foreground py-8'>No accounts payable found</p>
                  )
                : (
                    accountsInvoices.map((invoice) => (
                      <div key={invoice.id} className='flex justify-between items-center p-4 border rounded-lg'>
                        <div>
                          <h4 className='font-semibold'>{invoice.vendor_name}</h4>
                          <p className='text-sm text-muted-foreground'>
                            Invoice: {invoice.invoice_number}
                          </p>
                        </div>
                        <div className='text-right'>
                          <p className='text-xl font-bold'>₦{invoice.amount.toLocaleString()}</p>
                          <p className='text-sm text-muted-foreground'>{invoice.payment_status}</p>
                        </div>
                      </div>
                    ))
                  )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
