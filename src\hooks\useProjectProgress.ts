import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ProjectProgressAPI, ProjectWithProgress, ProjectAssignment, ProjectProgressUpdate } from '@/lib/project-progress-api';
import { toast } from 'sonner';

export const useMyProjects = () => {
  return useQuery({
    queryKey: ['my-projects'],
    queryFn: async () => {
      const { data, error } = await ProjectProgressAPI.getMyProjects();
      if (error) throw new Error(error.message);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });
};

export const useAllProjectsWithProgress = (filters?: {
  status?: string;
  managerId?: string;
  departmentId?: string;
}) => {
  return useQuery({
    queryKey: ['all-projects-progress', filters],
    queryFn: async () => {
      const { data, error } = await ProjectProgressAPI.getAllProjectsWithProgress(filters);
      if (error) throw new Error(error.message);
      return data;
    },
    staleTime: 3 * 60 * 1000, // 3 minutes
    refetchOnWindowFocus: true,
  });
};

export const useProjectWithProgress = (projectId: string) => {
  return useQuery({
    queryKey: ['project-progress', projectId],
    queryFn: async () => {
      const { data, error } = await ProjectProgressAPI.getProjectWithProgress(projectId);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!projectId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useProjectProgressUpdates = (projectId: string) => {
  return useQuery({
    queryKey: ['project-progress-updates', projectId],
    queryFn: async () => {
      const { data, error } = await ProjectProgressAPI.getProjectProgressUpdates(projectId);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!projectId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

export const useUpdateAssignmentProgress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      assignmentId,
      progressData,
    }: {
      assignmentId: string;
      progressData: {
        progress_percentage: number;
        hours_worked?: number;
        status?: string;
        notes?: string;
      };
    }) => {
      const { data, error } = await ProjectProgressAPI.updateAssignmentProgress(assignmentId, progressData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['my-projects'] });
      queryClient.invalidateQueries({ queryKey: ['all-projects-progress'] });
      if (data?.project_id) {
        queryClient.invalidateQueries({ queryKey: ['project-progress', data.project_id] });
        queryClient.invalidateQueries({ queryKey: ['project-progress-updates', data.project_id] });
      }
      
      toast.success('Progress updated successfully!', {
        description: `Project progress updated to ${data?.progress_percentage}%`,
      });
    },
    onError: (error: Error) => {
      toast.error('Failed to update progress', {
        description: error.message,
      });
    },
  });
};

export const useCreateProgressUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (updateData: {
      project_id: string;
      assignment_id?: string;
      new_progress: number;
      hours_worked?: number;
      update_type?: string;
      description?: string;
    }) => {
      const { data, error } = await ProjectProgressAPI.createProgressUpdate(updateData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['my-projects'] });
      queryClient.invalidateQueries({ queryKey: ['all-projects-progress'] });
      if (data?.project_id) {
        queryClient.invalidateQueries({ queryKey: ['project-progress', data.project_id] });
        queryClient.invalidateQueries({ queryKey: ['project-progress-updates', data.project_id] });
      }
      
      toast.success('Progress update created!', {
        description: 'Project progress has been recorded successfully.',
      });
    },
    onError: (error: Error) => {
      toast.error('Failed to create progress update', {
        description: error.message,
      });
    },
  });
};

export const useAssignUserToProject = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (assignmentData: {
      project_id: string;
      assigned_to: string;
      role?: string;
      hours_allocated?: number;
      start_date?: string;
      end_date?: string;
    }) => {
      const { data, error } = await ProjectProgressAPI.assignUserToProject(assignmentData);
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (data) => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: ['my-projects'] });
      queryClient.invalidateQueries({ queryKey: ['all-projects-progress'] });
      if (data?.project_id) {
        queryClient.invalidateQueries({ queryKey: ['project-progress', data.project_id] });
      }
      
      toast.success('User assigned to project!', {
        description: `Successfully assigned user to project.`,
      });
    },
    onError: (error: Error) => {
      toast.error('Failed to assign user to project', {
        description: error.message,
      });
    },
  });
};

// Utility hook for project statistics
export const useProjectStats = () => {
  const { data: myProjects } = useMyProjects();
  
  const stats = {
    totalProjects: myProjects?.length || 0,
    activeProjects: myProjects?.filter(p => p.status === 'active').length || 0,
    completedProjects: myProjects?.filter(p => p.status === 'completed').length || 0,
    averageProgress: myProjects?.length 
      ? Math.round(myProjects.reduce((sum, p) => sum + (p.user_assignment?.progress_percentage || 0), 0) / myProjects.length)
      : 0,
    totalHoursWorked: myProjects?.reduce((sum, p) => sum + (p.user_assignment?.hours_worked || 0), 0) || 0,
    totalHoursAllocated: myProjects?.reduce((sum, p) => sum + (p.user_assignment?.hours_allocated || 0), 0) || 0,
  };

  return stats;
};

// Hook for real-time project updates
export const useProjectRealTimeUpdates = (projectId: string) => {
  const queryClient = useQueryClient();

  // This would typically use Supabase real-time subscriptions
  // For now, we'll use polling with a shorter interval
  const { data } = useQuery({
    queryKey: ['project-realtime', projectId],
    queryFn: async () => {
      const { data, error } = await ProjectProgressAPI.getProjectWithProgress(projectId);
      if (error) throw new Error(error.message);
      return data;
    },
    enabled: !!projectId,
    refetchInterval: 30000, // 30 seconds
    refetchIntervalInBackground: true,
  });

  return data;
};

// Hook for project color coding based on status and progress
export const useProjectColors = () => {
  const getProjectColor = (project: ProjectWithProgress | any) => {
    const progress = project.overall_progress || project.progress_percentage || 0;
    const status = project.status;

    if (status === 'completed') return 'bg-green-500';
    if (status === 'cancelled') return 'bg-gray-500';
    if (status === 'on_hold') return 'bg-yellow-500';
    
    // Color based on progress
    if (progress >= 80) return 'bg-blue-500';
    if (progress >= 60) return 'bg-indigo-500';
    if (progress >= 40) return 'bg-purple-500';
    if (progress >= 20) return 'bg-pink-500';
    return 'bg-red-500';
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'text-green-600 bg-green-100';
    if (progress >= 60) return 'text-blue-600 bg-blue-100';
    if (progress >= 40) return 'text-yellow-600 bg-yellow-100';
    if (progress >= 20) return 'text-orange-600 bg-orange-100';
    return 'text-red-600 bg-red-100';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'active': return 'text-blue-600 bg-blue-100';
      case 'in_progress': return 'text-indigo-600 bg-indigo-100';
      case 'on_hold': return 'text-yellow-600 bg-yellow-100';
      case 'planning': return 'text-purple-600 bg-purple-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return { getProjectColor, getProgressColor, getStatusColor };
};
