import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useAuth } from '@/components/auth/AuthProvider'

export const StaffProfile = () => {
  const { userProfile } = useAuth()

  if (!userProfile) {
    return (
      <Card className='glass-card border border-primary/20'>
        <CardHeader>
          <CardTitle className='text-xl md:text-2xl'>Staff Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Loading profile...</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className='glass-card border border-primary/20'>
      <CardHeader>
        <CardTitle className='text-xl md:text-2xl'>Staff Profile</CardTitle>
      </CardHeader>
      <CardContent>
        <div className='space-y-4'>
          <div>
            <h3 className='text-lg font-semibold'>
              {userProfile.full_name || 'No name set'}
            </h3>
            <p className='text-muted-foreground'>
              {userProfile.role} • {userProfile.department || 'No department'}
            </p>
          </div>

          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div>
              <label className='text-sm font-medium text-muted-foreground'>Email</label>
              <p>{userProfile.email || 'No email set'}</p>
            </div>

            <div>
              <label className='text-sm font-medium text-muted-foreground'>Phone</label>
              <p>{userProfile.phone || 'No phone set'}</p>
            </div>

            <div>
              <label className='text-sm font-medium text-muted-foreground'>Position</label>
              <p>{userProfile.position || 'No position set'}</p>
            </div>

            <div>
              <label className='text-sm font-medium text-muted-foreground'>Location</label>
              <p>{userProfile.location || 'No location set'}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
