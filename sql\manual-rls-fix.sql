-- ============================================================================
-- MANUAL RLS FIX - RUN IN SUPABASE SQL EDITOR
-- Copy and paste this entire script into Supabase SQL Editor and run it
-- ============================================================================

-- STEP 1: Disable RLS to stop infinite recursion
ALTER TABLE public.profiles DISABLE ROW LEVEL SECURITY;

-- STEP 2: Drop all problematic policies that cause recursion
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Ad<PERSON> can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can create profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert profiles" ON public.profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON public.profiles;
DROP POLICY IF EXISTS "Enable update for users based on email" ON public.profiles;

-- STEP 3: Re-enable RLS with clean state
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- STEP 4: Create safe policies that don't cause recursion
CREATE POLICY "profiles_select_own" ON public.profiles
    FOR SELECT
    USING (auth.uid() = id);

CREATE POLICY "profiles_insert_own" ON public.profiles
    FOR INSERT
    WITH CHECK (auth.uid() = id);

CREATE POLICY "profiles_update_own" ON public.profiles
    FOR UPDATE
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

-- Allow service role full access (for admin operations)
CREATE POLICY "profiles_service_role" ON public.profiles
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- STEP 5: Create departments table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.departments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    manager_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on departments
ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;

-- Create safe department policies
CREATE POLICY "departments_select_all" ON public.departments
    FOR SELECT
    USING (true);

CREATE POLICY "departments_service_role" ON public.departments
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- STEP 6: Insert sample departments
INSERT INTO public.departments (name, description) VALUES
    ('IT Department', 'Information Technology and Systems'),
    ('HR Department', 'Human Resources and Administration'),
    ('Finance Department', 'Finance and Accounting'),
    ('Operations', 'Field Operations and Maintenance'),
    ('Engineering', 'Technical Engineering and Design')
ON CONFLICT (name) DO NOTHING;

-- STEP 7: Fix time_logs table if it exists (CRITICAL - this is causing recursion)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'time_logs') THEN
        -- Disable RLS temporarily to stop recursion
        ALTER TABLE public.time_logs DISABLE ROW LEVEL SECURITY;

        -- Drop problematic time_logs policies
        DROP POLICY IF EXISTS "Users can view own time logs" ON public.time_logs;
        DROP POLICY IF EXISTS "Users can insert own time logs" ON public.time_logs;
        DROP POLICY IF EXISTS "Users can update own time logs" ON public.time_logs;
        DROP POLICY IF EXISTS "Enable read access for users based on user_id" ON public.time_logs;
        DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.time_logs;

        -- Re-enable RLS
        ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

        -- Create safe time_logs policies
        CREATE POLICY "time_logs_select_own" ON public.time_logs
            FOR SELECT
            USING (user_id = auth.uid());

        CREATE POLICY "time_logs_insert_own" ON public.time_logs
            FOR INSERT
            WITH CHECK (user_id = auth.uid());

        CREATE POLICY "time_logs_update_own" ON public.time_logs
            FOR UPDATE
            USING (user_id = auth.uid())
            WITH CHECK (user_id = auth.uid());

        CREATE POLICY "time_logs_service_role" ON public.time_logs
            FOR ALL
            USING (current_setting('role') = 'service_role')
            WITH CHECK (current_setting('role') = 'service_role');
    END IF;
END $$;

-- STEP 8: Fix memos table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'memos') THEN
        -- Drop problematic memo policies
        DROP POLICY IF EXISTS "Users can view published memos" ON public.memos;
        DROP POLICY IF EXISTS "Users can view memos" ON public.memos;
        DROP POLICY IF EXISTS "Users can create memos" ON public.memos;

        -- Create safe memo policies
        CREATE POLICY "memos_select_own_or_published" ON public.memos
            FOR SELECT
            USING (
                created_by = auth.uid() OR
                status = 'published' OR
                current_setting('role') = 'service_role'
            );

        CREATE POLICY "memos_insert_authenticated" ON public.memos
            FOR INSERT
            WITH CHECK (auth.uid() = created_by);

        CREATE POLICY "memos_update_own" ON public.memos
            FOR UPDATE
            USING (created_by = auth.uid())
            WITH CHECK (created_by = auth.uid());
    END IF;
END $$;

-- STEP 9: Display success message
DO $$
BEGIN
    RAISE NOTICE '🎉 RLS INFINITE RECURSION FIX COMPLETED!';
    RAISE NOTICE '';
    RAISE NOTICE '✅ Profiles table RLS fixed';
    RAISE NOTICE '✅ Time_logs table RLS fixed (CRITICAL)';
    RAISE NOTICE '✅ Safe policies created using auth.uid()';
    RAISE NOTICE '✅ Departments table created/fixed';
    RAISE NOTICE '✅ Memos table policies fixed (if exists)';
    RAISE NOTICE '';
    RAISE NOTICE '🔐 Authentication should now work without errors!';
    RAISE NOTICE '🔄 Refresh your application and try signing up/in again.';
END $$;

-- STEP 10: Test query to verify fix
SELECT 
    'RLS Fix Verification' as status,
    COUNT(*) as department_count
FROM public.departments;
