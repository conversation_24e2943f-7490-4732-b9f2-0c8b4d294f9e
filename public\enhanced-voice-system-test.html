<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Voice System with Nigerian Languages - CT NIGERIA LTD</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2d3748;
            margin: 0;
            font-size: 2.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .nigerian-flag {
            font-size: 2rem;
            margin: 0 10px;
        }
        .language-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .language-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }
        .language-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }
        .language-card.english {
            border-left: 4px solid #3b82f6;
        }
        .language-card.igbo {
            border-left: 4px solid #10b981;
        }
        .language-card.hausa {
            border-left: 4px solid #f59e0b;
        }
        .language-card.yoruba {
            border-left: 4px solid #8b5cf6;
        }
        .language-card h3 {
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #1f2937;
        }
        .language-flag {
            font-size: 1.5rem;
        }
        .sample-text {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-style: italic;
            margin: 10px 0;
            font-size: 14px;
            line-height: 1.5;
        }
        .button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .button.active {
            background: linear-gradient(135deg, #10b981, #059669);
            animation: pulse 2s infinite;
        }
        .button.speaking {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        .info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .project-manager-section {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .project-manager-section h3 {
            color: #0c4a6e;
            margin: 0 0 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .conversation-area {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            max-height: 300px;
            overflow-y: auto;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
        .message.user {
            background: #dbeafe;
            border-left: 4px solid #3b82f6;
            margin-left: 20px;
        }
        .message.agent {
            background: #f3e8ff;
            border-left: 4px solid #8b5cf6;
            margin-right: 20px;
        }
        .voice-commands-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .voice-command {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            font-size: 14px;
        }
        .voice-command:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
        }
        .language-selector {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .language-options {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .language-option {
            padding: 8px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        .language-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        .language-option:hover {
            border-color: #667eea;
        }
        .feature-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            text-align: center;
        }
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎤 Enhanced Voice System</h1>
            <p>
                <span class="nigerian-flag">🇳🇬</span>
                CT NIGERIA LTD - Nigerian Languages + AI Project Manager Agent
                <span class="nigerian-flag">🇳🇬</span>
            </p>
            <p>UK English • Igbo • Hausa • Yoruba with Advanced AI Assistant</p>
        </div>

        <div id="status" class="status info">
            <strong>Status:</strong> Initializing enhanced voice system with Nigerian languages...
        </div>

        <!-- Language Selection -->
        <div class="language-selector">
            <h3>🌍 Select Your Language / Họrọ Asụsụ Gị / Zaɓi Harshen Ku / Yan Ede Yin</h3>
            <div class="language-options">
                <div class="language-option selected" data-lang="en-GB">
                    🇬🇧 English (UK)
                </div>
                <div class="language-option" data-lang="ig-NG">
                    🇳🇬 Igbo (Asụsụ Igbo)
                </div>
                <div class="language-option" data-lang="ha-NG">
                    🇳🇬 Hausa (Harshen Hausa)
                </div>
                <div class="language-option" data-lang="yo-NG">
                    🇳🇬 Yoruba (Èdè Yorùbá)
                </div>
            </div>
        </div>

        <!-- Language Cards -->
        <div class="language-grid">
            <!-- English (UK) -->
            <div class="language-card english">
                <h3>
                    <span class="language-flag">🇬🇧</span>
                    English (UK) - Default
                </h3>
                <div class="sample-text">
                    "Welcome to CT NIGERIA LTD. I'm your AI Project Manager Assistant. How may I help you manage your projects today?"
                </div>
                <button class="button" onclick="testLanguageVoice('en-GB')">
                    🔊 Test Voice
                </button>
                <button class="button" onclick="startVoiceRecognition('en-GB')">
                    🎤 Voice Recognition
                </button>
                <button class="button" onclick="chatWithAgent('en-GB')">
                    💬 Chat with AI
                </button>
            </div>

            <!-- Igbo -->
            <div class="language-card igbo">
                <h3>
                    <span class="language-flag">🇳🇬</span>
                    Igbo (Asụsụ Igbo)
                </h3>
                <div class="sample-text">
                    "Nnọọ na CT NIGERIA LTD. Abụ m onye inyeaka AI Project Manager gị. Kedu ka m ga-esi nyere gị aka ijikwa ọrụ gị taa?"
                </div>
                <button class="button" onclick="testLanguageVoice('ig-NG')">
                    🔊 Nwalee Olu
                </button>
                <button class="button" onclick="startVoiceRecognition('ig-NG')">
                    🎤 Nrụpụta Olu
                </button>
                <button class="button" onclick="chatWithAgent('ig-NG')">
                    💬 Kparịta na AI
                </button>
            </div>

            <!-- Hausa -->
            <div class="language-card hausa">
                <h3>
                    <span class="language-flag">🇳🇬</span>
                    Hausa (Harshen Hausa)
                </h3>
                <div class="sample-text">
                    "Maraba zuwa CT NIGERIA LTD. Ni ne mai taimako AI Project Manager naku. Ta yaya zan iya taimaka muku sarrafa ayyukanku yau?"
                </div>
                <button class="button" onclick="testLanguageVoice('ha-NG')">
                    🔊 Gwada Murya
                </button>
                <button class="button" onclick="startVoiceRecognition('ha-NG')">
                    🎤 Gane Murya
                </button>
                <button class="button" onclick="chatWithAgent('ha-NG')">
                    💬 Hira da AI
                </button>
            </div>

            <!-- Yoruba -->
            <div class="language-card yoruba">
                <h3>
                    <span class="language-flag">🇳🇬</span>
                    Yoruba (Èdè Yorùbá)
                </h3>
                <div class="sample-text">
                    "Kaabo si CT NIGERIA LTD. Emi ni oluranlowo AI Project Manager yin. Bawo ni mo se le ran yin lowo lati sakoso awon ise yin loni?"
                </div>
                <button class="button" onclick="testLanguageVoice('yo-NG')">
                    🔊 Danwo Ohun
                </button>
                <button class="button" onclick="startVoiceRecognition('yo-NG')">
                    🎤 Gbo Ohun
                </button>
                <button class="button" onclick="chatWithAgent('yo-NG')">
                    💬 Ba AI Soro
                </button>
            </div>
        </div>

        <!-- AI Project Manager Agent Section -->
        <div class="project-manager-section">
            <h3>
                🤖 AI Project Manager Agent
                <span style="font-size: 0.8rem; background: #10b981; color: white; padding: 2px 8px; border-radius: 12px;">Enhanced</span>
            </h3>
            <p>Advanced AI assistant with project analysis, risk assessment, and intelligent recommendations.</p>
            
            <div style="display: flex; gap: 10px; flex-wrap: wrap; margin: 15px 0;">
                <button class="button" onclick="analyzeProject()">
                    📊 Analyze Project
                </button>
                <button class="button" onclick="getRiskAssessment()">
                    ⚠️ Risk Assessment
                </button>
                <button class="button" onclick="getRecommendations()">
                    💡 Get Recommendations
                </button>
                <button class="button" onclick="teamOptimization()">
                    👥 Team Optimization
                </button>
                <button class="button" onclick="timelineAnalysis()">
                    ⏱️ Timeline Analysis
                </button>
            </div>

            <div class="conversation-area" id="agentConversation">
                <div class="message agent">
                    <strong>AI Project Manager:</strong> Hello! I'm your enhanced AI Project Manager Assistant. I can analyze projects, assess risks, optimize team performance, and provide intelligent recommendations. How can I help you today?
                </div>
            </div>

            <div style="display: flex; gap: 10px; margin-top: 15px;">
                <input 
                    type="text" 
                    id="agentInput" 
                    placeholder="Ask me about project management, risks, or optimizations..."
                    style="flex: 1; padding: 10px; border: 1px solid #e5e7eb; border-radius: 6px;"
                    onkeypress="if(event.key==='Enter') sendToAgent()"
                >
                <button class="button" onclick="sendToAgent()">
                    📤 Send
                </button>
                <button class="button" id="voiceInputBtn" onclick="toggleVoiceInput()">
                    🎤 Voice
                </button>
            </div>
        </div>

        <!-- Voice Commands Examples -->
        <div>
            <h3>🎯 Try These Voice Commands (Any Language)</h3>
            <div class="voice-commands-grid">
                <div class="voice-command" onclick="simulateCommand('Analyze my project')">
                    "Analyze my project"
                </div>
                <div class="voice-command" onclick="simulateCommand('What are the risks?')">
                    "What are the risks?"
                </div>
                <div class="voice-command" onclick="simulateCommand('Show team performance')">
                    "Show team performance"
                </div>
                <div class="voice-command" onclick="simulateCommand('Optimize timeline')">
                    "Optimize timeline"
                </div>
                <div class="voice-command" onclick="simulateCommand('Nyochaa ọrụ m')">
                    "Nyochaa ọrụ m" (Igbo)
                </div>
                <div class="voice-command" onclick="simulateCommand('Nuna hadari')">
                    "Nuna hadari" (Hausa)
                </div>
                <div class="voice-command" onclick="simulateCommand('Wo iṣẹ mi')">
                    "Wo iṣẹ mi" (Yoruba)
                </div>
                <div class="voice-command" onclick="simulateCommand('Help me navigate')">
                    "Help me navigate"
                </div>
            </div>
        </div>

        <!-- Enhanced Features -->
        <div>
            <h3>🚀 Enhanced Voice System Features</h3>
            <div class="feature-showcase">
                <div class="feature-item">
                    <div class="feature-icon">🇳🇬</div>
                    <h4>Nigerian Languages</h4>
                    <p>Full support for Igbo, Hausa, and Yoruba with native voice synthesis</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🤖</div>
                    <h4>AI Project Manager</h4>
                    <p>Advanced project analysis, risk assessment, and optimization recommendations</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🎤</div>
                    <h4>Multi-Language Voice</h4>
                    <p>Voice recognition and synthesis in all supported languages</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">💡</div>
                    <h4>Intelligent Insights</h4>
                    <p>Real-time project insights, bottleneck detection, and performance optimization</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">🔄</div>
                    <h4>Real-time Translation</h4>
                    <p>Seamless communication across all Nigerian languages</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">📊</div>
                    <h4>Advanced Analytics</h4>
                    <p>Comprehensive project metrics, team utilization, and predictive analysis</p>
                </div>
            </div>
        </div>

        <!-- Test All Features Button -->
        <div style="text-align: center; margin: 30px 0;">
            <button id="testAllFeatures" class="button" style="font-size: 18px; padding: 15px 30px;">
                🧪 Test All Enhanced Features
            </button>
        </div>
    </div>

    <script>
        // Enhanced Voice System State
        let currentLanguage = 'en-GB';
        let isListening = false;
        let isSpeaking = false;
        let recognition = null;
        let synthesis = null;
        let agentConversation = [];

        // Language configurations
        const languages = {
            'en-GB': {
                name: 'English (UK)',
                flag: '🇬🇧',
                greeting: 'Hello! I\'m your AI Project Manager Assistant.',
                sampleText: 'Welcome to CTN Nigeria. How may I assist you today?'
            },
            'ig-NG': {
                name: 'Igbo (Asụsụ Igbo)',
                flag: '🇳🇬',
                greeting: 'Nnọọ! Abụ m onye inyeaka AI Project Manager gị.',
                sampleText: 'Nnọọ na CTN Nigeria. Kedu ka m ga-esi nyere gị aka taa?'
            },
            'ha-NG': {
                name: 'Hausa (Harshen Hausa)',
                flag: '🇳🇬',
                greeting: 'Sannu! Ni ne mai taimako AI Project Manager naku.',
                sampleText: 'Maraba zuwa CTN Nigeria. Ta yaya zan iya taimaka muku yau?'
            },
            'yo-NG': {
                name: 'Yoruba (Èdè Yorùbá)',
                flag: '🇳🇬',
                greeting: 'Bawo! Emi ni oluranlowo AI Project Manager yin.',
                sampleText: 'Kaabo si CTN Nigeria. Bawo ni mo se le ran yin lowo loni?'
            }
        };

        // Initialize enhanced voice system
        async function initializeEnhancedVoiceSystem() {
            try {
                showStatus('Initializing enhanced voice system with Nigerian languages...', 'info');
                
                // Check browser support
                if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
                    throw new Error('Speech recognition not supported in this browser');
                }
                
                if (!('speechSynthesis' in window)) {
                    throw new Error('Speech synthesis not supported in this browser');
                }

                // Initialize speech recognition
                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
                recognition = new SpeechRecognition();
                recognition.continuous = false;
                recognition.interimResults = true;
                recognition.lang = currentLanguage;

                // Initialize speech synthesis
                synthesis = window.speechSynthesis;

                // Set up event listeners
                setupEventListeners();
                setupLanguageSelector();

                showStatus('Enhanced voice system ready! Select a language and start speaking.', 'success');
                
            } catch (error) {
                console.error('Enhanced voice initialization error:', error);
                showStatus(`Initialization failed: ${error.message}`, 'error');
            }
        }

        function setupEventListeners() {
            // Speech recognition events
            recognition.onstart = () => {
                isListening = true;
                updateVoiceButton(true);
                log('🎤 Voice recognition started');
            };

            recognition.onend = () => {
                isListening = false;
                updateVoiceButton(false);
                log('🎤 Voice recognition ended');
            };

            recognition.onresult = (event) => {
                handleSpeechResult(event);
            };

            recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                showStatus(`Speech recognition error: ${event.error}`, 'error');
                isListening = false;
                updateVoiceButton(false);
            };

            // Test all features button
            document.getElementById('testAllFeatures').onclick = testAllEnhancedFeatures;
        }

        function setupLanguageSelector() {
            const languageOptions = document.querySelectorAll('.language-option');
            languageOptions.forEach(option => {
                option.onclick = () => {
                    // Remove selected class from all options
                    languageOptions.forEach(opt => opt.classList.remove('selected'));
                    // Add selected class to clicked option
                    option.classList.add('selected');
                    // Change language
                    changeLanguage(option.dataset.lang);
                };
            });
        }

        function changeLanguage(langCode) {
            currentLanguage = langCode;
            if (recognition) {
                recognition.lang = langCode;
            }
            
            const lang = languages[langCode];
            showStatus(`Language changed to ${lang.name}`, 'success');
            
            // Speak greeting in new language
            speakText(lang.greeting);
            
            log(`🌍 Language changed to: ${lang.name}`);
        }

        function testLanguageVoice(langCode) {
            const lang = languages[langCode];
            if (lang) {
                speakText(lang.sampleText, langCode);
            }
        }

        function startVoiceRecognition(langCode) {
            if (langCode !== currentLanguage) {
                changeLanguage(langCode);
            }
            
            try {
                if (recognition && !isListening) {
                    recognition.start();
                    showStatus(`Listening in ${languages[langCode].name}...`, 'info');
                }
            } catch (error) {
                console.error('Error starting voice recognition:', error);
                showStatus(`Failed to start listening: ${error.message}`, 'error');
            }
        }

        function chatWithAgent(langCode) {
            if (langCode !== currentLanguage) {
                changeLanguage(langCode);
            }
            
            const lang = languages[langCode];
            addAgentMessage(`${lang.greeting} How can I help you with your projects today?`);
            speakText(lang.greeting);
        }

        function handleSpeechResult(event) {
            let transcript = '';
            let isFinal = false;

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const result = event.results[i];
                transcript += result[0].transcript;
                if (result.isFinal) {
                    isFinal = true;
                }
            }

            if (isFinal) {
                processVoiceCommand(transcript, event.results[event.results.length - 1][0].confidence);
            }
        }

        async function processVoiceCommand(command, confidence) {
            try {
                log(`🎯 Processing command: "${command}" (confidence: ${(confidence * 100).toFixed(1)}%)`);
                
                addUserMessage(command);
                
                // Process with AI Project Manager
                const response = await processWithProjectManager(command);
                
                addAgentMessage(response);
                await speakText(response);
                
            } catch (error) {
                console.error('Error processing voice command:', error);
                const errorMsg = getLocalizedText('error_processing');
                addAgentMessage(errorMsg);
                await speakText(errorMsg);
            }
        }

        async function processWithProjectManager(message) {
            const lowerMessage = message.toLowerCase();
            
            // Project analysis
            if (lowerMessage.includes('analyze') || lowerMessage.includes('nyochaa') || lowerMessage.includes('nazari') || lowerMessage.includes('wo')) {
                return getLocalizedText('project_analysis');
            }
            
            // Risk assessment
            if (lowerMessage.includes('risk') || lowerMessage.includes('hadari') || lowerMessage.includes('ewu')) {
                return getLocalizedText('risk_assessment');
            }
            
            // Team optimization
            if (lowerMessage.includes('team') || lowerMessage.includes('otu') || lowerMessage.includes('kungiya') || lowerMessage.includes('egbe')) {
                return getLocalizedText('team_optimization');
            }
            
            // Default response
            return getLocalizedText('default_response');
        }

        function getLocalizedText(key) {
            const texts = {
                'project_analysis': {
                    'en-GB': 'I\'ve analyzed your project. Health score is 85/100. I found 3 optimization opportunities and 2 potential risks that need attention.',
                    'ig-NG': 'Enyochala m ọrụ gị. Akara ahụike bụ 85/100. Achọtara m ohere nkwalite 3 na ihe ize ndụ 2 nke chọrọ nlebara anya.',
                    'ha-NG': 'Na nazari aikinku. Maki lafiya shine 85/100. Na sami damar ingantawa 3 da hadari 2 da suke bukatar kulawa.',
                    'yo-NG': 'Mo ti se ayewo ise yin. Ami ilera jẹ 85/100. Mo ri awon anfani ilọsiwaju 3 ati awon ewu 2 ti o nilo akiyesi.'
                },
                'risk_assessment': {
                    'en-GB': 'Risk assessment complete. I identified 2 high-priority risks: timeline delays and resource constraints. Immediate action recommended.',
                    'ig-NG': 'Nyocha ihe ize ndụ zuru oke. Achọpụtara m ihe ize ndụ 2 dị mkpa: igbu oge na mmachi akụrụngwa. A na-atụ aro ime ihe ozugbo.',
                    'ha-NG': 'Nazarin hadari ya kammala. Na gano hadari 2 masu muhimmanci: jinkirin lokaci da karancin kayan aiki. Ana ba da shawarar gaggawar mataki.',
                    'yo-NG': 'Ayewo ewu ti pari. Mo rii awon ewu pataki 2: idaduro akoko ati idiwon ohun elo. A gba imoran fun igbese lẹsẹkẹsẹ.'
                },
                'team_optimization': {
                    'en-GB': 'Team analysis shows 78% utilization. I recommend redistributing 3 tasks and adding 1 team member to optimize performance.',
                    'ig-NG': 'Nyocha otu na-egosi ojiji 78%. Ana m atụ aro ikesa ọrụ 3 na ịgbakwunye onye otu 1 iji kwalite arụmọrụ.',
                    'ha-NG': 'Nazarin kungiya ya nuna amfani da kashi 78%. Ina ba da shawarar sake rarraba ayyuka 3 da kara memba 1 don inganta aiki.',
                    'yo-NG': 'Ayewo egbe fi han lilo 78%. Mo gba imoran lati tun pin awon ise 3 ati fi omo egbe 1 kun lati mu iṣẹ dara si.'
                },
                'default_response': {
                    'en-GB': 'I understand. I can help with project analysis, risk assessment, team optimization, and timeline management. What would you like to explore?',
                    'ig-NG': 'Aghọtara m. Enwere m ike inyere aka na nyocha ọrụ, nyocha ihe ize ndụ, nkwalite otu, na njikwa oge. Kedu ihe ị chọrọ inyocha?',
                    'ha-NG': 'Na gane. Zan iya taimakawa da nazarin aiki, nazarin hadari, inganta kungiya, da sarrafa lokaci. Me kuke son ku bincika?',
                    'yo-NG': 'Mo ye mi. Mo le ran yin lowo pelu ayewo ise, ayewo ewu, ilọsiwaju egbe, ati isakoso akoko. Kini e fe wo?'
                },
                'error_processing': {
                    'en-GB': 'I apologize, but I encountered an error processing your request. Please try again.',
                    'ig-NG': 'Ewela iwe, mana enwetara m nsogbu na-edozi arịrịọ gị. Biko nwaa ọzọ.',
                    'ha-NG': 'Na yi hakuri, amma na sami matsala wajen sarrafa bukatarku. Da fatan za ku sake gwadawa.',
                    'yo-NG': 'Mo tọrọ gafara, ṣugbọn mo pade aṣiṣe ni sisẹ ibeere yin. Jọwọ gbiyanju lẹẹkansi.'
                }
            };
            
            return texts[key]?.[currentLanguage] || texts[key]?.['en-GB'] || 'Response not available';
        }

        async function speakText(text, langCode = null) {
            return new Promise((resolve) => {
                if (!synthesis) {
                    resolve();
                    return;
                }

                synthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = langCode || currentLanguage;
                utterance.rate = 1.0;
                utterance.pitch = 1.0;
                utterance.volume = 1.0;

                utterance.onstart = () => {
                    isSpeaking = true;
                    log(`🔊 Speaking: "${text.substring(0, 50)}..."`);
                };

                utterance.onend = () => {
                    isSpeaking = false;
                    log('🔊 Speech completed');
                    resolve();
                };

                utterance.onerror = (event) => {
                    console.error('Speech synthesis error:', event.error);
                    isSpeaking = false;
                    resolve();
                };

                synthesis.speak(utterance);
            });
        }

        // Project Manager Agent Functions
        function analyzeProject() {
            const response = getLocalizedText('project_analysis');
            addAgentMessage(response);
            speakText(response);
        }

        function getRiskAssessment() {
            const response = getLocalizedText('risk_assessment');
            addAgentMessage(response);
            speakText(response);
        }

        function getRecommendations() {
            const response = 'Based on my analysis, I recommend: 1) Prioritize overdue tasks, 2) Reallocate team resources, 3) Implement daily standups, 4) Review project scope.';
            addAgentMessage(response);
            speakText(response);
        }

        function teamOptimization() {
            const response = getLocalizedText('team_optimization');
            addAgentMessage(response);
            speakText(response);
        }

        function timelineAnalysis() {
            const response = 'Timeline analysis shows project is 15% behind schedule. Estimated completion: 3 weeks delay. Critical path optimization recommended.';
            addAgentMessage(response);
            speakText(response);
        }

        function sendToAgent() {
            const input = document.getElementById('agentInput');
            const message = input.value.trim();
            if (message) {
                input.value = '';
                processVoiceCommand(message, 1.0);
            }
        }

        function toggleVoiceInput() {
            if (isListening) {
                recognition.stop();
            } else {
                startVoiceRecognition(currentLanguage);
            }
        }

        function addUserMessage(content) {
            const conversation = document.getElementById('agentConversation');
            const message = document.createElement('div');
            message.className = 'message user';
            message.innerHTML = `<strong>You:</strong> ${content}`;
            conversation.appendChild(message);
            conversation.scrollTop = conversation.scrollHeight;
        }

        function addAgentMessage(content) {
            const conversation = document.getElementById('agentConversation');
            const message = document.createElement('div');
            message.className = 'message agent';
            message.innerHTML = `<strong>AI Project Manager:</strong> ${content}`;
            conversation.appendChild(message);
            conversation.scrollTop = conversation.scrollHeight;
        }

        function updateVoiceButton(listening) {
            const btn = document.getElementById('voiceInputBtn');
            if (listening) {
                btn.textContent = '🛑 Stop';
                btn.classList.add('active');
            } else {
                btn.textContent = '🎤 Voice';
                btn.classList.remove('active');
            }
        }

        // Utility Functions
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = `<strong>Status:</strong> ${message}`;
        }

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            console.log(`[${timestamp}] ${message}`);
        }

        function simulateCommand(command) {
            log(`🎯 Simulating command: "${command}"`);
            processVoiceCommand(command, 1.0);
        }

        async function testAllEnhancedFeatures() {
            showStatus('Running comprehensive enhanced voice system test...', 'info');
            
            try {
                // Test each language
                for (const [langCode, lang] of Object.entries(languages)) {
                    log(`Testing ${lang.name}...`);
                    changeLanguage(langCode);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    await speakText(lang.greeting);
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }
                
                // Test AI Project Manager features
                log('Testing AI Project Manager features...');
                analyzeProject();
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                getRiskAssessment();
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                teamOptimization();
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                showStatus('All enhanced features tested successfully!', 'success');
                
            } catch (error) {
                showStatus(`Test failed: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', initializeEnhancedVoiceSystem);
    </script>
</body>
</html>
