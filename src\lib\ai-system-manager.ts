/**
 * AI System Manager - Comprehensive AI functionality coordinator
 * Manages all AI services, LangChain integration, and API keys
 */

import { supabase } from '@/integrations/supabase/client';

export interface AIServiceConfig {
  service: 'openai' | 'anthropic' | 'google' | 'azure' | 'custom';
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  isActive: boolean;
}

export interface AIResponse {
  success: boolean;
  response?: string;
  error?: string;
  metadata?: {
    model: string;
    tokensUsed: number;
    responseTime: number;
    service: string;
  };
}

export interface AIInteraction {
  id: string;
  userId: string;
  sessionId: string;
  input: string;
  output: string;
  service: string;
  model: string;
  tokensUsed: number;
  cost: number;
  timestamp: Date;
  metadata: Record<string, any>;
}

class AISystemManager {
  private apiKeys: Map<string, AIServiceConfig> = new Map();
  private isInitialized = false;
  private currentSession: string = '';

  /**
   * Initialize the AI system
   */
  async initialize(userId: string): Promise<boolean> {
    try {
      // Load API keys from database
      await this.loadAPIKeys(userId);
      
      // Initialize AI services if available
      if (this.hasValidAPIKey('openai')) {
        await this.initializeAIServices();
      }
      
      // Create new session
      this.currentSession = this.generateSessionId();
      
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize AI system:', error);
      return false;
    }
  }

  /**
   * Load API keys from database
   */
  private async loadAPIKeys(userId: string): Promise<void> {
    const { data: keys, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true);

    if (error) {
      console.error('Failed to load API keys:', error);
      return;
    }

    this.apiKeys.clear();
    keys?.forEach(key => {
      this.apiKeys.set(key.service, {
        service: key.service,
        apiKey: key.api_key,
        model: key.model || this.getDefaultModel(key.service),
        temperature: key.temperature || 0.7,
        maxTokens: key.max_tokens || 4096,
        isActive: key.is_active
      });
    });
  }

  /**
   * Initialize AI services with available API keys
   */
  private async initializeAIServices(): Promise<void> {
    const openaiConfig = this.apiKeys.get('openai');
    if (openaiConfig) {
      // Set environment variable for AI services
      (window as any).__OPENAI_API_KEY__ = openaiConfig.apiKey;
      console.log('AI services initialized with OpenAI configuration');
    }
  }

  /**
   * Process AI request with fallback chain
   */
  async processRequest(
    input: string, 
    options: {
      service?: string;
      useMemory?: boolean;
      context?: string;
      userId?: string;
    } = {}
  ): Promise<AIResponse> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: 'AI system not initialized'
      };
    }

    const startTime = Date.now();
    let response: AIResponse;

    try {
      // Process with direct API calls only (LangChain removed)

      // Fallback to direct API calls
      response = await this.processWithDirectAPI(input, options);
      
      // Log interaction
      if (options.userId) {
        await this.logInteraction(input, response, options.userId);
      }

      return response;
    } catch (error) {
      console.error('AI request failed:', error);
      return {
        success: false,
        error: `AI request failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Process with LangChain (REMOVED - Native JS dependencies eliminated)
   */
  private async processWithLangChain(
    input: string,
    options: any
  ): Promise<AIResponse> {
    return {
      success: false,
      error: 'LangChain functionality has been removed to eliminate native JS dependencies'
    };
  }

  /**
   * Process with direct API
   */
  private async processWithDirectAPI(
    input: string, 
    options: any
  ): Promise<AIResponse> {
    const preferredService = options.service || 'openai';
    const config = this.apiKeys.get(preferredService);

    if (!config) {
      throw new Error(`No API key configured for ${preferredService}`);
    }

    // Call Supabase Edge Function
    const { data, error } = await supabase.functions.invoke('ai-assistant', {
      body: {
        message: input,
        service: preferredService,
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        context: options.context
      }
    });

    if (error) {
      throw error;
    }

    return {
      success: true,
      response: data.response,
      metadata: {
        model: config.model || 'unknown',
        tokensUsed: data.tokensUsed || 0,
        responseTime: data.responseTime || 0,
        service: preferredService
      }
    };
  }

  /**
   * Log AI interaction to database
   */
  private async logInteraction(
    input: string, 
    response: AIResponse, 
    userId?: string
  ): Promise<void> {
    if (!userId || !response.success) return;

    try {
      await supabase.from('ai_interactions').insert({
        user_id: userId,
        session_id: this.currentSession,
        interaction_type: 'chat',
        input_text: input,
        output_text: response.response,
        model_used: response.metadata?.model || 'unknown',
        tokens_used: response.metadata?.tokensUsed || 0,
        cost: this.calculateCost(response.metadata?.tokensUsed || 0, response.metadata?.service || 'openai'),
        status: 'completed',
        metadata: response.metadata || {}
      });
    } catch (error) {
      console.error('Failed to log AI interaction:', error);
    }
  }

  /**
   * Generate document with AI
   */
  async generateDocument(
    template: string,
    data: Record<string, any>,
    options: {
      format?: 'markdown' | 'html' | 'pdf';
      enhance?: boolean;
      userId?: string;
    } = {}
  ): Promise<AIResponse> {
    const prompt = this.buildDocumentPrompt(template, data, options.format);
    
    return this.processRequest(prompt, {
      service: 'openai',
      useMemory: false,
      context: 'document_generation',
      userId: options.userId
    });
  }

  /**
   * Analyze document with AI
   */
  async analyzeDocument(
    content: string,
    analysisType: 'summary' | 'sentiment' | 'keywords' | 'classification',
    userId?: string
  ): Promise<AIResponse> {
    const prompt = this.buildAnalysisPrompt(content, analysisType);
    
    return this.processRequest(prompt, {
      service: 'openai',
      useMemory: false,
      context: 'document_analysis',
      userId
    });
  }

  /**
   * Get system status
   */
  getSystemStatus(): {
    isInitialized: boolean;
    availableServices: string[];
    langChainAvailable: boolean;
    currentSession: string;
  } {
    return {
      isInitialized: this.isInitialized,
      availableServices: Array.from(this.apiKeys.keys()),
      langChainAvailable: false, // LangChain removed
      currentSession: this.currentSession
    };
  }

  /**
   * Utility methods
   */
  private hasValidAPIKey(service: string): boolean {
    const config = this.apiKeys.get(service);
    return config?.isActive && config.apiKey.length > 0;
  }

  private getDefaultModel(service: string): string {
    const defaults = {
      openai: 'gpt-3.5-turbo',
      anthropic: 'claude-3-sonnet-20240229',
      google: 'gemini-pro',
      azure: 'gpt-35-turbo',
      custom: 'custom-model'
    };
    return defaults[service as keyof typeof defaults] || 'unknown';
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateCost(tokens: number, service: string): number {
    const rates = {
      openai: 0.002 / 1000, // $0.002 per 1K tokens
      anthropic: 0.003 / 1000,
      google: 0.001 / 1000,
      azure: 0.002 / 1000,
      custom: 0.001 / 1000
    };
    return tokens * (rates[service as keyof typeof rates] || 0.001);
  }

  private buildDocumentPrompt(template: string, data: Record<string, any>, format?: string): string {
    return `Generate a ${format || 'markdown'} document based on the following template and data:

Template: ${template}

Data: ${JSON.stringify(data, null, 2)}

Please create a well-structured, professional document that incorporates all the provided data.`;
  }

  private buildAnalysisPrompt(content: string, analysisType: string): string {
    const prompts = {
      summary: 'Please provide a concise summary of the following content:',
      sentiment: 'Analyze the sentiment of the following content and provide a detailed assessment:',
      keywords: 'Extract the key terms and phrases from the following content:',
      classification: 'Classify the following content into appropriate categories:'
    };

    return `${prompts[analysisType as keyof typeof prompts] || prompts.summary}\n\n${content}`;
  }
}

// Export singleton instance
export const aiSystemManager = new AISystemManager();
