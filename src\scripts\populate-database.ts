import { Database } from '@/integrations/supabase/types';
import { supabase } from '../integrations/supabase/client';

type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
type DepartmentInsert = Database['public']['Tables']['departments']['Insert'];

interface Department {
  id?: string;
  name: string;
  description: string;
  manager_id?: string;
}

interface User {
  id?: string;
  full_name: string;
  email: string;
  role: string;
  department_id?: string;
}

const departments: Department[] = [
  {
    name: 'Engineering',
    description: 'Software development and technical operations',
  },
  {
    name: 'Operations',
    description: 'Day-to-day business operations and logistics',
  },
  {
    name: 'Finance',
    description: 'Financial planning, accounting, and budget management',
  },
  {
    name: 'Human Resources',
    description: 'Employee management and organizational development',
  },
  {
    name: 'Construction',
    description: 'Construction projects and site management',
  },
  {
    name: 'Fleet Management',
    description: 'Vehicle maintenance and fleet operations',
  },
  {
    name: 'Sales',
    description: 'Sales operations and customer relationship management',
  },
  {
    name: 'Marketing',
    description: 'Marketing campaigns and brand management',
  },
];

const sampleUsers: User[] = [
  // Admin
  {
    full_name: 'System Administrator',
    email: '<EMAIL>',
    role: 'admin',
  },
  // Engineering Department
  {
    full_name: 'John Engineering Manager',
    email: '<EMAIL>',
    role: 'manager',
  },
  {
    full_name: 'Alice Developer',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Bob Developer',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Carol QA Engineer',
    email: '<EMAIL>',
    role: 'staff',
  },
  // Operations Department
  {
    full_name: 'Sarah Operations Manager',
    email: '<EMAIL>',
    role: 'manager',
  },
  {
    full_name: 'Diana Operations',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Eric Operations',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Fiona Coordinator',
    email: '<EMAIL>',
    role: 'staff',
  },
  // Finance Department
  {
    full_name: 'Mike Finance Manager',
    email: '<EMAIL>',
    role: 'manager',
  },
  {
    full_name: 'Grace Accountant',
    email: '<EMAIL>',
    role: 'accountant',
  },
  {
    full_name: 'Henry Finance',
    email: '<EMAIL>',
    role: 'staff',
  },
  // Construction Department
  {
    full_name: 'David Construction Manager',
    email: '<EMAIL>',
    role: 'manager',
  },
  {
    full_name: 'Ivan Site Manager',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Judy Foreman',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Kevin Safety Officer',
    email: '<EMAIL>',
    role: 'staff',
  },
  // Fleet Management Department
  {
    full_name: 'Lisa Fleet Manager',
    email: '<EMAIL>',
    role: 'manager',
  },
  {
    full_name: 'Mark Driver',
    email: '<EMAIL>',
    role: 'staff',
  },
  {
    full_name: 'Nina Mechanic',
    email: '<EMAIL>',
    role: 'staff',
  },
];

async function populateDepartments() {
  console.log('🏢 Creating departments...');
  
  const createdDepartments: Department[] = [];
  
  for (const dept of departments) {
    try {
      const { data, error } = await supabase
        .from('departments')
        .upsert([dept], { onConflict: 'name' })
        .select()
        .single();
      
      if (error) {
        console.error(`Error creating department ${dept.name}:`, error);
        continue;
      }
      
      createdDepartments.push(data);
      console.log(`✅ Created department: ${dept.name}`);
    } catch (error) {
      console.error(`Failed to create department ${dept.name}:`, error);
    }
  }
  
  return createdDepartments;
}

async function populateUsers(departmentMap: Map<string, string>) {
  console.log('👥 Creating users...');
  
  const createdUsers: User[] = [];
  
  for (const user of sampleUsers) {
    try {
      // Determine department based on email pattern
      let departmentId: string | undefined;
      
      if (user.email.includes('engineering') || user.email.includes('dev') || user.email.includes('qa')) {
        departmentId = departmentMap.get('Engineering');
      } else if (user.email.includes('ops') || user.email.includes('coord')) {
        departmentId = departmentMap.get('Operations');
      } else if (user.email.includes('finance') || user.email.includes('accountant')) {
        departmentId = departmentMap.get('Finance');
      } else if (user.email.includes('site') || user.email.includes('foreman') || user.email.includes('safety')) {
        departmentId = departmentMap.get('Construction');
      } else if (user.email.includes('fleet') || user.email.includes('driver') || user.email.includes('mechanic')) {
        departmentId = departmentMap.get('Fleet Management');
      } else if (user.email.includes('manager') && user.full_name.includes('Operations')) {
        departmentId = departmentMap.get('Operations');
      } else if (user.email.includes('manager') && user.full_name.includes('Finance')) {
        departmentId = departmentMap.get('Finance');
      } else if (user.email.includes('manager') && user.full_name.includes('Construction')) {
        departmentId = departmentMap.get('Construction');
      } else if (user.email.includes('manager') && user.full_name.includes('Fleet')) {
        departmentId = departmentMap.get('Fleet Management');
      }
      
      const userData = {
        full_name: user.full_name,
        email: user.email,
        role: user.role,
        department_id: departmentId,
      };
      
      const { data, error } = await supabase
        .from('profiles')
        .upsert([userData as any], { onConflict: 'email' })
        .select()
        .single();
      
      if (error) {
        console.error(`Error creating user ${user.full_name}:`, error);
        continue;
      }
      
      createdUsers.push(data);
      console.log(`✅ Created user: ${user.full_name} (${user.role})`);
    } catch (error) {
      console.error(`Failed to create user ${user.full_name}:`, error);
    }
  }
  
  return createdUsers;
}

async function assignManagersToDepartments(departmentMap: Map<string, string>, users: User[]) {
  console.log('👨‍💼 Assigning managers to departments...');
  
        for (const [department, departmentId] of departmentMap.entries()) {
          // Find all managers in this department
          const managers = users.filter(user => user.role === 'manager' && user.department_id === departmentId);
          if (managers.length === 0) {
            console.warn(`No manager found for department ${department}`);
            continue;
          }
          // Assign the first manager found
          const manager = managers[0];
          try {
            const { error } = await supabase
              .from('departments')
              .update({ manager_id: manager.id })
              .eq('id', departmentId);
            if (error) {
              console.error(`[assignManagersToDepartments] Error assigning manager to ${department} (manager: ${manager.email}):`, error);
              continue;
            }
            console.log(`✅ Assigned ${manager.full_name} as manager of ${department}`);
          } catch (error) {
            console.error(`[assignManagersToDepartments] Failed to assign manager to ${department}:`, error);
          }
        }
  
  for (const assignment of managerAssignments) {
    try {
      const manager = users.find(user => user.email === assignment.managerEmail);
      const departmentId = departmentMap.get(assignment.department);
      
      if (!manager || !departmentId) {
        console.warn(`Could not find manager or department for ${assignment.department}`);
        continue;
      }
      
      const { error } = await supabase
        .from('departments')
        .update({ manager_id: manager.id })
        .eq('id', departmentId);
      
      if (error) {
        console.error(`Error assigning manager to ${assignment.department}:`, error);
        continue;
      }
      
      console.log(`✅ Assigned ${manager.full_name} as manager of ${assignment.department}`);
    } catch (error) {
      console.error(`Failed to assign manager to ${assignment.department}:`, error);
    }
  }
}

async function createSampleProjects(departmentMap: Map<string, string>) {
  console.log('📋 Creating sample projects...');
  
  const projects = [
    {
      name: 'Website Redesign',
      description: 'Complete overhaul of company website with modern design',
      department: 'Engineering',
      status: 'active',
    },
    {
      name: 'Mobile App Development',
      description: 'Develop new mobile application for customer portal',
      department: 'Engineering',
      status: 'active',
    },
    {
      name: 'Database Migration',
      description: 'Migrate legacy database to new cloud infrastructure',
      department: 'Engineering',
      status: 'planning',
    },
    {
      name: 'Office Renovation',
      description: 'Renovation of main office building',
      department: 'Construction',
      status: 'active',
    },
    {
      name: 'New Warehouse Construction',
      description: 'Construction of additional warehouse facility',
      department: 'Construction',
      status: 'planning',
    },
    {
      name: 'Fleet Maintenance Program',
      description: 'Comprehensive maintenance program for company vehicles',
      department: 'Fleet Management',
      status: 'active',
    },
  ];
  
  for (const project of projects) {
    try {
      const departmentId = departmentMap.get(project.department);
      if (!departmentId) {
        console.warn(`Could not find department ${project.department}`);
        continue;
      }
      
      const { error } = await supabase
        .from('projects')
        .upsert([{
          name: project.name,
          description: project.description,
          department_id: departmentId,
          status: project.status,
        }], { onConflict: 'name' });
      
      if (error) {
        console.error(`Error creating project ${project.name}:`, error);
        continue;
      }
      
      console.log(`✅ Created project: ${project.name}`);
    } catch (error) {
      console.error(`Failed to create project ${project.name}:`, error);
    }
  }
}

async function createSampleTasks(departmentMap: Map<string, string>) {
  console.log('📝 Creating sample tasks...');
  
  const tasks = [
    {
      title: 'Setup Development Environment',
      description: 'Configure local development environment for new project',
      department: 'Engineering',
      priority: 'medium',
      status: 'pending',
    },
    {
      title: 'Database Schema Design',
      description: 'Design database schema for new customer portal',
      department: 'Engineering',
      priority: 'high',
      status: 'in_progress',
    },
    {
      title: 'Safety Inspection',
      description: 'Conduct monthly safety inspection of construction site',
      department: 'Construction',
      priority: 'high',
      status: 'pending',
    },
    {
      title: 'Vehicle Maintenance Check',
      description: 'Perform routine maintenance on fleet vehicles',
      department: 'Fleet Management',
      priority: 'medium',
      status: 'pending',
    },
    {
      title: 'Monthly Financial Report',
      description: 'Prepare monthly financial report for management',
      department: 'Finance',
      priority: 'high',
      status: 'in_progress',
    },
  ];
  
  for (const task of tasks) {
    try {
      const departmentId = departmentMap.get(task.department);
      if (!departmentId) {
        console.warn(`Could not find department ${task.department}`);
        continue;
      }
      
      const { error } = await supabase
        .from('tasks')
        .upsert([{
          title: task.title,
          description: task.description,
          department_id: departmentId,
          priority: task.priority,
          status: task.status,
        }], { onConflict: 'title' });
      
      if (error) {
        console.error(`Error creating task ${task.title}:`, error);
        continue;
      }
      
      console.log(`✅ Created task: ${task.title}`);
    } catch (error) {
      console.error(`Failed to create task ${task.title}:`, error);
    }
  }
}

export async function populateDatabase() {
  console.log('🚀 Starting database population...');
  
  try {
    // Step 1: Create departments
    const createdDepartments = await populateDepartments();
    
    // Create department name to ID mapping
    const departmentMap = new Map<string, string>();
    createdDepartments.forEach(dept => {
      if (dept.id) {
        departmentMap.set(dept.name, dept.id);
      }
    });
    
    // Step 2: Create users with department assignments
    const createdUsers = await populateUsers(departmentMap);
    
    // Step 3: Assign managers to departments
    await assignManagersToDepartments(departmentMap, createdUsers);
    
    // Step 4: Create sample projects
    await createSampleProjects(departmentMap);
    
    // Step 5: Create sample tasks
    await createSampleTasks(departmentMap);
    
    console.log('✅ Database population completed successfully!');
    
    // Print summary
    console.log('\n📊 Summary:');
    console.log(`- Departments created: ${createdDepartments.length}`);
    console.log(`- Users created: ${createdUsers.length}`);
    console.log(`- Managers assigned: ${createdDepartments.filter(d => d.manager_id).length}`);
    
    return {
      success: true,
      departmentCount: createdDepartments.length,
      userCount: createdUsers.length,
      managerCount: createdDepartments.filter(d => d.manager_id).length
    };
    
  } catch (error) {
    console.error('❌ Database population failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

// Function to check if database needs population
export async function checkDatabaseStatus() {
  try {
    const { data: departments } = await supabase.from('departments').select('count', { count: 'exact' });
    const { data: profiles } = await supabase.from('profiles').select('count', { count: 'exact' });
    
    return {
      isEmpty: (departments?.[0]?.count || 0) === 0 && (profiles?.[0]?.count || 0) === 0,
      departmentCount: departments?.[0]?.count || 0,
      profileCount: profiles?.[0]?.count || 0
    };
  } catch (error) {
    console.error('Error checking database status:', error);
    return { isEmpty: true, departmentCount: 0, profileCount: 0 };
  }
} 
