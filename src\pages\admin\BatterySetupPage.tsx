import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  Database, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Play,
  Battery,
  MapPin,
  Settings
} from 'lucide-react';
import { setupBatteryTables } from '@/scripts/setup-battery-tables';
import { toast } from 'sonner';

export const BatterySetupPage: React.FC = () => {
  const [isSetupRunning, setIsSetupRunning] = useState(false);
  const [setupProgress, setSetupProgress] = useState(0);
  const [setupStatus, setSetupStatus] = useState<'idle' | 'running' | 'success' | 'error'>('idle');
  const [setupLogs, setSetupLogs] = useState<string[]>([]);

  const runSetup = async () => {
    setIsSetupRunning(true);
    setSetupStatus('running');
    setSetupProgress(0);
    setSetupLogs([]);

    try {
      // Step 1: Initialize
      setSetupLogs(prev => [...prev, '🔋 Starting battery management system setup...']);
      setSetupProgress(10);

      // Step 2: Create tables
      setSetupLogs(prev => [...prev, '📊 Creating database tables...']);
      setSetupProgress(30);

      const result = await setupBatteryTables();

      if (result.success) {
        setSetupProgress(70);
        setSetupLogs(prev => [...prev, '✅ Database tables created successfully']);

        // Step 3: Verify setup
        setSetupLogs(prev => [...prev, '🔍 Verifying setup...']);
        setSetupProgress(90);

        // Step 4: Complete
        setSetupProgress(100);
        setSetupLogs(prev => [...prev, '🎉 Battery management system setup completed!']);
        setSetupStatus('success');
        toast.success('Battery management system setup completed successfully!');
      } else {
        throw new Error(result.error?.message || 'Setup failed');
      }
    } catch (error) {
      console.error('Setup error:', error);
      setSetupLogs(prev => [...prev, `❌ Setup failed: ${error instanceof Error ? error.message : 'Unknown error'}`]);
      setSetupStatus('error');
      toast.error('Setup failed. Please check the logs and try again.');
    } finally {
      setIsSetupRunning(false);
    }
  };

  const getStatusColor = () => {
    switch (setupStatus) {
      case 'success': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'running': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = () => {
    switch (setupStatus) {
      case 'success': return <CheckCircle className="h-4 w-4" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      case 'running': return <Loader2 className="h-4 w-4 animate-spin" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Battery className="h-8 w-8" />
            Battery Management Setup
          </h1>
          <p className="text-muted-foreground mt-1">
            Initialize the battery management system database tables and configuration
          </p>
        </div>
        <Badge className={getStatusColor()}>
          {getStatusIcon()}
          <span className="ml-2 capitalize">{setupStatus}</span>
        </Badge>
      </div>

      {/* Setup Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="neumorphism-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Database className="h-8 w-8 text-blue-500" />
              <div>
                <h3 className="font-semibold">Database Tables</h3>
                <p className="text-sm text-muted-foreground">6 tables with audit columns</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="neumorphism-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <Battery className="h-8 w-8 text-green-500" />
              <div>
                <h3 className="font-semibold">Battery Types</h3>
                <p className="text-sm text-muted-foreground">Sample battery configurations</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="neumorphism-card">
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <MapPin className="h-8 w-8 text-purple-500" />
              <div>
                <h3 className="font-semibold">Locations</h3>
                <p className="text-sm text-muted-foreground">Sites and storage facilities</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Setup Control */}
      <Card className="neumorphism-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Setup Control
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {setupStatus === 'idle' && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                This will create the battery management database tables and insert sample data. 
                Make sure you have proper database permissions.
              </AlertDescription>
            </Alert>
          )}

          {setupStatus === 'success' && (
            <Alert className="border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                Battery management system has been successfully set up! You can now navigate to the 
                Battery Management page to start managing your battery inventory.
              </AlertDescription>
            </Alert>
          )}

          {setupStatus === 'error' && (
            <Alert className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800 dark:text-red-200">
                Setup failed. Please check the logs below and ensure you have proper database permissions.
              </AlertDescription>
            </Alert>
          )}

          {isSetupRunning && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Setup Progress</span>
                <span>{setupProgress}%</span>
              </div>
              <Progress value={setupProgress} className="w-full" />
            </div>
          )}

          <div className="flex gap-3">
            <Button 
              onClick={runSetup} 
              disabled={isSetupRunning || setupStatus === 'success'}
              className="flex items-center gap-2"
            >
              {isSetupRunning ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Running Setup...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Run Setup
                </>
              )}
            </Button>

            {setupStatus === 'success' && (
              <Button 
                variant="outline" 
                onClick={() => window.location.href = '/dashboard/battery'}
              >
                Go to Battery Management
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Setup Logs */}
      {setupLogs.length > 0 && (
        <Card className="neumorphism-card">
          <CardHeader>
            <CardTitle>Setup Logs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm space-y-1 max-h-64 overflow-y-auto">
              {setupLogs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Database Schema Information */}
      <Card className="neumorphism-card">
        <CardHeader>
          <CardTitle>Database Schema Overview</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Core Tables</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• battery_types - Battery specifications</li>
                <li>• battery_locations - Storage locations</li>
                <li>• batteries - Main inventory</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Tracking Tables</h4>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• battery_readings - Performance data</li>
                <li>• battery_maintenance - Service records</li>
                <li>• battery_transfers - Movement history</li>
              </ul>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-semibold">Audit Columns (All Tables)</h4>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• created_by, updated_by - User tracking</li>
              <li>• profile_id - Record ownership</li>
              <li>• generated_by - System/device identification</li>
              <li>• created_at, updated_at - Timestamp tracking</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
