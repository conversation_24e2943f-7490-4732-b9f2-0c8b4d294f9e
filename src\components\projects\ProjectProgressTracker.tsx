import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  Users, 
  Target,
  AlertTriangle,
  CheckCircle,
  Calendar,
  BarChart3,
  Plus,
  Edit,
  Save
} from "lucide-react";

interface ProjectProgress {
  project_id: string;
  project_name: string;
  overall_progress: number;
  task_progress: number;
  assignment_progress: number;
  total_tasks: number;
  completed_tasks: number;
  total_assignments: number;
  health_score: number;
  risk_level: string;
}

interface ProjectProgressTrackerProps {
  projectId: string;
  onProgressUpdate?: (progress: number) => void;
}

export const ProjectProgressTracker: React.FC<ProjectProgressTrackerProps> = ({
  projectId,
  onProgressUpdate
}) => {
  const [progress, setProgress] = useState<ProjectProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [showUpdateForm, setShowUpdateForm] = useState(false);
  const [updateData, setUpdateData] = useState({
    progress_percentage: 0,
    hours_worked: 0,
    description: '',
    update_type: 'progress' as const
  });
  const { toast } = useToast();

  useEffect(() => {
    loadProjectProgress();
  }, [projectId]);

  const loadProjectProgress = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .rpc('get_project_progress_details', { project_uuid: projectId });

      if (error) throw error;

      if (data && data.length > 0) {
        setProgress(data[0]);
        setUpdateData(prev => ({
          ...prev,
          progress_percentage: data[0].overall_progress
        }));
      }
    } catch (error: any) {
      console.error('Error loading project progress:', error);
      toast({
        title: "Error",
        description: "Failed to load project progress",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updateProjectProgress = async () => {
    setUpdating(true);
    try {
      // Calculate new progress
      const { data: newProgress, error: calcError } = await supabase
        .rpc('calculate_project_progress', { project_uuid: projectId });

      if (calcError) throw calcError;

      // Record progress update
      const { error: updateError } = await supabase
        .from('project_progress_updates')
        .insert({
          project_id: projectId,
          updated_by: (await supabase.auth.getUser()).data.user?.id,
          previous_progress: progress?.overall_progress || 0,
          new_progress: updateData.progress_percentage,
          hours_worked: updateData.hours_worked,
          update_type: updateData.update_type,
          description: updateData.description
        });

      if (updateError) throw updateError;

      await loadProjectProgress();
      setShowUpdateForm(false);
      onProgressUpdate?.(newProgress);

      toast({
        title: "Success",
        description: "Project progress updated successfully",
      });
    } catch (error: any) {
      console.error('Error updating project progress:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update project progress",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-orange-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getHealthColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!progress) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-yellow-500" />
          <h3 className="text-lg font-semibold mb-2">No Progress Data</h3>
          <p className="text-muted-foreground">Unable to load project progress information.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Progress Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Project Progress
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowUpdateForm(!showUpdateForm)}
          >
            {showUpdateForm ? <Save className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
            {showUpdateForm ? 'Cancel' : 'Update'}
          </Button>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Overall Progress */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label className="text-sm font-medium">Overall Progress</Label>
              <span className="text-2xl font-bold">{progress.overall_progress}%</span>
            </div>
            <Progress value={progress.overall_progress} className="h-3" />
          </div>

          {/* Progress Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <Label className="text-sm">Task Progress</Label>
                <span className="font-semibold">{progress.task_progress}%</span>
              </div>
              <Progress value={progress.task_progress} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {progress.completed_tasks} of {progress.total_tasks} tasks completed
              </p>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <Label className="text-sm">Assignment Progress</Label>
                <span className="font-semibold">{progress.assignment_progress}%</span>
              </div>
              <Progress value={progress.assignment_progress} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {progress.total_assignments} team assignments
              </p>
            </div>
          </div>

          {/* Health & Risk Indicators */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <div className={`p-2 rounded-full ${getHealthColor(progress.health_score)}`}>
                {progress.health_score >= 80 ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertTriangle className="h-4 w-4" />
                )}
              </div>
              <div>
                <p className="text-sm font-medium">Health Score</p>
                <p className={`text-lg font-bold ${getHealthColor(progress.health_score)}`}>
                  {progress.health_score}%
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className={`p-2 rounded-full ${getRiskColor(progress.risk_level)}`}>
                <TrendingUp className="h-4 w-4" />
              </div>
              <div>
                <p className="text-sm font-medium">Risk Level</p>
                <Badge variant="outline" className={getRiskColor(progress.risk_level)}>
                  {progress.risk_level.toUpperCase()}
                </Badge>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="p-2 rounded-full text-blue-600">
                <Users className="h-4 w-4" />
              </div>
              <div>
                <p className="text-sm font-medium">Team Size</p>
                <p className="text-lg font-bold">{progress.total_assignments}</p>
              </div>
            </div>
          </div>

          {/* Update Form */}
          {showUpdateForm && (
            <div className="border-t pt-4 space-y-4">
              <h4 className="font-semibold">Update Progress</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="progress">Progress Percentage</Label>
                  <Input
                    id="progress"
                    type="number"
                    min="0"
                    max="100"
                    value={updateData.progress_percentage}
                    onChange={(e) => setUpdateData(prev => ({
                      ...prev,
                      progress_percentage: parseInt(e.target.value) || 0
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="hours">Hours Worked</Label>
                  <Input
                    id="hours"
                    type="number"
                    min="0"
                    value={updateData.hours_worked}
                    onChange={(e) => setUpdateData(prev => ({
                      ...prev,
                      hours_worked: parseInt(e.target.value) || 0
                    }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="description">Update Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the progress made..."
                  value={updateData.description}
                  onChange={(e) => setUpdateData(prev => ({
                    ...prev,
                    description: e.target.value
                  }))}
                />
              </div>
              <Button 
                onClick={updateProjectProgress} 
                disabled={updating}
                className="w-full"
              >
                {updating ? 'Updating...' : 'Save Progress Update'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
