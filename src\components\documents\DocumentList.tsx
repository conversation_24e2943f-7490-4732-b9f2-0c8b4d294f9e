import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Download, 
  Eye, 
  Trash2, 
  Folder,
  MoreVertical,
  Calendar,
  User,
  HardDrive
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatBytes } from "@/lib/utils";
import { ModernDocumentCard } from "./ModernDocumentCard";

interface DocumentType {
  id: string;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  category: string;
  tags: string[] | null;
  created_at: string;
  updated_at: string;
  file_path: string;
  uploaded_by: string;
  uploader_name?: string;
  access_level: string;
}

interface Folder {
  id: string;
  name: string;
  document_count?: number;
  access_level: string;
}

interface DocumentListProps {
  documents: DocumentType[];
  folders: Folder[];
  onSelectDocument: (document: DocumentType) => void;
  onSelectFolder: (folderId: string) => void;
  onDeleteDocument: (documentId: string) => void;
}

export const DocumentList = ({
  documents,
  folders,
  onSelectDocument,
  onSelectFolder,
  onDeleteDocument
}: DocumentListProps) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const getFileIcon = (fileType: string) => {
    if (fileType.includes('image')) return '🖼️';
    if (fileType.includes('pdf')) return '📄';
    if (fileType.includes('word') || fileType.includes('document')) return '📝';
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) return '📊';
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) return '📈';
    return '📄';
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      general: 'bg-blue-100 text-blue-800',
      reports: 'bg-green-100 text-green-800',
      contracts: 'bg-purple-100 text-purple-800',
      invoices: 'bg-yellow-100 text-yellow-800',
      presentations: 'bg-red-100 text-red-800'
    };
    return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (viewMode === 'grid') {
    return (
      <div className="space-y-6">
        {/* Folders */}
        {folders.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Folders</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {folders.map((folder) => (
                <Card 
                  key={folder.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onSelectFolder(folder.id)}
                >
                  <CardContent className="p-4 text-center">
                    <Folder className="h-12 w-12 mx-auto mb-2 text-blue-500" />
                    <h4 className="font-medium truncate">{folder.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {folder.document_count || 0} items
                    </p>
                    <Badge variant="outline" className="mt-2">
                      {folder.access_level}
                    </Badge>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Documents */}
        {documents.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold mb-3">Documents</h3>
            <div className="flex flex-wrap gap-6 justify-center sm:justify-start">
              {documents.map((document) => (
                <ModernDocumentCard
                  key={document.id}
                  document={{
                    ...document,
                    tags: document.tags || []
                  }}
                  onDownload={(doc) => {
                    // Handle download logic
                    console.log('Download document:', doc.id);
                  }}
                  onDelete={(doc) => {
                    onDeleteDocument(doc.id);
                  }}
                  onPreview={(doc) => {
                    onSelectDocument(doc);
                  }}
                  onShare={(doc) => {
                    // Handle share logic
                    console.log('Share document:', doc.id);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {folders.length === 0 && documents.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">No documents or folders found</p>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* List view implementation would go here */}
      <p>List view not implemented yet. Switch to grid view.</p>
    </div>
  );
};