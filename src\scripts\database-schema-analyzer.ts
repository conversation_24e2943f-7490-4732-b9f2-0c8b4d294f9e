/**
 * Database Schema Analyzer
 * Scans for mismatches between expected tables and actual database schema
 */

import { supabase } from '@/integrations/supabase/client';

interface TableInfo {
  name: string;
  exists: boolean;
  columns?: string[];
  rowCount?: number;
  error?: string;
}

interface SchemaAnalysis {
  coreTablesStatus: TableInfo[];
  aiTablesStatus: TableInfo[];
  roleSpecificTablesStatus: TableInfo[];
  missingTables: string[];
  schemaIssues: string[];
  recommendations: string[];
}

export class DatabaseSchemaAnalyzer {
  
  // Core system tables that should exist
  private coreTablesList = [
    'profiles',
    'departments', 
    'tasks',
    'projects',
    'time_logs',
    'notifications',
    'recent_activity_logs',
    'system_activities'
  ];

  // AI-related tables
  private aiTablesList = [
    'conversation_history',
    'conversation_analytics',
    'langchain_operations', 
    'ai_interactions',
    'ai_results',
    'voice_commands',
    'system_health'
  ];

  // Role-specific tables
  private roleSpecificTablesList = [
    'invoices',           // Accountant
    'expenses',           // Accountant/Staff-Admin
    'budgets',           // Manager/Admin
    'leave_requests',    // All roles
    'leave_balances',    // HR/Manager
    'memos',             // Manager/Admin
    'telecom_sites',     // Manager/Admin
    'battery_management', // Staff-Admin
    'project_assignments', // Manager/Admin
    'email_notifications', // All roles
    'memo_notifications'   // Manager/Admin
  ];

  /**
   * Run comprehensive schema analysis
   */
  public async analyzeSchema(): Promise<SchemaAnalysis> {
    console.log('🔍 Starting database schema analysis...');

    const [coreTablesStatus, aiTablesStatus, roleSpecificTablesStatus] = await Promise.all([
      this.checkTablesList(this.coreTablesList, 'Core Tables'),
      this.checkTablesList(this.aiTablesList, 'AI Tables'),
      this.checkTablesList(this.roleSpecificTablesList, 'Role-Specific Tables')
    ]);

    const missingTables = [
      ...coreTablesStatus.filter(t => !t.exists).map(t => t.name),
      ...aiTablesStatus.filter(t => !t.exists).map(t => t.name),
      ...roleSpecificTablesStatus.filter(t => !t.exists).map(t => t.name)
    ];

    const schemaIssues = this.identifySchemaIssues(
      coreTablesStatus, 
      aiTablesStatus, 
      roleSpecificTablesStatus
    );

    const recommendations = this.generateRecommendations(missingTables, schemaIssues);

    return {
      coreTablesStatus,
      aiTablesStatus,
      roleSpecificTablesStatus,
      missingTables,
      schemaIssues,
      recommendations
    };
  }

  /**
   * Check a list of tables
   */
  private async checkTablesList(tableNames: string[], category: string): Promise<TableInfo[]> {
    console.log(`📋 Checking ${category}...`);
    
    const results: TableInfo[] = [];

    for (const tableName of tableNames) {
      try {
        // Try to query the table
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });

        if (error) {
          results.push({
            name: tableName,
            exists: false,
            error: error.message
          });
          console.log(`❌ ${tableName}: ${error.message}`);
        } else {
          // Get column information
          const { data: sampleData } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);

          const columns = sampleData && sampleData.length > 0 
            ? Object.keys(sampleData[0]) 
            : [];

          results.push({
            name: tableName,
            exists: true,
            columns,
            rowCount: count || 0
          });
          console.log(`✅ ${tableName}: ${count || 0} rows, ${columns.length} columns`);
        }
      } catch (err) {
        results.push({
          name: tableName,
          exists: false,
          error: `Query failed: ${err}`
        });
        console.log(`❌ ${tableName}: Query failed`);
      }
    }

    return results;
  }

  /**
   * Identify schema issues
   */
  private identifySchemaIssues(
    coreTablesStatus: TableInfo[],
    aiTablesStatus: TableInfo[],
    roleSpecificTablesStatus: TableInfo[]
  ): string[] {
    const issues: string[] = [];

    // Check for missing core tables
    const missingCoreTables = coreTablesStatus.filter(t => !t.exists);
    if (missingCoreTables.length > 0) {
      issues.push(`Missing ${missingCoreTables.length} core tables: ${missingCoreTables.map(t => t.name).join(', ')}`);
    }

    // Check for missing AI tables
    const missingAITables = aiTablesStatus.filter(t => !t.exists);
    if (missingAITables.length > 0) {
      issues.push(`Missing ${missingAITables.length} AI tables: ${missingAITables.map(t => t.name).join(', ')}`);
    }

    // Check for empty critical tables
    const emptyCoreTables = coreTablesStatus.filter(t => t.exists && t.rowCount === 0);
    if (emptyCoreTables.length > 0) {
      issues.push(`Empty core tables detected: ${emptyCoreTables.map(t => t.name).join(', ')}`);
    }

    // Check for tables with permission errors
    const permissionErrors = [
      ...coreTablesStatus,
      ...aiTablesStatus,
      ...roleSpecificTablesStatus
    ].filter(t => t.error && t.error.includes('permission'));

    if (permissionErrors.length > 0) {
      issues.push(`Permission errors on tables: ${permissionErrors.map(t => t.name).join(', ')}`);
    }

    return issues;
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(missingTables: string[], schemaIssues: string[]): string[] {
    const recommendations: string[] = [];

    if (missingTables.length > 0) {
      recommendations.push('Run database migrations to create missing tables');
      
      // Specific recommendations for missing table categories
      const missingAI = missingTables.filter(t => this.aiTablesList.includes(t));
      if (missingAI.length > 0) {
        recommendations.push('Execute src/scripts/setup-ai-tables.sql for AI functionality');
      }

      const missingCore = missingTables.filter(t => this.coreTablesList.includes(t));
      if (missingCore.length > 0) {
        recommendations.push('Run core system migrations from supabase/migrations/');
      }
    }

    if (schemaIssues.some(issue => issue.includes('permission'))) {
      recommendations.push('Review and update Row Level Security (RLS) policies');
      recommendations.push('Ensure user has appropriate role permissions');
    }

    if (schemaIssues.some(issue => issue.includes('Empty'))) {
      recommendations.push('Initialize empty tables with default data');
      recommendations.push('Check if departments table has initial departments');
    }

    // Role-specific recommendations
    recommendations.push('Verify role-based table access matches user permissions');
    recommendations.push('Test AI system functionality after schema fixes');
    recommendations.push('Run system health check after applying fixes');

    return recommendations;
  }

  /**
   * Get role-specific table requirements
   */
  public getRoleTableRequirements(role: string): string[] {
    const requirements: string[] = [...this.coreTablesList];

    switch (role) {
      case 'admin':
        requirements.push(...this.aiTablesList, ...this.roleSpecificTablesList);
        break;
      case 'manager':
        requirements.push(
          'projects', 'project_assignments', 'memos', 'memo_notifications',
          'leave_requests', 'leave_balances', 'telecom_sites', 'budgets'
        );
        break;
      case 'accountant':
        requirements.push('invoices', 'expenses', 'budgets');
        break;
      case 'staff-admin':
        requirements.push('expenses', 'battery_management', 'telecom_sites');
        break;
      case 'hr':
        requirements.push('leave_requests', 'leave_balances', 'email_notifications');
        break;
      case 'staff':
        requirements.push('leave_requests', 'email_notifications');
        break;
    }

    return [...new Set(requirements)]; // Remove duplicates
  }

  /**
   * Quick health check for specific role
   */
  public async checkRoleAccess(role: string): Promise<{
    accessible: string[];
    inaccessible: string[];
    issues: string[];
  }> {
    const requiredTables = this.getRoleTableRequirements(role);
    const accessible: string[] = [];
    const inaccessible: string[] = [];
    const issues: string[] = [];

    for (const table of requiredTables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('id')
          .limit(1);

        if (error) {
          inaccessible.push(table);
          issues.push(`${table}: ${error.message}`);
        } else {
          accessible.push(table);
        }
      } catch (err) {
        inaccessible.push(table);
        issues.push(`${table}: Query failed`);
      }
    }

    return { accessible, inaccessible, issues };
  }

  /**
   * Generate schema report
   */
  public generateReport(analysis: SchemaAnalysis): string {
    const report = `
# Database Schema Analysis Report

## Summary
- **Core Tables**: ${analysis.coreTablesStatus.filter(t => t.exists).length}/${analysis.coreTablesStatus.length} available
- **AI Tables**: ${analysis.aiTablesStatus.filter(t => t.exists).length}/${analysis.aiTablesStatus.length} available  
- **Role-Specific Tables**: ${analysis.roleSpecificTablesStatus.filter(t => t.exists).length}/${analysis.roleSpecificTablesStatus.length} available
- **Missing Tables**: ${analysis.missingTables.length}
- **Schema Issues**: ${analysis.schemaIssues.length}

## Core Tables Status
${analysis.coreTablesStatus.map(t => 
  `- ${t.exists ? '✅' : '❌'} **${t.name}**: ${t.exists ? `${t.rowCount} rows` : t.error}`
).join('\n')}

## AI Tables Status  
${analysis.aiTablesStatus.map(t => 
  `- ${t.exists ? '✅' : '❌'} **${t.name}**: ${t.exists ? `${t.rowCount} rows` : t.error}`
).join('\n')}

## Issues Identified
${analysis.schemaIssues.map(issue => `- ⚠️ ${issue}`).join('\n')}

## Recommendations
${analysis.recommendations.map(rec => `- 🔧 ${rec}`).join('\n')}

## Next Steps
1. Address missing tables by running appropriate migrations
2. Fix permission issues with RLS policies
3. Initialize empty tables with default data
4. Test role-based access after fixes
5. Run AI system setup if AI tables are missing

---
*Generated on ${new Date().toISOString()}*
`;

    return report;
  }
}

// Export singleton instance
export const dbSchemaAnalyzer = new DatabaseSchemaAnalyzer();

// Export convenience functions
export const analyzeSchema = () => dbSchemaAnalyzer.analyzeSchema();
export const checkRoleAccess = (role: string) => dbSchemaAnalyzer.checkRoleAccess(role);
export const generateSchemaReport = (analysis: SchemaAnalysis) => dbSchemaAnalyzer.generateReport(analysis);

// Table checks may fail if RLS is not configured for the current user.
