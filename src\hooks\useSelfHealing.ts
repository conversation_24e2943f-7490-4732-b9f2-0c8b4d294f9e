/**
 * Self-Healing System Hook
 * React hook to manage the LangChain self-healing system
 */

import { useEffect, useState, useCallback } from 'react';
import { selfHealingSystem, type SystemError } from '@/lib/langchain-self-healing';
import { useAuth } from '@/components/auth/AuthProvider';
import { useToast } from '@/hooks/use-toast';

export interface SelfHealingState {
  isActive: boolean;
  errors: SystemError[];
  systemHealth: 'healthy' | 'warning' | 'critical';
  autoFixEnabled: boolean;
  totalErrors: number;
  resolvedErrors: number;
  criticalErrors: number;
}

export function useSelfHealing() {
  const [state, setState] = useState<SelfHealingState>({
    isActive: false,
    errors: [],
    systemHealth: 'healthy',
    autoFixEnabled: true,
    totalErrors: 0,
    resolvedErrors: 0,
    criticalErrors: 0,
  });

  const { userProfile } = useAuth();
  const { toast } = useToast();

  // Update state periodically
  useEffect(() => {
    const updateState = () => {
      const activeErrors = selfHealingSystem.getActiveErrors();
      const criticalErrors = activeErrors.filter(e => e.severity === 'critical').length;
      const highErrors = activeErrors.filter(e => e.severity === 'high').length;
      
      let systemHealth: 'healthy' | 'warning' | 'critical' = 'healthy';
      if (criticalErrors > 0) {
        systemHealth = 'critical';
      } else if (highErrors > 0 || activeErrors.length > 3) {
        systemHealth = 'warning';
      }

      setState(prev => ({
        ...prev,
        errors: activeErrors,
        systemHealth,
        totalErrors: activeErrors.length,
        criticalErrors,
      }));
    };

    // Initial update
    updateState();

    // Set up periodic updates
    const interval = setInterval(updateState, 2000);
    return () => clearInterval(interval);
  }, []);

  // Auto-start monitoring for admins and managers
  useEffect(() => {
    if (userProfile?.role && ['admin', 'manager'].includes(userProfile.role)) {
      if (!state.isActive) {
        startMonitoring();
      }
    }
  }, [userProfile?.role]);

  const startMonitoring = useCallback(() => {
    try {
      selfHealingSystem.startMonitoring();
      setState(prev => ({ ...prev, isActive: true }));
      
      toast({
        title: "🛡️ Self-Healing System Activated",
        description: "System monitoring and auto-repair is now active",
      });
    } catch (error) {
      console.error('Failed to start self-healing system:', error);
      toast({
        title: "Failed to Start Self-Healing",
        description: "Unable to activate system monitoring",
        variant: "destructive",
      });
    }
  }, [toast]);

  const stopMonitoring = useCallback(() => {
    try {
      selfHealingSystem.stopMonitoring();
      setState(prev => ({ ...prev, isActive: false }));
      
      toast({
        title: "Self-Healing System Stopped",
        description: "System monitoring has been disabled",
        variant: "destructive",
      });
    } catch (error) {
      console.error('Failed to stop self-healing system:', error);
    }
  }, [toast]);

  const toggleAutoFix = useCallback(() => {
    setState(prev => ({ ...prev, autoFixEnabled: !prev.autoFixEnabled }));
    
    toast({
      title: `Auto-Fix ${!state.autoFixEnabled ? 'Enabled' : 'Disabled'}`,
      description: `Automatic error fixing is now ${!state.autoFixEnabled ? 'ON' : 'OFF'}`,
    });
  }, [state.autoFixEnabled, toast]);

  const manualFix = useCallback(async (errorId: string) => {
    try {
      const success = await selfHealingSystem.manualFix(errorId);
      
      toast({
        title: success ? "🔧 Fix Attempted" : "❌ Fix Failed",
        description: success 
          ? "Manual fix has been attempted for this error"
          : "Unable to automatically fix this error",
        variant: success ? "default" : "destructive",
      });

      return success;
    } catch (error) {
      console.error('Manual fix failed:', error);
      toast({
        title: "Fix Error",
        description: "An error occurred while attempting the fix",
        variant: "destructive",
      });
      return false;
    }
  }, [toast]);

  const markResolved = useCallback((errorId: string) => {
    try {
      selfHealingSystem.markResolved(errorId);
      
      toast({
        title: "✅ Error Resolved",
        description: "Error has been marked as resolved",
      });
    } catch (error) {
      console.error('Failed to mark error as resolved:', error);
    }
  }, [toast]);

  const getErrorsByType = useCallback(() => {
    const errorsByType = state.errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return errorsByType;
  }, [state.errors]);

  const getErrorsBySeverity = useCallback(() => {
    const errorsBySeverity = state.errors.reduce((acc, error) => {
      acc[error.severity] = (acc[error.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return errorsBySeverity;
  }, [state.errors]);

  const getRecentErrors = useCallback((limit: number = 5) => {
    return state.errors
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }, [state.errors]);

  const getCriticalErrors = useCallback(() => {
    return state.errors.filter(error => error.severity === 'critical');
  }, [state.errors]);

  const hasPermission = useCallback(() => {
    return userProfile?.role && ['admin', 'manager'].includes(userProfile.role);
  }, [userProfile?.role]);

  return {
    // State
    ...state,
    
    // Actions
    startMonitoring,
    stopMonitoring,
    toggleAutoFix,
    manualFix,
    markResolved,
    
    // Utilities
    getErrorsByType,
    getErrorsBySeverity,
    getRecentErrors,
    getCriticalErrors,
    hasPermission,
    
    // Computed values
    hasErrors: state.errors.length > 0,
    hasCriticalErrors: state.criticalErrors > 0,
    isHealthy: state.systemHealth === 'healthy',
    needsAttention: state.systemHealth !== 'healthy',
  };
}
