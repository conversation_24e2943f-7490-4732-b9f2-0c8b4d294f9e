import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { Bo<PERSON>, Send, User, Loader, Paperclip, Mic, Image, FileText, Zap, Brain, MessageSquare } from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'text' | 'file' | 'image';
}

export const EnhancedAIChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();
  
  // Enhanced quick action tags for CTNL AI Work-Board
  const quickActions = [
    { label: "Time Logs", icon: "⏰", query: "Show me my time logs for today" },
    { label: "Projects", icon: "📊", query: "What projects am I assigned to?" },
    { label: "Reports", icon: "📋", query: "Help me create a report" },
    { label: "Analytics", icon: "📈", query: "Show me analytics dashboard" },
    { label: "Tasks", icon: "✅", query: "What are my pending tasks?" },
    { label: "Help", icon: "❓", query: "How can you help me?" }
  ];

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
      type: 'text'
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: input.trim(),
          userId: userProfile?.id,
          context: {
            role: userProfile?.role,
            department: userProfile?.department,
            timestamp: new Date().toISOString()
          }
        }
      });

      if (error) throw error;

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response || "I'm here to help! Ask me about your work, projects, or any CTNL AI Work-Board features.",
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error: any) {
      toast({
        title: "AI Assistant Error",
        description: error.message || "Failed to get response from AI assistant",
        variant: "destructive",
      });

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: "I'm experiencing some technical difficulties. Please try again in a moment. In the meantime, you can use the quick actions below or explore the dashboard features.",
        timestamp: new Date(),
        type: 'text'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleQuickAction = (action: typeof quickActions[0]) => {
    setInput(action.query);
  };

  return (
    <div className="container_chat_bot">
      {/* Enhanced Chat Container with CTNL AI Colors */}
      <div className="container-chat-options">
        <div className="chat">
          {/* Enhanced Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700/50 bg-gradient-to-r from-gray-900/50 to-black/50">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="h-10 w-10 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-lg">
                  <Brain className="h-5 w-5 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 h-4 w-4 bg-green-400 rounded-full border-2 border-gray-900 animate-pulse"></div>
              </div>
              <div>
                <span className="text-white font-semibold text-sm">CTNL AI Assistant</span>
                <p className="text-gray-400 text-xs">Powered by Advanced AI</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-400 animate-pulse" />
              <span className="text-xs text-green-400 font-medium">Online</span>
            </div>
          </div>

          {/* Messages Area with Enhanced Styling */}
          <div className="flex-1 p-4 h-80 overflow-y-auto custom-scrollbar">
            {messages.length === 0 ? (
              <div className="text-center text-gray-300 py-8">
                <div className="relative mx-auto mb-6">
                  <div className="h-20 w-20 mx-auto rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-2xl">
                    <MessageSquare className="h-10 w-10 text-white" />
                  </div>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-red-500 to-red-600 animate-ping opacity-20"></div>
                </div>
                <h3 className="text-lg font-semibold text-white mb-2">Welcome to CTNL AI Assistant</h3>
                <p className="text-sm text-gray-400 mb-4">I'm here to help you with work tasks, projects, and company processes.</p>
                <div className="flex justify-center gap-2 text-xs">
                  <span className="px-2 py-1 bg-red-500/20 text-red-300 rounded">Smart</span>
                  <span className="px-2 py-1 bg-green-500/20 text-green-300 rounded">Fast</span>
                  <span className="px-2 py-1 bg-blue-500/20 text-blue-300 rounded">Helpful</span>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.role === 'assistant' && (
                      <div className="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                    )}
                    <div
                      className={`max-w-[85%] p-3 rounded-2xl shadow-lg ${
                        message.role === 'user'
                          ? 'bg-gradient-to-r from-green-600 to-green-700 text-white border border-green-500/30'
                          : 'bg-gradient-to-r from-gray-800 to-gray-700 text-gray-100 border border-gray-600/50'
                      }`}
                    >
                      <p className="text-sm leading-relaxed">{message.content}</p>
                      <p className="text-xs opacity-70 mt-2 flex items-center gap-1">
                        <span>{message.timestamp.toLocaleTimeString()}</span>
                        {message.role === 'assistant' && (
                          <span className="text-red-300">• AI</span>
                        )}
                      </p>
                    </div>
                    {message.role === 'user' && (
                      <div className="h-8 w-8 rounded-full bg-gradient-to-r from-green-600 to-green-700 flex items-center justify-center flex-shrink-0 shadow-lg">
                        <User className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>
                ))}
                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center shadow-lg">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="bg-gradient-to-r from-gray-800 to-gray-700 p-3 rounded-2xl border border-gray-600/50 shadow-lg">
                      <div className="flex items-center gap-2">
                        <Loader className="h-4 w-4 animate-spin text-red-400" />
                        <span className="text-sm text-gray-300">AI is thinking...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Enhanced Input Area */}
          <div className="chat-bot border-t border-gray-700/50">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about CTNL AI Work-Board..."
              disabled={isLoading}
              className="w-full h-14 bg-transparent border-none text-white text-sm resize-none outline-none p-4 placeholder:text-gray-400"
              rows={1}
            />
          </div>

          {/* Enhanced Options and Controls */}
          <div className="options bg-gradient-to-r from-gray-900/50 to-black/50">
            <div className="btns-add">
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1 hover:bg-red-500/20 rounded-lg group">
                <Paperclip className="h-4 w-4 group-hover:rotate-12 transition-transform" />
              </button>
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1 hover:bg-blue-500/20 rounded-lg group">
                <Image className="h-4 w-4 group-hover:scale-110 transition-transform" />
              </button>
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1 hover:bg-green-500/20 rounded-lg group">
                <FileText className="h-4 w-4 group-hover:rotate-6 transition-transform" />
              </button>
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1 hover:bg-purple-500/20 rounded-lg group">
                <Mic className="h-4 w-4 group-hover:scale-110 transition-transform" />
              </button>
            </div>
            
            <button 
              onClick={sendMessage} 
              disabled={!input.trim() || isLoading}
              className="btn-submit disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Quick Action Tags */}
      <div className="tags">
        {quickActions.map((action, index) => (
          <span 
            key={index}
            onClick={() => handleQuickAction(action)}
            className="hover:bg-red-600/30 hover:border-red-400 transition-all duration-200 cursor-pointer group flex items-center gap-1"
          >
            <span className="group-hover:scale-110 transition-transform">{action.icon}</span>
            <span>{action.label}</span>
          </span>
        ))}
      </div>
    </div>
  );
};
