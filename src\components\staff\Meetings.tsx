
import { useAuth } from "@/components/auth/AuthProvider";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Calendar, Clock, Eye, MapPin, Users, Video } from "lucide-react";
import { useState } from "react";

export const Meetings = () => {
  const { userProfile } = useAuth();
  const [selectedMeeting, setSelectedMeeting] = useState<any>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const { data: meetings = [], isLoading, error } = useQuery({
    queryKey: ['staff-meetings', userProfile?.id],
    queryFn: async () => {
      if (!userProfile?.id) return [];

      try {
        // First try zoom_meetings table
        const { data: zoomData, error: zoomError } = await supabase
          .from('zoom_meetings')
          .select(`
            id,
            topic as title,
            agenda as description,
            start_time,
            duration,
            join_url as meeting_url,
            status,
            host_id as organizer_id,
            host:profiles!host_id(id, full_name, email)
          `)
          .eq('host_id', userProfile.id)
          .order('start_time', { ascending: true });

        if (!zoomError && zoomData) {
          // Transform zoom meetings to match expected format
          const transformedMeetings = zoomData.map(meeting => ({
            ...meeting,
            end_time: new Date(new Date(meeting.start_time).getTime() + (meeting.duration * 60000)).toISOString(),
            meeting_type: 'zoom',
            location: 'Online'
          }));
          return transformedMeetings;
        }

        // Fallback to regular meetings table
        const { data: meetingsData, error: meetingsError } = await supabase
          .from('meetings')
          .select(`
            id,
            title,
            description,
            start_time,
            end_time,
            meeting_url,
            status,
            organizer_id,
            meeting_type,
            location,
            organizer:profiles!organizer_id(id, full_name, email)
          `)
          .eq('organizer_id', userProfile.id)
          .order('start_time', { ascending: true });

        if (meetingsError) {
          console.error('Meeting fetch error:', meetingsError);
          return [];
        }

        return meetingsData || [];
      } catch (err) {
        console.error('Meeting query failed:', err);
        return [];
      }
    },
    enabled: !!userProfile?.id,
    retry: 1,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'default';
      case 'in_progress': return 'secondary';
      case 'completed': return 'outline';
      case 'cancelled': return 'destructive';
      default: return 'outline';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading meetings...</div>
        </CardContent>
      </Card>
    );
  }

  const upcomingMeetings = meetings?.filter(meeting => 
    new Date(meeting.start_time) > new Date()
  ) || [];
  
  const pastMeetings = meetings?.filter(meeting => 
    new Date(meeting.start_time) <= new Date()
  ) || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">My Meetings</h2>
          <p className="text-muted-foreground">View your scheduled meetings and join sessions</p>
        </div>
      </div>

      {/* Upcoming Meetings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Upcoming Meetings ({upcomingMeetings.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {upcomingMeetings.length > 0 ? (
            <div className="space-y-4">
              {upcomingMeetings.map((meeting) => (
                <div key={meeting.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium">{meeting.title}</h4>
                      <p className="text-sm text-muted-foreground">{meeting.description}</p>
                    </div>
                    <Badge variant={getStatusColor(meeting.status)}>
                      {meeting.status}
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      {format(new Date(meeting.start_time), 'MMM dd, yyyy HH:mm')} - 
                      {format(new Date(meeting.end_time), 'HH:mm')}
                    </div>
                    
                    {meeting.location && (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        {meeting.location}
                      </div>
                    )}
                    
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      {meeting.meeting_type}
                    </div>
                    
                    {meeting.meeting_url && (
                      <div className="flex items-center gap-2">
                        <Video className="h-4 w-4" />
                        Online Meeting
                      </div>
                    )}
                  </div>
                  
                  <div className="flex gap-2 mt-4">
                    {meeting.meeting_url && (
                      <Button size="sm">
                        <Video className="h-4 w-4 mr-2" />
                        Join Meeting
                      </Button>
                    )}
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setSelectedMeeting(meeting);
                        setShowDetailsModal(true);
                      }}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No upcoming meetings</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Past Meetings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Recent Meetings ({pastMeetings.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {pastMeetings.length > 0 ? (
            <div className="space-y-4">
              {pastMeetings.slice(0, 5).map((meeting) => (
                <div key={meeting.id} className="border rounded-lg p-4 opacity-75">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium">{meeting.title}</h4>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {format(new Date(meeting.start_time), 'MMM dd, yyyy HH:mm')}
                      </div>
                    </div>
                    <Badge variant="outline">
                      {meeting.status}
                    </Badge>
                  </div>
                  
                  {meeting.notes && (
                    <p className="text-sm text-muted-foreground mt-2">
                      {meeting.notes.substring(0, 100)}...
                    </p>
                  )}
                  
                  <Button
                    size="sm"
                    variant="ghost"
                    className="mt-2"
                    onClick={() => {
                      setSelectedMeeting(meeting);
                      setShowDetailsModal(true);
                    }}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Summary
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No recent meetings</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Meeting Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Meeting Details</DialogTitle>
          </DialogHeader>
          {selectedMeeting && (
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{selectedMeeting.title}</h3>
                <p className="text-muted-foreground">{selectedMeeting.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm font-medium">Date & Time</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {format(new Date(selectedMeeting.start_time), 'PPP p')}
                  </p>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm font-medium">Duration</span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {selectedMeeting.duration || 'Not specified'}
                  </p>
                </div>

                {selectedMeeting.location && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span className="text-sm font-medium">Location</span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {selectedMeeting.location}
                    </p>
                  </div>
                )}

                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    <span className="text-sm font-medium">Status</span>
                  </div>
                  <Badge variant={selectedMeeting.status === 'scheduled' ? 'default' : 'secondary'}>
                    {selectedMeeting.status}
                  </Badge>
                </div>
              </div>

              {selectedMeeting.meeting_url && (
                <div className="pt-4 border-t">
                  <Button
                    className="w-full"
                    onClick={() => window.open(selectedMeeting.meeting_url, '_blank')}
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Join Meeting
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};
