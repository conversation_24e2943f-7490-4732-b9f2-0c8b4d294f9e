import { useState } from "react";
import { useAIOperations } from "@/hooks/useAIOperations";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Brain, Loader, Plus, Trash2, AlertCircle } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";

export const AIResults = () => {
  const { useAIResults, useCreateAIResult, useDeleteAIResult } = useAIOperations();
  const { data: results, isLoading, error } = useAIResults();
  const createAIResult = useCreateAIResult();
  const deleteAIResult = useDeleteAIResult();
  const { toast } = useToast();

  const [isAdding, setIsAdding] = useState(false);
  const [newQuery, setNewQuery] = useState("");
  const [newResult, setNewResult] = useState("");

  const handleAddResult = async () => {
    if (!newQuery.trim() || !newResult.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both query and result fields",
        variant: "destructive",
      });
      return;
    }

    try {
      let resultData;
      try {
        resultData = JSON.parse(newResult);
      } catch {
        resultData = { content: newResult };
      }

      await createAIResult.mutateAsync({
        query_text: newQuery.trim(),
        result_data: resultData,
        model_used: "manual-entry",
        created_by: null,
      });

      setNewQuery("");
      setNewResult("");
      setIsAdding(false);

      toast({
        title: "Success",
        description: "AI result added successfully",
      });
    } catch (error) {
      console.error("Error adding AI result:", error);
      toast({
        title: "Error",
        description: "Failed to add AI result. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteResult = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this AI result? This action cannot be undone.")) {
      try {
        await deleteAIResult.mutateAsync(id);
      } catch (error) {
        console.error("Error deleting AI result:", error);
        toast({
          title: "Error",
          description: "Failed to delete AI result. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            AI Analysis Results
          </div>
          <Button
            onClick={() => setIsAdding(!isAdding)}
            size="sm"
            variant="outline"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Test Result
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Error loading AI results: {error.message}
            </AlertDescription>
          </Alert>
        )}

        {isAdding && (
          <Card className="mb-4 p-4 bg-muted/20">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Query Text</label>
                <Input
                  value={newQuery}
                  onChange={(e) => setNewQuery(e.target.value)}
                  placeholder="Enter the AI query..."
                />
              </div>
              <div>
                <label className="text-sm font-medium">Result Data (JSON or text)</label>
                <Textarea
                  value={newResult}
                  onChange={(e) => setNewResult(e.target.value)}
                  placeholder='{"summary": "Analysis result...", "confidence": 0.95}'
                  rows={4}
                />
              </div>
              <div className="flex gap-2">
                <Button onClick={handleAddResult} disabled={createAIResult.isPending}>
                  {createAIResult.isPending ? (
                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Plus className="h-4 w-4 mr-2" />
                  )}
                  Add Result
                </Button>
                <Button variant="outline" onClick={() => setIsAdding(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </Card>
        )}

        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {results && results.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No AI results found. Add a test result to get started.</p>
              </div>
            )}

            {results?.map((result) => (
              <Card key={result.id} className="p-4 bg-muted/50">
                <div className="flex justify-between items-start mb-2">
                  <p className="font-medium flex-1">{result.query_text}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-destructive hover:text-destructive"
                    onClick={() => handleDeleteResult(result.id)}
                    disabled={deleteAIResult.isPending}
                  >
                    {deleteAIResult.isPending ? (
                      <Loader className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <div className="text-sm text-muted-foreground">
                  <pre className="whitespace-pre-wrap">
                    {JSON.stringify(result.result_data, null, 2)}
                  </pre>
                </div>
                <div className="mt-2 text-xs text-muted-foreground flex justify-between">
                  <span>Model: {result.model_used}</span>
                  <span>{new Date(result.created_at).toLocaleString()}</span>
                </div>
              </Card>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};