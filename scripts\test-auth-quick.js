/**
 * 🧪 QUICK AUTHENTICATION SYSTEM TEST
 * This script quickly tests if the authentication system is working
 */

const { createClient } = require('@supabase/supabase-js');

// Local Supabase configuration
const supabaseUrl = 'http://127.0.0.1:54321';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.Qcv7XjPK3z8zM0JkLuQI09GJCpjc8qj4uMh7-E8E1XU';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testAuthSystem() {
  console.log('🧪 Testing Authentication System...\n');

  try {
    // Test 1: Check departments table
    console.log('1. 🏢 Testing departments table...');
    const { data: departments, error: deptError } = await supabase
      .from('departments')
      .select('id, name, description')
      .limit(3);

    if (deptError) {
      console.error('❌ Departments error:', deptError.message);
      return false;
    } else {
      console.log('✅ Departments working:', departments.length, 'found');
      console.log('   📋 Sample:', departments.map(d => d.name).join(', '));
    }

    // Test 2: Check profiles table structure
    console.log('\n2. 👤 Testing profiles table...');
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);

    if (profileError) {
      console.error('❌ Profiles error:', profileError.message);
      return false;
    } else {
      console.log('✅ Profiles table accessible');
    }

    // Test 3: Test sign-up flow (create test user)
    console.log('\n3. 🔐 Testing user creation...');
    const testEmail = 'test-' + Date.now() + '@example.com';
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: 'test123456',
      options: {
        data: {
          full_name: 'Test User',
          role: 'staff'
        }
      }
    });

    if (signUpError) {
      console.error('❌ Sign-up error:', signUpError.message);
      return false;
    } else {
      console.log('✅ User creation successful');
      console.log('   📧 Email:', signUpData.user?.email);
      console.log('   🆔 ID:', signUpData.user?.id);
    }

    // Test 4: Check if profile was created via trigger
    if (signUpData.user?.id) {
      console.log('\n4. 🔗 Testing profile trigger...');
      
      // Wait a moment for trigger to execute
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const { data: newProfile, error: profileCheckError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', signUpData.user.id)
        .single();

      if (profileCheckError) {
        console.error('❌ Profile trigger error:', profileCheckError.message);
        return false;
      } else {
        console.log('✅ Profile trigger working');
        console.log('   👤 Name:', newProfile.full_name);
        console.log('   🏢 Department:', newProfile.department_id);
        console.log('   🎭 Role:', newProfile.role);
      }
    }

    console.log('\n🎉 All tests passed! Authentication system is working correctly.');
    return true;

  } catch (error) {
    console.error('🚨 Test failed:', error.message);
    return false;
  }
}

// Run the test
testAuthSystem()
  .then(success => {
    if (success) {
      console.log('\n✅ AUTHENTICATION SYSTEM: READY FOR USE');
      process.exit(0);
    } else {
      console.log('\n❌ AUTHENTICATION SYSTEM: NEEDS ATTENTION');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n🚨 Test execution failed:', error);
    process.exit(1);
  });
