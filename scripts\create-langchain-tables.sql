-- LangChain Integration Database Tables
-- Run this script in your Supabase SQL editor to create the necessary tables

-- Conversation History Table
CREATE TABLE IF NOT EXISTS conversation_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('human', 'ai')),
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conversation Analytics Table
CREATE TABLE IF NOT EXISTS conversation_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    session_id TEXT NOT NULL,
    human_message_length INTEGER,
    ai_message_length INTEGER,
    processing_time INTEGER, -- in milliseconds
    model_used TEXT,
    interface_type TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents Table (simplified - for basic document storage)
CREATE TABLE IF NOT EXISTS documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- LangChain Operations Monitoring Table
CREATE TABLE IF NOT EXISTS langchain_operations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    operation_type TEXT NOT NULL CHECK (operation_type IN ('chat', 'rag', 'agent', 'document_processing', 'summarization')),
    duration INTEGER NOT NULL, -- in milliseconds
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    session_id TEXT,
    model TEXT,
    tokens_used INTEGER,
    input_length INTEGER,
    output_length INTEGER,
    interface_type TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_conversation_history_user_session ON conversation_history(user_id, session_id);
CREATE INDEX IF NOT EXISTS idx_conversation_history_created_at ON conversation_history(created_at);
CREATE INDEX IF NOT EXISTS idx_conversation_analytics_user_id ON conversation_analytics(user_id);
CREATE INDEX IF NOT EXISTS idx_conversation_analytics_created_at ON conversation_analytics(created_at);
CREATE INDEX IF NOT EXISTS idx_langchain_operations_type ON langchain_operations(operation_type);
CREATE INDEX IF NOT EXISTS idx_langchain_operations_user_id ON langchain_operations(user_id);
CREATE INDEX IF NOT EXISTS idx_langchain_operations_created_at ON langchain_operations(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_conversation_history_updated_at
    BEFORE UPDATE ON conversation_history
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at
    BEFORE UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies
ALTER TABLE conversation_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE langchain_operations ENABLE ROW LEVEL SECURITY;

-- Policies for conversation_history
CREATE POLICY "Users can view their own conversation history" ON conversation_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own conversation history" ON conversation_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policies for conversation_analytics
CREATE POLICY "Users can view their own analytics" ON conversation_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service role can insert analytics" ON conversation_analytics
    FOR INSERT WITH CHECK (true);

-- Admin policies for monitoring
CREATE POLICY "Admins can view all langchain operations" ON langchain_operations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

CREATE POLICY "Service role can insert operations" ON langchain_operations
    FOR INSERT WITH CHECK (true);

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Insert a test record to verify everything works
INSERT INTO conversation_history (user_id, session_id, type, content, metadata)
VALUES (
    auth.uid(),
    'test_session',
    'system',
    'LangChain integration tables created successfully!',
    jsonb_build_object('test', true, 'created_by', 'setup_script')
);

-- Success message
SELECT 'LangChain tables created successfully!' as status;
