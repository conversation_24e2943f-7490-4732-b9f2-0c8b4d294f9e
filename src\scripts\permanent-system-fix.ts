import { supabase } from '@/integrations/supabase/client';

/**
 * PERMANENT SYSTEM FIX
 * This script creates a robust, self-healing system that prevents fixes from disappearing
 * and ensures all core features work permanently.
 */

const logStep = (message: string) => {
  console.log(`🔧 ${message}`);
};

const logSuccess = (message: string) => {
  console.log(`✅ ${message}`);
};

const logError = (message: string) => {
  console.error(`❌ ${message}`);
};

// Step 1: Create comprehensive database schema with all required tables
const createComprehensiveSchema = async () => {
  logStep('Creating comprehensive database schema...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable necessary extensions
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
        CREATE EXTENSION IF NOT EXISTS "pgcrypto";
        
        -- Create comprehensive profiles table
        CREATE TABLE IF NOT EXISTS public.profiles (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
          full_name TEXT NOT NULL,
          email TEXT UNIQUE NOT NULL,
          role TEXT NOT NULL DEFAULT 'staff' CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin')),
          account_type TEXT DEFAULT 'staff' CHECK (account_type IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin')),
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'pending')),
          department_id UUID REFERENCES public.departments(id),
          avatar_url TEXT,
          phone TEXT,
          bio TEXT,
          location TEXT,
          skills TEXT[],
          preferences JSONB DEFAULT '{}',
          settings JSONB DEFAULT '{}',
          notification_preferences JSONB DEFAULT '{}',
          timezone TEXT DEFAULT 'UTC',
          last_login TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create departments table
        CREATE TABLE IF NOT EXISTS public.departments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL UNIQUE,
          description TEXT,
          manager_id UUID REFERENCES public.profiles(id),
          budget DECIMAL(15,2),
          location TEXT,
          status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create projects table
        CREATE TABLE IF NOT EXISTS public.projects (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name TEXT NOT NULL,
          description TEXT,
          client_name TEXT,
          budget DECIMAL(15,2),
          location TEXT,
          start_date DATE,
          end_date DATE,
          status TEXT DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
          manager_id UUID REFERENCES public.profiles(id),
          created_by UUID REFERENCES public.profiles(id),
          department_id UUID REFERENCES public.departments(id),
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create project_assignments table
        CREATE TABLE IF NOT EXISTS public.project_assignments (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
          project_name TEXT, -- Fallback for legacy compatibility
          assigned_to UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
          department_id UUID REFERENCES public.departments(id),
          role TEXT DEFAULT 'team_member',
          status TEXT DEFAULT 'assigned' CHECK (status IN ('assigned', 'active', 'completed', 'removed')),
          start_date DATE DEFAULT CURRENT_DATE,
          end_date DATE,
          hours_allocated INTEGER DEFAULT 40,
          hours_worked INTEGER DEFAULT 0,
          progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
          notes TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create memos table
        CREATE TABLE IF NOT EXISTS public.memos (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          memo_type TEXT DEFAULT 'general' CHECK (memo_type IN ('general', 'policy', 'announcement', 'urgent', 'meeting')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          visibility TEXT DEFAULT 'department' CHECK (visibility IN ('public', 'department', 'private', 'managers_only')),
          target_audience TEXT[],
          status TEXT DEFAULT 'published' CHECK (status IN ('draft', 'published', 'archived')),
          created_by UUID REFERENCES public.profiles(id),
          department_id UUID REFERENCES public.departments(id),
          effective_date DATE,
          expiry_date DATE,
          tags TEXT[],
          attachments JSONB DEFAULT '[]',
          read_by UUID[],
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create reports table
        CREATE TABLE IF NOT EXISTS public.reports (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          description TEXT,
          report_type TEXT DEFAULT 'general' CHECK (report_type IN ('general', 'financial', 'project', 'hr', 'technical', 'battery', 'telecom')),
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          status TEXT DEFAULT 'submitted' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected')),
          submitted_by UUID REFERENCES public.profiles(id),
          reviewed_by UUID REFERENCES public.profiles(id),
          department_id UUID REFERENCES public.departments(id),
          project_id UUID REFERENCES public.projects(id),
          due_date DATE,
          submitted_date DATE DEFAULT CURRENT_DATE,
          reviewed_date DATE,
          content JSONB DEFAULT '{}',
          attachments JSONB DEFAULT '[]',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create tasks table
        CREATE TABLE IF NOT EXISTS public.tasks (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          description TEXT,
          task_type TEXT DEFAULT 'general',
          priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
          assigned_to UUID REFERENCES public.profiles(id),
          created_by UUID REFERENCES public.profiles(id),
          project_id UUID REFERENCES public.projects(id),
          department_id UUID REFERENCES public.departments(id),
          due_date DATE,
          start_date DATE,
          completion_date DATE,
          estimated_hours INTEGER,
          actual_hours INTEGER,
          progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
          tags TEXT[],
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create system_activities table for comprehensive logging
        CREATE TABLE IF NOT EXISTS public.system_activities (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(id),
          action TEXT NOT NULL,
          description TEXT,
          entity_type TEXT,
          entity_id UUID,
          metadata JSONB DEFAULT '{}',
          severity TEXT DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
          category TEXT DEFAULT 'general',
          ip_address INET,
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create AI-related tables
        CREATE TABLE IF NOT EXISTS public.ai_interactions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID REFERENCES public.profiles(id),
          role TEXT DEFAULT 'user' CHECK (role IN ('user', 'assistant', 'system')),
          message TEXT NOT NULL,
          type TEXT DEFAULT 'chat',
          query TEXT,
          response TEXT,
          actions JSONB DEFAULT '[]',
          metadata JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE TABLE IF NOT EXISTS public.ai_documents (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          title TEXT NOT NULL,
          content TEXT NOT NULL,
          document_type TEXT,
          category TEXT DEFAULT 'general',
          tags TEXT[],
          analysis JSONB,
          embedding TEXT,
          indexed_at TIMESTAMP WITH TIME ZONE,
          index_version TEXT,
          created_by UUID REFERENCES public.profiles(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Create indexes for performance
        CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
        CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
        CREATE INDEX IF NOT EXISTS idx_profiles_department_id ON public.profiles(department_id);
        CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
        CREATE INDEX IF NOT EXISTS idx_projects_manager_id ON public.projects(manager_id);
        CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON public.project_assignments(project_id);
        CREATE INDEX IF NOT EXISTS idx_project_assignments_assigned_to ON public.project_assignments(assigned_to);
        CREATE INDEX IF NOT EXISTS idx_memos_created_by ON public.memos(created_by);
        CREATE INDEX IF NOT EXISTS idx_memos_department_id ON public.memos(department_id);
        CREATE INDEX IF NOT EXISTS idx_reports_submitted_by ON public.reports(submitted_by);
        CREATE INDEX IF NOT EXISTS idx_reports_project_id ON public.reports(project_id);
        CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to ON public.tasks(assigned_to);
        CREATE INDEX IF NOT EXISTS idx_tasks_project_id ON public.tasks(project_id);
        CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON public.system_activities(user_id);
        CREATE INDEX IF NOT EXISTS idx_system_activities_created_at ON public.system_activities(created_at);
        CREATE INDEX IF NOT EXISTS idx_ai_interactions_user_id ON public.ai_interactions(user_id);
        CREATE INDEX IF NOT EXISTS idx_ai_documents_category ON public.ai_documents(category);
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Comprehensive database schema created successfully');
    return true;
  } catch (error) {
    logError(`Failed to create schema: ${error.message}`);
    return false;
  }
};

// Step 2: Create comprehensive RLS policies
const createComprehensiveRLS = async () => {
  logStep('Creating comprehensive RLS policies...');
  
  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Enable RLS on all tables
        ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.departments ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.project_assignments ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.system_activities ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.ai_interactions ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.ai_documents ENABLE ROW LEVEL SECURITY;
        
        -- Drop all existing policies to start fresh
        DROP POLICY IF EXISTS "profiles_select_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_select_admin" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_select_manager" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_select_hr" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_select_staff_admin" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_insert_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_insert_admin" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_update_own" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_update_admin" ON public.profiles;
        DROP POLICY IF EXISTS "profiles_delete_admin" ON public.profiles;
        
        -- Profiles policies
        CREATE POLICY "profiles_select_own" ON public.profiles
          FOR SELECT USING (auth.uid() = user_id);
        
        CREATE POLICY "profiles_select_admin" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role IN ('admin', 'hr', 'staff-admin')
            )
          );
        
        CREATE POLICY "profiles_select_manager" ON public.profiles
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles p1
              WHERE p1.user_id = auth.uid() 
              AND p1.role = 'manager'
              AND (
                p1.department_id = public.profiles.department_id OR
                public.profiles.user_id = auth.uid()
              )
            )
          );
        
        CREATE POLICY "profiles_insert_own" ON public.profiles
          FOR INSERT WITH CHECK (auth.uid() = user_id);
        
        CREATE POLICY "profiles_insert_admin" ON public.profiles
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role = 'admin'
            )
          );
        
        CREATE POLICY "profiles_update_own" ON public.profiles
          FOR UPDATE USING (auth.uid() = user_id)
          WITH CHECK (auth.uid() = user_id);
        
        CREATE POLICY "profiles_update_admin" ON public.profiles
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role IN ('admin', 'hr')
            )
          );
        
        CREATE POLICY "profiles_delete_admin" ON public.profiles
          FOR DELETE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role = 'admin'
            )
          );
        
        -- Projects policies
        CREATE POLICY "projects_select_all" ON public.projects
          FOR SELECT USING (true);
        
        CREATE POLICY "projects_insert_authorized" ON public.projects
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
            )
          );
        
        CREATE POLICY "projects_update_authorized" ON public.projects
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND (
                role IN ('admin', 'manager') OR
                user_id = public.projects.manager_id OR
                user_id = public.projects.created_by
              )
            )
          );
        
        -- Project assignments policies
        CREATE POLICY "project_assignments_select_all" ON public.project_assignments
          FOR SELECT USING (true);
        
        CREATE POLICY "project_assignments_insert_authorized" ON public.project_assignments
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
            )
          );
        
        CREATE POLICY "project_assignments_update_authorized" ON public.project_assignments
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND (
                role IN ('admin', 'manager') OR
                user_id = public.project_assignments.assigned_to
              )
            )
          );
        
        -- Memos policies
        CREATE POLICY "memos_select_all" ON public.memos
          FOR SELECT USING (true);
        
        CREATE POLICY "memos_insert_authorized" ON public.memos
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid()
            )
          );
        
        CREATE POLICY "memos_update_own" ON public.memos
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND (
                role IN ('admin', 'manager') OR
                user_id = public.memos.created_by
              )
            )
          );
        
        -- Reports policies
        CREATE POLICY "reports_select_all" ON public.reports
          FOR SELECT USING (true);
        
        CREATE POLICY "reports_insert_authorized" ON public.reports
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid()
            )
          );
        
        CREATE POLICY "reports_update_authorized" ON public.reports
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND (
                role IN ('admin', 'manager') OR
                user_id = public.reports.submitted_by
              )
            )
          );
        
        -- Tasks policies
        CREATE POLICY "tasks_select_all" ON public.tasks
          FOR SELECT USING (true);
        
        CREATE POLICY "tasks_insert_authorized" ON public.tasks
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid()
            )
          );
        
        CREATE POLICY "tasks_update_authorized" ON public.tasks
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND (
                role IN ('admin', 'manager') OR
                user_id = public.tasks.assigned_to OR
                user_id = public.tasks.created_by
              )
            )
          );
        
        -- System activities policies
        CREATE POLICY "system_activities_select_admin" ON public.system_activities
          FOR SELECT USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
            )
          );
        
        CREATE POLICY "system_activities_insert_all" ON public.system_activities
          FOR INSERT WITH CHECK (true);
        
        -- AI interactions policies
        CREATE POLICY "ai_interactions_select_own" ON public.ai_interactions
          FOR SELECT USING (
            user_id = (SELECT id FROM public.profiles WHERE user_id = auth.uid())
          );
        
        CREATE POLICY "ai_interactions_insert_own" ON public.ai_interactions
          FOR INSERT WITH CHECK (
            user_id = (SELECT id FROM public.profiles WHERE user_id = auth.uid())
          );
        
        -- AI documents policies
        CREATE POLICY "ai_documents_select_all" ON public.ai_documents
          FOR SELECT USING (true);
        
        CREATE POLICY "ai_documents_insert_authorized" ON public.ai_documents
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid()
            )
          );
        
        -- Departments policies
        CREATE POLICY "departments_select_all" ON public.departments
          FOR SELECT USING (true);
        
        CREATE POLICY "departments_insert_admin" ON public.departments
          FOR INSERT WITH CHECK (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role = 'admin'
            )
          );
        
        CREATE POLICY "departments_update_admin" ON public.departments
          FOR UPDATE USING (
            EXISTS (
              SELECT 1 FROM public.profiles 
              WHERE user_id = auth.uid() AND role IN ('admin', 'manager')
            )
          );
        
        -- Grant permissions
        GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
        GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;
        GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Comprehensive RLS policies created successfully');
    return true;
  } catch (error) {
    logError(`Failed to create RLS policies: ${error.message}`);
    return false;
  }
};

// Step 3: Create comprehensive database functions
const createComprehensiveFunctions = async () => {
  logStep('Creating comprehensive database functions...');

  try {
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        -- Function to log system activities
        CREATE OR REPLACE FUNCTION log_system_activity(
          p_user_id UUID DEFAULT NULL,
          p_action VARCHAR(100) DEFAULT 'system_action',
          p_description TEXT DEFAULT 'System activity',
          p_entity_type VARCHAR(100) DEFAULT NULL,
          p_entity_id UUID DEFAULT NULL,
          p_metadata JSONB DEFAULT '{}',
          p_severity VARCHAR(20) DEFAULT 'info',
          p_category VARCHAR(50) DEFAULT 'general'
        )
        RETURNS UUID AS $$
        DECLARE
          activity_id UUID;
        BEGIN
          INSERT INTO system_activities (
            user_id,
            action,
            description,
            entity_type,
            entity_id,
            metadata,
            severity,
            category,
            created_at,
            updated_at
          ) VALUES (
            p_user_id,
            p_action,
            p_description,
            p_entity_type,
            p_entity_id,
            p_metadata,
            p_severity,
            p_category,
            NOW(),
            NOW()
          ) RETURNING id INTO activity_id;

          RETURN activity_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Function to get user profile by auth user ID
        CREATE OR REPLACE FUNCTION get_user_profile(auth_user_id UUID)
        RETURNS TABLE (
          id UUID,
          user_id UUID,
          full_name TEXT,
          email TEXT,
          role TEXT,
          account_type TEXT,
          status TEXT,
          department_id UUID,
          avatar_url TEXT,
          phone TEXT,
          bio TEXT,
          location TEXT,
          skills TEXT[],
          preferences JSONB,
          settings JSONB,
          notification_preferences JSONB,
          timezone TEXT,
          last_login TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE,
          updated_at TIMESTAMP WITH TIME ZONE
        ) AS $$
        BEGIN
          RETURN QUERY
          SELECT p.* FROM public.profiles p WHERE p.user_id = auth_user_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Function to create project with proper validation
        CREATE OR REPLACE FUNCTION create_project(
          p_name TEXT,
          p_description TEXT DEFAULT NULL,
          p_client_name TEXT DEFAULT NULL,
          p_budget DECIMAL(15,2) DEFAULT NULL,
          p_location TEXT DEFAULT NULL,
          p_start_date DATE DEFAULT NULL,
          p_end_date DATE DEFAULT NULL,
          p_status TEXT DEFAULT 'planning',
          p_priority TEXT DEFAULT 'medium',
          p_manager_id UUID DEFAULT NULL,
          p_department_id UUID DEFAULT NULL,
          p_created_by UUID DEFAULT NULL
        )
        RETURNS UUID AS $$
        DECLARE
          project_id UUID;
          current_user_id UUID;
        BEGIN
          -- Get current user profile ID
          SELECT id INTO current_user_id FROM public.profiles WHERE user_id = auth.uid();

          -- Use current user as created_by if not specified
          IF p_created_by IS NULL THEN
            p_created_by := current_user_id;
          END IF;

          -- Use current user as manager if not specified and user is manager/admin
          IF p_manager_id IS NULL THEN
            SELECT id INTO p_manager_id FROM public.profiles
            WHERE user_id = auth.uid() AND role IN ('admin', 'manager');
          END IF;

          INSERT INTO public.projects (
            name, description, client_name, budget, location,
            start_date, end_date, status, priority,
            manager_id, created_by, department_id,
            created_at, updated_at
          ) VALUES (
            p_name, p_description, p_client_name, p_budget, p_location,
            p_start_date, p_end_date, p_status, p_priority,
            p_manager_id, p_created_by, p_department_id,
            NOW(), NOW()
          ) RETURNING id INTO project_id;

          -- Log activity
          PERFORM log_system_activity(
            current_user_id,
            'project_created',
            'Created project: ' || p_name,
            'project',
            project_id,
            jsonb_build_object('project_name', p_name, 'status', p_status)
          );

          RETURN project_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Function to assign member to project
        CREATE OR REPLACE FUNCTION assign_project_member(
          p_project_id UUID,
          p_assigned_to UUID,
          p_role TEXT DEFAULT 'team_member',
          p_hours_allocated INTEGER DEFAULT 40
        )
        RETURNS UUID AS $$
        DECLARE
          assignment_id UUID;
          current_user_id UUID;
          project_name TEXT;
          member_name TEXT;
        BEGIN
          -- Get current user profile ID
          SELECT id INTO current_user_id FROM public.profiles WHERE user_id = auth.uid();

          -- Get project and member names for logging
          SELECT name INTO project_name FROM public.projects WHERE id = p_project_id;
          SELECT full_name INTO member_name FROM public.profiles WHERE id = p_assigned_to;

          INSERT INTO public.project_assignments (
            project_id, assigned_to, role, status,
            hours_allocated, hours_worked, progress_percentage,
            start_date, created_at, updated_at
          ) VALUES (
            p_project_id, p_assigned_to, p_role, 'assigned',
            p_hours_allocated, 0, 0,
            CURRENT_DATE, NOW(), NOW()
          ) RETURNING id INTO assignment_id;

          -- Log activity
          PERFORM log_system_activity(
            current_user_id,
            'member_assigned',
            'Assigned ' || member_name || ' to project: ' || project_name,
            'project_assignment',
            assignment_id,
            jsonb_build_object('project_id', p_project_id, 'assigned_to', p_assigned_to, 'role', p_role)
          );

          RETURN assignment_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Function to submit memo
        CREATE OR REPLACE FUNCTION submit_memo(
          p_title TEXT,
          p_content TEXT,
          p_memo_type TEXT DEFAULT 'general',
          p_priority TEXT DEFAULT 'medium',
          p_visibility TEXT DEFAULT 'department',
          p_target_audience TEXT[] DEFAULT NULL,
          p_department_id UUID DEFAULT NULL,
          p_effective_date DATE DEFAULT NULL,
          p_expiry_date DATE DEFAULT NULL,
          p_tags TEXT[] DEFAULT NULL
        )
        RETURNS UUID AS $$
        DECLARE
          memo_id UUID;
          current_user_id UUID;
        BEGIN
          -- Get current user profile ID
          SELECT id INTO current_user_id FROM public.profiles WHERE user_id = auth.uid();

          -- Use user's department if not specified
          IF p_department_id IS NULL THEN
            SELECT department_id INTO p_department_id FROM public.profiles WHERE id = current_user_id;
          END IF;

          INSERT INTO public.memos (
            title, content, memo_type, priority, visibility,
            target_audience, status, created_by, department_id,
            effective_date, expiry_date, tags,
            created_at, updated_at
          ) VALUES (
            p_title, p_content, p_memo_type, p_priority, p_visibility,
            p_target_audience, 'published', current_user_id, p_department_id,
            p_effective_date, p_expiry_date, p_tags,
            NOW(), NOW()
          ) RETURNING id INTO memo_id;

          -- Log activity
          PERFORM log_system_activity(
            current_user_id,
            'memo_submitted',
            'Submitted memo: ' || p_title,
            'memo',
            memo_id,
            jsonb_build_object('memo_type', p_memo_type, 'priority', p_priority)
          );

          RETURN memo_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Function to submit report
        CREATE OR REPLACE FUNCTION submit_report(
          p_title TEXT,
          p_description TEXT DEFAULT NULL,
          p_report_type TEXT DEFAULT 'general',
          p_priority TEXT DEFAULT 'medium',
          p_department_id UUID DEFAULT NULL,
          p_project_id UUID DEFAULT NULL,
          p_due_date DATE DEFAULT NULL,
          p_content JSONB DEFAULT '{}',
          p_attachments JSONB DEFAULT '[]'
        )
        RETURNS UUID AS $$
        DECLARE
          report_id UUID;
          current_user_id UUID;
        BEGIN
          -- Get current user profile ID
          SELECT id INTO current_user_id FROM public.profiles WHERE user_id = auth.uid();

          -- Use user's department if not specified
          IF p_department_id IS NULL THEN
            SELECT department_id INTO p_department_id FROM public.profiles WHERE id = current_user_id;
          END IF;

          INSERT INTO public.reports (
            title, description, report_type, priority, status,
            submitted_by, department_id, project_id, due_date,
            submitted_date, content, attachments,
            metadata, created_at, updated_at
          ) VALUES (
            p_title, p_description, p_report_type, p_priority, 'submitted',
            current_user_id, p_department_id, p_project_id, p_due_date,
            CURRENT_DATE, p_content, p_attachments,
            jsonb_build_object('submission_timestamp', NOW(), 'user_role', (SELECT role FROM public.profiles WHERE id = current_user_id)),
            NOW(), NOW()
          ) RETURNING id INTO report_id;

          -- Log activity
          PERFORM log_system_activity(
            current_user_id,
            'report_submitted',
            'Submitted report: ' || p_title,
            'report',
            report_id,
            jsonb_build_object('report_type', p_report_type, 'priority', p_priority)
          );

          RETURN report_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Grant execute permissions
        GRANT EXECUTE ON FUNCTION log_system_activity TO authenticated;
        GRANT EXECUTE ON FUNCTION get_user_profile TO authenticated;
        GRANT EXECUTE ON FUNCTION create_project TO authenticated;
        GRANT EXECUTE ON FUNCTION assign_project_member TO authenticated;
        GRANT EXECUTE ON FUNCTION submit_memo TO authenticated;
        GRANT EXECUTE ON FUNCTION submit_report TO authenticated;
      `
    });

    if (error) {
      throw error;
    }

    logSuccess('Comprehensive database functions created successfully');
    return true;
  } catch (error) {
    logError(`Failed to create database functions: ${error.message}`);
    return false;
  }
};

// Main function to run all fixes
export const runPermanentSystemFix = async () => {
  logStep('Starting permanent system fix...');

  try {
    // Step 1: Create comprehensive schema
    if (!(await createComprehensiveSchema())) {
      throw new Error('Failed to create comprehensive schema');
    }

    // Step 2: Create comprehensive RLS policies
    if (!(await createComprehensiveRLS())) {
      throw new Error('Failed to create comprehensive RLS policies');
    }

    // Step 3: Create comprehensive database functions
    if (!(await createComprehensiveFunctions())) {
      throw new Error('Failed to create comprehensive database functions');
    }

    logSuccess('🎉 Permanent system fix completed successfully!');

    // Store success in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('permanent_system_fix_completed', new Date().toISOString());
    }

    return true;

  } catch (error) {
    logError(`Permanent system fix failed: ${error.message}`);
    return false;
  }
};

// Auto-run if this file is executed directly
if (typeof window !== 'undefined') {
  // Check if fix has been run recently
  const lastRun = localStorage.getItem('permanent_system_fix_completed');
  const shouldRun = !lastRun || (new Date().getTime() - new Date(lastRun).getTime()) > 24 * 60 * 60 * 1000;
  
  if (shouldRun) {
    runPermanentSystemFix();
  }
}
