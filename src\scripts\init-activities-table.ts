import { supabase } from '@/integrations/supabase/client'

/**
 * Initialize the system_activities table and related functions
 * This script ensures the activities system is properly set up
 */

export const initializeActivitiesTable = async () => {
  try {
    console.log('🔧 Initializing activities table...')

    // Check if the table exists
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'system_activities')

    if (tablesError) {
      console.error('Error checking for activities table:', tablesError)
      return false
    }

    if (!tables || tables.length === 0) {
      console.log('📋 Creating system_activities table...')

      // Create the table using SQL
      const { error: createError } = await supabase.rpc('exec_sql', {
        sql: `
          -- Create system_activities table
          CREATE TABLE IF NOT EXISTS system_activities (
              id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
              user_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
              activity_type VARCHAR(100) NOT NULL,
              description TEXT NOT NULL,
              metadata JSONB DEFAULT '{}',
              ip_address INET,
              user_agent TEXT,
              session_id VARCHAR(255),
              severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical', 'success')),
              category VARCHAR(50) DEFAULT 'general' CHECK (category IN ('general', 'auth', 'project', 'user', 'system', 'database', 'api', 'security')),
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON system_activities(user_id);
          CREATE INDEX IF NOT EXISTS idx_system_activities_activity_type ON system_activities(activity_type);
          CREATE INDEX IF NOT EXISTS idx_system_activities_created_at ON system_activities(created_at DESC);
          CREATE INDEX IF NOT EXISTS idx_system_activities_severity ON system_activities(severity);
          CREATE INDEX IF NOT EXISTS idx_system_activities_category ON system_activities(category);

          -- Enable RLS
          ALTER TABLE system_activities ENABLE ROW LEVEL SECURITY;
        `
      })

      if (createError) {
        console.error('Error creating activities table:', createError)
        return false
      }

      console.log('✅ Activities table created successfully')
    } else {
      console.log('✅ Activities table already exists')
    }

    // Create RLS policies
    console.log('🔒 Setting up RLS policies...')

    const { error: policyError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Drop existing policies if they exist
        DROP POLICY IF EXISTS "Admin can view all activities" ON system_activities;
        DROP POLICY IF EXISTS "Users can view own activities" ON system_activities;
        DROP POLICY IF EXISTS "Authenticated users can insert activities" ON system_activities;

        -- Create new policies
        CREATE POLICY "Admin can view all activities" ON system_activities
            FOR SELECT USING (
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE profiles.id = auth.uid() 
                    AND profiles.role = 'admin'
                )
            );

        CREATE POLICY "Users can view own activities" ON system_activities
            FOR SELECT USING (user_id = auth.uid());

        CREATE POLICY "Authenticated users can insert activities" ON system_activities
            FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);
      `
    })

    if (policyError) {
      console.warn('Warning: Could not create RLS policies:', policyError)
    } else {
      console.log('✅ RLS policies created successfully')
    }

    // Create helper functions
    console.log('⚙️ Creating helper functions...')

    const { error: functionError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Function to log activities
        CREATE OR REPLACE FUNCTION log_system_activity(
            p_user_id UUID DEFAULT NULL,
            p_activity_type VARCHAR(100) DEFAULT 'system_action',
            p_description TEXT DEFAULT 'System activity',
            p_metadata JSONB DEFAULT '{}',
            p_severity VARCHAR(20) DEFAULT 'info',
            p_category VARCHAR(50) DEFAULT 'general'
        )
        RETURNS UUID AS $$
        DECLARE
            activity_id UUID;
        BEGIN
            INSERT INTO system_activities (
                user_id,
                activity_type,
                description,
                metadata,
                severity,
                category
            ) VALUES (
                p_user_id,
                p_activity_type,
                p_description,
                p_metadata,
                p_severity,
                p_category
            ) RETURNING id INTO activity_id;
            
            RETURN activity_id;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;

        -- Grant execute permission
        GRANT EXECUTE ON FUNCTION log_system_activity TO authenticated;
      `
    })

    if (functionError) {
      console.warn('Warning: Could not create helper functions:', functionError)
    } else {
      console.log('✅ Helper functions created successfully')
    }

    // Insert initial activities
    console.log('📝 Inserting initial activities...')

    const { error: insertError } = await supabase
      .from('system_activities')
      .insert([
        {
          activity_type: 'system_initialization',
          description: 'Activities system initialized successfully',
          metadata: {
            version: '1.0.0',
            timestamp: new Date().toISOString()
          },
          severity: 'success',
          category: 'system'
        },
        {
          activity_type: 'table_creation',
          description: 'System activities table created and configured',
          metadata: {
            table: 'system_activities',
            features: ['RLS', 'indexes', 'functions']
          },
          severity: 'info',
          category: 'database'
        }
      ])

    if (insertError) {
      console.warn('Warning: Could not insert initial activities:', insertError)
    } else {
      console.log('✅ Initial activities inserted successfully')
    }

    console.log('🎉 Activities system initialization completed successfully!')
    return true
  } catch (error) {
    console.error('❌ Failed to initialize activities system:', error)
    return false
  }
}

// Function to test the activities system
export const testActivitiesSystem = async () => {
  try {
    console.log('🧪 Testing activities system...')

    // Test inserting an activity
    const { data: testActivity, error: insertError } = await supabase
      .from('system_activities')
      .insert({
        activity_type: 'system_test',
        description: 'Testing activities system functionality',
        metadata: { test: true, timestamp: new Date().toISOString() },
        severity: 'info',
        category: 'system'
      })
      .select()
      .single()

    if (insertError) {
      console.error('❌ Failed to insert test activity:', insertError)
      return false
    }

    console.log('✅ Test activity inserted:', testActivity.id)

    // Test querying activities
    const { data: activities, error: queryError } = await supabase
      .from('system_activities')
      .select('*')
      .limit(5)

    if (queryError) {
      console.error('❌ Failed to query activities:', queryError)
      return false
    }

    console.log('✅ Successfully queried activities:', activities?.length || 0)

    // Test the log function
    const { data: logResult, error: logError } = await supabase
      .rpc('log_system_activity', {
        p_activity_type: 'function_test',
        p_description: 'Testing log_system_activity function',
        p_metadata: { test: true, function: 'log_system_activity' },
        p_severity: 'success',
        p_category: 'system'
      })

    if (logError) {
      console.warn('⚠️ Log function test failed:', logError)
    } else {
      console.log('✅ Log function test successful:', logResult)
    }

    console.log('🎉 Activities system test completed successfully!')
    return true
  } catch (error) {
    console.error('❌ Activities system test failed:', error)
    return false
  }
}

// Auto-initialize when this module is imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  initializeActivitiesTable().then(success => {
    if (success) {
      testActivitiesSystem()
    }
  })
}
