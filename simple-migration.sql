-- SIMPLE WORKING Migration for LangChain & Real-time Features
-- This version uses a simple approach that will definitely work
-- Run this in Supabase Dashboard SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Step 1: Fix api_keys table structure
-- Add columns if they don't exist (ignore errors if they do exist)
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS provider TEXT;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS api_key TEXT;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- Add unique constraint (ignore error if it exists)
DO $$
BEGIN
    ALTER TABLE api_keys ADD CONSTRAINT api_keys_provider_unique UNIQUE (provider);
EXCEPTION
    WHEN duplicate_object THEN
        -- Constraint already exists, ignore
        NULL;
END $$;

-- Step 2: Insert/Update OpenAI API key
-- Delete existing openai entry if it exists
DELETE FROM api_keys WHERE provider = 'openai';

-- Insert new OpenAI API key
-- REPLACE 'your-openai-api-key-here' WITH YOUR ACTUAL OPENAI API KEY
INSERT INTO api_keys (provider, api_key, is_active) 
VALUES ('openai', 'your-openai-api-key-here', true);

-- Step 3: Create LangChain tables
CREATE TABLE IF NOT EXISTS langchain_conversations (
    id TEXT PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT,
    messages JSONB DEFAULT '[]'::jsonb,
    context JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_langchain_conversations_user_id ON langchain_conversations(user_id);
CREATE INDEX IF NOT EXISTS idx_langchain_conversations_updated_at ON langchain_conversations(updated_at);

-- Enable RLS and create policy for conversations
ALTER TABLE langchain_conversations ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    CREATE POLICY "Users can manage their own conversations" ON langchain_conversations
    FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
EXCEPTION
    WHEN duplicate_object THEN
        -- Policy already exists, ignore
        NULL;
END $$;

-- Create langchain_documents table
CREATE TABLE IF NOT EXISTS langchain_documents (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    embedding vector(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_langchain_documents_embedding ON langchain_documents USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_langchain_documents_metadata ON langchain_documents USING gin(metadata);

-- Enable RLS and create policies for documents
ALTER TABLE langchain_documents ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    CREATE POLICY "Authenticated users can read documents" ON langchain_documents
    FOR SELECT TO authenticated USING (true);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

DO $$
BEGIN
    CREATE POLICY "Authenticated users can insert documents" ON langchain_documents
    FOR INSERT TO authenticated WITH CHECK (true);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Create user_presence table
CREATE TABLE IF NOT EXISTS user_presence (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('online', 'away', 'busy', 'offline')) DEFAULT 'offline',
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    current_page TEXT,
    is_typing BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'::jsonb,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_user_presence_status ON user_presence(status);
CREATE INDEX IF NOT EXISTS idx_user_presence_last_seen ON user_presence(last_seen);

-- Enable RLS and create policies for presence
ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    CREATE POLICY "Users can manage their own presence" ON user_presence
    FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

DO $$
BEGIN
    CREATE POLICY "Users can view all presence data" ON user_presence
    FOR SELECT TO authenticated USING (true);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Create collaborative_sessions table
CREATE TABLE IF NOT EXISTS collaborative_sessions (
    id TEXT PRIMARY KEY,
    type TEXT CHECK (type IN ('document', 'project', 'task', 'meeting')) NOT NULL,
    resource_id TEXT NOT NULL,
    participants JSONB DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_collaborative_sessions_type ON collaborative_sessions(type);
CREATE INDEX IF NOT EXISTS idx_collaborative_sessions_resource_id ON collaborative_sessions(resource_id);

-- Enable RLS and create policy for sessions
ALTER TABLE collaborative_sessions ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    CREATE POLICY "Authenticated users can manage sessions" ON collaborative_sessions
    FOR ALL TO authenticated USING (true) WITH CHECK (true);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Create document_comments table
CREATE TABLE IF NOT EXISTS document_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES document_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    position INTEGER NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_user_id ON document_comments(user_id);

-- Enable RLS and create policies for comments
ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    CREATE POLICY "Users can manage their own comments" ON document_comments
    FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

DO $$
BEGIN
    CREATE POLICY "Users can read all comments" ON document_comments
    FOR SELECT TO authenticated USING (true);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Create realtime_notifications table
CREATE TABLE IF NOT EXISTS realtime_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_realtime_notifications_user_id ON realtime_notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_realtime_notifications_read ON realtime_notifications(read);

-- Enable RLS and create policy for notifications
ALTER TABLE realtime_notifications ENABLE ROW LEVEL SECURITY;

DO $$
BEGIN
    CREATE POLICY "Users can manage their own notifications" ON realtime_notifications
    FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- Step 4: Create utility functions
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5
)
RETURNS TABLE (
    id text,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        langchain_documents.id,
        langchain_documents.content,
        langchain_documents.metadata,
        1 - (langchain_documents.embedding <=> query_embedding) AS similarity
    FROM langchain_documents
    WHERE 1 - (langchain_documents.embedding <=> query_embedding) > match_threshold
    ORDER BY similarity DESC
    LIMIT match_count;
$$;

CREATE OR REPLACE FUNCTION update_user_presence(
    p_user_id UUID,
    p_status TEXT DEFAULT NULL,
    p_current_page TEXT DEFAULT NULL,
    p_is_typing BOOLEAN DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO user_presence (user_id, status, current_page, is_typing, last_seen, updated_at)
    VALUES (p_user_id, COALESCE(p_status, 'online'), p_current_page, COALESCE(p_is_typing, FALSE), NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        status = COALESCE(p_status, user_presence.status),
        current_page = COALESCE(p_current_page, user_presence.current_page),
        is_typing = COALESCE(p_is_typing, user_presence.is_typing),
        last_seen = NOW(),
        updated_at = NOW();
END;
$$;

-- Step 5: Verification
SELECT 
    'SUCCESS: LangChain and Real-time collaboration features installed!' as status,
    COUNT(*) as new_tables_created
FROM information_schema.tables 
WHERE table_name IN (
    'langchain_conversations',
    'langchain_documents', 
    'user_presence',
    'collaborative_sessions',
    'document_comments',
    'realtime_notifications'
);

-- Show API key status
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'OpenAI API Key: Configured ✅'
        ELSE 'OpenAI API Key: Not configured ❌ - Please check your API key'
    END as api_key_status
FROM api_keys 
WHERE provider = 'openai' AND api_key IS NOT NULL AND api_key != 'your-openai-api-key-here';
