<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CTNL AI WORK-BOARD - Comprehensive System Manual</title>
    <meta name="description" content="Complete system manual for CTNL AI Workboard with voice commands, AI features, and comprehensive guides">
    <meta name="theme-color" content="#dc2626">

    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="CTNL AI Manual">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            background: #000000;
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            padding: 2rem 0;
            border-bottom: 1px solid #333;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.2rem;
            color: #888;
            max-width: 600px;
        }

        /* Navigation */
        .nav {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1rem;
            margin: 2rem 0;
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .nav-toggle {
            display: none;
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            padding: 0.75rem;
            color: #ffffff;
            cursor: pointer;
            margin-bottom: 1rem;
            width: 100%;
            text-align: center;
            font-weight: 500;
        }

        .nav-toggle:hover {
            background: rgba(220, 38, 38, 0.2);
        }

        .nav-content {
            transition: all 0.3s ease;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            align-items: stretch;
        }

        .nav-item {
            background: rgba(220, 38, 38, 0.1);
            border: 1px solid rgba(220, 38, 38, 0.3);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: #ffffff;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .nav-item:hover {
            background: rgba(220, 38, 38, 0.2);
            border-color: rgba(220, 38, 38, 0.5);
            transform: translateY(-2px);
        }

        /* Feature Sections */
        .feature-section {
            margin: 4rem 0;
            padding: 3rem 0;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .feature-subtitle {
            font-size: 1.5rem;
            font-weight: 500;
            margin: 2rem 0 1rem 0;
            color: #ffffff;
        }

        .feature-description {
            color: #888;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        /* Content Cards */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
            align-items: start;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .content-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
        }

        .content-card:hover {
            transform: translateY(-5px);
            border-color: rgba(220, 38, 38, 0.3);
            box-shadow: 0 20px 40px rgba(220, 38, 38, 0.1);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #ffffff;
        }

        .card-description {
            color: #888;
            margin-bottom: 1.5rem;
            font-size: 0.95rem;
        }

        /* Lists */
        .feature-list {
            list-style: none;
            margin-bottom: 1.5rem;
        }

        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            color: #ccc;
            font-size: 0.9rem;
        }

        .feature-list li:last-child {
            border-bottom: none;
        }

        .feature-list li::before {
            content: '✓';
            color: #dc2626;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        /* Voice Commands */
        .voice-command {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            color: #22c55e;
            font-size: 0.9rem;
        }

        /* Code Blocks */
        .code-block {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        /* Buttons */
        .action-button {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(220, 38, 38, 0.3);
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
            align-items: stretch;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #dc2626;
            display: block;
        }

        .stat-label {
            color: #888;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .feature-title {
                font-size: 1.8rem;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-toggle {
                display: block;
            }
            
            .nav-content.collapsed {
                display: none;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .content-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="container">
            <h1>🚀 CTNL AI WORK-BOARD</h1>
            <p>Comprehensive System Manual - Your complete guide to the next-generation AI-powered workforce management system with voice commands, self-healing capabilities, and advanced analytics</p>
        </div>
    </div>

    <div class="container">
        <!-- Quick Navigation -->
        <nav class="nav">
            <button class="nav-toggle" onclick="toggleNav()">📱 Navigation Menu</button>
            <div class="nav-content" id="navContent">
                <div class="nav-grid">
                    <a href="#overview" class="nav-item">📊 System Overview</a>
                    <a href="#voice-commands" class="nav-item">🎙️ Voice Commands</a>
                    <a href="#ai-features" class="nav-item">🤖 AI Features</a>
                    <a href="#user-guides" class="nav-item">📋 User Guides</a>
                    <a href="#roles" class="nav-item">👥 User Roles</a>
                    <a href="#setup" class="nav-item">⚙️ Setup Guide</a>
                    <a href="#architecture" class="nav-item">🏗️ Architecture</a>
                    <a href="#troubleshooting" class="nav-item">🔧 Troubleshooting</a>
                </div>
            </div>
        </nav>

        <!-- System Overview -->
        <div class="feature-section" id="overview">
            <h2 class="feature-title">🚀 System Overview</h2>
            <p class="feature-description">
                CTNL AI Workboard is a next-generation workforce management system featuring advanced AI capabilities,
                voice command control, self-healing technology, and comprehensive analytics. Built with enterprise-grade
                security and mobile-first design.
            </p>

            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">5</span>
                    <div class="stat-label">User Roles</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">50+</span>
                    <div class="stat-label">Voice Commands</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">3</span>
                    <div class="stat-label">AI Interfaces</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">99.9%</span>
                    <div class="stat-label">Uptime</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">Mobile Responsive</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">24/7</span>
                    <div class="stat-label">Self-Healing</div>
                </div>
            </div>

            <h3 class="feature-subtitle">🎯 Key Capabilities</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🎙️</div>
                        <div class="card-title">Voice Command System</div>
                    </div>
                    <div class="card-description">
                        Complete hands-free operation with natural language processing and context-aware responses.
                    </div>
                    <ul class="feature-list">
                        <li>Natural language voice commands</li>
                        <li>Context-aware responses</li>
                        <li>Multi-language support ready</li>
                        <li>Professional voice synthesis</li>
                        <li>Mobile voice optimization</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🤖</div>
                        <div class="card-title">Advanced AI Integration</div>
                    </div>
                    <div class="card-description">
                        GPT-4 powered AI with LangChain framework for intelligent assistance and automation.
                    </div>
                    <ul class="feature-list">
                        <li>GPT-4 Turbo integration</li>
                        <li>LangChain framework</li>
                        <li>Multiple AI interfaces</li>
                        <li>Conversation memory</li>
                        <li>Self-healing capabilities</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🛡️</div>
                        <div class="card-title">Enterprise Security</div>
                    </div>
                    <div class="card-description">
                        Role-based access control with comprehensive security policies and audit trails.
                    </div>
                    <ul class="feature-list">
                        <li>Row Level Security (RLS)</li>
                        <li>Role-based permissions</li>
                        <li>Encrypted data storage</li>
                        <li>Audit trails</li>
                        <li>HTTPS/SSL encryption</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">📱</div>
                        <div class="card-title">Mobile-First Design</div>
                    </div>
                    <div class="card-description">
                        Optimized for mobile devices with touch-friendly interface and offline capabilities.
                    </div>
                    <ul class="feature-list">
                        <li>Responsive design</li>
                        <li>Touch-friendly interface</li>
                        <li>GPS integration</li>
                        <li>Offline capabilities</li>
                        <li>Progressive Web App</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Voice Commands Section -->
        <div class="feature-section" id="voice-commands">
            <h2 class="feature-title">🎙️ Voice Command System</h2>
            <p class="feature-description">
                Control the entire system using natural voice commands. The AI-powered voice assistant understands
                context and provides intelligent responses based on your current page and user role.
            </p>

            <h3 class="feature-subtitle">🕐 Time Management Commands</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">⏰</div>
                        <div class="card-title">Clock-in/out Commands</div>
                    </div>
                    <div class="card-description">Voice commands for time tracking and attendance management.</div>
                    <div class="voice-command">"Clock in" → Start work session with GPS</div>
                    <div class="voice-command">"Clock out" → End work session</div>
                    <div class="voice-command">"Get my location" → Retrieve GPS coordinates</div>
                    <div class="voice-command">"Where am I" → Display current address</div>
                    <div class="voice-command">"Show my hours" → Display time summary</div>
                    <div class="voice-command">"Time report" → Generate time report</div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <div class="card-title">Performance Commands</div>
                    </div>
                    <div class="card-description">Voice commands for viewing performance metrics and analytics.</div>
                    <div class="voice-command">"Show my stats" → Personal performance metrics</div>
                    <div class="voice-command">"My performance" → Detailed analytics</div>
                    <div class="voice-command">"Team overview" → Team performance (managers)</div>
                    <div class="voice-command">"Generate report" → Create custom reports</div>
                    <div class="voice-command">"Financial summary" → Financial reports</div>
                </div>
            </div>

            <h3 class="feature-subtitle">🧭 Navigation Commands</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🏠</div>
                        <div class="card-title">System Navigation</div>
                    </div>
                    <div class="card-description">Navigate through the system using voice commands.</div>
                    <div class="voice-command">"Go to dashboard" → Main dashboard</div>
                    <div class="voice-command">"Go to projects" → Project management</div>
                    <div class="voice-command">"Time management" → Time tracking</div>
                    <div class="voice-command">"My account" → Account settings</div>
                    <div class="voice-command">"Show help" → Available commands</div>
                    <div class="voice-command">"System status" → Health check</div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🤖</div>
                        <div class="card-title">AI Assistance Commands</div>
                    </div>
                    <div class="card-description">Interact with AI features using voice commands.</div>
                    <div class="voice-command">"Help me with [task]" → AI assistance</div>
                    <div class="voice-command">"Analyze this document" → Document processing</div>
                    <div class="voice-command">"Create project [name]" → Project creation</div>
                    <div class="voice-command">"Search for [term]" → Global search</div>
                    <div class="voice-command">"What can you do" → System capabilities</div>
                </div>
            </div>

            <h3 class="feature-subtitle">🎯 How to Use Voice Commands</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">🎤</div>
                    <div class="card-title">Voice Assistant Activation</div>
                </div>
                <div class="card-description">
                    The voice assistant is available on every page as a floating button in the bottom-right corner.
                </div>
                <ul class="feature-list">
                    <li>Click the voice assistant button (microphone icon)</li>
                    <li>Allow microphone permissions when prompted</li>
                    <li>Speak your command clearly and naturally</li>
                    <li>Wait for the AI response and confirmation</li>
                    <li>Use "Help" command to see available options</li>
                </ul>
                <div class="code-block">
                    Example Usage:
                    1. Click voice button
                    2. Say: "Clock in"
                    3. AI responds: "Getting your location..."
                    4. AI confirms: "Successfully clocked in at [location]"
                </div>
            </div>
        </div>

        <!-- AI Features Section -->
        <div class="feature-section" id="ai-features">
            <h2 class="feature-title">🤖 Advanced AI Features</h2>
            <p class="feature-description">
                Experience next-generation AI capabilities with GPT-4 Turbo, LangChain framework, and self-healing technology.
            </p>

            <h3 class="feature-subtitle">🎭 AI Interfaces</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">💬</div>
                        <div class="card-title">Standard Chat Interface</div>
                    </div>
                    <div class="card-description">
                        Basic AI assistance for general questions and simple tasks.
                    </div>
                    <ul class="feature-list">
                        <li>Natural language conversations</li>
                        <li>General help and guidance</li>
                        <li>Simple task assistance</li>
                        <li>Context-aware responses</li>
                        <li>Voice and text input</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/ai" class="action-button">Access Standard Chat</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">💻</div>
                        <div class="card-title">Hacker Terminal Interface</div>
                    </div>
                    <div class="card-description">
                        Advanced command-line style interface for technical operations and system administration.
                    </div>
                    <ul class="feature-list">
                        <li>System commands and operations</li>
                        <li>Database queries and analysis</li>
                        <li>Technical troubleshooting</li>
                        <li>Advanced system monitoring</li>
                        <li>Command-style responses</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/ai" class="action-button">Access Hacker Terminal</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🚀</div>
                        <div class="card-title">Futuristic AI Interface</div>
                    </div>
                    <div class="card-description">
                        Advanced workflow automation with multi-step task execution and action detection.
                    </div>
                    <ul class="feature-list">
                        <li>Complex workflow automation</li>
                        <li>Multi-step task execution</li>
                        <li>Action detection and routing</li>
                        <li>Progress tracking</li>
                        <li>Neural network visualization</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/ai" class="action-button">Access Futuristic AI</a>
                </div>
            </div>

            <h3 class="feature-subtitle">🔧 Self-Healing System</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🛡️</div>
                        <div class="card-title">Automatic Error Detection</div>
                    </div>
                    <div class="card-description">
                        Real-time monitoring and detection of system errors across all components.
                    </div>
                    <ul class="feature-list">
                        <li>JavaScript runtime errors</li>
                        <li>Database connection issues</li>
                        <li>API endpoint failures</li>
                        <li>UI component crashes</li>
                        <li>Performance bottlenecks</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🔄</div>
                        <div class="card-title">Automatic Repair</div>
                    </div>
                    <div class="card-description">
                        Intelligent self-repair mechanisms that fix issues without human intervention.
                    </div>
                    <ul class="feature-list">
                        <li>Database reconnection</li>
                        <li>API retry with backoff</li>
                        <li>Service restart and recovery</li>
                        <li>Memory cleanup</li>
                        <li>Circuit breaker activation</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🧠</div>
                        <div class="card-title">AI-Powered Analysis</div>
                    </div>
                    <div class="card-description">
                        GPT-4 powered root cause analysis and intelligent fix suggestions.
                    </div>
                    <ul class="feature-list">
                        <li>Root cause analysis</li>
                        <li>Fix recommendations</li>
                        <li>Prevention strategies</li>
                        <li>Code optimization suggestions</li>
                        <li>Security vulnerability detection</li>
                    </ul>
                </div>
            </div>

            <h3 class="feature-subtitle">📊 AI Analytics & Insights</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">📈</div>
                    <div class="card-title">Intelligent Analytics</div>
                </div>
                <div class="card-description">
                    AI-powered analytics provide deep insights into system performance and user behavior.
                </div>
                <ul class="feature-list">
                    <li>Predictive analytics for project completion</li>
                    <li>Performance optimization recommendations</li>
                    <li>Resource allocation suggestions</li>
                    <li>Risk assessment and early warnings</li>
                    <li>Trend analysis and pattern recognition</li>
                    <li>Automated report generation with insights</li>
                </ul>
            </div>
        </div>

        <!-- User Guides Section -->
        <div class="feature-section" id="user-guides">
            <h2 class="feature-title">📋 Step-by-Step User Guides</h2>
            <p class="feature-description">
                Comprehensive guides for all major system functions with both manual steps and voice command alternatives.
            </p>

            <h3 class="feature-subtitle">🕐 Getting Started - Clock-in Process</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">👆</div>
                        <div class="card-title">Manual Clock-in Steps</div>
                    </div>
                    <div class="card-description">Traditional step-by-step clock-in process.</div>
                    <ul class="feature-list">
                        <li>Open the application → Navigate to ai.ctnigeria.com</li>
                        <li>Location permission → Allow GPS access when prompted</li>
                        <li>Verify location → Check displayed address is correct</li>
                        <li>Clock in → Click the green "Clock In" button</li>
                        <li>Confirmation → Wait for success message</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🎤</div>
                        <div class="card-title">Voice Clock-in Process</div>
                    </div>
                    <div class="card-description">Hands-free clock-in using voice commands.</div>
                    <ul class="feature-list">
                        <li>Activate voice → Click the voice assistant button</li>
                        <li>Say "Clock in" → System starts process automatically</li>
                        <li>Follow prompts → Voice assistant guides each step</li>
                        <li>Confirmation → Listen for "Successfully clocked in"</li>
                    </ul>
                    <div class="voice-command">Voice Command: "Clock in"</div>
                </div>
            </div>

            <h3 class="feature-subtitle">📊 Dashboard Navigation</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">👤</div>
                        <div class="card-title">Staff User Workflow</div>
                    </div>
                    <div class="card-description">Daily workflow for staff members.</div>
                    <ul class="feature-list">
                        <li>Dashboard → Performance Tab</li>
                        <li>View today's hours and task assignments</li>
                        <li>Check performance metrics</li>
                        <li>Access AI assistant for help</li>
                    </ul>
                    <div class="voice-command">"Show my stats"</div>
                    <div class="voice-command">"Go to my tasks"</div>
                    <div class="voice-command">"What's my performance today"</div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">👥</div>
                        <div class="card-title">Manager Workflow</div>
                    </div>
                    <div class="card-description">Management tasks and team oversight.</div>
                    <ul class="feature-list">
                        <li>Dashboard → Team Overview</li>
                        <li>Monitor team performance</li>
                        <li>Review project progress</li>
                        <li>Assign new tasks and generate reports</li>
                    </ul>
                    <div class="voice-command">"Team overview"</div>
                    <div class="voice-command">"Project status"</div>
                    <div class="voice-command">"Show team performance"</div>
                </div>
            </div>

            <h3 class="feature-subtitle">🤖 AI Assistant Usage</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">AI Interaction Guide</div>
                </div>
                <div class="card-description">How to effectively use the AI assistant features.</div>
                <div class="code-block">
                    Accessing AI Features:
                    1. Navigate → Dashboard → AI tab
                    2. Choose interface:
                       - Standard Chat (Basic assistance)
                       - Hacker Terminal (Advanced commands)
                       - Futuristic AI (Workflow automation)
                    3. Start conversation → Type or speak your question
                </div>
                <div class="code-block">
                    Voice-Activated AI:
                    1. Click voice button → Floating button (bottom-right)
                    2. Say "Help" → Get list of available commands
                    3. Natural conversation → Speak normally, AI understands
                    4. Follow guidance → AI provides step-by-step instructions
                </div>
            </div>

            <h3 class="feature-subtitle">📋 Project Management</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">📁</div>
                    <div class="card-title">Creating Projects</div>
                </div>
                <div class="card-description">Step-by-step project creation for admins and managers.</div>
                <div class="code-block">
                    Manual Project Creation:
                    1. Navigate → Dashboard → Projects tab
                    2. Click "New Project" → Fill in project details
                    3. Assign team members → Select from dropdown
                    4. Set deadlines → Choose start and end dates
                    5. Save project → Click "Create Project"
                </div>
                <div class="voice-command">Voice Alternative: "Create new project called [project name]"</div>
                <p style="color: #888; margin-top: 1rem;">
                    The AI will guide you through the entire creation process with voice prompts.
                </p>
            </div>
        </div>

        <!-- User Roles Section -->
        <div class="feature-section" id="roles">
            <h2 class="feature-title">👥 User Roles & Permissions</h2>
            <p class="feature-description">
                CTNL AI Workboard uses a hierarchical role system with granular permissions and access controls.
            </p>

            <h3 class="feature-subtitle">🏆 Role Hierarchy</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">⚡</div>
                    <div class="card-title">Permission Levels</div>
                </div>
                <div class="card-description">
                    Role hierarchy from highest to lowest privileges: Admin → Manager → Accountant → Staff-Admin → Staff
                </div>
                <div class="code-block">
                    Admin (Highest Privileges)
                      ↓
                    Manager
                      ↓
                    Accountant
                      ↓
                    Staff-Admin
                      ↓
                    Staff (Basic Privileges)
                </div>
            </div>

            <h3 class="feature-subtitle">🔐 Role Capabilities</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">👑</div>
                        <div class="card-title">Admin</div>
                    </div>
                    <div class="card-description">Full system access with all administrative privileges.</div>
                    <ul class="feature-list">
                        <li>Full system access to all data and operations</li>
                        <li>User management and role assignment</li>
                        <li>System configuration and settings</li>
                        <li>All AI features and interfaces</li>
                        <li>Complete reporting and analytics</li>
                        <li>Self-healing system monitoring</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/admin" class="action-button">Admin Dashboard</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">👔</div>
                        <div class="card-title">Manager</div>
                    </div>
                    <div class="card-description">Team management and project oversight capabilities.</div>
                    <ul class="feature-list">
                        <li>Team performance monitoring</li>
                        <li>Project management and assignment</li>
                        <li>Team reporting and analytics</li>
                        <li>All AI features</li>
                        <li>Leave approval and management</li>
                        <li>Resource allocation</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/manager" class="action-button">Manager Dashboard</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">💰</div>
                        <div class="card-title">Accountant</div>
                    </div>
                    <div class="card-description">Financial management and reporting focus.</div>
                    <ul class="feature-list">
                        <li>Financial reports and analytics</li>
                        <li>Document management</li>
                        <li>Expense tracking and approval</li>
                        <li>Standard AI features</li>
                        <li>Budget monitoring</li>
                        <li>Audit trail access</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/accountant" class="action-button">Accountant Dashboard</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">⚙️</div>
                        <div class="card-title">Staff-Admin</div>
                    </div>
                    <div class="card-description">Administrative support with limited system access.</div>
                    <ul class="feature-list">
                        <li>Basic user management</li>
                        <li>Document processing</li>
                        <li>Standard AI features</li>
                        <li>Limited reporting</li>
                        <li>Data entry and maintenance</li>
                        <li>System support tasks</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/staff-admin" class="action-button">Staff-Admin Dashboard</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">👤</div>
                        <div class="card-title">Staff</div>
                    </div>
                    <div class="card-description">Basic user access for daily work activities.</div>
                    <ul class="feature-list">
                        <li>Personal time tracking</li>
                        <li>Task management</li>
                        <li>Basic AI assistance</li>
                        <li>Personal performance reports</li>
                        <li>Leave requests</li>
                        <li>Profile management</li>
                    </ul>
                    <a href="https://ai.ctnigeria.com/dashboard/staff" class="action-button">Staff Dashboard</a>
                </div>
            </div>

            <h3 class="feature-subtitle">📊 Permission Matrix</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">🔒</div>
                    <div class="card-title">Access Control Matrix</div>
                </div>
                <div class="card-description">
                    Detailed breakdown of what each role can access and modify.
                </div>
                <div class="code-block">
                    Feature Access by Role:

                    User Management:     Admin ✓ | Manager (Team) | Others ✗
                    Project Management:  Admin ✓ | Manager ✓ | Accountant (View) | Staff-Admin (Limited) | Staff (Assigned)
                    Time Management:     Admin (All) | Manager (Team) | Others (Own)
                    Financial Reports:   Admin ✓ | Manager (Team) | Accountant ✓ | Others ✗
                    AI Features:         Admin (All) | Manager (All) | Others (Standard)
                    System Settings:     Admin ✓ | Staff-Admin (Limited) | Others ✗
                </div>
            </div>
        </div>

        <!-- Setup Guide Section -->
        <div class="feature-section" id="setup">
            <h2 class="feature-title">⚙️ Setup & Implementation Guide</h2>
            <p class="feature-description">
                Complete setup instructions for administrators to deploy and configure the CTNL AI Workboard system.
            </p>

            <h3 class="feature-subtitle">🚀 Quick Setup Checklist</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">1️⃣</div>
                        <div class="card-title">Phase 1: Database Setup</div>
                    </div>
                    <div class="card-description">Essential database configuration (5 minutes)</div>
                    <ul class="feature-list">
                        <li>Run MINIMAL_SETUP.sql in Supabase</li>
                        <li>Run SELF_HEALING_TABLES.sql in Supabase</li>
                        <li>Verify tables exist in Supabase dashboard</li>
                        <li>Check RLS policies are enabled</li>
                    </ul>
                    <a href="https://dvflgnqwbsjityrowatf.supabase.co" class="action-button" target="_blank">Open Supabase</a>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">2️⃣</div>
                        <div class="card-title">Phase 2: Voice System</div>
                    </div>
                    <div class="card-description">Voice command activation (2 minutes)</div>
                    <ul class="feature-list">
                        <li>Allow microphone permissions in browser</li>
                        <li>Test voice commands with "Help"</li>
                        <li>Verify voice responses work</li>
                        <li>Check mobile voice functionality</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">3️⃣</div>
                        <div class="card-title">Phase 3: AI System Testing</div>
                    </div>
                    <div class="card-description">AI integration verification (3 minutes)</div>
                    <ul class="feature-list">
                        <li>Test LangChain (look for [AI-ENHANCED] prefix)</li>
                        <li>Test self-healing system monitoring</li>
                        <li>Test voice AI interactions</li>
                        <li>Verify all AI interfaces work</li>
                    </ul>
                </div>
            </div>

            <h3 class="feature-subtitle">🔗 Important Links & Resources</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🌐</div>
                        <div class="card-title">System Dashboards</div>
                    </div>
                    <div class="card-description">Access points for system management</div>
                    <ul class="feature-list">
                        <li><a href="https://ai.ctnigeria.com" target="_blank" style="color: #dc2626;">Production App</a></li>
                        <li><a href="https://dvflgnqwbsjityrowatf.supabase.co" target="_blank" style="color: #dc2626;">Supabase Dashboard</a></li>
                        <li><a href="https://github.com/obibiifeanyi/aiworkboardctnl" target="_blank" style="color: #dc2626;">GitHub Repository</a></li>
                        <li><a href="https://vercel.com/dashboard" target="_blank" style="color: #dc2626;">Vercel Deployment</a></li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">📚</div>
                        <div class="card-title">Documentation Files</div>
                    </div>
                    <div class="card-description">Technical documentation and guides</div>
                    <ul class="feature-list">
                        <li>COMPREHENSIVE_SYSTEM_ANALYSIS.md</li>
                        <li>VISUAL_USER_GUIDE.md</li>
                        <li>ENHANCED_FEATURES_SUMMARY.md</li>
                        <li>LANGCHAIN_SETUP.md</li>
                        <li>SELF_HEALING_DOCUMENTATION.md</li>
                        <li>IMPLEMENTATION_GUIDE.md</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🛠️</div>
                        <div class="card-title">Setup Scripts</div>
                    </div>
                    <div class="card-description">SQL scripts for database configuration</div>
                    <ul class="feature-list">
                        <li>MINIMAL_SETUP.sql (Basic setup)</li>
                        <li>RLS_SETUP.sql (Advanced security)</li>
                        <li>SELF_HEALING_TABLES.sql (Monitoring)</li>
                        <li>test-langchain.js (Testing script)</li>
                    </ul>
                </div>
            </div>

            <h3 class="feature-subtitle">📋 Pre-Launch Checklist</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">✅</div>
                    <div class="card-title">Deployment Verification</div>
                </div>
                <div class="card-description">
                    Complete this checklist before going live with the system.
                </div>
                <div class="code-block">
                    ☐ Database tables created and verified
                    ☐ Voice system tested on multiple browsers
                    ☐ AI responses working with enhanced prefix
                    ☐ Self-healing system monitoring active
                    ☐ User training materials prepared
                    ☐ Support documentation updated
                    ☐ Mobile functionality tested
                    ☐ Security policies verified
                    ☐ Backup procedures in place
                    ☐ User accounts created and roles assigned
                </div>
            </div>

            <h3 class="feature-subtitle">🎯 Success Metrics</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">>80%</span>
                    <div class="stat-label">User Adoption (30 days)</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">>50%</span>
                    <div class="stat-label">Voice Command Usage</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">>99%</span>
                    <div class="stat-label">System Uptime</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">4.5/5</span>
                    <div class="stat-label">User Satisfaction</div>
                </div>
            </div>
        </div>

        <!-- Architecture Section -->
        <div class="feature-section" id="architecture">
            <h2 class="feature-title">🏗️ System Architecture</h2>
            <p class="feature-description">
                Technical overview of the CTNL AI Workboard system architecture and components.
            </p>

            <h3 class="feature-subtitle">🔧 Technical Stack</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">⚛️</div>
                        <div class="card-title">Frontend Technologies</div>
                    </div>
                    <div class="card-description">Modern React-based frontend with TypeScript</div>
                    <ul class="feature-list">
                        <li>React 18 with TypeScript</li>
                        <li>Tailwind CSS for styling</li>
                        <li>Shadcn/ui component library</li>
                        <li>React Router for navigation</li>
                        <li>React Query for data management</li>
                        <li>Vite for build optimization</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🗄️</div>
                        <div class="card-title">Backend Services</div>
                    </div>
                    <div class="card-description">Supabase-powered backend with PostgreSQL</div>
                    <ul class="feature-list">
                        <li>Supabase (Database, Auth, Edge Functions)</li>
                        <li>PostgreSQL with Row Level Security</li>
                        <li>Real-time subscriptions</li>
                        <li>File storage with CDN</li>
                        <li>Edge Functions for serverless logic</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🤖</div>
                        <div class="card-title">AI & ML Services</div>
                    </div>
                    <div class="card-description">Advanced AI integration with multiple providers</div>
                    <ul class="feature-list">
                        <li>OpenAI GPT-4 Turbo</li>
                        <li>LangChain framework</li>
                        <li>Speech Recognition API</li>
                        <li>Text-to-Speech API</li>
                        <li>Self-healing AI system</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🌐</div>
                        <div class="card-title">External Integrations</div>
                    </div>
                    <div class="card-description">Third-party services and APIs</div>
                    <ul class="feature-list">
                        <li>Resend for email notifications</li>
                        <li>Vercel Analytics for usage tracking</li>
                        <li>Google Maps for location services</li>
                        <li>Browser APIs (GPS, Speech, etc.)</li>
                    </ul>
                </div>
            </div>

            <h3 class="feature-subtitle">📊 Performance Metrics</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number"><2s</span>
                    <div class="stat-label">Page Load Time</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number"><500ms</span>
                    <div class="stat-label">API Response</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">95+</span>
                    <div class="stat-label">Lighthouse Score</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">A+</span>
                    <div class="stat-label">Security Rating</div>
                </div>
            </div>
        </div>

        <!-- Troubleshooting Section -->
        <div class="feature-section" id="troubleshooting">
            <h2 class="feature-title">🔧 Troubleshooting Guide</h2>
            <p class="feature-description">
                Common issues and solutions for the CTNL AI Workboard system.
            </p>

            <h3 class="feature-subtitle">🎤 Voice Command Issues</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🚫</div>
                        <div class="card-title">Voice Commands Not Working</div>
                    </div>
                    <div class="card-description">Steps to resolve voice recognition issues</div>
                    <ul class="feature-list">
                        <li>Check browser compatibility (Chrome/Edge recommended)</li>
                        <li>Grant microphone permissions</li>
                        <li>Test microphone in other applications</li>
                        <li>Clear browser cache and cookies</li>
                        <li>Restart browser and try again</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🔊</div>
                        <div class="card-title">Audio Response Issues</div>
                    </div>
                    <div class="card-description">Fixing text-to-speech problems</div>
                    <ul class="feature-list">
                        <li>Check system volume settings</li>
                        <li>Test speakers/headphones</li>
                        <li>Verify browser audio permissions</li>
                        <li>Try different browser</li>
                        <li>Check for browser extensions blocking audio</li>
                    </ul>
                </div>
            </div>

            <h3 class="feature-subtitle">🤖 AI System Issues</h3>
            <div class="content-grid">
                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">⚠️</div>
                        <div class="card-title">No [AI-ENHANCED] Responses</div>
                    </div>
                    <div class="card-description">LangChain integration troubleshooting</div>
                    <ul class="feature-list">
                        <li>Check USE_LANGCHAIN=true in environment</li>
                        <li>Verify OpenAI API key is valid</li>
                        <li>Check browser console for errors</li>
                        <li>Ensure database tables are created</li>
                        <li>Test with different AI interface</li>
                    </ul>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="card-icon">🐌</div>
                        <div class="card-title">Slow AI Responses</div>
                    </div>
                    <div class="card-description">Performance optimization tips</div>
                    <ul class="feature-list">
                        <li>First requests are slower (normal behavior)</li>
                        <li>Check internet connection speed</li>
                        <li>Try shorter, more specific queries</li>
                        <li>Clear browser cache</li>
                        <li>Use different AI interface</li>
                    </ul>
                </div>
            </div>

            <h3 class="feature-subtitle">📱 Mobile Issues</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">📲</div>
                    <div class="card-title">Mobile Functionality Problems</div>
                </div>
                <div class="card-description">Mobile-specific troubleshooting steps</div>
                <ul class="feature-list">
                    <li>Enable location services for GPS features</li>
                    <li>Allow microphone access for voice commands</li>
                    <li>Use supported mobile browsers (Chrome, Safari)</li>
                    <li>Check mobile data/WiFi connection</li>
                    <li>Clear mobile browser cache</li>
                    <li>Try landscape orientation for better layout</li>
                </ul>
            </div>

            <h3 class="feature-subtitle">🆘 Getting Help</h3>
            <div class="content-card">
                <div class="card-header">
                    <div class="card-icon">💬</div>
                    <div class="card-title">Support Options</div>
                </div>
                <div class="card-description">How to get assistance when you need it</div>
                <ul class="feature-list">
                    <li>🎙️ Voice Command: Say "Help" in the app</li>
                    <li>📧 Email: <EMAIL></li>
                    <li>📚 Documentation: Check guide files in repository</li>
                    <li>🤖 AI Assistant: Use any AI interface for guidance</li>
                    <li>📱 In-app help: Click help buttons throughout the system</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer style="background: rgba(255, 255, 255, 0.05); border-top: 1px solid rgba(255, 255, 255, 0.1); padding: 3rem 0; margin-top: 4rem;">
        <div class="container">
            <div style="text-align: center;">
                <h3 style="color: #dc2626; margin-bottom: 1rem; font-size: 1.5rem;">🎉 Congratulations!</h3>
                <p style="color: #888; margin-bottom: 2rem; max-width: 600px; margin-left: auto; margin-right: auto;">
                    Your CTNL AI Workboard is now a next-generation, voice-enabled, AI-powered work management system
                    with self-healing capabilities and comprehensive analytics. Your team has access to the most
                    advanced workforce management platform available!
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 2rem;">
                    <a href="https://ai.ctnigeria.com" class="action-button" target="_blank">🚀 Launch System</a>
                    <a href="https://dvflgnqwbsjityrowatf.supabase.co" class="action-button" target="_blank">⚙️ Supabase Dashboard</a>
                    <a href="https://github.com/obibiifeanyi/aiworkboardctnl" class="action-button" target="_blank">📚 GitHub Repository</a>
                </div>
                <p style="color: #666; font-size: 0.9rem;">
                    © 2024 CTNL AI Work-Board. Built with ❤️ using React, Supabase, and advanced AI technologies.
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Navigation toggle for mobile
        function toggleNav() {
            const navContent = document.getElementById('navContent');
            navContent.classList.toggle('collapsed');
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all content cards
        document.querySelectorAll('.content-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });

        // Add click tracking for analytics
        document.querySelectorAll('.action-button').forEach(button => {
            button.addEventListener('click', function() {
                console.log('Button clicked:', this.textContent);
                // Add analytics tracking here if needed
            });
        });
    </script>
</body>
</html>
