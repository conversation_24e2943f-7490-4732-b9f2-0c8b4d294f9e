import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { Plus, Trash2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface PaymentItem {
  description: string;
  amount: string;
}

export const MemoGeneration = () => {
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    to_recipient: "",
    subject: "",
    purpose: "",
    account_details: "",
  });
  
  const [paymentItems, setPaymentItems] = useState<PaymentItem[]>([
    { description: "", amount: "" }
  ]);

  // Fetch all profiles for the recipient dropdown
  const { data: profiles } = useQuery({
    queryKey: ['profiles-for-memo'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .order('full_name');

      if (error) throw error;
      return data;
    },
  });

  const addPaymentItem = () => {
    setPaymentItems([...paymentItems, { description: "", amount: "" }]);
  };

  const removePaymentItem = (index: number) => {
    if (paymentItems.length > 1) {
      setPaymentItems(paymentItems.filter((_, i) => i !== index));
    }
  };

  const updatePaymentItem = (index: number, field: keyof PaymentItem, value: string) => {
    const updated = paymentItems.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    );
    setPaymentItems(updated);
  };

  const calculateTotal = () => {
    return paymentItems.reduce((total, item) => {
      const amount = parseFloat(item.amount) || 0;
      return total + amount;
    }, 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!userProfile?.id) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const totalAmount = calculateTotal();
      
      const { error } = await supabase
        .from('memos')
        .insert([{
          title: formData.subject,
          content: formData.purpose,
          created_by: userProfile.id,
          from_user: userProfile.id,
          to_recipient: formData.to_recipient,
          subject: formData.subject,
          purpose: formData.purpose,
          payment_items: paymentItems as any,
          account_details: formData.account_details,
          total_amount: totalAmount,
          memo_date: new Date().toISOString().split('T')[0],
          status: 'pending'
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Memo submitted successfully and is pending approval",
      });

      // Clear form
      setFormData({
        to_recipient: "",
        subject: "",
        purpose: "",
        account_details: "",
      });
      setPaymentItems([{ description: "", amount: "" }]);

    } catch (error) {
      console.error('Error submitting memo:', error);
      toast({
        title: "Error",
        description: "Failed to submit memo",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-center">
          Payment Request Memo
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="to_recipient">To (Recipient)</Label>
              <Select 
                value={formData.to_recipient} 
                onValueChange={(value) => setFormData({ ...formData, to_recipient: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select recipient..." />
                </SelectTrigger>
                <SelectContent>
                  {profiles?.filter(profile => profile.full_name && profile.full_name.trim() !== '').map((profile) => (
                    <SelectItem key={profile.id} value={profile.full_name || profile.id}>
                      <div className="flex flex-col">
                        <span>{profile.full_name}</span>
                        <span className="text-sm text-muted-foreground">
                          {profile.email} • {profile.role}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                  {profiles?.filter(profile => !profile.full_name || profile.full_name.trim() === '').map((profile) => (
                    <SelectItem key={profile.id} value={profile.email || profile.id}>
                      <div className="flex flex-col">
                        <span>{profile.email}</span>
                        <span className="text-sm text-muted-foreground">
                          {profile.role}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={formData.subject}
                onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
                placeholder="Payment request subject"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="purpose">Purpose/Justification</Label>
            <Textarea
              id="purpose"
              value={formData.purpose}
              onChange={(e) => setFormData({ ...formData, purpose: e.target.value })}
              placeholder="Explain the purpose and justification for this payment request..."
              className="min-h-[100px]"
              required
            />
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-lg font-semibold">Payment Items</Label>
              <Button
                type="button"
                onClick={addPaymentItem}
                size="sm"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
            
            {paymentItems.map((item, index) => (
              <div key={index} className="flex gap-4 items-end">
                <div className="flex-1 space-y-2">
                  <Label>Description</Label>
                  <Input
                    value={item.description}
                    onChange={(e) => updatePaymentItem(index, 'description', e.target.value)}
                    placeholder="Item description"
                    required
                  />
                </div>
                
                <div className="w-32 space-y-2">
                  <Label>Amount (₦)</Label>
                  <Input
                    type="number"
                    step="0.01"
                    value={item.amount}
                    onChange={(e) => updatePaymentItem(index, 'amount', e.target.value)}
                    placeholder="0.00"
                    required
                  />
                </div>
                
                {paymentItems.length > 1 && (
                  <Button
                    type="button"
                    onClick={() => removePaymentItem(index)}
                    size="sm"
                    variant="outline"
                    className="text-red-600 hover:text-red-800"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ))}
            
            <div className="text-right">
              <div className="text-lg font-semibold">
                Total: ₦{calculateTotal().toLocaleString('en-NG', { minimumFractionDigits: 2 })}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="account_details">Bank Account Details (if applicable)</Label>
            <Textarea
              id="account_details"
              value={formData.account_details}
              onChange={(e) => setFormData({ ...formData, account_details: e.target.value })}
              placeholder="Bank name, account number, account name..."
              className="min-h-[80px]"
            />
          </div>

          <Button 
            type="submit" 
            className="w-full" 
            size="lg"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Payment Request"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
