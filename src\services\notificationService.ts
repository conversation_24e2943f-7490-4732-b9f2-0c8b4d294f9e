import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface TimeNotification {
  id: string;
  user_id: string;
  type: 'clock_in' | 'clock_out' | 'break_start' | 'break_end' | 'overtime_alert' | 'late_arrival';
  title: string;
  message: string;
  data?: Record<string, any>;
  read: boolean;
  created_at: string;
}

export interface NotificationSubscription {
  user_id: string;
  notification_types: string[];
  push_enabled: boolean;
  email_enabled: boolean;
  in_app_enabled: boolean;
}

class NotificationService {
  private static instance: NotificationService;
  private subscriptions: Map<string, (notification: TimeNotification) => void> = new Map();

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Subscribe to real-time notifications
  subscribeToNotifications(
    userId: string,
    callback: (notification: TimeNotification) => void
  ): () => void {
    this.subscriptions.set(userId, callback);

    // Set up Supabase real-time subscription
    const channel = supabase
      .channel(`notifications-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          const notification = payload.new as TimeNotification;
          callback(notification);
        }
      )
      .subscribe();

    // Return unsubscribe function
    return () => {
      this.subscriptions.delete(userId);
      supabase.removeChannel(channel);
    };
  }

  // Send notification to specific user
  async sendNotification(notification: Omit<TimeNotification, 'id' | 'created_at' | 'read'>): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .insert({
          ...notification,
          read: false,
          created_at: new Date().toISOString(),
        });

      if (error) throw error;

      // Also trigger browser notification if permission granted
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification.type,
        });
      }
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  }

  // Send clock-in notification
  async notifyClockIn(userId: string, userName: string, location: string, time: Date): Promise<void> {
    const timeString = time.toLocaleTimeString();

    await this.sendNotification({
      user_id: userId,
      type: 'clock_in',
      title: 'Clock In Successful',
      message: `${userName} clocked in at ${location}`,
      data: { location, time: timeString },
    });

    // Notify managers
    await this.notifyManagers(userId, {
      type: 'clock_in',
      title: 'Team Member Clock In',
      message: `${userName} has clocked in at ${location}`,
      data: { userId, userName, location, time: timeString },
    });

    // Send email notification to managers for important events
    await this.sendEmailNotification('clock_in', {
      userName,
      location,
      time: timeString,
      action: 'clocked in'
    });
  }

  // Send clock-out notification
  async notifyClockOut(
    userId: string,
    userName: string,
    location: string,
    time: Date,
    durationMinutes: number
  ): Promise<void> {
    const timeString = time.toLocaleTimeString();
    const durationString = `${Math.floor(durationMinutes / 60)}h ${durationMinutes % 60}m`;

    await this.sendNotification({
      user_id: userId,
      type: 'clock_out',
      title: 'Clock Out Successful',
      message: `Work session completed. Duration: ${durationString}`,
      data: { location, time: timeString, duration: durationString },
    });

    // Notify managers
    await this.notifyManagers(userId, {
      type: 'clock_out',
      title: 'Team Member Clock Out',
      message: `${userName} has clocked out. Session duration: ${durationString}`,
      data: { userId, userName, location, time: timeString, duration: durationString },
    });

    // Send email notification to managers for important events
    await this.sendEmailNotification('clock_out', {
      userName,
      location,
      time: timeString,
      duration: durationString,
      action: 'clocked out'
    });
  }

  // Send overtime alert
  async notifyOvertime(userId: string, userName: string, overtimeHours: number): Promise<void> {
    await this.sendNotification({
      user_id: userId,
      type: 'overtime_alert',
      title: 'Overtime Alert',
      message: `You have worked ${overtimeHours} hours of overtime today`,
      data: { overtimeHours },
    });

    // Notify managers about overtime
    await this.notifyManagers(userId, {
      type: 'overtime_alert',
      title: 'Team Member Overtime',
      message: `${userName} has worked ${overtimeHours} hours of overtime`,
      data: { userId, userName, overtimeHours },
    });
  }

  // Send late arrival notification
  async notifyLateArrival(userId: string, userName: string, minutesLate: number): Promise<void> {
    await this.sendNotification({
      user_id: userId,
      type: 'late_arrival',
      title: 'Late Arrival Recorded',
      message: `You arrived ${minutesLate} minutes late today`,
      data: { minutesLate },
    });

    // Notify managers about late arrival
    await this.notifyManagers(userId, {
      type: 'late_arrival',
      title: 'Team Member Late Arrival',
      message: `${userName} arrived ${minutesLate} minutes late`,
      data: { userId, userName, minutesLate },
    });
  }

  // Notify all managers and admins
  private async notifyManagers(
    staffUserId: string,
    notification: Omit<TimeNotification, 'id' | 'created_at' | 'read' | 'user_id'>
  ): Promise<void> {
    try {
      // Get all managers and admins
      const { data: managers, error } = await supabase
        .from('profiles')
        .select('id')
        .in('role', ['manager', 'admin', 'staff-admin']);

      if (error) throw error;

      // Send notification to each manager
      const notifications = managers?.map(manager => ({
        ...notification,
        user_id: manager.id,
        read: false,
        created_at: new Date().toISOString(),
      })) || [];

      if (notifications.length > 0) {
        const { error: insertError } = await supabase
          .from('notifications')
          .insert(notifications);

        if (insertError) throw insertError;
      }
    } catch (error) {
      console.error('Failed to notify managers:', error);
    }
  }

  // Get user notifications
  async getUserNotifications(userId: string, limit: number = 50): Promise<TimeNotification[]> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      return [];
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }

  // Mark all notifications as read for user
  async markAllAsRead(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  }

  // Request browser notification permission
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  // Update notification preferences
  async updateNotificationPreferences(
    userId: string,
    preferences: Partial<NotificationSubscription>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('notification_preferences')
        .upsert({
          user_id: userId,
          ...preferences,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      console.error('Failed to update notification preferences:', error);
    }
  }

  // Get notification preferences
  async getNotificationPreferences(userId: string): Promise<NotificationSubscription | null> {
    try {
      const { data, error } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') throw error;
      return data;
    } catch (error) {
      console.error('Failed to fetch notification preferences:', error);
      return null;
    }
  }

  // Send email notification to managers and admins
  private async sendEmailNotification(type: string, data: any): Promise<void> {
    try {
      // Get managers and admins with email preferences enabled
      const { data: managers, error } = await supabase
        .from('profiles')
        .select('id, email, full_name')
        .in('role', ['manager', 'admin', 'staff-admin']);

      if (error) throw error;

      if (managers && managers.length > 0) {
        const recipients = managers
          .filter(manager => manager.email)
          .map(manager => manager.email);

        if (recipients.length > 0) {
          await supabase.functions.invoke('send-notification', {
            body: {
              type,
              recipients,
              data
            }
          });
        }
      }
    } catch (error) {
      console.error('Failed to send email notification:', error);
      // Don't throw error to avoid breaking the main notification flow
    }
  }
}

export const notificationService = NotificationService.getInstance();
