import { CreateBatteryForm } from '@/components/battery/CreateBatteryForm';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { batteryService } from '@/services/api/battery';
import type { BatteryStats, Battery as BatteryType } from '@/types/battery';
import { format } from 'date-fns';
import {
    Activity,
    AlertTriangle,
    Battery,
    Calendar,
    DollarSign,
    Filter,
    MapPin,
    MoreHorizontal,
    Plus,
    Search
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';

export const BatteryManagement: React.FC = () => {
  const [batteries, setBatteries] = useState<BatteryType[]>([]);
  const [stats, setStats] = useState<BatteryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateDialog, setShowCreateDialog] = useState(false);

  // Load batteries and stats
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [batteriesResult, statsResult] = await Promise.all([
        batteryService.getBatteries({ limit: 50 }),
        batteryService.getBatteryStats()
      ]);

      if (batteriesResult.success) {
        setBatteries(batteriesResult.data?.data || []);
      }

      if (statsResult.success) {
        setStats(statsResult.data);
      }
    } catch (error) {
      console.error('Error loading battery data:', error);
      toast.error('Failed to load battery data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSuccess = (newBattery: BatteryType) => {
    setBatteries(prev => [newBattery, ...prev]);
    setShowCreateDialog(false);
    loadData(); // Refresh stats
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'retired': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      case 'disposed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'excellent': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'good': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'fair': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'poor': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const filteredBatteries = batteries.filter(battery =>
    battery.serial_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
    battery.battery_type?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    battery.current_location?.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Battery Management</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="neumorphism-card animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Battery className="h-8 w-8" />
            Battery Management
          </h1>
          <p className="text-muted-foreground mt-1">
            Manage and monitor your battery inventory
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Battery
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Battery</DialogTitle>
              <DialogDescription>
                Create a new battery entry in the management system with complete specifications and tracking details.
              </DialogDescription>
            </DialogHeader>
            <CreateBatteryForm
              onSuccess={handleCreateSuccess}
              onCancel={() => setShowCreateDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="neumorphism-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Batteries</p>
                  <p className="text-2xl font-bold">{stats.total_batteries}</p>
                </div>
                <Battery className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="neumorphism-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active_batteries}</p>
                </div>
                <Activity className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="neumorphism-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Maintenance</p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.maintenance_batteries}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="neumorphism-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Capacity</p>
                  <p className="text-2xl font-bold">{stats.total_capacity_ah.toFixed(0)}Ah</p>
                </div>
                <DollarSign className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <Card className="neumorphism-card">
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search batteries by serial number, type, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Battery List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredBatteries.map((battery) => (
          <Card key={battery.id} className="neumorphism-card hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{battery.serial_number}</CardTitle>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex gap-2">
                <Badge className={getStatusColor(battery.status)}>
                  {battery.status}
                </Badge>
                <Badge className={getConditionColor(battery.condition)}>
                  {battery.condition}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm">
                  <Battery className="h-4 w-4 text-muted-foreground" />
                  <span>{battery.battery_type?.name}</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {battery.battery_type?.voltage}V • {battery.battery_type?.capacity_ah}Ah • {battery.battery_type?.chemistry}
                </div>
              </div>

              {battery.current_location && (
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span>{battery.current_location.name}</span>
                </div>
              )}

              {battery.purchase_date && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span>Purchased: {format(new Date(battery.purchase_date), 'MMM dd, yyyy')}</span>
                </div>
              )}

              {battery.purchase_cost && (
                <div className="flex items-center gap-2 text-sm">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span>₦{battery.purchase_cost.toLocaleString()}</span>
                </div>
              )}

              {battery.notes && (
                <div className="text-sm text-muted-foreground bg-gray-50 dark:bg-gray-800 p-2 rounded">
                  {battery.notes}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredBatteries.length === 0 && (
        <Card className="neumorphism-card">
          <CardContent className="p-12 text-center">
            <Battery className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No batteries found</h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding your first battery'}
            </p>
            {!searchTerm && (
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Battery
              </Button>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
