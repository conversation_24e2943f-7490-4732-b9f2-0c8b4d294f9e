import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Key, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff,
  CheckCircle,
  AlertCircle,
  Save
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { toast } from 'sonner';

interface APIKey {
  id: string;
  service: string;
  api_key: string;
  name?: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  last_used_at?: string;
  usage_count: number;
}

const API_SERVICES = [
  { value: 'openai', label: 'OpenAI', description: 'GPT models and AI features' },
  { value: 'anthropic', label: 'Anthropic', description: 'Claude AI models' },
  { value: 'google', label: 'Google AI', description: 'Gemini and other Google AI services' },
  { value: 'azure', label: 'Azure OpenAI', description: 'Microsoft Azure AI services' },
  { value: 'custom', label: 'Custom', description: 'Other AI services' }
];

export const APIKeysManager: React.FC = () => {
  const { user } = useAuth();
  const [apiKeys, setApiKeys] = useState<APIKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set());
  const [newKey, setNewKey] = useState({
    service: 'openai',
    api_key: '',
    name: '',
    description: ''
  });

  useEffect(() => {
    if (user) {
      loadAPIKeys();
    }
  }, [user]);

  const loadAPIKeys = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('api_keys')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading API keys:', error);
        toast.error('Failed to load API keys');
        return;
      }

      setApiKeys(data || []);
    } catch (error) {
      console.error('Error loading API keys:', error);
      toast.error('Failed to load API keys');
    } finally {
      setLoading(false);
    }
  };

  const saveAPIKey = async () => {
    if (!newKey.api_key.trim()) {
      toast.error('API key is required');
      return;
    }

    try {
      const { error } = await supabase
        .from('api_keys')
        .upsert({
          user_id: user?.id,
          service: newKey.service,
          api_key: newKey.api_key.trim(),
          name: newKey.name.trim() || null,
          description: newKey.description.trim() || null,
          is_active: true
        }, {
          onConflict: 'user_id,service'
        });

      if (error) {
        console.error('Error saving API key:', error);
        toast.error('Failed to save API key');
        return;
      }

      toast.success('API key saved successfully');
      setNewKey({ service: 'openai', api_key: '', name: '', description: '' });
      setShowAddForm(false);
      loadAPIKeys();
    } catch (error) {
      console.error('Error saving API key:', error);
      toast.error('Failed to save API key');
    }
  };

  const deleteAPIKey = async (id: string) => {
    try {
      const { error } = await supabase
        .from('api_keys')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting API key:', error);
        toast.error('Failed to delete API key');
        return;
      }

      toast.success('API key deleted successfully');
      loadAPIKeys();
    } catch (error) {
      console.error('Error deleting API key:', error);
      toast.error('Failed to delete API key');
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const maskAPIKey = (key: string) => {
    if (key.length <= 8) return '••••••••';
    return key.substring(0, 4) + '••••••••' + key.substring(key.length - 4);
  };

  const getServiceInfo = (service: string) => {
    return API_SERVICES.find(s => s.value === service) || { label: service, description: '' };
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            API Keys
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">Loading API keys...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Key className="h-5 w-5" />
              API Keys Management
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Manage your AI service API keys securely
            </p>
          </div>
          <Button onClick={() => setShowAddForm(!showAddForm)}>
            <Plus className="h-4 w-4 mr-2" />
            Add API Key
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add New API Key Form */}
          {showAddForm && (
            <Card className="border-dashed">
              <CardContent className="pt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="service">Service</Label>
                    <select
                      id="service"
                      value={newKey.service}
                      onChange={(e) => setNewKey({ ...newKey, service: e.target.value })}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {API_SERVICES.map(service => (
                        <option key={service.value} value={service.value}>
                          {service.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="name">Name (Optional)</Label>
                    <Input
                      id="name"
                      value={newKey.name}
                      onChange={(e) => setNewKey({ ...newKey, name: e.target.value })}
                      placeholder="My API Key"
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="api_key">API Key</Label>
                    <Input
                      id="api_key"
                      type="password"
                      value={newKey.api_key}
                      onChange={(e) => setNewKey({ ...newKey, api_key: e.target.value })}
                      placeholder="sk-..."
                    />
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="description">Description (Optional)</Label>
                    <Input
                      id="description"
                      value={newKey.description}
                      onChange={(e) => setNewKey({ ...newKey, description: e.target.value })}
                      placeholder="Description of this API key"
                    />
                  </div>
                  <div className="md:col-span-2 flex gap-2">
                    <Button onClick={saveAPIKey}>
                      <Save className="h-4 w-4 mr-2" />
                      Save API Key
                    </Button>
                    <Button variant="outline" onClick={() => setShowAddForm(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Existing API Keys */}
          {apiKeys.length === 0 ? (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No API keys configured. Add your first API key to enable AI features.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-3">
              {apiKeys.map((key) => {
                const serviceInfo = getServiceInfo(key.service);
                const isVisible = visibleKeys.has(key.id);
                
                return (
                  <Card key={key.id} className="border-l-4 border-l-blue-500">
                    <CardContent className="pt-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">{serviceInfo.label}</Badge>
                            {key.is_active ? (
                              <Badge className="bg-green-100 text-green-800">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Active
                              </Badge>
                            ) : (
                              <Badge variant="destructive">Inactive</Badge>
                            )}
                            {key.name && (
                              <span className="text-sm font-medium">{key.name}</span>
                            )}
                          </div>
                          <div className="text-sm text-muted-foreground mb-2">
                            {key.description || serviceInfo.description}
                          </div>
                          <div className="font-mono text-sm bg-gray-100 p-2 rounded flex items-center gap-2">
                            {isVisible ? key.api_key : maskAPIKey(key.api_key)}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleKeyVisibility(key.id)}
                            >
                              {isVisible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                          <div className="text-xs text-muted-foreground mt-2">
                            Created: {new Date(key.created_at).toLocaleDateString()}
                            {key.usage_count > 0 && ` • Used ${key.usage_count} times`}
                            {key.last_used_at && ` • Last used: ${new Date(key.last_used_at).toLocaleDateString()}`}
                          </div>
                        </div>
                        <div className="ml-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteAPIKey(key.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
