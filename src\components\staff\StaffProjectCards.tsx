import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Clock, 
  Target, 
  Users, 
  Calendar, 
  TrendingUp, 
  Edit3, 
  Eye,
  AlertCircle,
  CheckCircle2,
  PlayCircle,
  PauseCircle,
  XCircle
} from "lucide-react";
import { useMyProjects, useProjectColors, useProjectStats } from "@/hooks/useProjectProgress";
import { ProjectProgressUpdateForm } from "./ProjectProgressUpdateForm";
import { ProjectWithProgress } from "@/lib/project-progress-api";
import { format } from "date-fns";

interface StaffProjectCardsProps {
  showStats?: boolean;
  maxHeight?: string;
}

export const StaffProjectCards: React.FC<StaffProjectCardsProps> = ({ 
  showStats = true, 
  maxHeight = "600px" 
}) => {
  const { data: projects, isLoading, error } = useMyProjects();
  const { getProjectColor, getProgressColor, getStatusColor } = useProjectColors();
  const stats = useProjectStats();
  const [selectedProject, setSelectedProject] = useState<ProjectWithProgress | null>(null);
  const [showUpdateForm, setShowUpdateForm] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle2 className="h-4 w-4" />;
      case 'active': return <PlayCircle className="h-4 w-4" />;
      case 'in_progress': return <PlayCircle className="h-4 w-4" />;
      case 'on_hold': return <PauseCircle className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  const handleUpdateProgress = (project: ProjectWithProgress) => {
    setSelectedProject(project);
    setShowUpdateForm(true);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        {showStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="glassmorphism">
                <CardContent className="p-4">
                  <Skeleton className="h-8 w-full mb-2" />
                  <Skeleton className="h-4 w-20" />
                </CardContent>
              </Card>
            ))}
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="glassmorphism">
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="glassmorphism border-red-200">
        <CardContent className="p-6 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-red-700 mb-2">Failed to Load Projects</h3>
          <p className="text-red-600">{error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Project Statistics */}
      {showStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4" data-aos="fade-up">
          <Card className="glassmorphism border-blue-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Total Projects</p>
                  <p className="text-2xl font-bold text-blue-700">{stats.totalProjects}</p>
                </div>
                <Target className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-green-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Active Projects</p>
                  <p className="text-2xl font-bold text-green-700">{stats.activeProjects}</p>
                </div>
                <PlayCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-purple-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Avg Progress</p>
                  <p className="text-2xl font-bold text-purple-700">{stats.averageProgress}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card className="glassmorphism border-orange-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-orange-600">Hours Worked</p>
                  <p className="text-2xl font-bold text-orange-700">{stats.totalHoursWorked}</p>
                </div>
                <Clock className="h-8 w-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Project Cards */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold modern-heading">My Projects</h2>
          <Badge variant="outline" className="text-sm">
            {projects?.length || 0} Projects
          </Badge>
        </div>

        {!projects || projects.length === 0 ? (
          <Card className="glassmorphism">
            <CardContent className="p-8 text-center">
              <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No Projects Assigned</h3>
              <p className="text-gray-500">You don't have any projects assigned yet. Check back later or contact your manager.</p>
            </CardContent>
          </Card>
        ) : (
          <ScrollArea style={{ maxHeight }} className="pr-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {projects.map((project, index) => {
                const assignment = project.user_assignment || project.assignments?.[0];
                const progress = assignment?.progress_percentage || 0;
                const hoursWorked = assignment?.hours_worked || 0;
                const hoursAllocated = assignment?.hours_allocated || 0;
                
                return (
                  <Card 
                    key={project.id} 
                    className="glassmorphism hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border-l-4"
                    style={{ borderLeftColor: getProjectColor(project).replace('bg-', '#') }}
                    data-aos="fade-up"
                    data-aos-delay={index * 100}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-lg font-bold mb-2 line-clamp-2">
                            {project.name}
                          </CardTitle>
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={`${getStatusColor(project.status)} text-xs`}>
                              {getStatusIcon(project.status)}
                              <span className="ml-1 capitalize">{project.status}</span>
                            </Badge>
                            {assignment?.role && (
                              <Badge variant="outline" className="text-xs">
                                {assignment.role.replace('_', ' ')}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      {project.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {project.description}
                        </p>
                      )}
                    </CardHeader>

                    <CardContent className="space-y-4">
                      {/* Progress Section */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="font-medium">Progress</span>
                          <span className={`font-bold ${getProgressColor(progress)}`}>
                            {progress}%
                          </span>
                        </div>
                        <Progress 
                          value={progress} 
                          className="h-2 bg-gray-200"
                        />
                      </div>

                      {/* Project Details */}
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="font-medium">{hoursWorked}h</p>
                            <p className="text-xs text-muted-foreground">of {hoursAllocated}h</p>
                          </div>
                        </div>
                        
                        {project.end_date && (
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <div>
                              <p className="font-medium text-xs">Due</p>
                              <p className="text-xs text-muted-foreground">
                                {format(new Date(project.end_date), 'MMM dd')}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Team Info */}
                      {project.team_size > 0 && (
                        <div className="flex items-center gap-2 text-sm">
                          <Users className="h-4 w-4 text-gray-500" />
                          <span>{project.team_size} team member{project.team_size !== 1 ? 's' : ''}</span>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex-1"
                          onClick={() => handleUpdateProgress(project)}
                        >
                          <Edit3 className="h-4 w-4 mr-1" />
                          Update
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            // Navigate to project details
                            console.log('View project details:', project.id);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Progress Update Form Modal */}
      {selectedProject && (
        <ProjectProgressUpdateForm
          project={selectedProject}
          assignment={selectedProject.user_assignment || selectedProject.assignments?.[0]}
          open={showUpdateForm}
          onOpenChange={setShowUpdateForm}
          onSuccess={() => {
            setShowUpdateForm(false);
            setSelectedProject(null);
          }}
        />
      )}
    </div>
  );
};
