import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { 
  Upload, 
  File, 
  X, 
  CheckCircle, 
  AlertCircle, 
  FileText,
  Image,
  Video,
  Music,
  Archive,
  FileSpreadsheet
} from "lucide-react";

interface FileUploadProps {
  uploadType: 'report_attachment' | 'memo_attachment' | 'profile_image' | 'project_file' | 'general_document';
  entityType?: 'report' | 'memo' | 'project' | 'task' | 'user_profile' | 'department' | 'expense_report' | 'invoice';
  entityId?: string;
  maxFiles?: number;
  maxSize?: number; // in bytes
  acceptedTypes?: string[];
  onUploadComplete?: (files: UploadedFile[]) => void;
  onUploadError?: (error: string) => void;
  className?: string;
}

interface UploadedFile {
  id: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  file_path: string;
  upload_status: string;
}

interface FileWithProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  id?: string;
  error?: string;
}

export const FileUploadComponent: React.FC<FileUploadProps> = ({
  uploadType,
  entityType,
  entityId,
  maxFiles = 5,
  maxSize = 10 * 1024 * 1024, // 10MB default
  acceptedTypes = ['image/*', 'application/pdf', '.doc,.docx,.txt,.xlsx,.xls,.ppt,.pptx'],
  onUploadComplete,
  onUploadError,
  className = ''
}) => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [uploadingFiles, setUploadingFiles] = useState<FileWithProgress[]>([]);
  const [completedFiles, setCompletedFiles] = useState<UploadedFile[]>([]);

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) return <Image className="h-4 w-4" />;
    if (mimeType.startsWith('video/')) return <Video className="h-4 w-4" />;
    if (mimeType.startsWith('audio/')) return <Music className="h-4 w-4" />;
    if (mimeType.includes('pdf')) return <FileText className="h-4 w-4" />;
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return <FileSpreadsheet className="h-4 w-4" />;
    if (mimeType.includes('zip') || mimeType.includes('rar')) return <Archive className="h-4 w-4" />;
    return <File className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const uploadFile = async (file: File): Promise<UploadedFile> => {
    if (!userProfile?.id) {
      throw new Error('User not authenticated');
    }

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const storedFilename = `${timestamp}_${randomString}.${fileExtension}`;
    const filePath = `${uploadType}/${storedFilename}`;

    // Upload to Supabase Storage
    const { data: storageData, error: storageError } = await supabase.storage
      .from('documents')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });

    if (storageError) {
      throw new Error(`Storage upload failed: ${storageError.message}`);
    }

    // Create file record in database
    const { data: fileData, error: dbError } = await supabase
      .from('file_uploads')
      .insert([{
        original_filename: file.name,
        stored_filename: storedFilename,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        file_extension: fileExtension,
        upload_type: uploadType,
        uploaded_by: userProfile.id,
        upload_status: 'uploaded',
        file_hash: await generateFileHash(file)
      }])
      .select()
      .single();

    if (dbError) {
      // Clean up storage if database insert fails
      await supabase.storage.from('documents').remove([filePath]);
      throw new Error(`Database insert failed: ${dbError.message}`);
    }

    // Create attachment record if entity info provided
    if (entityType && entityId) {
      const { error: attachmentError } = await supabase
        .from('document_attachments')
        .insert([{
          file_upload_id: fileData.id,
          entity_type: entityType,
          entity_id: entityId,
          attachment_type: 'supporting_document',
          created_by: userProfile.id
        }]);

      if (attachmentError) {
        console.error('Failed to create attachment record:', attachmentError);
        // Don't fail the upload, just log the error
      }
    }

    return fileData;
  };

  const generateFileHash = async (file: File): Promise<string> => {
    const buffer = await file.arrayBuffer();
    const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  };

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (acceptedFiles.length + completedFiles.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `Maximum ${maxFiles} files allowed`,
        variant: "destructive",
      });
      return;
    }

    const newFiles: FileWithProgress[] = acceptedFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const
    }));

    setUploadingFiles(prev => [...prev, ...newFiles]);

    // Upload files one by one
    for (let i = 0; i < newFiles.length; i++) {
      const fileWithProgress = newFiles[i];
      
      try {
        // Simulate progress updates
        const progressInterval = setInterval(() => {
          setUploadingFiles(prev => 
            prev.map(f => 
              f.file === fileWithProgress.file 
                ? { ...f, progress: Math.min(f.progress + 10, 90) }
                : f
            )
          );
        }, 200);

        const uploadedFile = await uploadFile(fileWithProgress.file);

        clearInterval(progressInterval);

        // Mark as completed
        setUploadingFiles(prev => 
          prev.map(f => 
            f.file === fileWithProgress.file 
              ? { ...f, progress: 100, status: 'completed', id: uploadedFile.id }
              : f
          )
        );

        setCompletedFiles(prev => [...prev, uploadedFile]);

        toast({
          title: "File uploaded successfully",
          description: `${fileWithProgress.file.name} has been uploaded`,
        });

      } catch (error: any) {
        setUploadingFiles(prev => 
          prev.map(f => 
            f.file === fileWithProgress.file 
              ? { ...f, status: 'error', error: error.message }
              : f
          )
        );

        toast({
          title: "Upload failed",
          description: error.message,
          variant: "destructive",
        });

        onUploadError?.(error.message);
      }
    }

    // Call completion callback
    if (onUploadComplete) {
      setTimeout(() => {
        onUploadComplete(completedFiles);
      }, 1000);
    }
  }, [completedFiles, maxFiles, uploadType, entityType, entityId, userProfile, toast, onUploadComplete, onUploadError]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxSize,
    multiple: maxFiles > 1
  });

  const removeFile = (fileToRemove: FileWithProgress) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== fileToRemove.file));
  };

  const removeCompletedFile = async (fileToRemove: UploadedFile) => {
    try {
      // Delete from storage
      await supabase.storage.from('documents').remove([fileToRemove.file_path]);
      
      // Delete from database
      await supabase.from('file_uploads').delete().eq('id', fileToRemove.id);
      
      setCompletedFiles(prev => prev.filter(f => f.id !== fileToRemove.id));
      
      toast({
        title: "File removed",
        description: `${fileToRemove.original_filename} has been removed`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to remove file",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-gray-300 hover:border-primary hover:bg-gray-50'
            }`}
          >
            <input {...getInputProps()} />
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            {isDragActive ? (
              <p className="text-lg font-medium text-primary">Drop files here...</p>
            ) : (
              <div>
                <p className="text-lg font-medium mb-2">
                  Drag & drop files here, or click to select
                </p>
                <p className="text-sm text-gray-500">
                  Maximum {maxFiles} files, up to {formatFileSize(maxSize)} each
                </p>
                <p className="text-xs text-gray-400 mt-1">
                  Supported: Images, PDF, Documents, Spreadsheets
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Uploading Files */}
      {uploadingFiles.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h4 className="font-medium mb-3">Uploading Files</h4>
            <div className="space-y-3">
              {uploadingFiles.map((fileWithProgress, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {fileWithProgress.status === 'completed' ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : fileWithProgress.status === 'error' ? (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    ) : (
                      getFileIcon(fileWithProgress.file.type)
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {fileWithProgress.file.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(fileWithProgress.file.size)}
                    </p>
                    {fileWithProgress.status === 'uploading' && (
                      <Progress value={fileWithProgress.progress} className="mt-1" />
                    )}
                    {fileWithProgress.status === 'error' && (
                      <p className="text-xs text-red-500 mt-1">
                        {fileWithProgress.error}
                      </p>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(fileWithProgress)}
                    className="flex-shrink-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Completed Files */}
      {completedFiles.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h4 className="font-medium mb-3">Uploaded Files</h4>
            <div className="space-y-2">
              {completedFiles.map((file) => (
                <div key={file.id} className="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm font-medium">{file.original_filename}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(file.file_size)}</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCompletedFile(file)}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
