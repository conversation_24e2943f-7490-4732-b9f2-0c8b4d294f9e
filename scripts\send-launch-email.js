// Simple script to send launch notification emails
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

const recipients = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

async function sendLaunchNotification() {
  try {
    console.log('🚀 Sending CTNL AI WorkBoard launch notification to all recipients...');
    
    const { data, error } = await supabase.functions.invoke('send-notification', {
      body: {
        type: 'system_launch',
        recipients: recipients,
        data: {
          systemName: 'CTNL AI WorkBoard',
          launchDate: new Date().toLocaleDateString(),
          dashboardUrl: 'https://ai.ctnigeria.com',
          loginUrl: 'https://ai.ctnigeria.com/auth',
          userManualUrl: 'https://ai.ctnigeria.com/user-manual.html',
          features: [
            '🤖 AI-Powered Task Assistant & Automation',
            '🕒 Real-Time Time Attendance with GPS Location',
            '📋 Advanced Project Management with Kanban Boards',
            '💰 Comprehensive Financial & Accounting Suite',
            '📱 Fully Mobile-Responsive Design',
            '👥 Multi-Role Access Control (Admin, Manager, Staff, Accountant)',
            '🔔 Real-time Notifications & Email Alerts',
            '📊 Advanced Reporting & Analytics Dashboard',
            '📖 Complete User Manual & Documentation'
          ],
          stats: {
            userRoles: 5,
            databaseTables: 27,
            aiFunctions: '15+',
            mobileReady: '100%'
          }
        }
      }
    });

    if (error) {
      console.error('❌ Error sending notification:', error);
      return;
    }

    console.log('✅ Launch notification sent successfully!');
    console.log('📧 Recipients:', recipients.length);
    console.log('📋 Response:', data);
    
    console.log('\n🎉 CTNL AI WorkBoard Launch Notification Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📧 Email Recipients:');
    recipients.forEach((email, index) => {
      console.log(`   ${index + 1}. ${email}`);
    });
    console.log('\n🔗 Important Links:');
    console.log('   • Website: https://ai.ctnigeria.com');
    console.log('   • Login: https://ai.ctnigeria.com/auth');
    console.log('   • User Manual: https://ai.ctnigeria.com/user-manual.html');
    console.log('\n✨ The team has been notified that CTNL AI WorkBoard is ready for use!');
    
  } catch (error) {
    console.error('❌ Failed to send launch notification:', error);
  }
}

// Run the notification
sendLaunchNotification();
