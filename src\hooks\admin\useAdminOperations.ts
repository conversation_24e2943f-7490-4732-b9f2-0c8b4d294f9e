
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

interface AdminDashboardStats {
  totalUsers: number;
  activeUsers: number;
  pendingTasks: number;
  completedTasks: number;
}

interface SystemActivity {
  id: string;
  type: string;
  description: string;
  user_id: string | null;
  metadata: any | null;
  created_at: string;
  user?: {
    full_name: string;
  };
}

interface DepartmentWithManager {
  id: string;
  name: string;
  description: string | null;
  manager_id: string | null;
  employee_count: number | null;
  created_at: string;
  updated_at: string;
  profiles?: {
    full_name: string;
  }[];
}

export const useAdminOperations = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const useSystemActivities = () => {
    return useQuery({
      queryKey: ["system-activities"],
      queryFn: async () => {
        const { data, error } = await supabase
          .from("system_activities")
          .select("*, profiles!system_activities_user_id_fkey(full_name)")
          .order("created_at", { ascending: false });

        if (error) throw error;
        return data as unknown as SystemActivity[];
      },
    });
  };

  const useDepartments = () => {
    return useQuery({
      queryKey: ["departments"],
      queryFn: async () => {
        const { data, error } = await supabase
          .from("departments")
          .select(`
            *,
            manager:profiles!departments_manager_id_fkey(full_name)
          `);

        if (error) throw error;
        return data as DepartmentWithManager[];
      },
    });
  };

  const useAdminStats = () => {
    return useQuery({
      queryKey: ["admin-stats"],
      queryFn: async () => {
        const stats: AdminDashboardStats = {
          totalUsers: 0,
          activeUsers: 0,
          pendingTasks: 0,
          completedTasks: 0,
        };

        try {
          // Fetch total users
          const { count: totalUsers } = await supabase
            .from("profiles")
            .select("id", { count: "exact", head: true });

          // Fetch pending tasks
          const { count: pendingTasks } = await supabase
            .from("tasks")
            .select("id", { count: "exact", head: true })
            .eq("status", "pending");

          // Fetch completed tasks
          const { count: completedTasks } = await supabase
            .from("tasks")
            .select("id", { count: "exact", head: true })
            .eq("status", "completed");

          stats.totalUsers = totalUsers || 0;
          stats.pendingTasks = pendingTasks || 0;
          stats.completedTasks = completedTasks || 0;
        } catch (error) {
          console.error('Error fetching admin stats:', error);
        }

        return stats;
      },
      retry: 1,
      staleTime: 30000,
    });
  };

  const updateDepartment = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<DepartmentWithManager> }) => {
      const updateData = {
        name: data.name,
        description: data.description,
        manager_id: data.manager_id,
        employee_count: data.employee_count,
      };

      const { error } = await supabase
        .from("departments")
        .update(updateData)
        .eq("id", id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["departments"] });
      toast({
        title: "Department Updated",
        description: "The department has been successfully updated.",
      });
    },
  });

  return {
    useSystemActivities,
    useDepartments,
    useAdminStats,
    updateDepartment,
  };
};
