/**
 * Voice Assistant Component
 * Provides voice-guided navigation and assistance
 */

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Mic, 
  MicOff, 
  Volume2, 
  VolumeX, 
  HelpCircle, 
  MessageSquare,
  Settings,
  Play,
  Pause
} from "lucide-react";
import { voiceCommandSystem } from "@/lib/voice-command-system";
import { enhancedVoiceSystem } from "@/lib/enhanced-voice-system";
import { useAuth } from "@/components/auth/AuthProvider";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "react-router-dom";

interface VoiceMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export const VoiceAssistant = () => {
  const [isListening, setIsListening] = useState(false);
  const [isSupported, setIsSupported] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<VoiceMessage[]>([]);
  const [showInterface, setShowInterface] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const location = useLocation();

  useEffect(() => {
    // Check if voice commands are supported
    setIsSupported(voiceCommandSystem.isSupported());
    
    // Set context based on current page and user role
    const currentPage = location.pathname.split('/').pop() || 'dashboard';
    voiceCommandSystem.setContext(currentPage, userProfile?.role || 'staff');

    // Monitor speech synthesis
    const checkSpeaking = () => {
      setIsSpeaking(window.speechSynthesis.speaking);
    };
    
    const interval = setInterval(checkSpeaking, 100);
    return () => clearInterval(interval);
  }, [location.pathname, userProfile?.role]);

  const startListening = () => {
    if (!isSupported) {
      toast({
        title: "Voice Commands Not Supported",
        description: "Your browser doesn't support voice commands.",
        variant: "destructive",
      });
      return;
    }

    setIsListening(true);
    voiceCommandSystem.startListening();
    
    addMessage('assistant', "I'm listening. How can I help you?");
    
    toast({
      title: "Voice Assistant Active",
      description: "Listening for your commands...",
    });
  };

  const stopListening = () => {
    setIsListening(false);
    voiceCommandSystem.stopListening();
    
    toast({
      title: "Voice Assistant Stopped",
      description: "Voice commands are now disabled.",
    });
  };

  const addMessage = (type: 'user' | 'assistant', content: string) => {
    const message: VoiceMessage = {
      id: `${type}_${Date.now()}`,
      type,
      content,
      timestamp: new Date(),
    };
    
    setMessages(prev => [...prev, message]);
  };

  const speakText = async (text: string) => {
    setIsSpeaking(true);
    addMessage('assistant', text);

    try {
      if (enhancedVoiceSystem.isSupported() && enhancedVoiceSystem.isReady()) {
        await enhancedVoiceSystem.speakWithPersonality(text, 'professional');
      } else {
        voiceCommandSystem.speak(text, { personality: 'professional' });
      }
    } catch (error) {
      console.error('Voice synthesis failed:', error);
    } finally {
      setIsSpeaking(false);
    }
  };

  const introduceSystem = () => {
    voiceCommandSystem.introduceSystem();
    addMessage('assistant', 'Welcome to CTNL AI Workboard! I can help you navigate and use the system with voice commands.');
  };

  const showHelp = () => {
    const helpText = `Welcome to your voice-controlled CTNL AI Workboard! Here's what I can help you with:

    Navigation Commands:
    - "Go to dashboard" or "Home" - Return to main dashboard
    - "Go to projects" - View and manage your projects
    - "Go to time management" - Track your time and logs
    - "Go to reports" - View analytics and reports
    - "Go to AI" - Access AI assistant features
    - "Go to settings" - Manage your preferences
    - "Go to profile" - View your account details

    Work Actions:
    - "Clock in" or "Start work" - Begin your work day
    - "Clock out" or "End work" - Finish your work day
    - "Show my projects" - List your current projects
    - "Create project" - Start a new project
    - "Show status" - Check your current work status

    System Control:
    - "Search" - Find anything in the system
    - "Refresh" or "Reload" - Refresh the current page
    - "Go back" - Return to previous page
    - "Help" - Show this help again
    - "Stop listening" - Disable voice commands

    Just speak naturally! My maker Ifeanyi took his time to develop me so,  I understand conversational language and I'm here to make your work easier.`;

    speakText(helpText);
  };

  const quickCommands = [
    { label: "🏠 Dashboard", command: "go to dashboard" },
    { label: "⏰ Clock In", command: "clock in" },
    { label: "📊 Projects", command: "go to projects" },
    { label: "🤖 AI Assistant", command: "go to ai" },
    { label: "📈 Reports", command: "go to reports" },
    { label: "⚙️ Settings", command: "go to settings" },
    { label: "❓ Help", command: "help" },
    { label: "🔍 Search", command: "search" },
  ];

  if (!isSupported) {
    return (
      <Card className="w-80">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MicOff className="h-5 w-5 text-muted-foreground" />
            Voice Assistant
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Voice commands are not supported in your browser. Please use a modern browser like Chrome or Edge.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Floating Voice Button */}
      {!showInterface && (
        <div className="relative">
          <Button
            onClick={() => setShowInterface(true)}
            className="rounded-full w-14 h-14 shadow-lg hover:shadow-xl transition-all duration-300 bg-red-600 hover:bg-red-700 text-white border-2 border-red-500"
            variant={isListening ? "default" : "secondary"}
          >
            {isListening ? (
              <Mic className="h-6 w-6 animate-pulse text-white" />
            ) : (
              <Mic className="h-6 w-6 text-white" />
            )}
          </Button>
          
          {isListening && (
            <div className="absolute -top-2 -right-2">
              <div className="w-4 h-4 bg-red-500 rounded-full animate-pulse shadow-lg"></div>
            </div>
          )}

          {/* Pulse animation when listening */}
          {isListening && (
            <div className="absolute inset-0 rounded-full bg-red-500 opacity-30 animate-ping"></div>
          )}
        </div>
      )}

      {/* Voice Assistant Interface */}
      {showInterface && (
        <Card className="w-96 max-h-96 shadow-xl border-red-200 bg-white dark:bg-gray-900">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Mic className="h-5 w-5 text-red-600" />
                Voice Assistant
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge
                  variant={isListening ? "default" : "secondary"}
                  className={isListening ? "bg-red-600 text-white animate-pulse" : ""}
                >
                  {isListening ? "🎤 Listening" : "💤 Idle"}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowInterface(false)}
                  className="hover:bg-red-50"
                >
                  ×
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Status */}
            <div className="flex items-center gap-2 text-sm">
              <div className={`w-2 h-2 rounded-full ${isListening ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`}></div>
              <span className="text-muted-foreground">
                {isListening ? '🎤 Ready for voice commands' : '💤 Click to activate voice control'}
              </span>
              {isSpeaking && (
                <Badge variant="outline" className="ml-auto border-red-200 text-red-700">
                  <Volume2 className="h-3 w-3 mr-1" />
                  Speaking
                </Badge>
              )}
            </div>

            {/* Control Buttons */}
            <div className="grid grid-cols-2 gap-2">
              {!isListening ? (
                <Button
                  onClick={startListening}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white"
                >
                  <Mic className="h-4 w-4" />
                  Start Listening
                </Button>
              ) : (
                <Button
                  onClick={stopListening}
                  variant="destructive"
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700 animate-pulse"
                >
                  <MicOff className="h-4 w-4" />
                  Stop Listening
                </Button>
              )}

              <Button
                onClick={showHelp}
                variant="outline"
                className="flex items-center gap-2 border-red-200 text-red-700 hover:bg-red-50"
              >
                <HelpCircle className="h-4 w-4" />
                Help
              </Button>
            </div>

            {/* Quick Commands */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Quick Commands:</h4>
              <div className="grid grid-cols-2 gap-1">
                {quickCommands.map((cmd, index) => (
                  <Button
                    key={index}
                    variant="ghost"
                    size="sm"
                    onClick={() => speakText(`Executing: ${cmd.command}`)}
                    className="text-xs h-8"
                  >
                    {cmd.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Recent Messages */}
            {messages.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Recent:</h4>
                <ScrollArea className="h-24">
                  <div className="space-y-1">
                    {messages.slice(-3).map((message) => (
                      <div
                        key={message.id}
                        className={`text-xs p-2 rounded ${
                          message.type === 'user'
                            ? 'bg-primary/10 text-primary'
                            : 'bg-muted text-muted-foreground'
                        }`}
                      >
                        <div className="flex items-center gap-1 mb-1">
                          {message.type === 'user' ? (
                            <Mic className="h-3 w-3" />
                          ) : (
                            <Volume2 className="h-3 w-3" />
                          )}
                          <span className="font-medium capitalize">{message.type}</span>
                        </div>
                        <p>{message.content}</p>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            )}

            {/* Introduction Button */}
            <Button
              onClick={introduceSystem}
              variant="outline"
              size="sm"
              className="w-full text-xs"
            >
              <Play className="h-3 w-3 mr-1" />
              System Introduction
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
