-- Comprehensive Foreign Key Constraint Fix
-- This script fixes all foreign key constraint issues related to user deletions

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- STEP 1: Identify all tables with foreign key references to auth.users
DO $$
DECLARE
    rec RECORD;
    sql_statement TEXT;
BEGIN
    -- Get all foreign key constraints that reference auth.users
    FOR rec IN 
        SELECT 
            tc.table_schema,
            tc.table_name,
            kcu.column_name,
            tc.constraint_name
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
            AND ccu.table_name = 'users'
            AND tc.table_schema = 'public'
    LOOP
        RAISE NOTICE 'Found foreign key: %.% (%) -> auth.users', 
            rec.table_schema, rec.table_name, rec.column_name;
    END LOOP;
END $$;

-- STEP 2: Fix document_analysis table specifically
-- Check if document_analysis table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'document_analysis') THEN
        
        -- Option A: Update all created_by to NULL (safest option)
        UPDATE public.document_analysis SET created_by = NULL;
        RAISE NOTICE 'Updated document_analysis.created_by to NULL for all records';
        
        -- Option B: Delete orphaned records (uncomment if needed)
        -- DELETE FROM public.document_analysis 
        -- WHERE created_by NOT IN (SELECT id FROM auth.users);
        
    ELSE
        RAISE NOTICE 'document_analysis table does not exist';
    END IF;
END $$;

-- STEP 3: Create document_analysis table with proper constraints if it doesn't exist
CREATE TABLE IF NOT EXISTS public.document_analysis (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_path TEXT,
    file_name TEXT NOT NULL,
    file_type TEXT,
    file_size BIGINT,
    analysis_status TEXT DEFAULT 'pending',
    analysis_result JSONB,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 4: Fix other common tables that might have similar issues
-- Fix conversation_history table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'conversation_history') THEN
        UPDATE public.conversation_history SET user_id = NULL 
        WHERE user_id NOT IN (SELECT id FROM auth.users);
        RAISE NOTICE 'Fixed conversation_history foreign key references';
    END IF;
END $$;

-- Fix conversation_analytics table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'conversation_analytics') THEN
        UPDATE public.conversation_analytics SET user_id = NULL 
        WHERE user_id NOT IN (SELECT id FROM auth.users);
        RAISE NOTICE 'Fixed conversation_analytics foreign key references';
    END IF;
END $$;

-- Fix ai_results table
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables 
               WHERE table_schema = 'public' 
               AND table_name = 'ai_results') THEN
        UPDATE public.ai_results SET created_by = NULL 
        WHERE created_by NOT IN (SELECT id FROM auth.users);
        RAISE NOTICE 'Fixed ai_results foreign key references';
    END IF;
END $$;

-- STEP 5: Update foreign key constraints to use CASCADE or SET NULL
-- This prevents future constraint violations

-- Drop and recreate document_analysis constraint
DO $$
BEGIN
    -- Drop existing constraint if it exists
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'document_analysis_created_by_fkey' 
               AND table_name = 'document_analysis' 
               AND table_schema = 'public') THEN
        ALTER TABLE public.document_analysis 
        DROP CONSTRAINT document_analysis_created_by_fkey;
    END IF;
    
    -- Add new constraint with SET NULL on delete
    ALTER TABLE public.document_analysis 
    ADD CONSTRAINT document_analysis_created_by_fkey 
    FOREIGN KEY (created_by) 
    REFERENCES auth.users(id) 
    ON DELETE SET NULL;
    
    RAISE NOTICE 'Updated document_analysis foreign key constraint to SET NULL on delete';
END $$;

-- STEP 6: Create a function to safely delete users
CREATE OR REPLACE FUNCTION public.safe_delete_user(user_id_to_delete UUID)
RETURNS BOOLEAN AS $$
DECLARE
    tables_to_clean TEXT[] := ARRAY[
        'document_analysis',
        'conversation_history', 
        'conversation_analytics',
        'ai_results',
        'time_logs',
        'notifications'
    ];
    table_name TEXT;
    sql_statement TEXT;
    affected_rows INTEGER;
BEGIN
    -- Clean up related records in each table
    FOREACH table_name IN ARRAY tables_to_clean
    LOOP
        -- Check if table exists
        IF EXISTS (SELECT 1 FROM information_schema.tables 
                   WHERE table_schema = 'public' 
                   AND table_name = table_name) THEN
            
            -- Try different column names that might reference users
            IF EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_schema = 'public' 
                       AND table_name = table_name 
                       AND column_name = 'created_by') THEN
                sql_statement := format('UPDATE public.%I SET created_by = NULL WHERE created_by = $1', table_name);
                EXECUTE sql_statement USING user_id_to_delete;
                GET DIAGNOSTICS affected_rows = ROW_COUNT;
                RAISE NOTICE 'Updated % records in %.created_by', affected_rows, table_name;
            END IF;
            
            IF EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_schema = 'public' 
                       AND table_name = table_name 
                       AND column_name = 'user_id') THEN
                sql_statement := format('UPDATE public.%I SET user_id = NULL WHERE user_id = $1', table_name);
                EXECUTE sql_statement USING user_id_to_delete;
                GET DIAGNOSTICS affected_rows = ROW_COUNT;
                RAISE NOTICE 'Updated % records in %.user_id', affected_rows, table_name;
            END IF;
        END IF;
    END LOOP;
    
    -- Now safely delete the user
    DELETE FROM auth.users WHERE id = user_id_to_delete;
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    IF affected_rows > 0 THEN
        RAISE NOTICE 'Successfully deleted user %', user_id_to_delete;
        RETURN TRUE;
    ELSE
        RAISE NOTICE 'User % not found or already deleted', user_id_to_delete;
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- STEP 7: Verification queries
-- Check for any remaining constraint violations
SELECT 'document_analysis' as table_name, COUNT(*) as orphaned_records
FROM public.document_analysis da
LEFT JOIN auth.users u ON da.created_by = u.id
WHERE da.created_by IS NOT NULL AND u.id IS NULL

UNION ALL

SELECT 'conversation_history' as table_name, COUNT(*) as orphaned_records
FROM public.conversation_history ch
LEFT JOIN auth.users u ON ch.user_id = u.id
WHERE ch.user_id IS NOT NULL AND u.id IS NULL

UNION ALL

SELECT 'conversation_analytics' as table_name, COUNT(*) as orphaned_records
FROM public.conversation_analytics ca
LEFT JOIN auth.users u ON ca.user_id = u.id
WHERE ca.user_id IS NOT NULL AND u.id IS NULL;

-- Show current foreign key constraints
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name,
    rc.delete_rule
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND ccu.table_name = 'users'
    AND tc.table_schema = 'public'
ORDER BY tc.table_name;
