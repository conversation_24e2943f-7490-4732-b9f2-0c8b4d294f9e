import { Resend } from 'resend';

// Initialize Resend with API key
const resend = new Resend('re_123456789'); // Replace with actual API key

// Email recipients - key stakeholders and users
const recipients = [
  '<EMAIL>', // Primary admin/owner
  // Add more email addresses as needed for different user roles
  // '<EMAIL>',
  // '<EMAIL>',
  // '<EMAIL>'
];

// Enhanced email template with modern design matching the user manual
const createEmailTemplate = () => {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI WorkBoard CT - System Ready</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            padding: 2rem;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            color: #ccc;
            margin-bottom: 0;
        }

        .content {
            padding: 2rem;
        }

        .status-banner {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 2rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .feature-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .feature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.25rem;
        }

        .feature-desc {
            font-size: 0.9rem;
            color: #666;
        }

        .cta-section {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            margin: 2rem 0;
        }

        .cta-button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 0.75rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
            margin: 1rem 0.5rem 0 0.5rem;
            transition: all 0.3s ease;
        }

        .cta-button:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin: 2rem 0;
            text-align: center;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #dc2626;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .footer {
            background: #f8f9fa;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .footer p {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .footer a {
            color: #dc2626;
            text-decoration: none;
        }

        @media (max-width: 600px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI WorkBoard CT</h1>
            <p>Your AI-Powered Workforce Management System is Ready!</p>
        </div>

        <div class="content">
            <div class="status-banner">
                🚀 System Status: FULLY OPERATIONAL
            </div>

            <h2 style="color: #333; margin-bottom: 1rem;">Welcome to the Future of Workforce Management!</h2>

            <p style="color: #666; margin-bottom: 1.5rem;">
                We're excited to announce that your AI WorkBoard CT system is now fully operational and ready for use.
                This comprehensive platform brings together advanced AI capabilities, real-time tracking, and intelligent
                automation to revolutionize your workforce management.
            </p>

            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">5</span>
                    <div class="stat-label">User Roles</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">27</span>
                    <div class="stat-label">Database Tables</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">15+</span>
                    <div class="stat-label">AI Functions</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">Mobile Ready</div>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-item">
                    <span class="feature-icon">🤖</span>
                    <div class="feature-title">AI Assistant</div>
                    <div class="feature-desc">Advanced AI for document analysis and automation</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⏰</span>
                    <div class="feature-title">Time Tracking</div>
                    <div class="feature-desc">GPS-enabled clock-in/out with device tracking</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <div class="feature-title">Project Management</div>
                    <div class="feature-desc">Kanban boards with progress monitoring</div>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">💰</span>
                    <div class="feature-title">Financial Suite</div>
                    <div class="feature-desc">Complete invoicing and expense management</div>
                </div>
            </div>

            <div class="cta-section">
                <h3 style="margin-bottom: 1rem;">Ready to Get Started?</h3>
                <p style="margin-bottom: 1.5rem; opacity: 0.9;">
                    Access your personalized dashboard and explore the comprehensive user manual to maximize your productivity.
                </p>
                <a href="https://ai.ctnigeria.com" class="cta-button">🚀 Launch Dashboard</a>
                <a href="https://ai.ctnigeria.com/user-manual.html" class="cta-button">📖 View User Manual</a>
            </div>

            <h3 style="color: #333; margin: 2rem 0 1rem 0;">What's Included:</h3>
            <ul style="color: #666; padding-left: 1.5rem; margin-bottom: 2rem;">
                <li>✅ <strong>Role-Based Access:</strong> Admin, Manager, Accountant, Staff-Admin, and Staff portals</li>
                <li>✅ <strong>Real-Time Notifications:</strong> Email and in-app alerts with customizable preferences</li>
                <li>✅ <strong>Mobile Responsive:</strong> Full functionality across all devices</li>
                <li>✅ <strong>Advanced Security:</strong> Role-based permissions with audit trails</li>
                <li>✅ <strong>AI Integration:</strong> Intelligent document processing and task automation</li>
                <li>✅ <strong>Comprehensive Reporting:</strong> Real-time analytics and insights</li>
            </ul>

            <div style="background: #e3f2fd; border: 1px solid #bbdefb; border-radius: 8px; padding: 1.5rem; margin: 2rem 0;">
                <h4 style="color: #1565c0; margin-bottom: 0.5rem;">🔐 Your Access Information:</h4>
                <p style="color: #1976d2; margin-bottom: 0.5rem;">
                    <strong>Website:</strong> <a href="https://ai.ctnigeria.com" style="color: #1976d2;">ai.ctnigeria.com</a>
                </p>
                <p style="color: #1976d2; margin-bottom: 0;">
                    <strong>User Manual:</strong> <a href="https://ai.ctnigeria.com/user-manual.html" style="color: #1976d2;">Complete User Guide</a>
                </p>
            </div>
        </div>

        <div class="footer">
            <p><strong>Need Support?</strong></p>
            <p>Contact our team at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin-top: 1rem; font-size: 0.8rem; color: #999;">
                © 2024 AI WorkBoard CT. Powered by advanced AI technology.
            </p>
        </div>
    </div>
</body>
</html>
  `;
};

async function sendUserManualNotification() {
  try {
    console.log('🚀 Sending AI WorkBoard CT launch notifications...');

    const emailTemplate = createEmailTemplate();

    // Send email to all recipients
    const emailResult = await resend.emails.send({
      from: 'AI WorkBoard CT <<EMAIL>>',
      to: recipients,
      subject: '🚀 AI WorkBoard CT is Ready - Your Complete Workforce Management System',
      html: emailTemplate,
    });

    if (emailResult.error) {
      throw new Error(`Email sending failed: ${emailResult.error.message}`);
    }

    console.log('✅ Launch notification sent successfully!');
    console.log(`📧 Email ID: ${emailResult.data?.id}`);
    console.log(`👥 Recipients: ${recipients.length}`);
    console.log(`📋 Recipients list: ${recipients.join(', ')}`);

    return {
      success: true,
      emailId: emailResult.data?.id,
      recipients: recipients.length,
      message: 'AI WorkBoard CT launch notification sent successfully!'
    };

  } catch (error) {
    console.error('❌ Failed to send launch notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute the notification sending
if (import.meta.main) {
  sendUserManualNotification()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 SUCCESS: AI WorkBoard CT launch notification completed!');
        console.log(`📊 Summary: ${result.recipients} recipients notified`);
        console.log('🔗 Users can now access: https://ai.ctnigeria.com');
        console.log('📖 User Manual: https://ai.ctnigeria.com/user-manual.html');
      } else {
        console.log('\n❌ FAILED: Launch notification could not be sent');
        console.log(`Error: ${result.error}`);
      }
    })
    .catch(error => {
      console.error('\n💥 CRITICAL ERROR:', error);
    });
}

export { sendUserManualNotification };