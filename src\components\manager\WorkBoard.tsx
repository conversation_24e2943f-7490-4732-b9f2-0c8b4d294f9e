import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Loader2, Plus, Users, TrendingUp, AlertCircle, RefreshCw, Calendar, MapPin, DollarSign, Eye, Edit } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useNavigate } from "react-router-dom";
import { ProjectAPI } from "@/lib/project-api";

interface Project {
  id: string;
  name: string;
  progress_percentage?: number;
  status: string;
  description?: string;
  manager_id?: string;
  created_at: string;
  updated_at: string;
}

export const WorkBoard = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [retryCount, setRetryCount] = useState(0);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [showQuickPreview, setShowQuickPreview] = useState(false);

  // Fetch real projects data with error handling
  const { 
    data: projects = [], 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['projects', retryCount],
    queryFn: async () => {
      try {
        const response = await api.projects.getAll();
        if (response.success && response.data) {
          return response.data as Project[];
        } else {
          throw new Error(response.error?.message || 'Failed to load projects');
        }
      } catch (error: any) {
        console.error('Error loading projects:', error);
        throw error;
      }
    },
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error: any) => {
      if (retryCount === 0) {
        toast({
          title: "Failed to Load Projects",
          description: "Unable to load project data. Using fallback data.",
          variant: "destructive",
        });
      }
    }
  });

  // Mutation for quick status updates
  const statusUpdateMutation = useMutation({
    mutationFn: async ({ projectId, status }: { projectId: string; status: string }) => {
      const response = await ProjectAPI.updateProject(projectId, { status });
      if (!response.success) {
        throw new Error(response.error?.message || 'Failed to update project status');
      }
      return response.data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['projects'] });
      toast({
        title: "Status Updated",
        description: `Project status updated to ${variables.status}`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update project status",
        variant: "destructive",
      });
    }
  });

  // Quick status update handler
  const handleQuickStatusUpdate = (projectId: string, status: string) => {
    statusUpdateMutation.mutate({ projectId, status });
  };

  // Quick preview handler
  const handleQuickPreview = (project: Project) => {
    setSelectedProject(project);
    setShowQuickPreview(true);
  };

  // Fetch team members count for projects
  const { data: teamCounts = {} } = useQuery({
    queryKey: ['project-team-counts'],
    queryFn: async () => {
      try {
        const response = await api.tasks.getAll({});
        if (response.success && response.data) {
          // Count unique team members per project
          const counts: Record<string, number> = {};
          response.data.forEach((task: any) => {
            if (task.project_id && task.assigned_to_id) {
              if (!counts[task.project_id]) {
                counts[task.project_id] = new Set();
              }
              (counts[task.project_id] as any).add(task.assigned_to_id);
            }
          });
          
          // Convert sets to counts
          Object.keys(counts).forEach(projectId => {
            counts[projectId] = (counts[projectId] as any).size;
          });
          
          return counts;
        }
        return {};
      } catch (error) {
        console.warn('Could not load team counts:', error);
        return {};
      }
    },
    enabled: projects.length > 0
  });

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    refetch();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500/20 text-green-700';
      case 'in_progress':
      case 'active':
        return 'bg-blue-500/20 text-blue-700';
      case 'planning':
        return 'bg-yellow-500/20 text-yellow-700';
      case 'on_hold':
      case 'paused':
        return 'bg-orange-500/20 text-orange-700';
      case 'cancelled':
        return 'bg-red-500/20 text-red-700';
      default:
        return 'bg-gray-500/20 text-gray-700';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    if (progress >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Error state
  if (error && !projects.length) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold">Failed to Load Projects</h3>
            <p className="text-sm text-muted-foreground max-w-md">
              {error.message || "There was a problem loading your projects. Please try again."}
            </p>
          </div>
          <Button onClick={handleRetry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (isLoading && !projects.length) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading projects...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Project Workboard</h2>
          <p className="text-muted-foreground">
            Manage and track your team's projects
          </p>
        </div>
        <Button
          className="flex items-center gap-2"
          onClick={() => navigate('/dashboard/manager/projects')}
        >
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </div>

      {/* Show warning if using fallback data */}
      {error && projects.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Some project data may be outdated. 
            <Button 
              variant="link" 
              size="sm" 
              onClick={handleRetry}
              className="p-0 h-auto ml-1"
            >
              Refresh now
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">No Projects Found</h3>
              <p className="text-sm text-muted-foreground">
                Get started by creating your first project
              </p>
            </div>
            <Button onClick={() => navigate('/dashboard/manager/projects')}>
              <Plus className="h-4 w-4 mr-2" />
              Create Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => {
            const progress = project.progress_percentage || 0;
            const teamSize = teamCounts[project.id] || 0;
            
            return (
              <Card
                key={project.id}
                className="group bg-black/10 dark:bg-white/5 backdrop-blur-lg border-none hover:bg-black/20 dark:hover:bg-white/10 transition-all duration-300 hover:shadow-lg hover:scale-[1.02]"
              >
                <CardHeader className="pb-3 relative overflow-hidden">
                  {/* Background gradient effect */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  <div className="flex items-start justify-between relative z-10">
                    <div className="space-y-2 flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <CardTitle className="text-lg font-semibold truncate">{project.name}</CardTitle>
                        {project.status === 'active' && (
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                        )}
                      </div>
                      {project.description && (
                        <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                          {project.description}
                        </p>
                      )}
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3" />
                        <span>Created {new Date(project.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2 ml-3">
                      <Badge
                        className={`${getStatusColor(project.status)} text-xs border-0 font-medium`}
                      >
                        {project.status.replace('_', ' ')}
                      </Badge>
                      <div className="text-xs text-muted-foreground text-right">
                        ID: {project.id.slice(-6)}
                      </div>
                    </div>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Enhanced Progress Section */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground font-medium">Progress</span>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-lg">{progress}%</span>
                        <Badge variant="outline" className="text-xs">
                          {progress >= 90 ? 'Near Complete' : progress >= 50 ? 'On Track' : 'Starting'}
                        </Badge>
                      </div>
                    </div>
                    <div className="relative">
                      <div className="w-full bg-muted rounded-full h-3 overflow-hidden">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ease-out ${getProgressColor(progress)} relative`}
                          style={{ width: `${progress}%` }}
                        >
                          {/* Progress shine effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
                        </div>
                      </div>
                      {/* Progress milestones */}
                      <div className="flex justify-between mt-1 text-xs text-muted-foreground">
                        <span>0%</span>
                        <span>50%</span>
                        <span>100%</span>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Team & Status Section */}
                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2 text-sm">
                        <div className="p-1.5 bg-primary/10 rounded-full">
                          <Users className="h-3.5 w-3.5 text-primary" />
                        </div>
                        <div>
                          <span className="font-medium">{teamSize}</span>
                          <span className="text-muted-foreground ml-1">
                            {teamSize === 1 ? 'member' : 'members'}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        project.status === 'active' ? 'bg-green-500 animate-pulse' :
                        project.status === 'on_hold' ? 'bg-yellow-500' :
                        project.status === 'completed' ? 'bg-blue-500' :
                        'bg-gray-500'
                      }`} />
                      <span className="text-sm font-medium capitalize">
                        {project.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>

                  {/* Enhanced Quick Actions */}
                  <div className="flex items-center gap-2 pt-3 border-t border-border/50">
                    <div className="flex gap-1 flex-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-8 px-2 transition-all duration-200 ${
                          project.status === 'active'
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                            : 'hover:bg-green-50 hover:text-green-600 dark:hover:bg-green-900/20'
                        }`}
                        onClick={() => handleQuickStatusUpdate(project.id, 'active')}
                        disabled={project.status === 'active' || statusUpdateMutation.isPending}
                        title="Set to Active"
                      >
                        ▶️
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-8 px-2 transition-all duration-200 ${
                          project.status === 'on_hold'
                            ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                            : 'hover:bg-yellow-50 hover:text-yellow-600 dark:hover:bg-yellow-900/20'
                        }`}
                        onClick={() => handleQuickStatusUpdate(project.id, 'on_hold')}
                        disabled={project.status === 'on_hold' || statusUpdateMutation.isPending}
                        title="Put on Hold"
                      >
                        ⏸️
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`h-8 px-2 transition-all duration-200 ${
                          project.status === 'completed'
                            ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                            : 'hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20'
                        }`}
                        onClick={() => handleQuickStatusUpdate(project.id, 'completed')}
                        disabled={project.status === 'completed' || statusUpdateMutation.isPending}
                        title="Mark Complete"
                      >
                        ✅
                      </Button>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-8 px-3 bg-primary/5 hover:bg-primary/10 border-primary/20"
                      onClick={() => navigate(`/dashboard/tasks?project=${project.id}`)}
                    >
                      <span className="text-xs font-medium">Tasks</span>
                    </Button>
                  </div>

                  {/* Enhanced Main Action Buttons */}
                  <div className="flex gap-2 pt-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 bg-white/5 hover:bg-white/10 border-white/20 backdrop-blur-sm"
                      onClick={() => handleQuickPreview(project)}
                    >
                      <Eye className="h-3.5 w-3.5 mr-1.5" />
                      <span className="text-xs font-medium">Quick View</span>
                    </Button>
                    <Button
                      size="sm"
                      className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg"
                      onClick={() => navigate(`/dashboard/manager/projects?project=${project.id}`)}
                    >
                      <Edit className="h-3.5 w-3.5 mr-1.5" />
                      <span className="text-xs font-medium">Manage</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Loading indicator for refresh */}
      {isLoading && projects.length > 0 && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">Refreshing projects...</span>
        </div>
      )}

      {/* Quick Preview Modal */}
      <Dialog open={showQuickPreview} onOpenChange={setShowQuickPreview}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedProject?.name}
              <Badge variant={selectedProject?.status === 'active' ? 'default' : 'secondary'}>
                {selectedProject?.status}
              </Badge>
            </DialogTitle>
          </DialogHeader>

          {selectedProject && (
            <div className="space-y-4">
              {/* Project Info */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span>Created: {new Date(selectedProject.created_at).toLocaleDateString()}</span>
                  </div>
                  {selectedProject.manager_id && (
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span>Manager: {selectedProject.manager_id}</span>
                    </div>
                  )}
                </div>
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    <span>Progress: {selectedProject.progress_percentage || 0}%</span>
                  </div>
                </div>
              </div>

              {/* Description */}
              {selectedProject.description && (
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-sm text-muted-foreground">{selectedProject.description}</p>
                </div>
              )}

              {/* Quick Actions */}
              <div className="flex gap-2 pt-4 border-t">
                <Button
                  onClick={() => {
                    setShowQuickPreview(false);
                    navigate(`/dashboard/manager/projects?project=${selectedProject.id}`);
                  }}
                  className="flex-1"
                >
                  Open Full Details
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowQuickPreview(false);
                    navigate(`/dashboard/tasks?project=${selectedProject.id}`);
                  }}
                  className="flex-1"
                >
                  View Tasks
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};