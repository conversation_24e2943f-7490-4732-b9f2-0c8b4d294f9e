
import { useAuth } from "@/components/auth/AuthProvider";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { FileText, Plus, Receipt } from "lucide-react";
import { useState } from "react";

export const InvoiceManagement = () => {
  const [newInvoice, setNewInvoice] = useState({
    client_name: "",
    amount: "",
    description: "",
    vendor_name: ""
  });
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { userProfile } = useAuth();

  // Fetch memos for invoice creation
  const { data: memos } = useQuery({
    queryKey: ['memos'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memos')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  // Fetch existing invoices
  const { data: invoices } = useQuery({
    queryKey: ['accounts_invoices'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('accounts_invoices')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  // Create invoice mutation - Fixed to include all required fields
  const createInvoice = useMutation({
    mutationFn: async (invoiceData: any) => {
      const { data, error } = await supabase
        .from('accounts_invoices')
        .insert({
          invoice_number: `INV-${Date.now()}`,
          vendor_name: invoiceData.vendor_name || invoiceData.client_name,
          client_name: invoiceData.client_name,
          amount: parseFloat(invoiceData.amount),
          total_amount: parseFloat(invoiceData.amount),
          description: invoiceData.description,
          due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: 'pending',
          created_by: userProfile?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accounts_invoices'] });
      setNewInvoice({ client_name: "", amount: "", description: "", vendor_name: "" });
      toast({
        title: "Success",
        description: "Invoice created successfully",
      });
    },
    onError: (error: any) => {
      console.error('Invoice creation error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create invoice",
        variant: "destructive",
      });
    },
  });

  const handleCreateInvoice = () => {
    if (!newInvoice.client_name || !newInvoice.amount) {
      toast({
        title: "Error",
        description: "Please fill in client name and amount",
        variant: "destructive",
      });
      return;
    }

    createInvoice.mutate(newInvoice);
  };

  const handleCreateFromMemo = async (memo: any) => {
    try {
      await createInvoice.mutateAsync({
        client_name: memo.department || "Client",
        amount: "1000", // Default amount - should be calculated based on memo
        description: `Invoice for memo: ${memo.title}`,
        vendor_name: memo.department || "Client"
      });
    } catch (error) {
      console.error('Error creating invoice from memo:', error);
    }
  };

  return (
    <div className="space-y-6" data-aos="fade-up">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Invoice Management</h2>
          <p className="text-muted-foreground">Create and manage invoices from memos</p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* Create New Invoice */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Receipt className="h-5 w-5 text-primary" />
              Create New Invoice
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="client_name">Client Name</Label>
              <Input
                id="client_name"
                value={newInvoice.client_name}
                onChange={(e) => setNewInvoice({ ...newInvoice, client_name: e.target.value })}
                placeholder="Enter client name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="vendor_name">Vendor Name</Label>
              <Input
                id="vendor_name"
                value={newInvoice.vendor_name}
                onChange={(e) => setNewInvoice({ ...newInvoice, vendor_name: e.target.value })}
                placeholder="Enter vendor name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="amount">Amount (₦)</Label>
              <Input
                id="amount"
                type="number"
                value={newInvoice.amount}
                onChange={(e) => setNewInvoice({ ...newInvoice, amount: e.target.value })}
                placeholder="Enter amount"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={newInvoice.description}
                onChange={(e) => setNewInvoice({ ...newInvoice, description: e.target.value })}
                placeholder="Enter invoice description"
                rows={3}
              />
            </div>
            
            <Button 
              onClick={handleCreateInvoice} 
              disabled={createInvoice.isPending}
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              {createInvoice.isPending ? "Creating..." : "Create Invoice"}
            </Button>
          </CardContent>
        </Card>

        {/* Create from Memos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-primary" />
              Create from Memos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {memos?.map((memo) => (
                <Card key={memo.id} className="p-4 bg-gray-50 dark:bg-gray-800">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">{memo.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {memo.created_at && format(new Date(memo.created_at), 'PPP')}
                      </p>
                      {memo.department && (
                        <p className="text-sm text-blue-600">Department: {memo.department}</p>
                      )}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCreateFromMemo(memo)}
                      disabled={createInvoice.isPending}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Invoice
                    </Button>
                  </div>
                </Card>
              ))}
              
              {!memos?.length && (
                <div className="text-center py-8 text-muted-foreground">
                  No memos found
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Invoices */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Invoices</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {invoices?.slice(0, 5).map((invoice) => (
              <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-medium">{invoice.invoice_number}</h3>
                  <p className="text-sm text-muted-foreground">
                    Client: {invoice.client_name || invoice.vendor_name}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Created: {format(new Date(invoice.created_at), 'PPP')}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold">₦{invoice.amount?.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">
                    Status: {invoice.payment_status}
                  </p>
                </div>
              </div>
            ))}
            
            {!invoices?.length && (
              <div className="text-center py-8 text-muted-foreground">
                No invoices found
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
