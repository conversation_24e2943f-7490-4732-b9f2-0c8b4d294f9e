/**
 * LangChain & Real-time Integration Provider
 * Initializes and manages all enhanced features
 */

import { toast } from '@/hooks/use-toast';
import { useSupabaseAuth } from '@/hooks/useSupabaseAuth';
import { langChainAgent } from '@/lib/langchain/agent-system';
import { langChainConfig } from '@/lib/langchain/config';
import { realtimeService } from '@/lib/realtime/realtime-service';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';

interface LangChainRealtimeContextType {
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  features: {
    langchain: boolean;
    realtime: boolean;
    rag: boolean;
    agents: boolean;
    collaboration: boolean;
  };
  stats: {
    onlineUsers: number;
    totalDocuments: number;
    availableTools: number;
    activeSessions: number;
  };
}

const LangChainRealtimeContext = createContext<LangChainRealtimeContextType | undefined>(undefined);

interface LangChainRealtimeProviderProps {
  children: ReactNode;
  enableLangChain?: boolean;
  enableRealtime?: boolean;
  enableRAG?: boolean;
  enableAgents?: boolean;
  enableCollaboration?: boolean;
}

export function LangChainRealtimeProvider({
  children,
  enableLangChain = true,
  enableRealtime = true,
  enableRAG = true,
  enableAgents = true,
  enableCollaboration = true,
}: LangChainRealtimeProviderProps) {
  const { user } = useSupabaseAuth();
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    onlineUsers: 0,
    totalDocuments: 0,
    availableTools: 0,
    activeSessions: 0,
  });

  const features = {
    langchain: enableLangChain,
    realtime: enableRealtime,
    rag: enableRAG,
    agents: enableAgents,
    collaboration: enableCollaboration,
  };

  useEffect(() => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    const initializeServices = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Initialize LangChain if enabled
        if (enableLangChain) {
          console.log('Initializing LangChain services...');
          
          try {
            await langChainConfig.initialize();
            console.log('✓ LangChain configuration initialized');
          } catch (configError) {
            console.warn('LangChain configuration failed:', configError);
            // Continue without LangChain features
          }

          // Initialize agents if enabled
          if (enableAgents) {
            try {
              await langChainAgent.initialize();
              console.log('✓ LangChain agents initialized');
              setStats(prev => ({ ...prev, availableTools: langChainAgent.getAvailableTools().length }));
            } catch (agentError) {
              console.warn('LangChain agents initialization failed:', agentError);
            }
          }
        }

        // Initialize real-time services if enabled
        if (enableRealtime) {
          console.log('Initializing real-time services...');
          
          try {
            await realtimeService.initializePresence(
              user.id,
              user.user_metadata?.full_name || user.email || 'Unknown User',
              user.user_metadata?.avatar_url
            );
            console.log('✓ Real-time presence initialized');
          } catch (realtimeError) {
            console.warn('Real-time initialization failed:', realtimeError);
          }
        }

        // Set up event listeners for stats updates
        if (enableRealtime) {
          realtimeService.addEventListener('presence_sync', (event) => {
            setStats(prev => ({ ...prev, onlineUsers: event.payload.users.length }));
          });

          realtimeService.addEventListener('session_joined', () => {
            setStats(prev => ({ ...prev, activeSessions: realtimeService.getActiveSessions().length }));
          });

          realtimeService.addEventListener('session_left', () => {
            setStats(prev => ({ ...prev, activeSessions: realtimeService.getActiveSessions().length }));
          });
        }

        // Load initial stats
        await updateStats();

        setIsInitialized(true);
        
        toast({
          title: "Enhanced Features Initialized",
          description: "LangChain and real-time collaboration features are now active.",
        });

      } catch (initError) {
        console.error('Failed to initialize enhanced features:', initError);
        setError(initError instanceof Error ? initError.message : 'Initialization failed');
        
        toast({
          title: "Initialization Warning",
          description: "Some enhanced features may not be available. Basic functionality will continue to work.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializeServices();

    // Cleanup on unmount
    return () => {
      if (enableRealtime) {
        realtimeService.cleanup().catch(console.error);
      }
    };
  }, [user, enableLangChain, enableRealtime, enableRAG, enableAgents, enableCollaboration]);

  // Update stats periodically
  useEffect(() => {
    if (!isInitialized) return;

    const interval = setInterval(updateStats, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [isInitialized]);

  const updateStats = async () => {
    try {
      // Update RAG stats if enabled
      if (enableRAG && enableLangChain) {
        try {
          const { langChainRAG } = await import('@/lib/langchain/rag-system');
          const ragStats = await langChainRAG.getStatistics();
          setStats(prev => ({ ...prev, totalDocuments: ragStats.totalDocuments }));
        } catch (ragError) {
          console.warn('Failed to update RAG stats:', ragError);
        }
      }

      // Update real-time stats if enabled
      if (enableRealtime) {
        const activeSessions = realtimeService.getActiveSessions().length;
        setStats(prev => ({ ...prev, activeSessions }));
      }
    } catch (error) {
      console.warn('Failed to update stats:', error);
    }
  };

  // Handle page visibility changes
  useEffect(() => {
    if (!enableRealtime || !isInitialized) return;

    const handleVisibilityChange = async () => {
      if (document.hidden) {
        await realtimeService.updateStatus('away');
      } else {
        await realtimeService.updateStatus('online');
        await realtimeService.updateCurrentPage(window.location.pathname);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [enableRealtime, isInitialized]);

  // Handle page navigation
  useEffect(() => {
    if (!enableRealtime || !isInitialized) return;

    const updateCurrentPage = () => {
      realtimeService.updateCurrentPage(window.location.pathname);
    };

    // Update on initial load
    updateCurrentPage();

    // Listen for navigation changes
    window.addEventListener('popstate', updateCurrentPage);
    
    // For SPA navigation, we'd need to hook into the router
    // This is a basic implementation
    const originalPushState = history.pushState;
    history.pushState = function(...args) {
      originalPushState.apply(history, args);
      setTimeout(updateCurrentPage, 0);
    };

    return () => {
      window.removeEventListener('popstate', updateCurrentPage);
      history.pushState = originalPushState;
    };
  }, [enableRealtime, isInitialized]);

  const contextValue: LangChainRealtimeContextType = {
    isInitialized,
    isLoading,
    error,
    features,
    stats,
  };

  return (
    <LangChainRealtimeContext.Provider value={contextValue}>
      {children}
    </LangChainRealtimeContext.Provider>
  );
}

export function useLangChainRealtime() {
  const context = useContext(LangChainRealtimeContext);
  if (context === undefined) {
    throw new Error('useLangChainRealtime must be used within a LangChainRealtimeProvider');
  }
  return context;
}

/**
 * Status indicator component for the enhanced features
 */
export function EnhancedFeaturesStatus() {
  const { isInitialized, isLoading, error, features, stats } = useLangChainRealtime();

  if (isLoading) {
    return (
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <div className="h-2 w-2 bg-yellow-500 rounded-full animate-pulse" />
        <span>Initializing enhanced features...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center space-x-2 text-sm text-red-500">
        <div className="h-2 w-2 bg-red-500 rounded-full" />
        <span>Some features unavailable</span>
      </div>
    );
  }

  if (!isInitialized) {
    return null;
  }

  return (
    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
      <div className="flex items-center space-x-2">
        <div className="h-2 w-2 bg-green-500 rounded-full" />
        <span>Enhanced features active</span>
      </div>
      
      {features.realtime && (
        <div className="flex items-center space-x-1">
          <span>{stats.onlineUsers} online</span>
        </div>
      )}
      
      {features.rag && (
        <div className="flex items-center space-x-1">
          <span>{stats.totalDocuments} docs</span>
        </div>
      )}
      
      {features.agents && (
        <div className="flex items-center space-x-1">
          <span>{stats.availableTools} tools</span>
        </div>
      )}
      
      {features.collaboration && stats.activeSessions > 0 && (
        <div className="flex items-center space-x-1">
          <span>{stats.activeSessions} active sessions</span>
        </div>
      )}
    </div>
  );
}
