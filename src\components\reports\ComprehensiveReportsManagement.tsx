
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, Download, Calendar, Filter, Search } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export const ComprehensiveReportsManagement = () => {
  const [selectedReportType, setSelectedReportType] = useState("all");
  const [dateRange, setDateRange] = useState("30");
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();

  const { data: reports, isLoading } = useQuery({
    queryKey: ['comprehensive-reports', selectedReportType, dateRange],
    queryFn: async () => {
      let query = supabase
        .from('construction_reports')
        .select(`
          id,
          report_title,
          report_type,
          created_at,
          created_by,
          site_id,
          progress_percentage,
          profiles:created_by (full_name)
        `)
        .order('created_at', { ascending: false });

      if (selectedReportType !== 'all') {
        query = query.eq('report_type', selectedReportType);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });

  const generateReport = async (type: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { error } = await supabase
        .from('construction_reports')
        .insert([{
          report_title: `${type} Report - ${new Date().toLocaleDateString()}`,
          report_type: type,
          work_performed: `Generated ${type} report`,
          created_by: user.id,
          site_id: '00000000-0000-0000-0000-000000000000',
        }]);

      if (error) throw error;

      toast({
        title: "Report Generated",
        description: `${type} report has been generated successfully`,
      });
    } catch (error) {
      console.error('Error generating report:', error);
      toast({
        title: "Error",
        description: "Failed to generate report",
        variant: "destructive",
      });
    }
  };

  const reportTypes = [
    { value: "all", label: "All Reports" },
    { value: "construction", label: "Construction Reports" },
    { value: "financial", label: "Financial Reports" },
    { value: "fleet", label: "Fleet Reports" },
    { value: "battery", label: "Battery Reports" },
    { value: "attendance", label: "Attendance Reports" },
    { value: "telecom", label: "Telecom Reports" }
  ];

  const filteredReports = reports?.filter(report =>
    report.report_title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Comprehensive Reports Management</h2>
          <p className="text-muted-foreground">Generate, view, and manage all system reports</p>
        </div>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="generate">Generate Reports</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="archive">Archive</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="flex gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search reports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={selectedReportType} onValueChange={setSelectedReportType}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-4">
            {isLoading ? (
              <div className="text-center py-8">Loading reports...</div>
            ) : filteredReports?.length === 0 ? (
              <Card>
                <CardContent className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Reports Found</h3>
                  <p className="text-muted-foreground">Try adjusting your filters or generate a new report.</p>
                </CardContent>
              </Card>
            ) : (
              filteredReports?.map((report) => (
                <Card key={report.id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-semibold">{report.report_title}</h3>
                        <p className="text-sm text-muted-foreground">
                          Type: {report.report_type} | Created: {new Date(report.created_at).toLocaleDateString()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          By: {report.profiles?.full_name || 'Unknown'}
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Export
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="generate" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {[
              { type: "construction", title: "Construction Report", desc: "Site progress and activities" },
              { type: "financial", title: "Financial Report", desc: "Revenue, expenses, and budgets" },
              { type: "fleet", title: "Fleet Report", desc: "Vehicle usage and maintenance" },
              { type: "battery", title: "Battery Report", desc: "Battery status and performance" },
              { type: "attendance", title: "Attendance Report", desc: "Employee time tracking" },
              { type: "telecom", title: "Telecom Report", desc: "Site connectivity and performance" }
            ].map((reportType) => (
              <Card key={reportType.type}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    {reportType.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">{reportType.desc}</p>
                  <Button 
                    onClick={() => generateReport(reportType.type)}
                    className="w-full"
                  >
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Report Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center p-4 border rounded-lg">
                  <h3 className="text-2xl font-bold">{reports?.length || 0}</h3>
                  <p className="text-sm text-muted-foreground">Total Reports</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <h3 className="text-2xl font-bold">
                    {reports?.filter(r => new Date(r.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)).length || 0}
                  </h3>
                  <p className="text-sm text-muted-foreground">This Week</p>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <h3 className="text-2xl font-bold">
                    {new Set(reports?.map(r => r.report_type)).size || 0}
                  </h3>
                  <p className="text-sm text-muted-foreground">Report Types</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="archive">
          <Card>
            <CardHeader>
              <CardTitle>Report Archive</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Archive functionality for old reports will be implemented here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
