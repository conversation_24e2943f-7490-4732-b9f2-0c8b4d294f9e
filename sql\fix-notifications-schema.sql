-- Fix notifications table schema issues
-- This script adds missing columns and fixes the notifications table

-- Step 1: Check if notifications table exists and add missing columns
DO $$ 
BEGIN
    -- Add 'data' column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'data'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN data JSONB DEFAULT '{}';
        RAISE NOTICE 'Added data column to notifications table';
    END IF;

    -- Add 'read' column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'read'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN read BOOLEAN DEFAULT false;
        RAISE NOTICE 'Added read column to notifications table';
    END IF;

    -- Ensure other required columns exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'type'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN type TEXT NOT NULL DEFAULT 'info';
        RAISE NOTICE 'Added type column to notifications table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'title'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN title TEXT NOT NULL DEFAULT 'Notification';
        RAISE NOTICE 'Added title column to notifications table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'message'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN message TEXT;
        RAISE NOTICE 'Added message column to notifications table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'user_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN user_id UUID REFERENCES auth.users(id);
        RAISE NOTICE 'Added user_id column to notifications table';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'created_at'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.notifications ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        RAISE NOTICE 'Added created_at column to notifications table';
    END IF;

EXCEPTION
    WHEN undefined_table THEN
        -- Create the notifications table if it doesn't exist
        CREATE TABLE public.notifications (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            type TEXT NOT NULL DEFAULT 'info',
            title TEXT NOT NULL DEFAULT 'Notification',
            message TEXT,
            data JSONB DEFAULT '{}',
            user_id UUID REFERENCES auth.users(id),
            read BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        RAISE NOTICE 'Created notifications table with all required columns';
END $$;

-- Step 2: Create or update RLS policies for notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "notifications_select_own" ON public.notifications;
DROP POLICY IF EXISTS "notifications_insert_own" ON public.notifications;
DROP POLICY IF EXISTS "notifications_update_own" ON public.notifications;
DROP POLICY IF EXISTS "notifications_service_role" ON public.notifications;

-- Create new RLS policies
CREATE POLICY "notifications_select_own" ON public.notifications
    FOR SELECT
    USING (user_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'manager', 'staff-admin'));

CREATE POLICY "notifications_insert_own" ON public.notifications
    FOR INSERT
    WITH CHECK (user_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'manager', 'staff-admin'));

CREATE POLICY "notifications_update_own" ON public.notifications
    FOR UPDATE
    USING (user_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'manager', 'staff-admin'))
    WITH CHECK (user_id = auth.uid() OR auth.jwt() ->> 'role' IN ('admin', 'manager', 'staff-admin'));

CREATE POLICY "notifications_service_role" ON public.notifications
    FOR ALL
    USING (current_setting('role') = 'service_role')
    WITH CHECK (current_setting('role') = 'service_role');

-- Step 3: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON public.notifications(created_at);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON public.notifications(type);

-- Step 4: Create a function to clean up old notifications
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS void AS $$
BEGIN
    -- Delete notifications older than 30 days
    DELETE FROM public.notifications 
    WHERE created_at < NOW() - INTERVAL '30 days';
    
    RAISE NOTICE 'Cleaned up old notifications';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Grant necessary permissions
GRANT ALL ON public.notifications TO authenticated;
GRANT ALL ON public.notifications TO service_role;

RAISE NOTICE 'Notifications table schema fix completed successfully';
