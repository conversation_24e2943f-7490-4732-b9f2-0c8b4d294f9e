﻿export interface EmailTemplateData {
  [key: string]: any;
}

export interface EmailTemplate {
  subject: string;
  html: string;
}

export class EmailTemplateService {
  private frontendUrl: string;

  constructor(frontendUrl: string = 'https://ai.ctnigeria.com') {
    this.frontendUrl = frontendUrl;
  }

  public getTemplate(type: string, data: EmailTemplateData): EmailTemplate {
    const baseStyles = 'font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;';
    
    switch (type) {
      case 'task_assigned':
        return {
          subject: `New Task Assigned: ${data.taskTitle || 'Untitled Task'}`,
          html: `<div style="${baseStyles}"><h1>Task Assigned</h1><p>Hello ${data.userName || 'User'}, you have been assigned a new task: ${data.taskTitle || 'Untitled Task'}</p></div>`
        };
      
      case 'memo_approved':
        return {
          subject: `Memo Approved: ${data.memoTitle || 'Untitled Memo'}`,
          html: `<div style="${baseStyles}"><h1>Memo Approved</h1><p>Your memo "${data.memoTitle || 'Untitled Memo'}" has been approved.</p></div>`
        };
      
      case 'test_notification':
        return {
          subject: 'CTNL AI WORK-BOARD - Test Email',
          html: `<div style="${baseStyles}"><h1>Test Email</h1><p>Hello ${data.userName || 'User'}, this is a test email from CTNL AI WORK-BOARD. ${data.testMessage || 'Your email notifications are working correctly!'}</p></div>`
        };

      default:
        return {
          subject: 'CTNL AI WORK-BOARD Notification',
          html: `<div style="${baseStyles}"><h1>Notification</h1><p>You have a new notification from CTNL AI WORK-BOARD.</p></div>`
        };
    }
  }
}

export const emailTemplateService = new EmailTemplateService();
