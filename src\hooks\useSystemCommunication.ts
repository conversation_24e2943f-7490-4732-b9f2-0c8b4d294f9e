import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/components/auth/AuthProvider';

interface APIHealthCheck {
  service: string;
  status: 'healthy' | 'degraded' | 'down';
  responseTime: number;
  lastChecked: Date;
  error?: string;
}

interface SystemStatus {
  database: APIHealthCheck;
  edgeFunctions: APIHealthCheck[];
  aiServices: APIHealthCheck;
  authentication: APIHealthCheck;
  overall: 'healthy' | 'degraded' | 'down';
}

export const useSystemCommunication = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const checkDatabaseHealth = useCallback(async (): Promise<APIHealthCheck> => {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      
      const responseTime = Date.now() - startTime;
      
      if (error) throw error;
      
      return {
        service: 'Supabase Database',
        status: 'healthy',
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        service: 'Supabase Database',
        status: 'down',
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        error: error.message,
      };
    }
  }, []);

  const checkEdgeFunctionHealth = useCallback(async (functionName: string): Promise<APIHealthCheck> => {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase.functions.invoke(functionName, {
        body: { 
          healthCheck: true,
          message: 'System health check',
          userId: user?.id,
          userRole: user?.role || 'staff',
          context: { type: 'health_check' }
        }
      });
      
      const responseTime = Date.now() - startTime;
      
      if (error) throw error;
      
      return {
        service: `Edge Function: ${functionName}`,
        status: 'healthy',
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        service: `Edge Function: ${functionName}`,
        status: 'down',
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        error: error.message,
      };
    }
  }, [user]);

  const checkAIServicesHealth = useCallback(async (): Promise<APIHealthCheck> => {
    const startTime = Date.now();
    try {
      // Test AI services through our edge functions
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: 'Health check',
          userId: user?.id,
          context: {
            role: user?.role || 'staff',
            department: user?.department || 'General'
          }
        }
      });
      
      const responseTime = Date.now() - startTime;
      
      if (error) throw error;
      
      return {
        service: 'AI Services (OpenAI)',
        status: 'healthy',
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        service: 'AI Services (OpenAI)',
        status: 'down',
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        error: error.message,
      };
    }
  }, [user]);

  const checkAuthenticationHealth = useCallback(async (): Promise<APIHealthCheck> => {
    const startTime = Date.now();
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      const responseTime = Date.now() - startTime;
      
      if (error) throw error;
      
      return {
        service: 'Supabase Authentication',
        status: session ? 'healthy' : 'degraded',
        responseTime,
        lastChecked: new Date(),
      };
    } catch (error) {
      return {
        service: 'Supabase Authentication',
        status: 'down',
        responseTime: Date.now() - startTime,
        lastChecked: new Date(),
        error: error.message,
      };
    }
  }, []);

  const runSystemHealthCheck = useCallback(async () => {
    setLoading(true);
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Starting comprehensive system health check...');
      }

      // Check all system components in parallel
      const [
        databaseHealth,
        authHealth,
        aiServicesHealth,
        ...edgeFunctionHealths
      ] = await Promise.all([
        checkDatabaseHealth(),
        checkAuthenticationHealth(),
        checkAIServicesHealth(),
        checkEdgeFunctionHealth('ai-agent-intent'),
        checkEdgeFunctionHealth('ai-agent-executor'),
        checkEdgeFunctionHealth('ai-agent-response'),
        checkEdgeFunctionHealth('ai-assistant'),
        checkEdgeFunctionHealth('ai-file-analyzer'),
        checkEdgeFunctionHealth('analyze-content'),
        checkEdgeFunctionHealth('analyze-document'),
      ]);

      // Determine overall system status
      const allServices = [databaseHealth, authHealth, aiServicesHealth, ...edgeFunctionHealths];
      const healthyServices = allServices.filter(s => s.status === 'healthy').length;
      const degradedServices = allServices.filter(s => s.status === 'degraded').length;
      const downServices = allServices.filter(s => s.status === 'down').length;

      let overallStatus: 'healthy' | 'degraded' | 'down';
      if (downServices > 0) {
        overallStatus = 'down';
      } else if (degradedServices > 0) {
        overallStatus = 'degraded';
      } else {
        overallStatus = 'healthy';
      }

      const status: SystemStatus = {
        database: databaseHealth,
        authentication: authHealth,
        aiServices: aiServicesHealth,
        edgeFunctions: edgeFunctionHealths,
        overall: overallStatus,
      };

      setSystemStatus(status);

      // Log results in development only
      if (process.env.NODE_ENV === 'development') {
        console.log('📊 System Health Check Results:', {
          overall: overallStatus,
          healthy: healthyServices,
          degraded: degradedServices,
          down: downServices,
          totalServices: allServices.length
        });
      }

      // Show toast notification
      toast({
        title: "System Health Check Complete",
        description: `Overall Status: ${overallStatus.toUpperCase()} | ${healthyServices}/${allServices.length} services healthy`,
        variant: overallStatus === 'healthy' ? 'default' : 'destructive',
      });

      return status;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ System health check failed:', error);
      }
      toast({
        title: "Health Check Failed",
        description: "Unable to complete system health check",
        variant: "destructive",
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [checkDatabaseHealth, checkAuthenticationHealth, checkAIServicesHealth, checkEdgeFunctionHealth, toast]);

  const testAPIConnectivity = useCallback(async () => {
    setLoading(true);
    try {
      console.log('🔌 Testing API connectivity...');
      
      // Test core database operations
      const { data: profileTest } = await supabase.from('profiles').select('id').limit(1);
      const { data: taskTest } = await supabase.from('tasks').select('id').limit(1);
      const { data: projectTest } = await supabase.from('projects').select('id').limit(1);
      
      // Test real-time subscriptions
      const channel = supabase
        .channel('connectivity-test')
        .on('postgres_changes', { event: '*', schema: 'public', table: 'profiles' }, () => {})
        .subscribe();
      
      setTimeout(() => {
        supabase.removeChannel(channel);
      }, 1000);

      if (process.env.NODE_ENV === 'development') {
        console.log('✅ API connectivity test passed');
      }
      toast({
        title: "API Connectivity Test",
        description: "All API endpoints are responding correctly",
      });

      return true;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ API connectivity test failed:', error);
      }
      toast({
        title: "API Connectivity Failed",
        description: error.message,
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  return {
    systemStatus,
    loading,
    runSystemHealthCheck,
    testAPIConnectivity,
  };
};