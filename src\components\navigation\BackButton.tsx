import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Home, ChevronLeft } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

interface BackButtonProps {
  /** Custom destination path. If not provided, will navigate back in history */
  to?: string;
  /** Custom label for the button */
  label?: string;
  /** Button variant */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  /** Button size */
  size?: "default" | "sm" | "lg" | "icon";
  /** Custom icon component */
  icon?: React.ComponentType<{ className?: string }>;
  /** Additional CSS classes */
  className?: string;
  /** Custom styles */
  style?: React.CSSProperties;
  /** Show home icon instead of back arrow for dashboard navigation */
  showHome?: boolean;
  /** Disable the button */
  disabled?: boolean;
  /** Callback function called before navigation */
  onBeforeNavigate?: () => boolean | Promise<boolean>;
}

export const BackButton: React.FC<BackButtonProps> = ({
  to,
  label,
  variant = "outline",
  size = "sm",
  icon: CustomIcon,
  className,
  style,
  showHome = false,
  disabled = false,
  onBeforeNavigate
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Determine the default destination based on current path
  const getDefaultDestination = () => {
    const path = location.pathname;
    
    // Admin pages go back to admin dashboard
    if (path.startsWith('/dashboard/admin/')) {
      return '/dashboard/admin';
    }
    
    // Manager pages go back to manager dashboard
    if (path.startsWith('/dashboard/manager/')) {
      return '/dashboard/manager';
    }
    
    // Staff pages go back to staff dashboard
    if (path.startsWith('/dashboard/staff/')) {
      return '/dashboard/staff';
    }
    
    // Accountant pages go back to accountant dashboard
    if (path.startsWith('/dashboard/accountant/')) {
      return '/dashboard/accountant';
    }
    
    // Staff-admin pages go back to staff-admin dashboard
    if (path.startsWith('/dashboard/staff-admin/')) {
      return '/dashboard/staff-admin';
    }
    
    // Default to main dashboard
    return '/dashboard';
  };

  // Determine the default label based on destination
  const getDefaultLabel = () => {
    const destination = to || getDefaultDestination();
    
    if (destination.includes('/admin')) return 'Back to Admin';
    if (destination.includes('/manager')) return 'Back to Manager';
    if (destination.includes('/staff-admin')) return 'Back to Staff Admin';
    if (destination.includes('/staff')) return 'Back to Staff';
    if (destination.includes('/accountant')) return 'Back to Accountant';
    
    return 'Back to Dashboard';
  };

  // Determine the icon to use
  const IconComponent = CustomIcon || (showHome ? Home : ArrowLeft);

  const handleClick = async () => {
    if (disabled) return;

    // Call onBeforeNavigate if provided
    if (onBeforeNavigate) {
      const canNavigate = await onBeforeNavigate();
      if (!canNavigate) return;
    }

    if (to) {
      navigate(to);
    } else {
      // Try to go back in history, fallback to default destination
      if (window.history.length > 1) {
        navigate(-1);
      } else {
        navigate(getDefaultDestination());
      }
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled}
      className={cn(
        "neumorphism-button border border-border/30 hover:bg-muted/30 transition-all duration-200",
        className
      )}
      style={{
        borderRadius: '8px',
        ...style
      }}
    >
      <IconComponent className="h-4 w-4 mr-2" />
      {label || getDefaultLabel()}
    </Button>
  );
};

// Specialized back button for sidebar navigation
export const SidebarBackButton: React.FC<Omit<BackButtonProps, 'variant' | 'size'>> = (props) => {
  return (
    <BackButton
      {...props}
      variant="ghost"
      size="sm"
      icon={ChevronLeft}
      className={cn(
        "w-full justify-start text-left hover:bg-muted/20",
        props.className
      )}
    />
  );
};

// Specialized back button for page headers
export const PageHeaderBackButton: React.FC<Omit<BackButtonProps, 'variant' | 'size'>> = (props) => {
  return (
    <BackButton
      {...props}
      variant="outline"
      size="sm"
      className={cn(
        "neumorphism-button shadow-sm",
        props.className
      )}
    />
  );
};

// Specialized floating back button
export const FloatingBackButton: React.FC<Omit<BackButtonProps, 'variant' | 'size'>> = (props) => {
  return (
    <div className="fixed bottom-6 left-6 z-50">
      <BackButton
        {...props}
        variant="default"
        size="lg"
        className={cn(
          "neumorphism-button shadow-lg hover:shadow-xl transition-all duration-300",
          props.className
        )}
        style={{
          borderRadius: '50%',
          width: '56px',
          height: '56px',
          padding: '0',
          ...props.style
        }}
      />
    </div>
  );
};

// Hook for programmatic navigation with back button logic
export const useBackNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const goBack = (fallbackPath?: string) => {
    if (fallbackPath) {
      navigate(fallbackPath);
    } else if (window.history.length > 1) {
      navigate(-1);
    } else {
      // Determine fallback based on current path
      const path = location.pathname;
      
      if (path.startsWith('/dashboard/admin/')) {
        navigate('/dashboard/admin');
      } else if (path.startsWith('/dashboard/manager/')) {
        navigate('/dashboard/manager');
      } else if (path.startsWith('/dashboard/staff-admin/')) {
        navigate('/dashboard/staff-admin');
      } else if (path.startsWith('/dashboard/staff/')) {
        navigate('/dashboard/staff');
      } else if (path.startsWith('/dashboard/accountant/')) {
        navigate('/dashboard/accountant');
      } else {
        navigate('/dashboard');
      }
    }
  };

  const goToParent = () => {
    const pathParts = location.pathname.split('/').filter(Boolean);
    if (pathParts.length > 1) {
      pathParts.pop(); // Remove last segment
      navigate('/' + pathParts.join('/'));
    } else {
      navigate('/dashboard');
    }
  };

  const goToDashboard = () => {
    const path = location.pathname;
    
    if (path.startsWith('/dashboard/admin/')) {
      navigate('/dashboard/admin');
    } else if (path.startsWith('/dashboard/manager/')) {
      navigate('/dashboard/manager');
    } else if (path.startsWith('/dashboard/staff-admin/')) {
      navigate('/dashboard/staff-admin');
    } else if (path.startsWith('/dashboard/staff/')) {
      navigate('/dashboard/staff');
    } else if (path.startsWith('/dashboard/accountant/')) {
      navigate('/dashboard/accountant');
    } else {
      navigate('/dashboard');
    }
  };

  return {
    goBack,
    goToParent,
    goToDashboard,
    currentPath: location.pathname
  };
};

export default BackButton;
