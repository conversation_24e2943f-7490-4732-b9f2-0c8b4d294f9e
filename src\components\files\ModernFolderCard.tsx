import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Folder, 
  FolderOpen, 
  Edit, 
  Trash2, 
  Plus,
  FileText,
  Users
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface FolderData {
  id: string;
  name: string;
  description?: string;
  file_count: number;
  created_at: string;
  category?: string;
  color?: string;
}

interface ModernFolderCardProps {
  folder: FolderData;
  onOpen: (folder: FolderData) => void;
  onEdit: (folder: FolderData) => void;
  onDelete: (folder: FolderData) => void;
  onAddFile?: (folder: FolderData) => void;
}

const getFolderGradient = (category?: string, color?: string) => {
  if (color) return color;
  
  switch (category?.toLowerCase()) {
    case 'documents':
      return 'from-blue-500 to-blue-600';
    case 'images':
      return 'from-green-500 to-green-600';
    case 'projects':
      return 'from-purple-500 to-purple-600';
    case 'reports':
      return 'from-orange-500 to-orange-600';
    case 'archive':
      return 'from-gray-500 to-gray-600';
    default:
      return 'from-red-500 to-red-600'; // Brand color
  }
};

export const ModernFolderCard: React.FC<ModernFolderCardProps> = ({
  folder,
  onOpen,
  onEdit,
  onDelete,
  onAddFile
}) => {
  const gradientColor = getFolderGradient(folder.category, folder.color);

  return (
    <div className="modern-folder-card group">
      {/* Top Section with Gradient */}
      <div className={`top-section bg-gradient-to-br ${gradientColor}`}>
        {/* Decorative Border */}
        <div className="border">
          <div className="border-before"></div>
        </div>
        <div className="top-section-before"></div>
        
        {/* Icons Section */}
        <div className="icons">
          {/* Folder Icon */}
          <div className="logo">
            <FolderOpen className="w-full h-full text-white" />
          </div>
          
          {/* Action Icons */}
          <div className="social-media">
            {onAddFile && (
              <button
                onClick={() => onAddFile(folder)}
                className="action-btn"
                title="Add File"
              >
                <Plus className="w-4 h-4" />
              </button>
            )}
            <button
              onClick={() => onEdit(folder)}
              className="action-btn"
              title="Edit Folder"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(folder)}
              className="action-btn text-red-300 hover:text-red-100"
              title="Delete Folder"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Circular Design Elements */}
        <div className="design-elements">
          <div className="circle-1 center color-border">
            <div className="circle-2 center color-border">
              <div className="circle-3 center color-border">
                <div className="circle-4 center color-border">
                  <div className="circle-5" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="bottom-section">
        <button 
          onClick={() => onOpen(folder)}
          className="title-button"
          title={folder.name}
        >
          {folder.name.length > 18 ? `${folder.name.substring(0, 18)}...` : folder.name}
        </button>
        
        {folder.description && (
          <p className="description">
            {folder.description.length > 40 
              ? `${folder.description.substring(0, 40)}...` 
              : folder.description
            }
          </p>
        )}

        {folder.category && (
          <div className="mt-2 text-center">
            <Badge variant="outline" className="text-xs">
              {folder.category}
            </Badge>
          </div>
        )}

        <div className="row">
          <div className="item">
            <span className="big-text">{folder.file_count}</span>
            <span className="regular-text">Files</span>
          </div>
          <div className="item">
            <span className="big-text">
              {formatDistanceToNow(new Date(folder.created_at), { addSuffix: false })}
            </span>
            <span className="regular-text">Created</span>
          </div>
          <div className="item">
            <span className="big-text">
              <FileText className="w-3 h-3 mx-auto" />
            </span>
            <span className="regular-text">Folder</span>
          </div>
        </div>
      </div>

      <style jsx>{`
        .modern-folder-card {
          width: 280px;
          border-radius: 20px;
          background: #1a1a1a;
          padding: 5px;
          overflow: hidden;
          box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 20px 0px;
          transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
          position: relative;
        }

        .modern-folder-card:hover {
          transform: scale(1.05);
        }

        .top-section {
          height: 150px;
          border-radius: 15px;
          display: flex;
          flex-direction: column;
          position: relative;
          overflow: hidden;
        }

        .border {
          border-bottom-right-radius: 10px;
          height: 30px;
          width: 130px;
          background: #1a1a1a;
          position: relative;
          transform: skew(-40deg);
          box-shadow: -10px -10px 0 0 #1a1a1a;
        }

        .border::before {
          content: "";
          position: absolute;
          width: 15px;
          height: 15px;
          top: 0;
          right: -15px;
          background: rgba(255, 255, 255, 0);
          border-top-left-radius: 10px;
          box-shadow: -5px -5px 0 2px #1a1a1a;
        }

        .top-section::before {
          content: "";
          position: absolute;
          top: 30px;
          left: 0;
          background: rgba(255, 255, 255, 0);
          height: 15px;
          width: 15px;
          border-top-left-radius: 15px;
          box-shadow: -5px -5px 0 2px #1a1a1a;
        }

        .icons {
          position: absolute;
          top: 0;
          width: 100%;
          height: 30px;
          display: flex;
          justify-content: space-between;
          z-index: 10;
        }

        .logo {
          height: 100%;
          aspect-ratio: 1;
          padding: 7px 0 7px 15px;
        }

        .social-media {
          height: 100%;
          padding: 8px 15px;
          display: flex;
          gap: 7px;
        }

        .action-btn {
          height: 100%;
          background: none;
          border: none;
          color: #1a1a1a;
          cursor: pointer;
          transition: color 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .action-btn:hover {
          color: white;
        }

        .design-elements {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 100%;
          pointer-events: none;
        }

        .center {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .color-border {
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.1);
          box-shadow: 0px 0px 10px 2px rgba(0, 0, 0, 0.1);
        }

        .circle-1 {
          height: 120px;
          width: 120px;
          position: absolute;
          right: -30px;
          top: 20px;
        }

        .circle-2 {
          height: 90px;
          width: 90px;
        }

        .circle-3 {
          height: 70px;
          width: 70px;
        }

        .circle-4 {
          height: 50px;
          width: 50px;
        }

        .circle-5 {
          height: 30px;
          width: 30px;
          border-radius: 50%;
          background-color: rgba(255, 255, 255, 0.8);
        }

        .bottom-section {
          margin-top: 15px;
          padding: 10px 5px;
        }

        .title-button {
          display: block;
          font-size: 16px;
          font-weight: bold;
          color: white;
          text-align: center;
          letter-spacing: 1px;
          line-height: 1.2;
          background: none;
          border: none;
          cursor: pointer;
          transition: color 0.2s ease;
          width: 100%;
        }

        .title-button:hover {
          color: #dc2626;
        }

        .description {
          font-size: 11px;
          color: rgba(170, 222, 243, 0.6);
          text-align: center;
          margin-top: 5px;
          line-height: 1.3;
        }

        .row {
          display: flex;
          justify-content: space-between;
          margin-top: 15px;
        }

        .item {
          flex: 30%;
          text-align: center;
          padding: 5px;
          color: rgba(170, 222, 243, 0.8);
        }

        .big-text {
          font-size: 11px;
          display: block;
          font-weight: 600;
        }

        .regular-text {
          font-size: 8px;
          opacity: 0.7;
        }

        .item:nth-child(2) {
          border-left: 1px solid rgba(255, 255, 255, 0.2);
          border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Dark theme adjustments */
        .dark .modern-folder-card {
          background: #0a0a0a;
        }

        .dark .border {
          background: #0a0a0a;
          box-shadow: -10px -10px 0 0 #0a0a0a;
        }

        .dark .border::before {
          box-shadow: -5px -5px 0 2px #0a0a0a;
        }

        .dark .top-section::before {
          box-shadow: -5px -5px 0 2px #0a0a0a;
        }

        .dark .title-button {
          color: #e5e5e5;
        }
      `}</style>
    </div>
  );
};
