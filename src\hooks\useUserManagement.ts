
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface UserProfile {
  id: string;
  full_name: string | null;
  email: string | null;
  role: string | null;
  department_id: string | null;
  department?: {
    name: string;
    description: string | null;
  };
  status: string | null;
  created_at: string;
  updated_at: string;
}

interface Department {
  id: string;
  name: string;
  description: string | null;
  manager_id: string | null;
  employee_count: number;
}

export const useUserManagement = () => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          department:department_id (
            name,
            description
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      // Transform data to handle potential type mismatches
      const transformedUsers = (data || []).map(user => ({
        id: user.id,
        full_name: user.full_name,
        email: user.email,
        role: user.role || 'staff',
        department_id: user.department_id,
        department: user.department,
        status: user.status || 'active',
        created_at: user.created_at,
        updated_at: user.updated_at
      }));
      
      setUsers(transformedUsers);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to fetch users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name');

      if (error) throw error;
      setDepartments(data || []);
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchDepartments();

    // Set up real-time subscriptions
    const usersChannel = supabase
      .channel('users-management')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'profiles'
        },
        () => {
          fetchUsers();
        }
      )
      .subscribe();

    const deptsChannel = supabase
      .channel('departments-management')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'departments'
        },
        () => {
          fetchDepartments();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(usersChannel);
      supabase.removeChannel(deptsChannel);
    };
  }, []);

  const updateUserRole = async (userId: string, role: string, departmentId?: string) => {
    setLoading(true);
    try {
      // Get current user data to track department changes
      const { data: currentUser } = await supabase
        .from('profiles')
        .select('department_id')
        .eq('id', userId)
        .single();

      const updateData: any = { role };
      if (departmentId) {
        updateData.department_id = departmentId;
      }

      const { error } = await supabase
        .from('profiles')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      // Update department counts if department changed
      if (departmentId && currentUser) {
        const affectedDepartments = [];
        if (currentUser.department_id) affectedDepartments.push(currentUser.department_id);
        if (departmentId !== currentUser.department_id) affectedDepartments.push(departmentId);

        for (const deptId of affectedDepartments) {
          const { count } = await supabase
            .from('profiles')
            .select('id', { count: 'exact' })
            .eq('department_id', deptId);

          await supabase
            .from('departments')
            .update({ employee_count: count || 0 })
            .eq('id', deptId);
        }
      }

      toast({
        title: "Success",
        description: "User role updated successfully",
      });

      await fetchUsers();
      return { success: true };
    } catch (error) {
      console.error('Error updating user role:', error);
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const createDepartment = async (name: string, description?: string, managerId?: string) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('departments')
        .insert([{
          name,
          description,
          manager_id: managerId
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Department created successfully",
      });

      await fetchDepartments();
      return { success: true };
    } catch (error) {
      console.error('Error creating department:', error);
      toast({
        title: "Error",
        description: "Failed to create department",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  return {
    users,
    departments,
    loading,
    updateUserRole,
    createDepartment,
    refetchUsers: fetchUsers,
    refetchDepartments: fetchDepartments
  };
};
