/**
 * Cache Busting and PWA Cleanup Utilities
 * Ensures old implementations are completely cleared
 */

export class CacheBuster {
  private static readonly APP_VERSION = '2024.12.18.002';
  private static readonly STORAGE_KEY = 'ctnl-app-version';
  private static readonly LAST_CLEAR_KEY = 'ctnl-last-cache-clear';

  /**
   * Initialize cache busting on app startup
   */
  public static async initialize(): Promise<void> {
    console.log('🧹 CacheBuster: Initializing...');

    try {
      // Check if we need to clear cache
      const shouldClear = this.shouldClearCache();
      
      if (shouldClear) {
        console.log('🧹 CacheBuster: Cache clearing required');
        await this.performComprehensiveClear();
      } else {
        console.log('✅ CacheBuster: Cache is up to date');
      }

      // Update version tracking
      this.updateVersionTracking();
    } catch (error) {
      console.error('❌ CacheBuster: Error during initialization:', error);
    }
  }

  /**
   * Check if cache clearing is needed
   */
  private static shouldClearCache(): boolean {
    const storedVersion = localStorage.getItem(this.STORAGE_KEY);
    const lastClear = localStorage.getItem(this.LAST_CLEAR_KEY);
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const fiveMinutes = 5 * 60 * 1000;

    // Don't clear if we just cleared recently (prevent infinite loops)
    if (lastClear && (now - parseInt(lastClear)) < fiveMinutes) {
      console.log('✅ Cache cleared recently, skipping to prevent infinite reload');
      return false;
    }

    // Clear if version mismatch (but not too frequently)
    if (storedVersion !== this.APP_VERSION) {
      console.log('🔄 Version mismatch detected:', storedVersion, '→', this.APP_VERSION);
      return true;
    }

    // Only clear periodically in development mode (check for localhost)
    const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
    if (isDevelopment && (!lastClear || (now - parseInt(lastClear)) > oneHour)) {
      console.log('🔄 Periodic cache clear needed (development mode)');
      return true;
    }

    return false;
  }

  /**
   * Perform comprehensive cache clearing
   */
  private static async performComprehensiveClear(): Promise<void> {
    console.log('🧹 Starting comprehensive cache clear...');

    // 1. Clear all service workers
    await this.clearServiceWorkers();

    // 2. Clear all caches
    await this.clearAllCaches();

    // 3. Clear problematic localStorage items
    this.clearProblematicStorage();

    // 4. Clear browser caches (where possible)
    this.clearBrowserCaches();

    console.log('✅ Comprehensive cache clear completed');
  }

  /**
   * Clear all service workers
   */
  private static async clearServiceWorkers(): Promise<void> {
    if (!('serviceWorker' in navigator)) return;

    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log(`🔍 Found ${registrations.length} service worker registrations`);

      const unregisterPromises = registrations.map(async (registration) => {
        console.log('🧹 Unregistering service worker:', registration.scope);
        try {
          const result = await registration.unregister();
          console.log('✅ Service worker unregistered:', result);
          return result;
        } catch (error) {
          console.error('❌ Failed to unregister service worker:', error);
          return false;
        }
      });

      await Promise.all(unregisterPromises);

      // Force clear common service worker scopes
      const commonScopes = [
        '/',
        '/sw.js',
        '/service-worker.js',
        '/workbox-sw.js',
        '/pwa-sw.js'
      ];

      for (const scope of commonScopes) {
        try {
          const registration = await navigator.serviceWorker.getRegistration(scope);
          if (registration) {
            console.log('🧹 Force clearing scope:', scope);
            await registration.unregister();
          }
        } catch (error) {
          // Ignore errors for non-existent registrations
        }
      }
    } catch (error) {
      console.error('❌ Error clearing service workers:', error);
    }
  }

  /**
   * Clear all browser caches
   */
  private static async clearAllCaches(): Promise<void> {
    if (!('caches' in window)) return;

    try {
      const cacheNames = await caches.keys();
      console.log(`🔍 Found ${cacheNames.length} caches:`, cacheNames);

      const deletePromises = cacheNames.map(async (cacheName) => {
        console.log('🧹 Deleting cache:', cacheName);
        try {
          const result = await caches.delete(cacheName);
          console.log('✅ Cache deleted:', cacheName, result);
          return result;
        } catch (error) {
          console.error('❌ Failed to delete cache:', cacheName, error);
          return false;
        }
      });

      const results = await Promise.all(deletePromises);
      console.log('🎉 Cache deletion results:', results);
    } catch (error) {
      console.error('❌ Error clearing caches:', error);
    }
  }

  /**
   * Clear problematic localStorage items
   */
  private static clearProblematicStorage(): void {
    try {
      const keysToRemove: string[] = [];

      // Get all localStorage keys
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          // Remove PWA-related keys
          if (
            key.includes('workbox') ||
            key.includes('sw-') ||
            key.includes('pwa') ||
            key.includes('cache') ||
            key.includes('manifest') ||
            key.includes('offline') ||
            key.startsWith('wb-') ||
            key.includes('precache')
          ) {
            keysToRemove.push(key);
          }
        }
      }

      // Remove identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log('🧹 Removed localStorage key:', key);
      });

      // Also clear sessionStorage of similar items
      const sessionKeysToRemove: string[] = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (
          key.includes('workbox') ||
          key.includes('sw-') ||
          key.includes('pwa') ||
          key.includes('cache')
        )) {
          sessionKeysToRemove.push(key);
        }
      }

      sessionKeysToRemove.forEach(key => {
        sessionStorage.removeItem(key);
        console.log('🧹 Removed sessionStorage key:', key);
      });

    } catch (error) {
      console.error('❌ Error clearing problematic storage:', error);
    }
  }

  /**
   * Clear browser caches where possible
   */
  private static clearBrowserCaches(): void {
    try {
      // Clear IndexedDB databases related to caching
      if ('indexedDB' in window) {
        const dbsToDelete = [
          'workbox-background-sync',
          'workbox-expiration',
          'keyval-store',
          'pwa-cache'
        ];

        dbsToDelete.forEach(dbName => {
          try {
            const deleteReq = indexedDB.deleteDatabase(dbName);
            deleteReq.onsuccess = () => {
              console.log('🧹 Deleted IndexedDB:', dbName);
            };
            deleteReq.onerror = () => {
              // Ignore errors for non-existent databases
            };
          } catch (error) {
            // Ignore errors
          }
        });
      }

      // Clear any WebSQL databases (deprecated but might exist)
      if ('webkitStorageInfo' in window) {
        try {
          (window as any).webkitStorageInfo.requestQuota(
            (window as any).TEMPORARY,
            0,
            () => console.log('🧹 WebSQL quota cleared'),
            () => {} // Ignore errors
          );
        } catch (error) {
          // Ignore errors
        }
      }
    } catch (error) {
      console.error('❌ Error clearing browser caches:', error);
    }
  }

  /**
   * Update version tracking
   */
  private static updateVersionTracking(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, this.APP_VERSION);
      localStorage.setItem(this.LAST_CLEAR_KEY, Date.now().toString());
      console.log('✅ Version tracking updated:', this.APP_VERSION);
    } catch (error) {
      console.error('❌ Error updating version tracking:', error);
    }
  }

  /**
   * Force hard reload (bypass all caches)
   */
  public static forceHardReload(): void {
    console.log('🔄 Forcing hard reload...');
    
    // Add cache-busting parameter
    const url = new URL(window.location.href);
    url.searchParams.set('_cb', Date.now().toString());
    
    // Force reload from server
    window.location.href = url.toString();
  }

  /**
   * Check if app is running from cache
   */
  public static isRunningFromCache(): boolean {
    try {
      // Check if we're in a cached environment
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      const isInWebAppChrome = window.matchMedia('(display-mode: standalone)').matches;
      
      return isStandalone || isInWebAppiOS || isInWebAppChrome;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get current app version
   */
  public static getCurrentVersion(): string {
    return this.APP_VERSION;
  }

  /**
   * Get stored app version
   */
  public static getStoredVersion(): string | null {
    return localStorage.getItem(this.STORAGE_KEY);
  }

  /**
   * Manual cache clear (for user-triggered clearing)
   */
  public static async manualCacheClear(): Promise<void> {
    console.log('🧹 Manual cache clear requested');

    await this.performComprehensiveClear();
    this.updateVersionTracking();

    // Show user feedback
    console.log('✅ Manual cache clear completed');

    // Don't auto-reload, let user decide
    // User can manually refresh if needed
  }
}

// Auto-initialize disabled to prevent infinite reloads
// Manual initialization only through App.tsx
// if (typeof window !== 'undefined') {
//   setTimeout(() => {
//     CacheBuster.initialize();
//   }, 100);
// }
