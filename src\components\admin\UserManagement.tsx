
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";

import { UserList } from "@/components/admin/UserList";
import { UserForm } from "@/components/admin/UserForm";
import { CreateUserForm } from "@/components/admin/CreateUserForm";
import { UserDepartmentAssignment } from "@/components/admin/UserDepartmentAssignment";
import { DepartmentSyncPanel } from "@/components/admin/DepartmentSyncPanel";
import { CacheClearingPanel } from "@/components/admin/CacheClearingPanel";
import { useUserManagement } from "@/hooks/useUserManagement";
import { useToast } from "@/hooks/use-toast";

export const UserManagement = () => {
  const { toast } = useToast();
  const { users, departments, loading, updateUserRole, createDepartment, refetchUsers } = useUserManagement();
  const [selectedUser, setSelectedUser] = useState<any>(null);

  const handleEditUser = (user: any) => {
    setSelectedUser(user);
    // Switch to the manage user tab
    const tabsElement = document.querySelector('[role="tablist"]');
    const newTab = tabsElement?.querySelector('[value="new"]') as HTMLButtonElement;
    if (newTab) {
      newTab.click();
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    const userData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      role: formData.get('role') as string,
      department: formData.get('department') as string,
    };

    if (selectedUser) {
      // Update existing user role
      const result = await updateUserRole(
        selectedUser.id,
        userData.role,
        userData.department // Now this is already the department ID
      );
      
      if (result.success) {
        setSelectedUser(null);
        e.currentTarget.reset();
        await refetchUsers();
      }
    } else {
      toast({
        title: "Info",
        description: "User creation requires authentication setup",
        variant: "default",
      });
    }
  };

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  return (
    <Tabs defaultValue="users" className="space-y-6">
      <TabsList className="grid w-full grid-cols-2 md:grid-cols-5 gap-2">
        <TabsTrigger value="cache">Cache</TabsTrigger>
        <TabsTrigger value="sync">Dept Sync</TabsTrigger>
        <TabsTrigger value="users">Users ({users.length})</TabsTrigger>
        <TabsTrigger value="assignments">Assignments</TabsTrigger>
        <TabsTrigger value="new">Manage User</TabsTrigger>
      </TabsList>

      <TabsContent value="cache" className="space-y-4">
        <CacheClearingPanel />
      </TabsContent>

      <TabsContent value="sync" className="space-y-4">
        <DepartmentSyncPanel />
      </TabsContent>

      <TabsContent value="users" className="space-y-4">
        <UserList
          users={users.map(user => ({
            id: user.id,
            name: user.full_name || 'No Name',
            email: user.email || 'No Email',
            role: user.role || 'staff',
            department: user.department?.name || 'No Department',
            status: user.status || 'active',
            created_at: user.created_at,
            updated_at: user.updated_at
          }))}
          onEditUser={(user) => handleEditUser(users.find(u => u.id === user.id))}
          onUserUpdated={refetchUsers}
        />
      </TabsContent>

      <TabsContent value="assignments" className="space-y-4">
        <UserDepartmentAssignment />
      </TabsContent>

      <TabsContent value="new" className="space-y-4">
        <div className="space-y-4">
          {selectedUser ? (
            <>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Edit User: {selectedUser.full_name}</h3>
                  <p className="text-sm text-muted-foreground">
                    Update user information and settings. Changes will be saved immediately.
                  </p>
                </div>
                <Button variant="outline" onClick={() => setSelectedUser(null)}>
                  Back to Create
                </Button>
              </div>
              <UserForm
                defaultValues={{
                  id: selectedUser.id,
                  name: selectedUser.full_name || '',
                  email: selectedUser.email || '',
                  role: selectedUser.role || 'staff',
                  department: selectedUser.department_id || '',
                  phone: selectedUser.phone || '',
                  position: selectedUser.position || '',
                  location: selectedUser.location || '',
                  bio: selectedUser.bio || '',
                  status: selectedUser.status || 'active'
                }}
                onCancel={() => setSelectedUser(null)}
                onSuccess={() => {
                  setSelectedUser(null);
                  refetchUsers();
                }}
                departments={departments}
                isEditing={true}
              />
            </>
          ) : (
            <>
              <h3 className="text-lg font-semibold">Create New User Account</h3>
              <p className="text-sm text-muted-foreground">
                Create a new user account with proper authentication setup. The user will receive login credentials and can authenticate on first login.
              </p>
              <CreateUserForm
                departments={departments}
                onUserCreated={refetchUsers}
              />
            </>
          )}
        </div>
      </TabsContent>
    </Tabs>
  );
};
