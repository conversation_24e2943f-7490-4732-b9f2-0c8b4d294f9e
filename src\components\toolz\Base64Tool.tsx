import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Copy, RotateCcw, Lock, Unlock, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Base64Tool: React.FC = () => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('encode');
  const { toast } = useToast();

  const encodeBase64 = (text: string): string => {
    try {
      setError('');
      return btoa(unescape(encodeURIComponent(text)));
    } catch (err) {
      setError('Failed to encode text. Please check your input.');
      return '';
    }
  };

  const decodeBase64 = (text: string): string => {
    try {
      setError('');
      return decodeURIComponent(escape(atob(text)));
    } catch (err) {
      setError('Invalid Base64 string. Please check your input.');
      return '';
    }
  };

  const handleEncode = () => {
    const encoded = encodeBase64(inputText);
    setOutputText(encoded);
  };

  const handleDecode = () => {
    const decoded = decodeBase64(inputText);
    setOutputText(decoded);
  };

  const handleProcess = () => {
    if (activeTab === 'encode') {
      handleEncode();
    } else {
      handleDecode();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "Text copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy text",
        variant: "destructive",
      });
    }
  };

  const clearAll = () => {
    setInputText('');
    setOutputText('');
    setError('');
  };

  const swapInputOutput = () => {
    const temp = inputText;
    setInputText(outputText);
    setOutputText(temp);
    setActiveTab(activeTab === 'encode' ? 'decode' : 'encode');
    setError('');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-green-500 rounded-lg">
          <Lock className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">Base64 Encoder/Decoder</h2>
          <p className="text-muted-foreground">Encode and decode Base64 strings</p>
        </div>
      </div>

      {/* Mode Selection */}
      <Card>
        <CardContent className="p-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="encode" className="flex items-center gap-2">
                <Lock className="h-4 w-4" />
                Encode
              </TabsTrigger>
              <TabsTrigger value="decode" className="flex items-center gap-2">
                <Unlock className="h-4 w-4" />
                Decode
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </CardContent>
      </Card>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {activeTab === 'encode' ? 'Plain Text Input' : 'Base64 Input'}
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={swapInputOutput} disabled={!outputText}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Swap
              </Button>
              <Button variant="outline" size="sm" onClick={clearAll}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder={
              activeTab === 'encode' 
                ? "Enter plain text to encode..." 
                : "Enter Base64 string to decode..."
            }
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            className="min-h-[120px] font-mono"
          />
          <div className="flex items-center justify-between mt-2">
            <Badge variant="outline">
              {inputText.length} characters
            </Badge>
            <Button onClick={handleProcess} disabled={!inputText.trim()}>
              {activeTab === 'encode' ? 'Encode' : 'Decode'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Output Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {activeTab === 'encode' ? 'Base64 Output' : 'Plain Text Output'}
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(outputText)}
              disabled={!outputText}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 rounded-md p-3 min-h-[120px] font-mono text-sm">
            {outputText || (
              <span className="text-muted-foreground italic">
                {activeTab === 'encode' 
                  ? 'Encoded Base64 will appear here...' 
                  : 'Decoded text will appear here...'}
              </span>
            )}
          </div>
          {outputText && (
            <div className="flex items-center justify-between mt-2">
              <Badge variant="outline">
                {outputText.length} characters
              </Badge>
              <Badge variant="secondary">
                {activeTab === 'encode' ? 'Base64 Encoded' : 'Decoded'}
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>About Base64</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">What is Base64?</h4>
              <p className="text-muted-foreground mb-3">
                Base64 is a binary-to-text encoding scheme that represents binary data 
                in an ASCII string format by translating it into a radix-64 representation.
              </p>
              <h4 className="font-semibold mb-2">Common Uses:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Email attachments (MIME)</li>
                <li>• Data URLs in web development</li>
                <li>• API authentication tokens</li>
                <li>• Storing binary data in text format</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Character Set:</h4>
              <p className="text-muted-foreground mb-3">
                Base64 uses 64 characters: A-Z, a-z, 0-9, +, / and = for padding.
              </p>
              <h4 className="font-semibold mb-2">Features:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Safe for URLs and filenames</li>
                <li>• Reversible encoding</li>
                <li>• Increases data size by ~33%</li>
                <li>• Preserves binary data integrity</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Base64Tool;
