import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/components/auth/AuthProvider';
import { supabase } from '@/integrations/supabase/client';
import { safeSupabaseQuery, analyzeSupabaseError, detectCORSEnvironment } from '@/utils/supabaseErrorHandler';
import { JWTDebugPanel } from './JWTDebugPanel';
import { Loader2, Database, User, CheckCircle, AlertCircle, XCircle, Network, Shield } from 'lucide-react';

interface DebugResult {
  test: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  data?: any;
  error?: any;
}

export const DatabaseAccessDebug = () => {
  const { userProfile } = useAuth();
  const [results, setResults] = useState<DebugResult[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (result: DebugResult) => {
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testLeaveBalancesAccess = async () => {
    if (!userProfile?.id) {
      addResult({
        test: 'Leave Balances Access',
        status: 'warning',
        message: 'No user profile available'
      });
      return;
    }

    const currentYear = new Date().getFullYear();

    // Test using the enhanced error handler
    const result = await safeSupabaseQuery(
      () => supabase
        .from('leave_balances')
        .select('*')
        .eq('user_id', userProfile.id)
        .eq('year', currentYear),
      'leave_balances',
      userProfile.id,
      { enableFallback: true, logErrors: true }
    );

    if (result.error) {
      const errorAnalysis = result.error;
      addResult({
        test: 'Leave Balances Access',
        status: result.usedFallback ? 'warning' : 'error',
        message: result.usedFallback
          ? `${errorAnalysis.userMessage} (Using fallback data)`
          : errorAnalysis.userMessage,
        data: {
          analysis: errorAnalysis,
          fallbackUsed: result.usedFallback,
          data: result.data
        }
      });
    } else {
      addResult({
        test: 'Leave Balances Access',
        status: 'success',
        message: `Found ${Array.isArray(result.data) ? result.data.length : result.data ? 1 : 0} leave balance records`,
        data: result.data
      });
    }
  };

  const testCORSEnvironment = async () => {
    const corsInfo = detectCORSEnvironment();

    addResult({
      test: 'CORS Environment Analysis',
      status: corsInfo.mightHaveCORS ? 'warning' : 'success',
      message: corsInfo.isDevelopment
        ? 'Development environment - CORS should be minimal'
        : corsInfo.mightHaveCORS
          ? 'Production environment - verify CORS configuration'
          : 'Environment analysis complete',
      data: {
        environment: corsInfo,
        recommendations: corsInfo.recommendations
      }
    });
  };

  const testLeaveBalancesTableExists = async () => {
    try {
      const { data, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .eq('table_name', 'leave_balances');

      if (error) {
        addResult({
          test: 'Leave Balances Table Exists',
          status: 'error',
          message: `Error checking table: ${error.message}`,
          error: error
        });
      } else if (data && data.length > 0) {
        addResult({
          test: 'Leave Balances Table Exists',
          status: 'success',
          message: 'leave_balances table exists',
          data: data
        });
      } else {
        addResult({
          test: 'Leave Balances Table Exists',
          status: 'error',
          message: 'leave_balances table does not exist'
        });
      }
    } catch (error: any) {
      addResult({
        test: 'Leave Balances Table Exists',
        status: 'error',
        message: error.message,
        error: error
      });
    }
  };

  const testUserRole = async () => {
    if (!userProfile) {
      addResult({
        test: 'User Role Check',
        status: 'warning',
        message: 'No user profile available'
      });
      return;
    }

    addResult({
      test: 'User Role Check',
      status: 'success',
      message: `User role: ${userProfile.role}`,
      data: {
        id: userProfile.id,
        role: userProfile.role,
        email: userProfile.email,
        full_name: userProfile.full_name
      }
    });
  };

  const testRLSPolicies = async () => {
    try {
      const { data, error } = await supabase
        .from('pg_policies')
        .select('policyname, tablename, roles, cmd, qual')
        .eq('tablename', 'leave_balances');

      if (error) {
        addResult({
          test: 'RLS Policies Check',
          status: 'error',
          message: `Error checking RLS policies: ${error.message}`,
          error: error
        });
      } else {
        addResult({
          test: 'RLS Policies Check',
          status: 'success',
          message: `Found ${data?.length || 0} RLS policies for leave_balances`,
          data: data
        });
      }
    } catch (error: any) {
      addResult({
        test: 'RLS Policies Check',
        status: 'error',
        message: error.message,
        error: error
      });
    }
  };

  const testLocationService = async () => {
    try {
      // Test BigDataCloud API (CORS-friendly)
      const response = await fetch(
        'https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=9.060352&longitude=7.471104&localityLanguage=en'
      );

      if (response.ok) {
        const data = await response.json();
        addResult({
          test: 'Location Service (BigDataCloud)',
          status: 'success',
          message: 'Location service is working',
          data: data
        });
      } else {
        addResult({
          test: 'Location Service (BigDataCloud)',
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`
        });
      }
    } catch (error: any) {
      addResult({
        test: 'Location Service (BigDataCloud)',
        status: 'error',
        message: `CORS or network error: ${error.message}`,
        error: error
      });
    }
  };

  const createSampleLeaveBalance = async () => {
    if (!userProfile?.id) {
      addResult({
        test: 'Create Sample Leave Balance',
        status: 'warning',
        message: 'No user profile available'
      });
      return;
    }

    try {
      const currentYear = new Date().getFullYear();
      const { data, error } = await supabase
        .from('leave_balances')
        .upsert([{
          user_id: userProfile.id,
          leave_type: 'annual',
          year: currentYear,
          total_days: 25,
          used_days: 0
        }], {
          onConflict: 'user_id,leave_type,year'
        })
        .select();

      if (error) {
        addResult({
          test: 'Create Sample Leave Balance',
          status: 'error',
          message: `Error creating leave balance: ${error.message}`,
          error: error
        });
      } else {
        addResult({
          test: 'Create Sample Leave Balance',
          status: 'success',
          message: 'Successfully created/updated leave balance',
          data: data
        });
      }
    } catch (error: any) {
      addResult({
        test: 'Create Sample Leave Balance',
        status: 'error',
        message: error.message,
        error: error
      });
    }
  };

  const runAllTests = async () => {
    setLoading(true);
    clearResults();
    
    await testUserRole();
    await testCORSEnvironment();
    await testLeaveBalancesTableExists();
    await testRLSPolicies();
    await testLeaveBalancesAccess();
    await testLocationService();
    
    setLoading(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Database Access Debug Panel
        </CardTitle>
        <CardDescription>
          Debug database access issues, RLS policies, and external API calls
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button onClick={runAllTests} disabled={loading}>
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Run All Tests
          </Button>
          <Button variant="outline" onClick={testLeaveBalancesAccess}>
            Test Leave Balances
          </Button>
          <Button variant="outline" onClick={testCORSEnvironment}>
            Test CORS Environment
          </Button>
          <Button variant="outline" onClick={testLocationService}>
            Test Location Service
          </Button>
          <Button variant="outline" onClick={createSampleLeaveBalance}>
            Create Sample Data
          </Button>
          <Button variant="outline" onClick={clearResults}>
            Clear Results
          </Button>
        </div>

        {userProfile && (
          <div className="p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2 text-sm">
              <User className="h-4 w-4" />
              <span>Current User: {userProfile.full_name} ({userProfile.role})</span>
              <Badge variant="outline">{userProfile.id}</Badge>
            </div>
          </div>
        )}

        <div className="space-y-2">
          {results.map((result, index) => (
            <div key={index} className="p-3 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(result.status)}
                <span className="font-medium">{result.test}</span>
                <Badge variant={result.status === 'success' ? 'default' : result.status === 'error' ? 'destructive' : 'secondary'}>
                  {result.status}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
              {result.data && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-blue-600 hover:text-blue-800">Show data</summary>
                  <pre className="mt-2 bg-muted p-2 rounded overflow-auto max-h-40">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
              {result.error && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-red-600 hover:text-red-800">Show error details</summary>
                  <pre className="mt-2 bg-red-50 p-2 rounded overflow-auto max-h-40">
                    {JSON.stringify(result.error, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
