-- Comprehensive File Upload and Document Management Schema
-- This script creates all necessary tables for file uploads, attachments, and document management

-- Create file_uploads table for storing file metadata
CREATE TABLE IF NOT EXISTS public.file_uploads (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    original_filename TEXT NOT NULL,
    stored_filename TEXT NOT NULL UNIQUE,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    file_extension TEXT,
    upload_type TEXT NOT NULL CHECK (upload_type IN ('report_attachment', 'memo_attachment', 'profile_image', 'project_file', 'general_document')),
    uploaded_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    upload_status TEXT DEFAULT 'uploaded' CHECK (upload_status IN ('uploading', 'uploaded', 'processing', 'failed', 'deleted')),
    file_hash TEXT, -- For duplicate detection
    download_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT false,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_attachments table for linking files to various entities
CREATE TABLE IF NOT EXISTS public.document_attachments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_upload_id UUID REFERENCES public.file_uploads(id) ON DELETE CASCADE,
    entity_type TEXT NOT NULL CHECK (entity_type IN ('report', 'memo', 'project', 'task', 'user_profile', 'department', 'expense_report', 'invoice')),
    entity_id UUID NOT NULL,
    attachment_type TEXT DEFAULT 'supporting_document' CHECK (attachment_type IN ('supporting_document', 'evidence', 'receipt', 'image', 'video', 'audio', 'spreadsheet', 'presentation')),
    description TEXT,
    is_required BOOLEAN DEFAULT false,
    display_order INTEGER DEFAULT 0,
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create memos table for memo management
CREATE TABLE IF NOT EXISTS public.memos (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    memo_type TEXT DEFAULT 'general' CHECK (memo_type IN ('general', 'urgent', 'policy', 'announcement', 'meeting_notes', 'directive', 'reminder')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived', 'deleted')),
    visibility TEXT DEFAULT 'department' CHECK (visibility IN ('public', 'department', 'role_specific', 'private')),
    target_audience JSONB DEFAULT '{}', -- Store roles, departments, or specific users
    author_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id),
    effective_date DATE,
    expiry_date DATE,
    is_pinned BOOLEAN DEFAULT false,
    read_count INTEGER DEFAULT 0,
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create memo_reads table for tracking who has read memos
CREATE TABLE IF NOT EXISTS public.memo_reads (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    memo_id UUID REFERENCES public.memos(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(memo_id, user_id)
);

-- Create file_access_logs table for audit trail
CREATE TABLE IF NOT EXISTS public.file_access_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_upload_id UUID REFERENCES public.file_uploads(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    access_type TEXT NOT NULL CHECK (access_type IN ('view', 'download', 'delete', 'share')),
    ip_address INET,
    user_agent TEXT,
    access_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create file_shares table for file sharing functionality
CREATE TABLE IF NOT EXISTS public.file_shares (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_upload_id UUID REFERENCES public.file_uploads(id) ON DELETE CASCADE,
    shared_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    shared_with UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    share_type TEXT DEFAULT 'view' CHECK (share_type IN ('view', 'download', 'edit')),
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create storage_buckets table for organizing files
CREATE TABLE IF NOT EXISTS public.storage_buckets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bucket_name TEXT NOT NULL UNIQUE,
    bucket_type TEXT NOT NULL CHECK (bucket_type IN ('reports', 'memos', 'projects', 'profiles', 'general')),
    description TEXT,
    max_file_size BIGINT DEFAULT 10485760, -- 10MB default
    allowed_mime_types TEXT[],
    is_public BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.file_uploads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memos ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.memo_reads ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.file_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.storage_buckets ENABLE ROW LEVEL SECURITY;

-- RLS Policies for file_uploads
CREATE POLICY "Users can view own uploaded files" ON public.file_uploads FOR SELECT USING (
    auth.uid() = uploaded_by OR is_public = true
);

CREATE POLICY "Users can upload files" ON public.file_uploads FOR INSERT WITH CHECK (
    auth.uid() = uploaded_by
);

CREATE POLICY "Users can update own files" ON public.file_uploads FOR UPDATE USING (
    auth.uid() = uploaded_by
);

CREATE POLICY "Admins can manage all files" ON public.file_uploads FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

-- RLS Policies for document_attachments
CREATE POLICY "Users can view attachments for accessible entities" ON public.document_attachments FOR SELECT USING (
    auth.uid() = created_by OR
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

CREATE POLICY "Users can create attachments" ON public.document_attachments FOR INSERT WITH CHECK (
    auth.uid() = created_by
);

-- RLS Policies for memos
CREATE POLICY "Users can view published memos based on visibility" ON public.memos FOR SELECT USING (
    status = 'published' AND (
        visibility = 'public' OR
        auth.uid() = author_id OR
        (visibility = 'department' AND department_id IN (
            SELECT department_id FROM public.profiles WHERE id = auth.uid()
        )) OR
        (visibility = 'role_specific' AND EXISTS (
            SELECT 1 FROM public.profiles p 
            WHERE p.id = auth.uid() AND p.role = ANY(
                SELECT jsonb_array_elements_text(target_audience->'roles')
            )
        )) OR
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'manager')
        )
    )
);

CREATE POLICY "Users can create memos" ON public.memos FOR INSERT WITH CHECK (
    auth.uid() = author_id
);

CREATE POLICY "Authors can update own memos" ON public.memos FOR UPDATE USING (
    auth.uid() = author_id
);

-- RLS Policies for memo_reads
CREATE POLICY "Users can track own memo reads" ON public.memo_reads FOR ALL USING (
    auth.uid() = user_id
);

-- RLS Policies for file_access_logs
CREATE POLICY "Users can view own access logs" ON public.file_access_logs FOR SELECT USING (
    auth.uid() = user_id
);

CREATE POLICY "System can create access logs" ON public.file_access_logs FOR INSERT WITH CHECK (true);

-- RLS Policies for file_shares
CREATE POLICY "Users can view shares involving them" ON public.file_shares FOR SELECT USING (
    auth.uid() = shared_by OR auth.uid() = shared_with
);

CREATE POLICY "Users can create shares for own files" ON public.file_shares FOR INSERT WITH CHECK (
    auth.uid() = shared_by
);

-- RLS Policies for storage_buckets
CREATE POLICY "Users can view storage buckets" ON public.storage_buckets FOR SELECT USING (true);

CREATE POLICY "Admins can manage storage buckets" ON public.storage_buckets FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin')
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_file_uploads_uploaded_by ON public.file_uploads(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_file_uploads_upload_type ON public.file_uploads(upload_type);
CREATE INDEX IF NOT EXISTS idx_file_uploads_mime_type ON public.file_uploads(mime_type);
CREATE INDEX IF NOT EXISTS idx_file_uploads_created_at ON public.file_uploads(created_at);
CREATE INDEX IF NOT EXISTS idx_document_attachments_entity ON public.document_attachments(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_document_attachments_file_id ON public.document_attachments(file_upload_id);
CREATE INDEX IF NOT EXISTS idx_memos_author_id ON public.memos(author_id);
CREATE INDEX IF NOT EXISTS idx_memos_department_id ON public.memos(department_id);
CREATE INDEX IF NOT EXISTS idx_memos_status ON public.memos(status);
CREATE INDEX IF NOT EXISTS idx_memos_visibility ON public.memos(visibility);
CREATE INDEX IF NOT EXISTS idx_memos_created_at ON public.memos(created_at);
CREATE INDEX IF NOT EXISTS idx_memo_reads_memo_id ON public.memo_reads(memo_id);
CREATE INDEX IF NOT EXISTS idx_memo_reads_user_id ON public.memo_reads(user_id);
CREATE INDEX IF NOT EXISTS idx_file_access_logs_file_id ON public.file_access_logs(file_upload_id);
CREATE INDEX IF NOT EXISTS idx_file_access_logs_user_id ON public.file_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_file_shares_file_id ON public.file_shares(file_upload_id);
CREATE INDEX IF NOT EXISTS idx_file_shares_shared_with ON public.file_shares(shared_with);

-- Create functions for file management
CREATE OR REPLACE FUNCTION get_file_download_url(file_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    file_path TEXT;
    file_name TEXT;
BEGIN
    SELECT f.file_path, f.stored_filename
    INTO file_path, file_name
    FROM public.file_uploads f
    WHERE f.id = file_id;
    
    IF file_path IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Update download count
    UPDATE public.file_uploads 
    SET download_count = download_count + 1 
    WHERE id = file_id;
    
    -- Log access
    INSERT INTO public.file_access_logs (file_upload_id, user_id, access_type)
    VALUES (file_id, auth.uid(), 'download');
    
    RETURN file_path;
END;
$$;

-- Create function to get memo statistics
CREATE OR REPLACE FUNCTION get_memo_statistics(memo_uuid UUID)
RETURNS TABLE (
    total_readers INTEGER,
    read_percentage DECIMAL,
    recent_readers INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(mr.user_id)::INTEGER as total_readers,
        CASE 
            WHEN target_count.total > 0 
            THEN (COUNT(mr.user_id)::DECIMAL / target_count.total * 100)
            ELSE 0
        END as read_percentage,
        COUNT(CASE WHEN mr.read_at > NOW() - INTERVAL '24 hours' THEN 1 END)::INTEGER as recent_readers
    FROM public.memo_reads mr
    RIGHT JOIN public.memos m ON m.id = memo_uuid
    LEFT JOIN (
        SELECT 
            CASE 
                WHEN m2.visibility = 'public' THEN (SELECT COUNT(*) FROM public.profiles WHERE status = 'active')
                WHEN m2.visibility = 'department' THEN (SELECT COUNT(*) FROM public.profiles WHERE department_id = m2.department_id AND status = 'active')
                ELSE 1
            END as total
        FROM public.memos m2 
        WHERE m2.id = memo_uuid
    ) target_count ON true
    WHERE mr.memo_id = memo_uuid OR mr.memo_id IS NULL
    GROUP BY target_count.total;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_file_download_url(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_memo_statistics(UUID) TO authenticated;
