
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";

export const useFinancialData = (dateRange: string = "30") => {
  const { data: invoices, isLoading: invoicesLoading } = useQuery({
    queryKey: ['financial-invoices', dateRange],
    queryFn: async () => {
      const days = parseInt(dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('invoices')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
  });

  const { data: expenses, isLoading: expensesLoading } = useQuery({
    queryKey: ['financial-expenses', dateRange],
    queryFn: async () => {
      const days = parseInt(dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('expense_reports')
        .select('*')
        .gte('expense_date', startDate.toISOString().split('T')[0])
        .order('expense_date', { ascending: false });

      if (error) throw error;
      return data || [];
    },
  });

  const { data: accountsInvoices, isLoading: accountsLoading } = useQuery({
    queryKey: ['accounts-invoices', dateRange],
    queryFn: async () => {
      const days = parseInt(dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('accounts_invoices')
        .select('*')
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
  });

  const { data: financialSummary, isLoading: summaryLoading } = useQuery({
    queryKey: ['financial-summary', dateRange],
    queryFn: async () => {
      const days = parseInt(dateRange);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .rpc('get_financial_summary', {
          p_start_date: startDate.toISOString().split('T')[0],
          p_end_date: new Date().toISOString().split('T')[0]
        });

      if (error) throw error;
      return data?.[0] || {
        total_revenue: 0,
        total_expenses: 0,
        net_profit: 0,
        paid_invoices_count: 0,
        pending_invoices_count: 0,
        approved_expenses_count: 0,
        pending_expenses_count: 0
      };
    },
  });

  const isLoading = invoicesLoading || expensesLoading || accountsLoading || summaryLoading;

  return {
    invoices: invoices || [],
    expenses: expenses || [],
    accountsInvoices: accountsInvoices || [],
    financialSummary,
    isLoading
  };
};
