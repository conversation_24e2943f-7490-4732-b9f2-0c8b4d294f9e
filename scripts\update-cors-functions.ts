#!/usr/bin/env -S deno run --allow-read --allow-write

/**
 * <PERSON><PERSON><PERSON> to update all Supabase Edge Functions to use the new CORS configuration
 * This ensures ai.ctnigeria.com is properly allowed for all function calls
 */

import { walk } from "https://deno.land/std@0.190.0/fs/walk.ts";

const FUNCTIONS_DIR = "./supabase/functions";

// Functions that should be updated (excluding _shared)
const FUNCTION_DIRS = [
  "admin-auth",
  "advanced-document-analysis", 
  "ai-agent-executor",
  "ai-agent-intent",
  "ai-agent-response",
  "ai-file-analyzer",
  "ai-system-orchestrator",
  "analyze-and-notify",
  "analyze-content",
  "analyze-document",
  "enhanced-rag-search",
  "generate-report",
  "index-documents",
  "integration-manager",
  "payment-notification"
];

async function updateFunctionCors(functionPath: string) {
  console.log(`🔧 Updating CORS for: ${functionPath}`);
  
  try {
    const content = await Deno.readTextFile(functionPath);
    
    // Check if already updated
    if (content.includes('withCors') || content.includes('createCorsResponse')) {
      console.log(`   ✅ Already updated: ${functionPath}`);
      return;
    }
    
    let updatedContent = content;
    
    // Update imports
    if (content.includes('import { serve }')) {
      updatedContent = updatedContent.replace(
        /import { serve } from [^;]+;/,
        'import { serve } from "https://deno.land/std@0.190.0/http/server.ts";\nimport { withCors, createCorsResponse } from "../_shared/cors.ts";'
      );
    }
    
    // Remove old CORS headers definition
    updatedContent = updatedContent.replace(
      /const corsHeaders = \{[^}]+\};?\n?/g,
      ''
    );
    
    // Update serve function to use withCors wrapper
    updatedContent = updatedContent.replace(
      /serve\(async \(req\) => \{[\s\S]*?if \(req\.method === ['""]OPTIONS['"]\) \{[\s\S]*?return new Response\([^}]+\}[^}]*\);[\s\S]*?\}/,
      'serve(withCors(async (req) => {'
    );
    
    // Update Response returns to use createCorsResponse
    updatedContent = updatedContent.replace(
      /return new Response\(\s*JSON\.stringify\(([^)]+)\),\s*\{\s*(?:status:\s*(\d+),\s*)?headers:\s*\{[^}]*corsHeaders[^}]*\}\s*\}\s*\);?/g,
      (match, data, status) => {
        const statusCode = status ? `, ${status}` : '';
        return `return createCorsResponse(${data}, req${statusCode});`;
      }
    );
    
    // Close the withCors wrapper at the end
    updatedContent = updatedContent.replace(
      /(\s+)\}\);?\s*$/,
      '$1}));'
    );
    
    // Write updated content
    await Deno.writeTextFile(functionPath, updatedContent);
    console.log(`   ✅ Updated: ${functionPath}`);
    
  } catch (error) {
    console.error(`   ❌ Failed to update ${functionPath}:`, error.message);
  }
}

async function main() {
  console.log('🚀 Updating Supabase Edge Functions CORS Configuration\n');
  console.log('=' .repeat(60));
  
  console.log('📋 Functions to update:');
  FUNCTION_DIRS.forEach(dir => console.log(`   • ${dir}`));
  console.log('');
  
  for (const functionDir of FUNCTION_DIRS) {
    const functionPath = `${FUNCTIONS_DIR}/${functionDir}/index.ts`;
    
    try {
      // Check if file exists
      await Deno.stat(functionPath);
      await updateFunctionCors(functionPath);
    } catch (error) {
      if (error instanceof Deno.errors.NotFound) {
        console.log(`   ⚠️  File not found: ${functionPath}`);
      } else {
        console.error(`   ❌ Error processing ${functionPath}:`, error.message);
      }
    }
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 CORS Update Complete!');
  
  console.log('\n✅ CORS Configuration Summary:');
  console.log('• Production domain ai.ctnigeria.com is now allowed');
  console.log('• All functions use centralized CORS configuration');
  console.log('• Proper origin validation implemented');
  console.log('• CORS preflight requests handled automatically');
  
  console.log('\n🚀 Next Steps:');
  console.log('1. Deploy functions to Supabase: supabase functions deploy');
  console.log('2. Test function calls from ai.ctnigeria.com');
  console.log('3. Verify no CORS errors in browser console');
  console.log('4. Monitor function performance and error rates');
}

if (import.meta.main) {
  main().catch((error) => {
    console.error('💥 Script failed:', error);
    Deno.exit(1);
  });
}
