<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Correct Schema Fix</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        .warning {
            color: #856404;
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .log {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            border: 1px solid #dee2e6;
        }
        .progress {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.5s ease;
            border-radius: 4px;
        }
        .center {
            text-align: center;
        }
        .schema-fix {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Final Correct Schema Fix</h1>
            <p>Uses the exact column names and constraints from your database</p>
        </div>
        
        <div class="schema-fix">
            <h4>🎯 Correct Schema Mappings:</h4>
            <ul>
                <li><strong>Reports:</strong> report_type must be 'general', 'weekly', 'monthly', 'project', 'incident', 'maintenance', 'financial', 'safety', or 'quality'</li>
                <li><strong>Time Logs:</strong> Uses clock_in, clock_out, total_hours (not hours_worked)</li>
                <li><strong>Invoices:</strong> Requires subtotal field (not just amount)</li>
                <li><strong>Notifications:</strong> Uses is_read (not read)</li>
                <li><strong>Expense Reports:</strong> Already working with amount field</li>
            </ul>
        </div>
        
        <div class="progress">
            <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
        </div>
        
        <div id="status"></div>
        
        <div class="center">
            <button id="fixBtn" class="button" onclick="runFinalFix()">
                ✅ Apply Final Correct Fix
            </button>
            
            <button id="dashboardBtn" class="button" onclick="openDashboard()" disabled>
                📊 Open Dashboard
            </button>
        </div>
        
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type;
            statusDiv.textContent = message;
        }
        
        function updateProgress(percent) {
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percent + '%';
        }
        
        async function runFinalFix() {
            const fixBtn = document.getElementById('fixBtn');
            const dashboardBtn = document.getElementById('dashboardBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Applying Final Fix...';
            
            try {
                log('✅ Starting final correct schema fix...');
                updateProgress(10);
                
                const { data: { user } } = await supabase.auth.getUser();
                if (!user) throw new Error('Please log in first');
                
                const userId = user.id;
                log('✅ User authenticated: ' + user.email);
                updateProgress(20);
                
                let totalInserted = 0;
                
                // Fix 1: Reports with correct report_type values
                log('🔧 Fixing reports with valid report_type values...');
                try {
                    const reportData = [
                        {
                            title: 'Weekly Progress Report',
                            description: 'Weekly team progress and achievements',
                            report_type: 'weekly',  // Valid value from constraint
                            submitted_by: userId
                        },
                        {
                            title: 'Monthly Financial Summary',
                            description: 'Monthly financial performance analysis',
                            report_type: 'monthly',  // Valid value from constraint
                            submitted_by: userId
                        },
                        {
                            title: 'Project Status Update',
                            description: 'Current project milestones and progress',
                            report_type: 'project',  // Valid value from constraint
                            submitted_by: userId
                        },
                        {
                            title: 'General Operations Report',
                            description: 'General operational activities and updates',
                            report_type: 'general',  // Valid value from constraint
                            submitted_by: userId
                        }
                    ];
                    
                    const { data: reportResult, error: reportError } = await supabase
                        .from('reports')
                        .insert(reportData)
                        .select();
                    
                    if (reportError) {
                        log(`⚠️ Reports: ${reportError.message}`);
                    } else {
                        log(`✅ Inserted ${reportResult?.length || 0} reports with valid report_type`);
                        totalInserted += reportResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Reports error: ${e.message}`);
                }
                
                updateProgress(35);
                
                // Fix 2: Time logs with correct column structure (clock_in/clock_out)
                log('🔧 Fixing time logs with clock_in/clock_out structure...');
                try {
                    const now = new Date();
                    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    const twoDaysAgo = new Date(now.getTime() - 48 * 60 * 60 * 1000);
                    
                    const timeLogData = [
                        {
                            user_id: userId,
                            clock_in: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 9, 0).toISOString(),
                            clock_out: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 17, 0).toISOString(),
                            notes: 'Dashboard development work'
                        },
                        {
                            user_id: userId,
                            clock_in: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 9, 30).toISOString(),
                            clock_out: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 16, 30).toISOString(),
                            notes: 'Bug fixes and testing'
                        },
                        {
                            user_id: userId,
                            clock_in: new Date(twoDaysAgo.getFullYear(), twoDaysAgo.getMonth(), twoDaysAgo.getDate(), 10, 0).toISOString(),
                            clock_out: new Date(twoDaysAgo.getFullYear(), twoDaysAgo.getMonth(), twoDaysAgo.getDate(), 18, 0).toISOString(),
                            notes: 'Client meetings and project planning'
                        }
                    ];
                    
                    const { data: timeLogResult, error: timeLogError } = await supabase
                        .from('time_logs')
                        .insert(timeLogData)
                        .select();
                    
                    if (timeLogError) {
                        log(`⚠️ Time logs: ${timeLogError.message}`);
                    } else {
                        log(`✅ Inserted ${timeLogResult?.length || 0} time logs with clock_in/clock_out`);
                        totalInserted += timeLogResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Time logs error: ${e.message}`);
                }
                
                updateProgress(50);
                
                // Fix 3: Invoices with required subtotal field
                log('🔧 Fixing invoices with required subtotal field...');
                try {
                    const invoiceData = [
                        {
                            invoice_number: 'INV-' + Date.now() + '-001',
                            client_name: 'Acme Corporation',
                            amount: 5000.00,
                            total_amount: 5000.00,
                            subtotal: 5000.00,  // Required field
                            created_by: userId
                        },
                        {
                            invoice_number: 'INV-' + Date.now() + '-002',
                            client_name: 'Tech Solutions Ltd',
                            amount: 7500.00,
                            total_amount: 7500.00,
                            subtotal: 7500.00,  // Required field
                            created_by: userId
                        },
                        {
                            invoice_number: 'INV-' + Date.now() + '-003',
                            client_name: 'Digital Dynamics Inc',
                            amount: 3200.00,
                            total_amount: 3200.00,
                            subtotal: 3200.00,  // Required field
                            created_by: userId
                        }
                    ];
                    
                    const { data: invoiceResult, error: invoiceError } = await supabase
                        .from('invoices')
                        .insert(invoiceData)
                        .select();
                    
                    if (invoiceError) {
                        log(`⚠️ Invoices: ${invoiceError.message}`);
                    } else {
                        log(`✅ Inserted ${invoiceResult?.length || 0} invoices with subtotal`);
                        totalInserted += invoiceResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Invoices error: ${e.message}`);
                }
                
                updateProgress(70);
                
                // Fix 4: Notifications with is_read field (not read)
                log('🔧 Fixing notifications with is_read field...');
                try {
                    const notificationData = [
                        {
                            user_id: userId,
                            title: 'Dashboard Setup Complete',
                            message: 'Your dashboard has been successfully configured with sample data!',
                            type: 'success',
                            is_read: false  // Correct field name
                        },
                        {
                            user_id: userId,
                            title: 'Data Import Successful',
                            message: 'All sample data has been imported successfully',
                            type: 'info',
                            is_read: false  // Correct field name
                        },
                        {
                            user_id: userId,
                            title: 'System Ready',
                            message: 'All dashboard components are now functional and ready to use',
                            type: 'success',
                            is_read: false  // Correct field name
                        }
                    ];
                    
                    const { data: notificationResult, error: notificationError } = await supabase
                        .from('notifications')
                        .insert(notificationData)
                        .select();
                    
                    if (notificationError) {
                        log(`⚠️ Notifications: ${notificationError.message}`);
                    } else {
                        log(`✅ Inserted ${notificationResult?.length || 0} notifications with is_read`);
                        totalInserted += notificationResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Notifications error: ${e.message}`);
                }
                
                updateProgress(85);
                
                // Fix 5: Additional expense reports (these were already working)
                log('🔧 Adding more expense reports...');
                try {
                    const expenseData = [
                        {
                            title: 'Training and Development',
                            description: 'Professional development courses and certifications',
                            amount: 800.00,
                            category: 'training',
                            submitted_by: userId,
                            expense_date: new Date().toISOString().split('T')[0]
                        },
                        {
                            title: 'Client Entertainment',
                            description: 'Business lunch with potential clients',
                            amount: 250.00,
                            category: 'meals',
                            submitted_by: userId,
                            expense_date: new Date(Date.now() - 86400000).toISOString().split('T')[0]
                        }
                    ];
                    
                    const { data: expenseResult, error: expenseError } = await supabase
                        .from('expense_reports')
                        .insert(expenseData)
                        .select();
                    
                    if (expenseError) {
                        log(`⚠️ Additional expense reports: ${expenseError.message}`);
                    } else {
                        log(`✅ Inserted ${expenseResult?.length || 0} additional expense reports`);
                        totalInserted += expenseResult?.length || 0;
                    }
                } catch (e) {
                    log(`⚠️ Additional expense reports error: ${e.message}`);
                }
                
                updateProgress(100);
                
                log(`🎉 Final correct schema fix completed!`);
                log(`📊 Total records inserted: ${totalInserted}`);
                
                if (totalInserted > 0) {
                    showStatus(`🎉 Successfully inserted ${totalInserted} records with correct schema! Dashboard should now display comprehensive data.`, 'success');
                    dashboardBtn.disabled = false;
                } else {
                    showStatus('⚠️ No new records inserted. All data may already exist or there are schema issues.', 'warning');
                }
                
            } catch (error) {
                log('❌ Final schema fix failed: ' + error.message);
                showStatus('❌ Fix failed: ' + error.message, 'error');
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '✅ Apply Final Correct Fix';
            }
        }
        
        function openDashboard() {
            window.open('/', '_blank');
        }
        
        // Make functions global
        window.runFinalFix = runFinalFix;
        window.openDashboard = openDashboard;
    </script>
</body>
</html>
