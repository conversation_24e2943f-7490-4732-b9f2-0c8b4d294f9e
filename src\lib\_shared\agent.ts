import { Tool } from "@langchain/core/tools";
import { AgentExecutor, createOpenAIFunctionsAgent } from "langchain/agents";
import {
  ChatPromptTemplate,
  MessagesPlaceholder,
} from "@langchain/core/prompts";
import { AIMessage } from "@langchain/core/messages";
import type { BaseChatModel } from "@langchain/core/language_models/chat_models";
import { langChainConfig } from "./config";
import { ragSystem } from "./rag-system";
import { supabase } from "../supabase";

// 1. Database Query Tool
export class DatabaseQueryTool extends Tool {
  name = "database_query";
  description = "Query the Supabase database for users, reports, memos";

  async _call(input: string): Promise<string> {
    try {
      const query = input.toLowerCase();

      const table =
        query.includes("user") || query.includes("profile")
          ? "profiles"
          : query.includes("report")
            ? "reports"
            : query.includes("memo")
              ? "memos"
              : null;

      if (!table) return "Specify a valid table: users, reports, or memos.";

      const { data, error } = await supabase.from(table).select("*").limit(10);
      if (error) throw error;

      return JSON.stringify(data, null, 2);
    } catch (error) {
      return `Database query error: ${String(error)}`;
    }
  }
}

// 2. Document Analysis Tool
export class DocumentAnalysisTool extends Tool {
  name = "document_analysis";
  description = "Use RAG to analyze documents and extract insights";

  async _call(input: string): Promise<string> {
    try {
      const response = await ragSystem.query({
        question: input,
        maxResults: 5,
      });
      return `📘 Answer: ${response.answer}\n🔗 Sources: ${response.sources.length}\n📊 Confidence: ${(response.confidence * 100).toFixed(1)}%`;
    } catch (error) {
      return `Document analysis error: ${String(error)}`;
    }
  }
}

// 3. Report Generation Tool
export class ReportGenerationTool extends Tool {
  name = "report_generation";
  description = "Generate summary reports from system data";

  async _call(input: string): Promise<string> {
    try {
      const type = input.toLowerCase();
      if (type.includes("user")) return await this.generateUserReport();
      if (type.includes("activity")) return await this.generateActivityReport();
      if (type.includes("performance"))
        return await this.generatePerformanceReport();

      return "Specify a report type: user, activity, or performance.";
    } catch (error) {
      return `Report generation error: ${String(error)}`;
    }
  }

  private async generateUserReport(): Promise<string> {
    const { data: users, error } = await supabase
      .from("profiles")
      .select("role, created_at")
      .order("created_at", { ascending: false });

    if (error) throw error;

    const roleStats =
      users?.reduce(
        (acc, user) => {
          acc[user.role] = (acc[user.role] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ) || {};

    return `👥 User Report
- Total Users: ${users?.length || 0}
- Role Breakdown: ${JSON.stringify(roleStats, null, 2)}
- Last Registered: ${users?.[0]?.created_at || "N/A"}`;
  }

  private async generateActivityReport(): Promise<string> {
    return "Activity Report: Placeholder - implement from logs.";
  }

  private async generatePerformanceReport(): Promise<string> {
    return "Performance Report: Placeholder - implement from metrics.";
  }
}

// 4. System Management Tool
export class SystemManagementTool extends Tool {
  name = "system_management";
  description = "Access system status and configuration info";

  async _call(input: string): Promise<string> {
    try {
      const command = input.toLowerCase();
      if (command.includes("status") || command.includes("health"))
        return this.getSystemStatus();
      if (command.includes("config") || command.includes("setting"))
        return this.getSystemConfig();

      return "Commands: status | health | config | settings";
    } catch (error) {
      return `System management error: ${String(error)}`;
    }
  }

  private getSystemStatus(): string {
    const config = langChainConfig.getConfig();
    const validation = langChainConfig.validateConfig();

    return `🛠️ System Status:
✔ Config Valid: ${validation.isValid}
🔍 Vector Store: ${config.vectorStore.provider}
🧠 Model: ${config.openai.apiKey ? "OpenAI" : config.anthropic.apiKey ? "Anthropic" : "None"}
🧠 Memory: ${config.memory.type}
⚠ Errors: ${validation.errors.join(", ") || "None"}`;
  }

  private getSystemConfig(): string {
    const config = langChainConfig.getConfig();
    return `🔧 Config:
- Chunk Size: ${config.rag.chunkSize}
- Top K: ${config.rag.topK}
- Max Tokens: ${config.memory.maxTokens}
- Max Iterations: ${config.agent.maxIterations}`;
  }
}

// 5. Main Agent Class
export class AIWorkboardAgent {
  private agent: AgentExecutor | null = null;
  private tools: Tool[];
  private model: BaseChatModel;

  constructor(model?: BaseChatModel) {
    this.model = model || langChainConfig.getDefaultModel();
    this.tools = [
      new DatabaseQueryTool(),
      new DocumentAnalysisTool(),
      new ReportGenerationTool(),
      new SystemManagementTool(),
    ];
  }

  public async initialize(): Promise<void> {
    const prompt = ChatPromptTemplate.fromMessages([
      ["system", this.getSystemPrompt()],
      ["human", "{input}"],
      new MessagesPlaceholder("agent_scratchpad"),
    ]);

    const agent = await createOpenAIFunctionsAgent({
      llm: this.model,
      tools: this.tools,
      prompt,
    });

    this.agent = new AgentExecutor({
      agent,
      tools: this.tools,
      maxIterations: langChainConfig.getConfig().agent.maxIterations,
      verbose: langChainConfig.getConfig().agent.verbose,
    });

    console.info("[Agent] AI Workboard Agent initialized.");
  }

  public async execute(input: string): Promise<string> {
    if (!this.agent) await this.initialize();
    if (!this.agent) throw new Error("Agent initialization failed");

    const result = await this.agent.invoke({ input });
    return result.output;
  }

  public async *streamExecute(
    input: string
  ): AsyncGenerator<string, void, unknown> {
    if (!this.agent) await this.initialize();
    if (!this.agent) throw new Error("Agent not initialized");

    const stream = await this.agent.streamLog({ input });

    for await (const chunk of stream) {
      for (const op of chunk.ops || []) {
        if (op.op === "add" && op.path?.includes("messages")) {
          const message = op.value;
          if (message instanceof AIMessage) yield message.content as string;
        }
      }
    }
  }

  public addTool(tool: Tool): void {
    this.tools.push(tool);
    this.agent = null;
  }

  private getSystemPrompt(): string {
    return `You are an AI assistant for the CTNL AI Workboard system with tools for:
- 📂 Data Queries (users, reports, memos)
- 📑 Document Analysis (RAG)
- 📊 Report Generation (user, activity)
- ⚙ System Management (status, config)

Be helpful, secure, accurate. Prioritize clarity and user privacy.`;
  }
}

export const aiWorkboardAgent = new AIWorkboardAgent();
