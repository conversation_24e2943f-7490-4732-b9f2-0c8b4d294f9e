#!/bin/bash

# Bash script to install dependencies and start the development server
# Run this script from the project root directory

echo "🚀 AI CTNL Dashboard - Install Dependencies and Start Server"
echo "================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# Check if Node.js is installed
echo -e "${YELLOW}📋 Checking Node.js installation...${NC}"
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"
else
    echo -e "${RED}❌ Node.js is not installed. Please install Node.js 20+ first.${NC}"
    echo -e "${YELLOW}Download from: https://nodejs.org/${NC}"
    exit 1
fi

# Check if npm is installed
echo -e "${YELLOW}📋 Checking npm installation...${NC}"
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm version: $NPM_VERSION${NC}"
else
    echo -e "${RED}❌ npm is not installed.${NC}"
    exit 1
fi

# Clear cache and clean install
echo -e "${YELLOW}🧹 Clearing cache and cleaning up...${NC}"

if [ -d "node_modules" ]; then
    rm -rf node_modules
    echo -e "${GREEN}✅ Removed node_modules${NC}"
fi

if [ -f "package-lock.json" ]; then
    rm -f package-lock.json
    echo -e "${GREEN}✅ Removed package-lock.json${NC}"
fi

if [ -d ".vite" ]; then
    rm -rf .vite
    echo -e "${GREEN}✅ Removed .vite cache${NC}"
fi

if [ -d "dist" ]; then
    rm -rf dist
    echo -e "${GREEN}✅ Removed dist folder${NC}"
fi

# Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
echo -e "${WHITE}This may take a few minutes...${NC}"

if npm install; then
    echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    echo -e "${YELLOW}Trying with --legacy-peer-deps...${NC}"
    if npm install --legacy-peer-deps; then
        echo -e "${GREEN}✅ Dependencies installed with legacy peer deps${NC}"
    else
        echo -e "${RED}❌ Failed to install dependencies even with legacy peer deps${NC}"
        echo -e "${YELLOW}Please check your internet connection and try again.${NC}"
        exit 1
    fi
fi

# Check if .env file exists
echo -e "${YELLOW}🔧 Checking environment configuration...${NC}"
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️ .env file not found. Creating template...${NC}"
    
    cat > .env << 'EOF'
# Supabase Configuration
VITE_SUPABASE_URL=https://dvflgnqwbsjityrowatf.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28

# OpenAI Configuration (Optional)
VITE_OPENAI_API_KEY=your_openai_api_key_here

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=AI CTNL Dashboard

# Development
NODE_ENV=development
EOF
    
    echo -e "${GREEN}✅ Created .env template file${NC}"
    echo -e "${YELLOW}📝 Please update the .env file with your actual API keys if needed${NC}"
else
    echo -e "${GREEN}✅ .env file found${NC}"
fi

# Display project information
echo ""
echo -e "${CYAN}📊 Project Information:${NC}"
echo -e "${WHITE}- Project: AI CTNL Dashboard${NC}"
echo -e "${WHITE}- Framework: React + TypeScript + Vite${NC}"
echo -e "${WHITE}- UI: Tailwind CSS + shadcn/ui${NC}"
echo -e "${WHITE}- Backend: Supabase${NC}"
echo -e "${WHITE}- Port: 5173 (default)${NC}"

echo ""
echo -e "${CYAN}🔧 Available Fix Tools:${NC}"
echo -e "${WHITE}- Dashboard Fix: http://localhost:5173/fix-dashboard-complete.html${NC}"
echo -e "${WHITE}- Profile Roles Fix: http://localhost:5173/fix-profile-roles.html${NC}"
echo -e "${WHITE}- Activities Fix: http://localhost:5173/fix-activities.html${NC}"

echo ""
echo -e "${GREEN}🚀 Starting development server...${NC}"
echo -e "${WHITE}The dashboard will open in your browser automatically.${NC}"
echo -e "${WHITE}Press Ctrl+C to stop the server.${NC}"
echo ""

# Start the development server
if npm run dev; then
    echo -e "${GREEN}✅ Server started successfully${NC}"
else
    echo -e "${RED}❌ Failed to start development server${NC}"
    echo -e "${YELLOW}Please check the error messages above and try again.${NC}"
    exit 1
fi
