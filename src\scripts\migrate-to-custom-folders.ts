/**
 * Migration Script: Custom Folder Management System
 * This script migrates from department-based folders to custom user folders
 */

import { supabase } from '@/integrations/supabase/client';

export async function migrateToCustomFolders() {
  try {
    console.log('🚀 Starting migration to custom folder system...');

    // Step 1: Check if custom_folders table exists
    const { data: tables, error: tableError } = await supabase
      .rpc('check_table_exists', { table_name: 'custom_folders' });

    if (tableError) {
      console.error('❌ Error checking table existence:', tableError);
      return false;
    }

    if (!tables) {
      console.log('📋 Creating custom_folders table...');
      
      // Execute the schema creation SQL
      const schemaSQL = `
        -- Custom Folder Management System
        CREATE TABLE IF NOT EXISTS public.custom_folders (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            parent_folder_id UUID REFERENCES public.custom_folders(id) ON DELETE CASCADE,
            folder_type TEXT DEFAULT 'custom' CHECK (folder_type IN ('custom', 'system', 'department', 'project')),
            access_level TEXT DEFAULT 'private' CHECK (access_level IN ('private', 'department', 'public')),
            
            -- Ownership and permissions
            created_by UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
            department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
            shared_with UUID[] DEFAULT '{}',
            
            -- Metadata
            color TEXT DEFAULT '#3B82F6',
            icon TEXT DEFAULT 'folder',
            is_archived BOOLEAN DEFAULT FALSE,
            is_system BOOLEAN DEFAULT FALSE,
            
            -- Audit fields
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            -- Constraints
            CONSTRAINT folder_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
            CONSTRAINT no_self_parent CHECK (id != parent_folder_id)
        );

        -- Create folder_permissions table
        CREATE TABLE IF NOT EXISTS public.folder_permissions (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            folder_id UUID NOT NULL REFERENCES public.custom_folders(id) ON DELETE CASCADE,
            user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
            permission_type TEXT NOT NULL CHECK (permission_type IN ('read', 'write', 'admin')),
            granted_by UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
            granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            
            UNIQUE(folder_id, user_id, permission_type)
        );

        -- Add folder_id to document_archive
        ALTER TABLE public.document_archive 
        ADD COLUMN IF NOT EXISTS folder_id UUID REFERENCES public.custom_folders(id) ON DELETE SET NULL;

        -- Create indexes
        CREATE INDEX IF NOT EXISTS idx_custom_folders_created_by ON public.custom_folders(created_by);
        CREATE INDEX IF NOT EXISTS idx_custom_folders_parent ON public.custom_folders(parent_folder_id);
        CREATE INDEX IF NOT EXISTS idx_custom_folders_department ON public.custom_folders(department_id);
        CREATE INDEX IF NOT EXISTS idx_folder_permissions_folder ON public.folder_permissions(folder_id);
        CREATE INDEX IF NOT EXISTS idx_document_archive_folder ON public.document_archive(folder_id);

        -- Enable RLS
        ALTER TABLE public.custom_folders ENABLE ROW LEVEL SECURITY;
        ALTER TABLE public.folder_permissions ENABLE ROW LEVEL SECURITY;
      `;

      const { error: createError } = await supabase.rpc('execute_sql', { sql: schemaSQL });
      
      if (createError) {
        console.error('❌ Error creating tables:', createError);
        return false;
      }

      console.log('✅ Tables created successfully');
    }

    // Step 2: Create RLS policies
    console.log('🔒 Setting up RLS policies...');
    
    const rlsSQL = `
      -- Drop existing policies if they exist
      DROP POLICY IF EXISTS "Users can view accessible folders" ON public.custom_folders;
      DROP POLICY IF EXISTS "Users can create folders" ON public.custom_folders;
      DROP POLICY IF EXISTS "Users can update own folders" ON public.custom_folders;
      DROP POLICY IF EXISTS "Users can delete own folders" ON public.custom_folders;

      -- Create RLS policies for custom_folders
      CREATE POLICY "Users can view accessible folders" ON public.custom_folders
          FOR SELECT USING (
              auth.uid() = created_by OR
              auth.uid() = ANY(shared_with) OR
              (access_level = 'department' AND department_id IN (
                  SELECT department_id FROM public.profiles WHERE id = auth.uid()
              )) OR
              access_level = 'public' OR
              EXISTS (
                  SELECT 1 FROM public.folder_permissions fp 
                  WHERE fp.folder_id = id AND fp.user_id = auth.uid()
              ) OR
              EXISTS (
                  SELECT 1 FROM public.profiles 
                  WHERE id = auth.uid() AND role IN ('admin', 'manager')
              )
          );

      CREATE POLICY "Users can create folders" ON public.custom_folders
          FOR INSERT WITH CHECK (auth.uid() = created_by);

      CREATE POLICY "Users can update own folders" ON public.custom_folders
          FOR UPDATE USING (
              auth.uid() = created_by OR
              EXISTS (
                  SELECT 1 FROM public.folder_permissions fp 
                  WHERE fp.folder_id = id AND fp.user_id = auth.uid() AND fp.permission_type = 'admin'
              ) OR
              EXISTS (
                  SELECT 1 FROM public.profiles 
                  WHERE id = auth.uid() AND role IN ('admin')
              )
          );

      CREATE POLICY "Users can delete own folders" ON public.custom_folders
          FOR DELETE USING (
              auth.uid() = created_by AND is_system = FALSE OR
              EXISTS (
                  SELECT 1 FROM public.profiles 
                  WHERE id = auth.uid() AND role IN ('admin')
              )
          );
    `;

    const { error: rlsError } = await supabase.rpc('execute_sql', { sql: rlsSQL });
    
    if (rlsError) {
      console.error('❌ Error setting up RLS policies:', rlsError);
      return false;
    }

    console.log('✅ RLS policies created successfully');

    // Step 3: Create default folders for existing users
    console.log('📁 Creating default folders for existing users...');
    
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id, full_name, department_id')
      .limit(100); // Process in batches

    if (usersError) {
      console.error('❌ Error fetching users:', usersError);
      return false;
    }

    for (const user of users || []) {
      try {
        // Check if user already has a default folder
        const { data: existingFolder } = await supabase
          .from('custom_folders')
          .select('id')
          .eq('created_by', user.id)
          .eq('name', 'My Documents')
          .single();

        if (!existingFolder) {
          const { error: folderError } = await supabase
            .from('custom_folders')
            .insert({
              name: 'My Documents',
              description: `Personal document folder for ${user.full_name}`,
              created_by: user.id,
              department_id: user.department_id,
              access_level: 'private',
              folder_type: 'custom',
              color: '#10B981',
              icon: 'user'
            });

          if (folderError) {
            console.warn(`⚠️ Could not create default folder for user ${user.id}:`, folderError);
          } else {
            console.log(`✅ Created default folder for user: ${user.full_name}`);
          }
        }
      } catch (error) {
        console.warn(`⚠️ Error processing user ${user.id}:`, error);
      }
    }

    // Step 4: Create system folders
    console.log('🏢 Creating system folders...');
    
    const { data: adminUser } = await supabase
      .from('profiles')
      .select('id')
      .eq('role', 'admin')
      .limit(1)
      .single();

    if (adminUser) {
      const systemFolders = [
        {
          name: 'Company Policies',
          description: 'Official company policies and procedures',
          access_level: 'public',
          folder_type: 'system',
          is_system: true,
          color: '#EF4444',
          icon: 'shield'
        },
        {
          name: 'Templates',
          description: 'Document templates and forms',
          access_level: 'public',
          folder_type: 'system',
          is_system: true,
          color: '#8B5CF6',
          icon: 'template'
        },
        {
          name: 'Shared Resources',
          description: 'Shared company resources and documents',
          access_level: 'public',
          folder_type: 'system',
          is_system: true,
          color: '#06B6D4',
          icon: 'users'
        }
      ];

      for (const folder of systemFolders) {
        try {
          const { data: existing } = await supabase
            .from('custom_folders')
            .select('id')
            .eq('name', folder.name)
            .eq('is_system', true)
            .single();

          if (!existing) {
            const { error } = await supabase
              .from('custom_folders')
              .insert({
                ...folder,
                created_by: adminUser.id
              });

            if (error) {
              console.warn(`⚠️ Could not create system folder ${folder.name}:`, error);
            } else {
              console.log(`✅ Created system folder: ${folder.name}`);
            }
          }
        } catch (error) {
          console.warn(`⚠️ Error creating system folder ${folder.name}:`, error);
        }
      }
    }

    console.log('🎉 Migration completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ Migration failed:', error);
    return false;
  }
}

// Make function available globally for console access
if (typeof window !== 'undefined') {
  (window as any).migrateToCustomFolders = migrateToCustomFolders;
}

export default migrateToCustomFolders;
