#!/usr/bin/env -S deno run --allow-read --allow-write --allow-net

/**
 * Comprehensive Cache Clearing and Error Fixing Script
 * This script addresses:
 * 1. Cache clearing issues
 * 2. Blank screen errors
 * 3. Service worker entrance errors
 * 4. PWA/Workbox cleanup
 */

import { walk } from "https://deno.land/std@0.190.0/fs/walk.ts";

const COLORS = {
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  RESET: '\x1b[0m'
};

function log(message: string, color: string = COLORS.WHITE) {
  console.log(`${color}${message}${COLORS.RESET}`);
}

async function clearCacheAndFixErrors() {
  log("🧹 Starting comprehensive cache clearing and error fixing...", COLORS.CYAN);

  // 1. Remove any service worker files
  log("\n1. 🔍 Checking for service worker files...", COLORS.BLUE);
  const swFiles = [
    "public/sw.js",
    "public/service-worker.js",
    "public/workbox-*.js",
    "src/sw.js",
    "src/service-worker.js"
  ];

  for (const file of swFiles) {
    try {
      await Deno.remove(file);
      log(`   ✅ Removed: ${file}`, COLORS.GREEN);
    } catch {
      // File doesn't exist, which is good
    }
  }

  // 2. Remove PWA manifest files
  log("\n2. 🔍 Checking for PWA manifest files...", COLORS.BLUE);
  const manifestFiles = [
    "public/manifest.json",
    "public/manifest.webmanifest",
    "src/manifest.json"
  ];

  for (const file of manifestFiles) {
    try {
      await Deno.remove(file);
      log(`   ✅ Removed: ${file}`, COLORS.GREEN);
    } catch {
      // File doesn't exist, which is good
    }
  }

  // 3. Update index.html to remove any PWA references
  log("\n3. 📝 Updating index.html...", COLORS.BLUE);
  try {
    const indexPath = "index.html";
    let indexContent = await Deno.readTextFile(indexPath);
    
    // Remove manifest link
    indexContent = indexContent.replace(/<link rel="manifest"[^>]*>/g, '');
    
    // Remove service worker registration
    indexContent = indexContent.replace(/navigator\.serviceWorker\.register[^}]*}/g, '');
    
    // Remove any workbox imports
    indexContent = indexContent.replace(/<script[^>]*workbox[^>]*><\/script>/g, '');
    
    await Deno.writeTextFile(indexPath, indexContent);
    log("   ✅ Updated index.html", COLORS.GREEN);
  } catch (error) {
    log(`   ⚠️  Could not update index.html: ${error.message}`, COLORS.YELLOW);
  }

  // 4. Create cache clearing utility
  log("\n4. 🛠️  Creating enhanced cache clearing utility...", COLORS.BLUE);
  const cacheFixContent = `
/**
 * Enhanced Cache Clearing Utility
 * Fixes blank screen and cache-related issues
 */

export class CacheFixer {
  static clearAllCaches() {
    try {
      // Clear localStorage
      if (typeof localStorage !== 'undefined') {
        localStorage.clear();
        console.log('✅ localStorage cleared');
      }

      // Clear sessionStorage
      if (typeof sessionStorage !== 'undefined') {
        sessionStorage.clear();
        console.log('✅ sessionStorage cleared');
      }

      // Clear IndexedDB
      if (typeof indexedDB !== 'undefined') {
        indexedDB.databases().then(databases => {
          databases.forEach(db => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name);
            }
          });
        });
        console.log('✅ IndexedDB cleared');
      }

      // Clear service worker caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            caches.delete(name);
          });
        });
        console.log('✅ Service worker caches cleared');
      }

      // Unregister service workers
      if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
          registrations.forEach(registration => {
            registration.unregister();
          });
        });
        console.log('✅ Service workers unregistered');
      }

      return true;
    } catch (error) {
      console.error('❌ Error clearing caches:', error);
      return false;
    }
  }

  static fixBlankScreen() {
    try {
      // Force reload if blank screen detected
      if (document.body.children.length === 0 || 
          document.getElementById('root')?.children.length === 0) {
        console.log('🔄 Blank screen detected, forcing reload...');
        this.clearAllCaches();
        setTimeout(() => {
          window.location.reload();
        }, 1000);
        return true;
      }
      return false;
    } catch (error) {
      console.error('❌ Error fixing blank screen:', error);
      return false;
    }
  }

  static preventCacheStuck() {
    // Prevent cache from getting stuck
    const lastClear = localStorage.getItem('lastCacheClear');
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (!lastClear || (now - parseInt(lastClear)) > oneHour) {
      this.clearAllCaches();
      localStorage.setItem('lastCacheClear', now.toString());
    }
  }

  static init() {
    // Run on page load
    document.addEventListener('DOMContentLoaded', () => {
      this.preventCacheStuck();
      
      // Check for blank screen after a delay
      setTimeout(() => {
        this.fixBlankScreen();
      }, 2000);
    });

    // Make available globally
    (window as any).clearAllCaches = () => this.clearAllCaches();
    (window as any).fixBlankScreen = () => this.fixBlankScreen();
  }
}

// Auto-initialize
CacheFixer.init();
`;

  await Deno.writeTextFile("src/utils/cacheFixer.ts", cacheFixContent);
  log("   ✅ Created cache fixer utility", COLORS.GREEN);

  // 5. Update main.tsx to include cache fixer
  log("\n5. 📝 Updating main.tsx...", COLORS.BLUE);
  try {
    const mainPath = "src/main.tsx";
    let mainContent = await Deno.readTextFile(mainPath);
    
    // Add cache fixer import if not present
    if (!mainContent.includes("cacheFixer")) {
      const importLine = "import '@/utils/cacheFixer';\n";
      mainContent = importLine + mainContent;
    }
    
    await Deno.writeTextFile(mainPath, mainContent);
    log("   ✅ Updated main.tsx", COLORS.GREEN);
  } catch (error) {
    log(`   ⚠️  Could not update main.tsx: ${error.message}`, COLORS.YELLOW);
  }

  // 6. Create error boundary enhancement
  log("\n6. 🛡️  Enhancing error boundary...", COLORS.BLUE);
  const errorBoundaryContent = `
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class EnhancedErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Clear caches on critical errors
    if (error.message.includes('Loading chunk') || 
        error.message.includes('ChunkLoadError') ||
        error.message.includes('Loading CSS chunk')) {
      this.clearCachesAndReload();
    }
  }

  clearCachesAndReload = () => {
    try {
      localStorage.clear();
      sessionStorage.clear();
      
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
        });
      }
      
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    } catch (error) {
      console.error('Error clearing caches:', error);
      window.location.reload();
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center p-8 max-w-md">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Something went wrong
            </h1>
            <p className="text-gray-600 mb-6">
              The application encountered an error. We're clearing the cache and reloading.
            </p>
            <button
              onClick={this.clearCachesAndReload}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Clear Cache & Reload
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
`;

  await Deno.writeTextFile("src/components/EnhancedErrorBoundary.tsx", errorBoundaryContent);
  log("   ✅ Created enhanced error boundary", COLORS.GREEN);

  // 7. Create deployment script
  log("\n7. 🚀 Creating deployment script...", COLORS.BLUE);
  const deployScript = `#!/bin/bash

# Deployment script with cache clearing
echo "🚀 Starting deployment with cache clearing..."

# Clear local caches
echo "🧹 Clearing local caches..."
rm -rf node_modules/.cache
rm -rf .vite
rm -rf dist
rm -rf .next

# Clear npm cache
npm cache clean --force

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Build project
echo "🔨 Building project..."
npm run build

# Verify build
if [ -d "dist" ]; then
  echo "✅ Build successful"
  echo "📊 Build size:"
  du -sh dist/
else
  echo "❌ Build failed"
  exit 1
fi

echo "🎉 Deployment ready!"
`;

  await Deno.writeTextFile("scripts/deploy-with-cache-clear.sh", deployScript);
  await Deno.chmod("scripts/deploy-with-cache-clear.sh", 0o755);
  log("   ✅ Created deployment script", COLORS.GREEN);

  // 8. Update package.json scripts
  log("\n8. 📦 Updating package.json scripts...", COLORS.BLUE);
  try {
    const packagePath = "package.json";
    const packageContent = await Deno.readTextFile(packagePath);
    const packageJson = JSON.parse(packageContent);
    
    packageJson.scripts = {
      ...packageJson.scripts,
      "clear-cache": "rm -rf node_modules/.cache && rm -rf .vite && rm -rf dist",
      "fresh-build": "npm run clear-cache && npm ci && npm run build",
      "fix-errors": "deno run --allow-read --allow-write scripts/clear-cache-and-fix-errors.ts"
    };
    
    await Deno.writeTextFile(packagePath, JSON.stringify(packageJson, null, 2));
    log("   ✅ Updated package.json scripts", COLORS.GREEN);
  } catch (error) {
    log(`   ⚠️  Could not update package.json: ${error.message}`, COLORS.YELLOW);
  }

  log("\n✅ Cache clearing and error fixing completed!", COLORS.GREEN);
  log("\n📋 Summary of changes:", COLORS.CYAN);
  log("   • Removed any service worker files", COLORS.WHITE);
  log("   • Removed PWA manifest files", COLORS.WHITE);
  log("   • Updated index.html to remove PWA references", COLORS.WHITE);
  log("   • Created enhanced cache clearing utility", COLORS.WHITE);
  log("   • Enhanced error boundary with cache clearing", COLORS.WHITE);
  log("   • Created deployment script with cache clearing", COLORS.WHITE);
  log("   • Updated package.json with cache clearing scripts", COLORS.WHITE);
  
  log("\n🎯 Next steps:", COLORS.YELLOW);
  log("   1. Run: npm run clear-cache", COLORS.WHITE);
  log("   2. Run: npm run fresh-build", COLORS.WHITE);
  log("   3. Test the application", COLORS.WHITE);
  log("   4. Deploy using: ./scripts/deploy-with-cache-clear.sh", COLORS.WHITE);
}

if (import.meta.main) {
  await clearCacheAndFixErrors();
}
