// Service Worker Registration and PWA utilities

export interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

class PWAManager {
  private deferredPrompt: PWAInstallPrompt | null = null;
  private isInstalled = false;
  private swRegistration: ServiceWorkerRegistration | null = null;

  constructor() {
    this.init();
  }

  private async init() {
    // Check if already installed
    this.checkInstallStatus();
    
    // Register service worker
    await this.registerServiceWorker();
    
    // Setup install prompt handling
    this.setupInstallPrompt();
    
    // Setup update handling
    this.setupUpdateHandling();
  }

  private checkInstallStatus() {
    // Check if running as PWA
    this.isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                     (window.navigator as any).standalone ||
                     document.referrer.includes('android-app://');
    
    console.log('PWA Install Status:', this.isInstalled ? 'Installed' : 'Not Installed');
  }

  private async registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        this.swRegistration = await navigator.serviceWorker.register('/sw.js', {
          scope: '/'
        });
        
        console.log('Service Worker registered successfully:', this.swRegistration);
        
        // Check for updates
        this.swRegistration.addEventListener('updatefound', () => {
          console.log('Service Worker update found');
          this.handleServiceWorkerUpdate();
        });
        
      } catch (error) {
        console.error('Service Worker registration failed:', error);
      }
    } else {
      console.log('Service Worker not supported');
    }
  }

  private setupInstallPrompt() {
    window.addEventListener('beforeinstallprompt', (e) => {
      console.log('PWA install prompt available');
      e.preventDefault();
      this.deferredPrompt = e as any;
      
      // Show custom install button or banner
      this.showInstallBanner();
    });

    window.addEventListener('appinstalled', () => {
      console.log('PWA installed successfully');
      this.isInstalled = true;
      this.deferredPrompt = null;
      this.hideInstallBanner();
    });
  }

  private setupUpdateHandling() {
    if (!this.swRegistration) return;

    this.swRegistration.addEventListener('updatefound', () => {
      const newWorker = this.swRegistration!.installing;
      if (!newWorker) return;

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          // New version available
          this.showUpdateBanner();
        }
      });
    });
  }

  private handleServiceWorkerUpdate() {
    if (!this.swRegistration) return;

    const newWorker = this.swRegistration.installing;
    if (!newWorker) return;

    newWorker.addEventListener('statechange', () => {
      if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
        console.log('New service worker installed, update available');
        this.notifyUpdate();
      }
    });
  }

  public async promptInstall(): Promise<boolean> {
    if (!this.deferredPrompt) {
      console.log('No install prompt available');
      return false;
    }

    try {
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;
      
      console.log('Install prompt result:', choiceResult.outcome);
      
      if (choiceResult.outcome === 'accepted') {
        this.deferredPrompt = null;
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Install prompt failed:', error);
      return false;
    }
  }

  public async updateServiceWorker(): Promise<void> {
    if (!this.swRegistration) return;

    try {
      await this.swRegistration.update();
      window.location.reload();
    } catch (error) {
      console.error('Service worker update failed:', error);
    }
  }

  private showInstallBanner() {
    // Create and show install banner
    const banner = document.createElement('div');
    banner.id = 'pwa-install-banner';
    banner.className = 'fixed bottom-4 left-4 right-4 bg-primary text-primary-foreground p-4 rounded-lg shadow-lg z-50 flex items-center justify-between';
    banner.innerHTML = `
      <div>
        <h3 class="font-semibold">Install CT Nigeria App</h3>
        <p class="text-sm opacity-90">Get the full experience with our mobile app</p>
      </div>
      <div class="flex gap-2">
        <button id="pwa-install-btn" class="bg-white text-primary px-3 py-1 rounded text-sm font-medium">
          Install
        </button>
        <button id="pwa-dismiss-btn" class="opacity-75 hover:opacity-100">
          ✕
        </button>
      </div>
    `;

    document.body.appendChild(banner);

    // Add event listeners
    document.getElementById('pwa-install-btn')?.addEventListener('click', () => {
      this.promptInstall();
    });

    document.getElementById('pwa-dismiss-btn')?.addEventListener('click', () => {
      this.hideInstallBanner();
    });
  }

  private hideInstallBanner() {
    const banner = document.getElementById('pwa-install-banner');
    if (banner) {
      banner.remove();
    }
  }

  private showUpdateBanner() {
    // Create and show update banner
    const banner = document.createElement('div');
    banner.id = 'pwa-update-banner';
    banner.className = 'fixed top-4 left-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50 flex items-center justify-between';
    banner.innerHTML = `
      <div>
        <h3 class="font-semibold">Update Available</h3>
        <p class="text-sm opacity-90">A new version of the app is ready</p>
      </div>
      <div class="flex gap-2">
        <button id="pwa-update-btn" class="bg-white text-blue-600 px-3 py-1 rounded text-sm font-medium">
          Update
        </button>
        <button id="pwa-update-dismiss-btn" class="opacity-75 hover:opacity-100">
          ✕
        </button>
      </div>
    `;

    document.body.appendChild(banner);

    // Add event listeners
    document.getElementById('pwa-update-btn')?.addEventListener('click', () => {
      this.updateServiceWorker();
    });

    document.getElementById('pwa-update-dismiss-btn')?.addEventListener('click', () => {
      banner.remove();
    });
  }

  private notifyUpdate() {
    console.log('Service Worker update available');
    this.showUpdateBanner();
  }

  public get canInstall(): boolean {
    return !!this.deferredPrompt && !this.isInstalled;
  }

  public get installed(): boolean {
    return this.isInstalled;
  }
}

// Create singleton instance
export const pwaManager = new PWAManager();

// Export utility functions
export const registerServiceWorker = () => pwaManager;
export const promptPWAInstall = () => pwaManager.promptInstall();
export const updatePWA = () => pwaManager.updateServiceWorker();
