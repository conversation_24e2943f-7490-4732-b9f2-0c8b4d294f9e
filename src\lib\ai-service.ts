/**
 * Comprehensive AI Service for CTNL AI Workboard
 * Handles real data fetching, AI processing, and system integration
 */

import { supabase } from '@/integrations/supabase/client';

export interface AIRequest {
  message: string;
  userId?: string;
  userRole?: string;
  interface?: 'standard' | 'hacker_terminal' | 'futuristic' | 'enhanced';
  context?: Record<string, any>;
}

export interface AIResponse {
  response: string;
  enhanced: boolean;
  data?: any;
  actions?: Array<{
    type: string;
    status: 'success' | 'error' | 'pending';
    result?: any;
  }>;
  metadata?: {
    processingTime: number;
    model: string;
    dataFetched: boolean;
  };
}

export interface SystemData {
  profiles: any[];
  tasks: any[];
  projects: any[];
  departments: any[];
  timeLog: any[];
  recentActivity: any[];
}

/**
 * Comprehensive AI Service Class
 */
export class AIService {
  private static instance: AIService;
  private systemData: Partial<SystemData> = {};
  private lastDataFetch: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Process AI request with real data integration
   */
  public async processRequest(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now();
    
    try {
      // Fetch real system data if needed
      await this.ensureDataFreshness(request.userId);
      
      // Enhance context with real data
      const enhancedContext = await this.buildEnhancedContext(request);
      
      // Process with AI service
      let aiResponse: string;
      let enhanced = false;

      try {
        // Use direct OpenAI API or Supabase Edge Function
        aiResponse = await this.generateAIResponse(request, enhancedContext);
        enhanced = true;
      } catch (error) {
        console.warn('AI service failed, using fallback:', error);
        aiResponse = await this.generateFallbackResponse(request, enhancedContext);
      }

      // Execute any data actions if requested
      const actions = await this.executeDataActions(request, enhancedContext);
      
      const processingTime = Date.now() - startTime;
      
      // Log the interaction
      await this.logInteraction(request, aiResponse, processingTime);
      
      return {
        response: aiResponse,
        enhanced,
        data: this.getRelevantData(request),
        actions,
        metadata: {
          processingTime,
          model: enhanced ? 'gpt-4-turbo-preview' : 'fallback',
          dataFetched: true
        }
      };
      
    } catch (error) {
      console.error('AI Service error:', error);
      return {
        response: "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
        enhanced: false,
        metadata: {
          processingTime: Date.now() - startTime,
          model: 'error',
          dataFetched: false
        }
      };
    }
  }

  /**
   * Ensure system data is fresh
   */
  private async ensureDataFreshness(userId?: string): Promise<void> {
    const now = Date.now();
    if (now - this.lastDataFetch < this.CACHE_DURATION) {
      return; // Data is still fresh
    }

    try {
      await Promise.all([
        this.fetchProfiles(userId),
        this.fetchTasks(userId),
        this.fetchProjects(userId),
        this.fetchDepartments(),
        this.fetchTimeLog(userId),
        this.fetchRecentActivity(userId)
      ]);
      
      this.lastDataFetch = now;
      console.log('✅ System data refreshed successfully');
    } catch (error) {
      console.warn('⚠️ Some data fetching failed:', error);
    }
  }

  /**
   * Fetch user profiles
   */
  private async fetchProfiles(userId?: string): Promise<void> {
    try {
      let query = supabase.from('profiles').select('*');
      
      if (userId) {
        // Users can see their own profile + others based on role
        query = query.or(`id.eq.${userId},role.in.(admin,manager)`);
      }
      
      const { data, error } = await query.limit(50);
      
      if (!error && data) {
        this.systemData.profiles = data;
      }
    } catch (error) {
      console.warn('Failed to fetch profiles:', error);
      this.systemData.profiles = [];
    }
  }

  /**
   * Fetch tasks
   */
  private async fetchTasks(userId?: string): Promise<void> {
    try {
      let query = supabase.from('tasks').select('*');
      
      if (userId) {
        query = query.or(`assigned_to.eq.${userId},created_by.eq.${userId}`);
      }
      
      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(100);
      
      if (!error && data) {
        this.systemData.tasks = data;
      }
    } catch (error) {
      console.warn('Failed to fetch tasks:', error);
      this.systemData.tasks = [];
    }
  }

  /**
   * Fetch projects
   */
  private async fetchProjects(userId?: string): Promise<void> {
    try {
      let query = supabase.from('projects').select('*');
      
      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(50);
      
      if (!error && data) {
        this.systemData.projects = data;
      }
    } catch (error) {
      console.warn('Failed to fetch projects:', error);
      this.systemData.projects = [];
    }
  }

  /**
   * Fetch departments
   */
  private async fetchDepartments(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name', { ascending: true });
      
      if (!error && data) {
        this.systemData.departments = data;
      }
    } catch (error) {
      console.warn('Failed to fetch departments:', error);
      this.systemData.departments = [];
    }
  }

  /**
   * Fetch time logs
   */
  private async fetchTimeLog(userId?: string): Promise<void> {
    try {
      let query = supabase.from('time_logs').select('*');
      
      if (userId) {
        query = query.eq('user_id', userId);
      }
      
      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(50);
      
      if (!error && data) {
        this.systemData.timeLog = data;
      }
    } catch (error) {
      console.warn('Failed to fetch time logs:', error);
      this.systemData.timeLog = [];
    }
  }

  /**
   * Fetch recent activity
   */
  private async fetchRecentActivity(userId?: string): Promise<void> {
    try {
      let query = supabase.from('recent_activity_logs').select('*');
      
      if (userId) {
        query = query.eq('user_id', userId);
      }
      
      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(20);
      
      if (!error && data) {
        this.systemData.recentActivity = data;
      }
    } catch (error) {
      console.warn('Failed to fetch recent activity:', error);
      this.systemData.recentActivity = [];
    }
  }

  /**
   * Build enhanced context with real data
   */
  private async buildEnhancedContext(request: AIRequest): Promise<Record<string, any>> {
    const context: Record<string, any> = {
      ...request.context,
      interface: request.interface || 'standard',
      userRole: request.userRole,
      timestamp: new Date().toISOString(),
      systemData: {
        profilesCount: this.systemData.profiles?.length || 0,
        tasksCount: this.systemData.tasks?.length || 0,
        projectsCount: this.systemData.projects?.length || 0,
        departmentsCount: this.systemData.departments?.length || 0,
        timeLogsCount: this.systemData.timeLog?.length || 0,
        recentActivityCount: this.systemData.recentActivity?.length || 0
      }
    };

    // Add specific data based on request content
    const message = request.message.toLowerCase();
    
    if (message.includes('task') || message.includes('todo')) {
      context.relevantTasks = this.systemData.tasks?.slice(0, 10);
    }
    
    if (message.includes('project')) {
      context.relevantProjects = this.systemData.projects?.slice(0, 10);
    }
    
    if (message.includes('time') || message.includes('log')) {
      context.relevantTimeLogs = this.systemData.timeLog?.slice(0, 10);
    }
    
    if (message.includes('department') || message.includes('team')) {
      context.departments = this.systemData.departments;
    }
    
    if (message.includes('activity') || message.includes('recent')) {
      context.recentActivity = this.systemData.recentActivity?.slice(0, 5);
    }

    return context;
  }

  /**
   * Generate AI response using OpenAI API
   */
  private async generateAIResponse(request: AIRequest, context: Record<string, any>): Promise<string> {
    // Use Supabase Edge Function for AI processing
    const { data, error } = await supabase.functions.invoke('ai-assistant', {
      body: {
        message: request.message,
        userId: request.userId,
        userRole: request.userRole,
        interface: request.interface,
        context: context
      }
    });

    if (error) {
      throw new Error(`AI service error: ${error.message}`);
    }

    return data?.response || data?.message || 'I apologize, but I encountered an issue processing your request.';
  }

  /**
   * Generate fallback response with real data
   */
  private async generateFallbackResponse(request: AIRequest, context: Record<string, any>): Promise<string> {
    const message = request.message.toLowerCase();
    let response = "I'm here to help you with the CTNL AI Work-Board system. ";

    if (request.userRole) {
      response += `As a ${request.userRole}, `;
    }

    // Data-driven responses
    if (message.includes('task')) {
      const taskCount = this.systemData.tasks?.length || 0;
      response += `I can see you have ${taskCount} tasks in the system. `;
      
      if (taskCount > 0) {
        const pendingTasks = this.systemData.tasks?.filter(t => t.status === 'pending').length || 0;
        response += `${pendingTasks} are still pending completion. `;
      }
    }
    
    if (message.includes('project')) {
      const projectCount = this.systemData.projects?.length || 0;
      response += `There are ${projectCount} projects in the system. `;
    }
    
    if (message.includes('time') || message.includes('log')) {
      const timeLogCount = this.systemData.timeLog?.length || 0;
      response += `I can see ${timeLogCount} time log entries. `;
    }
    
    if (message.includes('department')) {
      const deptCount = this.systemData.departments?.length || 0;
      response += `The system has ${deptCount} departments configured. `;
    }

    if (message.includes('help')) {
      response += "I can assist you with tasks, projects, time tracking, reports, and system navigation.";
    } else {
      response += "I understand your request and I'm here to help with real-time data from your system.";
    }

    return response;
  }

  /**
   * Execute data actions based on request
   */
  private async executeDataActions(request: AIRequest, context: Record<string, any>): Promise<Array<any>> {
    const actions: Array<any> = [];
    const message = request.message.toLowerCase();

    // Example actions based on request content
    if (message.includes('create task')) {
      actions.push({
        type: 'task_creation_ready',
        status: 'pending',
        result: 'Ready to create a new task. Please provide task details.'
      });
    }

    if (message.includes('show') || message.includes('list')) {
      if (message.includes('task')) {
        actions.push({
          type: 'data_fetch',
          status: 'success',
          result: `Fetched ${this.systemData.tasks?.length || 0} tasks`
        });
      }
      
      if (message.includes('project')) {
        actions.push({
          type: 'data_fetch',
          status: 'success',
          result: `Fetched ${this.systemData.projects?.length || 0} projects`
        });
      }
    }

    return actions;
  }

  /**
   * Get relevant data for response
   */
  private getRelevantData(request: AIRequest): any {
    const message = request.message.toLowerCase();
    const data: any = {};

    if (message.includes('task')) {
      data.tasks = this.systemData.tasks?.slice(0, 10);
    }
    
    if (message.includes('project')) {
      data.projects = this.systemData.projects?.slice(0, 10);
    }
    
    if (message.includes('department')) {
      data.departments = this.systemData.departments;
    }
    
    if (message.includes('time') || message.includes('log')) {
      data.timeLogs = this.systemData.timeLog?.slice(0, 10);
    }

    return Object.keys(data).length > 0 ? data : null;
  }

  /**
   * Log AI interaction
   */
  private async logInteraction(request: AIRequest, response: string, processingTime: number): Promise<void> {
    try {
      if (request.userId) {
        await supabase.from('ai_interactions').insert({
          user_id: request.userId,
          role: 'user',
          message: request.message,
          query: request.message,
          type: request.interface || 'standard',
          interface_type: request.interface || 'standard',
          metadata: {
            response_length: response.length,
            processing_time: processingTime,
            data_fetched: true,
            timestamp: new Date().toISOString()
          }
        });
      }
    } catch (error) {
      console.warn('Failed to log AI interaction:', error);
    }
  }

  /**
   * Get system health status
   */
  public async getSystemHealth(): Promise<any> {
    try {
      const { data, error } = await supabase
        .from('system_health')
        .select('*')
        .order('last_check', { ascending: false });
      
      if (error) throw error;
      return data;
    } catch (error) {
      console.warn('Failed to get system health:', error);
      return [];
    }
  }

  /**
   * Clear cached data
   */
  public clearCache(): void {
    this.systemData = {};
    this.lastDataFetch = 0;
    console.log('🧹 AI Service cache cleared');
  }
}

// Export singleton instance
export const aiService = AIService.getInstance();

// Export convenience function
export async function processAIRequest(request: AIRequest): Promise<AIResponse> {
  return await aiService.processRequest(request);
}
