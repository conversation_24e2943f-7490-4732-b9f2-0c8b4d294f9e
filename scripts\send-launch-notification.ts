import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const SUPABASE_URL = 'https://your-project.supabase.co'; // Replace with actual URL
const SUPABASE_ANON_KEY = 'your-anon-key'; // Replace with actual key

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Email recipients for the launch notification
const recipients = [
  '<EMAIL>', // Primary admin/owner - Obibi <PERSON>yi
  '<EMAIL>',     // <PERSON><PERSON>
  'obinna<PERSON>@ctnigeria.com',  // OBINNA OTTI
  '<EMAIL>',   // <PERSON>
  '<EMAIL>', // mulikat Ajamaji
  '<EMAIL>'      // Ugochukwu Mbalisigwe
];

async function sendLaunchNotification() {
  try {
    console.log('🚀 Sending AI WorkBoard CT launch notification...');
    
    // Use the existing send-notification Supabase function
    const response = await supabase.functions.invoke('send-notification', {
      body: {
        type: 'system_launch',
        recipients: recipients,
        data: {
          systemName: 'CTNL AI WorkBoard',
          launchDate: new Date().toLocaleDateString(),
          dashboardUrl: 'https://ai.ctnigeria.com',
          loginUrl: 'https://ai.ctnigeria.com/auth',
          userManualUrl: 'https://ai.ctnigeria.com/user-manual.html',
          features: [
            '🤖 AI-Powered Task Assistant & Automation',
            '🕒 Real-Time Time Attendance with GPS Location',
            '📋 Advanced Project Management with Kanban Boards',
            '💰 Comprehensive Financial & Accounting Suite',
            '📱 Fully Mobile-Responsive Design',
            '👥 Multi-Role Access Control (Admin, Manager, Staff, Accountant)',
            '🔔 Real-time Notifications & Email Alerts',
            '📊 Advanced Reporting & Analytics Dashboard',
            '📖 Complete User Manual & Documentation'
          ],
          stats: {
            userRoles: 5,
            databaseTables: 27,
            aiFunctions: '15+',
            mobileReady: '100%'
          }
        }
      }
    });

    if (response.error) {
      throw new Error(`Notification function error: ${response.error.message}`);
    }

    console.log('✅ Launch notification sent successfully!');
    console.log(`📧 Response:`, response.data);
    console.log(`👥 Recipients: ${recipients.length}`);
    console.log(`📋 Recipients list: ${recipients.join(', ')}`);
    
    return {
      success: true,
      response: response.data,
      recipients: recipients.length,
      message: 'AI WorkBoard CT launch notification sent successfully!'
    };

  } catch (error) {
    console.error('❌ Failed to send launch notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute the notification
if (import.meta.main) {
  sendLaunchNotification()
    .then(result => {
      if (result.success) {
        console.log('\n🎉 SUCCESS: AI WorkBoard CT launch notification completed!');
        console.log(`📊 Summary: ${result.recipients} recipients notified`);
        console.log('🔗 Users can now access: https://ai.ctnigeria.com');
        console.log('📖 User Manual: https://ai.ctnigeria.com/user-manual.html');
        console.log('\n📋 Next Steps:');
        console.log('1. Users should visit ai.ctnigeria.com to access their dashboards');
        console.log('2. Review the comprehensive user manual for detailed guidance');
        console.log('3. Configure notification preferences in user settings');
        console.log('4. Begin using role-specific features immediately');
      } else {
        console.log('\n❌ FAILED: Launch notification could not be sent');
        console.log(`Error: ${result.error}`);
        console.log('\n🔧 Troubleshooting:');
        console.log('1. Verify Supabase URL and API key are correct');
        console.log('2. Ensure send-notification function is deployed');
        console.log('3. Check CORS configuration for function calls');
      }
    })
    .catch(error => {
      console.error('\n💥 CRITICAL ERROR:', error);
    });
}

export { sendLaunchNotification };
