/**
 * Project Manager Assistant Component
 * AI-powered project planning with LangGraph workflow
 */

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Brain, 
  Users, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Target,
  TrendingUp,
  Lightbulb,
  Play,
  Download,
  Share
} from "lucide-react";
import { ProjectManagerAgent, ProjectPlan, TeamMember } from "@/lib/project-manager-agent";
import { useToast } from "@/hooks/use-toast";

interface ProjectManagerAssistantProps {
  onProjectPlanCreated?: (plan: ProjectPlan) => void;
}

export const ProjectManagerAssistant: React.FC<ProjectManagerAssistantProps> = ({
  onProjectPlanCreated
}) => {
  const [projectDescription, setProjectDescription] = useState('');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [newMember, setNewMember] = useState({ name: '', role: '', skills: '', availability: 100 });
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentStep, setCurrentStep] = useState('');
  const [progress, setProgress] = useState(0);
  const [projectPlan, setProjectPlan] = useState<ProjectPlan | null>(null);
  const [insights, setInsights] = useState<string[]>([]);
  const { toast } = useToast();

  const agent = new ProjectManagerAgent();

  const addTeamMember = () => {
    if (!newMember.name || !newMember.role) {
      toast({
        title: "Missing Information",
        description: "Please provide name and role for the team member.",
        variant: "destructive",
      });
      return;
    }

    const member: TeamMember = {
      id: Date.now().toString(),
      name: newMember.name,
      role: newMember.role,
      skills: newMember.skills.split(',').map(s => s.trim()).filter(s => s),
      availability: newMember.availability,
      current_workload: 0
    };

    setTeamMembers([...teamMembers, member]);
    setNewMember({ name: '', role: '', skills: '', availability: 100 });
    
    toast({
      title: "Team Member Added",
      description: `${member.name} has been added to the team.`,
    });
  };

  const removeTeamMember = (id: string) => {
    setTeamMembers(teamMembers.filter(member => member.id !== id));
  };

  const generateProjectPlan = async () => {
    if (!projectDescription.trim()) {
      toast({
        title: "Missing Project Description",
        description: "Please provide a project description.",
        variant: "destructive",
      });
      return;
    }

    if (teamMembers.length === 0) {
      toast({
        title: "No Team Members",
        description: "Please add at least one team member.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setProgress(0);
    setCurrentStep('Initializing AI Agent...');

    try {
      // Simulate progress updates
      const steps = [
        'Analyzing project description...',
        'Generating actionable tasks...',
        'Mapping task dependencies...',
        'Creating optimized schedule...',
        'Allocating tasks to team members...',
        'Assessing project risks...',
        'Generating insights...',
        'Finalizing project plan...'
      ];

      for (let i = 0; i < steps.length; i++) {
        setCurrentStep(steps[i]);
        setProgress((i + 1) / steps.length * 100);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      const plan = await agent.createProjectPlan(projectDescription, teamMembers, 3);
      
      setProjectPlan(plan);
      setProgress(100);
      setCurrentStep('Project plan generated successfully!');
      
      if (onProjectPlanCreated) {
        onProjectPlanCreated(plan);
      }

      toast({
        title: "Project Plan Generated",
        description: `Created plan with ${plan.tasks.length} tasks and ${plan.total_duration_days} days duration.`,
      });

    } catch (error) {
      console.error('Error generating project plan:', error);
      toast({
        title: "Generation Failed",
        description: "Failed to generate project plan. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const exportProjectPlan = () => {
    if (!projectPlan) return;

    const exportData = {
      project_description: projectDescription,
      team_members: teamMembers,
      project_plan: projectPlan,
      generated_at: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `project-plan-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Plan Exported",
      description: "Project plan has been exported successfully.",
    });
  };

  const getRiskColor = (score: number) => {
    if (score <= 3) return 'text-green-600';
    if (score <= 6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-primary" />
            AI Project Manager Assistant
          </CardTitle>
          <p className="text-muted-foreground">
            Transform your project description into a structured, actionable plan with AI-powered task generation, 
            dependency mapping, scheduling, and risk assessment.
          </p>
        </CardHeader>
      </Card>

      <Tabs defaultValue="setup" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="setup">Setup</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
          <TabsTrigger value="generate">Generate</TabsTrigger>
          <TabsTrigger value="results" disabled={!projectPlan}>Results</TabsTrigger>
        </TabsList>

        {/* Setup Tab */}
        <TabsContent value="setup" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Project Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                placeholder="Describe your project in detail. Include objectives, requirements, constraints, and any specific considerations..."
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
                rows={8}
                className="w-full"
              />
              <p className="text-sm text-muted-foreground mt-2">
                Provide a comprehensive description to help the AI generate accurate tasks and dependencies.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Team Tab */}
        <TabsContent value="team" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Team Members ({teamMembers.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Add Team Member Form */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 border rounded-lg">
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    value={newMember.name}
                    onChange={(e) => setNewMember({...newMember, name: e.target.value})}
                    placeholder="John Doe"
                  />
                </div>
                <div>
                  <Label htmlFor="role">Role</Label>
                  <Input
                    id="role"
                    value={newMember.role}
                    onChange={(e) => setNewMember({...newMember, role: e.target.value})}
                    placeholder="Frontend Developer"
                  />
                </div>
                <div>
                  <Label htmlFor="skills">Skills (comma-separated)</Label>
                  <Input
                    id="skills"
                    value={newMember.skills}
                    onChange={(e) => setNewMember({...newMember, skills: e.target.value})}
                    placeholder="React, TypeScript, CSS"
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={addTeamMember} className="w-full">
                    Add Member
                  </Button>
                </div>
              </div>

              {/* Team Members List */}
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {teamMembers.map((member) => (
                    <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{member.name}</h4>
                          <Badge variant="outline">{member.role}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Skills: {member.skills.join(', ') || 'None specified'}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Availability: {member.availability}%
                        </p>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => removeTeamMember(member.id)}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Generate Tab */}
        <TabsContent value="generate" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                AI Project Planning
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!isGenerating ? (
                <div className="text-center space-y-4">
                  <p className="text-muted-foreground">
                    Ready to generate your AI-powered project plan with:
                  </p>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <Target className="h-8 w-8 mx-auto text-primary mb-2" />
                      <p className="text-sm font-medium">Task Generation</p>
                    </div>
                    <div className="text-center">
                      <Calendar className="h-8 w-8 mx-auto text-primary mb-2" />
                      <p className="text-sm font-medium">Smart Scheduling</p>
                    </div>
                    <div className="text-center">
                      <Users className="h-8 w-8 mx-auto text-primary mb-2" />
                      <p className="text-sm font-medium">Team Allocation</p>
                    </div>
                    <div className="text-center">
                      <AlertTriangle className="h-8 w-8 mx-auto text-primary mb-2" />
                      <p className="text-sm font-medium">Risk Assessment</p>
                    </div>
                  </div>
                  <Button 
                    onClick={generateProjectPlan} 
                    size="lg" 
                    className="w-full max-w-md"
                    disabled={!projectDescription.trim() || teamMembers.length === 0}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Generate Project Plan
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-center">
                    <Brain className="h-12 w-12 mx-auto text-primary mb-4 animate-pulse" />
                    <h3 className="text-lg font-medium mb-2">AI Agent Working...</h3>
                    <p className="text-muted-foreground mb-4">{currentStep}</p>
                    <Progress value={progress} className="w-full max-w-md mx-auto" />
                    <p className="text-sm text-muted-foreground mt-2">{Math.round(progress)}% Complete</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Results Tab */}
        <TabsContent value="results" className="space-y-4">
          {projectPlan && (
            <>
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Target className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Tasks</p>
                        <p className="text-2xl font-bold">{projectPlan.tasks.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Duration</p>
                        <p className="text-2xl font-bold">{projectPlan.total_duration_days} days</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Team Size</p>
                        <p className="text-2xl font-bold">{teamMembers.length}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-primary" />
                      <div>
                        <p className="text-sm text-muted-foreground">Risk Score</p>
                        <p className={`text-2xl font-bold ${getRiskColor(projectPlan.overall_risk_score)}`}>
                          {projectPlan.overall_risk_score}/100
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Project Timeline Visualization */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Project Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {projectPlan.schedule.map((scheduleItem) => {
                      const task = projectPlan.tasks.find(t => t.id === scheduleItem.task_id);
                      const allocation = projectPlan.allocations.find(a => a.task_id === scheduleItem.task_id);
                      const assignedMember = teamMembers.find(m => m.id === allocation?.team_member_id);
                      const risk = projectPlan.risks.find(r => r.task_id === scheduleItem.task_id);

                      if (!task) return null;

                      return (
                        <div key={scheduleItem.task_id} className="relative">
                          <div className="flex items-center gap-4 p-3 border rounded-lg">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium">{task.task_name}</h4>
                                <Badge className={getPriorityColor(task.priority)}>
                                  {task.priority}
                                </Badge>
                                {risk && (
                                  <Badge variant="outline" className={getRiskColor(risk.risk_score)}>
                                    Risk: {risk.risk_score}/10
                                  </Badge>
                                )}
                              </div>
                              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <Calendar className="h-3 w-3" />
                                  {scheduleItem.start_date.toLocaleDateString()} - {scheduleItem.end_date.toLocaleDateString()}
                                </span>
                                <span className="flex items-center gap-1">
                                  <Clock className="h-3 w-3" />
                                  {scheduleItem.duration_days} days
                                </span>
                                {assignedMember && (
                                  <span className="flex items-center gap-1">
                                    <Users className="h-3 w-3" />
                                    {assignedMember.name}
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="w-32">
                              <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
                                <div
                                  className={`h-full rounded-full ${
                                    task.priority === 'critical' ? 'bg-red-500' :
                                    task.priority === 'high' ? 'bg-orange-500' :
                                    task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                                  }`}
                                  style={{ width: '100%' }}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Tasks Overview */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      Generated Tasks
                    </CardTitle>
                    <div className="flex gap-2">
                      <Button variant="outline" onClick={exportProjectPlan}>
                        <Download className="h-4 w-4 mr-2" />
                        Export Plan
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    <div className="space-y-2">
                      {projectPlan.tasks.map((task) => {
                        const taskRisk = projectPlan.risks.find(r => r.task_id === task.id);
                        const taskAllocation = projectPlan.allocations.find(a => a.task_id === task.id);
                        const assignedMember = teamMembers.find(m => m.id === taskAllocation?.team_member_id);

                        return (
                          <div key={task.id} className="p-3 border rounded-lg">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-medium">{task.task_name}</h4>
                                  <Badge className={getPriorityColor(task.priority)}>
                                    {task.priority}
                                  </Badge>
                                  {taskRisk && (
                                    <Badge variant="outline" className={getRiskColor(taskRisk.risk_score)}>
                                      Risk: {taskRisk.risk_score}/10
                                    </Badge>
                                  )}
                                </div>
                                <p className="text-sm text-muted-foreground mb-2">{task.task_description}</p>
                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    {task.estimated_days} days
                                  </span>
                                  {assignedMember && (
                                    <span className="flex items-center gap-1">
                                      <Users className="h-3 w-3" />
                                      {assignedMember.name}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </CardContent>
              </Card>

              {/* Risk Analysis */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5" />
                    Risk Analysis
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {projectPlan.risks
                      .sort((a, b) => b.risk_score - a.risk_score)
                      .slice(0, 5)
                      .map((risk) => {
                        const task = projectPlan.tasks.find(t => t.id === risk.task_id);
                        if (!task) return null;

                        return (
                          <div key={risk.task_id} className="p-3 border rounded-lg">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-medium">{task.task_name}</h4>
                              <Badge variant="outline" className={getRiskColor(risk.risk_score)}>
                                {risk.risk_score}/10
                              </Badge>
                            </div>
                            <div className="space-y-2 text-sm">
                              <div>
                                <p className="font-medium text-muted-foreground">Risk Factors:</p>
                                <ul className="list-disc list-inside text-muted-foreground">
                                  {risk.risk_factors.map((factor, index) => (
                                    <li key={index}>{factor}</li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <p className="font-medium text-muted-foreground">Mitigation Strategies:</p>
                                <ul className="list-disc list-inside text-muted-foreground">
                                  {risk.mitigation_strategies.map((strategy, index) => (
                                    <li key={index}>{strategy}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};
