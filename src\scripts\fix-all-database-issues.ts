import { supabase } from '@/integrations/supabase/client'

/**
 * Comprehensive script to fix all database and API issues
 */
export async function fixAllDatabaseIssues () {
  console.log('🔧 Starting comprehensive database fixes...')

  const results = {
    rpcFunctions: [],
    schemaFixes: [],
    errors: []
  }

  try {
    // 1. <PERSON>reate missing RPC functions
    console.log('📊 Creating RPC functions...')

    // Create get_attendance_stats function
    try {
      await supabase.rpc('exec_sql', {
        sql: `
          CREATE OR REPLACE FUNCTION get_attendance_stats(user_id_param UUID DEFAULT NULL)
          RETURNS JSON AS $$
          DECLARE
              result JSON;
              total_hours NUMERIC := 0;
              days_present INTEGER := 0;
              avg_hours NUMERIC := 0;
              current_streak INTEGER := 0;
          BEGIN
              -- Get total hours worked
              SELECT COALESCE(SUM(total_hours), 0) INTO total_hours
              FROM time_logs 
              WHERE (user_id_param IS NULL OR user_id = user_id_param)
              AND clock_out IS NOT NULL;
              
              -- Get days present (unique dates with clock in)
              SELECT COUNT(DISTINCT DATE(clock_in)) INTO days_present
              FROM time_logs 
              WHERE (user_id_param IS NULL OR user_id = user_id_param)
              AND clock_in IS NOT NULL;
              
              -- Calculate average hours per day
              IF days_present > 0 THEN
                  avg_hours := total_hours / days_present;
              END IF;
              
              -- Calculate current streak (consecutive days with attendance)
              current_streak := COALESCE(
                  (SELECT COUNT(*) FROM (
                      SELECT DATE(clock_in) as work_date
                      FROM time_logs 
                      WHERE (user_id_param IS NULL OR user_id = user_id_param)
                      AND clock_in IS NOT NULL
                      AND DATE(clock_in) >= CURRENT_DATE - INTERVAL '30 days'
                      GROUP BY DATE(clock_in)
                      ORDER BY DATE(clock_in) DESC
                      LIMIT 30
                  ) recent_days), 0
              );
              
              -- Build result JSON
              result := json_build_object(
                  'total_hours', total_hours,
                  'days_present', days_present,
                  'average_hours_per_day', ROUND(avg_hours, 2),
                  'current_streak', current_streak,
                  'attendance_rate', CASE 
                      WHEN days_present > 0 THEN ROUND((days_present::NUMERIC / 30) * 100, 1)
                      ELSE 0
                  END
              );
              
              RETURN result;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `
      })
      results.rpcFunctions.push('✅ get_attendance_stats created')
    } catch (error) {
      results.errors.push(`❌ get_attendance_stats: ${error.message}`)
    }

    // Create get_financial_summary function
    try {
      await supabase.rpc('exec_sql', {
        sql: `
          CREATE OR REPLACE FUNCTION get_financial_summary()
          RETURNS JSON AS $$
          DECLARE
              result JSON;
              total_budget NUMERIC := 0;
              total_spent NUMERIC := 0;
              pending_invoices NUMERIC := 0;
              monthly_expenses NUMERIC := 0;
          BEGIN
              -- Get total budget from projects (using correct column name)
              SELECT COALESCE(SUM(budget), 0) INTO total_budget
              FROM projects WHERE status != 'cancelled';
              
              -- Get total spent from projects
              SELECT COALESCE(SUM(budget_spent), 0) INTO total_spent
              FROM projects WHERE status != 'cancelled';
              
              -- Get pending invoices total
              SELECT COALESCE(SUM(total_amount), 0) INTO pending_invoices
              FROM invoices WHERE status = 'pending';
              
              -- Get current month expenses
              SELECT COALESCE(SUM(amount), 0) INTO monthly_expenses
              FROM expense_reports 
              WHERE DATE_TRUNC('month', expense_date) = DATE_TRUNC('month', CURRENT_DATE)
              AND status = 'approved';
              
              -- Build result JSON
              result := json_build_object(
                  'total_budget', total_budget,
                  'total_spent', total_spent,
                  'budget_remaining', total_budget - total_spent,
                  'budget_utilization', CASE 
                      WHEN total_budget > 0 THEN ROUND((total_spent / total_budget) * 100, 1)
                      ELSE 0
                  END,
                  'pending_invoices', pending_invoices,
                  'monthly_expenses', monthly_expenses,
                  'cash_flow', total_budget - total_spent - pending_invoices
              );
              
              RETURN result;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `
      })
      results.rpcFunctions.push('✅ get_financial_summary created')
    } catch (error) {
      results.errors.push(`❌ get_financial_summary: ${error.message}`)
    }

    // Create get_team_time_logs function
    try {
      await supabase.rpc('exec_sql', {
        sql: `
          CREATE OR REPLACE FUNCTION get_team_time_logs(manager_id_param UUID DEFAULT NULL)
          RETURNS TABLE(
              user_id UUID,
              full_name TEXT,
              email TEXT,
              clock_in TIMESTAMP WITH TIME ZONE,
              clock_out TIMESTAMP WITH TIME ZONE,
              total_hours NUMERIC,
              status TEXT,
              location TEXT,
              created_at TIMESTAMP WITH TIME ZONE
          ) AS $$
          BEGIN
              RETURN QUERY
              SELECT 
                  tl.user_id,
                  p.full_name,
                  p.email,
                  tl.clock_in,
                  tl.clock_out,
                  tl.total_hours,
                  tl.status,
                  tl.location,
                  tl.created_at
              FROM time_logs tl
              JOIN profiles p ON tl.user_id = p.id
              WHERE (
                  manager_id_param IS NULL 
                  OR p.id IN (
                      SELECT pa.assigned_to 
                      FROM project_assignments pa 
                      JOIN projects pr ON pa.project_id = pr.id 
                      WHERE pr.manager_id = manager_id_param
                  )
              )
              ORDER BY tl.created_at DESC
              LIMIT 100;
          END;
          $$ LANGUAGE plpgsql SECURITY DEFINER;
        `
      })
      results.rpcFunctions.push('✅ get_team_time_logs created')
    } catch (error) {
      results.errors.push(`❌ get_team_time_logs: ${error.message}`)
    }

    // 2. Test the functions
    console.log('🧪 Testing RPC functions...')

    try {
      const { data: attendanceTest } = await supabase.rpc('get_attendance_stats')
      results.rpcFunctions.push('✅ get_attendance_stats tested successfully')
    } catch (error) {
      results.errors.push(`❌ get_attendance_stats test failed: ${error.message}`)
    }

    try {
      const { data: financialTest } = await supabase.rpc('get_financial_summary')
      results.rpcFunctions.push('✅ get_financial_summary tested successfully')
    } catch (error) {
      results.errors.push(`❌ get_financial_summary test failed: ${error.message}`)
    }

    try {
      const { data: teamLogsTest } = await supabase.rpc('get_team_time_logs')
      results.rpcFunctions.push('✅ get_team_time_logs tested successfully')
    } catch (error) {
      results.errors.push(`❌ get_team_time_logs test failed: ${error.message}`)
    }

    console.log('✅ Database fixes completed!')
    return results
  } catch (error) {
    console.error('❌ Database fix failed:', error)
    results.errors.push(`❌ General error: ${error.message}`)
    return results
  }
}

// Auto-run the fixes when this module is imported
if (typeof window !== 'undefined') {
  console.log('🔧 Auto-running database fixes...')
  fixAllDatabaseIssues().then(results => {
    console.log('📊 Database Fix Results:', results)
  })
}
