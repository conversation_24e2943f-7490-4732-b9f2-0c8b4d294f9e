/**
 * Advanced AI Task Automation System
 * Enables AI to perform complex user interactions including:
 * - Page navigation and routing
 * - But<PERSON> clicks and form interactions
 * - Text input and form filling
 * - File uploads and submissions
 * - Memo creation and management
 * - Multi-step workflow execution
 */

import { supabase } from '@/integrations/supabase/client';
import { aiService } from '@/lib/ai-service';

export interface AITaskRequest {
  command: string;
  userId: string;
  userRole: string;
  context?: {
    currentPage?: string;
    formData?: Record<string, any>;
    files?: File[];
    targetElements?: string[];
  };
}

export interface AITaskStep {
  id: string;
  type: 'navigate' | 'click' | 'input' | 'upload' | 'submit' | 'wait' | 'verify';
  target: string;
  value?: string;
  files?: File[];
  description: string;
  expectedOutcome: string;
  timeout?: number;
}

export interface AITaskExecution {
  id: string;
  name: string;
  description: string;
  steps: AITaskStep[];
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  currentStep?: number;
  result?: any;
  error?: string;
}

export class AITaskAutomationSystem {
  private activeExecution: AITaskExecution | null = null;
  private executionHistory: AITaskExecution[] = [];

  /**
   * Main entry point for AI task automation
   */
  public async executeUserCommand(request: AITaskRequest): Promise<AITaskExecution> {
    console.log('🤖 AI Task Automation: Processing command:', request.command);

    // Step 1: Parse and understand the command
    const taskPlan = await this.parseCommand(request);
    
    // Step 2: Create execution plan
    const execution: AITaskExecution = {
      id: `task_${Date.now()}`,
      name: taskPlan.name,
      description: taskPlan.description,
      steps: taskPlan.steps,
      status: 'pending',
      progress: 0
    };

    // Step 3: Execute the task
    this.activeExecution = execution;
    this.executionHistory.push(execution);

    try {
      await this.executeTask(execution);
      execution.status = 'completed';
      execution.progress = 100;
    } catch (error) {
      execution.status = 'failed';
      execution.error = `${error}`;
      console.error('AI Task Automation failed:', error);
    } finally {
      this.activeExecution = null;
    }

    return execution;
  }

  /**
   * Parse user command into executable task plan
   */
  private async parseCommand(request: AITaskRequest): Promise<{
    name: string;
    description: string;
    steps: AITaskStep[];
  }> {
    const command = request.command.toLowerCase();

    // Predefined task patterns
    if (command.includes('write memo') || command.includes('create memo')) {
      return this.createMemoTask(request);
    }
    
    if (command.includes('upload file') || command.includes('upload document')) {
      return this.createFileUploadTask(request);
    }
    
    if (command.includes('navigate to') || command.includes('go to')) {
      return this.createNavigationTask(request);
    }
    
    if (command.includes('fill form') || command.includes('submit form')) {
      return this.createFormTask(request);
    }

    // Use AI to parse complex commands
    return this.parseComplexCommand(request);
  }

  /**
   * Create memo writing task
   */
  private async createMemoTask(request: AITaskRequest): Promise<{
    name: string;
    description: string;
    steps: AITaskStep[];
  }> {
    const memoContent = this.extractMemoContent(request.command);
    
    return {
      name: 'Create Memo',
      description: 'Navigate to memo creation page and write memo',
      steps: [
        {
          id: 'nav_memo',
          type: 'navigate',
          target: '/dashboard/manager',
          description: 'Navigate to manager dashboard',
          expectedOutcome: 'Manager dashboard loaded'
        },
        {
          id: 'click_memo_tab',
          type: 'click',
          target: '[data-value="memos"]',
          description: 'Click on memos tab',
          expectedOutcome: 'Memos section opened'
        },
        {
          id: 'click_new_memo',
          type: 'click',
          target: 'button:contains("New Memo"), button:contains("Create Memo"), [data-testid="new-memo"]',
          description: 'Click new memo button',
          expectedOutcome: 'Memo creation form opened'
        },
        {
          id: 'input_memo_title',
          type: 'input',
          target: 'input[name="title"], input[placeholder*="title"], #memo-title',
          value: memoContent.title,
          description: 'Enter memo title',
          expectedOutcome: 'Title entered'
        },
        {
          id: 'input_memo_content',
          type: 'input',
          target: 'textarea[name="content"], textarea[placeholder*="content"], #memo-content',
          value: memoContent.content,
          description: 'Enter memo content',
          expectedOutcome: 'Content entered'
        },
        {
          id: 'submit_memo',
          type: 'submit',
          target: 'button[type="submit"], button:contains("Save"), button:contains("Create")',
          description: 'Submit memo',
          expectedOutcome: 'Memo created successfully'
        }
      ]
    };
  }

  /**
   * Create file upload task
   */
  private async createFileUploadTask(request: AITaskRequest): Promise<{
    name: string;
    description: string;
    steps: AITaskStep[];
  }> {
    return {
      name: 'Upload File',
      description: 'Navigate to upload section and upload files',
      steps: [
        {
          id: 'nav_upload',
          type: 'navigate',
          target: '/dashboard/admin',
          description: 'Navigate to admin dashboard',
          expectedOutcome: 'Admin dashboard loaded'
        },
        {
          id: 'click_upload_section',
          type: 'click',
          target: 'button:contains("Upload"), [data-testid="upload-button"]',
          description: 'Click upload section',
          expectedOutcome: 'Upload interface opened'
        },
        {
          id: 'upload_files',
          type: 'upload',
          target: 'input[type="file"], [data-testid="file-input"]',
          files: request.context?.files || [],
          description: 'Upload selected files',
          expectedOutcome: 'Files uploaded successfully'
        },
        {
          id: 'submit_upload',
          type: 'submit',
          target: 'button:contains("Submit"), button:contains("Upload")',
          description: 'Submit uploaded files',
          expectedOutcome: 'Files processed and saved'
        }
      ]
    };
  }

  /**
   * Create navigation task
   */
  private async createNavigationTask(request: AITaskRequest): Promise<{
    name: string;
    description: string;
    steps: AITaskStep[];
  }> {
    const destination = this.extractDestination(request.command);
    
    return {
      name: 'Navigate',
      description: `Navigate to ${destination}`,
      steps: [
        {
          id: 'navigate',
          type: 'navigate',
          target: destination,
          description: `Navigate to ${destination}`,
          expectedOutcome: `${destination} page loaded`
        },
        {
          id: 'verify_page',
          type: 'verify',
          target: 'body',
          description: 'Verify page loaded correctly',
          expectedOutcome: 'Page content visible'
        }
      ]
    };
  }

  /**
   * Parse complex commands using AI
   */
  private async parseComplexCommand(request: AITaskRequest): Promise<{
    name: string;
    description: string;
    steps: AITaskStep[];
  }> {
    const response = await aiService.processRequest({
      message: `Parse this user command into executable steps: "${request.command}"
      
      Available actions:
      - navigate: Go to a specific page/route
      - click: Click buttons, links, or elements
      - input: Enter text into form fields
      - upload: Upload files
      - submit: Submit forms
      - wait: Wait for elements to load
      - verify: Check if action was successful
      
      Current context: ${JSON.stringify(request.context)}
      User role: ${request.userRole}
      
      Return a JSON object with: name, description, and steps array.
      Each step should have: id, type, target, value (if needed), description, expectedOutcome`,
      userId: request.userId,
      userRole: request.userRole,
      interface: 'futuristic'
    });

    try {
      const parsed = JSON.parse(response.response);
      return parsed;
    } catch (error) {
      // Fallback to basic navigation
      return {
        name: 'Basic Command',
        description: 'Execute basic command',
        steps: [
          {
            id: 'basic_action',
            type: 'click',
            target: 'body',
            description: 'Execute basic action',
            expectedOutcome: 'Action completed'
          }
        ]
      };
    }
  }

  /**
   * Execute the task step by step
   */
  private async executeTask(execution: AITaskExecution): Promise<void> {
    execution.status = 'running';
    
    for (let i = 0; i < execution.steps.length; i++) {
      const step = execution.steps[i];
      execution.currentStep = i;
      execution.progress = (i / execution.steps.length) * 100;

      console.log(`🔄 Executing step ${i + 1}/${execution.steps.length}: ${step.description}`);

      try {
        await this.executeStep(step);
        console.log(`✅ Step completed: ${step.description}`);
      } catch (error) {
        console.error(`❌ Step failed: ${step.description}`, error);
        throw new Error(`Step "${step.description}" failed: ${error}`);
      }

      // Wait between steps for UI to update
      await this.wait(1000);
    }
  }

  /**
   * Execute individual step
   */
  private async executeStep(step: AITaskStep): Promise<void> {
    switch (step.type) {
      case 'navigate':
        await this.executeNavigation(step);
        break;
      case 'click':
        await this.executeClick(step);
        break;
      case 'input':
        await this.executeInput(step);
        break;
      case 'upload':
        await this.executeUpload(step);
        break;
      case 'submit':
        await this.executeSubmit(step);
        break;
      case 'wait':
        await this.executeWait(step);
        break;
      case 'verify':
        await this.executeVerify(step);
        break;
      default:
        throw new Error(`Unknown step type: ${step.type}`);
    }
  }

  /**
   * Execute navigation
   */
  private async executeNavigation(step: AITaskStep): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Use React Router navigation
        window.location.href = step.target;
        
        // Wait for navigation to complete
        setTimeout(() => {
          if (window.location.pathname.includes(step.target.split('?')[0])) {
            resolve();
          } else {
            reject(new Error('Navigation failed'));
          }
        }, 2000);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Execute click action
   */
  private async executeClick(step: AITaskStep): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const selectors = step.target.split(',').map(s => s.trim());
        let element: Element | null = null;

        // Try each selector until one works
        for (const selector of selectors) {
          element = document.querySelector(selector);
          if (element) break;
        }

        if (!element) {
          // Try finding by text content
          const buttons = Array.from(document.querySelectorAll('button, a, [role="button"]'));
          element = buttons.find(btn => 
            btn.textContent?.toLowerCase().includes(step.target.toLowerCase())
          ) as Element;
        }

        if (element && element instanceof HTMLElement) {
          // Scroll element into view
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          
          // Highlight element briefly
          const originalStyle = element.style.cssText;
          element.style.cssText += 'outline: 3px solid #ff0000; outline-offset: 2px;';
          
          setTimeout(() => {
            element!.style.cssText = originalStyle;
            (element as HTMLElement).click();
            resolve();
          }, 1000);
        } else {
          reject(new Error(`Element not found: ${step.target}`));
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Execute input action
   */
  private async executeInput(step: AITaskStep): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const selectors = step.target.split(',').map(s => s.trim());
        let element: HTMLInputElement | HTMLTextAreaElement | null = null;

        for (const selector of selectors) {
          element = document.querySelector(selector) as HTMLInputElement | HTMLTextAreaElement;
          if (element) break;
        }

        if (element) {
          // Focus and clear existing content
          element.focus();
          element.value = '';
          
          // Type text with realistic delay
          const text = step.value || '';
          let index = 0;
          
          const typeChar = () => {
            if (index < text.length) {
              element!.value += text[index];
              
              // Trigger input events
              element!.dispatchEvent(new Event('input', { bubbles: true }));
              element!.dispatchEvent(new Event('change', { bubbles: true }));
              
              index++;
              setTimeout(typeChar, 50); // 50ms between characters
            } else {
              resolve();
            }
          };
          
          typeChar();
        } else {
          reject(new Error(`Input element not found: ${step.target}`));
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Execute file upload
   */
  private async executeUpload(step: AITaskStep): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const fileInput = document.querySelector(step.target) as HTMLInputElement;
        
        if (fileInput && fileInput.type === 'file') {
          if (step.files && step.files.length > 0) {
            // Create DataTransfer object to simulate file selection
            const dataTransfer = new DataTransfer();
            step.files.forEach(file => dataTransfer.items.add(file));
            
            fileInput.files = dataTransfer.files;
            fileInput.dispatchEvent(new Event('change', { bubbles: true }));
            
            resolve();
          } else {
            reject(new Error('No files provided for upload'));
          }
        } else {
          reject(new Error(`File input not found: ${step.target}`));
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Execute form submission
   */
  private async executeSubmit(step: AITaskStep): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const selectors = step.target.split(',').map(s => s.trim());
        let element: Element | null = null;

        for (const selector of selectors) {
          element = document.querySelector(selector);
          if (element) break;
        }

        if (element && element instanceof HTMLElement) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          
          setTimeout(() => {
            (element as HTMLElement).click();
            resolve();
          }, 1000);
        } else {
          reject(new Error(`Submit element not found: ${step.target}`));
        }
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Execute wait
   */
  private async executeWait(step: AITaskStep): Promise<void> {
    const duration = step.timeout || 2000;
    await this.wait(duration);
  }

  /**
   * Execute verification
   */
  private async executeVerify(step: AITaskStep): Promise<void> {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(step.target);
      if (element) {
        resolve();
      } else {
        reject(new Error(`Verification failed: ${step.target} not found`));
      }
    });
  }

  /**
   * Utility functions
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private extractMemoContent(command: string): { title: string; content: string } {
    const titleMatch = command.match(/title[:\s]+"([^"]+)"/i) || command.match(/titled?\s+"([^"]+)"/i);
    const contentMatch = command.match(/content[:\s]+"([^"]+)"/i) || command.match(/saying?\s+"([^"]+)"/i);
    
    return {
      title: titleMatch?.[1] || 'AI Generated Memo',
      content: contentMatch?.[1] || command.replace(/write memo|create memo/gi, '').trim()
    };
  }

  private extractDestination(command: string): string {
    const destinations = {
      'dashboard': '/dashboard',
      'admin': '/dashboard/admin',
      'manager': '/dashboard/manager',
      'staff': '/dashboard/staff',
      'accountant': '/dashboard/accountant',
      'projects': '/dashboard/admin',
      'tasks': '/dashboard/staff',
      'reports': '/dashboard/manager',
      'settings': '/dashboard/admin'
    };

    for (const [key, path] of Object.entries(destinations)) {
      if (command.toLowerCase().includes(key)) {
        return path;
      }
    }

    return '/dashboard';
  }

  /**
   * Get current execution status
   */
  public getCurrentExecution(): AITaskExecution | null {
    return this.activeExecution;
  }

  /**
   * Get execution history
   */
  public getExecutionHistory(): AITaskExecution[] {
    return this.executionHistory;
  }
}

// Export singleton instance
export const aiTaskAutomation = new AITaskAutomationSystem();
