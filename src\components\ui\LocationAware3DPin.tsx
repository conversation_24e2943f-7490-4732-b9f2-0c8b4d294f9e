"use client";
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PinContainer } from "@/components/ui/3d-pin";
import { Button } from "@/components/ui/button";
import { MapPin, Navigation, Loader2, ExternalLink } from "lucide-react";
import { LocationData } from "@/types/timeTracking";
import { locationService } from "@/services/locationService";
import { cn } from "@/lib/utils";

interface LocationAware3DPinProps {
  className?: string;
  containerClassName?: string;
  showLoginButton?: boolean;
  onLocationUpdate?: (location: LocationData) => void;
}

export const LocationAware3DPin: React.FC<LocationAware3DPinProps> = ({
  className,
  containerClassName,
  showLoginButton = true,
  onLocationUpdate
}) => {
  const navigate = useNavigate();
  const [location, setLocation] = useState<LocationData | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(true);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isNavigating, setIsNavigating] = useState(false);

  // Fetch user's current location on component mount
  useEffect(() => {
    const fetchLocation = async () => {
      try {
        setIsLoadingLocation(true);
        setLocationError(null);

        console.log('📍 Fetching user location for 3D PIN...');
        console.log('🗺️ World map image should be loading from: /images/world-map.png');

        // Try to get high accuracy location first
        const userLocation = await locationService.getCurrentLocation();

        console.log('📍 Location fetched successfully:', {
          address: userLocation.address,
          coordinates: `${userLocation.latitude}, ${userLocation.longitude}`,
          accuracy: userLocation.accuracy,
          method: userLocation.method
        });

        setLocation(userLocation);

        // Notify parent component if callback provided
        if (onLocationUpdate) {
          onLocationUpdate(userLocation);
        }

      } catch (error) {
        console.error('📍 Failed to fetch location:', error);
        setLocationError(error instanceof Error ? error.message : 'Location unavailable');

        // Set fallback location
        const fallbackLocation: LocationData = {
          latitude: 0,
          longitude: 0,
          address: 'Location unavailable - Please enable location services',
          accuracy: 0,
          method: 'manual'
        };

        setLocation(fallbackLocation);

        if (onLocationUpdate) {
          onLocationUpdate(fallbackLocation);
        }
      } finally {
        setIsLoadingLocation(false);
      }
    };

    fetchLocation();
  }, [onLocationUpdate]);

  // Handle navigation to login page
  const handleLoginNavigation = () => {
    setIsNavigating(true);
    console.log('🔐 Navigating to login page from 3D PIN...');
    
    // Add a small delay for visual feedback
    setTimeout(() => {
      navigate('/auth', { replace: false });
    }, 300);
  };

  // Handle location refresh
  const handleRefreshLocation = async () => {
    setIsLoadingLocation(true);
    setLocationError(null);
    
    try {
      const freshLocation = await locationService.getCurrentLocation();
      setLocation(freshLocation);
      
      if (onLocationUpdate) {
        onLocationUpdate(freshLocation);
      }
      
      console.log('📍 Location refreshed:', freshLocation.address);
    } catch (error) {
      console.error('📍 Failed to refresh location:', error);
      setLocationError(error instanceof Error ? error.message : 'Location refresh failed');
    } finally {
      setIsLoadingLocation(false);
    }
  };

  // Format location display text
  const getLocationDisplayText = () => {
    if (isLoadingLocation) return 'Detecting your location...';
    if (locationError) return 'Location unavailable';
    if (!location) return 'No location data';
    
    // Shorten long addresses for better display
    const address = location.address;
    if (address.length > 50) {
      return address.substring(0, 47) + '...';
    }
    
    return address;
  };

  // Get location accuracy indicator
  const getAccuracyIndicator = () => {
    if (!location || locationError) return null;
    
    if (location.accuracy <= 10) return { color: 'text-green-500', text: 'High accuracy' };
    if (location.accuracy <= 50) return { color: 'text-yellow-500', text: 'Good accuracy' };
    if (location.accuracy <= 100) return { color: 'text-orange-500', text: 'Fair accuracy' };
    return { color: 'text-red-500', text: 'Low accuracy' };
  };

  const accuracyInfo = getAccuracyIndicator();

  return (
    <div className={cn("flex justify-center", containerClassName)}>
      <PinContainer
        title={isLoadingLocation ? "Detecting Location..." : location?.address || "Your Location"}
        href="#"
        containerClassName="h-[220px] w-full"
      >
        <div className="flex basis-full flex-col p-4 tracking-tight text-slate-100/50 sm:basis-1/2 w-[18rem] h-[14rem]">
          {/* Header */}
          <div className="flex items-center gap-2 mb-2">
            <div className="flex items-center gap-1">
              {isLoadingLocation ? (
                <Loader2 className="h-4 w-4 text-red-500 animate-spin" />
              ) : locationError ? (
                <MapPin className="h-4 w-4 text-red-500" />
              ) : (
                <Navigation className="h-4 w-4 text-green-500" />
              )}
              <h3 className="!pb-0 !m-0 font-bold text-sm text-slate-100 dark:text-slate-900">
                Your Current Location
              </h3>
            </div>
            
            {/* Refresh button */}
            {!isLoadingLocation && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefreshLocation}
                className="h-6 w-6 p-0 text-slate-400 hover:text-slate-200"
              >
                <Navigation className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* Location Display */}
          <div className="text-xs !m-0 !p-0 font-normal mb-3">
            <span className={cn(
              "block mb-1",
              locationError ? "text-red-400" : "text-slate-500 dark:text-slate-600"
            )}>
              {getLocationDisplayText()}
            </span>
            
            {/* Accuracy indicator */}
            {accuracyInfo && (
              <span className={cn("text-xs", accuracyInfo.color)}>
                {accuracyInfo.text} ({Math.round(location?.accuracy || 0)}m)
              </span>
            )}
          </div>

          {/* Visual Location Card with World Map PNG */}
          <div className="flex flex-1 w-full rounded-lg relative overflow-hidden">
            {/* World Map PNG as Primary Background */}
            <div className="absolute inset-0 flex items-center justify-center">
              <img
                src="/images/world-map.png"
                alt="World Map"
                className="w-full h-full object-cover rounded-lg"
                style={{
                  filter: 'brightness(0.7) contrast(1.1) saturate(1.2)',
                }}
                onLoad={() => {
                  console.log('🗺️ World map PNG loaded successfully as primary background!');
                }}
                onError={(e) => {
                  console.error('🗺️ World map PNG failed to load, using fallback');
                  // Fallback to red gradient if PNG fails to load
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const fallback = target.parentElement?.querySelector('.world-map-fallback') as HTMLElement;
                  if (fallback) fallback.style.display = 'block';
                }}
              />

              {/* Fallback Red Gradient Background */}
              <div className="world-map-fallback hidden w-full h-full relative bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-lg">
                <div className="absolute inset-0 bg-gradient-to-br from-red-400/20 to-transparent"></div>

                {/* Simple world map overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-60">
                  <div className="text-white/40 text-6xl">🌍</div>
                </div>
              </div>
            </div>

            {/* Dark overlay for text readability */}
            <div className="absolute inset-0 bg-gradient-to-br from-black/40 via-transparent to-black/50 rounded-lg"></div>

            {/* Location Info */}
            <div className="absolute bottom-3 left-3 text-white z-10">
              <div className="text-xs font-semibold drop-shadow-lg">CTNL AI Work-Board</div>
              <div className="text-xs opacity-90 flex items-center gap-1 drop-shadow-lg">
                <MapPin className="h-3 w-3" />
                Live Location
              </div>
            </div>

            {/* Coordinates (if available) */}
            {location && !locationError && (
              <div className="absolute top-2 right-2 text-white text-xs z-10 drop-shadow-lg">
                {location.latitude.toFixed(4)}, {location.longitude.toFixed(4)}
              </div>
            )}

            {/* Pulsing location indicator */}
            {location && !locationError && (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10">
                <div className="relative">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse shadow-lg"></div>
                  <div className="absolute inset-0 w-3 h-3 bg-red-400 rounded-full animate-ping opacity-75"></div>
                </div>
              </div>
            )}
          </div>

          {/* Login Button */}
          {showLoginButton && (
            <div className="mt-3">
              <Button
                onClick={handleLoginNavigation}
                disabled={isNavigating}
                className={cn(
                  "w-full bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600",
                  "text-white font-semibold text-sm py-2 transition-all duration-200",
                  "shadow-lg hover:shadow-xl transform hover:scale-105",
                  isNavigating && "opacity-75 cursor-not-allowed"
                )}
              >
                {isNavigating ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Redirecting...
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <ExternalLink className="h-4 w-4" />
                    Continue to Login
                  </div>
                )}
              </Button>
            </div>
          )}
        </div>
      </PinContainer>
    </div>
  );
};
