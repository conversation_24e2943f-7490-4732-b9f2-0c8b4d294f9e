import React, { useState, use<PERSON>emo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Search,
  Wrench,
  Image,
  FileText,
  Calculator,
  Clock,
  List,
  Video,
  Code,
  Database,
  Palette,
  Zap,
  Star,
  TrendingUp,
  X,
  AlertTriangle,
  GitBranch,
  Settings,
  ExternalLink
} from "lucide-react";

// Import tool components
import TextCaseConverter from "@/components/toolz/TextCaseConverter";
import Base64Tool from "@/components/toolz/Base64Tool";
import JsonFormatter from "@/components/toolz/JsonFormatter";
// import { OmniToolsIntegration } from "@/components/omni-tools/OmniToolsPlaceholder.tsx";

// Inline OmniTools placeholder component to avoid build issues
const OmniToolsIntegration = () => {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Omni Tools</h1>
        <p className="text-muted-foreground">
          Advanced tools and utilities for productivity
        </p>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <AlertTriangle className="h-16 w-16 text-yellow-500" />
          </div>
          <CardTitle className="text-xl">Omni Tools Unavailable</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              The Omni Tools integration is currently unavailable because the omni-tools
              components are not included in this build.
            </p>

            <div className="bg-muted p-4 rounded-lg text-left">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <GitBranch className="h-4 w-4" />
                Technical Details:
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• The <code>omni-tools/</code> directory is in <code>.gitignore</code></li>
                <li>• External git repository components were not included in deployment</li>
                <li>• This prevents build errors but makes tools unavailable</li>
              </ul>
            </div>

            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg text-left">
              <h4 className="font-medium mb-2 flex items-center gap-2">
                <Settings className="h-4 w-4" />
                To Enable Omni Tools:
              </h4>
              <ol className="text-sm text-muted-foreground space-y-1">
                <li>1. Remove <code>omni-tools/</code> from <code>.gitignore</code></li>
                <li>2. Re-clone or copy the omni-tools repository</li>
                <li>3. Ensure all tool components are properly imported</li>
                <li>4. Rebuild the application</li>
              </ol>
            </div>

            <div className="flex gap-2 justify-center">
              <Button variant="outline" onClick={() => window.location.href = '/toolz'}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Use Legacy Tools Instead
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/debug'}>
                <Settings className="h-4 w-4 mr-2" />
                Debug Tools
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Tool categories and their tools
const toolCategories = [
  {
    id: 'text',
    name: 'Text Tools',
    icon: FileText,
    color: 'bg-blue-500',
    tools: [
      { name: 'Text Case Converter', description: 'Convert text between different cases', category: 'text', component: TextCaseConverter },
      { name: 'Text Splitter', description: 'Split text into lines or words', category: 'text' },
      { name: 'Text Joiner', description: 'Join text lines with custom separators', category: 'text' },
      { name: 'Text Replacer', description: 'Find and replace text patterns', category: 'text' },
      { name: 'Text Repeater', description: 'Repeat text multiple times', category: 'text' },
      { name: 'Text Reverser', description: 'Reverse text characters or words', category: 'text' },
      { name: 'Base64 Encoder/Decoder', description: 'Encode and decode Base64 text', category: 'text', component: Base64Tool },
      { name: 'Text Statistics', description: 'Analyze text length, words, characters', category: 'text' },
      { name: 'Morse Code Converter', description: 'Convert text to/from Morse code', category: 'text' },
      { name: 'ROT13 Encoder', description: 'Apply ROT13 cipher to text', category: 'text' }
    ]
  },
  {
    id: 'image',
    name: 'Image Tools',
    icon: Image,
    color: 'bg-green-500',
    tools: [
      { name: 'Image Resizer', description: 'Resize images to specific dimensions', category: 'image' },
      { name: 'Image Converter', description: 'Convert between image formats', category: 'image' },
      { name: 'Image Compressor', description: 'Reduce image file size', category: 'image' },
      { name: 'Image Cropper', description: 'Crop images to desired area', category: 'image' },
      { name: 'Image Rotator', description: 'Rotate images by degrees', category: 'image' },
      { name: 'Background Remover', description: 'Remove background from images', category: 'image' },
      { name: 'Image Filter', description: 'Apply filters and effects', category: 'image' },
      { name: 'Watermark Tool', description: 'Add watermarks to images', category: 'image' }
    ]
  },
  {
    id: 'video',
    name: 'Video Tools',
    icon: Video,
    color: 'bg-purple-500',
    tools: [
      { name: 'Video Trimmer', description: 'Cut and trim video clips', category: 'video' },
      { name: 'Video Converter', description: 'Convert video formats', category: 'video' },
      { name: 'Video Compressor', description: 'Reduce video file size', category: 'video' },
      { name: 'Frame Extractor', description: 'Extract frames from videos', category: 'video' },
      { name: 'GIF Creator', description: 'Create GIFs from videos', category: 'video' },
      { name: 'Video Reverser', description: 'Reverse video playback', category: 'video' }
    ]
  },
  {
    id: 'math',
    name: 'Math Tools',
    icon: Calculator,
    color: 'bg-orange-500',
    tools: [
      { name: 'Number Generator', description: 'Generate random numbers', category: 'math' },
      { name: 'Prime Number Generator', description: 'Generate prime numbers', category: 'math' },
      { name: 'Perfect Number Generator', description: 'Generate perfect numbers', category: 'math' },
      { name: 'Number Base Converter', description: 'Convert between number bases', category: 'math' },
      { name: 'Percentage Calculator', description: 'Calculate percentages', category: 'math' },
      { name: 'Unit Converter', description: 'Convert between units', category: 'math' }
    ]
  },
  {
    id: 'time',
    name: 'Date & Time Tools',
    icon: Clock,
    color: 'bg-red-500',
    tools: [
      { name: 'Date Calculator', description: 'Calculate date differences', category: 'time' },
      { name: 'Timezone Converter', description: 'Convert between timezones', category: 'time' },
      { name: 'Unix Timestamp Converter', description: 'Convert Unix timestamps', category: 'time' },
      { name: 'Date Formatter', description: 'Format dates in various styles', category: 'time' },
      { name: 'Age Calculator', description: 'Calculate age from birthdate', category: 'time' }
    ]
  },
  {
    id: 'data',
    name: 'Data Tools',
    icon: Database,
    color: 'bg-cyan-500',
    tools: [
      { name: 'JSON Formatter', description: 'Format and validate JSON', category: 'data', component: JsonFormatter },
      { name: 'CSV Converter', description: 'Convert CSV to other formats', category: 'data' },
      { name: 'XML Formatter', description: 'Format and validate XML', category: 'data' },
      { name: 'URL Encoder/Decoder', description: 'Encode and decode URLs', category: 'data' },
      { name: 'Hash Generator', description: 'Generate MD5, SHA hashes', category: 'data' },
      { name: 'QR Code Generator', description: 'Generate QR codes', category: 'data' }
    ]
  },
  {
    id: 'list',
    name: 'List Tools',
    icon: List,
    color: 'bg-yellow-500',
    tools: [
      { name: 'List Sorter', description: 'Sort lists alphabetically or numerically', category: 'list' },
      { name: 'List Shuffler', description: 'Randomize list order', category: 'list' },
      { name: 'Duplicate Remover', description: 'Remove duplicate items', category: 'list' },
      { name: 'List Merger', description: 'Merge multiple lists', category: 'list' },
      { name: 'List Splitter', description: 'Split lists by criteria', category: 'list' }
    ]
  }
];

const ToolzPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [favoriteTools, setFavoriteTools] = useState<string[]>([]);
  const [selectedTool, setSelectedTool] = useState<any>(null);
  const [isToolModalOpen, setIsToolModalOpen] = useState(false);

  // Flatten all tools for search
  const allTools = useMemo(() => {
    return toolCategories.flatMap(category => 
      category.tools.map(tool => ({ ...tool, categoryId: category.id, categoryName: category.name }))
    );
  }, []);

  // Filter tools based on search and category
  const filteredTools = useMemo(() => {
    let tools = allTools;
    
    if (selectedCategory !== 'all') {
      tools = tools.filter(tool => tool.categoryId === selectedCategory);
    }
    
    if (searchTerm) {
      tools = tools.filter(tool => 
        tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    return tools;
  }, [allTools, searchTerm, selectedCategory]);

  const toggleFavorite = (toolName: string) => {
    setFavoriteTools(prev => 
      prev.includes(toolName) 
        ? prev.filter(name => name !== toolName)
        : [...prev, toolName]
    );
  };

  const handleToolClick = (tool: any) => {
    if (tool.component) {
      setSelectedTool(tool);
      setIsToolModalOpen(true);
    } else {
      alert(`${tool.name} is coming soon!\n\nThis tool is currently under development.`);
    }
  };

  const closeToolModal = () => {
    setIsToolModalOpen(false);
    setSelectedTool(null);
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
            <Wrench className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              TOOLZ
            </h1>
            <p className="text-muted-foreground">
              Powerful web-based tools for everyday tasks
            </p>
          </div>
        </div>
        <Badge variant="secondary" className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
          {allTools.length} Tools Available
        </Badge>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="omni-tools" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="omni-tools">Omni Tools</TabsTrigger>
          <TabsTrigger value="legacy-tools">Legacy Tools</TabsTrigger>
        </TabsList>

        <TabsContent value="omni-tools">
          <OmniToolsIntegration />
        </TabsContent>

        <TabsContent value="legacy-tools" className="space-y-6">

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search tools..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Tabs value={selectedCategory} onValueChange={setSelectedCategory} className="w-full sm:w-auto">
              <TabsList className="grid grid-cols-4 sm:grid-cols-8 w-full">
                <TabsTrigger value="all" className="text-xs">All</TabsTrigger>
                {toolCategories.map(category => (
                  <TabsTrigger key={category.id} value={category.id} className="text-xs">
                    {category.name.split(' ')[0]}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {filteredTools.map((tool, index) => {
          const category = toolCategories.find(cat => cat.id === tool.categoryId);
          const IconComponent = category?.icon || Wrench;
          const isFavorite = favoriteTools.includes(tool.name);
          
          return (
            <Card 
              key={`${tool.categoryId}-${tool.name}-${index}`}
              className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 hover:scale-[1.02]"
              style={{ borderLeftColor: category?.color.replace('bg-', '#') }}
              onClick={() => handleToolClick(tool)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <div className={`p-2 ${category?.color} rounded-lg`}>
                      <IconComponent className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-sm font-semibold line-clamp-1">
                        {tool.name}
                      </CardTitle>
                      <Badge variant="outline" className="text-xs mt-1">
                        {tool.categoryName}
                      </Badge>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFavorite(tool.name);
                    }}
                    className="h-8 w-8 p-0"
                  >
                    <Star 
                      className={`h-4 w-4 ${isFavorite ? 'fill-yellow-400 text-yellow-400' : 'text-gray-400'}`} 
                    />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {tool.description}
                </p>
                <div className="flex items-center justify-between mt-3">
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <TrendingUp className="h-3 w-3" />
                    Popular
                  </div>
                  <Button size="sm" variant="outline" className="text-xs">
                    Use Tool
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* No results */}
      {filteredTools.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Wrench className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tools found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search terms or category filter.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{allTools.length}</div>
            <div className="text-sm text-muted-foreground">Total Tools</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{toolCategories.length}</div>
            <div className="text-sm text-muted-foreground">Categories</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{favoriteTools.length}</div>
            <div className="text-sm text-muted-foreground">Favorites</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{filteredTools.length}</div>
            <div className="text-sm text-muted-foreground">Filtered</div>
          </CardContent>
        </Card>
      </div>

      {/* Tool Modal */}
      <Dialog open={isToolModalOpen} onOpenChange={setIsToolModalOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              {selectedTool?.name}
              <Button
                variant="ghost"
                size="sm"
                onClick={closeToolModal}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            {selectedTool?.component && React.createElement(selectedTool.component)}
          </div>
        </DialogContent>
      </Dialog>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ToolzPage;
