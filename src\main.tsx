import { Theme<PERSON>rovider } from '@/components/theme-provider'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { StrictMode } from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Check environment variables for production deployment
import '@/utils/env-check'

// Register service worker for PWA functionality

// Conditionally import cache utilities only in development
if (import.meta.env.DEV) {
  import('@/utils/cacheFixer');
  import('@/utils/cacheManager');
}

// @ts-ignore - AOS types not available
import AOS from 'aos'
import 'aos/dist/aos.css'

// Initialize AOS with custom settings
AOS.init({
  duration: 800,
  easing: 'ease-in-out',
  once: true,
  mirror: false,
  anchorPlacement: 'top-bottom',
  offset: 50,
});

// Create a client with optimized performance settings and aggressive cache clearing
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1 * 60 * 1000, // 1 minute for faster cache invalidation
      gcTime: 2 * 60 * 1000, // 2 minutes cache retention for faster clearing (renamed from cacheTime in v5)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false, // Disable refetch on window focus for better performance
      refetchOnReconnect: true, // Refetch when connection is restored
    },
    mutations: {
      retry: 1,
      retryDelay: 1000,
    },
  },
})

// Initialize cache management only in development
if (import.meta.env.DEV) {
  // Dynamic import to avoid circular dependencies in production
  import('@/utils/cacheManager').then(({ cacheManager }) => {
    cacheManager.setQueryClient(queryClient);

    // Clear cache on page load/refresh to handle server restarts
    const clearCacheOnLoad = () => {
      if (cacheManager.shouldClearCache(30)) {
        console.log('🧹 Clearing React Query cache due to server restart or time threshold');
        cacheManager.clearAll();
      }
      cacheManager.handleServerRestart();
    };

    clearCacheOnLoad();
    cacheManager.startPeriodicCleanup(60);
  }).catch(() => {
    // Silently fail if cache manager is not available
  });
}



// Make cache clearing available globally for console access (development only)
if (typeof window !== 'undefined' && import.meta.env.DEV) {
  (window as any).clearCache = () => {
    console.log('🧹 Clearing cache from console...');
    import('@/utils/cacheManager').then(({ cacheManager }) => {
      cacheManager.clearAll();
      localStorage.removeItem('lastCacheClear');
      localStorage.removeItem('serverStartTime');
      console.log('✅ Cache cleared! Reload the page for best results.');
    });
    return 'Cache cleared successfully!';
  };

  (window as any).clearDashboardCache = () => {
    console.log('🧹 Clearing dashboard cache from console...');
    import('@/utils/cacheManager').then(({ cacheManager }) => {
      cacheManager.clearDashboardCache();
      console.log('✅ Dashboard cache cleared!');
    });
    return 'Dashboard cache cleared successfully!';
  };

  // Add database debugging helper
  (window as any).fixDatabaseErrors = () => {
    console.log('🔧 Database Error Fix Helper');
    console.log('If you are seeing project assignment errors, try these commands:');
    console.log('1. checkDatabaseSchema() - Check current table structure');
    console.log('2. testProjectAssignment() - Test assignment creation');
    console.log('3. getDatabaseInfo() - Get detailed database information');
    console.log('4. analyzeDatabase() - Comprehensive database analysis');
    console.log('5. checkAssetsTable() - Check assets inventory table');
    console.log('6. testDashboardData() - Test dashboard data sources');
    console.log('7. quickFixDashboard() - Create sample data for empty dashboard');
    console.log('8. fixDuplicateDepartments() - Fix duplicate department errors');
    console.log('9. quickFixDuplicates() - Quick fix for current duplicate error');
    console.log('10. createSampleBatteryReports() - Create sample battery reports for testing');
    console.log('11. migrateToCustomFolders() - Migrate to new custom folder system');
    console.log('');
    console.log('These functions are available after the app loads.');
    return 'Database debugging commands listed above.';
  };

  // Add battery reports helper
  (window as any).createSampleBatteryReports = async () => {
    try {
      const { createSampleBatteryReports } = await import('@/scripts/create-sample-battery-reports');
      return await createSampleBatteryReports();
    } catch (error) {
      console.error('Failed to load battery reports script:', error);
      return false;
    }
  };

  // Add folder migration helper
  (window as any).migrateToCustomFolders = async () => {
    try {
      const { migrateToCustomFolders } = await import('@/scripts/migrate-to-custom-folders');
      return await migrateToCustomFolders();
    } catch (error) {
      console.error('Failed to load folder migration script:', error);
      return false;
    }
  };
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeProvider defaultTheme="dark" storageKey="ctnl-theme">
      <QueryClientProvider client={queryClient}>
        <App />
      </QueryClientProvider>
    </ThemeProvider>
  </StrictMode>
);

// Initialize PWA features after app renders
if (import.meta.env.PROD) {
  // Register service worker in production
  import('@/utils/sw-registration').then(({ registerServiceWorker }) => {
    registerServiceWorker();
    console.log('🚀 PWA features initialized');
  }).catch((error) => {
    console.warn('PWA initialization failed:', error);
  });
}
