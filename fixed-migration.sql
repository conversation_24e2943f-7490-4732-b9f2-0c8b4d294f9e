-- Fixed Migration for LangChain & Real-time Features
-- Run this in Supabase Dashboard SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- First, let's check the existing api_keys table structure
-- and modify it if needed
DO $$
BEGIN
    -- Add provider column to api_keys if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'api_keys' AND column_name = 'provider'
    ) THEN
        ALTER TABLE api_keys ADD COLUMN provider TEXT;
        ALTER TABLE api_keys ADD CONSTRAINT api_keys_provider_unique UNIQUE (provider);
    END IF;
    
    -- Add is_active column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'api_keys' AND column_name = 'is_active'
    ) THEN
        ALTER TABLE api_keys ADD COLUMN is_active BOOLEAN DEFAULT true;
    END IF;
    
    -- Add api_key column if it doesn't exist (might be named differently)
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'api_keys' AND column_name = 'api_key'
    ) THEN
        -- Check if there's a 'key' column instead
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'api_keys' AND column_name = 'key'
        ) THEN
            ALTER TABLE api_keys RENAME COLUMN key TO api_key;
        ELSE
            ALTER TABLE api_keys ADD COLUMN api_key TEXT;
        END IF;
    END IF;
END $$;

-- Now create the new tables for LangChain features
DO $$
BEGIN
    -- LangChain Conversations Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_conversations') THEN
        CREATE TABLE langchain_conversations (
            id TEXT PRIMARY KEY,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            title TEXT,
            messages JSONB DEFAULT '[]'::jsonb,
            context JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_langchain_conversations_user_id ON langchain_conversations(user_id);
        CREATE INDEX idx_langchain_conversations_updated_at ON langchain_conversations(updated_at);
        
        -- Enable RLS
        ALTER TABLE langchain_conversations ENABLE ROW LEVEL SECURITY;
        
        -- Create policy
        CREATE POLICY "Users can manage their own conversations" ON langchain_conversations
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
    END IF;

    -- LangChain Documents Table (Vector Store)
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_documents') THEN
        CREATE TABLE langchain_documents (
            id TEXT PRIMARY KEY,
            content TEXT NOT NULL,
            metadata JSONB DEFAULT '{}'::jsonb,
            embedding vector(1536), -- OpenAI text-embedding-3-small dimensions
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_langchain_documents_embedding ON langchain_documents USING ivfflat (embedding vector_cosine_ops);
        CREATE INDEX idx_langchain_documents_metadata ON langchain_documents USING gin(metadata);
        
        -- Enable RLS
        ALTER TABLE langchain_documents ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "Authenticated users can read documents" ON langchain_documents
        FOR SELECT TO authenticated USING (true);
        
        CREATE POLICY "Authenticated users can insert documents" ON langchain_documents
        FOR INSERT TO authenticated WITH CHECK (true);
    END IF;

    -- LangChain Document Metadata Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'langchain_document_metadata') THEN
        CREATE TABLE langchain_document_metadata (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            metadata JSONB DEFAULT '{}'::jsonb,
            chunk_count INTEGER DEFAULT 0,
            processing_time INTEGER DEFAULT 0,
            total_tokens INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Enable RLS
        ALTER TABLE langchain_document_metadata ENABLE ROW LEVEL SECURITY;
        
        -- Create policy
        CREATE POLICY "Authenticated users can read document metadata" ON langchain_document_metadata
        FOR SELECT TO authenticated USING (true);
    END IF;

    -- User Presence Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_presence') THEN
        CREATE TABLE user_presence (
            user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
            status TEXT CHECK (status IN ('online', 'away', 'busy', 'offline')) DEFAULT 'offline',
            last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            current_page TEXT,
            is_typing BOOLEAN DEFAULT FALSE,
            metadata JSONB DEFAULT '{}'::jsonb,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_user_presence_status ON user_presence(status);
        CREATE INDEX idx_user_presence_last_seen ON user_presence(last_seen);
        
        -- Enable RLS
        ALTER TABLE user_presence ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "Users can manage their own presence" ON user_presence
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
        
        CREATE POLICY "Users can view all presence data" ON user_presence
        FOR SELECT TO authenticated USING (true);
    END IF;

    -- Collaborative Sessions Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'collaborative_sessions') THEN
        CREATE TABLE collaborative_sessions (
            id TEXT PRIMARY KEY,
            type TEXT CHECK (type IN ('document', 'project', 'task', 'meeting')) NOT NULL,
            resource_id TEXT NOT NULL,
            participants JSONB DEFAULT '[]'::jsonb,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_collaborative_sessions_type ON collaborative_sessions(type);
        CREATE INDEX idx_collaborative_sessions_resource_id ON collaborative_sessions(resource_id);
        CREATE INDEX idx_collaborative_sessions_last_activity ON collaborative_sessions(last_activity);
        
        -- Enable RLS
        ALTER TABLE collaborative_sessions ENABLE ROW LEVEL SECURITY;
        
        -- Create policy
        CREATE POLICY "Authenticated users can manage sessions" ON collaborative_sessions
        FOR ALL TO authenticated USING (true) WITH CHECK (true);
    END IF;

    -- Document Comments Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'document_comments') THEN
        CREATE TABLE document_comments (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            document_id TEXT NOT NULL,
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            parent_id UUID REFERENCES document_comments(id) ON DELETE CASCADE,
            content TEXT NOT NULL,
            position INTEGER NOT NULL,
            resolved BOOLEAN DEFAULT FALSE,
            metadata JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_document_comments_document_id ON document_comments(document_id);
        CREATE INDEX idx_document_comments_user_id ON document_comments(user_id);
        CREATE INDEX idx_document_comments_parent_id ON document_comments(parent_id);
        
        -- Enable RLS
        ALTER TABLE document_comments ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "Users can manage their own comments" ON document_comments
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
        
        CREATE POLICY "Users can read all comments" ON document_comments
        FOR SELECT TO authenticated USING (true);
    END IF;

    -- Real-time Notifications Table
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'realtime_notifications') THEN
        CREATE TABLE realtime_notifications (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            type TEXT NOT NULL,
            title TEXT NOT NULL,
            message TEXT NOT NULL,
            data JSONB DEFAULT '{}'::jsonb,
            read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        CREATE INDEX idx_realtime_notifications_user_id ON realtime_notifications(user_id);
        CREATE INDEX idx_realtime_notifications_read ON realtime_notifications(read);
        
        -- Enable RLS
        ALTER TABLE realtime_notifications ENABLE ROW LEVEL SECURITY;
        
        -- Create policy
        CREATE POLICY "Users can manage their own notifications" ON realtime_notifications
        FOR ALL TO authenticated USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);
    END IF;
END $$;

-- Vector similarity search function
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 5
)
RETURNS TABLE (
    id text,
    content text,
    metadata jsonb,
    similarity float
)
LANGUAGE sql STABLE
AS $$
    SELECT
        langchain_documents.id,
        langchain_documents.content,
        langchain_documents.metadata,
        1 - (langchain_documents.embedding <=> query_embedding) AS similarity
    FROM langchain_documents
    WHERE 1 - (langchain_documents.embedding <=> query_embedding) > match_threshold
    ORDER BY similarity DESC
    LIMIT match_count;
$$;

-- Function to update user presence
CREATE OR REPLACE FUNCTION update_user_presence(
    p_user_id UUID,
    p_status TEXT DEFAULT NULL,
    p_current_page TEXT DEFAULT NULL,
    p_is_typing BOOLEAN DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO user_presence (user_id, status, current_page, is_typing, last_seen, updated_at)
    VALUES (p_user_id, COALESCE(p_status, 'online'), p_current_page, COALESCE(p_is_typing, FALSE), NOW(), NOW())
    ON CONFLICT (user_id)
    DO UPDATE SET
        status = COALESCE(p_status, user_presence.status),
        current_page = COALESCE(p_current_page, user_presence.current_page),
        is_typing = COALESCE(p_is_typing, user_presence.is_typing),
        last_seen = NOW(),
        updated_at = NOW();
END;
$$;

-- Insert or update OpenAI API key
-- REPLACE 'your-openai-api-key-here' WITH YOUR ACTUAL OPENAI API KEY
INSERT INTO api_keys (provider, api_key, is_active) 
VALUES (
    'openai', 
    'your-openai-api-key-here', -- REPLACE THIS WITH YOUR REAL API KEY
    true
) ON CONFLICT (provider) DO UPDATE SET 
    api_key = EXCLUDED.api_key,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Verify installation
SELECT 
    'LangChain and Real-time collaboration features installed successfully!' as status,
    COUNT(*) as new_tables_created
FROM information_schema.tables 
WHERE table_name IN (
    'langchain_conversations',
    'langchain_documents', 
    'langchain_document_metadata',
    'user_presence',
    'collaborative_sessions',
    'document_comments',
    'realtime_notifications'
);

-- Show API key status
SELECT 
    provider,
    CASE 
        WHEN api_key IS NOT NULL THEN 'Configured'
        ELSE 'Not configured'
    END as status,
    is_active
FROM api_keys 
WHERE provider = 'openai';
