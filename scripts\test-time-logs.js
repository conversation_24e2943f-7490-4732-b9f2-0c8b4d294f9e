import { createClient } from '@supabase/supabase-js';

// Load environment variables
const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase environment variables');
  console.log('VITE_SUPABASE_URL:', supabaseUrl);
  console.log('VITE_SUPABASE_ANON_KEY:', supabaseAnonKey ? 'Set' : 'Not set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testTimeLogs() {
  try {
    console.log('Testing time_logs table access...');
    
    // Test 1: Check if time_logs table exists and is accessible
    console.log('\n1. Testing time_logs table access...');
    const { data: timeLogs, error: timeLogsError } = await supabase
      .from('time_logs')
      .select('id, user_id, clock_in, clock_out')
      .limit(5);
    
    if (timeLogsError) {
      console.error('❌ Error accessing time_logs table:', timeLogsError);
    } else {
      console.log('✅ time_logs table accessible');
      console.log(`   Found ${timeLogs?.length || 0} records`);
    }
    
    // Test 2: Check if get_realtime_clock_status function exists
    console.log('\n2. Testing get_realtime_clock_status function...');
    const { data: realtimeStatus, error: realtimeError } = await supabase
      .rpc('get_realtime_clock_status');
    
    if (realtimeError) {
      console.error('❌ Error calling get_realtime_clock_status:', realtimeError);
      if (realtimeError.message.includes('function get_realtime_clock_status() does not exist')) {
        console.log('   The function needs to be created in the database');
      }
    } else {
      console.log('✅ get_realtime_clock_status function works');
      console.log(`   Returned ${realtimeStatus?.length || 0} user statuses`);
    }
    
    // Test 3: Check if clock_in_records table exists (should not exist)
    console.log('\n3. Testing clock_in_records table (should not exist)...');
    const { data: clockInRecords, error: clockInRecordsError } = await supabase
      .from('clock_in_records')
      .select('*')
      .limit(1);
    
    if (clockInRecordsError) {
      if (clockInRecordsError.message.includes('relation "public.clock_in_records" does not exist')) {
        console.log('✅ clock_in_records table correctly does not exist');
      } else {
        console.error('❌ Unexpected error with clock_in_records:', clockInRecordsError);
      }
    } else {
      console.log('⚠️  clock_in_records table still exists (should be dropped)');
    }
    
    // Test 4: Test the specific query that was failing
    console.log('\n4. Testing the specific failing query pattern...');
    const today = new Date().toISOString().split('T')[0];
    const { data: todayLogs, error: todayLogsError } = await supabase
      .from('time_logs')
      .select('user_id, clock_in, clock_out')
      .gte('clock_in', `${today}T00:00:00`)
      .lte('clock_in', `${today}T23:59:59`)
      .order('clock_in', { ascending: false });
    
    if (todayLogsError) {
      console.error('❌ Error with today\'s logs query:', todayLogsError);
    } else {
      console.log('✅ Today\'s logs query works');
      console.log(`   Found ${todayLogs?.length || 0} logs for today`);
    }
    
    console.log('\n=== Test Summary ===');
    const tests = [
      { name: 'time_logs table access', passed: !timeLogsError },
      { name: 'get_realtime_clock_status function', passed: !realtimeError },
      { name: 'clock_in_records table removed', passed: clockInRecordsError?.message?.includes('does not exist') },
      { name: 'today\'s logs query', passed: !todayLogsError }
    ];
    
    const passedTests = tests.filter(t => t.passed).length;
    console.log(`${passedTests}/${tests.length} tests passed`);
    
    tests.forEach(test => {
      console.log(`${test.passed ? '✅' : '❌'} ${test.name}`);
    });
    
    if (passedTests === tests.length) {
      console.log('\n🎉 All tests passed! Database schema is fixed.');
    } else {
      console.log('\n⚠️  Some tests failed. Database migration may need to be applied.');
    }
    
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

testTimeLogs();
