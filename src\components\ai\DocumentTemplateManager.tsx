import { useAuth } from "@/components/auth/AuthProvider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
    BarChart3,
    Database,
    FileSpreadsheet,
    FileText,
    Palette,
    Plus,
    Save,
    Settings,
    X
} from "lucide-react";
import { useState } from 'react';

interface TemplateFormData {
  name: string;
  description: string;
  template_type: string;
  category: string;
  template_config: any;
  ai_prompts: any;
  chart_configs: any[];
  table_configs: any[];
  styling_config: any;
  data_sources: any[];
  variables: any[];
  is_public: boolean;
}

interface ChartConfig {
  id: string;
  type: string;
  title: string;
  dataSource: string;
  xAxis: string;
  yAxis: string;
  colors: string[];
}

interface TableConfig {
  id: string;
  name: string;
  dataSource: string;
  columns: string[];
  formatting: any;
}

interface DataSource {
  id: string;
  name: string;
  type: string;
  query: string;
  connection: any;
}

export const DocumentTemplateManager = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [isCreateOpen, setIsCreateOpen] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    description: '',
    template_type: 'excel',
    category: 'general',
    template_config: {},
    ai_prompts: {},
    chart_configs: [],
    table_configs: [],
    styling_config: {
      theme: 'modern',
      primaryColor: '#2563eb',
      secondaryColor: '#64748b',
      fontFamily: 'Arial',
      fontSize: 11
    },
    data_sources: [],
    variables: [],
    is_public: false
  });

  const [charts, setCharts] = useState<ChartConfig[]>([]);
  const [tables, setTables] = useState<TableConfig[]>([]);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);

  // Create template mutation
  const createTemplateMutation = useMutation({
    mutationFn: async (data: TemplateFormData) => {
      if (!userProfile?.id) throw new Error('User not authenticated');

      const { data: template, error } = await supabase
        .from('document_templates')
        .insert([{
          ...data,
          chart_configs: charts,
          table_configs: tables,
          data_sources: dataSources,
          created_by: userProfile.id
        }])
        .select()
        .single();

      if (error) throw error;
      return template;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['document-templates'] });
      setIsCreateOpen(false);
      resetForm();

      toast({
        title: "Template Created",
        description: "Document template has been created successfully.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Creation Failed",
        description: error.message || "Failed to create template.",
        variant: "destructive",
      });
    }
  });

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      template_type: 'excel',
      category: 'general',
      template_config: {},
      ai_prompts: {},
      chart_configs: [],
      table_configs: [],
      styling_config: {
        theme: 'modern',
        primaryColor: '#2563eb',
        secondaryColor: '#64748b',
        fontFamily: 'Arial',
        fontSize: 11
      },
      data_sources: [],
      variables: [],
      is_public: false
    });
    setCharts([]);
    setTables([]);
    setDataSources([]);
    setCurrentStep(1);
  };

  const addChart = () => {
    const newChart: ChartConfig = {
      id: Date.now().toString(),
      type: 'bar',
      title: 'New Chart',
      dataSource: '',
      xAxis: '',
      yAxis: '',
      colors: ['#2563eb', '#dc2626', '#16a34a']
    };
    setCharts([...charts, newChart]);
  };

  const updateChart = (id: string, updates: Partial<ChartConfig>) => {
    setCharts(charts.map(chart =>
      chart.id === id ? { ...chart, ...updates } : chart
    ));
  };

  const removeChart = (id: string) => {
    setCharts(charts.filter(chart => chart.id !== id));
  };

  const addTable = () => {
    const newTable: TableConfig = {
      id: Date.now().toString(),
      name: 'New Table',
      dataSource: '',
      columns: [],
      formatting: {}
    };
    setTables([...tables, newTable]);
  };

  const updateTable = (id: string, updates: Partial<TableConfig>) => {
    setTables(tables.map(table =>
      table.id === id ? { ...table, ...updates } : table
    ));
  };

  const removeTable = (id: string) => {
    setTables(tables.filter(table => table.id !== id));
  };

  const addDataSource = () => {
    const newDataSource: DataSource = {
      id: Date.now().toString(),
      name: 'New Data Source',
      type: 'database_table',
      query: '',
      connection: {}
    };
    setDataSources([...dataSources, newDataSource]);
  };

  const updateDataSource = (id: string, updates: Partial<DataSource>) => {
    setDataSources(dataSources.map(ds =>
      ds.id === id ? { ...ds, ...updates } : ds
    ));
  };

  const removeDataSource = (id: string) => {
    setDataSources(dataSources.filter(ds => ds.id !== id));
  };

  const handleSubmit = () => {
    if (!formData.name || !formData.description) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    createTemplateMutation.mutate(formData);
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Basic Information</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">Template Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter template name"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="template_type">Template Type</Label>
          <Select
            value={formData.template_type}
            onValueChange={(value) => setFormData(prev => ({ ...prev, template_type: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="excel">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  Excel Workbook
                </div>
              </SelectItem>
              <SelectItem value="pdf">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  PDF Report
                </div>
              </SelectItem>
              <SelectItem value="dashboard">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Interactive Dashboard
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select
            value={formData.category}
            onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="general">General</SelectItem>
              <SelectItem value="financial">Financial</SelectItem>
              <SelectItem value="hr">Human Resources</SelectItem>
              <SelectItem value="project">Project Management</SelectItem>
              <SelectItem value="sales">Sales & Marketing</SelectItem>
              <SelectItem value="operations">Operations</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center space-x-2 pt-6">
          <input
            type="checkbox"
            id="is_public"
            checked={formData.is_public}
            onChange={(e) => setFormData(prev => ({ ...prev, is_public: e.target.checked }))}
            className="rounded"
          />
          <Label htmlFor="is_public">Make template public</Label>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description *</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Describe what this template is for..."
          rows={3}
        />
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Data Sources</h3>
        <Button onClick={addDataSource} size="sm">
          <Plus className="h-4 w-4 mr-1" />
          Add Source
        </Button>
      </div>

      {dataSources.length === 0 ? (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <Database className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-muted-foreground">No data sources configured</p>
          <Button onClick={addDataSource} className="mt-2">
            <Plus className="h-4 w-4 mr-1" />
            Add Data Source
          </Button>
        </div>
      ) : (
        <div className="space-y-3">
          {dataSources.map((dataSource) => (
            <Card key={dataSource.id}>
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-3">
                  <Input
                    value={dataSource.name}
                    onChange={(e) => updateDataSource(dataSource.id, { name: e.target.value })}
                    className="font-medium"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeDataSource(dataSource.id)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-2">
                    <Label>Source Type</Label>
                    <Select
                      value={dataSource.type}
                      onValueChange={(value) => updateDataSource(dataSource.id, { type: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="database_table">Database Table</SelectItem>
                        <SelectItem value="api_endpoint">API Endpoint</SelectItem>
                        <SelectItem value="csv_file">CSV File</SelectItem>
                        <SelectItem value="json_data">JSON Data</SelectItem>
                        <SelectItem value="real_time_query">Real-time Query</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Query/Connection</Label>
                    <Textarea
                      value={dataSource.query}
                      onChange={(e) => updateDataSource(dataSource.id, { query: e.target.value })}
                      placeholder="SQL query, API endpoint, or connection details..."
                      rows={2}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Template
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Document Template</DialogTitle>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="flex items-center justify-center space-x-4 mb-6">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentStep >= step
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {step}
              </div>
              {step < 4 && (
                <div className={`w-12 h-1 ${
                  currentStep > step ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && (
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-muted-foreground">Charts configuration coming soon...</p>
            </div>
          )}
          {currentStep === 4 && (
            <div className="text-center py-8">
              <Palette className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-muted-foreground">Styling configuration coming soon...</p>
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
          >
            Previous
          </Button>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setIsCreateOpen(false);
                resetForm();
              }}
            >
              Cancel
            </Button>

            {currentStep < 4 ? (
              <Button onClick={() => setCurrentStep(currentStep + 1)}>
                Next
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                disabled={createTemplateMutation.isPending}
                className="flex items-center gap-2"
              >
                {createTemplateMutation.isPending ? (
                  <>
                    <Settings className="h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Create Template
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
