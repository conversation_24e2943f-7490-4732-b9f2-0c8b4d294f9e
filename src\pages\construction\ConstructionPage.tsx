
import { ConstructionReportGenerator } from "@/components/construction/ConstructionReportGenerator";
import { ConstructionSiteManagement } from "@/components/construction/ConstructionSiteManagement";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import {
    AlertTriangle,
    Building,
    Calendar,
    CheckCircle,
    Clock,
    FileText,
    MapPin,
    Plus
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

const ConstructionPage = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [selectedSite, setSelectedSite] = useState<any>(null);
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
  const [isSiteManagementOpen, setIsSiteManagementOpen] = useState(false);

  // Fetch construction sites from database
  const { data: sites, isLoading: sitesLoading } = useQuery({
    queryKey: ['construction-sites'],
    queryFn: async () => {
      // Try the query with relationships first
      let { data, error } = await supabase
        .from('construction_sites')
        .select(`
          id,
          site_name,
          location,
          status,
          site_type,
          start_date,
          expected_completion,
          budget_allocated,
          budget_spent,
          contractor_name,
          safety_rating,
          site_manager:profiles!site_manager_id(full_name),
          created_at
        `)
        .order('created_at', { ascending: false });

      // If the query fails (400, 404, 406, or PGRST116 errors), fall back to basic query
      if (error && (error.code === 'PGRST116' || error.status === 400 || error.status === 404 || error.status === 406)) {
        console.log('Falling back to basic construction_sites query, error:', error);
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('construction_sites')
          .select('*')
          .order('created_at', { ascending: false });

        data = fallbackData;
        error = fallbackError;
      }

      // If table doesn't exist, return empty array
      if (error && (error.status === 404 || error.code === 'PGRST106')) {
        console.log('construction_sites table not found, returning empty array');
        return [];
      }

      if (error) throw error;
      return data || [];
    },
  });

  // Fetch construction reports for statistics
  const { data: reports } = useQuery({
    queryKey: ['construction-reports'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('construction_reports')
        .select('id, progress_percentage, safety_incidents, created_at')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
  });

  // Calculate statistics
  const stats = {
    totalSites: sites?.length || 0,
    activeSites: sites?.filter(site => site.status === 'active').length || 0,
    completedSites: sites?.filter(site => site.status === 'completed').length || 0,
    safetyIncidents: reports?.filter(report => report.safety_incidents && report.safety_incidents.trim() !== '').length || 0
  };

  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Active</Badge>;
      case 'completed':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>;
      case 'planning':
        return <Badge className="bg-yellow-100 text-yellow-800"><Calendar className="h-3 w-3 mr-1" />Planning</Badge>;
      case 'on_hold':
        return <Badge className="bg-orange-100 text-orange-800"><AlertTriangle className="h-3 w-3 mr-1" />On Hold</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const calculateProgress = (site: any) => {
    if (site.status === 'completed') return 100;
    if (site.status === 'planning') return 0;

    // Calculate progress based on budget spent vs allocated
    if (site.budget_allocated && site.budget_spent) {
      return Math.min(Math.round((site.budget_spent / site.budget_allocated) * 100), 100);
    }

    // Default progress based on status
    switch (site.status) {
      case 'active': return 50;
      case 'on_hold': return 25;
      default: return 0;
    }
  };

  return (
    <div className="space-y-6" data-aos="fade-up">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4" data-aos="fade-up" data-aos-delay="100">
          <Card>
            <CardContent className="flex items-center p-6">
              <Building className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Total Sites</p>
                <p className="text-2xl font-bold">{stats.totalSites}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <Clock className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Active Sites</p>
                <p className="text-2xl font-bold">{stats.activeSites}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Completed</p>
                <p className="text-2xl font-bold">{stats.completedSites}</p>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="flex items-center p-6">
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-muted-foreground">Safety Incidents</p>
                <p className="text-2xl font-bold">{stats.safetyIncidents}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-2" data-aos="fade-up" data-aos-delay="200">
          <Dialog open={isSiteManagementOpen} onOpenChange={setIsSiteManagementOpen}>
            <DialogTrigger asChild>
              <Button onClick={() => setIsSiteManagementOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add New Site
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Construction Site Management</DialogTitle>
              </DialogHeader>
              <ConstructionSiteManagement />
            </DialogContent>
          </Dialog>
        </div>

        {/* Sites Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6" data-aos="fade-up" data-aos-delay="300">
          {sitesLoading ? (
            <div className="col-span-full text-center py-8">
              <p className="text-muted-foreground">Loading construction sites...</p>
            </div>
          ) : sites && sites.length > 0 ? (
            sites.map((site) => {
              const progress = calculateProgress(site);
              return (
                <Card key={site.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg">{site.site_name}</CardTitle>
                        <div className="flex items-center text-sm text-muted-foreground mt-1">
                          <MapPin className="h-4 w-4 mr-1" />
                          {site.location}
                        </div>
                      </div>
                      {getStatusBadge(site.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Manager</p>
                        <p className="font-medium">{site.project_manager?.full_name || 'Not assigned'}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Type</p>
                        <p className="font-medium capitalize">{site.site_type}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Timeline</p>
                        <p className="font-medium">
                          {site.start_date ? new Date(site.start_date).toLocaleDateString() : 'TBD'} - {site.expected_completion ? new Date(site.expected_completion).toLocaleDateString() : 'TBD'}
                        </p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Contractor</p>
                        <p className="font-medium">{site.contractor_name || 'Not assigned'}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-muted-foreground">Budget</p>
                        <p className="font-medium">₦{site.budget_allocated?.toLocaleString() || '0'}</p>
                      </div>
                      <div>
                        <p className="text-muted-foreground">Spent</p>
                        <p className="font-medium">₦{site.budget_spent?.toLocaleString() || '0'}</p>
                      </div>
                    </div>
                
                    <div className="flex gap-2 pt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1"
                        onClick={() => {
                          toast({
                            title: "Site Details",
                            description: `Viewing details for ${site.site_name}`,
                          });
                          setSelectedSite(site);
                          setIsSiteManagementOpen(true);
                        }}
                      >
                        View Details
                      </Button>
                      <Dialog open={isReportDialogOpen && selectedSite?.id === site.id} onOpenChange={(open) => {
                        setIsReportDialogOpen(open);
                        if (!open) setSelectedSite(null);
                      }}>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                            onClick={() => {
                              setSelectedSite(site);
                              setIsReportDialogOpen(true);
                            }}
                          >
                            <FileText className="h-4 w-4 mr-2" />
                            Generate Report
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                          <DialogHeader>
                            <DialogTitle>Generate Construction Report</DialogTitle>
                          </DialogHeader>
                          <ConstructionReportGenerator
                            siteId={selectedSite?.id?.toString()}
                            siteName={selectedSite?.site_name}
                            onReportGenerated={() => {
                              setIsReportDialogOpen(false);
                              setSelectedSite(null);
                              toast({
                                title: "Success",
                                description: `Report generated for ${selectedSite?.site_name}`,
                              });
                            }}
                          />
                        </DialogContent>
                      </Dialog>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          ) : (
            <div className="col-span-full text-center py-8">
              <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground mb-4">No construction sites found</p>
              <Button onClick={() => setIsSiteManagementOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Site
              </Button>
            </div>
          )}
        </div>
    </div>
  );
};

export default ConstructionPage;
