<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Status Check - CTN Nigeria</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2d3748;
            margin: 0;
            font-size: 2.5rem;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .status-card {
            background: #f7fafc;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }
        .status-card.success {
            border-left-color: #38a169;
            background: #f0fff4;
        }
        .status-card.warning {
            border-left-color: #ed8936;
            background: #fffaf0;
        }
        .status-card.error {
            border-left-color: #e53e3e;
            background: #fff5f5;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .button {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .button:disabled {
            background: #cbd5e0;
            cursor: not-allowed;
            transform: none;
        }
        .log {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
            font-size: 14px;
            line-height: 1.5;
        }
        .summary {
            background: #ebf8ff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #90cdf4;
            margin: 20px 0;
        }
        .summary h3 {
            margin: 0 0 15px 0;
            color: #2b6cb0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 System Status Check</h1>
            <p>Comprehensive analysis of CTN Nigeria system functionality</p>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button id="checkBtn" class="button" onclick="runSystemCheck()">
                🚀 Run System Check
            </button>
            
            <button id="testFeaturesBtn" class="button" onclick="testCoreFeatures()" disabled>
                🧪 Test Core Features
            </button>
        </div>

        <div id="summary" class="summary" style="display: none;">
            <h3>📊 System Summary</h3>
            <div id="summaryContent"></div>
        </div>

        <div id="statusGrid" class="status-grid"></div>

        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        let systemStatus = {};
        
        function log(message) {
            const logDiv = document.getElementById('log');
            logDiv.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function createStatusCard(title, status, details, icon = '📋') {
            const statusClass = status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'error';
            const statusIcon = status === 'success' ? '✅' : status === 'warning' ? '⚠️' : '❌';
            
            return `
                <div class="status-card ${statusClass}">
                    <h3>${icon} ${title} ${statusIcon}</h3>
                    <p>${details}</p>
                </div>
            `;
        }
        
        function updateStatusGrid() {
            const grid = document.getElementById('statusGrid');
            grid.innerHTML = Object.entries(systemStatus).map(([key, data]) => 
                createStatusCard(data.title, data.status, data.details, data.icon)
            ).join('');
        }
        
        function updateSummary() {
            const summary = document.getElementById('summary');
            const content = document.getElementById('summaryContent');
            
            const total = Object.keys(systemStatus).length;
            const success = Object.values(systemStatus).filter(s => s.status === 'success').length;
            const warnings = Object.values(systemStatus).filter(s => s.status === 'warning').length;
            const errors = Object.values(systemStatus).filter(s => s.status === 'error').length;
            
            content.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
                    <div><strong>Total Checks:</strong> ${total}</div>
                    <div style="color: #38a169;"><strong>✅ Working:</strong> ${success}</div>
                    <div style="color: #ed8936;"><strong>⚠️ Warnings:</strong> ${warnings}</div>
                    <div style="color: #e53e3e;"><strong>❌ Errors:</strong> ${errors}</div>
                </div>
                <div style="margin-top: 15px;">
                    <strong>Overall Status:</strong> 
                    ${errors === 0 ? '🎉 System is fully operational!' : 
                      warnings > 0 ? '⚠️ System has some issues but is mostly functional' : 
                      '❌ System has critical issues that need attention'}
                </div>
            `;
            
            summary.style.display = 'block';
        }
        
        async function runSystemCheck() {
            const checkBtn = document.getElementById('checkBtn');
            const testBtn = document.getElementById('testFeaturesBtn');
            
            checkBtn.disabled = true;
            checkBtn.textContent = '🔄 Checking...';
            systemStatus = {};
            
            try {
                log('🚀 Starting comprehensive system check...');
                
                // Test 1: Database Connection
                log('🔌 Testing database connection...');
                try {
                    const { data, error } = await supabase.auth.getSession();
                    systemStatus.connection = {
                        title: 'Database Connection',
                        status: 'success',
                        details: 'Successfully connected to Supabase database',
                        icon: '🔌'
                    };
                    log('✅ Database connection successful');
                } catch (err) {
                    systemStatus.connection = {
                        title: 'Database Connection',
                        status: 'error',
                        details: `Connection failed: ${err.message}`,
                        icon: '🔌'
                    };
                    log('❌ Database connection failed');
                }
                
                // Test 2: Profiles Table
                log('👥 Testing profiles table...');
                try {
                    const { data, error } = await supabase
                        .from('profiles')
                        .select('id, full_name, role')
                        .limit(5);
                    
                    if (error) {
                        systemStatus.profiles = {
                            title: 'Profiles Table',
                            status: 'warning',
                            details: `Limited access: ${error.message}`,
                            icon: '👥'
                        };
                        log('⚠️ Profiles table has limited access');
                    } else {
                        systemStatus.profiles = {
                            title: 'Profiles Table',
                            status: 'success',
                            details: `Accessible with ${data?.length || 0} records found`,
                            icon: '👥'
                        };
                        log(`✅ Profiles table accessible (${data?.length || 0} records)`);
                    }
                } catch (err) {
                    systemStatus.profiles = {
                        title: 'Profiles Table',
                        status: 'error',
                        details: `Error: ${err.message}`,
                        icon: '👥'
                    };
                    log('❌ Profiles table error');
                }
                
                // Test 3: Projects Table
                log('📋 Testing projects table...');
                try {
                    const { data, error } = await supabase
                        .from('projects')
                        .select('id, name, status')
                        .limit(5);
                    
                    if (error) {
                        systemStatus.projects = {
                            title: 'Projects Table',
                            status: 'warning',
                            details: `Limited access: ${error.message}`,
                            icon: '📋'
                        };
                        log('⚠️ Projects table has limited access');
                    } else {
                        systemStatus.projects = {
                            title: 'Projects Table',
                            status: 'success',
                            details: `Accessible with ${data?.length || 0} records found`,
                            icon: '📋'
                        };
                        log(`✅ Projects table accessible (${data?.length || 0} records)`);
                    }
                } catch (err) {
                    systemStatus.projects = {
                        title: 'Projects Table',
                        status: 'error',
                        details: `Error: ${err.message}`,
                        icon: '📋'
                    };
                    log('❌ Projects table error');
                }
                
                // Test 4: Memos Table
                log('📝 Testing memos table...');
                try {
                    const { data, error } = await supabase
                        .from('memos')
                        .select('id, title, memo_type')
                        .limit(5);
                    
                    if (error) {
                        systemStatus.memos = {
                            title: 'Memos Table',
                            status: 'warning',
                            details: `Limited access: ${error.message}`,
                            icon: '📝'
                        };
                        log('⚠️ Memos table has limited access');
                    } else {
                        systemStatus.memos = {
                            title: 'Memos Table',
                            status: 'success',
                            details: `Accessible with ${data?.length || 0} records found`,
                            icon: '📝'
                        };
                        log(`✅ Memos table accessible (${data?.length || 0} records)`);
                    }
                } catch (err) {
                    systemStatus.memos = {
                        title: 'Memos Table',
                        status: 'error',
                        details: `Error: ${err.message}`,
                        icon: '📝'
                    };
                    log('❌ Memos table error');
                }
                
                // Test 5: Reports Table
                log('📊 Testing reports table...');
                try {
                    const { data, error } = await supabase
                        .from('reports')
                        .select('id, title, report_type')
                        .limit(5);
                    
                    if (error) {
                        systemStatus.reports = {
                            title: 'Reports Table',
                            status: 'warning',
                            details: `Limited access: ${error.message}`,
                            icon: '📊'
                        };
                        log('⚠️ Reports table has limited access');
                    } else {
                        systemStatus.reports = {
                            title: 'Reports Table',
                            status: 'success',
                            details: `Accessible with ${data?.length || 0} records found`,
                            icon: '📊'
                        };
                        log(`✅ Reports table accessible (${data?.length || 0} records)`);
                    }
                } catch (err) {
                    systemStatus.reports = {
                        title: 'Reports Table',
                        status: 'error',
                        details: `Error: ${err.message}`,
                        icon: '📊'
                    };
                    log('❌ Reports table error');
                }
                
                // Test 6: Authentication
                log('🔐 Testing authentication system...');
                try {
                    const { data: { user }, error } = await supabase.auth.getUser();
                    
                    if (error) {
                        systemStatus.auth = {
                            title: 'Authentication',
                            status: 'warning',
                            details: 'Not authenticated (this is normal for anonymous access)',
                            icon: '🔐'
                        };
                        log('⚠️ Not authenticated (normal for anonymous access)');
                    } else if (user) {
                        systemStatus.auth = {
                            title: 'Authentication',
                            status: 'success',
                            details: `Authenticated as ${user.email}`,
                            icon: '🔐'
                        };
                        log(`✅ Authenticated as ${user.email}`);
                    } else {
                        systemStatus.auth = {
                            title: 'Authentication',
                            status: 'warning',
                            details: 'Anonymous access (no user logged in)',
                            icon: '🔐'
                        };
                        log('⚠️ Anonymous access');
                    }
                } catch (err) {
                    systemStatus.auth = {
                        title: 'Authentication',
                        status: 'error',
                        details: `Error: ${err.message}`,
                        icon: '🔐'
                    };
                    log('❌ Authentication error');
                }
                
                updateStatusGrid();
                updateSummary();
                
                log('🎉 System check completed!');
                testBtn.disabled = false;
                
            } catch (error) {
                log('❌ System check failed: ' + error.message);
            } finally {
                checkBtn.disabled = false;
                checkBtn.textContent = '🚀 Run System Check';
            }
        }
        
        async function testCoreFeatures() {
            const testBtn = document.getElementById('testFeaturesBtn');
            testBtn.disabled = true;
            testBtn.textContent = '🔄 Testing...';
            
            try {
                log('🧪 Testing core features...');
                
                // Test basic CRUD operations
                log('📝 Testing basic operations...');
                
                // Test if we can perform basic queries
                const tables = ['profiles', 'projects', 'memos', 'reports', 'departments'];
                let workingTables = 0;
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('*')
                            .limit(1);
                        
                        if (!error) {
                            workingTables++;
                            log(`✅ ${table} table is functional`);
                        } else {
                            log(`⚠️ ${table} table has restrictions: ${error.message}`);
                        }
                    } catch (err) {
                        log(`❌ ${table} table error: ${err.message}`);
                    }
                }
                
                log(`📊 Summary: ${workingTables}/${tables.length} tables are accessible`);
                
                if (workingTables >= 3) {
                    log('🎉 Core features are functional! The system is ready to use.');
                } else if (workingTables >= 1) {
                    log('⚠️ Some features are working, but authentication may be needed for full access.');
                } else {
                    log('❌ Most features need authentication or database setup.');
                }
                
            } catch (error) {
                log('❌ Feature test failed: ' + error.message);
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 Test Core Features';
            }
        }
        
        // Make functions global
        window.runSystemCheck = runSystemCheck;
        window.testCoreFeatures = testCoreFeatures;
        
        // Auto-run system check on load
        setTimeout(runSystemCheck, 1000);
    </script>
</body>
</html>
