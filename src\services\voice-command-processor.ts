import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { AIVoiceAgentService } from './ai-voice-agent-service';
import { SystemLogsService } from './system-logs-service';

/**
 * Voice Command Processing Engine
 * Natural language processing for voice commands and intent recognition
 * Handles complex command parsing and execution
 */

export interface CommandIntent {
  intent: string;
  confidence: number;
  entities: Array<{
    type: string;
    value: string;
    confidence: number;
  }>;
  parameters: Record<string, any>;
  context: Record<string, any>;
}

export interface CommandResult {
  success: boolean;
  message: string;
  data?: any;
  actions?: Array<{
    type: string;
    target: string;
    data?: any;
  }>;
  nextSteps?: string[];
  error?: string;
}

export interface VoiceCommandTemplate {
  id: string;
  name: string;
  patterns: string[];
  intent: string;
  parameters: string[];
  handler: string;
  examples: string[];
}

export class VoiceCommandProcessor {
  private static templates: Map<string, VoiceCommandTemplate> = new Map();
  private static intentHandlers: Map<string, Function> = new Map();
  private static isInitialized: boolean = false;

  // Initialize the command processor
  static async initialize(): Promise<boolean> {
    try {
      console.log('🎯 Initializing Voice Command Processor...');

      // Load command templates from database
      await this.loadCommandTemplates();

      // Initialize intent handlers
      this.initializeIntentHandlers();

      // Load custom commands
      await this.loadCustomCommands();

      this.isInitialized = true;
      console.log('✅ Voice Command Processor initialized successfully');

      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Voice Command Processor:', error);
      return false;
    }
  }

  // Process voice command and return result
  static async processCommand(command: string, context?: any): Promise<CommandResult> {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      console.log('🎯 Processing voice command:', command);

      // Parse command and extract intent
      const intent = await this.parseCommand(command, context);

      // Execute command based on intent
      const result = await this.executeCommand(intent, context);

      // Log command execution
      await this.logCommandExecution(command, intent, result);

      return result;
    } catch (error) {
      console.error('Error processing voice command:', error);
      
      return {
        success: false,
        message: "I'm sorry, I couldn't process that command. Please try again.",
        error: error.toString(),
        nextSteps: [
          "Try rephrasing your command",
          "Ask for help with available commands",
          "Use simpler language"
        ]
      };
    }
  }

  // Parse command and extract intent
  private static async parseCommand(command: string, context?: any): Promise<CommandIntent> {
    const normalizedCommand = command.toLowerCase().trim();
    
    // Try to match against templates
    for (const [templateId, template] of this.templates) {
      for (const pattern of template.patterns) {
        const match = this.matchPattern(normalizedCommand, pattern);
        if (match.confidence > 0.7) {
          return {
            intent: template.intent,
            confidence: match.confidence,
            entities: match.entities,
            parameters: match.parameters,
            context: { ...context, templateId, templateName: template.name }
          };
        }
      }
    }

    // Fallback to basic intent recognition
    return this.basicIntentRecognition(normalizedCommand, context);
  }

  // Match command against pattern
  private static matchPattern(command: string, pattern: string): {
    confidence: number;
    entities: any[];
    parameters: Record<string, any>;
  } {
    // Simple pattern matching - in production, use more sophisticated NLP
    const patternWords = pattern.toLowerCase().split(' ');
    const commandWords = command.split(' ');
    
    let matchCount = 0;
    const entities: any[] = [];
    const parameters: Record<string, any> = {};

    for (const patternWord of patternWords) {
      if (patternWord.startsWith('{') && patternWord.endsWith('}')) {
        // Parameter placeholder
        const paramName = patternWord.slice(1, -1);
        const matchingWord = commandWords.find(word => 
          !patternWords.includes(word.toLowerCase())
        );
        
        if (matchingWord) {
          parameters[paramName] = matchingWord;
          entities.push({
            type: paramName,
            value: matchingWord,
            confidence: 0.8
          });
          matchCount++;
        }
      } else if (commandWords.some(word => word.toLowerCase().includes(patternWord))) {
        matchCount++;
      }
    }

    const confidence = matchCount / patternWords.length;
    
    return { confidence, entities, parameters };
  }

  // Basic intent recognition fallback
  private static basicIntentRecognition(command: string, context?: any): CommandIntent {
    // Navigation intents
    if (this.containsWords(command, ['go', 'navigate', 'open', 'show', 'take me'])) {
      return {
        intent: 'navigate',
        confidence: 0.8,
        entities: this.extractNavigationEntities(command),
        parameters: {},
        context: context || {}
      };
    }

    // Creation intents
    if (this.containsWords(command, ['create', 'new', 'add', 'make', 'start'])) {
      return {
        intent: 'create',
        confidence: 0.8,
        entities: this.extractCreationEntities(command),
        parameters: {},
        context: context || {}
      };
    }

    // Query intents
    if (this.containsWords(command, ['show', 'list', 'find', 'search', 'get', 'what'])) {
      return {
        intent: 'query',
        confidence: 0.7,
        entities: this.extractQueryEntities(command),
        parameters: {},
        context: context || {}
      };
    }

    // Help intents
    if (this.containsWords(command, ['help', 'how', 'explain', 'guide', 'tutorial'])) {
      return {
        intent: 'help',
        confidence: 0.9,
        entities: this.extractHelpEntities(command),
        parameters: {},
        context: context || {}
      };
    }

    // Default to conversation
    return {
      intent: 'conversation',
      confidence: 0.5,
      entities: [],
      parameters: {},
      context: context || {}
    };
  }

  // Execute command based on intent
  private static async executeCommand(intent: CommandIntent, context?: any): Promise<CommandResult> {
    const handler = this.intentHandlers.get(intent.intent);
    
    if (handler) {
      return await handler(intent, context);
    }

    // Fallback to voice agent
    const agentResponse = await AIVoiceAgentService.processVoiceInput(
      intent.entities.map(e => e.value).join(' '),
      { ...context, intent }
    );

    return {
      success: true,
      message: agentResponse.text,
      actions: agentResponse.actions,
      nextSteps: agentResponse.suggestions
    };
  }

  // Initialize intent handlers
  private static initializeIntentHandlers(): void {
    // Navigation handler
    this.intentHandlers.set('navigate', async (intent: CommandIntent, context?: any) => {
      const target = intent.entities.find(e => e.type === 'target' || e.type === 'page');
      
      if (target) {
        const route = this.getRouteForTarget(target.value);
        if (route) {
          return {
            success: true,
            message: `Navigating to ${target.value}`,
            actions: [{
              type: 'navigate',
              target: route,
              data: { source: 'voice_command' }
            }],
            nextSteps: [`You're now on the ${target.value} page`]
          };
        }
      }

      return {
        success: false,
        message: "I couldn't find that page. Where would you like to go?",
        nextSteps: [
          "Try: 'Go to dashboard'",
          "Try: 'Open projects'",
          "Try: 'Show my tasks'"
        ]
      };
    });

    // Creation handler
    this.intentHandlers.set('create', async (intent: CommandIntent, context?: any) => {
      const entityType = intent.entities.find(e => 
        ['project', 'task', 'memo', 'report'].includes(e.value.toLowerCase())
      );

      if (entityType) {
        const type = entityType.value.toLowerCase();
        const route = `/create/${type}`;
        
        return {
          success: true,
          message: `I'll help you create a new ${type}`,
          actions: [{
            type: 'navigate',
            target: route,
            data: { source: 'voice_command', type }
          }],
          nextSteps: [`Fill in the ${type} details`, "I'll guide you through the process"]
        };
      }

      return {
        success: false,
        message: "What would you like to create?",
        nextSteps: [
          "Try: 'Create a project'",
          "Try: 'Add a task'",
          "Try: 'New memo'"
        ]
      };
    });

    // Query handler
    this.intentHandlers.set('query', async (intent: CommandIntent, context?: any) => {
      const queryType = intent.entities.find(e => 
        ['tasks', 'projects', 'team', 'reports'].includes(e.value.toLowerCase())
      );

      if (queryType) {
        const type = queryType.value.toLowerCase();
        
        return {
          success: true,
          message: `Showing your ${type}`,
          actions: [{
            type: 'navigate',
            target: `/${type}`,
            data: { source: 'voice_command', filter: 'user' }
          }],
          nextSteps: [`View ${type} details`, `Filter or search ${type}`]
        };
      }

      return {
        success: false,
        message: "What information are you looking for?",
        nextSteps: [
          "Try: 'Show my tasks'",
          "Try: 'List projects'",
          "Try: 'Find team members'"
        ]
      };
    });

    // Help handler
    this.intentHandlers.set('help', async (intent: CommandIntent, context?: any) => {
      const topic = intent.entities.find(e => e.value);
      
      if (topic) {
        return {
          success: true,
          message: `I'll help you with ${topic.value}`,
          actions: [{
            type: 'help',
            target: topic.value,
            data: { source: 'voice_command' }
          }],
          nextSteps: ["Follow the guided tutorial", "Ask specific questions"]
        };
      }

      return {
        success: true,
        message: "I'm here to help! I can assist with navigation, creating projects and tasks, managing your team, and much more.",
        nextSteps: [
          "Ask: 'How do I create a project?'",
          "Ask: 'Show me around'",
          "Ask: 'What can you do?'"
        ]
      };
    });
  }

  // Helper methods
  private static containsWords(text: string, words: string[]): boolean {
    return words.some(word => text.toLowerCase().includes(word));
  }

  private static extractNavigationEntities(command: string): any[] {
    const targets = ['dashboard', 'projects', 'tasks', 'team', 'reports', 'memos', 'profile', 'settings'];
    const found = targets.filter(target => command.toLowerCase().includes(target));
    
    return found.map(target => ({
      type: 'target',
      value: target,
      confidence: 0.9
    }));
  }

  private static extractCreationEntities(command: string): any[] {
    const types = ['project', 'task', 'memo', 'report', 'team member'];
    const found = types.filter(type => command.toLowerCase().includes(type));
    
    return found.map(type => ({
      type: 'entity_type',
      value: type,
      confidence: 0.9
    }));
  }

  private static extractQueryEntities(command: string): any[] {
    const queryTypes = ['tasks', 'projects', 'team', 'reports', 'memos', 'activities'];
    const found = queryTypes.filter(type => command.toLowerCase().includes(type));
    
    return found.map(type => ({
      type: 'query_type',
      value: type,
      confidence: 0.8
    }));
  }

  private static extractHelpEntities(command: string): any[] {
    const topics = ['project', 'task', 'team', 'navigation', 'time tracking', 'reports'];
    const found = topics.filter(topic => command.toLowerCase().includes(topic));
    
    return found.map(topic => ({
      type: 'help_topic',
      value: topic,
      confidence: 0.8
    }));
  }

  private static getRouteForTarget(target: string): string | null {
    const routes: Record<string, string> = {
      'dashboard': '/',
      'projects': '/projects',
      'tasks': '/tasks',
      'team': '/team',
      'reports': '/reports',
      'memos': '/memos',
      'profile': '/profile',
      'settings': '/settings',
      'analytics': '/analytics'
    };

    return routes[target.toLowerCase()] || null;
  }

  // Load command templates from database
  private static async loadCommandTemplates(): Promise<void> {
    try {
      const { data: templates } = await supabase
        .from('voice_command_templates')
        .select('*')
        .eq('is_active', true);

      if (templates) {
        templates.forEach(template => {
          this.templates.set(template.id, {
            id: template.id,
            name: template.name,
            patterns: template.command_patterns,
            intent: template.intent,
            parameters: template.required_parameters || [],
            handler: template.action_mappings?.handler || 'default',
            examples: template.example_phrases || []
          });
        });

        console.log(`📋 Loaded ${templates.length} command templates`);
      }
    } catch (error) {
      console.warn('Could not load command templates:', error);
    }
  }

  // Load custom commands for user
  private static async loadCustomCommands(): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      if (!currentUser) return;

      const { data: preferences } = await supabase
        .from('voice_user_preferences')
        .select('custom_commands')
        .eq('user_id', currentUser.user_id)
        .single();

      if (preferences?.custom_commands) {
        // Process custom commands
        console.log('🎛️ Custom commands loaded');
      }
    } catch (error) {
      console.warn('Could not load custom commands:', error);
    }
  }

  // Log command execution
  private static async logCommandExecution(
    command: string,
    intent: CommandIntent,
    result: CommandResult
  ): Promise<void> {
    try {
      await SystemLogsService.info(
        'voice',
        'Voice command executed',
        `Command: "${command}" | Intent: ${intent.intent} | Success: ${result.success}`,
        {
          command,
          intent: intent.intent,
          confidence: intent.confidence,
          success: result.success,
          entities: intent.entities
        }
      );
    } catch (error) {
      console.error('Error logging command execution:', error);
    }
  }

  // Get available commands
  static getAvailableCommands(): VoiceCommandTemplate[] {
    return Array.from(this.templates.values());
  }

  // Test command processing
  static async testCommand(command: string): Promise<{
    intent: CommandIntent;
    result: CommandResult;
    processingTime: number;
  }> {
    const startTime = Date.now();
    
    const intent = await this.parseCommand(command);
    const result = await this.executeCommand(intent);
    
    return {
      intent,
      result,
      processingTime: Date.now() - startTime
    };
  }
}

export default VoiceCommandProcessor;
