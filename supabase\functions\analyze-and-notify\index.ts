import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { withCors, createCorsResponse, corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const openAIKey = Deno.env.get('OPENAI_API_KEY') || Deno.env.get('VITE_OPENAI_API_KEY');
const resendApiKey = Deno.env.get('RESEND_API_KEY');

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function analyzeContent(content: string, type: string) {
  if (!openAIKey || openAIKey === '' || openAIKey === 'your_openai_api_key_here') {
    // Return mock analysis
    return {
      result_data: `Analysis completed for ${type}. Content analyzed: "${content.substring(0, 100)}..."
      
Key Findings:
• Content length: ${content.length} characters
• Type: ${type}
• Analysis timestamp: ${new Date().toISOString()}
• Status: Processed successfully

Recommendations:
• Review the analyzed content for accuracy
• Consider implementing suggested improvements
• Share findings with relevant team members
• Monitor performance metrics

This analysis has been processed and relevant stakeholders have been notified.`,
      confidence: 0.85,
      summary: `Analyzed ${type} content with ${content.length} characters`,
      recommendations: [
        'Review content for accuracy',
        'Implement suggested improvements',
        'Share with team members'
      ]
    };
  }

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openAIKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: `You are an AI analyst for CTNL AI WORK-BOARD. Analyze the provided content and provide insights, recommendations, and actionable findings. Focus on business value and practical applications.`
          },
          {
            role: 'user',
            content: `Analyze this ${type} content: ${content}`
          }
        ],
        max_tokens: 800,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const result = await response.json();
    const analysis = result.choices[0]?.message?.content || 'Analysis could not be completed';

    return {
      result_data: analysis,
      confidence: 0.9,
      summary: `AI analysis completed for ${type}`,
      recommendations: [
        'Review AI analysis results',
        'Implement recommended actions',
        'Monitor outcomes'
      ]
    };
  } catch (error) {
    console.error('OpenAI analysis error:', error);
    throw new Error(`AI analysis failed: ${error.message}`);
  }
}

async function notifyStakeholders(analysisResult: any, content: string) {
  if (!resendApiKey) {
    console.log('Email notifications skipped - Resend API key not configured');
    return { success: true, message: 'Analysis completed (email notifications disabled)' };
  }

  try {
    // Get admin and manager users
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('email, full_name, role')
      .in('role', ['admin', 'manager'])
      .eq('status', 'active');

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return { success: false, error: 'Failed to fetch notification recipients' };
    }

    if (!users || users.length === 0) {
      return { success: true, message: 'No admin/manager users found for notification' };
    }

    const recipients = users.map(user => user.email).filter(Boolean);
    
    if (recipients.length === 0) {
      return { success: true, message: 'No valid email addresses found' };
    }

    // Send notification email
    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'CTNL AI WORK-BOARD <<EMAIL>>',
        to: recipients.slice(0, 1), // Send to first recipient only in testing mode
        subject: '🤖 New AI Analysis Completed - CTNL AI WORK-BOARD',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%); color: white; padding: 20px; text-align: center;">
              <h1 style="margin: 0; color: #dc2626;">🤖 AI Analysis Complete</h1>
              <p style="margin: 10px 0 0 0; color: #ccc;">CTNL AI WORK-BOARD</p>
            </div>
            
            <div style="padding: 30px; background: white;">
              <h2 style="color: #1a1a1a;">New Analysis Results Available</h2>
              
              <p>A new AI analysis has been completed in the CTNL AI WORK-BOARD system.</p>
              
              <div style="background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin: 20px 0;">
                <h3 style="color: #2d3748; margin-top: 0;">Analysis Summary</h3>
                <p><strong>Content Preview:</strong> ${content.substring(0, 200)}${content.length > 200 ? '...' : ''}</p>
                <p><strong>Analysis Time:</strong> ${new Date().toLocaleString()}</p>
                <p><strong>Confidence:</strong> ${analysisResult.confidence * 100}%</p>
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="https://ai.ctnigeria.com/dashboard" 
                   style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600;">
                  View Full Analysis
                </a>
              </div>
              
              <p style="color: #666; font-size: 14px;">
                This analysis was generated by the AI system and is available for review in your dashboard.
              </p>
            </div>
            
            <div style="background: #2d3748; color: #a0aec0; padding: 15px; text-align: center; font-size: 12px;">
              © 2024 CTNL AI WORK-BOARD. All rights reserved.
            </div>
          </div>
        `,
      }),
    });

    if (!emailResponse.ok) {
      const error = await emailResponse.text();
      throw new Error(`Email sending failed: ${emailResponse.status} - ${error}`);
    }

    const emailResult = await emailResponse.json();
    return { success: true, data: emailResult };
  } catch (error) {
    console.error('Notification error:', error);
    return { success: false, error: error.message };
  }
}

const handler = async (req: Request): Promise<Response> => {
  try {
    const body = await req.json();

    // Handle health check requests
    if (body.healthCheck) {
      const openAIConfigured = openAIKey && openAIKey !== '' && openAIKey !== 'your_openai_api_key_here';
      const resendConfigured = resendApiKey && resendApiKey !== '';

      return createCorsResponse({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: {
          openAI: openAIConfigured ? 'configured' : 'missing',
          resend: resendConfigured ? 'configured' : 'missing',
          supabase: 'configured'
        }
      });
    }

    const { content, type = 'user_query', userId } = body;

    if (!content) {
      return createCorsResponse(
        { error: 'Missing required field: content' },
        400
      );
    }

    // Perform analysis
    const analysisResult = await analyzeContent(content, type);

    // Log the analysis
    if (userId) {
      try {
        await supabase
          .from('activity_logs')
          .insert({
            user_id: userId,
            action: 'ai_analysis_request',
            entity_type: 'ai_analysis',
            entity_id: `analysis_${Date.now()}`,
            metadata: {
              content: content.substring(0, 500),
              type,
              confidence: analysisResult.confidence
            }
          });
      } catch (logError) {
        console.error('Failed to log analysis:', logError);
      }
    }

    // Send notifications to stakeholders
    const notificationResult = await notifyStakeholders(analysisResult, content);

    return createCorsResponse({
      success: true,
      result: analysisResult,
      notification: notificationResult,
      message: 'Analysis completed and stakeholders notified'
    });

  } catch (error) {
    console.error('Analyze and notify error:', error);
    
    return createCorsResponse(
      { 
        error: 'Analysis and notification failed', 
        details: error.message,
        success: false
      },
      500
    );
  }
};

Deno.serve(withCors(handler));
