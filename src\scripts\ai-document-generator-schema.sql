-- AI Document Generator Database Schema
-- Comprehensive schema for AI-powered document generation with Excel, charts, and templates

-- Create document_templates table for reusable templates
CREATE TABLE IF NOT EXISTS public.document_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_type TEXT NOT NULL CHECK (template_type IN ('excel', 'pdf', 'word', 'powerpoint', 'report', 'dashboard', 'invoice', 'memo')),
    category TEXT DEFAULT 'general' CHECK (category IN ('general', 'financial', 'hr', 'project', 'sales', 'marketing', 'operations')),
    template_config JSONB NOT NULL DEFAULT '{}', -- Template structure and configuration
    ai_prompts JSONB DEFAULT '{}', -- AI prompts for content generation
    chart_configs JSONB DEFAULT '[]', -- Chart configurations for Excel/dashboard templates
    table_configs JSON<PERSON> DEFAULT '[]', -- Table configurations and data mappings
    styling_config JSONB DEFAULT '{}', -- Styling, colors, fonts, layouts
    data_sources JSONB DEFAULT '[]', -- Data source configurations
    variables JSONB DEFAULT '[]', -- Template variables and placeholders
    is_public BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    updated_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_generations table for tracking generated documents
CREATE TABLE IF NOT EXISTS public.document_generations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    template_id UUID REFERENCES public.document_templates(id) ON DELETE SET NULL,
    document_name TEXT NOT NULL,
    document_type TEXT NOT NULL,
    generation_type TEXT DEFAULT 'manual' CHECK (generation_type IN ('manual', 'scheduled', 'automated', 'ai_enhanced')),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
    file_path TEXT,
    file_size BIGINT,
    generation_config JSONB DEFAULT '{}', -- Configuration used for this generation
    ai_enhancements JSONB DEFAULT '{}', -- AI enhancements applied
    data_snapshot JSONB DEFAULT '{}', -- Data used for generation
    processing_time INTEGER, -- Time taken in milliseconds
    error_details TEXT,
    download_count INTEGER DEFAULT 0,
    generated_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE
);

-- Create ai_document_configs table for AI configuration
CREATE TABLE IF NOT EXISTS public.ai_document_configs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    config_name TEXT NOT NULL,
    ai_provider TEXT DEFAULT 'openai' CHECK (ai_provider IN ('openai', 'anthropic', 'google', 'azure')),
    model_name TEXT DEFAULT 'gpt-4',
    system_prompts JSONB DEFAULT '{}', -- System prompts for different document types
    enhancement_rules JSONB DEFAULT '[]', -- Rules for AI enhancements
    data_analysis_prompts JSONB DEFAULT '{}', -- Prompts for data analysis
    chart_generation_prompts JSONB DEFAULT '{}', -- Prompts for chart recommendations
    content_optimization JSONB DEFAULT '{}', -- Content optimization settings
    quality_checks JSONB DEFAULT '[]', -- Quality check configurations
    is_default BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_data_sources table for data source management
CREATE TABLE IF NOT EXISTS public.document_data_sources (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    source_name TEXT NOT NULL,
    source_type TEXT NOT NULL CHECK (source_type IN ('database_table', 'api_endpoint', 'csv_file', 'json_data', 'manual_input', 'real_time_query')),
    connection_config JSONB DEFAULT '{}', -- Connection details and credentials
    query_config JSONB DEFAULT '{}', -- SQL queries or API parameters
    data_mapping JSONB DEFAULT '{}', -- Field mappings and transformations
    refresh_schedule TEXT, -- Cron expression for data refresh
    last_refresh TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chart_templates table for reusable chart configurations
CREATE TABLE IF NOT EXISTS public.chart_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chart_name TEXT NOT NULL,
    chart_type TEXT NOT NULL CHECK (chart_type IN ('bar', 'line', 'pie', 'scatter', 'area', 'column', 'doughnut', 'radar', 'bubble', 'heatmap')),
    chart_config JSONB NOT NULL DEFAULT '{}', -- Chart.js or Excel chart configuration
    data_requirements JSONB DEFAULT '{}', -- Required data structure
    styling_options JSONB DEFAULT '{}', -- Colors, fonts, sizes
    ai_recommendations JSONB DEFAULT '{}', -- AI-suggested improvements
    usage_count INTEGER DEFAULT 0,
    is_public BOOLEAN DEFAULT false,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create document_analytics table for tracking document performance
CREATE TABLE IF NOT EXISTS public.document_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    document_generation_id UUID REFERENCES public.document_generations(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL CHECK (event_type IN ('generated', 'downloaded', 'viewed', 'shared', 'edited', 'deleted')),
    user_id UUID REFERENCES public.profiles(id),
    event_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    event_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ai_enhancement_logs table for AI operation tracking
CREATE TABLE IF NOT EXISTS public.ai_enhancement_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    document_generation_id UUID REFERENCES public.document_generations(id) ON DELETE CASCADE,
    enhancement_type TEXT NOT NULL CHECK (enhancement_type IN ('content_generation', 'data_analysis', 'chart_recommendation', 'layout_optimization', 'quality_check')),
    ai_provider TEXT NOT NULL,
    model_used TEXT NOT NULL,
    prompt_used TEXT,
    ai_response JSONB,
    processing_time INTEGER, -- Time in milliseconds
    tokens_used INTEGER,
    cost_estimate DECIMAL(10,4),
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.document_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_document_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_data_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chart_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.document_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_enhancement_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for document_templates
CREATE POLICY "Users can view public templates and own templates" ON public.document_templates FOR SELECT USING (
    is_public = true OR auth.uid() = created_by OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Users can create templates" ON public.document_templates FOR INSERT WITH CHECK (
    auth.uid() = created_by
);

CREATE POLICY "Users can update own templates" ON public.document_templates FOR UPDATE USING (
    auth.uid() = created_by OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin'))
);

-- RLS Policies for document_generations
CREATE POLICY "Users can view own generations" ON public.document_generations FOR SELECT USING (
    auth.uid() = generated_by OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Users can create generations" ON public.document_generations FOR INSERT WITH CHECK (
    auth.uid() = generated_by
);

-- RLS Policies for ai_document_configs
CREATE POLICY "Admins can manage AI configs" ON public.ai_document_configs FOR ALL USING (
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin'))
);

CREATE POLICY "Users can view AI configs" ON public.ai_document_configs FOR SELECT USING (true);

-- RLS Policies for document_data_sources
CREATE POLICY "Users can manage own data sources" ON public.document_data_sources FOR ALL USING (
    auth.uid() = created_by OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

-- RLS Policies for chart_templates
CREATE POLICY "Users can view public charts and own charts" ON public.chart_templates FOR SELECT USING (
    is_public = true OR auth.uid() = created_by OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "Users can create chart templates" ON public.chart_templates FOR INSERT WITH CHECK (
    auth.uid() = created_by
);

-- RLS Policies for document_analytics
CREATE POLICY "Users can view analytics for own documents" ON public.document_analytics FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.document_generations dg 
        WHERE dg.id = document_generation_id AND dg.generated_by = auth.uid()
    ) OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin', 'manager'))
);

CREATE POLICY "System can create analytics" ON public.document_analytics FOR INSERT WITH CHECK (true);

-- RLS Policies for ai_enhancement_logs
CREATE POLICY "Users can view AI logs for own documents" ON public.ai_enhancement_logs FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.document_generations dg 
        WHERE dg.id = document_generation_id AND dg.generated_by = auth.uid()
    ) OR
    EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid() AND role IN ('admin'))
);

CREATE POLICY "System can create AI logs" ON public.ai_enhancement_logs FOR INSERT WITH CHECK (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_templates_type ON public.document_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_document_templates_category ON public.document_templates(category);
CREATE INDEX IF NOT EXISTS idx_document_templates_created_by ON public.document_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_document_generations_template_id ON public.document_generations(template_id);
CREATE INDEX IF NOT EXISTS idx_document_generations_generated_by ON public.document_generations(generated_by);
CREATE INDEX IF NOT EXISTS idx_document_generations_status ON public.document_generations(status);
CREATE INDEX IF NOT EXISTS idx_document_generations_generated_at ON public.document_generations(generated_at);
CREATE INDEX IF NOT EXISTS idx_ai_document_configs_provider ON public.ai_document_configs(ai_provider);
CREATE INDEX IF NOT EXISTS idx_document_data_sources_type ON public.document_data_sources(source_type);
CREATE INDEX IF NOT EXISTS idx_chart_templates_type ON public.chart_templates(chart_type);
CREATE INDEX IF NOT EXISTS idx_document_analytics_generation_id ON public.document_analytics(document_generation_id);
CREATE INDEX IF NOT EXISTS idx_document_analytics_event_type ON public.document_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_ai_enhancement_logs_generation_id ON public.ai_enhancement_logs(document_generation_id);
CREATE INDEX IF NOT EXISTS idx_ai_enhancement_logs_enhancement_type ON public.ai_enhancement_logs(enhancement_type);
