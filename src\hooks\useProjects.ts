
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useEffect, useState } from 'react';

interface Project {
  id: string;
  name: string;
  description: string | null;
  client_name: string | null;
  budget: number | null;
  end_date: string | null;
  start_date: string | null;
  status: string | null;
  created_at: string;
  updated_at: string;
  tasks?: Task[];
}

interface Task {
  id: string;
  project_id: string | null;
  assigned_to_id: string | null;
  title: string;
  description: string | null;
  status: string;
  created_at: string | null;
  updated_at: string | null;
  profiles?: {
    full_name: string;
    email: string;
    role: string;
  };
}

interface CreateProjectData {
  name: string;
  description?: string;
  client_name?: string;
  budget?: number;
  start_date?: string;
  end_date?: string;
  status?: string;
}

export const useProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const fetchProjects = async () => {
    setLoading(true);
    try {
      // Try complex query first
      let { data, error } = await supabase
        .from('projects')
        .select(`
          id,
          name,
          description,
          client_name,
          budget,
          start_date,
          end_date,
          status,
          manager_id,
          department_id,
          created_at,
          updated_at,
          manager:profiles!manager_id (
            id,
            full_name,
            email,
            role
          ),
          department:departments!department_id (
            id,
            name
          ),
          tasks (
            id,
            project_id,
            assigned_to_id,
            title,
            description,
            status,
            priority,
            due_date,
            created_at,
            updated_at,
            assignee:profiles!assigned_to_id (
              id,
              full_name,
              email,
              role
            )
          )
        `)
        .order('created_at', { ascending: false });

      // If complex query fails, try simple query
      if (error && (error.status === 400 || error.status === 406)) {
        console.log('Complex projects query failed, trying simple query:', error);
        const simpleResult = await supabase
          .from('projects')
          .select('id, name, description, status, manager_id, department_id, created_at, updated_at, start_date, end_date, budget')
          .order('created_at', { ascending: false });

        data = simpleResult.data;
        error = simpleResult.error;
      }

      if (error) {
        console.error('Projects fetch error:', error);
        // Try fallback query without foreign key relationships
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('projects')
          .select('*')
          .order('created_at', { ascending: false });

        if (fallbackError) throw fallbackError;

        setProjects(fallbackData || []);
        return;
      }
      
      // Transform data to match our interface
      const transformedProjects = (data || []).map(project => ({
        id: project.id,
        name: project.name,
        description: project.description,
        client_name: project.client_name,
        budget: project.budget,
        start_date: project.start_date,
        end_date: project.end_date,
        status: project.status,
        created_at: project.created_at,
        updated_at: project.updated_at,
        tasks: project.tasks || []
      }));
      
      setProjects(transformedProjects);
    } catch (error) {
      console.error('Error fetching projects:', error);
      toast({
        title: "Error",
        description: "Failed to fetch projects",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  const createProject = async (data: CreateProjectData) => {
    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Prepare project data with proper validation
      const projectData = {
        name: data.name?.trim(),
        description: data.description?.trim() || null,
        client_name: data.client_name?.trim() || null,
        budget: data.budget || null,
        start_date: data.start_date || null,
        end_date: data.end_date || null,
        status: data.status || 'planning',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Validate required fields
      if (!projectData.name) {
        throw new Error('Project name is required');
      }

      const { data: newProject, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project created successfully",
      });

      await fetchProjects();
      return { success: true, data: newProject };
    } catch (error: any) {
      console.error('Error creating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create project",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const assignMemberToProject = async (projectId: string, staffId: string) => {
    setLoading(true);
    try {
      // Create a task assignment instead of project assignment
      const { error } = await supabase
        .from('tasks')
        .insert([{
          project_id: projectId,
          assigned_to_id: staffId,
          title: 'Project Assignment',
          description: 'Assigned to project',
          status: 'assigned'
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Member assigned to project successfully",
      });

      await fetchProjects();
      return { success: true };
    } catch (error) {
      console.error('Error assigning member to project:', error);
      toast({
        title: "Error",
        description: "Failed to assign member to project",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const removeMemberFromProject = async (taskId: string) => {
    setLoading(true);
    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Member removed from project successfully",
      });

      await fetchProjects();
      return { success: true };
    } catch (error) {
      console.error('Error removing member from project:', error);
      toast({
        title: "Error",
        description: "Failed to remove member from project",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const updateProject = async (projectId: string, updates: Partial<CreateProjectData>) => {
    setLoading(true);
    try {
      const updateData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      // Clean up undefined values
      Object.keys(updateData).forEach(key => {
        if (updateData[key] === undefined) {
          delete updateData[key];
        }
      });

      const { data, error } = await supabase
        .from('projects')
        .update(updateData)
        .eq('id', projectId)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Success",
        description: "Project updated successfully",
      });

      await fetchProjects();
      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update project",
        variant: "destructive",
      });
      return { success: false, error };
    } finally {
      setLoading(false);
    }
  };

  const updateProjectStatus = async (projectId: string, status: string) => {
    return updateProject(projectId, { status });
  };

  return {
    projects,
    loading,
    createProject,
    updateProject,
    updateProjectStatus,
    assignMemberToProject,
    removeMemberFromProject,
    refetch: fetchProjects
  };
};
