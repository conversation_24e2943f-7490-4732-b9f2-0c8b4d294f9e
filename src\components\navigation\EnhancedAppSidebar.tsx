import { useAuth } from "@/components/auth/AuthProvider";
import { LogoutButton } from "@/components/auth/LogoutButton";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { QuickCacheClearButton } from "@/components/ui/QuickCacheClearButton";
import {
    Sidebar,
    SidebarContent,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar
} from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import {
    BarChart3,
    Battery,
    Bell,
    Brain,
    Building2,
    Calendar,
    Car,
    CheckSquare,
    ChevronDown, ChevronRight,
    Clipboard,
    Clock,
    Code,
    Construction,
    Database,
    DollarSign,
    FileCheck,
    FileImage,
    FileText,
    Key,
    LayoutDashboard,
    Menu,
    Network,
    Package,
    Settings,
    Shield,
    ShoppingCart,
    StickyNote,
    TrendingUp,
    User,
    <PERSON>,
    Wrench
} from "lucide-react";
import { useState } from "react";
import { NavLink, useLocation } from "react-router-dom";

interface MenuItem {
  title: string;
  href: string;
  icon: any;
  subItems?: MenuItem[];
  badge?: string;
}

export function EnhancedAppSidebar() {
  const { userProfile } = useAuth();
  const { state, toggleSidebar } = useSidebar();
  const location = useLocation();
  const [expandedGroups, setExpandedGroups] = useState<string[]>(["main", "operations"]);

  const toggleGroup = (groupName: string) => {
    setExpandedGroups(prev =>
      prev.includes(groupName)
        ? prev.filter(g => g !== groupName)
        : [...prev, groupName]
    );
  };

  const handleUserManual = () => {
    // Open user manual in new tab
    window.open('/user-manual.html', '_blank');
  };

  // Simplified and consolidated menu items based on role
  const getMenuItems = (): MenuItem[] => {
    const role = userProfile?.role || userProfile?.account_type;
    
    const commonItems = [
      { title: "AI Assistant", href: "/dashboard/ai", icon: Brain },
      { title: "Documents", href: "/dashboard/documents", icon: FileImage },
      { title: "Settings", href: "/dashboard/settings", icon: Settings },
    ];
    
    switch (role) {
      case 'admin':
        return [
          { title: "Dashboard", href: "/dashboard/admin", icon: LayoutDashboard },
          { title: "User Management", href: "/dashboard/admin/users", icon: Users },

          { title: "System Diagnostics", href: "/dashboard/admin/diagnostics", icon: BarChart3 },
          { title: "API Keys", href: "/dashboard/admin/api-keys", icon: Key },
          { title: "Database Management", href: "/dashboard/admin/database", icon: Database },
          { title: "Integrations", href: "/dashboard/admin/integrations", icon: Shield },
          { title: "AI Management", href: "/dashboard/admin/ai", icon: Brain },
          { title: "Settings", href: "/dashboard/admin/settings", icon: Settings },
          { title: "Departments", href: "/dashboard/admin/departments", icon: Building2 },
          { title: "Projects", href: "/dashboard/admin/projects", icon: Clipboard },
          { title: "Reports", href: "/dashboard/admin/reports", icon: BarChart3 },
          { title: "Activities", href: "/dashboard/admin/activities", icon: Calendar },
          { title: "Communication", href: "/dashboard/admin/communication", icon: StickyNote },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Fleet", href: "/dashboard/fleet", icon: Car },
          { title: "Battery", href: "/dashboard/battery", icon: Battery },
          { title: "Battery Setup", href: "/dashboard/admin/battery-setup", icon: Settings },
          { title: "Auth Debug", href: "/dashboard/admin/auth-debug", icon: Shield },
          { title: "Database Fix", href: "/dashboard/admin/database-fix", icon: Database },
          { title: "RPC Setup", href: "/dashboard/admin/rpc-setup", icon: Code },
          { title: "Assets", href: "/dashboard/assets", icon: Package },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Notification Management", href: "/dashboard/admin/notification-management", icon: Bell },
          ...commonItems,
        ];
      
      case 'manager':
        return [
          { title: "Dashboard", href: "/dashboard/manager", icon: LayoutDashboard },
          { title: "Projects", href: "/dashboard/manager/projects", icon: Clipboard },
          { title: "Team", href: "/dashboard/manager/team", icon: Users },
          { title: "Time Tracking", href: "/dashboard/manager/time", icon: Clock },
          { title: "Work Board", href: "/dashboard/manager/workboard", icon: Clipboard },
          { title: "Leave Requests", href: "/dashboard/manager/leave", icon: FileCheck },
          { title: "Sites", href: "/dashboard/manager/sites", icon: Network },
          { title: "Memos", href: "/dashboard/manager/memos", icon: StickyNote },
          { title: "Reports", href: "/dashboard/manager/reports", icon: BarChart3 },
          { title: "Invoices", href: "/dashboard/manager/invoices", icon: FileText },
          { title: "Meetings", href: "/dashboard/manager/meetings", icon: Calendar },
          { title: "Fleet", href: "/dashboard/fleet", icon: Car },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          ...commonItems,
        ];
      
      case 'accountant':
        return [
          { title: "Dashboard", href: "/dashboard/accountant", icon: LayoutDashboard },
          { title: "Invoices", href: "/dashboard/accountant/invoices", icon: FileText },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Assets", href: "/dashboard/assets", icon: Package },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          ...commonItems,
        ];

      case 'staff-admin':
        return [
          { title: "Dashboard", href: "/dashboard/staff-admin", icon: LayoutDashboard },
          { title: "Expenses", href: "/dashboard/staff-admin/expenses", icon: DollarSign },
          { title: "Fleet", href: "/dashboard/staff-admin/fleet", icon: Car },
          { title: "Assets", href: "/dashboard/staff-admin/assets", icon: Package },
          { title: "Financial", href: "/dashboard/financial", icon: DollarSign },
          { title: "Construction", href: "/dashboard/construction", icon: Construction },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Reports", href: "/dashboard/reports", icon: BarChart3 },
          ...commonItems,
        ];
      
      default: // staff
        return [
          { title: "Dashboard", href: "/dashboard/staff", icon: LayoutDashboard },
          { title: "Current Tasks", href: "/dashboard/staff/current-tasks", icon: Clock },
          { title: "My Tasks", href: "/dashboard/staff/my-tasks", icon: FileCheck },
          { title: "Project Progress", href: "/dashboard/staff/project-progress", icon: TrendingUp },
          { title: "Task Updates", href: "/dashboard/staff/task-updates", icon: CheckSquare },
          { title: "Reports", href: "/dashboard/staff/reports", icon: BarChart3 },
          { title: "Battery Reports", href: "/dashboard/staff/battery-reports", icon: Battery },
          { title: "TOOLZ", href: "/dashboard/toolz", icon: Wrench },
          { title: "Procurement", href: "/dashboard/procurement", icon: ShoppingCart },
          { title: "Memos", href: "/dashboard/staff/memos", icon: StickyNote },
          { title: "Meetings", href: "/dashboard/staff/meetings", icon: Calendar },
          { title: "Profile", href: "/dashboard/staff/profile", icon: User },
          { title: "Settings", href: "/dashboard/staff/settings", icon: Settings },
          { title: "AI Assistant", href: "/dashboard/ai", icon: Brain },
          { title: "Documents", href: "/dashboard/documents", icon: FileImage },
        ];
    }
  };

  const menuItems = getMenuItems();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar
      className={cn(
        "border-r bg-gradient-to-b from-background to-muted/20 border-border/50 neumorphism-sidebar",
        // Mobile responsiveness
        "w-64 md:w-72 lg:w-80", // Responsive width
        "transition-all duration-300 ease-in-out", // Smooth transitions
        // Mobile-specific styling
        "md:relative md:translate-x-0", // Desktop positioning
        "fixed md:static z-50 md:z-auto", // Mobile overlay
        "h-full md:h-auto" // Full height on mobile
      )}
      style={{
        background: 'linear-gradient(145deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05))',
        boxShadow: 'inset 8px 8px 16px rgba(0,0,0,0.1), inset -8px -8px 16px rgba(255,255,255,0.1), 0 8px 32px rgba(0,0,0,0.1)',
        borderRadius: '0 8px 8px 0'
      }}
    >
      <SidebarContent>
        {/* Enhanced User Profile Section */}
        <SidebarGroup>
          <div
            className="neumorphism-card flex items-center gap-3 p-4 m-2 bg-background/50 backdrop-blur-sm border border-border/20"
            style={{
              borderRadius: '8px',
              boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.1), inset -2px -2px 4px rgba(255,255,255,0.1), 0 2px 8px rgba(0,0,0,0.05)'
            }}
          >
            <Avatar className="h-10 w-10 ring-1 ring-border/30 transition-all duration-300 hover:ring-primary/40">
              <AvatarImage src={userProfile?.avatar_url} />
              <AvatarFallback className="bg-muted/50 text-foreground font-semibold">
                {userProfile?.full_name?.charAt(0) || userProfile?.email?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-semibold truncate text-foreground">
                  {userProfile?.full_name || 'User'}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge
                    variant="secondary"
                    className="text-xs bg-muted/30 text-muted-foreground border border-border/30"
                    style={{ borderRadius: '8px' }}
                  >
                    {userProfile?.role || userProfile?.account_type || 'Staff'}
                  </Badge>
                </div>
              </div>
            )}
            {/* Simplified Toggle Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSidebar}
              className="ml-auto h-8 w-8 p-0 rounded-lg border border-border/20 hover:bg-muted/20 transition-all duration-200"
              style={{ borderRadius: '8px' }}
              title={isCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
            >
              <Menu className="h-4 w-4" />
            </Button>
          </div>
        </SidebarGroup>

        {/* Simplified Main Navigation */}
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground uppercase tracking-wider px-4 py-2">
            <div className="flex items-center justify-between w-full">
              <span>Navigation</span>
              {!isCollapsed && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 rounded-md border border-border/20 hover:bg-muted/20 transition-all duration-200"
                  style={{ borderRadius: '8px' }}
                  onClick={() => toggleGroup("main")}
                >
                  {expandedGroups.includes("main") ?
                    <ChevronDown className="h-3 w-3" /> :
                    <ChevronRight className="h-3 w-3" />
                  }
                </Button>
              )}
            </div>
          </SidebarGroupLabel>
          
          {(isCollapsed || expandedGroups.includes("main")) && (
            <SidebarGroupContent>
              <div
                className="neumorphism-card bg-background/30 backdrop-blur-sm border border-border/20 p-2 m-2"
                style={{
                  borderRadius: '8px',
                  boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.1), inset -2px -2px 4px rgba(255,255,255,0.1), 0 2px 8px rgba(0,0,0,0.05)'
                }}
              >
                <SidebarMenu className="space-y-1">
                {menuItems.map((item) => (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          `flex items-center gap-3 text-sm font-medium group px-3 py-2.5 transition-all duration-200 border border-border/20 ${
                            isActive
                              ? 'bg-primary/10 text-primary border-primary/30 shadow-inner'
                              : 'bg-background/50 hover:bg-muted/30 hover:border-border/40'
                          }`
                        }
                        style={{ borderRadius: '8px' }}
                      >
                        <div className={`p-1.5 transition-all duration-200 ${
                          location.pathname === item.href
                            ? 'bg-primary/20 text-primary'
                            : 'bg-muted/20 group-hover:bg-muted/40'
                        }`} style={{ borderRadius: '8px' }}>
                          <item.icon className="h-4 w-4 shrink-0" />
                        </div>
                        {!isCollapsed && (
                          <div className="flex items-center justify-between w-full">
                            <span className="font-medium">{item.title}</span>
                            {item.badge && (
                              <Badge
                                variant="secondary"
                                className="ml-2 text-xs bg-muted/30 text-muted-foreground border border-border/30"
                                style={{ borderRadius: '8px' }}
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </div>
                        )}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
                </SidebarMenu>
              </div>
            </SidebarGroupContent>
          )}
        </SidebarGroup>

        {/* Redesigned Action Buttons Section */}
        <SidebarGroup className="mt-auto">
          <SidebarGroupContent>
            <div
              className="neumorphism-card p-3 m-2 bg-background/50 backdrop-blur-sm border border-border/20"
              style={{
                borderRadius: '8px',
                boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.1), inset -2px -2px 4px rgba(255,255,255,0.1), 0 2px 8px rgba(0,0,0,0.05)'
              }}
            >
              {isCollapsed ? (
                <div className="flex flex-col gap-2">
                  <QuickCacheClearButton
                    variant="outline"
                    size="sm"
                    showText={false}
                    className="w-full h-10 border border-border/30 hover:bg-muted/30 transition-all duration-200"
                    style={{ borderRadius: '8px' }}
                  />
                  <LogoutButton
                    variant="outline"
                    className="w-full h-10 border border-border/30 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30 transition-all duration-200"
                    style={{ borderRadius: '8px' }}
                    showText={false}
                  />
                </div>
              ) : (
                <div className="space-y-2">
                  <QuickCacheClearButton
                    variant="outline"
                    size="sm"
                    showText={true}
                    className="w-full h-10 border border-border/30 hover:bg-muted/30 transition-all duration-200 text-sm font-medium"
                    style={{ borderRadius: '8px' }}
                  />
                  <LogoutButton
                    variant="outline"
                    className="w-full h-10 border border-border/30 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30 transition-all duration-200 text-sm font-medium"
                    style={{ borderRadius: '8px' }}
                  />
                </div>
              )}
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
