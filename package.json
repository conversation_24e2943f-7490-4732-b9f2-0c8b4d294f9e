{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "packageManager": "npm@10.0.0", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:prod": "NODE_ENV=production vite build", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "preview": "vite preview", "browsers": "npx browserslist", "browsers:coverage": "npx browserslist --coverage", "clear-cache": "powershell -ExecutionPolicy Bypass -File scripts/clear-cache.ps1", "clear-cache-unix": "rm -rf node_modules/.cache && rm -rf .vite && rm -rf dist", "fresh-install": "npm run clear-cache && npm install", "fresh-build": "npm run clear-cache && npm install && npm run build:prod", "fix-blank-screen": "npm run clear-cache && npm run fresh-install && npm run dev", "deploy:build": "npm run fresh-build && npm run build:analyze", "size-check": "node -e \"require('./build.js').buildSteps.report()\"", "preview:prod": "vite preview --config vite.config.production.ts", "deploy:vercel": "npm run build:prod && vercel --prod", "deploy:netlify": "npm run build:prod && netlify deploy --prod --dir=dist"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^3.9.0", "@iconify/react": "^6.0.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.81.5", "@vercel/analytics": "^1.5.0", "@zoom/videosdk": "^2.2.0", "aos": "^2.3.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.3.0", "formik": "^2.4.6", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.462.0", "moment": "^2.30.1", "next-themes": "^0.3.0", "openai": "^4.104.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^4.8.69", "rc-slider": "^11.1.8", "react": "^18.3.1", "react-big-calendar": "^1.19.4", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-hook-form": "^7.53.0", "react-image-crop": "^11.0.10", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-speech-synthesis": "^0.1.0", "recharts": "^2.12.7", "sonner": "^1.7.4", "tailwind-merge": "^2.5.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "type-fest": "^4.41.0", "use-deep-compare-effect": "^1.8.1", "uuid": "^11.1.0", "vaul": "^0.9.3", "vite": "^5.4.19", "yup": "^1.6.1", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/jspdf": "^1.3.3", "@types/node": "^22.16.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.21", "browserslist": "^4.25.1", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.5.6", "standard": "^17.1.2", "supabase": "^2.31.4", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1"}, "browserslist": ["> 0.5%", "last 2 versions", "not dead", "not ie 11", "not op_mini all", "supports es6-module", "supports es6-module-dynamic-import", "supports async-functions", "not android 4.4.3-4.4.4", "not ios_saf < 12", "not chrome < 90", "not firefox < 88", "not safari < 14", "not edge < 90"], "engines": {"node": ">=20.16.0 || >=22.3.0", "npm": ">=10.0.0"}}