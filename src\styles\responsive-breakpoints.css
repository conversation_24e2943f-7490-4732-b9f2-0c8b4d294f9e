/**
 * Responsive Design System for CTNL AI Workboard
 * Comprehensive breakpoints and responsive utilities
 */

/* ============= BREAKPOINT DEFINITIONS ============= */
:root {
  /* Breakpoint values */
  --breakpoint-xs: 320px;   /* Extra small devices (phones) */
  --breakpoint-sm: 640px;   /* Small devices (large phones) */
  --breakpoint-md: 768px;   /* Medium devices (tablets) */
  --breakpoint-lg: 1024px;  /* Large devices (laptops) */
  --breakpoint-xl: 1280px;  /* Extra large devices (desktops) */
  --breakpoint-2xl: 1536px; /* 2X large devices (large desktops) */
  
  /* Container max widths */
  --container-xs: 100%;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* Responsive spacing scale */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */
  --spacing-3xl: 4rem;      /* 64px */
  
  /* Responsive font sizes */
  --text-xs: 0.75rem;       /* 12px */
  --text-sm: 0.875rem;      /* 14px */
  --text-base: 1rem;        /* 16px */
  --text-lg: 1.125rem;      /* 18px */
  --text-xl: 1.25rem;       /* 20px */
  --text-2xl: 1.5rem;       /* 24px */
  --text-3xl: 1.875rem;     /* 30px */
  --text-4xl: 2.25rem;      /* 36px */
  --text-5xl: 3rem;         /* 48px */
}

/* ============= RESPONSIVE CONTAINERS ============= */
.responsive-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-md);
  padding-right: var(--spacing-md);
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: var(--container-sm);
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: var(--container-md);
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: var(--container-lg);
    padding-left: var(--spacing-xl);
    padding-right: var(--spacing-xl);
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: var(--container-xl);
  }
}

@media (min-width: 1536px) {
  .responsive-container {
    max-width: var(--container-2xl);
  }
}

/* ============= RESPONSIVE GRID SYSTEM ============= */
.responsive-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .responsive-grid {
    gap: var(--spacing-lg);
  }
  
  .responsive-grid.cols-sm-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid.cols-sm-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 768px) {
  .responsive-grid.cols-md-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .responsive-grid.cols-md-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-grid.cols-md-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1024px) {
  .responsive-grid {
    gap: var(--spacing-xl);
  }
  
  .responsive-grid.cols-lg-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .responsive-grid.cols-lg-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .responsive-grid.cols-lg-5 {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* ============= RESPONSIVE TYPOGRAPHY ============= */
.responsive-heading {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

.responsive-heading.size-sm {
  font-size: var(--text-lg);
}

.responsive-heading.size-md {
  font-size: var(--text-xl);
}

.responsive-heading.size-lg {
  font-size: var(--text-2xl);
}

.responsive-heading.size-xl {
  font-size: var(--text-3xl);
}

@media (min-width: 640px) {
  .responsive-heading.size-sm {
    font-size: var(--text-xl);
  }
  
  .responsive-heading.size-md {
    font-size: var(--text-2xl);
  }
  
  .responsive-heading.size-lg {
    font-size: var(--text-3xl);
  }
  
  .responsive-heading.size-xl {
    font-size: var(--text-4xl);
  }
}

@media (min-width: 768px) {
  .responsive-heading.size-lg {
    font-size: var(--text-4xl);
  }
  
  .responsive-heading.size-xl {
    font-size: var(--text-5xl);
  }
}

/* ============= RESPONSIVE CARDS ============= */
.responsive-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  padding: var(--spacing-md);
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .responsive-card {
    padding: var(--spacing-lg);
    border-radius: 1rem;
  }
}

@media (min-width: 1024px) {
  .responsive-card {
    padding: var(--spacing-xl);
    border-radius: 1.5rem;
  }
}

.responsive-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* ============= RESPONSIVE BUTTONS ============= */
.responsive-button {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

@media (min-width: 640px) {
  .responsive-button {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--text-base);
    border-radius: 0.75rem;
    gap: var(--spacing-sm);
  }
}

@media (min-width: 1024px) {
  .responsive-button {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: 1rem;
  }
}

/* ============= RESPONSIVE NAVIGATION ============= */
.responsive-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
}

@media (min-width: 768px) {
  .responsive-nav {
    padding: var(--spacing-md) var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .responsive-nav {
    padding: var(--spacing-md) var(--spacing-xl);
  }
}

/* ============= RESPONSIVE SIDEBAR ============= */
.responsive-sidebar {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: -100%;
  transition: left 0.3s ease;
  z-index: 40;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.responsive-sidebar.open {
  left: 0;
}

@media (min-width: 768px) {
  .responsive-sidebar {
    width: 280px;
    position: relative;
    left: 0;
    height: auto;
  }
}

@media (min-width: 1024px) {
  .responsive-sidebar {
    width: 320px;
  }
}

/* ============= RESPONSIVE UTILITIES ============= */
.hide-mobile {
  display: none;
}

@media (min-width: 640px) {
  .hide-mobile {
    display: block;
  }
}

.show-mobile {
  display: block;
}

@media (min-width: 640px) {
  .show-mobile {
    display: none;
  }
}

.hide-tablet {
  display: block;
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hide-tablet {
    display: none;
  }
}

.show-tablet {
  display: none;
}

@media (min-width: 768px) and (max-width: 1023px) {
  .show-tablet {
    display: block;
  }
}

.hide-desktop {
  display: block;
}

@media (min-width: 1024px) {
  .hide-desktop {
    display: none;
  }
}

.show-desktop {
  display: none;
}

@media (min-width: 1024px) {
  .show-desktop {
    display: block;
  }
}

/* ============= RESPONSIVE SPACING ============= */
.responsive-spacing {
  margin: var(--spacing-md);
}

@media (min-width: 640px) {
  .responsive-spacing {
    margin: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .responsive-spacing {
    margin: var(--spacing-xl);
  }
}

.responsive-padding {
  padding: var(--spacing-md);
}

@media (min-width: 640px) {
  .responsive-padding {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 1024px) {
  .responsive-padding {
    padding: var(--spacing-xl);
  }
}

/* ============= RESPONSIVE ANIMATIONS ============= */
@media (prefers-reduced-motion: reduce) {
  .responsive-card,
  .responsive-button,
  .responsive-sidebar {
    transition: none;
  }
}

/* ============= TOUCH DEVICE OPTIMIZATIONS ============= */
@media (hover: none) and (pointer: coarse) {
  .responsive-button {
    min-height: 44px; /* iOS touch target minimum */
    min-width: 44px;
  }
  
  .responsive-card:hover {
    transform: none; /* Disable hover effects on touch devices */
  }
}
