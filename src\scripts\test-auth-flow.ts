import { supabase } from '@/integrations/supabase/client';

export async function testAuthFlow() {
  console.log('🔍 Testing Authentication Flow...\n');

  try {
    // Test 1: Check current session
    console.log('1. Checking current session...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('❌ Session check failed:', sessionError.message);
      return false;
    }

    if (session) {
      console.log('✅ User is authenticated:', session.user.email);
      
      // Test 2: Check if profile exists
      console.log('\n2. Checking user profile...');
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single();

      if (profileError) {
        if (profileError.code === 'PGRST116') {
          console.log('⚠️ Profile not found, this should trigger auto-creation');
        } else {
          console.error('❌ Profile fetch error:', profileError.message);
        }
      } else {
        console.log('✅ Profile found:', {
          id: profile.id,
          email: profile.email,
          role: profile.role,
          full_name: profile.full_name
        });
      }

      // Test 3: Check role-based redirect logic
      console.log('\n3. Testing role-based redirect logic...');
      const roleRoutes = {
        admin: '/dashboard/admin',
        manager: '/dashboard/manager', 
        staff: '/dashboard/staff',
        accountant: '/dashboard/accountant',
        'staff-admin': '/dashboard/staff-admin'
      };
      
      const userRole = profile?.role || 'staff';
      const redirectPath = roleRoutes[userRole as keyof typeof roleRoutes] || '/dashboard/staff';
      console.log(`✅ User role: ${userRole} → Redirect to: ${redirectPath}`);

    } else {
      console.log('ℹ️ No active session - user needs to login');
    }

    // Test 4: Check profiles table structure
    console.log('\n4. Testing profiles table structure...');
    const { data: tableData, error: tableError } = await supabase
      .from('profiles')
      .select('id, email, role, full_name')
      .limit(1);

    if (tableError) {
      console.error('❌ Profiles table access failed:', tableError.message);
      return false;
    } else {
      console.log('✅ Profiles table accessible');
    }

    console.log('\n🎉 Authentication flow test completed successfully!');
    return true;

  } catch (error: any) {
    console.error('❌ Authentication flow test failed:', error.message);
    return false;
  }
}

// Auto-run if this script is executed directly
if (typeof window !== 'undefined') {
  // Browser environment - add to window for manual testing
  (window as any).testAuthFlow = testAuthFlow;
  console.log('🔧 testAuthFlow() function available in browser console');
}
