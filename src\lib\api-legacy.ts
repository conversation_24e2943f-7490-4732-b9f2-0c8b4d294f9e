import { supabase } from "@/integrations/supabase/client";

type Profile = {
  id: string;
  full_name: string | null;
  email: string | null;
  role: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
};

// ============= AUTH API =============
export const authAPI = {
  getCurrentUser: async () => {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  },

  getUserProfile: async (userId: string) => {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        *,
        department:departments(id, name, description)
      `)
      .eq('id', userId)
      .single();
    if (error) throw error;
    return data;
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }
};

// ============= USERS API =============
export const usersAPI = {
  getUsers: async () => {
    const { data, error } = await supabase
      .from('profiles')
      .select(`
        *,
        department:departments(id, name, description)
      `)
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createUser: async (userData: any) => {
    const { data, error } = await supabase
      .from('profiles')
      .insert(userData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  updateUser: async (userId: string, userData: any) => {
    const { data, error } = await supabase
      .from('profiles')
      .update(userData)
      .eq('id', userId)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  deleteUser: async (userId: string) => {
    const { error } = await supabase
      .from('profiles')
      .delete()
      .eq('id', userId);
    if (error) throw error;
  }
};

// ============= DEPARTMENTS API =============
export const departmentsAPI = {
  getDepartments: async () => {
    const { data, error } = await supabase
      .from('departments')
      .select('*')
      .order('name');
    if (error) throw error;
    return data;
  },

  createDepartment: async (deptData: any) => {
    const { data, error } = await supabase
      .from('departments')
      .insert(deptData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  updateDepartment: async (deptId: string, deptData: any) => {
    const { data, error } = await supabase
      .from('departments')
      .update(deptData)
      .eq('id', deptId)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= PROJECTS API =============
export const projectsAPI = {
  getProjects: async () => {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        manager:profiles!manager_id(id, full_name, email)
      `)
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  getProject: async (projectId: string) => {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        manager:profiles!manager_id(id, full_name, email),
        tasks(*)
      `)
      .eq('id', projectId)
      .single();
    if (error) throw error;
    return data;
  },

  createProject: async (projectData: any) => {
    const { data, error } = await supabase
      .from('projects')
      .insert(projectData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  updateProject: async (projectId: string, projectData: any) => {
    const { data, error } = await supabase
      .from('projects')
      .update(projectData)
      .eq('id', projectId)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= TASKS API =============
export const tasksAPI = {
  getTasks: async (filters?: { assignedTo?: string; projectId?: string; status?: string }) => {
    let query = supabase
      .from('tasks')
      .select(`
        *,
        assigned_to:profiles!assigned_to_id(id, full_name, email),
        created_by:profiles!created_by_id(id, full_name, email),
        project:projects(id, name)
      `);

    if (filters?.assignedTo) {
      query = query.eq('assigned_to_id', filters.assignedTo);
    }
    if (filters?.projectId) {
      query = query.eq('project_id', filters.projectId);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createTask: async (taskData: any) => {
    const { data, error } = await supabase
      .from('tasks')
      .insert(taskData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  updateTask: async (taskId: string, taskData: any) => {
    const { data, error } = await supabase
      .from('tasks')
      .update(taskData)
      .eq('id', taskId)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= REPORTS API =============
export const reportsAPI = {
  getBatteryReports: async (filters?: { reporterId?: string; siteId?: string }) => {
    let query = supabase
      .from('battery_reports')
      .select(`
        *,
        reporter:profiles!reporter_id(id, full_name, email)
      `);

    if (filters?.reporterId) {
      query = query.eq('reporter_id', filters.reporterId);
    }
    if (filters?.siteId) {
      query = query.eq('site_id', filters.siteId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createBatteryReport: async (reportData: any) => {
    const { data, error } = await supabase
      .from('battery_reports')
      .insert(reportData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  getTelecomReports: async (filters?: { reporterId?: string; siteId?: string }) => {
    let query = supabase
      .from('ct_power_reports')
      .select(`
        *,
        created_by:profiles!created_by(id, full_name, email)
      `);

    if (filters?.reporterId) {
      query = query.eq('created_by', filters.reporterId);
    }
    if (filters?.siteId) {
      query = query.eq('site_id', filters.siteId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createTelecomReport: async (reportData: any) => {
    const { data, error } = await supabase
      .from('ct_power_reports')
      .insert(reportData)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= TELECOM SITES API =============
export const telecomSitesAPI = {
  getSites: async () => {
    const { data, error } = await supabase
      .from('telecom_sites')
      .select('*')
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createSite: async (siteData: any) => {
    const { data, error } = await supabase
      .from('telecom_sites')
      .insert(siteData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  updateSite: async (siteId: string, siteData: any) => {
    const { data, error } = await supabase
      .from('telecom_sites')
      .update(siteData)
      .eq('id', siteId)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= MEMOS API =============
export const memosAPI = {
  getMemos: async (filters?: any) => {
    let query = supabase.from('staff_memos').select('*');

    if (filters?.senderId) {
      query = query.eq('sender_id', filters.senderId);
    }
    if (filters?.status) {
      query = query.eq('status', filters.status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });
    if (error) throw error;
    return data || [];
  },

  createMemo: async (memoData: any) => {
    const { data, error } = await supabase
      .from('staff_memos')
      .insert(memoData)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= FINANCIAL API =============
export const financialAPI = {
  getInvoices: async () => {
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        created_by:profiles!created_by(id, full_name, email)
      `)
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createInvoice: async (invoiceData: any) => {
    const { data, error } = await supabase
      .from('invoices')
      .insert(invoiceData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  getExpenses: async () => {
    const { data, error } = await supabase
      .from('expense_reports')
      .select('*')
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createExpense: async (expenseData: any) => {
    const { data, error } = await supabase
      .from('expenses')
      .insert(expenseData)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= FLEET API =============
export const fleetAPI = {
  getVehicles: async () => {
    const { data, error } = await supabase
      .from('fleet_vehicles')
      .select('*')
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createVehicle: async (vehicleData: any) => {
    const { data, error } = await supabase
      .from('fleet_vehicles')
      .insert(vehicleData)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= ASSETS API =============
export const assetsAPI = {
  getAssets: async () => {
    const { data, error } = await supabase
      .from('assets_inventory')
      .select(`
        *,
        created_by:profiles!created_by(id, full_name, email),
        department:departments(id, name)
      `)
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createAsset: async (assetData: any) => {
    const { data, error } = await supabase
      .from('assets_inventory')
      .insert(assetData)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= CONSTRUCTION API =============
export const constructionAPI = {
  getSites: async () => {
    const { data, error } = await supabase
      .from('construction_sites')
      .select(`
        *,
        site_manager:profiles!site_manager_id(id, full_name, email)
      `)
      .order('created_at', { ascending: false });
    if (error) throw error;
    return data;
  },

  createSite: async (siteData: any) => {
    const { data, error } = await supabase
      .from('construction_sites')
      .insert(siteData)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
};

// ============= TIME TRACKING API =============
export const timeTrackingAPI = {
  getTimeLogs: async (filters?: { userId?: string; date?: string }) => {
    let query = supabase
      .from('time_logs')
      .select(`
        *,
        user:profiles!user_id(id, full_name, email)
      `);

    if (filters?.userId) {
      query = query.eq('user_id', filters.userId);
    }
    if (filters?.date) {
      query = query.gte('clock_in', `${filters.date}T00:00:00`)
                   .lt('clock_in', `${filters.date}T23:59:59`);
    }

    const { data, error } = await query.order('clock_in', { ascending: false });
    if (error) throw error;
    return data;
  },

  clockIn: async (clockInData: any) => {
    const { data, error } = await supabase
      .from('time_logs')
      .insert(clockInData)
      .select()
      .single();
    if (error) throw error;
    return data;
  },

  clockOut: async (timeLogId: string, clockOutData: any) => {
    const { data, error } = await supabase
      .from('time_logs')
      .update(clockOutData)
      .eq('id', timeLogId)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
}; 
