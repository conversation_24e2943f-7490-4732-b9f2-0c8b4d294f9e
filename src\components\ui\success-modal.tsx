import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { <PERSON><PERSON>ircle, <PERSON>, <PERSON>rk<PERSON>, ArrowRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

interface SuccessModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title: string
  description: string
  actionLabel?: string
  onAction?: () => void
  variant?: "success" | "info" | "warning"
  showConfetti?: boolean
}

const SuccessModal = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Root>,
  SuccessModalProps
>(({ 
  open, 
  onOpenChange, 
  title, 
  description, 
  actionLabel = "Continue", 
  onAction,
  variant = "success",
  showConfetti = true,
  ...props 
}, ref) => {
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  React.useEffect(() => {
    if (open && showConfetti && mounted) {
      // Trigger confetti animation
      const timer = setTimeout(() => {
        // You can integrate with a confetti library here
        console.log("🎉 Success confetti triggered!")
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [open, showConfetti, mounted])

  const variantStyles = {
    success: {
      gradient: "from-green-500/20 via-emerald-500/20 to-teal-500/20",
      iconColor: "text-green-400",
      glowColor: "shadow-[0_0_50px_rgba(34,197,94,0.3)]",
      borderColor: "border-green-500/30"
    },
    info: {
      gradient: "from-blue-500/20 via-cyan-500/20 to-sky-500/20",
      iconColor: "text-blue-400", 
      glowColor: "shadow-[0_0_50px_rgba(59,130,246,0.3)]",
      borderColor: "border-blue-500/30"
    },
    warning: {
      gradient: "from-yellow-500/20 via-orange-500/20 to-amber-500/20",
      iconColor: "text-yellow-400",
      glowColor: "shadow-[0_0_50px_rgba(245,158,11,0.3)]",
      borderColor: "border-yellow-500/30"
    }
  }

  const currentVariant = variantStyles[variant]

  return (
    <DialogPrimitive.Root open={open} onOpenChange={onOpenChange} {...props}>
      <DialogPrimitive.Portal>
        <DialogPrimitive.Overlay className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <DialogPrimitive.Content
          className={cn(
            "fixed left-[50%] top-[50%] z-50 w-full max-w-md translate-x-[-50%] translate-y-[-50%] duration-500",
            "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]"
          )}
        >
          <div className={cn(
            "relative overflow-hidden rounded-[30px] border-2 bg-gradient-to-br from-black/90 via-black/95 to-black/90 backdrop-blur-[30px] p-8 text-white",
            currentVariant.borderColor,
            currentVariant.glowColor
          )}>
            {/* Animated background gradient */}
            <div className={cn(
              "absolute inset-0 bg-gradient-to-br opacity-30 animate-pulse",
              currentVariant.gradient
            )} />
            
            {/* Floating particles effect */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(6)].map((_, i) => (
                <div
                  key={i}
                  className={cn(
                    "absolute h-1 w-1 rounded-full opacity-60 animate-float",
                    currentVariant.iconColor.replace('text-', 'bg-')
                  )}
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 3}s`,
                    animationDuration: `${3 + Math.random() * 2}s`
                  }}
                />
              ))}
            </div>

            <div className="relative z-10 text-center space-y-6">
              {/* Icon with pulse animation */}
              <div className="flex justify-center">
                <div className={cn(
                  "relative flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br backdrop-blur-sm animate-pulse-slow",
                  currentVariant.gradient
                )}>
                  <CheckCircle className={cn("h-10 w-10", currentVariant.iconColor)} />
                  
                  {/* Ripple effect */}
                  <div className={cn(
                    "absolute inset-0 rounded-full border-2 animate-ping",
                    currentVariant.borderColor
                  )} />
                  <div className={cn(
                    "absolute inset-2 rounded-full border animate-ping",
                    currentVariant.borderColor
                  )} style={{ animationDelay: "0.5s" }} />
                </div>
              </div>

              {/* Content */}
              <div className="space-y-3">
                <DialogPrimitive.Title className="text-2xl font-bold bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  {title}
                </DialogPrimitive.Title>
                <DialogPrimitive.Description className="text-white/70 leading-relaxed">
                  {description}
                </DialogPrimitive.Description>
              </div>

              {/* Action buttons */}
              <div className="flex gap-3 justify-center pt-4">
                <Button
                  onClick={() => onOpenChange(false)}
                  variant="outline"
                  className="rounded-[20px] border-white/20 bg-white/5 text-white hover:bg-white/10 hover:border-white/30 transition-all duration-300"
                >
                  Close
                </Button>
                {onAction && (
                  <Button
                    onClick={() => {
                      onAction()
                      onOpenChange(false)
                    }}
                    className={cn(
                      "rounded-[20px] bg-gradient-to-r text-white font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg group",
                      variant === "success" && "from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700",
                      variant === "info" && "from-blue-500 to-cyan-600 hover:from-blue-600 hover:to-cyan-700",
                      variant === "warning" && "from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700"
                    )}
                  >
                    {actionLabel}
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Button>
                )}
              </div>
            </div>

            {/* Close button */}
            <DialogPrimitive.Close className="absolute right-4 top-4 rounded-full p-2 text-white/60 hover:text-white hover:bg-white/10 transition-all duration-300">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogPrimitive.Close>

            {/* Sparkles decoration */}
            <div className="absolute top-4 left-4">
              <Sparkles className="h-5 w-5 text-white/30 animate-pulse" />
            </div>
            <div className="absolute bottom-4 right-8">
              <Sparkles className="h-4 w-4 text-white/20 animate-pulse" style={{ animationDelay: "1s" }} />
            </div>
          </div>
        </DialogPrimitive.Content>
      </DialogPrimitive.Portal>
    </DialogPrimitive.Root>
  )
})

SuccessModal.displayName = "SuccessModal"

export { SuccessModal }

// Hook for easy usage
export const useSuccessModal = () => {
  const [isOpen, setIsOpen] = React.useState(false)
  
  const showSuccess = React.useCallback((config: Omit<SuccessModalProps, 'open' | 'onOpenChange'>) => {
    setIsOpen(true)
    return { ...config, open: isOpen, onOpenChange: setIsOpen }
  }, [isOpen])

  const hideSuccess = React.useCallback(() => {
    setIsOpen(false)
  }, [])

  return {
    isOpen,
    showSuccess,
    hideSuccess,
    SuccessModalProps: { open: isOpen, onOpenChange: setIsOpen }
  }
}
