import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';

/**
 * Advanced AI Service
 * Enhanced AI with comprehensive organization knowledge, context awareness, and intelligent responses
 */

interface AIContext {
  user: any;
  organization: {
    name: string;
    departments: any[];
    projects: any[];
    members: any[];
    recentActivities: any[];
    systemStats: any;
  };
  currentSession: {
    sessionId: string;
    timestamp: string;
    userRole: string;
    department: string;
    conversationHistory: any[];
  };
}

interface AIResponse {
  message: string;
  actions?: Array<{
    type: string;
    label: string;
    data: any;
    icon?: string;
  }>;
  suggestions?: string[];
  context?: any;
  confidence?: number;
  processingTime?: number;
  tokensUsed?: number;
}

interface AICapability {
  name: string;
  description: string;
  examples: string[];
  category: string;
}

export class AdvancedAIService {
  private static context: AIContext | null = null;
  private static knowledgeBase: Map<string, any> = new Map();
  private static capabilities: AICapability[] = [];
  private static sessionId: string = '';

  // Initialize AI with comprehensive organization knowledge
  static async initialize(): Promise<void> {
    try {
      console.log('🤖 Initializing Advanced AI Service...');
      
      this.sessionId = crypto.randomUUID();
      
      // Load current user
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      // Load comprehensive organization data
      const [departments, projects, members, activities, systemStats] = await Promise.all([
        ComprehensiveAPI.getAllDepartments(),
        ComprehensiveAPI.getAllProjects(),
        ComprehensiveAPI.getAllProfiles(),
        this.getRecentActivities(),
        this.getSystemStats()
      ]);

      this.context = {
        user: currentUser,
        organization: {
          name: 'CTN Nigeria',
          departments: departments || [],
          projects: projects || [],
          members: members || [],
          recentActivities: activities || [],
          systemStats: systemStats || {}
        },
        currentSession: {
          sessionId: this.sessionId,
          timestamp: new Date().toISOString(),
          userRole: currentUser?.role || 'staff',
          department: currentUser?.department_id || 'unknown',
          conversationHistory: []
        }
      };

      // Build comprehensive knowledge base
      await this.buildKnowledgeBase();
      
      // Initialize AI capabilities
      this.initializeCapabilities();
      
      console.log('✅ Advanced AI Service initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Advanced AI Service:', error);
    }
  }

  // Build comprehensive knowledge base
  private static async buildKnowledgeBase(): Promise<void> {
    if (!this.context) return;

    const kb = this.knowledgeBase;
    
    // Organization structure and insights
    kb.set('organization_structure', {
      departments: this.context.organization.departments.map(d => ({
        id: d.id,
        name: d.name,
        manager: d.manager?.full_name,
        memberCount: this.context.organization.members.filter(m => m.department_id === d.id).length,
        budget: d.budget,
        status: d.status
      })),
      totalMembers: this.context.organization.members.length,
      activeProjects: this.context.organization.projects.filter(p => p.status === 'active').length,
      totalProjects: this.context.organization.projects.length
    });

    // Project insights and analytics
    kb.set('project_insights', {
      totalProjects: this.context.organization.projects.length,
      byStatus: this.groupBy(this.context.organization.projects, 'status'),
      byPriority: this.groupBy(this.context.organization.projects, 'priority'),
      byDepartment: this.groupBy(this.context.organization.projects, 'department_id'),
      recentProjects: this.context.organization.projects
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 10),
      upcomingDeadlines: this.context.organization.projects
        .filter(p => p.end_date && new Date(p.end_date) > new Date())
        .sort((a, b) => new Date(a.end_date).getTime() - new Date(b.end_date).getTime())
        .slice(0, 5)
    });

    // Member insights and analytics
    kb.set('member_insights', {
      totalMembers: this.context.organization.members.length,
      byRole: this.groupBy(this.context.organization.members, 'role'),
      byDepartment: this.groupBy(this.context.organization.members, 'department_id'),
      byStatus: this.groupBy(this.context.organization.members, 'status'),
      activeMembers: this.context.organization.members.filter(m => m.status === 'active').length,
      recentJoins: this.context.organization.members
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 5)
    });

    // System capabilities and workflows
    kb.set('system_capabilities', {
      coreFeatures: [
        'Project Management & Tracking',
        'Member Assignment & Management',
        'Memo Submission & Publishing',
        'Report Generation & Submission',
        'Task Management & Assignment',
        'Time Tracking & Logging',
        'AI Assistance & Automation',
        'Activity Logging & Monitoring',
        'Role-Based Access Control',
        'Department Organization'
      ],
      workflows: [
        'Create Project → Assign Members → Track Progress → Complete',
        'Submit Memo → Review → Publish → Distribute',
        'Generate Report → Submit → Review → Approve → Archive',
        'Create Task → Assign → Track Time → Complete → Review',
        'Clock In → Work on Tasks → Log Time → Clock Out',
        'Request Help → AI Analysis → Provide Solution → Follow Up'
      ],
      integrations: [
        'Supabase Database',
        'Authentication System',
        'File Storage',
        'Email Notifications',
        'Real-time Updates',
        'Analytics & Reporting'
      ]
    });

    // Load AI knowledge base from database
    try {
      const { data: knowledgeEntries } = await supabase
        .from('ai_knowledge_base')
        .select('*')
        .eq('is_active', true)
        .order('usage_count', { ascending: false });

      if (knowledgeEntries) {
        kb.set('knowledge_entries', knowledgeEntries);
      }
    } catch (error) {
      console.warn('Could not load AI knowledge base:', error);
    }

    // Common queries and intelligent responses
    kb.set('intelligent_responses', {
      project_management: {
        'create project': 'I can help you create a new project. I need: project name, description, client (optional), budget, location, timeline, priority, and team assignments.',
        'assign members': 'To assign members to a project, I can show you available team members by department and role, then help you allocate hours and responsibilities.',
        'track progress': 'I can provide real-time project progress updates, milestone tracking, and identify potential bottlenecks or delays.',
        'project status': 'Projects can be: planning, active, on_hold, completed, or cancelled. I can help you update status and notify stakeholders.'
      },
      member_management: {
        'add member': 'I can guide you through adding new team members, setting up their profiles, assigning roles, and department placement.',
        'manage roles': 'Available roles: admin (full access), manager (department management), staff-admin (administrative), hr (human resources), accountant (financial), staff (general).',
        'department assignment': 'I can help assign members to departments and update their responsibilities and access levels.'
      },
      task_management: {
        'create task': 'I can help create tasks with proper categorization, priority levels, time estimates, and assignment to team members.',
        'track time': 'I can help you start time tracking, log work hours, and generate time reports for projects and tasks.',
        'task status': 'Task statuses: todo, in_progress, review, testing, done, cancelled, blocked. I can help update and track progress.'
      },
      system_help: {
        'how to': 'I can provide step-by-step guidance for any system function. Just ask "how to [action]" and I\'ll walk you through it.',
        'troubleshoot': 'I can help diagnose and resolve system issues, check logs, and provide solutions.',
        'analytics': 'I can generate insights on projects, team performance, time utilization, and system usage patterns.'
      }
    });
  }

  // Initialize AI capabilities
  private static initializeCapabilities(): void {
    this.capabilities = [
      {
        name: 'Project Management Assistant',
        description: 'Create, manage, and track projects with intelligent insights',
        examples: ['Create a new project', 'Show project status', 'Assign team members'],
        category: 'Project Management'
      },
      {
        name: 'Team Coordination',
        description: 'Manage team members, roles, and assignments',
        examples: ['Add new team member', 'Show department structure', 'Assign roles'],
        category: 'Team Management'
      },
      {
        name: 'Task & Time Management',
        description: 'Create tasks, track time, and monitor productivity',
        examples: ['Create a new task', 'Start time tracking', 'Generate time report'],
        category: 'Productivity'
      },
      {
        name: 'Analytics & Insights',
        description: 'Provide data-driven insights and analytics',
        examples: ['Show project analytics', 'Team performance report', 'System usage stats'],
        category: 'Analytics'
      },
      {
        name: 'System Navigation',
        description: 'Help navigate and use system features',
        examples: ['How to submit a memo', 'Where to find reports', 'System tutorials'],
        category: 'Help & Support'
      },
      {
        name: 'Intelligent Automation',
        description: 'Automate routine tasks and provide smart suggestions',
        examples: ['Auto-assign tasks', 'Suggest project timelines', 'Optimize workflows'],
        category: 'Automation'
      }
    ];
  }

  // Process AI chat message with enhanced intelligence
  static async processMessage(message: string): Promise<AIResponse> {
    const startTime = Date.now();
    
    if (!this.context) {
      await this.initialize();
    }

    const lowerMessage = message.toLowerCase();
    
    // Log the interaction
    await this.logInteraction(message, 'user');

    // Analyze intent and generate intelligent response
    const response = await this.generateIntelligentResponse(lowerMessage, message);
    
    // Calculate processing metrics
    const processingTime = Date.now() - startTime;
    response.processingTime = processingTime;
    response.tokensUsed = Math.ceil(message.length / 4); // Rough token estimation
    
    // Log AI response
    await this.logInteraction(response.message, 'assistant', {
      confidence: response.confidence,
      processingTime,
      tokensUsed: response.tokensUsed,
      actions: response.actions,
      suggestions: response.suggestions
    });
    
    // Add to conversation history
    this.context!.currentSession.conversationHistory.push({
      user: message,
      assistant: response.message,
      timestamp: new Date().toISOString(),
      confidence: response.confidence
    });
    
    return response;
  }

  // Generate intelligent response based on context and intent
  private static async generateIntelligentResponse(lowerMessage: string, originalMessage: string): Promise<AIResponse> {
    const kb = this.knowledgeBase;
    const context = this.context!;

    // Enhanced intent analysis with better context understanding
    const intent = this.analyzeIntent(lowerMessage);
    let confidence = 0.9; // Increased confidence with better analysis

    // Project-related queries
    if (intent.category === 'project_management') {
      return this.handleProjectQueries(intent, lowerMessage, context, kb);
    }

    // Member/Team-related queries
    if (intent.category === 'team_management') {
      return this.handleTeamQueries(intent, lowerMessage, context, kb);
    }

    // Task-related queries
    if (intent.category === 'task_management') {
      return this.handleTaskQueries(intent, lowerMessage, context, kb);
    }

    // Analytics and reporting
    if (intent.category === 'analytics') {
      return this.handleAnalyticsQueries(intent, lowerMessage, context, kb);
    }

    // System help and navigation
    if (intent.category === 'help') {
      return this.handleHelpQueries(intent, lowerMessage, context, kb);
    }

    // Default intelligent response
    return this.generateDefaultResponse(originalMessage, context, kb);
  }

  // Enhanced intent analysis with better pattern recognition
  private static analyzeIntent(message: string): { category: string; action: string; entities: string[]; confidence: number } {
    const lowerMessage = message.toLowerCase();

    // Enhanced keyword patterns with scoring
    const patterns = {
      project_management: {
        keywords: ['project', 'create project', 'new project', 'assign', 'progress', 'status', 'deadline', 'milestone', 'budget', 'client'],
        actions: ['create', 'update', 'delete', 'assign', 'track', 'manage'],
        score: 0
      },
      team_management: {
        keywords: ['member', 'team', 'assign', 'role', 'department', 'staff', 'employee', 'user', 'permission', 'access'],
        actions: ['add', 'remove', 'assign', 'manage', 'invite'],
        score: 0
      },
      task_management: {
        keywords: ['task', 'todo', 'assignment', 'time', 'track', 'log', 'work', 'complete', 'pending', 'priority'],
        actions: ['create', 'complete', 'assign', 'track', 'update'],
        score: 0
      },
      document_analysis: {
        keywords: ['document', 'analyze', 'upload', 'file', 'pdf', 'text', 'content', 'summary', 'extract'],
        actions: ['analyze', 'upload', 'process', 'extract', 'summarize'],
        score: 0
      },
      analytics: {
        keywords: ['report', 'analytics', 'stats', 'performance', 'insights', 'data', 'chart', 'graph', 'metrics'],
        actions: ['generate', 'view', 'export', 'analyze'],
        score: 0
      },
      help: {
        keywords: ['help', 'how', 'what', 'where', 'guide', 'tutorial', 'explain', 'assist', 'support'],
        actions: ['help', 'explain', 'guide', 'assist'],
        score: 0
      }
    };

    // Calculate scores for each category
    let bestCategory = 'general';
    let bestAction = 'chat';
    let maxScore = 0;
    let entities: string[] = [];

    for (const [category, pattern] of Object.entries(patterns)) {
      // Score based on keyword matches
      const keywordMatches = pattern.keywords.filter(keyword => lowerMessage.includes(keyword));
      const actionMatches = pattern.actions.filter(action => lowerMessage.includes(action));

      const score = keywordMatches.length * 2 + actionMatches.length * 3;

      if (score > maxScore) {
        maxScore = score;
        bestCategory = category;
        bestAction = actionMatches[0] || 'query';
        entities = [...keywordMatches, ...actionMatches];
      }
    }

    // Calculate confidence based on score and message length
    const confidence = Math.min(0.95, Math.max(0.3, maxScore / (lowerMessage.split(' ').length * 0.5)));

    return {
      category: bestCategory,
      action: bestAction,
      entities: entities.slice(0, 5), // Limit entities
      confidence
    };
  }

  // Handle project-related queries
  private static handleProjectQueries(intent: any, message: string, context: AIContext, kb: Map<string, any>): AIResponse {
    const projectInsights = kb.get('project_insights');
    
    if (message.includes('create') || message.includes('new')) {
      return {
        message: `🚀 **Project Creation Assistant**

I'll help you create a new project! As a ${context.user?.role}, you have the authority to create and manage projects.

📋 **Required Information:**
• **Project Name** - Clear, descriptive title
• **Description** - Project scope and objectives
• **Client** - Client name (if applicable)
• **Budget** - Estimated project budget
• **Location** - Project location/site
• **Timeline** - Start and end dates
• **Priority** - Low, Medium, High, or Urgent
• **Team** - Initial team member assignments

🎯 **Current Organization Status:**
• Total projects: ${projectInsights?.totalProjects || 0}
• Active projects: ${projectInsights?.byStatus?.active || 0}
• Your department: ${context.organization.departments.find(d => d.id === context.user?.department_id)?.name || 'Unknown'}
• Available team members: ${context.organization.members.filter(m => m.status === 'active').length}

Would you like me to guide you through the project creation process step by step?`,
        actions: [
          {
            type: 'navigate',
            label: 'Create New Project',
            data: { route: '/projects/new' },
            icon: '🚀'
          },
          {
            type: 'help',
            label: 'Project Creation Guide',
            data: { topic: 'project_creation' },
            icon: '📖'
          },
          {
            type: 'template',
            label: 'Use Project Template',
            data: { action: 'load_template' },
            icon: '📋'
          }
        ],
        suggestions: [
          'Show me project templates',
          'What are the project requirements?',
          'How do I assign team members?',
          'Show current active projects'
        ],
        confidence: 0.95
      };
    }

    if (message.includes('status') || message.includes('progress')) {
      return {
        message: `📊 **Project Status Overview**

🎯 **Current Projects Summary:**
• Total projects: ${projectInsights?.totalProjects || 0}
• Active: ${projectInsights?.byStatus?.active || 0}
• Planning: ${projectInsights?.byStatus?.planning || 0}
• On Hold: ${projectInsights?.byStatus?.on_hold || 0}
• Completed: ${projectInsights?.byStatus?.completed || 0}

⚡ **By Priority:**
• Urgent: ${projectInsights?.byPriority?.urgent || 0}
• High: ${projectInsights?.byPriority?.high || 0}
• Medium: ${projectInsights?.byPriority?.medium || 0}
• Low: ${projectInsights?.byPriority?.low || 0}

🆕 **Recent Projects:**
${projectInsights?.recentProjects?.slice(0, 3).map((p: any) => 
  `• ${p.name} (${p.status}) - ${p.priority} priority`
).join('\n') || 'No recent projects'}

⏰ **Upcoming Deadlines:**
${projectInsights?.upcomingDeadlines?.slice(0, 3).map((p: any) => 
  `• ${p.name} - Due: ${new Date(p.end_date).toLocaleDateString()}`
).join('\n') || 'No upcoming deadlines'}`,
        actions: [
          {
            type: 'navigate',
            label: 'View All Projects',
            data: { route: '/projects' },
            icon: '📋'
          },
          {
            type: 'analytics',
            label: 'Project Analytics',
            data: { type: 'project_analytics' },
            icon: '📊'
          },
          {
            type: 'filter',
            label: 'Filter by Status',
            data: { type: 'project_filter' },
            icon: '🔍'
          }
        ],
        suggestions: [
          'Show overdue projects',
          'Create a new project',
          'Show my assigned projects',
          'Generate project report'
        ],
        confidence: 0.92
      };
    }

    return this.generateDefaultResponse(message, context, kb);
  }

  // Handle team-related queries
  private static handleTeamQueries(intent: any, message: string, context: AIContext, kb: Map<string, any>): AIResponse {
    const memberInsights = kb.get('member_insights');
    
    return {
      message: `👥 **Team Management Dashboard**

🏢 **Organization Overview:**
• Total members: ${memberInsights?.totalMembers || 0}
• Active members: ${memberInsights?.activeMembers || 0}
• Departments: ${context.organization.departments.length}

👔 **By Role:**
${Object.entries(memberInsights?.byRole || {}).map(([role, count]) => 
  `• ${role.charAt(0).toUpperCase() + role.slice(1)}: ${count}`
).join('\n')}

🏢 **By Department:**
${context.organization.departments.slice(0, 5).map(d => {
  const memberCount = context.organization.members.filter(m => m.department_id === d.id).length;
  return `• ${d.name}: ${memberCount} members`;
}).join('\n')}

As a ${context.user?.role}, you can ${this.getUserPermissions(context.user?.role)}.`,
      actions: [
        {
          type: 'navigate',
          label: 'View Team Members',
          data: { route: '/team' },
          icon: '👥'
        },
        {
          type: 'action',
          label: 'Add New Member',
          data: { action: 'add_member' },
          icon: '➕'
        },
        {
          type: 'analytics',
          label: 'Team Analytics',
          data: { type: 'team_analytics' },
          icon: '📊'
        }
      ],
      suggestions: [
        'Add a new team member',
        'Show department structure',
        'Assign roles and permissions',
        'View member profiles'
      ],
      confidence: 0.88
    };
  }

  // Handle task-related queries
  private static handleTaskQueries(intent: any, message: string, context: AIContext, kb: Map<string, any>): AIResponse {
    return {
      message: `📝 **Task Management Assistant**

I can help you with comprehensive task management:

🎯 **Task Operations:**
• Create new tasks with detailed specifications
• Assign tasks to team members
• Track task progress and status
• Set priorities and deadlines
• Log time spent on tasks
• Add comments and updates

⏱️ **Time Tracking:**
• Start/stop time tracking for tasks
• Log work hours and activities
• Generate time reports
• Track billable vs non-billable time
• Monitor productivity metrics

📊 **Task Analytics:**
• Task completion rates
• Time utilization reports
• Team productivity insights
• Project task breakdowns

As a ${context.user?.role}, you can create tasks, assign them to team members, and track progress across all your projects.`,
      actions: [
        {
          type: 'navigate',
          label: 'Create New Task',
          data: { route: '/tasks/new' },
          icon: '➕'
        },
        {
          type: 'action',
          label: 'Start Time Tracking',
          data: { action: 'start_timer' },
          icon: '⏱️'
        },
        {
          type: 'navigate',
          label: 'View My Tasks',
          data: { route: '/tasks/my' },
          icon: '📋'
        }
      ],
      suggestions: [
        'Create a new task',
        'Start time tracking',
        'Show my assigned tasks',
        'Generate time report'
      ],
      confidence: 0.90
    };
  }

  // Handle analytics queries
  private static handleAnalyticsQueries(intent: any, message: string, context: AIContext, kb: Map<string, any>): AIResponse {
    const systemStats = context.organization.systemStats;
    
    return {
      message: `📊 **Analytics & Insights Dashboard**

🎯 **System Overview:**
• Total users: ${context.organization.members.length}
• Active projects: ${context.organization.projects.filter(p => p.status === 'active').length}
• Departments: ${context.organization.departments.length}
• Recent activities: ${context.organization.recentActivities.length}

📈 **Performance Metrics:**
• Project completion rate: ${this.calculateCompletionRate(context.organization.projects)}%
• Average project duration: ${this.calculateAverageProjectDuration(context.organization.projects)} days
• Team utilization: ${this.calculateTeamUtilization()}%

🔍 **Available Analytics:**
• Project performance reports
• Team productivity analysis
• Time utilization reports
• Department efficiency metrics
• System usage statistics
• Financial performance tracking

I can generate detailed reports and insights for any aspect of your organization's performance.`,
      actions: [
        {
          type: 'analytics',
          label: 'Project Analytics',
          data: { type: 'project_analytics' },
          icon: '📊'
        },
        {
          type: 'analytics',
          label: 'Team Performance',
          data: { type: 'team_performance' },
          icon: '👥'
        },
        {
          type: 'analytics',
          label: 'Time Reports',
          data: { type: 'time_reports' },
          icon: '⏱️'
        }
      ],
      suggestions: [
        'Generate project report',
        'Show team performance',
        'Time utilization analysis',
        'Department comparison'
      ],
      confidence: 0.85
    };
  }

  // Handle help queries
  private static handleHelpQueries(intent: any, message: string, context: AIContext, kb: Map<string, any>): AIResponse {
    const capabilities = kb.get('system_capabilities');
    
    return {
      message: `🤖 **AI Assistant Help Center**

I'm your intelligent organization assistant! Here's what I can help you with:

🎯 **Core Capabilities:**
${capabilities?.coreFeatures?.slice(0, 6).map((f: string) => `• ${f}`).join('\n') || ''}

🔄 **Common Workflows:**
${capabilities?.workflows?.slice(0, 3).map((w: string) => `• ${w}`).join('\n') || ''}

💡 **How to Get Help:**
• Ask specific questions: "How do I create a project?"
• Request guidance: "Guide me through memo submission"
• Get explanations: "Explain the task management system"
• Seek recommendations: "What's the best way to track time?"

🎭 **Your Current Context:**
• Role: ${context.user?.role}
• Department: ${context.organization.departments.find(d => d.id === context.user?.department_id)?.name || 'Unknown'}
• Access Level: ${this.getAccessLevel(context.user?.role)}

Just ask me anything about the system, and I'll provide detailed, contextual help!`,
      actions: [
        {
          type: 'help',
          label: 'System Tutorial',
          data: { type: 'tutorial' },
          icon: '🎓'
        },
        {
          type: 'help',
          label: 'Feature Guide',
          data: { type: 'features' },
          icon: '📖'
        },
        {
          type: 'help',
          label: 'Troubleshooting',
          data: { type: 'troubleshoot' },
          icon: '🔧'
        }
      ],
      suggestions: [
        'How do I create a project?',
        'Show me system features',
        'Explain user roles',
        'What can I do with my role?'
      ],
      confidence: 0.95
    };
  }

  // Generate default intelligent response
  private static generateDefaultResponse(message: string, context: AIContext, kb: Map<string, any>): AIResponse {
    return {
      message: `Hello! I'm your AI assistant for CTN Nigeria. I understand you're asking about "${message}".

🎯 **Quick Context:**
• You're logged in as: ${context.user?.full_name} (${context.user?.role})
• Organization: ${context.organization.name}
• Active projects: ${kb.get('project_insights')?.byStatus?.active || 0}
• Team members: ${kb.get('member_insights')?.totalMembers || 0}

🤖 **I can help you with:**
• Project management and creation
• Team member assignments and management
• Task creation and time tracking
• Memo and report submission
• System navigation and guidance
• Analytics and insights
• Workflow automation

What would you like to accomplish today?`,
      suggestions: [
        'Create a new project',
        'Show my tasks',
        'Start time tracking',
        'View team members',
        'Generate a report',
        'Show system capabilities'
      ],
      confidence: 0.75
    };
  }

  // Helper methods
  private static groupBy(array: any[], key: string): Record<string, number> {
    return array.reduce((result, item) => {
      const group = item[key] || 'unknown';
      result[group] = (result[group] || 0) + 1;
      return result;
    }, {});
  }

  private static getUserPermissions(role: string): string {
    const permissions = {
      admin: 'manage all aspects of the system including users, projects, and settings',
      manager: 'manage your department, create projects, and assign team members',
      'staff-admin': 'assist with administrative tasks and support team operations',
      hr: 'manage human resources, employee records, and organizational structure',
      accountant: 'handle financial aspects, budgets, and expense management',
      staff: 'work on assigned projects, submit reports, and track your time'
    };
    return permissions[role as keyof typeof permissions] || 'access standard user features';
  }

  private static getAccessLevel(role: string): string {
    const levels = {
      admin: 'Full System Access',
      manager: 'Department Management',
      'staff-admin': 'Administrative Support',
      hr: 'Human Resources',
      accountant: 'Financial Management',
      staff: 'Standard User'
    };
    return levels[role as keyof typeof levels] || 'Standard User';
  }

  private static calculateCompletionRate(projects: any[]): number {
    if (projects.length === 0) return 0;
    const completed = projects.filter(p => p.status === 'completed').length;
    return Math.round((completed / projects.length) * 100);
  }

  private static calculateAverageProjectDuration(projects: any[]): number {
    const completedProjects = projects.filter(p => p.status === 'completed' && p.start_date && p.end_date);
    if (completedProjects.length === 0) return 0;
    
    const totalDays = completedProjects.reduce((sum, p) => {
      const start = new Date(p.start_date);
      const end = new Date(p.end_date);
      return sum + Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
    }, 0);
    
    return Math.round(totalDays / completedProjects.length);
  }

  private static calculateTeamUtilization(): number {
    // This would typically calculate based on time logs and capacity
    // For now, return a placeholder value
    return 75;
  }

  private static async getRecentActivities(): Promise<any[]> {
    try {
      const { data } = await supabase
        .from('user_activities')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);
      return data || [];
    } catch (error) {
      console.warn('Could not load recent activities:', error);
      return [];
    }
  }

  private static async getSystemStats(): Promise<any> {
    try {
      // This would calculate various system statistics
      return {
        totalUsers: 0,
        activeProjects: 0,
        completedTasks: 0,
        systemUptime: '99.9%'
      };
    } catch (error) {
      console.warn('Could not load system stats:', error);
      return {};
    }
  }

  private static async logInteraction(message: string, role: 'user' | 'assistant', metadata: any = {}): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      await supabase.from('ai_interactions').insert({
        user_id: currentUser?.user_id,
        session_id: this.sessionId,
        role,
        message,
        type: 'chat',
        context: this.context ? {
          userRole: this.context.user?.role,
          department: this.context.user?.department_id,
          timestamp: new Date().toISOString()
        } : {},
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          model_used: 'advanced-ai-service'
        }
      });
    } catch (error) {
      console.error('Error logging AI interaction:', error);
    }
  }

  // Get AI capabilities
  static getCapabilities(): AICapability[] {
    return this.capabilities;
  }

  // Get organization insights
  static getOrganizationInsights(): any {
    return {
      context: this.context,
      knowledgeBase: Object.fromEntries(this.knowledgeBase),
      capabilities: this.capabilities
    };
  }

  // Refresh context
  static async refreshContext(): Promise<void> {
    await this.initialize();
  }

  // Get conversation history
  static getConversationHistory(): any[] {
    return this.context?.currentSession.conversationHistory || [];
  }

  // Clear conversation history
  static clearConversationHistory(): void {
    if (this.context) {
      this.context.currentSession.conversationHistory = [];
    }
  }
}

export default AdvancedAIService;
