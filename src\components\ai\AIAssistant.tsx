
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/auth/AuthProvider';
import { Bot, Send, User, Loader, Paperclip, Mic, Image, FileText } from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

export const AIAssistant = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { userProfile } = useAuth();
  const { toast } = useToast();

  // Quick action tags for common queries
  const quickActions = [
    "Time logs",
    "Projects",
    "Reports",
    "Analytics",
    "Help"
  ];

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('ai-assistant', {
        body: {
          message: input.trim(),
          userId: userProfile?.id,
          context: {
            role: userProfile?.role,
            department: userProfile?.department
          }
        }
      });

      if (error) throw error;

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: data.response,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error: any) {
      toast({
        title: "AI Assistant Error",
        description: error.message || "Failed to get response from AI assistant",
        variant: "destructive",
      });

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: "I'm sorry, I'm having trouble responding right now. Please try again later.",
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const handleQuickAction = (action: string) => {
    setInput(action);
  };

  return (
    <div className="container_chat_bot">
      {/* Enhanced Chat Container with CTNL AI Colors */}
      <div className="container-chat-options">
        <div className="chat">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <span className="text-white font-medium">CTNL AI Assistant</span>
            </div>
            <div className="h-2 w-2 rounded-full bg-green-400 animate-pulse"></div>
          </div>

          {/* Messages Area */}
          <ScrollArea className="flex-1 p-4 h-80">
            {messages.length === 0 ? (
              <div className="text-center text-gray-300 py-8">
                <div className="h-16 w-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                  <Bot className="h-8 w-8 text-white" />
                </div>
                <p className="text-sm">Hi! I'm your CTNL AI assistant.</p>
                <p className="text-xs text-gray-400 mt-1">Ask me about work, tasks, or company processes.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-3 ${
                      message.role === 'user' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    {message.role === 'assistant' && (
                      <div className="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center flex-shrink-0">
                        <Bot className="h-4 w-4 text-white" />
                      </div>
                    )}
                    <div
                      className={`max-w-[80%] p-3 rounded-lg ${
                        message.role === 'user'
                          ? 'bg-gradient-to-r from-green-600 to-green-700 text-white'
                          : 'bg-gray-700 text-gray-100 border border-gray-600'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                    {message.role === 'user' && (
                      <div className="h-8 w-8 rounded-full bg-gradient-to-r from-green-600 to-green-700 flex items-center justify-center flex-shrink-0">
                        <User className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>
                ))}
                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center">
                      <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="bg-gray-700 p-3 rounded-lg border border-gray-600">
                      <Loader className="h-4 w-4 animate-spin text-red-400" />
                    </div>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>

          {/* Enhanced Input Area */}
          <div className="chat-bot">
            <textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about CTNL AI Work-Board..."
              disabled={isLoading}
              className="w-full h-12 bg-transparent border-none text-white text-sm resize-none outline-none p-3"
              rows={1}
            />
          </div>

          {/* Options and Controls */}
          <div className="options">
            <div className="btns-add">
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1">
                <Paperclip className="h-4 w-4" />
              </button>
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1">
                <Image className="h-4 w-4" />
              </button>
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1">
                <FileText className="h-4 w-4" />
              </button>
              <button className="p-2 hover:text-white transition-all duration-300 hover:-translate-y-1">
                <Mic className="h-4 w-4" />
              </button>
            </div>

            <button
              onClick={sendMessage}
              disabled={!input.trim() || isLoading}
              className="btn-submit"
            >
              <Send className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Quick Action Tags */}
      <div className="tags">
        {quickActions.map((action, index) => (
          <span
            key={index}
            onClick={() => handleQuickAction(action)}
            className="hover:bg-red-600 hover:border-red-500 transition-all duration-200 cursor-pointer"
          >
            {action}
          </span>
        ))}
      </div>
    </div>
  );
};
