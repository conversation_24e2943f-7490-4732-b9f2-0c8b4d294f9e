import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, RotateCcw, Link, Plus } from 'lucide-react';
import { toast } from 'sonner';
import { ToolComponentProps } from '@/lib/tools/defineTool';

const TextJoiner: React.FC<ToolComponentProps> = ({ title }) => {
  const [inputText, setInputText] = useState('');
  const [separator, setSeparator] = useState(', ');
  const [prefix, setPrefix] = useState('');
  const [suffix, setSuffix] = useState('');
  const [removeEmpty, setRemoveEmpty] = useState(true);
  const [trimLines, setTrimLines] = useState(true);
  const [addNumbers, setAddNumbers] = useState(false);
  const [numberFormat, setNumberFormat] = useState('1. ');
  const [result, setResult] = useState('');

  const presetSeparators = [
    { value: ', ', label: 'Comma + Space (, )' },
    { value: ',', label: 'Comma (,)' },
    { value: '; ', label: 'Semicolon + Space (; )' },
    { value: ' | ', label: 'Pipe ( | )' },
    { value: '\n', label: 'New Line' },
    { value: ' ', label: 'Space' },
    { value: ' - ', label: 'Dash ( - )' },
    { value: ' + ', label: 'Plus ( + )' },
    { value: ' & ', label: 'Ampersand ( & )' },
    { value: 'custom', label: 'Custom' }
  ];

  const numberFormats = [
    { value: '1. ', label: '1. 2. 3.' },
    { value: '1) ', label: '1) 2) 3)' },
    { value: '(1) ', label: '(1) (2) (3)' },
    { value: '[1] ', label: '[1] [2] [3]' },
    { value: '1: ', label: '1: 2: 3:' },
    { value: '1 - ', label: '1 - 2 - 3 -' },
    { value: 'custom', label: 'Custom' }
  ];

  const performJoin = () => {
    if (!inputText.trim()) {
      setResult('');
      return;
    }

    let lines = inputText.split('\n');

    // Apply preprocessing
    if (trimLines) {
      lines = lines.map(line => line.trim());
    }

    if (removeEmpty) {
      lines = lines.filter(line => line.length > 0);
    }

    // Add numbering if enabled
    if (addNumbers) {
      lines = lines.map((line, index) => {
        let format = numberFormat;
        if (format === 'custom') {
          format = '1. '; // fallback
        }
        
        const number = index + 1;
        const formattedNumber = format.replace('1', number.toString());
        return formattedNumber + line;
      });
    }

    // Add prefix and suffix to each line
    if (prefix || suffix) {
      lines = lines.map(line => prefix + line + suffix);
    }

    // Join with separator
    const joinedText = lines.join(separator === '\n' ? '\n' : separator);
    setResult(joinedText);
  };

  useEffect(() => {
    performJoin();
  }, [inputText, separator, prefix, suffix, removeEmpty, trimLines, addNumbers, numberFormat]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const clearAll = () => {
    setInputText('');
    setResult('');
    setPrefix('');
    setSuffix('');
  };

  const loadSampleData = () => {
    const sampleData = `Apple
Banana
Cherry
Date
Elderberry`;
    setInputText(sampleData);
  };

  const getStats = () => {
    const lines = inputText.split('\n').filter(line => trimLines ? line.trim() : line);
    const processedLines = removeEmpty ? lines.filter(line => line.length > 0) : lines;
    
    return {
      originalLines: lines.length,
      processedLines: processedLines.length,
      resultLength: result.length,
      separatorCount: Math.max(0, processedLines.length - 1)
    };
  };

  const stats = getStats();

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-teal-50 via-cyan-50 to-blue-50 border-teal-200 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-3 text-teal-800">
            <div className="p-2 bg-teal-100 rounded-lg">
              <Link className="h-6 w-6 text-teal-600" />
            </div>
            {title || 'Text Joiner'}
          </CardTitle>
          <p className="text-teal-600 text-sm">
            Join multiple lines of text with custom separators and formatting
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Separator Configuration */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Separator:</label>
              <Select value={separator} onValueChange={setSeparator}>
                <SelectTrigger className="border-2 border-teal-200 focus:border-teal-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {presetSeparators.map(preset => (
                    <SelectItem key={preset.value} value={preset.value}>
                      {preset.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {separator === 'custom' && (
                <Input
                  placeholder="Enter custom separator..."
                  onChange={(e) => setSeparator(e.target.value)}
                  className="border-2 border-teal-200 focus:border-teal-400"
                />
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Numbering Format:</label>
              <Select value={numberFormat} onValueChange={setNumberFormat} disabled={!addNumbers}>
                <SelectTrigger className="border-2 border-teal-200 focus:border-teal-400">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {numberFormats.map(format => (
                    <SelectItem key={format.value} value={format.value}>
                      {format.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Prefix and Suffix */}
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Prefix (before each line):</label>
              <Input
                value={prefix}
                onChange={(e) => setPrefix(e.target.value)}
                placeholder="e.g., '- ' or '• '"
                className="border-2 border-teal-200 focus:border-teal-400"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-semibold text-gray-700">Suffix (after each line):</label>
              <Input
                value={suffix}
                onChange={(e) => setSuffix(e.target.value)}
                placeholder="e.g., ';' or '.'"
                className="border-2 border-teal-200 focus:border-teal-400"
              />
            </div>
          </div>

          {/* Options */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="remove-empty"
                checked={removeEmpty}
                onCheckedChange={setRemoveEmpty}
              />
              <label htmlFor="remove-empty" className="text-sm font-medium">
                Remove Empty
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="trim-lines"
                checked={trimLines}
                onCheckedChange={setTrimLines}
              />
              <label htmlFor="trim-lines" className="text-sm font-medium">
                Trim Lines
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="add-numbers"
                checked={addNumbers}
                onCheckedChange={setAddNumbers}
              />
              <label htmlFor="add-numbers" className="text-sm font-medium">
                Add Numbers
              </label>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSampleData}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Sample
            </Button>
          </div>

          {/* Input Text */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
              <span>📝</span>
              Lines to Join:
            </label>
            <Textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter each line of text on a separate line..."
              className="min-h-[140px] resize-none border-2 border-teal-200 focus:border-teal-400"
            />
          </div>

          {/* Stats */}
          {inputText && (
            <div className="flex gap-2 flex-wrap">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                📊 {stats.originalLines} → {stats.processedLines} lines
              </Badge>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                🔗 {stats.separatorCount} separators
              </Badge>
              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                📏 {stats.resultLength} chars
              </Badge>
            </div>
          )}

          {/* Result */}
          {result && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                  <span>✨</span>
                  Joined Result:
                </label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(result)}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              <Textarea
                value={result}
                readOnly
                className="min-h-[140px] bg-gray-50 border-2 border-gray-200 font-mono text-sm"
              />
            </div>
          )}

          {/* Clear Button */}
          <div className="flex justify-center">
            <Button variant="outline" onClick={clearAll}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-br from-gray-50 to-slate-50 border-gray-200">
        <CardHeader>
          <CardTitle className="text-gray-800 flex items-center gap-2">
            <span>💡</span>
            Usage Examples
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm text-gray-600">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <p><strong className="text-blue-600">Create Lists:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>Bullet points with "• " prefix</li>
                <li>Numbered lists with "1. " format</li>
                <li>HTML lists with "&lt;li&gt;" prefix/suffix</li>
                <li>CSV format with "," separator</li>
              </ul>
            </div>
            <div className="space-y-2">
              <p><strong className="text-green-600">Format Data:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-2">
                <li>SQL IN clauses with "', '" separator</li>
                <li>Array literals with ", " separator</li>
                <li>Command line arguments with " " separator</li>
                <li>Tag lists with " | " separator</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TextJoiner;
