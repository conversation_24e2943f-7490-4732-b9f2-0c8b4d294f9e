import { useEffect, useState, useRef } from 'react'

export const InfiniteRefreshDebug = () => {
  const [renderCount, setRenderCount] = useState(0)
  const [lastRenderTime, setLastRenderTime] = useState(Date.now())
  const startTimeRef = useRef(Date.now())

  useEffect(() => {
    setRenderCount(prev => prev + 1)
    setLastRenderTime(Date.now())

    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 Component rendered #${renderCount + 1} at ${new Date().toISOString()}`)

      // Check for rapid re-renders (more than 10 renders in 5 seconds)
      if (renderCount > 10) {
        const timeSinceStart = Date.now() - startTimeRef.current
        if (timeSinceStart < 5000) {
          console.warn('⚠️ Potential infinite refresh detected!', {
            renderCount,
            timeSinceStart,
            rendersPerSecond: renderCount / (timeSinceStart / 1000)
          })
        }
      }
    }
  }, [renderCount]) // Add dependency array to prevent infinite loop

  // Only render in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className='fixed bottom-4 right-4 bg-red-500 text-white p-2 rounded text-xs z-50'>
      Renders: {renderCount}
    </div>
  )
}
