import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseKey);

async function diagnoseAuthIssues() {
  console.log('🔍 Diagnosing Authentication Issues for ai.ctnigeria.com\n');
  console.log('=' .repeat(70));

  // Test 1: Basic Supabase Connection
  console.log('1️⃣ Testing Supabase Connection...');
  try {
    const { data, error } = await supabase.from('profiles').select('count', { count: 'exact', head: true });
    if (error) {
      console.log(`❌ Supabase connection failed: ${error.message}`);
      console.log(`   Error code: ${error.code}`);
      console.log(`   Error details: ${error.details}`);
    } else {
      console.log(`✅ Supabase connection successful`);
    }
  } catch (error: any) {
    console.log(`❌ Connection error: ${error.message}`);
  }

  // Test 2: Auth Configuration
  console.log('\n2️⃣ Testing Auth Configuration...');
  try {
    const { data: { session }, error } = await supabase.auth.getSession();
    if (error) {
      console.log(`❌ Auth session check failed: ${error.message}`);
    } else {
      console.log(`✅ Auth configuration working`);
      console.log(`   Current session: ${session ? 'Active' : 'None'}`);
    }
  } catch (error: any) {
    console.log(`❌ Auth config error: ${error.message}`);
  }

  // Test 3: Test User Authentication
  console.log('\n3️⃣ Testing User Authentication...');
  try {
    // Try to sign in with a test user (this will fail but shows if auth endpoint works)
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword123'
    });
    
    if (error) {
      if (error.message.includes('Invalid login credentials')) {
        console.log(`✅ Auth endpoint working (invalid credentials expected)`);
      } else {
        console.log(`❌ Auth endpoint error: ${error.message}`);
      }
    } else {
      console.log(`⚠️  Unexpected successful login with test credentials`);
    }
  } catch (error: any) {
    console.log(`❌ Auth test error: ${error.message}`);
  }

  // Test 4: Check Profiles Table Structure
  console.log('\n4️⃣ Testing Profiles Table...');
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, email, role, full_name')
      .limit(1);
    
    if (error) {
      console.log(`❌ Profiles table error: ${error.message}`);
      console.log(`   This could cause profile fetch failures during auth`);
    } else {
      console.log(`✅ Profiles table accessible`);
      console.log(`   Sample profiles: ${profiles?.length || 0} found`);
      if (profiles && profiles.length > 0) {
        console.log(`   Sample profile structure:`, Object.keys(profiles[0]));
      }
    }
  } catch (error: any) {
    console.log(`❌ Profiles test error: ${error.message}`);
  }

  // Test 5: Check RLS Policies
  console.log('\n5️⃣ Testing RLS Policies...');
  try {
    // Test if we can access profiles without authentication
    const { data: publicProfiles, error: publicError } = await supabase
      .from('profiles')
      .select('count', { count: 'exact', head: true });
    
    if (publicError) {
      if (publicError.message.includes('row-level security')) {
        console.log(`✅ RLS policies are active (expected for security)`);
      } else {
        console.log(`❌ RLS policy error: ${publicError.message}`);
      }
    } else {
      console.log(`⚠️  Profiles table accessible without auth (potential security issue)`);
    }
  } catch (error: any) {
    console.log(`❌ RLS test error: ${error.message}`);
  }

  // Test 6: Check for Common Auth Issues
  console.log('\n6️⃣ Checking Common Auth Issues...');
  
  // Check if URL is correct
  console.log(`   Supabase URL: ${supabaseUrl}`);
  console.log(`   Key length: ${supabaseKey.length} characters`);
  
  // Check for CORS issues (this would show in browser console)
  console.log(`   CORS: Check browser console for CORS errors`);
  
  // Check for localStorage issues
  try {
    localStorage.setItem('test', 'value');
    localStorage.removeItem('test');
    console.log(`   ✅ localStorage working`);
  } catch (error) {
    console.log(`   ❌ localStorage error: ${error}`);
  }

  // Test 7: Production-Specific Checks
  console.log('\n7️⃣ Production Environment Checks...');
  
  console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`   User Agent: ${typeof navigator !== 'undefined' ? navigator.userAgent : 'Node.js'}`);
  
  // Check if running in production domain
  if (typeof window !== 'undefined') {
    console.log(`   Current domain: ${window.location.hostname}`);
    console.log(`   Protocol: ${window.location.protocol}`);
    
    if (window.location.hostname === 'ai.ctnigeria.com') {
      console.log(`   ✅ Running on production domain`);
    } else {
      console.log(`   ⚠️  Not running on production domain`);
    }
  }

  // Test 8: Check for Infinite Loop Conditions
  console.log('\n8️⃣ Checking for Infinite Loop Conditions...');
  
  console.log(`   Potential causes of infinite loading:`);
  console.log(`   • Profile fetch failing and retrying`);
  console.log(`   • Auth state change listener not resolving`);
  console.log(`   • Session check hanging`);
  console.log(`   • Network timeout issues`);
  console.log(`   • CORS blocking requests`);
  console.log(`   • RLS policies preventing profile access`);

  // Test 9: Simulate Auth Flow
  console.log('\n9️⃣ Simulating Auth Flow...');
  
  try {
    console.log(`   Step 1: Getting initial session...`);
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.log(`   ❌ Session check failed: ${sessionError.message}`);
    } else {
      console.log(`   ✅ Session check completed`);
      
      if (session) {
        console.log(`   Step 2: Fetching user profile...`);
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();
        
        if (profileError) {
          console.log(`   ❌ Profile fetch failed: ${profileError.message}`);
          console.log(`   This is likely causing the infinite loading!`);
        } else {
          console.log(`   ✅ Profile fetch successful`);
        }
      } else {
        console.log(`   No active session (user not logged in)`);
      }
    }
  } catch (error: any) {
    console.log(`   ❌ Auth flow simulation failed: ${error.message}`);
  }

  console.log('\n' + '=' .repeat(70));
  console.log('🏁 Diagnosis Complete');
  
  console.log('\n💡 RECOMMENDATIONS:');
  console.log('1. Check browser console for CORS errors');
  console.log('2. Verify Supabase project settings allow ai.ctnigeria.com');
  console.log('3. Check if profile fetch is failing due to RLS policies');
  console.log('4. Ensure auth state listener is properly cleaning up');
  console.log('5. Add timeout handling to auth operations');
  console.log('6. Check network connectivity and DNS resolution');
}

// Run diagnosis
diagnoseAuthIssues().catch((error) => {
  console.error('💥 Diagnosis failed:', error);
  process.exit(1);
});
