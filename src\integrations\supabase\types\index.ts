
import { CoreTables } from './core';
import { ReportsTable } from './reports';
import { MemosTable } from './memos';
import { TasksTable } from './tasks';
import { AIResultsRow, AIKnowledgeBaseRow } from './ai';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: CoreTables & {
      reports: ReportsTable;
      memos: MemosTable;
      tasks: TasksTable;
      departments: {
        Row: {
          id: string
          name: string
          description: string | null
          manager_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          manager_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "departments_manager_id_fkey"
            columns: ["manager_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      project_assignments: {
        Row: {
          id: string
          project_name: string
          description: string | null
          assigned_to: string
          department_id: string
          start_date: string
          end_date: string | null
          status: "pending" | "in_progress" | "completed" | "cancelled"
          priority: "low" | "medium" | "high"
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_name: string
          description?: string | null
          assigned_to: string
          department_id: string
          start_date: string
          end_date?: string | null
          status?: "pending" | "in_progress" | "completed" | "cancelled"
          priority?: "low" | "medium" | "high"
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_name?: string
          description?: string | null
          assigned_to?: string
          department_id?: string
          start_date?: string
          end_date?: string | null
          status?: "pending" | "in_progress" | "completed" | "cancelled"
          priority?: "low" | "medium" | "high"
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_assignments_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_assignments_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          }
        ]
      }
      document_archive: {
        Row: {
          id: string
          title: string
          description: string | null
          file_path: string
          file_type: string
          file_size: number
          uploaded_by: string
          department_id: string | null
          tags: string[] | null
          is_archived: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          description?: string | null
          file_path: string
          file_type: string
          file_size: number
          uploaded_by: string
          department_id?: string | null
          tags?: string[] | null
          is_archived?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          description?: string | null
          file_path?: string
          file_type?: string
          file_size?: number
          uploaded_by?: string
          department_id?: string | null
          tags?: string[] | null
          is_archived?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "document_archive_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "document_archive_uploaded_by_fkey"
            columns: ["uploaded_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_results: {
        Row: {
          id: string
          query_text: string
          result_data: Json
          model_used: string
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          query_text: string
          result_data?: Json
          model_used?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          query_text?: string
          result_data?: Json
          model_used?: string
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_results_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_documents: {
        Row: {
          id: string
          title: string
          content: string
          document_type: string | null
          category: string | null
          tags: string[] | null
          analysis: Json
          embedding: string | null
          indexed_at: string | null
          index_version: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          content: string
          document_type?: string | null
          category?: string | null
          tags?: string[] | null
          analysis?: Json
          embedding?: string | null
          indexed_at?: string | null
          index_version?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          content?: string
          document_type?: string | null
          category?: string | null
          tags?: string[] | null
          analysis?: Json
          embedding?: string | null
          indexed_at?: string | null
          index_version?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_documents_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      ai_interactions: {
        Row: {
          id: string
          user_id: string | null
          role: string
          message: string
          type: string | null
          query: string | null
          response: string | null
          actions: Json[]
          metadata: Json
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          role: string
          message: string
          type?: string | null
          query?: string | null
          response?: string | null
          actions?: Json[]
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          role?: string
          message?: string
          type?: string | null
          query?: string | null
          response?: string | null
          actions?: Json[]
          metadata?: Json
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_interactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
