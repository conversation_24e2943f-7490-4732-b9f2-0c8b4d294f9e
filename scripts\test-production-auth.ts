import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://dvflgnqwbsjityrowatf.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28';

const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'pkce'
  }
});

async function testProductionAuth() {
  console.log('🔧 Testing Production Authentication System\n');
  console.log('=' .repeat(70));

  // Test 1: Basic connectivity
  console.log('1️⃣ Testing Supabase connectivity...');
  try {
    const startTime = Date.now();
    const { data, error } = await supabase.from('profiles').select('count', { count: 'exact', head: true });
    const endTime = Date.now();
    
    if (error) {
      console.log(`❌ Connection failed: ${error.message}`);
      console.log(`   Error code: ${error.code}`);
    } else {
      console.log(`✅ Connection successful (${endTime - startTime}ms)`);
    }
  } catch (error: any) {
    console.log(`❌ Network error: ${error.message}`);
  }

  // Test 2: Auth session handling
  console.log('\n2️⃣ Testing auth session handling...');
  try {
    const startTime = Date.now();
    const { data: { session }, error } = await supabase.auth.getSession();
    const endTime = Date.now();
    
    if (error) {
      console.log(`❌ Session check failed: ${error.message}`);
    } else {
      console.log(`✅ Session check successful (${endTime - startTime}ms)`);
      console.log(`   Session status: ${session ? 'Active' : 'None'}`);
    }
  } catch (error: any) {
    console.log(`❌ Session error: ${error.message}`);
  }

  // Test 3: Profile table access
  console.log('\n3️⃣ Testing profile table access...');
  try {
    const startTime = Date.now();
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, email, role, full_name')
      .limit(1);
    const endTime = Date.now();
    
    if (error) {
      console.log(`❌ Profile access failed: ${error.message}`);
      if (error.message.includes('row-level security')) {
        console.log(`   ℹ️  This is expected for unauthenticated users`);
      }
    } else {
      console.log(`✅ Profile access successful (${endTime - startTime}ms)`);
      console.log(`   Profiles found: ${profiles?.length || 0}`);
    }
  } catch (error: any) {
    console.log(`❌ Profile test error: ${error.message}`);
  }

  // Test 4: Auth endpoint responsiveness
  console.log('\n4️⃣ Testing auth endpoint responsiveness...');
  try {
    const startTime = Date.now();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'testpassword'
    });
    const endTime = Date.now();
    
    if (error) {
      if (error.message.includes('Invalid login credentials')) {
        console.log(`✅ Auth endpoint responsive (${endTime - startTime}ms)`);
        console.log(`   Expected error: ${error.message}`);
      } else {
        console.log(`❌ Unexpected auth error: ${error.message}`);
      }
    } else {
      console.log(`⚠️  Unexpected successful login with test credentials`);
    }
  } catch (error: any) {
    console.log(`❌ Auth endpoint error: ${error.message}`);
  }

  // Test 5: Performance benchmarks
  console.log('\n5️⃣ Performance benchmarks...');
  
  const performanceTests = [
    { name: 'Session check', operation: () => supabase.auth.getSession() },
    { name: 'Profile count', operation: () => supabase.from('profiles').select('count', { count: 'exact', head: true }) },
    { name: 'Auth test', operation: () => supabase.auth.signInWithPassword({ email: '<EMAIL>', password: 'test' }) }
  ];

  for (const test of performanceTests) {
    try {
      const startTime = Date.now();
      await test.operation();
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (duration < 1000) {
        console.log(`   ✅ ${test.name}: ${duration}ms (Good)`);
      } else if (duration < 3000) {
        console.log(`   ⚠️  ${test.name}: ${duration}ms (Slow)`);
      } else {
        console.log(`   ❌ ${test.name}: ${duration}ms (Too slow)`);
      }
    } catch (error: any) {
      console.log(`   ❌ ${test.name}: Failed (${error.message})`);
    }
  }

  // Test 6: Domain and CORS check
  console.log('\n6️⃣ Domain and CORS configuration...');
  console.log(`   Expected domain: ai.ctnigeria.com`);
  console.log(`   Supabase URL: ${supabaseUrl}`);
  console.log(`   API Key length: ${supabaseKey.length} characters`);
  
  // Test 7: Recommendations
  console.log('\n7️⃣ Recommendations for production...');
  console.log('   📋 Checklist:');
  console.log('   • Ensure ai.ctnigeria.com is added to Supabase allowed origins');
  console.log('   • Verify HTTPS is enabled for production domain');
  console.log('   • Check browser console for CORS errors');
  console.log('   • Ensure localStorage is available in production');
  console.log('   • Verify network connectivity and DNS resolution');
  console.log('   • Check if any browser extensions are blocking requests');

  console.log('\n' + '=' .repeat(70));
  console.log('🏁 Production Auth Test Complete');
  
  console.log('\n🔧 FIXES APPLIED:');
  console.log('✅ Added timeout handling to prevent infinite loading');
  console.log('✅ Added fallback profiles for failed profile fetches');
  console.log('✅ Added safe localStorage operations');
  console.log('✅ Added production-specific error handling');
  console.log('✅ Added 15-second safety timeout for auth initialization');
  console.log('✅ Added better CORS and domain handling');

  console.log('\n🚀 NEXT STEPS:');
  console.log('1. Deploy the updated code to production');
  console.log('2. Test sign-in on ai.ctnigeria.com');
  console.log('3. Check browser console for any remaining errors');
  console.log('4. Monitor auth performance and loading times');
}

// Run the test
testProductionAuth().catch((error) => {
  console.error('💥 Production auth test failed:', error);
  process.exit(1);
});
