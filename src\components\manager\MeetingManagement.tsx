
import { useAuth } from "@/components/auth/AuthProvider";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Calendar, Clock, Plus, Users, Video } from "lucide-react";
import { useEffect, useState } from "react";

interface Meeting {
  id: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  location?: string;
  meeting_type: string;
  status: string;
  organizer_id: string;
  meeting_url?: string;
}

interface MeetingParticipant {
  id: string;
  meeting_id: string;
  participant_id: string;
  response: string;
  attendance_status: string;
}

export const MeetingManagement = () => {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();
  const { userProfile } = useAuth();

  const fetchMeetings = async () => {
    try {
      // First try zoom_meetings table
      const { data: zoomData, error: zoomError } = await supabase
        .from('zoom_meetings')
        .select(`
          id,
          topic as title,
          agenda as description,
          start_time,
          duration,
          join_url as meeting_url,
          status,
          host_id as organizer_id,
          host:profiles!host_id(id, full_name, email)
        `)
        .order('start_time', { ascending: true });

      if (!zoomError && zoomData) {
        // Transform zoom meetings to match expected format
        const transformedMeetings = zoomData.map(meeting => ({
          ...meeting,
          end_time: new Date(new Date(meeting.start_time).getTime() + (meeting.duration * 60000)).toISOString(),
          meeting_type: 'zoom',
          location: 'Online'
        }));
        setMeetings(transformedMeetings);
        return;
      }

      // Fallback to regular meetings table
      const { data: meetingsData, error: meetingsError } = await supabase
        .from('meetings')
        .select(`
          id,
          title,
          description,
          start_time,
          end_time,
          meeting_url,
          status,
          organizer_id,
          meeting_type,
          location,
          organizer:profiles!organizer_id(id, full_name, email)
        `)
        .order('start_time', { ascending: true });

      if (meetingsError) throw meetingsError;

      setMeetings(meetingsData || []);
    } catch (error) {
      console.error('Error fetching meetings:', error);
      toast({
        title: "Error",
        description: "Failed to fetch meetings. Please check your connection.",
        variant: "destructive",
      });
      setMeetings([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMeetings();
  }, []);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    try {
      const startTime = formData.get('start_time') as string;
      const endTime = formData.get('end_time') as string;

      // Calculate duration in minutes
      const duration = endTime ?
        Math.round((new Date(endTime).getTime() - new Date(startTime).getTime()) / (1000 * 60)) :
        60;

      const meetingData = {
        topic: formData.get('title') as string,
        agenda: formData.get('description') as string,
        start_time: startTime,
        duration: duration,
        join_url: formData.get('meeting_url') as string || `https://zoom.us/j/${Math.random().toString(36).substr(2, 9)}`,
        host_id: userProfile?.id,
        status: 'scheduled'
      };

      const { error } = await supabase
        .from('zoom_meetings')
        .insert([meetingData]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Meeting scheduled successfully",
      });

      setIsDialogOpen(false);
      fetchMeetings();
    } catch (error) {
      console.error('Error scheduling meeting:', error);
      toast({
        title: "Error",
        description: "Failed to schedule meeting",
        variant: "destructive",
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-500/20 text-blue-500';
      case 'in_progress':
        return 'bg-green-500/20 text-green-500';
      case 'completed':
        return 'bg-gray-500/20 text-gray-500';
      case 'cancelled':
        return 'bg-red-500/20 text-red-500';
      default:
        return 'bg-gray-500/20 text-gray-500';
    }
  };

  const upcomingMeetings = meetings.filter(meeting => 
    new Date(meeting.start_time) > new Date() && meeting.status === 'scheduled'
  ).length;

  const todaysMeetings = meetings.filter(meeting => {
    const today = new Date();
    const meetingDate = new Date(meeting.start_time);
    return meetingDate.toDateString() === today.toDateString();
  }).length;

  if (loading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold">Meeting Management</h2>
        <p className="text-muted-foreground">Loading meetings...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Meeting Management</h2>
          <p className="text-muted-foreground">Schedule and manage team meetings</p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Meeting
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Schedule New Meeting</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="text-sm font-medium">Meeting Title *</label>
                <Input name="title" required />
              </div>
              
              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea name="description" rows={3} />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Start Time *</label>
                  <Input name="start_time" type="datetime-local" required />
                </div>
                <div>
                  <label className="text-sm font-medium">End Time *</label>
                  <Input name="end_time" type="datetime-local" required />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Meeting Type</label>
                  <Select name="meeting_type">
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General</SelectItem>
                      <SelectItem value="project">Project</SelectItem>
                      <SelectItem value="department">Department</SelectItem>
                      <SelectItem value="one_on_one">One-on-One</SelectItem>
                      <SelectItem value="training">Training</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Location</label>
                  <Input name="location" placeholder="Conference Room A or Online" />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Meeting URL (for online meetings)</label>
                <Input name="meeting_url" type="url" placeholder="https://zoom.us/j/..." />
              </div>

              <Button type="submit" className="w-full">Schedule Meeting</Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Calendar className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Meetings</p>
              <p className="text-2xl font-bold">{meetings.length}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Clock className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Today's Meetings</p>
              <p className="text-2xl font-bold">{todaysMeetings}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Upcoming</p>
              <p className="text-2xl font-bold">{upcomingMeetings}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Video className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Online Meetings</p>
              <p className="text-2xl font-bold">
                {meetings.filter(m => m.meeting_url).length}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Meetings List */}
      <Card>
        <CardHeader>
          <CardTitle>Scheduled Meetings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {meetings.map((meeting) => (
              <div key={meeting.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{meeting.title}</h4>
                  <p className="text-sm text-muted-foreground">{meeting.description}</p>
                  <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(meeting.start_time).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {new Date(meeting.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - 
                      {new Date(meeting.end_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                    {meeting.location && (
                      <span>{meeting.location}</span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${getStatusColor(meeting.status)}`}
                  >
                    {meeting.status}
                  </span>
                  <p className="text-sm text-muted-foreground mt-1">{meeting.meeting_type}</p>
                  {meeting.meeting_url && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => window.open(meeting.meeting_url, '_blank')}
                    >
                      <Video className="h-3 w-3 mr-1" />
                      Join
                    </Button>
                  )}
                </div>
              </div>
            ))}
            {meetings.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No meetings scheduled. Schedule your first meeting to get started.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
