
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Activity, 
  Bell, 
  Clock, 
  Users, 
  FileText, 
  Database,
  Zap,
  RefreshCw
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";

interface RealtimeUpdate {
  id: string;
  type: 'task_created' | 'project_updated' | 'document_generated' | 'system_activity';
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  severity: 'low' | 'medium' | 'high';
}

export const AIRealtimeUpdates = () => {
  const [updates, setUpdates] = useState<RealtimeUpdate[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const { userPro<PERSON>le } = useAuth();

  useEffect(() => {
    if (!userProfile) return;

    // Subscribe to real-time updates
    const channel = supabase
      .channel('system-updates')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'activity_logs'
        },
        (payload) => {
          const newUpdate: RealtimeUpdate = {
            id: payload.new.id,
            type: getUpdateType(payload.new.action),
            title: payload.new.action,
            description: payload.new.description || 'System activity recorded',
            timestamp: new Date(payload.new.created_at),
            user: payload.new.user_id,
            severity: getSeverity(payload.new.action)
          };
          
          setUpdates(prev => [newUpdate, ...prev.slice(0, 49)]); // Keep latest 50
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'projects'
        },
        (payload) => {
          const newUpdate: RealtimeUpdate = {
            id: payload.new.id,
            type: 'project_updated',
            title: 'New Project Created',
            description: `Project "${payload.new.name}" has been created`,
            timestamp: new Date(payload.new.created_at),
            severity: 'medium'
          };
          
          setUpdates(prev => [newUpdate, ...prev.slice(0, 49)]);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'tasks'
        },
        (payload) => {
          const newUpdate: RealtimeUpdate = {
            id: payload.new.id,
            type: 'task_created',
            title: 'New Task Assigned',
            description: `Task "${payload.new.title}" has been created`,
            timestamp: new Date(payload.new.created_at),
            severity: 'low'
          };
          
          setUpdates(prev => [newUpdate, ...prev.slice(0, 49)]);
        }
      )
      .subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [userProfile]);

  const getUpdateType = (action: string): RealtimeUpdate['type'] => {
    if (action.includes('task')) return 'task_created';
    if (action.includes('project')) return 'project_updated';
    if (action.includes('document')) return 'document_generated';
    return 'system_activity';
  };

  const getSeverity = (action: string): RealtimeUpdate['severity'] => {
    if (action.includes('error') || action.includes('failed')) return 'high';
    if (action.includes('created') || action.includes('updated')) return 'medium';
    return 'low';
  };

  const getIcon = (type: RealtimeUpdate['type']) => {
    switch (type) {
      case 'task_created': return <Clock className="h-4 w-4" />;
      case 'project_updated': return <Users className="h-4 w-4" />;
      case 'document_generated': return <FileText className="h-4 w-4" />;
      case 'system_activity': return <Database className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: RealtimeUpdate['severity']) => {
    switch (severity) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const refreshUpdates = async () => {
    try {
      const { data: activities, error } = await supabase
        .from('activity_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      const formattedUpdates: RealtimeUpdate[] = (activities || []).map(activity => ({
        id: activity.id,
        type: getUpdateType(activity.action),
        title: activity.action,
        description: activity.description || 'System activity recorded',
        timestamp: new Date(activity.created_at),
        user: activity.user_id,
        severity: getSeverity(activity.action)
      }));

      setUpdates(formattedUpdates);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error refreshing updates:', error);
      }
    }
  };

  useEffect(() => {
    refreshUpdates();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Real-time System Updates</h2>
          <p className="text-muted-foreground">Live activity feed and system notifications</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isConnected ? 'default' : 'destructive'} className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            {isConnected ? 'Connected' : 'Disconnected'}
          </Badge>
          <Button variant="outline" size="sm" onClick={refreshUpdates}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardContent className="flex items-center p-6">
            <Activity className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Activities</p>
              <p className="text-2xl font-bold">{updates.length}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Bell className="h-8 w-8 text-orange-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">High Priority</p>
              <p className="text-2xl font-bold">{updates.filter(u => u.severity === 'high').length}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <Zap className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Real-time Status</p>
              <p className="text-2xl font-bold">{isConnected ? 'Active' : 'Inactive'}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Live Activity Feed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px]">
            {updates.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Activity className="h-16 w-16 mx-auto mb-4 opacity-50" />
                <p>No recent activity</p>
                <p className="text-sm">Activity will appear here in real-time</p>
              </div>
            ) : (
              <div className="space-y-4">
                {updates.map((update) => (
                  <div key={update.id} className="flex items-start gap-3 p-3 rounded-lg border">
                    <div className="mt-1">
                      {getIcon(update.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium truncate">{update.title}</h4>
                        <Badge variant={getSeverityColor(update.severity)} className="text-xs">
                          {update.severity}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {update.description}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {update.timestamp.toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};
