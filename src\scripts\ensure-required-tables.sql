-- Ensure all required tables exist for the application
-- This script creates missing tables that are referenced in the application

-- Create reports table for unified report submission
CREATE TABLE IF NOT EXISTS public.reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    report_type TEXT NOT NULL CHECK (report_type IN ('general', 'weekly', 'monthly', 'project', 'incident', 'maintenance', 'financial', 'safety', 'quality')),
    priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'completed')),
    submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    reviewed_by UUID REFERENCES public.profiles(id),
    department_id UUID REFERENCES public.departments(id),
    project_id UUID REFERENCES public.projects(id),
    due_date DATE,
    completed_date DATE,
    metadata JSONB DEFAULT '{}',
    attachments JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create invoices table for financial management
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT UNIQUE NOT NULL,
    client_name TEXT NOT NULL,
    client_email TEXT,
    client_address TEXT,
    amount DECIMAL NOT NULL,
    total_amount DECIMAL NOT NULL,
    tax_amount DECIMAL DEFAULT 0,
    discount_amount DECIMAL DEFAULT 0,
    description TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'overdue', 'cancelled')),
    payment_method TEXT,
    payment_date DATE,
    due_date DATE,
    created_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create expense_reports table for expense management
CREATE TABLE IF NOT EXISTS public.expense_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    amount DECIMAL NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT,
    expense_date DATE NOT NULL,
    receipt_url TEXT,
    status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'reimbursed')),
    approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    approved_by UUID REFERENCES public.profiles(id),
    department_id UUID REFERENCES public.departments(id),
    project_id UUID REFERENCES public.projects(id),
    reimbursement_date DATE,
    notes TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create budgets table for budget management
CREATE TABLE IF NOT EXISTS public.budgets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    allocated_amount DECIMAL NOT NULL,
    spent_amount DECIMAL DEFAULT 0,
    remaining_amount DECIMAL GENERATED ALWAYS AS (allocated_amount - spent_amount) STORED,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'completed', 'cancelled')),
    department_id UUID REFERENCES public.departments(id),
    created_by UUID REFERENCES public.profiles(id),
    approved_by UUID REFERENCES public.profiles(id),
    approval_date DATE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create financial_reports table for financial reporting
CREATE TABLE IF NOT EXISTS public.financial_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    report_name TEXT NOT NULL,
    report_type TEXT NOT NULL CHECK (report_type IN ('comprehensive', 'income_statement', 'balance_sheet', 'cash_flow', 'budget_analysis')),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    report_data JSONB NOT NULL,
    generated_by UUID REFERENCES public.profiles(id),
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'completed', 'published', 'archived')),
    file_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create accounts_invoices table for accounts payable
CREATE TABLE IF NOT EXISTS public.accounts_invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT UNIQUE NOT NULL,
    vendor_name TEXT NOT NULL,
    vendor_email TEXT,
    client_name TEXT,
    amount DECIMAL NOT NULL,
    total_amount DECIMAL NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'paid', 'rejected', 'cancelled')),
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'partial', 'overdue')),
    due_date DATE,
    payment_date DATE,
    created_by UUID REFERENCES public.profiles(id),
    approved_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create battery_reports table for battery management
CREATE TABLE IF NOT EXISTS public.battery_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    battery_id UUID NOT NULL,
    charge_level INTEGER NOT NULL CHECK (charge_level >= 0 AND charge_level <= 100),
    voltage_reading DECIMAL,
    temperature DECIMAL,
    status TEXT NOT NULL CHECK (status IN ('excellent', 'good', 'fair', 'poor', 'critical')),
    maintenance_notes TEXT,
    issues_found TEXT,
    corrective_actions TEXT,
    reported_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    report_date DATE NOT NULL,
    approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
    approved_by UUID REFERENCES public.profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create construction_reports table for construction reporting
CREATE TABLE IF NOT EXISTS public.construction_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    report_title TEXT NOT NULL,
    report_type TEXT NOT NULL,
    site_id UUID,
    work_performed TEXT,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    issues_encountered TEXT,
    next_steps TEXT,
    weather_conditions TEXT,
    safety_incidents TEXT,
    materials_used TEXT,
    labor_hours INTEGER DEFAULT 0,
    budget_spent DECIMAL DEFAULT 0,
    quality_rating INTEGER CHECK (quality_rating >= 1 AND quality_rating <= 5),
    created_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    approved_by UUID REFERENCES public.profiles(id),
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'approved', 'rejected')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.accounts_invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.battery_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.construction_reports ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for reports
CREATE POLICY "Users can view own reports" ON public.reports FOR SELECT USING (auth.uid() = submitted_by);
CREATE POLICY "Users can create reports" ON public.reports FOR INSERT WITH CHECK (auth.uid() = submitted_by);
CREATE POLICY "Managers can view all reports" ON public.reports FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

-- Create RLS policies for invoices
CREATE POLICY "Accountants can manage invoices" ON public.invoices FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
    )
);

-- Create RLS policies for expense reports
CREATE POLICY "Users can view own expense reports" ON public.expense_reports FOR SELECT USING (auth.uid() = submitted_by);
CREATE POLICY "Users can create expense reports" ON public.expense_reports FOR INSERT WITH CHECK (auth.uid() = submitted_by);
CREATE POLICY "Managers can view all expense reports" ON public.expense_reports FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant')
    )
);

-- Create RLS policies for budgets
CREATE POLICY "Accountants can manage budgets" ON public.budgets FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
    )
);

-- Create RLS policies for financial reports
CREATE POLICY "Accountants can manage financial reports" ON public.financial_reports FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
    )
);

-- Create RLS policies for accounts invoices
CREATE POLICY "Accountants can manage accounts invoices" ON public.accounts_invoices FOR ALL USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
    )
);

-- Create RLS policies for battery reports
CREATE POLICY "Users can view own battery reports" ON public.battery_reports FOR SELECT USING (auth.uid() = reported_by);
CREATE POLICY "Users can create battery reports" ON public.battery_reports FOR INSERT WITH CHECK (auth.uid() = reported_by);
CREATE POLICY "Managers can view all battery reports" ON public.battery_reports FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

-- Create RLS policies for construction reports
CREATE POLICY "Users can view own construction reports" ON public.construction_reports FOR SELECT USING (auth.uid() = created_by);
CREATE POLICY "Users can create construction reports" ON public.construction_reports FOR INSERT WITH CHECK (auth.uid() = created_by);
CREATE POLICY "Managers can view all construction reports" ON public.construction_reports FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reports_submitted_by ON public.reports(submitted_by);
CREATE INDEX IF NOT EXISTS idx_reports_status ON public.reports(status);
CREATE INDEX IF NOT EXISTS idx_reports_report_type ON public.reports(report_type);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON public.invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON public.invoices(payment_status);
CREATE INDEX IF NOT EXISTS idx_expense_reports_submitted_by ON public.expense_reports(submitted_by);
CREATE INDEX IF NOT EXISTS idx_expense_reports_status ON public.expense_reports(status);
CREATE INDEX IF NOT EXISTS idx_budgets_department_id ON public.budgets(department_id);
CREATE INDEX IF NOT EXISTS idx_budgets_status ON public.budgets(status);
CREATE INDEX IF NOT EXISTS idx_battery_reports_reported_by ON public.battery_reports(reported_by);
CREATE INDEX IF NOT EXISTS idx_construction_reports_created_by ON public.construction_reports(created_by);
