
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User, Settings, Shield, Building2, Mail, Phone, MapPin } from 'lucide-react';
import { useAuth } from '@/components/auth/AuthProvider';
import { useState } from 'react';
import { useUserManagement } from '@/hooks/useUserManagement';
import { useToast } from '@/hooks/use-toast';

export const AccountDashboard = () => {
  const { userProfile, updateProfile } = useAuth();
  const { departments } = useUserManagement();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    full_name: userProfile?.full_name || '',
    email: userProfile?.email || '',
    phone: userProfile?.phone || '',
    position: userProfile?.position || '',
    location: userProfile?.location || '',
    bio: userProfile?.bio || ''
  });

  const handleSave = async () => {
    const result = await updateProfile(formData);
    if (!result.error) {
      setIsEditing(false);
      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully.",
      });
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800';
      case 'manager': return 'bg-blue-100 text-blue-800';
      case 'staff': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Account Dashboard</h1>
        <Button
          variant={isEditing ? "default" : "outline"}
          onClick={isEditing ? handleSave : () => setIsEditing(true)}
        >
          <Settings className="h-4 w-4 mr-2" />
          {isEditing ? 'Save Changes' : 'Edit Profile'}
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={userProfile?.avatar_url} />
                <AvatarFallback className="text-lg">
                  {userProfile?.full_name?.charAt(0).toUpperCase() || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="space-y-2">
                <h3 className="text-xl font-semibold">{userProfile?.full_name}</h3>
                <div className="flex gap-2">
                  <Badge className={getRoleBadgeColor(userProfile?.role || '')}>
                    <Shield className="h-3 w-3 mr-1" />
                    {userProfile?.role}
                  </Badge>
                  {userProfile?.department && (
                    <Badge variant="outline">
                      <Building2 className="h-3 w-3 mr-1" />
                      {userProfile.department}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {isEditing ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="full_name">Full Name</Label>
                  <Input
                    id="full_name"
                    value={formData.full_name}
                    onChange={(e) => setFormData({ ...formData, full_name: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="position">Position</Label>
                  <Input
                    id="position"
                    value={formData.position}
                    onChange={(e) => setFormData({ ...formData, position: e.target.value })}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{userProfile?.email}</span>
                </div>
                {userProfile?.phone && (
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span>{userProfile.phone}</span>
                  </div>
                )}
                {userProfile?.location && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>{userProfile.location}</span>
                  </div>
                )}
                {userProfile?.position && (
                  <div className="text-sm">
                    <strong>Position:</strong> {userProfile.position}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Account Security
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Account Status</Label>
              <Badge variant="outline" className="bg-green-50 text-green-700">
                Active
              </Badge>
            </div>
            
            <div className="space-y-2">
              <Label>Role Permissions</Label>
              <div className="text-sm text-muted-foreground">
                {userProfile?.role === 'admin' && (
                  <ul className="list-disc list-inside space-y-1">
                    <li>Full system access</li>
                    <li>User management</li>
                    <li>Department management</li>
                    <li>All task operations</li>
                  </ul>
                )}
                {userProfile?.role === 'manager' && (
                  <ul className="list-disc list-inside space-y-1">
                    <li>Department task management</li>
                    <li>Team oversight</li>
                    <li>Task assignment</li>
                    <li>Report generation</li>
                  </ul>
                )}
                {userProfile?.role === 'staff' && (
                  <ul className="list-disc list-inside space-y-1">
                    <li>Task management</li>
                    <li>Report submission</li>
                    <li>Profile updates</li>
                  </ul>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Department Access</Label>
              <div className="text-sm">
                {userProfile?.department || 'No department assigned'}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <Button variant="outline" className="h-auto p-4">
              <div className="text-center">
                <Settings className="h-8 w-8 mx-auto mb-2" />
                <div className="font-medium">Change Password</div>
                <div className="text-xs text-muted-foreground">Update security</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4">
              <div className="text-center">
                <User className="h-8 w-8 mx-auto mb-2" />
                <div className="font-medium">Update Avatar</div>
                <div className="text-xs text-muted-foreground">Change profile picture</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4">
              <div className="text-center">
                <Shield className="h-8 w-8 mx-auto mb-2" />
                <div className="font-medium">Privacy Settings</div>
                <div className="text-xs text-muted-foreground">Manage data</div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
