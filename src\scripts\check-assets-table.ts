import { supabase } from '../integrations/supabase/client';

async function checkAssetsTable() {
  try {
    console.log('🔍 Checking assets_inventory table...');
    
    // Check if table exists by trying to query it
    const { data: tableData, error: tableError } = await supabase
      .from('assets_inventory')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('❌ Table error:', tableError);
      
      if (tableError.message.includes('relation "public.assets_inventory" does not exist')) {
        console.log('📋 Table does not exist. Creating assets_inventory table...');
        
        // Try to create the table
        const { error: createError } = await supabase.rpc('exec_sql', {
          sql: `
            CREATE TABLE IF NOT EXISTS public.assets_inventory (
              id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
              name TEXT NOT NULL,
              type TEXT NOT NULL,
              status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'maintenance', 'retired')),
              condition TEXT DEFAULT 'good' CHECK (condition IN ('excellent', 'good', 'fair', 'poor')),
              location TEXT,
              purchase_date DATE,
              purchase_price DECIMAL,
              department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
              created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            -- Enable RLS
            ALTER TABLE public.assets_inventory ENABLE ROW LEVEL SECURITY;
            
            -- Create policies
            CREATE POLICY "assets_inventory_select" ON public.assets_inventory
              FOR SELECT TO authenticated USING (true);
            
            CREATE POLICY "assets_inventory_insert" ON public.assets_inventory
              FOR INSERT TO authenticated WITH CHECK (
                auth.uid() IN (
                  SELECT id FROM public.profiles WHERE role IN ('admin', 'manager', 'staff-admin')
                )
              );
            
            CREATE POLICY "assets_inventory_update" ON public.assets_inventory
              FOR UPDATE TO authenticated USING (
                auth.uid() = created_by OR
                auth.uid() IN (
                  SELECT id FROM public.profiles WHERE role IN ('admin', 'manager', 'staff-admin')
                )
              );
            
            CREATE POLICY "assets_inventory_delete" ON public.assets_inventory
              FOR DELETE TO authenticated USING (
                auth.uid() IN (
                  SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
                )
              );
          `
        });
        
        if (createError) {
          console.error('❌ Failed to create table:', createError);
        } else {
          console.log('✅ Table created successfully');
          
          // Add some sample data
          const { error: insertError } = await supabase
            .from('assets_inventory')
            .insert([
              {
                name: 'Dell Laptop',
                type: 'Computer',
                status: 'active',
                condition: 'good',
                location: 'Office A',
                purchase_price: 150000,
                purchase_date: '2024-01-15'
              },
              {
                name: 'HP Printer',
                type: 'Printer',
                status: 'active',
                condition: 'excellent',
                location: 'Office B',
                purchase_price: 75000,
                purchase_date: '2024-02-01'
              },
              {
                name: 'Samsung Monitor',
                type: 'Monitor',
                status: 'maintenance',
                condition: 'fair',
                location: 'Office A',
                purchase_price: 45000,
                purchase_date: '2023-12-10'
              }
            ]);
          
          if (insertError) {
            console.error('❌ Failed to insert sample data:', insertError);
          } else {
            console.log('✅ Sample data inserted');
          }
        }
      }
      return;
    }
    
    console.log('✅ Table exists');
    
    // Check table structure
    if (tableData && tableData.length > 0) {
      console.log('📋 Sample record structure:', Object.keys(tableData[0]));
      console.log('📊 Sample data:', tableData[0]);
    }
    
    // Get total count
    const { count, error: countError } = await supabase
      .from('assets_inventory')
      .select('*', { count: 'exact', head: true });
    
    if (countError) {
      console.error('❌ Count error:', countError);
    } else {
      console.log(`📊 Total records: ${count}`);
    }
    
    // Test the query used by AssetsPage
    console.log('🧪 Testing AssetsPage query...');
    const { data: assetsData, error: assetsError } = await supabase
      .from('assets_inventory')
      .select(`
        *,
        departments!department_id(name),
        profiles!created_by(full_name)
      `)
      .order('created_at', { ascending: false });
    
    if (assetsError) {
      console.error('❌ Assets query error:', assetsError);
      
      // Try simpler query
      console.log('🔄 Trying simpler query...');
      const { data: simpleData, error: simpleError } = await supabase
        .from('assets_inventory')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (simpleError) {
        console.error('❌ Simple query also failed:', simpleError);
      } else {
        console.log('✅ Simple query works:', simpleData?.length, 'records');
      }
    } else {
      console.log('✅ Assets query works:', assetsData?.length, 'records');
    }
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Run if called directly
if (typeof window !== 'undefined') {
  (window as any).checkAssetsTable = checkAssetsTable;
  console.log('🛠️ Assets table checker loaded. Run checkAssetsTable() to test.');
}

export { checkAssetsTable };
