
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import { Eye, Search, Calendar, User, DollarSign, Download, Paperclip } from "lucide-react";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

interface MemoAttachment {
  id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
}

interface Memo {
  id: string;
  title: string;
  content: string | null;
  status: 'pending' | 'approved' | 'rejected';
  created_by: string;
  department: string | null;
  created_at: string;
  updated_at: string;
  subject?: string | null;
  purpose?: string | null;
  total_amount?: number | null;
  memo_date?: string | null;
  payment_items?: any[] | null;
  account_details?: string | null;
  to_recipient?: string | null;
}

export const MemoList = () => {
  const { userProfile } = useAuth();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [selectedMemo, setSelectedMemo] = useState<Memo | null>(null);

  const { data: memos, isLoading } = useQuery({
    queryKey: ['memos-list'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('memos')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Memo[];
    },
  });

  const { data: profiles } = useQuery({
    queryKey: ['profiles-memo-list'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, email, role')
        .order('full_name');

      if (error) throw error;
      return data;
    },
  });

  const { data: memoAttachments } = useQuery({
    queryKey: ['memo-attachments', selectedMemo?.id],
    queryFn: async () => {
      if (!selectedMemo?.id) return [];
      
      const { data, error } = await supabase
        .from('memo_attachments')
        .select('*')
        .eq('memo_id', selectedMemo.id);

      if (error) throw error;
      return data as MemoAttachment[];
    },
    enabled: !!selectedMemo?.id,
  });

  const getUserName = (userId: string) => {
    const profile = profiles?.find(p => p.id === userId);
    return profile?.full_name || profile?.email || 'Unknown User';
  };

  const downloadAttachment = async (filePath: string, fileName: string) => {
    try {
      const { data, error } = await supabase.storage
        .from('memo-attachments')
        .download(filePath);

      if (error) throw error;

      const url = URL.createObjectURL(data);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading file:", error);
      toast({
        title: "Error",
        description: "Failed to download file",
        variant: "destructive",
      });
    }
  };

  const filteredMemos = memos?.filter(memo => {
    const matchesSearch = 
      memo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (memo.subject && memo.subject.toLowerCase().includes(searchTerm.toLowerCase())) ||
      getUserName(memo.created_by).toLowerCase().includes(searchTerm.toLowerCase()) ||
      (memo.to_recipient && memo.to_recipient.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesStatus = statusFilter === "all" || memo.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: "default",
      approved: "secondary",
      rejected: "destructive"
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || "default"}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const MemoDetailView = ({ memo }: { memo: Memo }) => (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{memo.subject || memo.title}</span>
          {getStatusBadge(memo.status)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-muted-foreground">From:</p>
            <p>{getUserName(memo.created_by)}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">To:</p>
            <p>{memo.to_recipient || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">Date:</p>
            <p>{memo.memo_date ? format(new Date(memo.memo_date), 'PPP') : format(new Date(memo.created_at), 'PPP')}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-muted-foreground">Total Amount:</p>
            <p className="font-semibold">₦{memo.total_amount?.toLocaleString('en-NG', { minimumFractionDigits: 2 }) || '0.00'}</p>
          </div>
        </div>
        
        <div>
          <p className="text-sm font-medium text-muted-foreground mb-2">Purpose:</p>
          <p className="bg-muted p-3 rounded-md">{memo.purpose || memo.content || 'No purpose specified'}</p>
        </div>

        {memo.payment_items && memo.payment_items.length > 0 && (
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-2">Payment Items:</p>
            <div className="space-y-2">
              {memo.payment_items.map((item: any, index: number) => (
                <div key={index} className="flex justify-between items-center bg-muted p-2 rounded">
                  <span>{item.description}</span>
                  <span className="font-medium">₦{parseFloat(item.amount || 0).toLocaleString('en-NG', { minimumFractionDigits: 2 })}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {memo.account_details && (
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-2">Account Details:</p>
            <p className="bg-muted p-3 rounded-md whitespace-pre-wrap">{memo.account_details}</p>
          </div>
        )}

        {memoAttachments && memoAttachments.length > 0 && (
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-2 flex items-center gap-2">
              <Paperclip className="h-4 w-4" />
              Attachments:
            </p>
            <div className="space-y-2">
              {memoAttachments.map((attachment) => (
                <div key={attachment.id} className="flex items-center justify-between bg-muted p-2 rounded">
                  <span className="text-sm">{attachment.file_name}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => downloadAttachment(attachment.file_path, attachment.file_name)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading memos...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Team Memos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search memos by title, subject, or sender..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {selectedMemo ? (
            <div>
              <Button 
                variant="outline" 
                onClick={() => setSelectedMemo(null)}
                className="mb-4"
              >
                ← Back to List
              </Button>
              <MemoDetailView memo={selectedMemo} />
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Subject</TableHead>
                    <TableHead>From</TableHead>
                    <TableHead>To</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMemos && filteredMemos.length > 0 ? (
                    filteredMemos.map((memo) => (
                      <TableRow key={memo.id}>
                        <TableCell className="font-medium">{memo.subject || memo.title}</TableCell>
                        <TableCell>{getUserName(memo.created_by)}</TableCell>
                        <TableCell>{memo.to_recipient || 'Not specified'}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            ₦{memo.total_amount?.toLocaleString('en-NG', { minimumFractionDigits: 2 }) || '0.00'}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {memo.memo_date ? format(new Date(memo.memo_date), 'MMM dd, yyyy') : format(new Date(memo.created_at), 'MMM dd, yyyy')}
                          </div>
                        </TableCell>
                        <TableCell>{getStatusBadge(memo.status)}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedMemo(memo)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        {searchTerm || statusFilter !== "all" ? "No memos match your search criteria" : "No memos found"}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
