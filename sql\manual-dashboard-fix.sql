-- Manual Dashboard Fix SQL Script
-- Run this script in your Supabase SQL Editor to fix all dashboard issues
-- Copy and paste this entire script into the Supabase dashboard SQL editor

-- ============================================================================
-- STEP 1: CREATE MISSING TABLES FOR DASHBOARD DATA
-- ============================================================================

-- Create invoices table for financial data
CREATE TABLE IF NOT EXISTS public.invoices (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  invoice_number VARCHAR(50) UNIQUE NOT NULL,
  client_name TEXT NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  total_amount DECIMAL(10,2) NOT NULL,
  payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'overdue', 'cancelled')),
  due_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL
);

-- Create expense_reports table
CREATE TABLE IF NOT EXISTS public.expense_reports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  amount DECIMAL(10,2) NOT NULL,
  category TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
  approval_status TEXT DEFAULT 'pending' CHECK (approval_status IN ('pending', 'approved', 'rejected')),
  submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  approved_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  receipt_url TEXT,
  expense_date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create reports table for report submissions
CREATE TABLE IF NOT EXISTS public.reports (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  report_type TEXT DEFAULT 'general' CHECK (report_type IN ('general', 'financial', 'project', 'maintenance', 'incident', 'performance')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  status TEXT DEFAULT 'submitted' CHECK (status IN ('submitted', 'under_review', 'approved', 'rejected', 'completed')),
  submitted_by UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  reviewed_by UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  due_date DATE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create time_logs table for time tracking
CREATE TABLE IF NOT EXISTS public.time_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  task_id UUID REFERENCES public.tasks(id) ON DELETE SET NULL,
  description TEXT,
  hours_worked DECIMAL(5,2) NOT NULL,
  log_date DATE DEFAULT CURRENT_DATE,
  billable BOOLEAN DEFAULT true,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'submitted', 'approved')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info' CHECK (type IN ('info', 'warning', 'error', 'success')),
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create system_activities table
CREATE TABLE IF NOT EXISTS public.system_activities (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  action VARCHAR(100) NOT NULL,
  description TEXT,
  entity_type VARCHAR(100),
  entity_id UUID,
  metadata JSONB DEFAULT '{}',
  severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
  category VARCHAR(50) DEFAULT 'general',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- STEP 2: CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON public.invoices(payment_status);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON public.invoices(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_expense_reports_status ON public.expense_reports(status);
CREATE INDEX IF NOT EXISTS idx_expense_reports_submitted_by ON public.expense_reports(submitted_by);
CREATE INDEX IF NOT EXISTS idx_reports_status ON public.reports(status);
CREATE INDEX IF NOT EXISTS idx_reports_submitted_by ON public.reports(submitted_by);
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_log_date ON public.time_logs(log_date DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);
CREATE INDEX IF NOT EXISTS idx_system_activities_user_id ON public.system_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_system_activities_created_at ON public.system_activities(created_at DESC);

-- ============================================================================
-- STEP 3: ENABLE ROW LEVEL SECURITY
-- ============================================================================

ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_activities ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- STEP 4: CREATE RLS POLICIES
-- ============================================================================

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "invoices_select_admin" ON public.invoices;
DROP POLICY IF EXISTS "invoices_insert_admin" ON public.invoices;
DROP POLICY IF EXISTS "expense_reports_select_own" ON public.expense_reports;
DROP POLICY IF EXISTS "expense_reports_insert_own" ON public.expense_reports;
DROP POLICY IF EXISTS "reports_select_own" ON public.reports;
DROP POLICY IF EXISTS "reports_insert_own" ON public.reports;
DROP POLICY IF EXISTS "time_logs_select_own" ON public.time_logs;
DROP POLICY IF EXISTS "time_logs_insert_own" ON public.time_logs;
DROP POLICY IF EXISTS "notifications_select_own" ON public.notifications;
DROP POLICY IF EXISTS "notifications_insert_system" ON public.notifications;
DROP POLICY IF EXISTS "system_activities_select_admin" ON public.system_activities;
DROP POLICY IF EXISTS "system_activities_insert_authenticated" ON public.system_activities;

-- INVOICES POLICIES
CREATE POLICY "invoices_select_admin_accountant" ON public.invoices
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'accountant', 'manager')
    )
  );

CREATE POLICY "invoices_insert_admin_accountant" ON public.invoices
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'accountant')
    )
  );

-- EXPENSE REPORTS POLICIES
CREATE POLICY "expense_reports_select_own_or_admin" ON public.expense_reports
  FOR SELECT USING (
    submitted_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'manager', 'accountant')
    )
  );

CREATE POLICY "expense_reports_insert_authenticated" ON public.expense_reports
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND submitted_by = auth.uid()
  );

-- REPORTS POLICIES
CREATE POLICY "reports_select_own_or_admin" ON public.reports
  FOR SELECT USING (
    submitted_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'manager', 'staff-admin')
    )
  );

CREATE POLICY "reports_insert_authenticated" ON public.reports
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND submitted_by = auth.uid()
  );

-- TIME LOGS POLICIES
CREATE POLICY "time_logs_select_own_or_admin" ON public.time_logs
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'manager')
    )
  );

CREATE POLICY "time_logs_insert_own" ON public.time_logs
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL AND user_id = auth.uid()
  );

-- NOTIFICATIONS POLICIES
CREATE POLICY "notifications_select_own" ON public.notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "notifications_insert_system" ON public.notifications
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL OR current_setting('role') = 'service_role'
  );

-- SYSTEM ACTIVITIES POLICIES
CREATE POLICY "system_activities_select_admin" ON public.system_activities
  FOR SELECT USING (
    user_id = auth.uid() OR
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'manager', 'staff-admin')
    )
  );

CREATE POLICY "system_activities_insert_authenticated" ON public.system_activities
  FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

-- ============================================================================
-- STEP 5: GRANT PERMISSIONS
-- ============================================================================

GRANT SELECT, INSERT, UPDATE ON public.invoices TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.expense_reports TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.reports TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.time_logs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.notifications TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.system_activities TO authenticated;

GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;

-- ============================================================================
-- STEP 6: FIX PROFILE ROLES (IF NEEDED)
-- ============================================================================

-- Update profile role constraints
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_role_check;
ALTER TABLE public.profiles ADD CONSTRAINT profiles_role_check 
  CHECK (role IN ('admin', 'manager', 'staff', 'accountant', 'hr', 'staff-admin'));

-- Add missing profile columns
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'staff';
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS preferences JSONB DEFAULT '{}';
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS timezone TEXT DEFAULT 'UTC';

-- ============================================================================
-- STEP 7: INSERT SAMPLE DATA (REPLACE 'YOUR_USER_ID' WITH ACTUAL USER ID)
-- ============================================================================

-- NOTE: Replace 'YOUR_USER_ID' with your actual user ID from auth.users
-- You can find your user ID by running: SELECT id FROM auth.users WHERE email = '<EMAIL>';

-- Sample invoices
INSERT INTO public.invoices (invoice_number, client_name, amount, total_amount, payment_status, due_date, created_by) VALUES
('INV-2024-001', 'Acme Corporation', 5000.00, 5000.00, 'paid', CURRENT_DATE + INTERVAL '30 days', 'YOUR_USER_ID'),
('INV-2024-002', 'Tech Solutions Ltd', 7500.00, 7500.00, 'pending', CURRENT_DATE + INTERVAL '15 days', 'YOUR_USER_ID'),
('INV-2024-003', 'Global Industries', 3200.00, 3200.00, 'overdue', CURRENT_DATE - INTERVAL '5 days', 'YOUR_USER_ID'),
('INV-2024-004', 'Digital Dynamics', 4800.00, 4800.00, 'paid', CURRENT_DATE + INTERVAL '20 days', 'YOUR_USER_ID'),
('INV-2024-005', 'Future Systems', 6200.00, 6200.00, 'pending', CURRENT_DATE + INTERVAL '10 days', 'YOUR_USER_ID')
ON CONFLICT (invoice_number) DO NOTHING;

-- Sample expense reports
INSERT INTO public.expense_reports (title, description, amount, category, status, submitted_by) VALUES
('Office Supplies', 'Monthly office supplies purchase', 450.00, 'office', 'approved', 'YOUR_USER_ID'),
('Travel Expenses', 'Business trip to Lagos', 1200.00, 'travel', 'pending', 'YOUR_USER_ID'),
('Equipment Purchase', 'New laptop for development', 2500.00, 'equipment', 'approved', 'YOUR_USER_ID'),
('Training Course', 'Professional development course', 800.00, 'training', 'pending', 'YOUR_USER_ID'),
('Client Meeting', 'Lunch meeting with client', 150.00, 'meals', 'approved', 'YOUR_USER_ID')
ON CONFLICT DO NOTHING;

-- Sample reports
INSERT INTO public.reports (title, description, report_type, priority, status, submitted_by) VALUES
('Monthly Performance Report', 'Summary of team performance for this month', 'performance', 'medium', 'submitted', 'YOUR_USER_ID'),
('Project Status Update', 'Current status of ongoing projects', 'project', 'high', 'under_review', 'YOUR_USER_ID'),
('Financial Summary', 'Quarterly financial overview', 'financial', 'high', 'approved', 'YOUR_USER_ID'),
('Maintenance Report', 'Equipment maintenance summary', 'maintenance', 'medium', 'completed', 'YOUR_USER_ID'),
('Incident Report', 'Security incident documentation', 'incident', 'urgent', 'under_review', 'YOUR_USER_ID')
ON CONFLICT DO NOTHING;

-- Sample time logs
INSERT INTO public.time_logs (user_id, description, hours_worked, log_date, billable) VALUES
('YOUR_USER_ID', 'Dashboard development', 8.0, CURRENT_DATE, true),
('YOUR_USER_ID', 'Bug fixes and testing', 6.5, CURRENT_DATE - INTERVAL '1 day', true),
('YOUR_USER_ID', 'Client meeting', 2.0, CURRENT_DATE - INTERVAL '2 days', true),
('YOUR_USER_ID', 'Code review', 3.5, CURRENT_DATE - INTERVAL '3 days', true),
('YOUR_USER_ID', 'Documentation update', 4.0, CURRENT_DATE - INTERVAL '4 days', true),
('YOUR_USER_ID', 'Team standup', 1.0, CURRENT_DATE - INTERVAL '5 days', false),
('YOUR_USER_ID', 'Project planning', 5.5, CURRENT_DATE - INTERVAL '6 days', true)
ON CONFLICT DO NOTHING;

-- Sample notifications
INSERT INTO public.notifications (user_id, title, message, type, read) VALUES
('YOUR_USER_ID', 'Welcome to Dashboard', 'Your dashboard is now ready to use!', 'success', false),
('YOUR_USER_ID', 'New Report Submitted', 'A new report has been submitted for review', 'info', false),
('YOUR_USER_ID', 'Invoice Overdue', 'Invoice INV-2024-003 is now overdue', 'warning', true),
('YOUR_USER_ID', 'Expense Approved', 'Your office supplies expense has been approved', 'success', true),
('YOUR_USER_ID', 'System Maintenance', 'Scheduled maintenance tonight at 2 AM', 'info', false)
ON CONFLICT DO NOTHING;

-- Sample system activities
INSERT INTO public.system_activities (user_id, action, description, severity, category) VALUES
('YOUR_USER_ID', 'login', 'User logged into the system', 'info', 'authentication'),
('YOUR_USER_ID', 'report_submit', 'Monthly performance report submitted', 'info', 'reports'),
('YOUR_USER_ID', 'expense_create', 'New expense report created', 'info', 'finance'),
('YOUR_USER_ID', 'dashboard_view', 'Dashboard accessed', 'info', 'navigation'),
('YOUR_USER_ID', 'profile_update', 'Profile information updated', 'info', 'user_management')
ON CONFLICT DO NOTHING;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check if tables were created successfully
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('invoices', 'expense_reports', 'reports', 'time_logs', 'notifications', 'system_activities')
ORDER BY tablename;

-- Check sample data
SELECT 'invoices' as table_name, count(*) as record_count FROM public.invoices
UNION ALL
SELECT 'expense_reports', count(*) FROM public.expense_reports
UNION ALL
SELECT 'reports', count(*) FROM public.reports
UNION ALL
SELECT 'time_logs', count(*) FROM public.time_logs
UNION ALL
SELECT 'notifications', count(*) FROM public.notifications
UNION ALL
SELECT 'system_activities', count(*) FROM public.system_activities;

-- ============================================================================
-- INSTRUCTIONS:
-- 1. Copy this entire script
-- 2. Go to your Supabase dashboard > SQL Editor
-- 3. Paste the script
-- 4. Replace 'YOUR_USER_ID' with your actual user ID (find it with: SELECT id FROM auth.users WHERE email = '<EMAIL>';)
-- 5. Run the script
-- 6. Check the verification queries at the bottom to confirm everything worked
-- ============================================================================
