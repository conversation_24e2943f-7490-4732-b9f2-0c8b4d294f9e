import { Button } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/hooks/use-toast'
import { supabase } from '@/integrations/supabase/client'
import { FileText, Loader, Upload } from 'lucide-react'
import { useState } from 'react'

export const DocumentAnalyzer = () => {
  const [content, setContent] = useState('')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [analysis, setAnalysis] = useState < string | null > (null)
  const { toast } = useToast()

  const handleAnalyze = async () => {
    if (!content.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter some content to analyze',
        variant: 'destructive'
      })
      return
    }

    setIsAnalyzing(true)
    setProgress(0)

    try {
      const interval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90))
      }, 500)

      const { data, error } = await supabase.functions.invoke('analyze-document', {
        body: { content, fileName: 'Staff Document', type: 'document' }
      })

      if (error) throw error

      clearInterval(interval)
      setProgress(100)
      setAnalysis(data.summary || data.result)

      toast({
        title: 'Analysis Complete',
        description: 'Your document has been analyzed successfully'
      })
    } catch (error) {
      console.error('Error:', error)
      toast({
        title: 'Error',
        description: 'Failed to analyze document. Please try again.',
        variant: 'destructive'
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  return (
    <div className='space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <FileText className='h-5 w-5' />
            Document Analyzer
          </CardTitle>
        </CardHeader>
        <CardContent className='space-y-4'>
          <Textarea
            placeholder='Enter or paste your document content here...'
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className='min-h-[200px]'
          />

          <Button
            onClick={handleAnalyze}
            disabled={isAnalyzing || !content.trim()}
            className='w-full'
          >
            {isAnalyzing
              ? (
                <>
                  <Loader className='mr-2 h-4 w-4 animate-spin' />
                  Analyzing...
                </>
                )
              : (
                <>
                  <Upload className='mr-2 h-4 w-4' />
                  Analyze Document
                </>
                )}
          </Button>

          {isAnalyzing && (
            <Progress value={progress} className='w-full' />
          )}

          {analysis && (
            <Card className='mt-4'>
              <CardHeader>
                <CardTitle className='text-lg'>Analysis Results</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='prose prose-sm max-w-none'>
                  {analysis.split('\n').map((paragraph, index) => (
                    <p key={index} className='mb-2'>{paragraph}</p>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
