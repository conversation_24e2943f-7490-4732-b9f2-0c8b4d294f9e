import { EmergencyDashboard } from './EmergencyDashboard'

// Direct dashboard that bypasses all authentication and routing issues
export const DirectDashboard = () => {
  console.log('🚀 DirectDashboard: Rendering direct dashboard bypass')

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4'>
        <h3 className='font-bold'>✅ Direct Dashboard Access</h3>
        <p>This dashboard bypasses all authentication and routing issues for testing.</p>
      </div>
      <EmergencyDashboard />
    </div>
  )
}
