import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, dateFnsLocalizer, View, Views } from "react-big-calendar";
import { format, parse, startOfWeek, getDay } from 'date-fns';
import { enUS } from 'date-fns/locale';
import { Video, Calendar as CalendarIcon, Plus, Edit, Trash2, RefreshCw, Users, Clock, ExternalLink } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/auth/AuthProvider";
import 'react-big-calendar/lib/css/react-big-calendar.css';

const locales = {
  'en-US': enUS,
};

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales,
});

interface ZoomMeeting {
  id: string;
  topic: string;
  agenda?: string;
  start_time: string;
  duration: number;
  join_url?: string;
  meeting_id?: string;
  passcode?: string;
  participants: number;
  status: 'scheduled' | 'started' | 'ended' | 'cancelled';
  host_id: string;
  host_name?: string;
  created_at: string;
  zoom_meeting_id?: string;
}

interface MeetingForm {
  topic: string;
  agenda: string;
  start_time: string;
  duration: number;
  password: string;
}

export const ZoomIntegration = () => {
  const { toast } = useToast();
  const { userProfile } = useAuth();
  const [meetings, setMeetings] = useState<ZoomMeeting[]>([]);
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingMeeting, setEditingMeeting] = useState<ZoomMeeting | null>(null);
  const [view, setView] = useState<View>(Views.MONTH);
  const [formData, setFormData] = useState<MeetingForm>({
    topic: '',
    agenda: '',
    start_time: new Date().toISOString().slice(0, 16),
    duration: 60,
    password: ''
  });

  useEffect(() => {
    loadMeetings();
  }, []);

  const loadMeetings = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('zoom_meetings')
        .select(`
          *,
          host:profiles!host_id(id, full_name, email)
        `)
        .eq('host_id', userProfile?.id)
        .order('start_time', { ascending: true });

      if (error) {
        console.error('Error loading zoom meetings:', error);
        // If zoom_meetings fails, try regular meetings table
        const { data: meetingsData, error: meetingsError } = await supabase
          .from('meetings')
          .select(`
            *,
            organizer:profiles!organizer_id(id, full_name, email)
          `)
          .eq('organizer_id', userProfile?.id)
          .order('start_time', { ascending: true });

        if (meetingsError) throw meetingsError;

        const formattedMeetings = (meetingsData || []).map((meeting: any) => ({
          ...meeting,
          host_name: meeting.organizer?.full_name || meeting.organizer?.email || 'Unknown Host',
          topic: meeting.title,
          agenda: meeting.description,
          duration: meeting.end_time ?
            Math.round((new Date(meeting.end_time).getTime() - new Date(meeting.start_time).getTime()) / (1000 * 60)) :
            60
        }));

        setMeetings(formattedMeetings);
        return;
      }

      const formattedMeetings = (data || []).map((meeting: any) => ({
        ...meeting,
        host_name: meeting.host?.full_name || meeting.host?.email || 'Unknown Host'
      }));

      setMeetings(formattedMeetings);
    } catch (error: any) {
      console.error('Error loading meetings:', error);
      toast({
        title: "Error Loading Meetings",
        description: error.message || "Failed to load meetings",
        variant: "destructive",
      });
      setMeetings([]);
    } finally {
      setLoading(false);
    }
  };

  const createMeeting = async () => {
    if (!formData.topic.trim()) {
      toast({
        title: "Topic Required",
        description: "Please enter a meeting topic",
        variant: "destructive",
      });
      return;
    }

    setCreating(true);
    try {
      // Call Zoom integration function to create actual meeting
      const { data: zoomData, error: zoomError } = await supabase.functions.invoke('integration-manager', {
        body: {
          action: 'create_zoom_meeting',
          meeting_data: {
            topic: formData.topic,
            agenda: formData.agenda,
            start_time: formData.start_time,
            duration: formData.duration,
            password: formData.password
          },
          user_id: userProfile?.id
        }
      });

      if (zoomError) throw zoomError;

      // Save meeting to our database
      const { error: dbError } = await supabase
        .from('zoom_meetings')
        .insert({
          topic: formData.topic,
          agenda: formData.agenda,
          start_time: formData.start_time,
          duration: formData.duration,
          join_url: zoomData.join_url,
          meeting_id: zoomData.id?.toString(),
          passcode: zoomData.password || formData.password,
          participants: 0,
          status: 'scheduled',
          host_id: userProfile?.id,
          zoom_meeting_id: zoomData.id?.toString()
        });

      if (dbError) throw dbError;

      await loadMeetings();
      resetForm();
      setDialogOpen(false);

      toast({
        title: "Meeting Created",
        description: `${formData.topic} has been scheduled successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Failed to Create Meeting",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const updateMeeting = async () => {
    if (!editingMeeting) return;

    setCreating(true);
    try {
      // Update Zoom meeting via API
      const { data: zoomData, error: zoomError } = await supabase.functions.invoke('integration-manager', {
        body: {
          action: 'update_zoom_meeting',
          meeting_id: editingMeeting.zoom_meeting_id,
          meeting_data: {
            topic: formData.topic,
            agenda: formData.agenda,
            start_time: formData.start_time,
            duration: formData.duration
          },
          user_id: userProfile?.id
        }
      });

      if (zoomError) throw zoomError;

      // Update in our database
      const { error: dbError } = await supabase
        .from('zoom_meetings')
        .update({
          topic: formData.topic,
          agenda: formData.agenda,
          start_time: formData.start_time,
          duration: formData.duration,
          updated_at: new Date().toISOString()
        })
        .eq('id', editingMeeting.id);

      if (dbError) throw dbError;

      await loadMeetings();
      resetForm();
      setDialogOpen(false);

      toast({
        title: "Meeting Updated",
        description: `${formData.topic} has been updated successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Update Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setCreating(false);
    }
  };

  const deleteMeeting = async (meeting: ZoomMeeting) => {
    try {
      // Delete from Zoom via API
      if (meeting.zoom_meeting_id) {
        await supabase.functions.invoke('integration-manager', {
          body: {
            action: 'delete_zoom_meeting',
            meeting_id: meeting.zoom_meeting_id,
            user_id: userProfile?.id
          }
        });
      }

      // Delete from our database
      const { error } = await supabase
        .from('zoom_meetings')
        .delete()
        .eq('id', meeting.id);

      if (error) throw error;

      await loadMeetings();

      toast({
        title: "Meeting Deleted",
        description: `${meeting.topic} has been deleted`,
      });
    } catch (error: any) {
      toast({
        title: "Delete Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setFormData({
      topic: '',
      agenda: '',
      start_time: new Date().toISOString().slice(0, 16),
      duration: 60,
      password: ''
    });
    setEditingMeeting(null);
  };

  const openEditDialog = (meeting: ZoomMeeting) => {
    setEditingMeeting(meeting);
    setFormData({
      topic: meeting.topic,
      agenda: meeting.agenda || '',
      start_time: new Date(meeting.start_time).toISOString().slice(0, 16),
      duration: meeting.duration,
      password: meeting.passcode || ''
    });
    setDialogOpen(true);
  };

  const openCreateDialog = () => {
    resetForm();
    setDialogOpen(true);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />Scheduled</Badge>;
      case 'started':
        return <Badge variant="default" className="bg-green-100 text-green-800"><Video className="h-3 w-3 mr-1" />Live</Badge>;
      case 'ended':
        return <Badge variant="outline">Ended</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const calendarEvents = meetings.map(meeting => ({
    id: meeting.id,
    title: meeting.topic,
    start: new Date(meeting.start_time),
    end: new Date(new Date(meeting.start_time).getTime() + meeting.duration * 60000),
    resource: meeting
  }));

  const handleSelectEvent = (event: any) => {
    openEditDialog(event.resource);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Video className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold">Zoom Integration</h2>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadMeetings} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Schedule Meeting
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingMeeting ? 'Edit Meeting' : 'Schedule New Meeting'}
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="topic">Meeting Topic</Label>
                  <Input
                    id="topic"
                    value={formData.topic}
                    onChange={(e) => setFormData({...formData, topic: e.target.value})}
                    placeholder="Enter meeting topic"
                  />
                </div>
                <div>
                  <Label htmlFor="agenda">Agenda (Optional)</Label>
                  <Textarea
                    id="agenda"
                    value={formData.agenda}
                    onChange={(e) => setFormData({...formData, agenda: e.target.value})}
                    placeholder="Meeting agenda..."
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="start_time">Start Time</Label>
                  <Input
                    id="start_time"
                    type="datetime-local"
                    value={formData.start_time}
                    onChange={(e) => setFormData({...formData, start_time: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <Input
                    id="duration"
                    type="number"
                    min="15"
                    max="480"
                    value={formData.duration}
                    onChange={(e) => setFormData({...formData, duration: Number(e.target.value)})}
                  />
                </div>
                <div>
                  <Label htmlFor="password">Meeting Password (Optional)</Label>
                  <Input
                    id="password"
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    placeholder="Enter meeting password"
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button 
                    onClick={editingMeeting ? updateMeeting : createMeeting} 
                    disabled={creating} 
                    className="flex-1"
                  >
                    {creating ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : null}
                    {editingMeeting ? 'Update Meeting' : 'Schedule Meeting'}
                  </Button>
                  <Button variant="outline" onClick={() => setDialogOpen(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Calendar View */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Meeting Calendar
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div style={{ height: '500px' }}>
            <Calendar
              localizer={localizer}
              events={calendarEvents}
              startAccessor="start"
              endAccessor="end"
              view={view}
              onView={setView}
              onSelectEvent={handleSelectEvent}
              style={{ height: '100%' }}
              views={[Views.MONTH, Views.WEEK, Views.DAY]}
            />
          </div>
        </CardContent>
      </Card>

      {/* Meetings Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            Scheduled Meetings ({meetings.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-500">Loading meetings...</span>
            </div>
          ) : meetings.length === 0 ? (
            <div className="text-center py-8">
              <Video className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No meetings scheduled</p>
              <Button onClick={openCreateDialog} className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Schedule First Meeting
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Topic</TableHead>
                    <TableHead>Start Time</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Participants</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {meetings.map((meeting) => (
                    <TableRow key={meeting.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{meeting.topic}</p>
                          {meeting.agenda && (
                            <p className="text-xs text-gray-500">{meeting.agenda.substring(0, 50)}...</p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <CalendarIcon className="h-3 w-3" />
                          {new Date(meeting.start_time).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <Clock className="h-3 w-3" />
                          {meeting.duration} min
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(meeting.status)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <Users className="h-3 w-3" />
                          {meeting.participants}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {meeting.join_url && (
                            <Button
                              variant="ghost"
                              size="sm"
                              asChild
                            >
                              <a href={meeting.join_url} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(meeting)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteMeeting(meeting)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Video className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Meetings</p>
                <p className="text-2xl font-bold">{meetings.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Scheduled</p>
                <p className="text-2xl font-bold">
                  {meetings.filter(m => m.status === 'scheduled').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Participants</p>
                <p className="text-2xl font-bold">
                  {meetings.reduce((sum, m) => sum + m.participants, 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Total Duration</p>
                <p className="text-2xl font-bold">
                  {Math.round(meetings.reduce((sum, m) => sum + m.duration, 0) / 60)}h
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
