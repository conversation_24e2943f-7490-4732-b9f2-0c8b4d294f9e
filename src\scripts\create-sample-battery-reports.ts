/**
 * Create Sample Battery Reports
 * This script creates sample battery reports for testing and demonstration
 */

import { supabase } from '@/integrations/supabase/client';

export async function createSampleBatteryReports() {
  try {
    console.log('🔋 Creating sample battery reports...');

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('❌ User not authenticated');
      return false;
    }

    // Sample battery reports data
    const sampleReports = [
      {
        battery_id: 'BATT-001',
        battery_serial_number: 'SN-12345-001',
        site_name: 'Lagos Central Tower',
        site_id: 'SITE-001',
        health_status: 'excellent',
        battery_voltage: 48.2,
        current_capacity: 95,
        charging_status: 'fully_charged',
        temperature: 25.5,
        load_current: 12.3,
        backup_time_remaining: 8.5,
        runtime_hours: 2400,
        maintenance_required: false,
        next_maintenance_date: '2024-03-15',
        customer_name: 'MTN Nigeria',
        amount_naira: 150000,
        maintenance_notes: 'Battery performing excellently. All parameters within normal range.',
        issues_reported: null,
        recommendations: 'Continue regular monitoring. Next maintenance scheduled for March 2024.',
        photos: [],
        report_date: new Date().toISOString().split('T')[0],
        reporter_id: user.id
      },
      {
        battery_id: 'BATT-002',
        battery_serial_number: 'SN-12345-002',
        site_name: 'Abuja Business District',
        site_id: 'SITE-002',
        health_status: 'good',
        battery_voltage: 47.8,
        current_capacity: 88,
        charging_status: 'charging',
        temperature: 28.2,
        load_current: 15.7,
        backup_time_remaining: 7.2,
        runtime_hours: 3200,
        maintenance_required: false,
        next_maintenance_date: '2024-02-28',
        customer_name: 'Airtel Nigeria',
        amount_naira: 125000,
        maintenance_notes: 'Battery in good condition. Slight voltage drop noted but within acceptable range.',
        issues_reported: 'Minor temperature fluctuation during peak load',
        recommendations: 'Monitor temperature closely. Consider ventilation improvement.',
        photos: [],
        report_date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
        reporter_id: user.id
      },
      {
        battery_id: 'BATT-003',
        battery_serial_number: 'SN-12345-003',
        site_name: 'Port Harcourt Industrial',
        site_id: 'SITE-003',
        health_status: 'fair',
        battery_voltage: 46.5,
        current_capacity: 72,
        charging_status: 'discharging',
        temperature: 32.1,
        load_current: 18.9,
        backup_time_remaining: 5.8,
        runtime_hours: 4800,
        maintenance_required: true,
        next_maintenance_date: '2024-01-30',
        customer_name: 'Glo Mobile',
        amount_naira: 95000,
        maintenance_notes: 'Battery showing signs of aging. Capacity reduced to 72%.',
        issues_reported: 'Reduced backup time, higher operating temperature',
        recommendations: 'Schedule maintenance within 2 weeks. Consider battery replacement planning.',
        photos: [],
        report_date: new Date(Date.now() - 172800000).toISOString().split('T')[0], // 2 days ago
        reporter_id: user.id
      },
      {
        battery_id: 'BATT-004',
        battery_serial_number: 'SN-12345-004',
        site_name: 'Kano Commercial Hub',
        site_id: 'SITE-004',
        health_status: 'poor',
        battery_voltage: 45.2,
        current_capacity: 58,
        charging_status: 'fault',
        temperature: 35.8,
        load_current: 22.1,
        backup_time_remaining: 3.2,
        runtime_hours: 6200,
        maintenance_required: true,
        next_maintenance_date: '2024-01-25',
        customer_name: '9mobile',
        amount_naira: 75000,
        maintenance_notes: 'Battery in poor condition. Multiple cells showing degradation.',
        issues_reported: 'Charging fault detected, high temperature, reduced capacity',
        recommendations: 'URGENT: Replace battery within 1 week. Risk of complete failure.',
        photos: [],
        report_date: new Date(Date.now() - 259200000).toISOString().split('T')[0], // 3 days ago
        reporter_id: user.id
      }
    ];

    // Insert sample reports
    const { data, error } = await supabase
      .from('battery_reports')
      .insert(sampleReports)
      .select();

    if (error) {
      console.error('❌ Error creating sample battery reports:', error);
      return false;
    }

    console.log('✅ Successfully created', data?.length || 0, 'sample battery reports');
    return true;

  } catch (error) {
    console.error('❌ Failed to create sample battery reports:', error);
    return false;
  }
}

// Make function available globally for console access
if (typeof window !== 'undefined') {
  (window as any).createSampleBatteryReports = createSampleBatteryReports;
}

export default createSampleBatteryReports;
