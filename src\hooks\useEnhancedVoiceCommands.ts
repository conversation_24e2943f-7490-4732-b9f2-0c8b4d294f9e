import { useToast } from '@/hooks/use-toast';
import { use<PERSON>ang<PERSON>hain } from '@/hooks/useLangChain';
import { AINavigationTask, aiVisualNavigation, NavigationStep } from '@/lib/ai-visual-navigation';
import { browserCompatibility } from '@/utils/browserCompatibility';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface VoiceCommand {
  pattern: RegExp;
  action: (matches: RegExpMatchArray, context: VoiceContext) => Promise<void>;
  description: string;
  category: 'navigation' | 'task' | 'ai' | 'data' | 'system';
}

interface VoiceContext {
  currentPage: string;
  userRole: string;
  isAuthenticated: boolean;
  availableElements: any[];
}

export const useEnhancedVoiceCommands = () => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastCommand, setLastCommand] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  const [aiMode, setAiMode] = useState<'assistant' | 'navigator' | 'executor'>('assistant');
  const [isInitialized, setIsInitialized] = useState(false);
  
  const navigate = useNavigate();
  const { toast } = useToast();
  const { sendMessage } = useLangChain();
  
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);

  // Initialize speech recognition and synthesis
  useEffect(() => {
    // Check browser compatibility first
    const capabilities = browserCompatibility.getCapabilities();

    if (capabilities.speechRecognition) {
      try {
        // Enhanced browser compatibility check
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

        if (!SpeechRecognition) {
          throw new Error('SpeechRecognition not available');
        }

        recognitionRef.current = new SpeechRecognition();

      recognitionRef.current.continuous = true;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      // Set maxAlternatives safely
      try {
        recognitionRef.current.maxAlternatives = 5;
      } catch (error) {
        console.warn('🎤 maxAlternatives not supported, using default');
      }

      // Enhanced accuracy settings - no grammar assignment to avoid errors
        console.log('🎤 Speech recognition initialized successfully');

      // Add event listeners for better accuracy
      recognitionRef.current.onstart = () => {
        console.log('🎤 Voice recognition started');
        setIsListening(true);
      };

      recognitionRef.current.onend = () => {
        console.log('🎤 Voice recognition ended');
        setIsListening(false);
      };

      recognitionRef.current.onerror = (event) => {
        console.error('🎤 Voice recognition error:', event.error);
        setIsListening(false);

        // Handle specific errors
        switch (event.error) {
          case 'network':
            toast({
              title: "Network Error",
              description: "Voice recognition requires internet connection.",
              variant: "destructive",
            });
            break;
          case 'not-allowed':
            toast({
              title: "Microphone Access Denied",
              description: "Please allow microphone access for voice commands.",
              variant: "destructive",
            });
            break;
          case 'no-speech':
            // Don't show error for no speech, just restart
            if (isListening) {
              setTimeout(() => startListening(), 1000);
            }
            break;
          default:
            toast({
              title: "Voice Recognition Error",
              description: `Error: ${event.error}. Please try again.`,
              variant: "destructive",
            });
        }
      };

      recognitionRef.current.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          const confidence = event.results[i][0].confidence;

          if (event.results[i].isFinal) {
            // Only process high-confidence results
            if (confidence > 0.7) {
              finalTranscript += transcript;
            }
          } else {
            interimTranscript += transcript;
          }
        }

        if (finalTranscript) {
          console.log('🎤 Final transcript:', finalTranscript, 'Confidence:', event.results[event.results.length - 1][0].confidence);
          processVoiceCommand(finalTranscript.trim());
        }
      };

      setIsInitialized(true);
      } catch (error) {
        console.error('🎤 Failed to initialize speech recognition:', error);
        setIsInitialized(false);
        toast({
          title: "Voice Recognition Error",
          description: "Failed to initialize voice recognition. Please check browser permissions.",
          variant: "destructive",
        });
      }
    } else {
      console.warn('⚠️ Speech Recognition not supported in this browser');
      toast({
        title: "Voice Commands Limited",
        description: "Speech recognition is not supported in this browser. Use manual commands instead.",
        variant: "destructive",
      });
    }

    if (capabilities.speechSynthesis) {
      try {
        synthRef.current = window.speechSynthesis;
        console.log('🔊 Speech synthesis initialized successfully');
      } catch (error) {
        console.error('🔊 Failed to initialize speech synthesis:', error);
      }
    } else {
      console.warn('⚠️ Speech Synthesis not supported in this browser');
    }

    // Initialize AI visual navigation with error handling
    aiVisualNavigation.initialize().catch((error) => {
      console.error('Failed to initialize AI navigation:', error);
      toast({
        title: "AI Navigation Limited",
        description: "Some AI features may not work in this browser.",
        variant: "destructive",
      });
    });

    return () => {
      aiVisualNavigation.cleanup();
    };
  }, [toast]);

  // Enhanced voice commands with AI navigation
  const voiceCommands: VoiceCommand[] = [
    // AI Navigation Commands
    {
      pattern: /^(ai|assistant),?\s*(navigate to|go to|open)\s+(.+)$/i,
      action: async (matches, context) => {
        const destination = matches[3];
        await executeAINavigation('navigate', destination, context);
      },
      description: 'AI navigate to [page/section]',
      category: 'ai'
    },
    
    {
      pattern: /^(ai|assistant),?\s*(click|press|tap)\s+(.+)$/i,
      action: async (matches, context) => {
        const target = matches[3];
        await executeAINavigation('click', target, context);
      },
      description: 'AI click [button/link]',
      category: 'ai'
    },
    
    {
      pattern: /^(ai|assistant),?\s*(fill|enter|type)\s+(.+)\s+(in|into)\s+(.+)$/i,
      action: async (matches, context) => {
        const value = matches[3];
        const field = matches[5];
        await executeAINavigation('fill', `${field}:${value}`, context);
      },
      description: 'AI fill [value] in [field]',
      category: 'ai'
    },
    
    {
      pattern: /^(ai|assistant),?\s*(create|add|new)\s+(.+)$/i,
      action: async (matches, context) => {
        const item = matches[3];
        await executeAITask('create', item, context);
      },
      description: 'AI create [item]',
      category: 'ai'
    },
    
    {
      pattern: /^(ai|assistant),?\s*(show me|demonstrate|guide me through)\s+(.+)$/i,
      action: async (matches, context) => {
        const process = matches[3];
        await executeAIDemo(process, context);
      },
      description: 'AI show me [process]',
      category: 'ai'
    },

    // Enhanced Navigation Commands
    {
      pattern: /^(go to|navigate to|open)\s+(dashboard|home)$/i,
      action: async (matches, context) => {
        const role = context.userRole || 'staff';
        navigate(`/dashboard/${role}`);
        await speak(`Navigating to ${role} dashboard`);
      },
      description: 'Navigate to dashboard',
      category: 'navigation'
    },
    
    {
      pattern: /^(go to|navigate to|open)\s+(tasks?|my tasks?)$/i,
      action: async (matches, context) => {
        navigate('/dashboard/staff/my-tasks');
        await speak('Opening your tasks');
      },
      description: 'Navigate to tasks',
      category: 'navigation'
    },
    
    {
      pattern: /^(go to|navigate to|open)\s+(projects?)$/i,
      action: async (matches, context) => {
        navigate('/dashboard/staff/projects');
        await speak('Opening projects');
      },
      description: 'Navigate to projects',
      category: 'navigation'
    },

    // Task Management Commands
    {
      pattern: /^(create|add|new)\s+(task|project|report)$/i,
      action: async (matches, context) => {
        const itemType = matches[2];
        await executeTaskCreation(itemType, context);
      },
      description: 'Create new [task/project/report]',
      category: 'task'
    },
    
    {
      pattern: /^(update|change|modify)\s+(task|project)\s+status\s+to\s+(.+)$/i,
      action: async (matches, context) => {
        const itemType = matches[2];
        const status = matches[3];
        await executeStatusUpdate(itemType, status, context);
      },
      description: 'Update [item] status to [status]',
      category: 'task'
    },

    // System Commands
    {
      pattern: /^(help|what can you do|commands)$/i,
      action: async (matches, context) => {
        await showVoiceHelp();
      },
      description: 'Show available commands',
      category: 'system'
    },
    
    {
      pattern: /^(stop|cancel|abort)$/i,
      action: async (matches, context) => {
        setIsListening(false);
        await speak('Voice commands stopped');
      },
      description: 'Stop voice commands',
      category: 'system'
    }
  ];

  // Execute AI navigation with visual feedback
  const executeAINavigation = async (action: string, target: string, context: VoiceContext) => {
    setIsProcessing(true);
    
    try {
      const elements = aiVisualNavigation.scanPageElements();
      
      let navigationTask: AINavigationTask;
      
      switch (action) {
        case 'navigate':
          navigationTask = await createNavigationTask(target, context);
          break;
        case 'click':
          navigationTask = await createClickTask(target, elements);
          break;
        case 'fill':
          navigationTask = await createFillTask(target, elements);
          break;
        default:
          throw new Error(`Unknown action: ${action}`);
      }
      
      await aiVisualNavigation.executeNavigationTask(navigationTask);
      
      toast({
        title: "AI Navigation Complete",
        description: `Successfully executed: ${navigationTask.description}`,
      });
      
    } catch (error) {
      console.error('AI Navigation failed:', error);
      await speak(`Navigation failed: ${error.message}`);
      
      toast({
        title: "Navigation Failed",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Create navigation task based on destination
  const createNavigationTask = async (destination: string, context: VoiceContext): Promise<AINavigationTask> => {
    const steps: NavigationStep[] = [];
    
    // Use AI to determine navigation path
    const aiResponse = await sendMessage(
      `How do I navigate to "${destination}" from the current page? Current context: ${JSON.stringify(context)}`
    );
    
    // Parse AI response and create steps
    if (destination.toLowerCase().includes('task')) {
      steps.push({
        id: 'nav-1',
        action: 'click',
        target: 'a[href*="tasks"], button[data-testid*="tasks"]',
        description: 'Clicking on tasks navigation'
      });
    } else if (destination.toLowerCase().includes('project')) {
      steps.push({
        id: 'nav-1',
        action: 'click',
        target: 'a[href*="projects"], button[data-testid*="projects"]',
        description: 'Clicking on projects navigation'
      });
    }
    
    return {
      id: `nav-${Date.now()}`,
      name: `Navigate to ${destination}`,
      description: `AI navigation to ${destination}`,
      steps,
      context: JSON.stringify(context),
      expectedOutcome: `Successfully navigated to ${destination}`
    };
  };

  // Create click task for specific element
  const createClickTask = async (target: string, elements: any[]): Promise<AINavigationTask> => {
    // Find matching element
    const matchingElement = elements.find(el => 
      el.text.toLowerCase().includes(target.toLowerCase()) ||
      el.attributes['aria-label']?.toLowerCase().includes(target.toLowerCase())
    );
    
    if (!matchingElement) {
      throw new Error(`Could not find element: ${target}`);
    }
    
    return {
      id: `click-${Date.now()}`,
      name: `Click ${target}`,
      description: `AI clicking on ${target}`,
      steps: [
        {
          id: 'click-1',
          action: 'highlight',
          target: matchingElement.selector,
          duration: 1500,
          description: `Highlighting ${target}`
        },
        {
          id: 'click-2',
          action: 'click',
          target: matchingElement.selector,
          description: `Clicking ${target}`
        }
      ],
      context: JSON.stringify({ target, element: matchingElement }),
      expectedOutcome: `Successfully clicked ${target}`
    };
  };

  // Create fill task for form fields
  const createFillTask = async (instruction: string, elements: any[]): Promise<AINavigationTask> => {
    const [field, value] = instruction.split(':');
    
    const inputElement = elements.find(el => 
      el.type === 'input' && (
        el.text.toLowerCase().includes(field.toLowerCase()) ||
        el.attributes.placeholder?.toLowerCase().includes(field.toLowerCase()) ||
        el.attributes.name?.toLowerCase().includes(field.toLowerCase())
      )
    );
    
    if (!inputElement) {
      throw new Error(`Could not find input field: ${field}`);
    }
    
    return {
      id: `fill-${Date.now()}`,
      name: `Fill ${field}`,
      description: `AI filling ${field} with ${value}`,
      steps: [
        {
          id: 'fill-1',
          action: 'highlight',
          target: inputElement.selector,
          duration: 1000,
          description: `Highlighting ${field} field`
        },
        {
          id: 'fill-2',
          action: 'type',
          target: inputElement.selector,
          value: value,
          description: `Typing "${value}" into ${field}`
        }
      ],
      context: JSON.stringify({ field, value, element: inputElement }),
      expectedOutcome: `Successfully filled ${field} with ${value}`
    };
  };

  // Execute AI task creation
  const executeAITask = async (action: string, item: string, context: VoiceContext) => {
    setIsProcessing(true);
    
    try {
      await speak(`Creating ${item} for you`);
      
      // Navigate to appropriate creation page
      if (item.toLowerCase().includes('task')) {
        navigate('/dashboard/staff/my-tasks');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Look for "Add Task" or "New Task" button
        const elements = aiVisualNavigation.scanPageElements();
        const addButton = elements.find(el => 
          el.text.toLowerCase().includes('add') || 
          el.text.toLowerCase().includes('new') ||
          el.text.toLowerCase().includes('create')
        );
        
        if (addButton) {
          await aiVisualNavigation.executeNavigationTask({
            id: `create-task-${Date.now()}`,
            name: 'Create New Task',
            description: 'AI creating a new task',
            steps: [
              {
                id: 'create-1',
                action: 'click',
                target: addButton.selector,
                description: 'Clicking create task button'
              }
            ],
            context: JSON.stringify(context),
            expectedOutcome: 'Task creation form opened'
          });
        }
      }
      
    } catch (error) {
      await speak(`Failed to create ${item}: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Execute AI demonstration
  const executeAIDemo = async (process: string, context: VoiceContext) => {
    setIsProcessing(true);
    
    try {
      await speak(`Let me show you how to ${process}`);
      
      // Create demo task based on process
      const demoSteps: NavigationStep[] = [
        {
          id: 'demo-1',
          action: 'speak',
          value: `I'll demonstrate ${process} step by step`,
          description: 'Introduction'
        }
      ];
      
      // Add process-specific steps
      if (process.toLowerCase().includes('task')) {
        demoSteps.push(
          {
            id: 'demo-2',
            action: 'highlight',
            target: 'nav, [role="navigation"]',
            duration: 2000,
            description: 'First, we navigate using the sidebar'
          },
          {
            id: 'demo-3',
            action: 'speak',
            value: 'This is where you can find all your navigation options',
            description: 'Explanation'
          }
        );
      }
      
      await aiVisualNavigation.executeNavigationTask({
        id: `demo-${Date.now()}`,
        name: `Demo: ${process}`,
        description: `AI demonstrating ${process}`,
        steps: demoSteps,
        context: JSON.stringify(context),
        expectedOutcome: `Successfully demonstrated ${process}`
      });
      
    } catch (error) {
      await speak(`Demo failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  // Process voice command
  const processVoiceCommand = useCallback(async (transcript: string) => {
    setLastCommand(transcript);
    setIsProcessing(true);
    
    const context: VoiceContext = {
      currentPage: window.location.pathname,
      userRole: 'staff', // Get from auth context
      isAuthenticated: true, // Get from auth context
      availableElements: aiVisualNavigation.scanPageElements()
    };
    
    try {
      // Find matching command
      const matchedCommand = voiceCommands.find(cmd => cmd.pattern.test(transcript));
      
      if (matchedCommand) {
        const matches = transcript.match(matchedCommand.pattern);
        if (matches) {
          await matchedCommand.action(matches, context);
        }
      } else {
        // Use AI to interpret unknown commands
        const aiResponse = await sendMessage(
          `Interpret this voice command and suggest an action: "${transcript}". Current page: ${context.currentPage}`
        );
        
        await speak(`I understand you want to ${transcript}. ${aiResponse}`);
      }
      
    } catch (error) {
      console.error('Voice command processing failed:', error);
      await speak('Sorry, I could not process that command');
    } finally {
      setIsProcessing(false);
    }
  }, [voiceCommands, sendMessage]);

  // Start listening with enhanced error handling
  const startListening = useCallback(() => {
    if (!recognitionRef.current || !isInitialized) {
      toast({
        title: "Speech Recognition Not Available",
        description: "Speech recognition is not initialized or supported in your browser",
        variant: "destructive",
      });
      return;
    }

    setIsListening(true);
    
    recognitionRef.current.onresult = (event) => {
      const transcript = event.results[event.results.length - 1][0].transcript.trim();
      const confidence = event.results[event.results.length - 1][0].confidence;
      
      setConfidence(confidence);
      
      if (event.results[event.results.length - 1].isFinal && confidence > 0.7) {
        processVoiceCommand(transcript);
      }
    };
    
    recognitionRef.current.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setIsListening(false);
    };
    
    recognitionRef.current.onend = () => {
      if (isListening) {
        recognitionRef.current?.start(); // Restart if still listening
      }
    };
    
    recognitionRef.current.start();
  }, [isListening, processVoiceCommand]);

  // Stop listening
  const stopListening = useCallback(() => {
    setIsListening(false);
    recognitionRef.current?.stop();
  }, []);

  // Speak text with enhanced error handling
  const speak = useCallback(async (text: string): Promise<void> => {
    return new Promise((resolve) => {
      try {
        if (!synthRef.current || !text.trim()) {
          console.log(`🤖 AI: ${text}`);
          resolve();
          return;
        }

        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 0.8;
        utterance.volume = 0.8;

        utterance.onend = () => resolve();
        utterance.onerror = (error) => {
          console.warn('🔊 Speech synthesis error:', error);
          resolve();
        };

        synthRef.current.speak(utterance);
      } catch (error) {
        console.error('🔊 Failed to speak text:', error);
        console.log(`🤖 AI: ${text}`);
        resolve();
      }
    });
  }, []);

  // Show voice help
  const showVoiceHelp = async () => {
    const helpText = `Available voice commands: 
    Navigation: "Go to dashboard", "Open tasks", "Navigate to projects"
    AI Commands: "AI navigate to tasks", "AI click submit button", "AI fill John in name field"
    Task Management: "Create new task", "Update task status to completed"
    System: "Help", "Stop"`;
    
    await speak(helpText);
    
    toast({
      title: "Voice Commands Help",
      description: "Check console for full list of available commands",
    });
    
    console.log('Voice Commands:', voiceCommands.map(cmd => cmd.description));
  };

  return {
    isListening,
    isProcessing,
    lastCommand,
    confidence,
    aiMode,
    setAiMode,
    isInitialized,
    startListening,
    stopListening,
    speak,
    processVoiceCommand
  };
};
