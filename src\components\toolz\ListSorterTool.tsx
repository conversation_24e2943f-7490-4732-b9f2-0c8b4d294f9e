import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Tabs<PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Copy, RotateCcw, ArrowUpDown, List, Shuffle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const ListSorterTool: React.FC = () => {
  const [inputList, setInputList] = useState('');
  const [outputList, setOutputList] = useState('');
  const [sortType, setSortType] = useState('alphabetical');
  const [sortOrder, setSortOrder] = useState('asc');
  const [delimiter, set<PERSON><PERSON>miter] = useState('newline');
  const { toast } = useToast();

  const parseList = (text: string): string[] => {
    if (!text.trim()) return [];
    
    switch (delimiter) {
      case 'newline':
        return text.split('\n').map(item => item.trim()).filter(item => item.length > 0);
      case 'comma':
        return text.split(',').map(item => item.trim()).filter(item => item.length > 0);
      case 'semicolon':
        return text.split(';').map(item => item.trim()).filter(item => item.length > 0);
      case 'space':
        return text.split(/\s+/).filter(item => item.length > 0);
      case 'tab':
        return text.split('\t').map(item => item.trim()).filter(item => item.length > 0);
      default:
        return text.split('\n').map(item => item.trim()).filter(item => item.length > 0);
    }
  };

  const formatList = (items: string[]): string => {
    switch (delimiter) {
      case 'newline':
        return items.join('\n');
      case 'comma':
        return items.join(', ');
      case 'semicolon':
        return items.join('; ');
      case 'space':
        return items.join(' ');
      case 'tab':
        return items.join('\t');
      default:
        return items.join('\n');
    }
  };

  const sortList = (items: string[]): string[] => {
    const sorted = [...items];
    
    switch (sortType) {
      case 'alphabetical':
        sorted.sort((a, b) => {
          const comparison = a.toLowerCase().localeCompare(b.toLowerCase());
          return sortOrder === 'asc' ? comparison : -comparison;
        });
        break;
      case 'length':
        sorted.sort((a, b) => {
          const comparison = a.length - b.length;
          return sortOrder === 'asc' ? comparison : -comparison;
        });
        break;
      case 'numerical':
        sorted.sort((a, b) => {
          const numA = parseFloat(a) || 0;
          const numB = parseFloat(b) || 0;
          const comparison = numA - numB;
          return sortOrder === 'asc' ? comparison : -comparison;
        });
        break;
      case 'reverse':
        return items.reverse();
      case 'random':
        for (let i = sorted.length - 1; i > 0; i--) {
          const j = Math.floor(Math.random() * (i + 1));
          [sorted[i], sorted[j]] = [sorted[j], sorted[i]];
        }
        break;
      default:
        break;
    }
    
    return sorted;
  };

  const handleSort = () => {
    const items = parseList(inputList);
    const sorted = sortList(items);
    const formatted = formatList(sorted);
    setOutputList(formatted);
  };

  const removeDuplicates = () => {
    const items = parseList(inputList);
    const unique = [...new Set(items)];
    const formatted = formatList(unique);
    setOutputList(formatted);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: "List copied to clipboard",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy list",
        variant: "destructive",
      });
    }
  };

  const clearAll = () => {
    setInputList('');
    setOutputList('');
  };

  const loadSample = () => {
    setInputList(`Apple
Banana
Cherry
Date
Elderberry
Fig
Grape
Honeydew
Kiwi
Lemon`);
  };

  const swapInputOutput = () => {
    const temp = inputList;
    setInputList(outputList);
    setOutputList(temp);
  };

  const items = parseList(inputList);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-blue-500 rounded-lg">
          <List className="h-6 w-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold">List Sorter</h2>
          <p className="text-muted-foreground">Sort and organize lists in various ways</p>
        </div>
      </div>

      {/* Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Sort Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort Type</label>
              <Select value={sortType} onValueChange={setSortType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="alphabetical">Alphabetical</SelectItem>
                  <SelectItem value="length">By Length</SelectItem>
                  <SelectItem value="numerical">Numerical</SelectItem>
                  <SelectItem value="reverse">Reverse Order</SelectItem>
                  <SelectItem value="random">Random Shuffle</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort Order</label>
              <Select value={sortOrder} onValueChange={setSortOrder} disabled={sortType === 'reverse' || sortType === 'random'}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asc">Ascending</SelectItem>
                  <SelectItem value="desc">Descending</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">Delimiter</label>
              <Select value={delimiter} onValueChange={setDelimiter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newline">New Line</SelectItem>
                  <SelectItem value="comma">Comma</SelectItem>
                  <SelectItem value="semicolon">Semicolon</SelectItem>
                  <SelectItem value="space">Space</SelectItem>
                  <SelectItem value="tab">Tab</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Input Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Input List
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={loadSample}>
                Load Sample
              </Button>
              <Button variant="outline" size="sm" onClick={swapInputOutput} disabled={!outputList}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Swap
              </Button>
              <Button variant="outline" size="sm" onClick={clearAll}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder={`Enter your list here...\n${delimiter === 'newline' ? 'One item per line' : `Items separated by ${delimiter}`}`}
            value={inputList}
            onChange={(e) => setInputList(e.target.value)}
            className="min-h-[150px] font-mono"
          />
          <div className="flex items-center justify-between mt-2">
            <Badge variant="outline">
              {items.length} items
            </Badge>
            <div className="flex gap-2">
              <Button onClick={handleSort} disabled={items.length === 0}>
                <ArrowUpDown className="h-4 w-4 mr-2" />
                Sort List
              </Button>
              <Button onClick={removeDuplicates} disabled={items.length === 0} variant="outline">
                Remove Duplicates
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Output Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Sorted List
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(outputList)}
              disabled={!outputList}
            >
              <Copy className="h-4 w-4 mr-2" />
              Copy
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-muted/50 rounded-md p-3 min-h-[150px] font-mono text-sm">
            {outputList || (
              <span className="text-muted-foreground italic">
                Sorted list will appear here...
              </span>
            )}
          </div>
          {outputList && (
            <div className="flex items-center justify-between mt-2">
              <Badge variant="outline">
                {parseList(outputList).length} items
              </Badge>
              <Badge variant="secondary">
                {sortType === 'alphabetical' ? 'Alphabetically' : 
                 sortType === 'length' ? 'By Length' :
                 sortType === 'numerical' ? 'Numerically' :
                 sortType === 'reverse' ? 'Reversed' :
                 'Randomly'} Sorted
              </Badge>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Sort Types</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold mb-2">Alphabetical:</h4>
              <p className="text-muted-foreground mb-3">
                Sorts items in alphabetical order (case-insensitive).
              </p>
              <h4 className="font-semibold mb-2">By Length:</h4>
              <p className="text-muted-foreground">
                Sorts items by their character length.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-2">Numerical:</h4>
              <p className="text-muted-foreground mb-3">
                Sorts items as numbers (non-numeric items treated as 0).
              </p>
              <h4 className="font-semibold mb-2">Random Shuffle:</h4>
              <p className="text-muted-foreground">
                Randomly rearranges the items in the list.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ListSorterTool;
