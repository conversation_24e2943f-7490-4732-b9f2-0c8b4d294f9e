import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { TelecomSiteForm } from "./TelecomSiteForm";
import { TelecomSiteCard } from "./TelecomSiteCard";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

interface TelecomSite {
  id: string;
  name: string;
  location: string;
  status: "active" | "maintenance" | "inactive" | "planning" | "completed";
  manager_id?: string;
  client_list?: string[];
  region?: string;
  address?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export const TelecomSiteList = () => {
  const [sites, setSites] = useState<TelecomSite[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const fetchSites = async () => {
    try {
      const { data, error } = await supabase
        .from('telecom_sites')
        .select(`
          id,
          name,
          location,
          status,
          manager_id,
          client_list,
          region,
          address,
          notes,
          created_at,
          updated_at
        `)
        .order('name');

      if (error) throw error;
      
      // Ensure status values match the expected type and provide fallback
      const processedSites = (data || []).map(site => ({
        ...site,
        name: site.name || 'Unnamed Site',
        location: site.location || 'Unknown Location',
        status: (site.status && ['active', 'maintenance', 'inactive', 'planning', 'completed'].includes(site.status) 
          ? site.status 
          : 'planning') as "active" | "maintenance" | "inactive" | "planning" | "completed"
      }));
      
      setSites(processedSites);
    } catch (error) {
      console.error('Error fetching sites:', error);
      toast({
        title: "Error",
        description: "Failed to fetch telecom sites",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSites();
  }, []);

  const handleAddSite = async (formData: FormData) => {
    try {
      const siteData = {
        name: formData.get('name') as string,
        location: formData.get('location') as string,
        status: 'planning',
        region: formData.get('region') as string,
        address: formData.get('address') as string,
        notes: formData.get('notes') as string
      };

      const { error } = await supabase
        .from('telecom_sites')
        .insert([siteData]);

      if (error) throw error;
      
      await fetchSites();
      toast({
        title: "Success",
        description: "Telecom site added successfully",
      });
    } catch (error) {
      console.error('Error adding site:', error);
      toast({
        title: "Error",
        description: "Failed to add telecom site",  
        variant: "destructive",
      });
    }
  };

  const handleUpdateSite = async (siteId: string, formData: FormData) => {
    try {
      const updates = {
        name: formData.get('name') as string,
        location: formData.get('location') as string,
        status: formData.get('status') as string,
        region: formData.get('region') as string,
        address: formData.get('address') as string,
        notes: formData.get('notes') as string,
      };

      const { error } = await supabase
        .from('telecom_sites')
        .update(updates)
        .eq('id', siteId);

      if (error) throw error;
      
      await fetchSites();
      toast({
        title: "Success",
        description: "Telecom site updated successfully",
      });
    } catch (error) {
      console.error('Error updating site:', error);
      toast({
        title: "Error",
        description: "Failed to update telecom site",
        variant: "destructive",
      });
    }
  };

  const handleDeleteSite = async (siteId: string) => {
    try {
      const { error } = await supabase
        .from('telecom_sites')
        .delete()
        .eq('id', siteId);

      if (error) throw error;
      
      await fetchSites();
      toast({
        title: "Success",
        description: "Telecom site deleted successfully",
      });
    } catch (error) {
      console.error('Error deleting site:', error);
      toast({
        title: "Error",
        description: "Failed to delete telecom site",
        variant: "destructive",
      });
    }
  };

  // Transform sites for TelecomSiteCard component
  const transformedSites = sites.map(site => ({
    id: site.id,
    siteId: site.id.slice(-8),
    name: site.name,
    location: site.location,
    status: site.status as "active" | "maintenance" | "inactive",
    workers: [],
    progress: 0,
    budget: 0
  }));

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Telecom Sites</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            Loading telecom sites...
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Telecom Sites Management</CardTitle>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Site
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Telecom Site</DialogTitle>
            </DialogHeader>
            <TelecomSiteForm type="add" onSubmit={handleAddSite} />
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent className="space-y-4">
        {transformedSites.map((site) => (
          <TelecomSiteCard
            key={site.id}
            site={site}
            onUpdate={handleUpdateSite}
            onDelete={handleDeleteSite}
          />
        ))}
        {transformedSites.length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            No telecom sites found. Add your first site to get started.
          </div>
        )}
      </CardContent>
    </Card>
  );
};
