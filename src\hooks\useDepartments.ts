import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/integrations/supabase/client'

export const useDepartments = () => {
  const { data: departments = [], isLoading, error } = useQuery({
    queryKey: ['departments'],
    queryFn: async () => {
      console.log('Fetching departments...')
      const { data, error } = await supabase
        .from('departments')
        .select('*')
        .order('name')

      if (error) {
        console.error('Error fetching departments:', error)
        throw error
      }

      console.log('Departments fetched:', data)
      return data || []
    },
    retry: 3,
    retryDelay: 1000
  })

  return {
    departments,
    isLoading,
    error
  }
}
