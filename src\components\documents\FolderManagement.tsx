import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import {
  Folder,
  FolderPlus,
  MoreVertical,
  Edit,
  Trash2,
  Users,
  Lock,
  Globe,
  Zap,
  Shield,
  RefreshCw,
  AlertCircle
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { FolderAPI, SYSTEM_FOLDER_TYPES, type FolderData } from "@/lib/folder-api";

interface Folder {
  id: string;
  name: string;
  parent_folder_id?: string;
  access_level: string;
  document_count?: number;
  created_at?: string;
}

interface FolderManagementProps {
  folders: Folder[];
  onCreateFolder: (name: string, parentId?: string) => void;
  onRefresh: () => void;
}

export const FolderManagement = ({
  folders,
  onCreateFolder,
  onRefresh
}: FolderManagementProps) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [selectedParent, setSelectedParent] = useState<string>("");
  const [accessLevel, setAccessLevel] = useState<string>("department");
  const [selectedSystemType, setSelectedSystemType] = useState<string>("");
  const [managers, setManagers] = useState<any[]>([]);
  const [selectedManager, setSelectedManager] = useState<string>("");
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isInitializing, setIsInitializing] = useState(false);
  const { toast } = useToast();

  // Load managers on component mount
  useEffect(() => {
    loadManagers();
  }, []);

  const loadManagers = async () => {
    const { data } = await FolderAPI.getAvailableManagers();
    setManagers(data || []);
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      let result;

      if (selectedParent === "root-level" || !selectedParent) {
        // Create root level folder with system type
        const folderData: FolderData = {
          name: newFolderName.trim(),
          type: selectedSystemType || 'custom',
          access_level: accessLevel as any,
          manager_id: selectedManager || undefined
        };

        result = await FolderAPI.createRootFolder(folderData);
      } else {
        // Create subfolder
        const folderData: FolderData & { parent_folder_id: string } = {
          name: newFolderName.trim(),
          parent_folder_id: selectedParent,
          access_level: accessLevel as any,
          manager_id: selectedManager || undefined
        };

        result = await FolderAPI.createSubfolder(folderData);
      }

      if (result.error) {
        toast({
          title: "Error",
          description: result.error.message,
          variant: "destructive",
        });
      } else {
        setShowSuccessModal(true);
        onRefresh();
        resetForm();
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create folder",
        variant: "destructive",
      });
    }
  };

  const resetForm = () => {
    setNewFolderName("");
    setSelectedParent("");
    setAccessLevel("department");
    setSelectedSystemType("");
    setSelectedManager("");
    setIsCreateDialogOpen(false);
  };

  const handleInitializeSystemFolders = async () => {
    setIsInitializing(true);
    try {
      const result = await FolderAPI.initializeSystemFolders();

      if (result.errors.length > 0) {
        toast({
          title: "Partial Success",
          description: `Created ${result.created} folders. ${result.errors.length} errors occurred.`,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: `Successfully created ${result.created} system folders.`,
        });
      }

      onRefresh();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to initialize system folders",
        variant: "destructive",
      });
    } finally {
      setIsInitializing(false);
    }
  };

  const getAccessIcon = (level: string) => {
    switch (level) {
      case 'public': return <Globe className="h-4 w-4" />;
      case 'private': return <Lock className="h-4 w-4" />;
      case 'department': return <Users className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getAccessColor = (level: string) => {
    switch (level) {
      case 'public': return 'bg-green-100 text-green-800';
      case 'private': return 'bg-red-100 text-red-800';
      case 'department': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get root folders (no parent)
  const rootFolders = folders.filter(f => !f.parent_folder_id);
  
  // Get subfolders for each root folder
  const getSubfolders = (parentId: string) => 
    folders.filter(f => f.parent_folder_id === parentId);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            Folder Management
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handleInitializeSystemFolders}
              disabled={isInitializing}
            >
              {isInitializing ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 mr-2" />
              )}
              Initialize System Folders
            </Button>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <FolderPlus className="h-4 w-4 mr-2" />
                New Folder
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Folder</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="folderName">Folder Name</Label>
                  <Input
                    id="folderName"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    placeholder="Enter folder name"
                  />
                </div>

                <div>
                  <Label htmlFor="parentFolder">Parent Folder (Optional)</Label>
                  <Select value={selectedParent} onValueChange={setSelectedParent}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select parent folder" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="root-level">Root Level</SelectItem>
                      {rootFolders.map((folder) => (
                        <SelectItem key={folder.id} value={folder.id}>
                          {folder.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {(selectedParent === "root-level" || !selectedParent) && (
                  <div>
                    <Label htmlFor="systemType">System Type</Label>
                    <Select value={selectedSystemType} onValueChange={setSelectedSystemType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select system type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="custom">Custom Folder</SelectItem>
                        {SYSTEM_FOLDER_TYPES.map((type) => (
                          <SelectItem key={type.type} value={type.type}>
                            <div className="flex items-center gap-2">
                              <span>{type.icon}</span>
                              <span>{type.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div>
                  <Label htmlFor="manager">Manager (Optional)</Label>
                  <Select value={selectedManager} onValueChange={(value) => setSelectedManager(value === 'none' ? '' : value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select manager" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No Manager</SelectItem>
                      {managers.map((manager) => (
                        <SelectItem key={manager.id} value={manager.id}>
                          {manager.full_name || manager.email} ({manager.role})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="accessLevel">Access Level</Label>
                  <Select value={accessLevel} onValueChange={setAccessLevel}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="public">
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4" />
                          Public
                        </div>
                      </SelectItem>
                      <SelectItem value="department">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4" />
                          Department
                        </div>
                      </SelectItem>
                      <SelectItem value="private">
                        <div className="flex items-center gap-2">
                          <Lock className="h-4 w-4" />
                          Private
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button 
                    variant="outline" 
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button onClick={handleCreateFolder}>
                    Create Folder
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-4">
            {rootFolders.length === 0 ? (
              <div className="text-center py-8">
                <Folder className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No folders created yet</p>
              </div>
            ) : (
              <div className="space-y-4">
                {rootFolders.map((folder) => (
                  <div key={folder.id} className="space-y-2">
                    {/* Root Folder */}
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Folder className="h-6 w-6 text-blue-500" />
                        <div>
                          <h4 className="font-medium">{folder.name}</h4>
                          <p className="text-sm text-muted-foreground">
                            {folder.document_count || 0} items
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant="outline" 
                          className={`flex items-center gap-1 ${getAccessColor(folder.access_level)}`}
                        >
                          {getAccessIcon(folder.access_level)}
                          {folder.access_level}
                        </Badge>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Rename
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    
                    {/* Subfolders */}
                    {getSubfolders(folder.id).map((subfolder) => (
                      <div key={subfolder.id} className="ml-8 flex items-center justify-between p-3 border rounded-lg bg-muted/50">
                        <div className="flex items-center gap-3">
                          <Folder className="h-5 w-5 text-blue-400" />
                          <div>
                            <h5 className="font-medium text-sm">{subfolder.name}</h5>
                            <p className="text-xs text-muted-foreground">
                              {subfolder.document_count || 0} items
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant="outline" 
                            className={`flex items-center gap-1 text-xs ${getAccessColor(subfolder.access_level)}`}
                          >
                            {getAccessIcon(subfolder.access_level)}
                            {subfolder.access_level}
                          </Badge>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-3 w-3" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>
                                <Edit className="h-4 w-4 mr-2" />
                                Rename
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Success Modal */}
      <SuccessModal
        open={showSuccessModal}
        onOpenChange={setShowSuccessModal}
        title="Folder Created Successfully!"
        description="Your folder has been created and is ready for document organization."
        variant="success"
        showConfetti={true}
      />
    </div>
  );
};