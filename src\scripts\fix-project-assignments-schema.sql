-- Fix project_assignments table schema to support both old and new formats
-- This script ensures compatibility between different schema versions

-- First, check if the table exists and what columns it has
DO $$
DECLARE
    has_project_id BOOLEAN := FALSE;
    has_project_name BOOLEAN := FALSE;
BEGIN
    -- Check if project_id column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'project_assignments' 
        AND column_name = 'project_id'
        AND table_schema = 'public'
    ) INTO has_project_id;
    
    -- Check if project_name column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'project_assignments' 
        AND column_name = 'project_name'
        AND table_schema = 'public'
    ) INTO has_project_name;
    
    RAISE NOTICE 'project_id exists: %, project_name exists: %', has_project_id, has_project_name;
    
    -- If we have project_name but not project_id, add project_id column
    IF has_project_name AND NOT has_project_id THEN
        RAISE NOTICE 'Adding project_id column to support new schema';
        ALTER TABLE public.project_assignments 
        ADD COLUMN IF NOT EXISTS project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE;
        
        -- Add new columns for enhanced project assignment tracking
        ALTER TABLE public.project_assignments 
        ADD COLUMN IF NOT EXISTS assigned_by UUID REFERENCES public.profiles(id),
        ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'team_member' CHECK (role IN ('team_member', 'lead', 'contributor', 'reviewer')),
        ADD COLUMN IF NOT EXISTS hours_allocated INTEGER DEFAULT 40,
        ADD COLUMN IF NOT EXISTS hours_worked INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS last_progress_update TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS notes TEXT;
        
        -- Update status column to include new values
        ALTER TABLE public.project_assignments 
        DROP CONSTRAINT IF EXISTS project_assignments_status_check;
        
        ALTER TABLE public.project_assignments 
        ADD CONSTRAINT project_assignments_status_check 
        CHECK (status IN ('pending', 'assigned', 'in_progress', 'completed', 'cancelled', 'on_hold'));
        
        -- Make project_name nullable since we now have project_id
        ALTER TABLE public.project_assignments 
        ALTER COLUMN project_name DROP NOT NULL;
        
        RAISE NOTICE 'Enhanced project_assignments table with new columns';
    END IF;
    
    -- If we have project_id but not project_name, add project_name for backward compatibility
    IF has_project_id AND NOT has_project_name THEN
        RAISE NOTICE 'Adding project_name column for backward compatibility';
        ALTER TABLE public.project_assignments 
        ADD COLUMN IF NOT EXISTS project_name TEXT;
        
        -- Populate project_name from projects table
        UPDATE public.project_assignments 
        SET project_name = p.name
        FROM public.projects p
        WHERE public.project_assignments.project_id = p.id
        AND public.project_assignments.project_name IS NULL;
        
        RAISE NOTICE 'Populated project_name from projects table';
    END IF;
    
    -- If neither exists, create the table with both columns
    IF NOT has_project_id AND NOT has_project_name THEN
        RAISE NOTICE 'Creating project_assignments table with hybrid schema';
        
        CREATE TABLE IF NOT EXISTS public.project_assignments (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
            project_name TEXT,
            description TEXT,
            assigned_to UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
            assigned_by UUID REFERENCES public.profiles(id),
            department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
            role TEXT DEFAULT 'team_member' CHECK (role IN ('team_member', 'lead', 'contributor', 'reviewer')),
            start_date TIMESTAMP WITH TIME ZONE NOT NULL,
            end_date TIMESTAMP WITH TIME ZONE,
            status TEXT CHECK (status IN ('pending', 'assigned', 'in_progress', 'completed', 'cancelled', 'on_hold')) DEFAULT 'pending',
            priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
            progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
            hours_allocated INTEGER DEFAULT 40,
            hours_worked INTEGER DEFAULT 0,
            last_progress_update TIMESTAMP WITH TIME ZONE,
            notes TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
            CONSTRAINT project_assignments_project_reference CHECK (
                (project_id IS NOT NULL) OR (project_name IS NOT NULL)
            )
        );
        
        -- Enable RLS
        ALTER TABLE public.project_assignments ENABLE ROW LEVEL SECURITY;
        
        -- Create policies
        CREATE POLICY "project_assignments_select" ON public.project_assignments
            FOR SELECT TO authenticated USING (
                auth.uid() = assigned_to OR
                auth.uid() = assigned_by OR
                auth.uid() IN (
                    SELECT manager_id FROM public.departments WHERE id = department_id
                ) OR
                auth.uid() IN (
                    SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
                )
            );
        
        CREATE POLICY "project_assignments_insert" ON public.project_assignments
            FOR INSERT TO authenticated WITH CHECK (
                auth.uid() IN (
                    SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
                )
            );
        
        CREATE POLICY "project_assignments_update" ON public.project_assignments
            FOR UPDATE TO authenticated USING (
                auth.uid() = assigned_to OR
                auth.uid() = assigned_by OR
                auth.uid() IN (
                    SELECT manager_id FROM public.departments WHERE id = department_id
                ) OR
                auth.uid() IN (
                    SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
                )
            );
        
        CREATE POLICY "project_assignments_delete" ON public.project_assignments
            FOR DELETE TO authenticated USING (
                auth.uid() = assigned_by OR
                auth.uid() IN (
                    SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
                )
            );
        
        -- Create updated_at trigger
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        
        CREATE TRIGGER update_project_assignments_updated_at
            BEFORE UPDATE ON public.project_assignments
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        
        RAISE NOTICE 'Created project_assignments table with hybrid schema';
    END IF;
    
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_assignments_project_id ON public.project_assignments(project_id);
CREATE INDEX IF NOT EXISTS idx_project_assignments_assigned_to ON public.project_assignments(assigned_to);
CREATE INDEX IF NOT EXISTS idx_project_assignments_status ON public.project_assignments(status);
CREATE INDEX IF NOT EXISTS idx_project_assignments_department_id ON public.project_assignments(department_id);

-- Log completion
DO $$
BEGIN
    RAISE NOTICE 'project_assignments schema fix completed successfully';
END $$;
