import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.38.4';
import { withCors, createCorsResponse, corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const resendApiKey = Deno.env.get('RESEND_API_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Email template styles
const baseStyles = `
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 600px;
  margin: 0 auto;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const headerStyles = `
  padding: 30px 20px;
  text-align: center;
  color: white;
`;

const contentStyles = `
  padding: 30px 20px;
  color: #374151;
  line-height: 1.6;
`;

const buttonStyles = `
  display: inline-block;
  background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  color: white;
  padding: 12px 24px;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 600;
  margin: 10px 5px;
`;

function getEmailTemplate(type: string, data: any = {}) {
  const frontendUrl = Deno.env.get('FRONTEND_URL') || 'https://ai.ctnigeria.com';

  const templates = {
    system_launch: {
      subject: `🚀 ${data.systemName || 'CTNL AI WORK-BOARD'} is Now Live - Your Workforce Management System is Ready!`,
      html: `
        <div style="${baseStyles}">
          <div style="${headerStyles} background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);">
            <h1 style="margin: 0; font-size: 28px; background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
              ${data.systemName || 'CTNL AI WORK-BOARD'}
            </h1>
            <p style="margin: 0.5rem 0 0 0; color: #ccc; font-size: 16px;">Your AI-Powered Workforce Management System is Ready!</p>
          </div>
          <div style="${contentStyles}">
            <h2 style="color: #1a1a1a; font-size: 24px; margin-bottom: 20px;">🎉 Welcome to the Future of Workforce Management!</h2>
            
            <p style="font-size: 16px; margin-bottom: 25px;">
              We're excited to announce that <strong>CTNL AI WORK-BOARD</strong> is now fully operational and ready to transform your workforce management experience.
            </p>

            <div style="background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; padding: 25px; margin: 25px 0;">
              <h3 style="color: #2d3748; font-size: 18px; margin-bottom: 15px;">🚀 What's Available Now:</h3>
              <ul style="color: #4a5568; font-size: 14px; line-height: 1.8; margin: 0; padding-left: 20px;">
                <li><strong>AI-Powered Document Analysis</strong> - Upload and analyze documents with advanced AI</li>
                <li><strong>Intelligent Chat Assistant</strong> - Get instant help and insights</li>
                <li><strong>Smart Time Tracking</strong> - Location-aware clock in/out with device detection</li>
                <li><strong>Role-Based Dashboards</strong> - Customized interfaces for all user roles</li>
                <li><strong>Project Management</strong> - Kanban boards with progress tracking</li>
                <li><strong>Real-Time Notifications</strong> - Email and in-app alerts</li>
                <li><strong>Advanced Reporting</strong> - Comprehensive analytics and insights</li>
                <li><strong>Mobile-Responsive Design</strong> - Works perfectly on all devices</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${data.manualUrl || frontendUrl + '/user-manual.html'}" style="${buttonStyles}">
                📖 View Complete User Manual
              </a>
              <br>
              <a href="${data.dashboardUrl || frontendUrl + '/dashboard'}" style="${buttonStyles}">
                🚀 Access Your Dashboard
              </a>
            </div>

            <p style="color: #4a5568; font-size: 14px; margin-top: 30px;">
              Thank you for choosing <strong>CTNL AI WORK-BOARD</strong>. We're committed to providing you with the most advanced workforce management solution available.
            </p>
          </div>
          <div style="background: #2d3748; color: #a0aec0; padding: 20px; text-align: center; font-size: 12px;">
            <p style="margin: 0;">
              © 2024 CTNL AI WORK-BOARD. All rights reserved.<br>
              Powered by advanced AI technology for workforce management excellence.
            </p>
          </div>
        </div>
      `,
    },

    test_notification: {
      subject: 'CTNL AI WORK-BOARD - Test Email',
      html: `
        <div style="${baseStyles}">
          <div style="${headerStyles} background: linear-gradient(135deg, #059669 0%, #10b981 100%);">
            <h1 style="margin: 0; font-size: 24px;">🧪 Test Email</h1>
          </div>
          <div style="${contentStyles}">
            <p style="font-size: 16px;">Hello ${data.userName || 'User'},</p>
            <p>This is a test email from <strong>CTNL AI WORK-BOARD</strong>.</p>
            <p>${data.testMessage || 'Your email notifications are working correctly!'}</p>
            <div style="text-align: center; margin: 20px 0;">
              <a href="${frontendUrl}" style="${buttonStyles}">Visit Dashboard</a>
            </div>
          </div>
        </div>
      `,
    },
  };

  return templates[type as keyof typeof templates] || {
    subject: `Notification from CTNL AI WORK-BOARD`,
    html: `
      <div style="${baseStyles}">
        <div style="${headerStyles} background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
          <h1 style="margin: 0; font-size: 24px;">🔔 Notification</h1>
        </div>
        <div style="${contentStyles}">
          <p style="font-size: 16px;">Hello,</p>
          <p>You have received a notification from CTNL AI WORK-BOARD.</p>
          <div style="text-align: center; margin: 20px 0;">
            <a href="${frontendUrl}" style="${buttonStyles}">Visit Dashboard</a>
          </div>
        </div>
      </div>
    `,
  };
}

async function sendEmail(to: string[], subject: string, html: string) {
  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'CTNL AI WORK-BOARD <<EMAIL>>',
        to,
        subject,
        html,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Resend API error: ${response.status} - ${error}`);
    }

    const result = await response.json();
    return { success: true, data: result };
  } catch (error) {
    console.error('Email sending error:', error);
    return { success: false, error: error.message };
  }
}

const handler = async (req: Request): Promise<Response> => {
  try {
    const { type, recipients, data } = await req.json();

    if (!type || !recipients || !Array.isArray(recipients)) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: type, recipients' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    const template = getEmailTemplate(type, data);
    const emailResult = await sendEmail(recipients, template.subject, template.html);

    return new Response(
      JSON.stringify({
        success: emailResult.success,
        message: emailResult.success ? 'Email sent successfully' : 'Email sending failed',
        data: emailResult.data,
        error: emailResult.error,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: emailResult.success ? 200 : 500,
      }
    );
  } catch (error) {
    console.error('Function error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', details: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
};

Deno.serve(withCors(handler));
