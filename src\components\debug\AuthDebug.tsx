/**
 * Auth Debug Component
 * Helps diagnose authentication issues and infinite loops
 */

import React from 'react';
import { useAuth } from '@/components/auth/AuthProvider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';

export const AuthDebug: React.FC = () => {
  const { 
    user, 
    session, 
    userProfile, 
    loading, 
    initialized, 
    isAuthenticated,
    signOut 
  } = useAuth();

  const getStatusIcon = (condition: boolean) => {
    return condition ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusBadge = (condition: boolean, trueText: string, falseText: string) => {
    return (
      <Badge variant={condition ? "default" : "destructive"}>
        {condition ? trueText : falseText}
      </Badge>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Authentication Debug Panel
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Auth State Overview */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              {getStatusIcon(initialized)}
              <span className="text-sm">Initialized</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(!loading)}
              <span className="text-sm">Not Loading</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(isAuthenticated)}
              <span className="text-sm">Authenticated</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(!!userProfile)}
              <span className="text-sm">Has Profile</span>
            </div>
          </div>

          {/* Detailed Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Auth Status</h4>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>Initialized:</span>
                  {getStatusBadge(initialized, "Yes", "No")}
                </div>
                <div className="flex justify-between">
                  <span>Loading:</span>
                  {getStatusBadge(!loading, "No", "Yes")}
                </div>
                <div className="flex justify-between">
                  <span>Authenticated:</span>
                  {getStatusBadge(isAuthenticated, "Yes", "No")}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">User Data</h4>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span>User ID:</span>
                  <span className="text-sm text-muted-foreground">
                    {user?.id ? `${user.id.substring(0, 8)}...` : 'None'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Email:</span>
                  <span className="text-sm text-muted-foreground">
                    {user?.email || 'None'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Role:</span>
                  <span className="text-sm text-muted-foreground">
                    {userProfile?.role || 'None'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Session Info */}
          <div className="space-y-2">
            <h4 className="font-medium">Session Information</h4>
            <div className="bg-muted p-3 rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="font-medium">Access Token:</span>
                  <span className="ml-2 text-muted-foreground">
                    {session?.access_token ? 'Present' : 'Missing'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Refresh Token:</span>
                  <span className="ml-2 text-muted-foreground">
                    {session?.refresh_token ? 'Present' : 'Missing'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Expires At:</span>
                  <span className="ml-2 text-muted-foreground">
                    {session?.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'Unknown'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Token Type:</span>
                  <span className="ml-2 text-muted-foreground">
                    {session?.token_type || 'Unknown'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Profile Data */}
          {userProfile && (
            <div className="space-y-2">
              <h4 className="font-medium">User Profile</h4>
              <div className="bg-muted p-3 rounded-lg">
                <pre className="text-xs overflow-auto">
                  {JSON.stringify(userProfile, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Troubleshooting Actions */}
          <div className="space-y-2">
            <h4 className="font-medium">Troubleshooting Actions</h4>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
              {isAuthenticated && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={signOut}
                >
                  Sign Out
                </Button>
              )}
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  localStorage.clear();
                  sessionStorage.clear();
                  window.location.reload();
                }}
              >
                Clear Storage & Reload
              </Button>
            </div>
          </div>

          {/* Recommendations */}
          <div className="space-y-2">
            <h4 className="font-medium">Recommendations</h4>
            <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
              <ul className="text-sm space-y-1">
                {!initialized && (
                  <li>• Auth is not initialized - check for JavaScript errors</li>
                )}
                {loading && (
                  <li>• Auth is still loading - this should resolve automatically</li>
                )}
                {isAuthenticated && !userProfile && (
                  <li>• User is authenticated but profile is missing - check database</li>
                )}
                {!isAuthenticated && initialized && (
                  <li>• User is not authenticated - sign in required</li>
                )}
                {isAuthenticated && userProfile && (
                  <li className="text-green-600 dark:text-green-400">
                    • Auth state is healthy - ready for dashboard
                  </li>
                )}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
