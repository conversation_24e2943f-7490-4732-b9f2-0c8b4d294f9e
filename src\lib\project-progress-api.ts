import { supabase } from "@/integrations/supabase/client";

export interface ProjectAssignment {
  id: string;
  project_id: string;
  assigned_to: string;
  assigned_by?: string;
  role: 'team_member' | 'lead' | 'contributor' | 'reviewer';
  status: 'assigned' | 'in_progress' | 'completed' | 'on_hold' | 'cancelled';
  start_date: string;
  end_date?: string;
  progress_percentage: number;
  hours_allocated: number;
  hours_worked: number;
  last_progress_update?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  project?: any;
  assigned_to_profile?: any;
  assigned_by_profile?: any;
}

export interface ProjectProgressUpdate {
  id: string;
  project_id: string;
  assignment_id?: string;
  updated_by: string;
  previous_progress: number;
  new_progress: number;
  hours_worked: number;
  update_type: 'progress' | 'status_change' | 'milestone' | 'blocker' | 'completion';
  description?: string;
  attachments?: any[];
  created_at: string;
  updated_by_profile?: any;
}

export interface ProjectMilestone {
  id: string;
  project_id: string;
  title: string;
  description?: string;
  target_date?: string;
  completion_date?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  progress_percentage: number;
  created_by?: string;
  created_at: string;
  updated_at: string;
  created_by_profile?: any;
}

export interface ProjectWithProgress {
  id: string;
  name: string;
  description?: string;
  status: string;
  priority?: string;
  progress_percentage: number;
  overall_progress: number;
  budget?: number;
  budget_spent?: number;
  start_date?: string;
  end_date?: string;
  completion_date?: string;
  manager_id?: string;
  department_id?: string;
  client_name?: string;
  estimated_hours: number;
  actual_hours: number;
  team_size: number;
  created_at: string;
  updated_at: string;
  manager?: any;
  department?: any;
  assignments?: ProjectAssignment[];
  milestones?: ProjectMilestone[];
  recent_updates?: ProjectProgressUpdate[];
}

export class ProjectProgressAPI {
  /**
   * Get all projects assigned to current user (staff view)
   */
  static async getMyProjects(): Promise<{ data: ProjectWithProgress[]; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Try the new schema first (with project_id foreign key)
      let { data, error } = await supabase
        .from('project_assignments')
        .select(`
          *,
          project:projects!project_id (
            *,
            manager:profiles!manager_id(id, full_name, email, role),
            department:departments!department_id(id, name)
          ),
          assigned_to_profile:profiles!assigned_to(id, full_name, email, role),
          assigned_by_profile:profiles!assigned_by(id, full_name, email, role)
        `)
        .eq('assigned_to', user.id)
        .in('status', ['assigned', 'in_progress'])
        .order('created_at', { ascending: false });

      // If the new schema fails (400, 406, or PGRST116 errors), fall back to old schema
      if (error && (error.code === 'PGRST116' || error.status === 400 || error.status === 406)) {
        console.log('Falling back to old schema for project_assignments, error:', error);
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('project_assignments')
          .select(`
            *,
            assigned_to_profile:profiles!assigned_to(id, full_name, email, role)
          `)
          .eq('assigned_to', user.id)
          .in('status', ['assigned', 'in_progress'])
          .order('created_at', { ascending: false });

        data = fallbackData;
        error = fallbackError;
      }

      // If both schemas fail, try a basic query without relationships
      if (error) {
        console.log('Both schemas failed, trying basic query without relationships');
        const { data: basicData, error: basicError } = await supabase
          .from('project_assignments')
          .select('*')
          .eq('assigned_to', user.id)
          .in('status', ['assigned', 'in_progress'])
          .order('created_at', { ascending: false });

        data = basicData;
        error = basicError;
      }

      if (error) throw error;

      // Transform data to match ProjectWithProgress interface
      const projects: ProjectWithProgress[] = data?.map((assignment: any) => {
        // Handle both new schema (with project object) and old schema (with project_name)
        if (assignment.project) {
          // New schema with project relationship
          return {
            ...assignment.project,
            assignments: [assignment],
            user_assignment: assignment
          };
        } else {
          // Old schema with project_name - create a minimal project object
          return {
            id: assignment.project_name || 'unknown',
            name: assignment.project_name || 'Unknown Project',
            description: assignment.description || '',
            status: 'active',
            assignments: [assignment],
            user_assignment: assignment
          };
        }
      }) || [];

      return { data: projects, error: null };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Get all projects with team progress (manager/admin view)
   */
  static async getAllProjectsWithProgress(filters?: {
    status?: string;
    managerId?: string;
    departmentId?: string;
  }): Promise<{ data: ProjectWithProgress[]; error: any }> {
    try {
      let query = supabase
        .from('projects')
        .select(`
          *,
          manager:profiles!manager_id(id, full_name, email, role),
          department:departments!department_id(id, name),
          assignments:project_assignments(
            *,
            assigned_to_profile:profiles!assigned_to(id, full_name, email, role),
            assigned_by_profile:profiles!assigned_by(id, full_name, email, role)
          ),
          milestones:project_milestones(*),
          recent_updates:project_progress_updates(
            *,
            updated_by_profile:profiles!updated_by(id, full_name, email, role)
          )
        `);

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }
      if (filters?.managerId) {
        query = query.eq('manager_id', filters.managerId);
      }
      if (filters?.departmentId) {
        query = query.eq('department_id', filters.departmentId);
      }

      const { data, error } = await query
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      return { data: data || [], error: null };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Get project by ID with full progress details
   */
  static async getProjectWithProgress(projectId: string): Promise<{ data: ProjectWithProgress | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          manager:profiles!manager_id(id, full_name, email, role),
          department:departments!department_id(id, name),
          assignments:project_assignments(
            *,
            assigned_to_profile:profiles!assigned_to(id, full_name, email, role),
            assigned_by_profile:profiles!assigned_by(id, full_name, email, role)
          ),
          milestones:project_milestones(
            *,
            created_by_profile:profiles!created_by(id, full_name, email, role)
          ),
          recent_updates:project_progress_updates(
            *,
            updated_by_profile:profiles!updated_by(id, full_name, email, role)
          )
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Update project assignment progress
   */
  static async updateAssignmentProgress(
    assignmentId: string,
    progressData: {
      progress_percentage: number;
      hours_worked?: number;
      status?: string;
      notes?: string;
    }
  ): Promise<{ data: ProjectAssignment | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Get current assignment to track previous progress
      const { data: currentAssignment } = await supabase
        .from('project_assignments')
        .select('progress_percentage, project_id, project_name')
        .eq('id', assignmentId)
        .single();

      // Update assignment
      let { data, error } = await supabase
        .from('project_assignments')
        .update({
          ...progressData,
          last_progress_update: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', assignmentId)
        .select(`
          *,
          project:projects!project_id(*),
          assigned_to_profile:profiles!assigned_to(id, full_name, email, role)
        `)
        .single();

      // If the new schema fails, fall back to old schema
      if (error && error.code === 'PGRST116') {
        console.log('Falling back to old schema for assignment update');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('project_assignments')
          .update({
            ...progressData,
            last_progress_update: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', assignmentId)
          .select(`
            *,
            assigned_to_profile:profiles!assigned_to(id, full_name, email, role)
          `)
          .single();

        data = fallbackData;
        error = fallbackError;
      }

      if (error) throw error;

      // Create progress update record
      if (currentAssignment && data) {
        await supabase
          .from('project_progress_updates')
          .insert({
            project_id: currentAssignment.project_id,
            assignment_id: assignmentId,
            updated_by: user.id,
            previous_progress: currentAssignment.progress_percentage,
            new_progress: progressData.progress_percentage,
            hours_worked: progressData.hours_worked || 0,
            update_type: progressData.status === 'completed' ? 'completion' : 'progress',
            description: progressData.notes || `Progress updated to ${progressData.progress_percentage}%`
          });
      }

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Create project progress update
   */
  static async createProgressUpdate(updateData: {
    project_id: string;
    assignment_id?: string;
    new_progress: number;
    hours_worked?: number;
    update_type?: string;
    description?: string;
  }): Promise<{ data: ProjectProgressUpdate | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('project_progress_updates')
        .insert({
          ...updateData,
          updated_by: user.id,
          previous_progress: 0, // Will be updated by trigger if needed
          hours_worked: updateData.hours_worked || 0,
          update_type: updateData.update_type || 'progress'
        })
        .select(`
          *,
          updated_by_profile:profiles!updated_by(id, full_name, email, role)
        `)
        .single();

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }

  /**
   * Get project progress updates
   */
  static async getProjectProgressUpdates(projectId: string): Promise<{ data: ProjectProgressUpdate[]; error: any }> {
    try {
      const { data, error } = await supabase
        .from('project_progress_updates')
        .select(`
          *,
          updated_by_profile:profiles!updated_by(id, full_name, email, role),
          assignment:project_assignments!assignment_id(
            id,
            assigned_to_profile:profiles!assigned_to(id, full_name, email, role)
          )
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;

      return { data: data || [], error: null };
    } catch (error: any) {
      return { data: [], error: { message: error.message } };
    }
  }

  /**
   * Assign user to project
   */
  static async assignUserToProject(assignmentData: {
    project_id: string;
    assigned_to: string;
    role?: string;
    hours_allocated?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<{ data: ProjectAssignment | null; error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Try new schema first
      let { data, error } = await supabase
        .from('project_assignments')
        .insert({
          ...assignmentData,
          assigned_by: user.id,
          role: assignmentData.role || 'team_member',
          status: 'assigned',
          progress_percentage: 0,
          hours_worked: 0,
          hours_allocated: assignmentData.hours_allocated || 40
        })
        .select(`
          *,
          project:projects!project_id(*),
          assigned_to_profile:profiles!assigned_to(id, full_name, email, role),
          assigned_by_profile:profiles!assigned_by(id, full_name, email, role)
        `)
        .single();

      // If new schema fails, try old schema
      if (error && error.code === 'PGRST116') {
        console.log('Falling back to old schema for assignment creation');

        // Get project name for old schema
        const { data: project } = await supabase
          .from('projects')
          .select('name')
          .eq('id', assignmentData.project_id)
          .single();

        const { data: fallbackData, error: fallbackError } = await supabase
          .from('project_assignments')
          .insert({
            project_name: project?.name || 'Unknown Project',
            assigned_to: assignmentData.assigned_to,
            status: 'assigned',
            progress_percentage: 0,
            start_date: assignmentData.start_date || new Date().toISOString()
          })
          .select(`
            *,
            assigned_to_profile:profiles!assigned_to(id, full_name, email, role)
          `)
          .single();

        data = fallbackData;
        error = fallbackError;
      }

      if (error) throw error;

      return { data, error: null };
    } catch (error: any) {
      return { data: null, error: { message: error.message } };
    }
  }
}
