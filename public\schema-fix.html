<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema Fix</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .error-box {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        .fix-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .fix-section h3 {
            margin: 0 0 15px 0;
            color: #28a745;
        }
        .button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .log {
            background: #f1f3f4;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Database Schema Fix</h1>
            <p>Fix column mismatches causing dashboard data loading failures</p>
        </div>
        
        <div class="error-box">
            <h3>🚨 Schema Issues Detected</h3>
            <p><strong>The following column mismatches are causing dashboard failures:</strong></p>
            <ul>
                <li>❌ <strong>invoices:</strong> Missing 'amount' column (has 'total_amount')</li>
                <li>❌ <strong>expense_reports:</strong> 'expense_date' not-null constraint violation</li>
                <li>❌ <strong>reports:</strong> Missing 'priority' column</li>
                <li>❌ <strong>time_logs:</strong> Missing 'description' column</li>
                <li>❌ <strong>notifications:</strong> Missing 'read' column</li>
            </ul>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progress" style="width: 0%;"></div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button id="fixBtn" class="button" onclick="fixSchemaIssues()">
                🔧 Fix All Schema Issues
            </button>
            <button id="testBtn" class="button" onclick="testDashboard()" disabled>
                🧪 Test Dashboard
            </button>
        </div>
        
        <div id="logContainer" style="display: none;">
            <h3>🔍 Fix Progress:</h3>
            <div id="log" class="log"></div>
        </div>
        
        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        const SUPABASE_URL = "https://dvflgnqwbsjityrowatf.supabase.co";
        const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR2ZmxnbnF3YnNqaXR5cm93YXRmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0OTMyMDYsImV4cCI6MjA1MDA2OTIwNn0.PMygdfhFV-47gia7VXA1h66AQGeLmTRTfEPbLwvho28";
        
        const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const logContainer = document.getElementById('logContainer');
            logContainer.style.display = 'block';
            logDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function updateProgress(percent) {
            document.getElementById('progress').style.width = percent + '%';
        }
        
        async function fixSchemaIssues() {
            const fixBtn = document.getElementById('fixBtn');
            const testBtn = document.getElementById('testBtn');
            
            fixBtn.disabled = true;
            fixBtn.textContent = '🔄 Fixing Schema...';
            
            try {
                log('🔧 Starting database schema fixes...');
                updateProgress(10);
                
                // Get current user
                const { data: { user } } = await supabase.auth.getUser();
                const userId = user?.id || 'system';
                
                log('👤 User ID: ' + userId);
                updateProgress(20);
                
                // Fix 1: Add missing columns to existing tables
                log('📋 Adding missing columns...');
                const { error: columnsError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Add missing columns to invoices table
                        ALTER TABLE public.invoices 
                        ADD COLUMN IF NOT EXISTS subtotal DECIMAL(10,2),
                        ADD COLUMN IF NOT EXISTS balance_amount DECIMAL(10,2) DEFAULT 0;
                        
                        -- Update existing invoices to have proper values
                        UPDATE public.invoices 
                        SET subtotal = total_amount, 
                            balance_amount = CASE WHEN payment_status = 'paid' THEN 0 ELSE total_amount END
                        WHERE subtotal IS NULL;
                        
                        -- Add missing columns to expense_reports table
                        ALTER TABLE public.expense_reports 
                        ADD COLUMN IF NOT EXISTS expense_date DATE DEFAULT CURRENT_DATE;
                        
                        -- Update existing expense_reports to have expense_date
                        UPDATE public.expense_reports 
                        SET expense_date = CURRENT_DATE 
                        WHERE expense_date IS NULL;
                        
                        -- Add missing columns to reports table
                        ALTER TABLE public.reports 
                        ADD COLUMN IF NOT EXISTS priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent'));
                        
                        -- Add missing columns to time_logs table
                        ALTER TABLE public.time_logs 
                        ADD COLUMN IF NOT EXISTS description TEXT;
                        
                        -- Add missing columns to notifications table
                        ALTER TABLE public.notifications 
                        ADD COLUMN IF NOT EXISTS read BOOLEAN DEFAULT FALSE,
                        ADD COLUMN IF NOT EXISTS is_read BOOLEAN DEFAULT FALSE;
                    `
                });
                
                if (columnsError) {
                    log('❌ Column addition failed: ' + columnsError.message);
                } else {
                    log('✅ Missing columns added successfully');
                }
                updateProgress(50);
                
                // Fix 2: Ensure NOT NULL constraints are properly handled
                log('🔒 Fixing NOT NULL constraints...');
                const { error: constraintsError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Make sure invoices have required NOT NULL columns
                        ALTER TABLE public.invoices 
                        ALTER COLUMN subtotal SET NOT NULL,
                        ALTER COLUMN balance_amount SET NOT NULL,
                        ALTER COLUMN total_amount SET NOT NULL;
                        
                        -- Make sure expense_reports have required NOT NULL columns
                        ALTER TABLE public.expense_reports 
                        ALTER COLUMN expense_date SET NOT NULL;
                    `
                });
                
                if (constraintsError) {
                    log('⚠️ Constraint fixes had issues: ' + constraintsError.message);
                } else {
                    log('✅ NOT NULL constraints fixed');
                }
                updateProgress(70);
                
                // Fix 3: Insert sample data with correct schema
                log('📊 Inserting sample data with correct schema...');
                const { error: dataError } = await supabase.rpc('exec_sql', {
                    sql: `
                        -- Insert sample data with all required columns
                        INSERT INTO public.invoices (invoice_number, subtotal, balance_amount, total_amount, payment_status, due_date, created_by) VALUES
                        ('INV-FIX-001', 5000.00, 0.00, 5000.00, 'paid', CURRENT_DATE + INTERVAL '30 days', '${userId}'),
                        ('INV-FIX-002', 7500.00, 7500.00, 7500.00, 'pending', CURRENT_DATE + INTERVAL '15 days', '${userId}'),
                        ('INV-FIX-003', 3200.00, 3200.00, 3200.00, 'pending', CURRENT_DATE + INTERVAL '10 days', '${userId}')
                        ON CONFLICT (invoice_number) DO NOTHING;
                        
                        -- Insert sample expense reports with expense_date
                        INSERT INTO public.expense_reports (title, description, amount, category, expense_date, status, submitted_by) VALUES
                        ('Office Supplies Fix', 'Monthly office supplies', 450.00, 'office', CURRENT_DATE, 'approved', '${userId}'),
                        ('Travel Expenses Fix', 'Business trip', 1200.00, 'travel', CURRENT_DATE - INTERVAL '1 day', 'pending', '${userId}'),
                        ('Equipment Fix', 'New equipment', 2500.00, 'equipment', CURRENT_DATE - INTERVAL '2 days', 'approved', '${userId}')
                        ON CONFLICT DO NOTHING;
                        
                        -- Insert sample reports with priority
                        INSERT INTO public.reports (title, description, priority, status, submitted_by) VALUES
                        ('Monthly Report Fix', 'Monthly performance report', 'medium', 'submitted', '${userId}'),
                        ('Urgent Issue Fix', 'Critical system issue', 'high', 'under_review', '${userId}'),
                        ('Weekly Update Fix', 'Weekly team update', 'low', 'approved', '${userId}')
                        ON CONFLICT DO NOTHING;
                        
                        -- Insert sample time logs with description
                        INSERT INTO public.time_logs (user_id, clock_in, clock_out, description, total_hours) VALUES
                        ('${userId}', NOW() - INTERVAL '8 hours', NOW() - INTERVAL '1 hour', 'Dashboard development work', 7.0),
                        ('${userId}', NOW() - INTERVAL '16 hours', NOW() - INTERVAL '9 hours', 'Bug fixing and testing', 7.0),
                        ('${userId}', NOW() - INTERVAL '24 hours', NOW() - INTERVAL '17 hours', 'Schema optimization', 7.0)
                        ON CONFLICT DO NOTHING;
                        
                        -- Insert sample notifications with read status
                        INSERT INTO public.notifications (user_id, title, message, read, is_read, type) VALUES
                        ('${userId}', 'Schema Fixed', 'Database schema has been updated', false, false, 'system'),
                        ('${userId}', 'Dashboard Ready', 'Your dashboard is now working', false, false, 'info'),
                        ('${userId}', 'Welcome', 'Welcome to the updated system', true, true, 'welcome')
                        ON CONFLICT DO NOTHING;
                    `
                });
                
                if (dataError) {
                    log('⚠️ Sample data insertion had issues: ' + dataError.message);
                } else {
                    log('✅ Sample data inserted successfully');
                }
                updateProgress(90);
                
                // Fix 4: Verify the fixes
                log('🔍 Verifying schema fixes...');
                const { data: verifyData, error: verifyError } = await supabase.rpc('exec_sql', {
                    sql: `
                        SELECT 
                            'invoices' as table_name,
                            COUNT(*) as record_count
                        FROM public.invoices
                        WHERE subtotal IS NOT NULL AND balance_amount IS NOT NULL
                        
                        UNION ALL
                        
                        SELECT 
                            'expense_reports' as table_name,
                            COUNT(*) as record_count
                        FROM public.expense_reports
                        WHERE expense_date IS NOT NULL
                        
                        UNION ALL
                        
                        SELECT 
                            'reports' as table_name,
                            COUNT(*) as record_count
                        FROM public.reports
                        WHERE priority IS NOT NULL;
                    `
                });
                
                if (verifyError) {
                    log('❌ Verification failed: ' + verifyError.message);
                } else {
                    log('✅ Schema verification completed');
                    log('📊 Verification results: ' + JSON.stringify(verifyData, null, 2));
                }
                
                updateProgress(100);
                log('🎉 All schema fixes completed successfully!');
                
                // Show success message
                document.getElementById('results').innerHTML = `
                    <div class="fix-section">
                        <h3>✅ Schema Fixes Complete!</h3>
                        <p><strong>All database schema issues have been resolved:</strong></p>
                        <ul>
                            <li>✅ <strong>invoices:</strong> Added subtotal and balance_amount columns</li>
                            <li>✅ <strong>expense_reports:</strong> Added expense_date with default values</li>
                            <li>✅ <strong>reports:</strong> Added priority column</li>
                            <li>✅ <strong>time_logs:</strong> Added description column</li>
                            <li>✅ <strong>notifications:</strong> Added read and is_read columns</li>
                        </ul>
                        <p><strong>Your dashboard should now load data without errors!</strong></p>
                    </div>
                `;
                
                testBtn.disabled = false;
                
            } catch (error) {
                log('❌ Schema fix failed: ' + error.message);
                document.getElementById('results').innerHTML = `
                    <div class="error-box">
                        <h3>❌ Schema Fix Failed</h3>
                        <p>Error: ${error.message}</p>
                        <p>Please check the console for more details.</p>
                    </div>
                `;
            } finally {
                fixBtn.disabled = false;
                fixBtn.textContent = '🔧 Fix All Schema Issues';
            }
        }
        
        function testDashboard() {
            window.open('/dashboard/test', '_blank');
        }
        
        // Make functions global
        window.fixSchemaIssues = fixSchemaIssues;
        window.testDashboard = testDashboard;
    </script>
</body>
</html>
