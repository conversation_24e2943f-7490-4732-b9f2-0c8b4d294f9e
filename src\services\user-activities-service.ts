import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { SystemLogsService } from './system-logs-service';

/**
 * User Activities Service
 * Comprehensive user activity tracking with detailed logging and activity feeds
 */

export type ActivityType = 'create' | 'update' | 'delete' | 'view' | 'login' | 'logout' | 'assign' | 'complete' | 'approve' | 'reject' | 'comment' | 'upload' | 'download' | 'export' | 'import' | 'share' | 'archive' | 'restore';
export type ImpactLevel = 'low' | 'medium' | 'high' | 'critical';
export type EntityType = 'project' | 'task' | 'memo' | 'report' | 'user' | 'department' | 'time_log' | 'comment' | 'file' | 'system';

export interface UserActivity {
  id?: string;
  user_id?: string;
  activity_type: ActivityType;
  action: string;
  description?: string;
  entity_type?: EntityType;
  entity_id?: string;
  entity_name?: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  changes?: Record<string, any>;
  impact_level?: ImpactLevel;
  category?: string;
  subcategory?: string;
  tags?: string[];
  ip_address?: string;
  user_agent?: string;
  location?: string;
  device_info?: Record<string, any>;
  session_id?: string;
  request_id?: string;
  duration_ms?: number;
  success?: boolean;
  error_message?: string;
  metadata?: Record<string, any>;
  created_at?: string;
}

export interface ActivityFilter {
  user_id?: string;
  activity_type?: ActivityType[];
  entity_type?: EntityType[];
  impact_level?: ImpactLevel[];
  category?: string[];
  date_from?: string;
  date_to?: string;
  search?: string;
  success?: boolean;
  limit?: number;
  offset?: number;
}

export interface ActivityStats {
  total: number;
  by_type: Record<ActivityType, number>;
  by_entity: Record<EntityType, number>;
  by_impact: Record<ImpactLevel, number>;
  by_user: Array<{ user_id: string; user_name: string; count: number }>;
  recent_activities: UserActivity[];
  most_active_entities: Array<{ entity_type: EntityType; entity_name: string; count: number }>;
}

export class UserActivitiesService {
  private static sessionId: string = '';
  private static requestId: string = '';

  // Initialize activities service
  static initialize(): void {
    this.sessionId = SystemLogsService.getSessionId();
    this.requestId = SystemLogsService.getRequestId();
    
    console.log('📊 User Activities Service initialized');
  }

  // Log a user activity
  static async logActivity(
    activity_type: ActivityType,
    action: string,
    description?: string,
    entity_type?: EntityType,
    entity_id?: string,
    entity_name?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      const activity: UserActivity = {
        user_id: currentUser?.user_id,
        activity_type,
        action,
        description,
        entity_type,
        entity_id,
        entity_name,
        impact_level: this.determineImpactLevel(activity_type, entity_type),
        category: this.determineCategory(activity_type, entity_type),
        session_id: this.sessionId,
        request_id: this.requestId,
        ip_address: await this.getClientIP(),
        user_agent: navigator.userAgent,
        location: await this.getLocation(),
        device_info: this.getDeviceInfo(),
        success: true,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          user_role: currentUser?.role,
          url: window.location.href
        }
      };

      await this.insertActivity(activity);
      
      // Also log to system logs for important activities
      if (activity.impact_level === 'high' || activity.impact_level === 'critical') {
        await SystemLogsService.logBusinessEvent(
          action,
          entity_type || 'unknown',
          entity_id,
          description,
          metadata
        );
      }
      
    } catch (error) {
      console.error('Failed to log user activity:', error);
      await SystemLogsService.error('general', 'Failed to log user activity', error.toString());
    }
  }

  // Convenience methods for common activities
  static async logCreate(
    entity_type: EntityType,
    entity_id: string,
    entity_name: string,
    description?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logActivity(
      'create',
      `Created ${entity_type}`,
      description || `Created new ${entity_type}: ${entity_name}`,
      entity_type,
      entity_id,
      entity_name,
      metadata
    );
  }

  static async logUpdate(
    entity_type: EntityType,
    entity_id: string,
    entity_name: string,
    old_values?: Record<string, any>,
    new_values?: Record<string, any>,
    description?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const changes = this.calculateChanges(old_values, new_values);
    
    await this.logActivity(
      'update',
      `Updated ${entity_type}`,
      description || `Updated ${entity_type}: ${entity_name}`,
      entity_type,
      entity_id,
      entity_name,
      {
        ...metadata,
        old_values,
        new_values,
        changes
      }
    );
  }

  static async logDelete(
    entity_type: EntityType,
    entity_id: string,
    entity_name: string,
    description?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logActivity(
      'delete',
      `Deleted ${entity_type}`,
      description || `Deleted ${entity_type}: ${entity_name}`,
      entity_type,
      entity_id,
      entity_name,
      metadata
    );
  }

  static async logView(
    entity_type: EntityType,
    entity_id: string,
    entity_name: string,
    description?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logActivity(
      'view',
      `Viewed ${entity_type}`,
      description || `Viewed ${entity_type}: ${entity_name}`,
      entity_type,
      entity_id,
      entity_name,
      metadata
    );
  }

  static async logLogin(
    success: boolean = true,
    error_message?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const activity: UserActivity = {
      activity_type: 'login',
      action: 'User Login',
      description: success ? 'User logged in successfully' : 'User login failed',
      entity_type: 'user',
      impact_level: 'low',
      category: 'authentication',
      session_id: this.sessionId,
      request_id: this.requestId,
      success,
      error_message,
      metadata
    };

    await this.insertActivity(activity);
  }

  static async logLogout(metadata?: Record<string, any>): Promise<void> {
    await this.logActivity(
      'logout',
      'User Logout',
      'User logged out',
      'user',
      undefined,
      undefined,
      metadata
    );
  }

  static async logAssignment(
    entity_type: EntityType,
    entity_id: string,
    entity_name: string,
    assigned_to: string,
    assigned_to_name: string,
    description?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logActivity(
      'assign',
      `Assigned ${entity_type}`,
      description || `Assigned ${entity_type} "${entity_name}" to ${assigned_to_name}`,
      entity_type,
      entity_id,
      entity_name,
      {
        ...metadata,
        assigned_to,
        assigned_to_name
      }
    );
  }

  static async logCompletion(
    entity_type: EntityType,
    entity_id: string,
    entity_name: string,
    description?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.logActivity(
      'complete',
      `Completed ${entity_type}`,
      description || `Completed ${entity_type}: ${entity_name}`,
      entity_type,
      entity_id,
      entity_name,
      metadata
    );
  }

  // Get activities with filtering and pagination
  static async getActivities(filter: ActivityFilter = {}): Promise<{ data: UserActivity[]; count: number }> {
    try {
      let query = supabase
        .from('user_activities')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filter.user_id) {
        query = query.eq('user_id', filter.user_id);
      }

      if (filter.activity_type && filter.activity_type.length > 0) {
        query = query.in('activity_type', filter.activity_type);
      }

      if (filter.entity_type && filter.entity_type.length > 0) {
        query = query.in('entity_type', filter.entity_type);
      }

      if (filter.impact_level && filter.impact_level.length > 0) {
        query = query.in('impact_level', filter.impact_level);
      }

      if (filter.category && filter.category.length > 0) {
        query = query.in('category', filter.category);
      }

      if (filter.date_from) {
        query = query.gte('created_at', filter.date_from);
      }

      if (filter.date_to) {
        query = query.lte('created_at', filter.date_to);
      }

      if (filter.search) {
        query = query.or(`action.ilike.%${filter.search}%,description.ilike.%${filter.search}%,entity_name.ilike.%${filter.search}%`);
      }

      if (filter.success !== undefined) {
        query = query.eq('success', filter.success);
      }

      // Apply pagination
      const limit = filter.limit || 50;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      // Order by most recent first
      query = query.order('created_at', { ascending: false });

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error fetching activities:', error);
      await SystemLogsService.error('database', 'Failed to fetch user activities', error.toString());
      return { data: [], count: 0 };
    }
  }

  // Get user activity feed
  static async getUserActivityFeed(user_id: string, limit: number = 20): Promise<UserActivity[]> {
    try {
      const { data } = await supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', user_id)
        .order('created_at', { ascending: false })
        .limit(limit);

      return data || [];
    } catch (error) {
      console.error('Error fetching user activity feed:', error);
      return [];
    }
  }

  // Get entity activity history
  static async getEntityActivityHistory(entity_type: EntityType, entity_id: string): Promise<UserActivity[]> {
    try {
      const { data } = await supabase
        .from('user_activities')
        .select('*')
        .eq('entity_type', entity_type)
        .eq('entity_id', entity_id)
        .order('created_at', { ascending: false });

      return data || [];
    } catch (error) {
      console.error('Error fetching entity activity history:', error);
      return [];
    }
  }

  // Private helper methods
  private static async insertActivity(activity: UserActivity): Promise<void> {
    try {
      await supabase.from('user_activities').insert(activity);
    } catch (error) {
      console.error('Failed to insert user activity:', error);
    }
  }

  private static determineImpactLevel(activity_type: ActivityType, entity_type?: EntityType): ImpactLevel {
    // Critical activities
    if (activity_type === 'delete' && ['project', 'user', 'department'].includes(entity_type || '')) {
      return 'critical';
    }

    // High impact activities
    if (['create', 'delete', 'approve', 'reject'].includes(activity_type)) {
      return 'high';
    }

    // Medium impact activities
    if (['update', 'assign', 'complete'].includes(activity_type)) {
      return 'medium';
    }

    // Low impact activities (view, comment, etc.)
    return 'low';
  }

  private static determineCategory(activity_type: ActivityType, entity_type?: EntityType): string {
    if (entity_type) {
      return `${entity_type}_management`;
    }

    switch (activity_type) {
      case 'login':
      case 'logout':
        return 'authentication';
      case 'view':
        return 'navigation';
      case 'upload':
      case 'download':
        return 'file_management';
      default:
        return 'general';
    }
  }

  private static calculateChanges(old_values?: Record<string, any>, new_values?: Record<string, any>): Record<string, any> {
    if (!old_values || !new_values) {
      return {};
    }

    const changes: Record<string, any> = {};
    
    for (const key in new_values) {
      if (old_values[key] !== new_values[key]) {
        changes[key] = {
          from: old_values[key],
          to: new_values[key]
        };
      }
    }

    return changes;
  }

  private static async getClientIP(): Promise<string> {
    try {
      return 'Unknown';
    } catch (error) {
      return 'Unknown';
    }
  }

  private static async getLocation(): Promise<string> {
    try {
      return 'Unknown';
    } catch (error) {
      return 'Unknown';
    }
  }

  private static getDeviceInfo(): Record<string, any> {
    return {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screen: {
        width: screen.width,
        height: screen.height
      },
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      }
    };
  }

  // Generate new request ID for tracking related activities
  static generateRequestId(): string {
    this.requestId = crypto.randomUUID();
    return this.requestId;
  }

  // Get current session ID
  static getSessionId(): string {
    return this.sessionId;
  }

  // Get current request ID
  static getRequestId(): string {
    return this.requestId;
  }
}

export default UserActivitiesService;
