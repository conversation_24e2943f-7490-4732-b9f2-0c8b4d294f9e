
import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Edit, Trash2, User, Users, Settings } from 'lucide-react';
import { useTasks } from '@/hooks/useTasks';
import { useUserManagement } from '@/hooks/useUserManagement';
import { useAuth } from '@/components/auth/AuthProvider';

export const RoleBasedTaskManagement = () => {
  const { userProfile } = useAuth();
  const { tasks, loading: tasksLoading, createTask, updateTask, deleteTask } = useTasks();
  const { users, departments, loading: usersLoading, updateUserRole, createDepartment } = useUserManagement();
  const [showCreateTask, setShowCreateTask] = useState(false);
  const [showUserManagement, setShowUserManagement] = useState(false);
  const [taskFormData, setTaskFormData] = useState({
    title: '',
    description: '',
    priority: 'medium' as const,
    assigned_to_id: '',
    due_date: '',
    estimated_hours: 0,
    department_id: ''
  });

  const isAdmin = userProfile?.role === 'admin';
  const isManager = userProfile?.role === 'manager';

  // Filter tasks based on user role
  const getFilteredTasks = () => {
    if (isAdmin) return tasks;
    if (isManager) {
      return tasks.filter(task => 
        task.department_id === userProfile?.department_id ||
        task.created_by_id === userProfile?.id
      );
    }
    return tasks.filter(task => 
      task.assigned_to_id === userProfile?.id ||
      task.created_by_id === userProfile?.id
    );
  };

  const handleCreateTask = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = await createTask(taskFormData);
    if (result.success) {
      setShowCreateTask(false);
      setTaskFormData({
        title: '',
        description: '',
        priority: 'medium',
        assigned_to_id: '',
        due_date: '',
        estimated_hours: 0,
        department_id: ''
      });
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTasks = getFilteredTasks();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Task Management</h2>
        <div className="flex gap-2">
          {(isAdmin || isManager) && (
            <Button onClick={() => setShowCreateTask(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Task
            </Button>
          )}
          {isAdmin && (
            <Button variant="outline" onClick={() => setShowUserManagement(true)}>
              <Users className="h-4 w-4 mr-2" />
              Manage Users
            </Button>
          )}
        </div>
      </div>

      <Tabs defaultValue="tasks" className="w-full">
        <TabsList>
          <TabsTrigger value="tasks">My Tasks</TabsTrigger>
          {(isAdmin || isManager) && <TabsTrigger value="team">Team Tasks</TabsTrigger>}
          {isAdmin && <TabsTrigger value="all">All Tasks</TabsTrigger>}
          {isAdmin && <TabsTrigger value="users">User Management</TabsTrigger>}
        </TabsList>

        <TabsContent value="tasks">
          <div className="grid gap-4">
            {filteredTasks.filter(task => 
              task.assigned_to_id === userProfile?.id
            ).map((task) => (
              <Card key={task.id}>
                <CardContent className="p-4">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold">{task.title}</h3>
                        <Badge className={getPriorityColor(task.priority)}>
                          {task.priority}
                        </Badge>
                        <Badge className={getStatusColor(task.status)}>
                          {task.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      {task.description && (
                        <p className="text-muted-foreground mb-2">{task.description}</p>
                      )}
                      <div className="flex gap-4 text-sm text-muted-foreground">
                        {task.due_date && (
                          <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                        )}
                        {task.estimated_hours && (
                          <span>Est: {task.estimated_hours}h</span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {task.status !== 'completed' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateTask(task.id, { 
                            status: task.status === 'pending' ? 'in_progress' : 'completed' 
                          })}
                        >
                          {task.status === 'pending' ? 'Start' : 'Complete'}
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {(isAdmin || isManager) && (
          <TabsContent value="team">
            <div className="grid gap-4">
              {filteredTasks.filter(task => 
                task.department_id === userProfile?.department_id
              ).map((task) => (
                <Card key={task.id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{task.title}</h3>
                          <Badge className={getPriorityColor(task.priority)}>
                            {task.priority}
                          </Badge>
                          <Badge className={getStatusColor(task.status)}>
                            {task.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        {task.description && (
                          <p className="text-muted-foreground mb-2">{task.description}</p>
                        )}
                        <div className="flex gap-4 text-sm text-muted-foreground">
                          {task.assignee && (
                            <span>Assigned to: {task.assignee.full_name}</span>
                          )}
                          {task.due_date && (
                            <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        {isAdmin && (
                          <Button size="sm" variant="outline" onClick={() => deleteTask(task.id)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        )}

        {isAdmin && (
          <TabsContent value="all">
            <div className="grid gap-4">
              {tasks.map((task) => (
                <Card key={task.id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold">{task.title}</h3>
                          <Badge className={getPriorityColor(task.priority)}>
                            {task.priority}
                          </Badge>
                          <Badge className={getStatusColor(task.status)}>
                            {task.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        {task.description && (
                          <p className="text-muted-foreground mb-2">{task.description}</p>
                        )}
                        <div className="flex gap-4 text-sm text-muted-foregreen">
                          {task.assignee && (
                            <span>Assigned to: {task.assignee.full_name}</span>
                          )}
                          {task.due_date && (
                            <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => deleteTask(task.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        )}

        {isAdmin && (
          <TabsContent value="users">
            <div className="space-y-4">
              <div className="grid gap-4">
                {users.map((user) => (
                  <Card key={user.id}>
                    <CardContent className="p-4">
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-semibold">{user.full_name}</h3>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                          <div className="flex gap-2 mt-2">
                            <Badge variant="outline">{user.role}</Badge>
                            {user.department && (
                              <Badge variant="secondary">{user.department.name}</Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Select
                            value={user.role || ''}
                            onValueChange={(value) => updateUserRole(user.id, value, user.department_id || undefined)}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="admin">Admin</SelectItem>
                              <SelectItem value="manager">Manager</SelectItem>
                              <SelectItem value="staff">Staff</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>
        )}
      </Tabs>

      {showCreateTask && (
        <Card className="fixed inset-4 z-50 overflow-auto">
          <CardHeader>
            <CardTitle>Create New Task</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateTask} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={taskFormData.title}
                  onChange={(e) => setTaskFormData({ ...taskFormData, title: e.target.value })}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={taskFormData.description}
                  onChange={(e) => setTaskFormData({ ...taskFormData, description: e.target.value })}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="priority">Priority</Label>
                  <Select value={taskFormData.priority} onValueChange={(value: any) => setTaskFormData({ ...taskFormData, priority: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="assigned_to">Assign To</Label>
                  <Select value={taskFormData.assigned_to_id} onValueChange={(value) => setTaskFormData({ ...taskFormData, assigned_to_id: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select assignee" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.filter(user => 
                        isAdmin || (isManager && user.department_id === userProfile?.department_id)
                      ).map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.full_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit">Create Task</Button>
                <Button type="button" variant="outline" onClick={() => setShowCreateTask(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
