import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

export const SimpleLoginTest = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [step, setStep] = useState<string>('');

  const testLogin = async () => {
    setLoading(true);
    setResult(null);
    setStep('Starting login test...');

    try {
      // Step 1: Test basic auth
      setStep('Step 1: Testing authentication...');
      const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (authError) {
        setResult({
          success: false,
          step: 'Authentication',
          error: authError.message,
          data: authError
        });
        setLoading(false);
        return;
      }

      setStep('Step 2: Authentication successful, checking session...');
      
      // Step 2: Check session
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      
      if (sessionError) {
        setResult({
          success: false,
          step: 'Session Check',
          error: sessionError.message,
          data: sessionError
        });
        setLoading(false);
        return;
      }

      setStep('Step 3: Session valid, fetching profile...');

      // Step 3: Try to fetch profile
      let profileData = null;
      let profileError = null;

      if (authData.user?.id) {
        const { data: profile, error: profError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', authData.user.id)
          .single();

        profileData = profile;
        profileError = profError;
      }

      setStep('Step 4: Complete');

      setResult({
        success: true,
        step: 'Complete',
        data: {
          auth: authData,
          session: sessionData,
          profile: profileData,
          profileError: profileError?.message
        }
      });

    } catch (error: any) {
      setResult({
        success: false,
        step: step,
        error: error.message,
        data: error
      });
    } finally {
      setLoading(false);
    }
  };

  const testLogout = async () => {
    setLoading(true);
    setStep('Logging out...');
    
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        setResult({
          success: false,
          step: 'Logout',
          error: error.message
        });
      } else {
        setResult({
          success: true,
          step: 'Logout',
          data: 'Successfully logged out'
        });
      }
    } catch (error: any) {
      setResult({
        success: false,
        step: 'Logout',
        error: error.message
      });
    } finally {
      setLoading(false);
      setStep('');
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Simple Login Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="test-email">Email</Label>
            <Input
              id="test-email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="test-password">Password</Label>
            <Input
              id="test-password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={testLogin} 
            disabled={loading || !email || !password}
            className="flex-1"
          >
            {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
            Test Login
          </Button>
          <Button 
            onClick={testLogout} 
            disabled={loading}
            variant="outline"
          >
            Test Logout
          </Button>
        </div>

        {step && (
          <Alert>
            <AlertDescription>
              Current Step: {step}
            </AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert variant={result.success ? "default" : "destructive"}>
            <div className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="h-4 w-4 text-red-500" />
              )}
              <AlertDescription>
                <strong>Step:</strong> {result.step}<br />
                {result.error && (
                  <>
                    <strong>Error:</strong> {result.error}<br />
                  </>
                )}
                {result.success && (
                  <>
                    <strong>Status:</strong> Success
                  </>
                )}
              </AlertDescription>
            </div>
          </Alert>
        )}

        {result?.data && (
          <details className="mt-4">
            <summary className="cursor-pointer font-semibold">Raw Result Data</summary>
            <pre className="mt-2 p-4 bg-muted rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
};
