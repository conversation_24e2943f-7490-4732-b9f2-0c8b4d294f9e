export const EmergencyDashboard = () => {
  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='space-y-6 p-6'>
        <div className='bg-gradient-to-r from-red-600 to-black text-white p-6 rounded-lg'>
          <h1 className='text-3xl font-bold mb-2'>Manager Dashboard</h1>
          <p className='text-red-100'>Welcome back! Here's your overview for today.</p>
          <div className='mt-2 text-sm text-red-200'>✅ Dashboard loaded successfully</div>
        </div>

        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
          <div className='bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Total Users</p>
                <p className='text-2xl font-bold'>12</p>
                <p className='text-xs text-green-600'>+2.5% from last month</p>
              </div>
              <div className='h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center'>
                <span className='text-blue-600'>👥</span>
              </div>
            </div>
          </div>

          <div className='bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Active Projects</p>
                <p className='text-2xl font-bold'>8</p>
                <p className='text-xs text-green-600'>+12% from last month</p>
              </div>
              <div className='h-8 w-8 bg-green-100 rounded-full flex items-center justify-center'>
                <span className='text-green-600'>📊</span>
              </div>
            </div>
          </div>

          <div className='bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Tasks Completed</p>
                <p className='text-2xl font-bold'>24</p>
                <p className='text-xs text-green-600'>+8% from last month</p>
              </div>
              <div className='h-8 w-8 bg-purple-100 rounded-full flex items-center justify-center'>
                <span className='text-purple-600'>✅</span>
              </div>
            </div>
          </div>

          <div className='bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow'>
            <div className='flex items-center justify-between'>
              <div>
                <p className='text-sm font-medium text-gray-600'>Revenue</p>
                <p className='text-2xl font-bold'>$45,000</p>
                <p className='text-xs text-green-600'>+15% from last month</p>
              </div>
              <div className='h-8 w-8 bg-yellow-100 rounded-full flex items-center justify-center'>
                <span className='text-yellow-600'>💰</span>
              </div>
            </div>
          </div>
        </div>

        <div className='bg-white p-6 rounded-lg shadow'>
          <h3 className='text-lg font-semibold mb-4 flex items-center'>
            <span className='mr-2'>📋</span>
            Recent Activity
          </h3>
          <div className='space-y-4'>
            <div className='flex items-start space-x-3'>
              <div className='w-2 h-2 rounded-full mt-2 bg-green-500' />
              <div className='flex-1 min-w-0'>
                <p className='text-sm font-medium text-gray-900'>New project created</p>
                <p className='text-sm text-gray-500'>by Manager • 2 minutes ago</p>
              </div>
            </div>
            <div className='flex items-start space-x-3'>
              <div className='w-2 h-2 rounded-full mt-2 bg-blue-500' />
              <div className='flex-1 min-w-0'>
                <p className='text-sm font-medium text-gray-900'>Task completed</p>
                <p className='text-sm text-gray-500'>by Staff Member • 15 minutes ago</p>
              </div>
            </div>
            <div className='flex items-start space-x-3'>
              <div className='w-2 h-2 rounded-full mt-2 bg-yellow-500' />
              <div className='flex-1 min-w-0'>
                <p className='text-sm font-medium text-gray-900'>Report submitted</p>
                <p className='text-sm text-gray-500'>by Team Lead • 1 hour ago</p>
              </div>
            </div>
          </div>
        </div>

        <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
          <button className='p-4 text-center rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors bg-white'>
            <div className='text-2xl mb-2'>⏰</div>
            <span className='text-sm font-medium'>Time Tracking</span>
          </button>
          <button className='p-4 text-center rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors bg-white'>
            <div className='text-2xl mb-2'>👥</div>
            <span className='text-sm font-medium'>Team Management</span>
          </button>
          <button className='p-4 text-center rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors bg-white'>
            <div className='text-2xl mb-2'>📊</div>
            <span className='text-sm font-medium'>Reports</span>
          </button>
          <button className='p-4 text-center rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors bg-white'>
            <div className='text-2xl mb-2'>📋</div>
            <span className='text-sm font-medium'>Projects</span>
          </button>
        </div>
      </div>
    </div>
  )
}
