import { supabase } from '@/integrations/supabase/client';
import { ComprehensiveAPI } from './comprehensive-api';
import { UserActivitiesService } from './user-activities-service';
import { SystemLogsService } from './system-logs-service';

/**
 * Task Assignment Service
 * Comprehensive task management system with assignment, tracking, and progress monitoring
 */

export type TaskStatus = 'todo' | 'in_progress' | 'review' | 'testing' | 'done' | 'cancelled' | 'blocked';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent' | 'critical';
export type TaskType = 'task' | 'bug' | 'feature' | 'improvement' | 'research' | 'documentation';
export type AssignmentRole = 'assignee' | 'reviewer' | 'watcher' | 'collaborator';

export interface Task {
  id?: string;
  title: string;
  description?: string;
  project_id?: string;
  assigned_to?: string;
  created_by?: string;
  parent_task_id?: string;
  status: TaskStatus;
  priority: TaskPriority;
  type: TaskType;
  category?: string;
  tags?: string[];
  estimated_hours?: number;
  actual_hours?: number;
  progress_percentage?: number;
  start_date?: string;
  due_date?: string;
  completed_date?: string;
  dependencies?: string[];
  blockers?: string[];
  acceptance_criteria?: string[];
  attachments?: any[];
  comments_count?: number;
  watchers?: string[];
  labels?: string[];
  custom_fields?: Record<string, any>;
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface TaskComment {
  id?: string;
  task_id: string;
  user_id: string;
  comment: string;
  comment_type?: 'comment' | 'status_change' | 'assignment' | 'time_log' | 'attachment';
  mentions?: string[];
  attachments?: any[];
  metadata?: Record<string, any>;
  created_at?: string;
  updated_at?: string;
}

export interface TaskAssignment {
  id?: string;
  task_id: string;
  assigned_to: string;
  assigned_by?: string;
  role: AssignmentRole;
  status: 'active' | 'completed' | 'removed';
  assigned_at?: string;
  completed_at?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

export interface TaskFilter {
  assigned_to?: string;
  created_by?: string;
  project_id?: string;
  status?: TaskStatus[];
  priority?: TaskPriority[];
  type?: TaskType[];
  category?: string[];
  tags?: string[];
  due_date_from?: string;
  due_date_to?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface TaskStats {
  total: number;
  by_status: Record<TaskStatus, number>;
  by_priority: Record<TaskPriority, number>;
  by_type: Record<TaskType, number>;
  by_assignee: Array<{ user_id: string; user_name: string; count: number }>;
  overdue_count: number;
  completion_rate: number;
  average_completion_time: number;
  upcoming_deadlines: Task[];
}

export class TaskAssignmentService {
  // Create a new task
  static async createTask(task: Omit<Task, 'id' | 'created_at' | 'updated_at'>): Promise<string | null> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const newTask: Task = {
        ...task,
        created_by: currentUser.user_id,
        progress_percentage: 0,
        comments_count: 0,
        watchers: [currentUser.user_id!],
        metadata: {
          ...task.metadata,
          created_by_name: currentUser.full_name,
          created_via: 'web_interface'
        }
      };

      const { data, error } = await supabase
        .from('tasks')
        .insert(newTask)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Create initial assignment if assigned_to is specified
      if (task.assigned_to) {
        await this.assignTask(data.id, task.assigned_to, 'assignee');
      }

      // Log the activity
      await UserActivitiesService.logCreate(
        'task',
        data.id,
        task.title,
        `Created new task: ${task.title}`,
        {
          task_type: task.type,
          priority: task.priority,
          project_id: task.project_id,
          assigned_to: task.assigned_to
        }
      );

      await SystemLogsService.logBusinessEvent(
        'task_created',
        'task',
        data.id,
        `Task "${task.title}" created`,
        { task_type: task.type, priority: task.priority }
      );

      return data.id;
    } catch (error) {
      console.error('Error creating task:', error);
      await SystemLogsService.error('business', 'Failed to create task', error.toString());
      return null;
    }
  }

  // Update a task
  static async updateTask(
    taskId: string,
    updates: Partial<Task>,
    comment?: string
  ): Promise<boolean> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get the current task for comparison
      const { data: currentTask } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (!currentTask) {
        throw new Error('Task not found');
      }

      const updatedTask = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { error } = await supabase
        .from('tasks')
        .update(updatedTask)
        .eq('id', taskId);

      if (error) {
        throw error;
      }

      // Log status changes
      if (updates.status && updates.status !== currentTask.status) {
        await this.addTaskComment(
          taskId,
          `Status changed from ${currentTask.status} to ${updates.status}`,
          'status_change'
        );

        // If task is completed, set completion date
        if (updates.status === 'done') {
          await supabase
            .from('tasks')
            .update({ completed_date: new Date().toISOString() })
            .eq('id', taskId);
        }
      }

      // Log assignment changes
      if (updates.assigned_to && updates.assigned_to !== currentTask.assigned_to) {
        await this.assignTask(taskId, updates.assigned_to, 'assignee');
      }

      // Add comment if provided
      if (comment) {
        await this.addTaskComment(taskId, comment);
      }

      // Log the activity
      await UserActivitiesService.logUpdate(
        'task',
        taskId,
        currentTask.title,
        currentTask,
        { ...currentTask, ...updates },
        `Updated task: ${currentTask.title}`
      );

      return true;
    } catch (error) {
      console.error('Error updating task:', error);
      await SystemLogsService.error('business', 'Failed to update task', error.toString());
      return false;
    }
  }

  // Assign task to user
  static async assignTask(
    taskId: string,
    userId: string,
    role: AssignmentRole = 'assignee',
    notes?: string
  ): Promise<boolean> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      // Get user and task details
      const [userProfile, task] = await Promise.all([
        ComprehensiveAPI.getProfileByUserId(userId),
        this.getTaskById(taskId)
      ]);

      if (!userProfile || !task) {
        throw new Error('User or task not found');
      }

      // Create assignment record
      const assignment: TaskAssignment = {
        task_id: taskId,
        assigned_to: userId,
        assigned_by: currentUser.user_id,
        role,
        status: 'active',
        assigned_at: new Date().toISOString(),
        notes,
        metadata: {
          assigned_by_name: currentUser.full_name,
          assigned_to_name: userProfile.full_name
        }
      };

      const { error: assignmentError } = await supabase
        .from('task_assignments')
        .insert(assignment);

      if (assignmentError) {
        throw assignmentError;
      }

      // Update task assigned_to if this is the primary assignee
      if (role === 'assignee') {
        await supabase
          .from('tasks')
          .update({ 
            assigned_to: userId,
            updated_at: new Date().toISOString()
          })
          .eq('id', taskId);
      }

      // Add watchers
      await this.addWatcher(taskId, userId);

      // Add comment about assignment
      await this.addTaskComment(
        taskId,
        `Task ${role === 'assignee' ? 'assigned to' : `${role} role given to`} ${userProfile.full_name}`,
        'assignment'
      );

      // Log the activity
      await UserActivitiesService.logAssignment(
        'task',
        taskId,
        task.title,
        userId,
        userProfile.full_name,
        `Assigned task to ${userProfile.full_name} as ${role}`,
        { role, notes }
      );

      return true;
    } catch (error) {
      console.error('Error assigning task:', error);
      await SystemLogsService.error('business', 'Failed to assign task', error.toString());
      return false;
    }
  }

  // Add comment to task
  static async addTaskComment(
    taskId: string,
    comment: string,
    comment_type: TaskComment['comment_type'] = 'comment',
    mentions?: string[],
    attachments?: any[]
  ): Promise<string | null> {
    try {
      const currentUser = await ComprehensiveAPI.getCurrentUserProfile();
      
      if (!currentUser) {
        throw new Error('User not authenticated');
      }

      const taskComment: TaskComment = {
        task_id: taskId,
        user_id: currentUser.user_id!,
        comment,
        comment_type,
        mentions,
        attachments,
        metadata: {
          user_name: currentUser.full_name,
          user_role: currentUser.role
        }
      };

      const { data, error } = await supabase
        .from('task_comments')
        .insert(taskComment)
        .select()
        .single();

      if (error) {
        throw error;
      }

      // Update comments count
      await supabase.rpc('increment_task_comments', { task_id: taskId });

      // Add mentioned users as watchers
      if (mentions && mentions.length > 0) {
        for (const userId of mentions) {
          await this.addWatcher(taskId, userId);
        }
      }

      // Log the activity
      await UserActivitiesService.logActivity(
        'comment',
        'Added Task Comment',
        `Added comment to task`,
        'task',
        taskId,
        undefined,
        { comment_type, comment_length: comment.length }
      );

      return data.id;
    } catch (error) {
      console.error('Error adding task comment:', error);
      return null;
    }
  }

  // Add watcher to task
  static async addWatcher(taskId: string, userId: string): Promise<boolean> {
    try {
      const { data: task } = await supabase
        .from('tasks')
        .select('watchers')
        .eq('id', taskId)
        .single();

      if (!task) {
        throw new Error('Task not found');
      }

      const watchers = task.watchers || [];
      if (!watchers.includes(userId)) {
        watchers.push(userId);

        await supabase
          .from('tasks')
          .update({ 
            watchers,
            updated_at: new Date().toISOString()
          })
          .eq('id', taskId);
      }

      return true;
    } catch (error) {
      console.error('Error adding watcher:', error);
      return false;
    }
  }

  // Get tasks with filtering
  static async getTasks(filter: TaskFilter = {}): Promise<{ data: Task[]; count: number }> {
    try {
      let query = supabase
        .from('tasks')
        .select('*', { count: 'exact' });

      // Apply filters
      if (filter.assigned_to) {
        query = query.eq('assigned_to', filter.assigned_to);
      }

      if (filter.created_by) {
        query = query.eq('created_by', filter.created_by);
      }

      if (filter.project_id) {
        query = query.eq('project_id', filter.project_id);
      }

      if (filter.status && filter.status.length > 0) {
        query = query.in('status', filter.status);
      }

      if (filter.priority && filter.priority.length > 0) {
        query = query.in('priority', filter.priority);
      }

      if (filter.type && filter.type.length > 0) {
        query = query.in('type', filter.type);
      }

      if (filter.category && filter.category.length > 0) {
        query = query.in('category', filter.category);
      }

      if (filter.due_date_from) {
        query = query.gte('due_date', filter.due_date_from);
      }

      if (filter.due_date_to) {
        query = query.lte('due_date', filter.due_date_to);
      }

      if (filter.search) {
        query = query.or(`title.ilike.%${filter.search}%,description.ilike.%${filter.search}%`);
      }

      // Apply pagination
      const limit = filter.limit || 50;
      const offset = filter.offset || 0;
      query = query.range(offset, offset + limit - 1);

      // Order by priority and due date
      query = query.order('priority', { ascending: false })
                   .order('due_date', { ascending: true });

      const { data, error, count } = await query;

      if (error) {
        throw error;
      }

      return { data: data || [], count: count || 0 };
    } catch (error) {
      console.error('Error fetching tasks:', error);
      return { data: [], count: 0 };
    }
  }

  // Get task by ID
  static async getTaskById(taskId: string): Promise<Task | null> {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .select('*')
        .eq('id', taskId)
        .single();

      if (error) {
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching task:', error);
      return null;
    }
  }

  // Get task comments
  static async getTaskComments(taskId: string): Promise<TaskComment[]> {
    try {
      const { data, error } = await supabase
        .from('task_comments')
        .select('*')
        .eq('task_id', taskId)
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching task comments:', error);
      return [];
    }
  }

  // Get task assignments
  static async getTaskAssignments(taskId: string): Promise<TaskAssignment[]> {
    try {
      const { data, error } = await supabase
        .from('task_assignments')
        .select('*')
        .eq('task_id', taskId)
        .eq('status', 'active')
        .order('assigned_at', { ascending: false });

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching task assignments:', error);
      return [];
    }
  }

  // Get user tasks
  static async getUserTasks(userId: string, status?: TaskStatus[]): Promise<Task[]> {
    try {
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('assigned_to', userId);

      if (status && status.length > 0) {
        query = query.in('status', status);
      }

      query = query.order('priority', { ascending: false })
                   .order('due_date', { ascending: true });

      const { data, error } = await query;

      if (error) {
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error fetching user tasks:', error);
      return [];
    }
  }

  // Get task statistics
  static async getTaskStats(project_id?: string, user_id?: string): Promise<TaskStats> {
    try {
      let query = supabase.from('tasks').select('*');

      if (project_id) {
        query = query.eq('project_id', project_id);
      }

      if (user_id) {
        query = query.eq('assigned_to', user_id);
      }

      const { data: tasks } = await query;

      if (!tasks) {
        return this.getEmptyStats();
      }

      const now = new Date();
      const overdueTasks = tasks.filter(task => 
        task.due_date && 
        new Date(task.due_date) < now && 
        task.status !== 'done' && 
        task.status !== 'cancelled'
      );

      const completedTasks = tasks.filter(task => task.status === 'done');
      const completionRate = tasks.length > 0 ? (completedTasks.length / tasks.length) * 100 : 0;

      // Calculate average completion time
      const completedTasksWithDates = completedTasks.filter(task => 
        task.created_at && task.completed_date
      );
      
      const totalCompletionTime = completedTasksWithDates.reduce((sum, task) => {
        const created = new Date(task.created_at!);
        const completed = new Date(task.completed_date!);
        return sum + (completed.getTime() - created.getTime());
      }, 0);

      const averageCompletionTime = completedTasksWithDates.length > 0 
        ? totalCompletionTime / completedTasksWithDates.length / (1000 * 60 * 60 * 24) // Convert to days
        : 0;

      // Get upcoming deadlines (next 7 days)
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      
      const upcomingDeadlines = tasks
        .filter(task => 
          task.due_date && 
          new Date(task.due_date) <= nextWeek && 
          new Date(task.due_date) >= now &&
          task.status !== 'done' && 
          task.status !== 'cancelled'
        )
        .sort((a, b) => new Date(a.due_date!).getTime() - new Date(b.due_date!).getTime())
        .slice(0, 10);

      const stats: TaskStats = {
        total: tasks.length,
        by_status: this.groupByField(tasks, 'status') as Record<TaskStatus, number>,
        by_priority: this.groupByField(tasks, 'priority') as Record<TaskPriority, number>,
        by_type: this.groupByField(tasks, 'type') as Record<TaskType, number>,
        by_assignee: await this.getTasksByAssignee(tasks),
        overdue_count: overdueTasks.length,
        completion_rate: Math.round(completionRate * 100) / 100,
        average_completion_time: Math.round(averageCompletionTime * 100) / 100,
        upcoming_deadlines: upcomingDeadlines
      };

      return stats;
    } catch (error) {
      console.error('Error fetching task stats:', error);
      return this.getEmptyStats();
    }
  }

  // Delete task
  static async deleteTask(taskId: string): Promise<boolean> {
    try {
      const task = await this.getTaskById(taskId);
      
      if (!task) {
        throw new Error('Task not found');
      }

      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId);

      if (error) {
        throw error;
      }

      // Log the activity
      await UserActivitiesService.logDelete(
        'task',
        taskId,
        task.title,
        `Deleted task: ${task.title}`
      );

      return true;
    } catch (error) {
      console.error('Error deleting task:', error);
      return false;
    }
  }

  // Private helper methods
  private static groupByField(tasks: Task[], field: string): Record<string, number> {
    return tasks.reduce((acc, task) => {
      const value = (task as any)[field] || 'unknown';
      acc[value] = (acc[value] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }

  private static async getTasksByAssignee(tasks: Task[]): Promise<Array<{ user_id: string; user_name: string; count: number }>> {
    try {
      const assigneeCounts: Record<string, number> = {};
      
      tasks.forEach(task => {
        if (task.assigned_to) {
          assigneeCounts[task.assigned_to] = (assigneeCounts[task.assigned_to] || 0) + 1;
        }
      });

      // Get user profiles for names
      const userProfiles = await ComprehensiveAPI.getAllProfiles();
      const userMap = new Map(userProfiles.map(u => [u.user_id, u.full_name]));

      return Object.entries(assigneeCounts)
        .map(([user_id, count]) => ({
          user_id,
          user_name: userMap.get(user_id) || 'Unknown User',
          count
        }))
        .sort((a, b) => b.count - a.count);
    } catch (error) {
      return [];
    }
  }

  private static getEmptyStats(): TaskStats {
    return {
      total: 0,
      by_status: {} as Record<TaskStatus, number>,
      by_priority: {} as Record<TaskPriority, number>,
      by_type: {} as Record<TaskType, number>,
      by_assignee: [],
      overdue_count: 0,
      completion_rate: 0,
      average_completion_time: 0,
      upcoming_deadlines: []
    };
  }
}

export default TaskAssignmentService;
